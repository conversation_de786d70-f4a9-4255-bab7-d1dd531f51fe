你是一名资深后端系统架构师，请根据我提供的 Java 接口代码（如 Controller, Facade 等），反向生成一份详细的接口设计文档，要求如下：

📘 文档内容要求（Markdown 格式）：
	1. 接口名称（类名）
	2. 接口作用简述（一句话说明该接口的核心职责）
	3. 接口清单：列出对外提供的所有 Endpoint 或核心方法及其用途
	4. 关键接口说明：
		- 请求 URL
		- 请求方法（GET/POST/PUT/DELETE 等）
		- 方法签名
		- 入参（Request Body/Query Param，需包含字段名、类型、是否必填、说明）
		- 出参（Response Body，需包含字段名、类型、含义）
		- 异常处理说明（如有）
	5. 关键业务逻辑说明：用自然语言描述核心处理逻辑与判断分支，尽可能详细
	6. 流程图（使用 PlantUML 语法绘制）：
		- @startuml ... @enduml 包裹流程图，描述单个接口的核心处理流程
	7. 时序图（使用 PlantUML 语法）：
		- 展示接口调用链：前端 → 网关 → 控制器 → Service → DB / 缓存 / 外部依赖
		- @startuml ... @enduml
	8. 异常处理机制：主要异常场景及处理方式
	9. 调用的公共模块或外部依赖：
		- 模块/服务名称
		- 功能简述
	10. 被哪些模块调用（可选，如果上下文中能分析到）

✏️ 注意事项：
	- 所有图示必须使用 PlantUML 标准语法
	- Markdown 文档结构清晰，适合归档和设计评审使用
	- 文件最终保存在项目根目录下 .cursor/doc/代码文档/上上级目录/上级目录/类名.md
