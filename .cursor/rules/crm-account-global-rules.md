---
description: CRM-Account项目全局开发规范
globs: 
alwaysApply: true
---

# CRM-Account项目全局开发规范

**作者**: hongdong.xie  
**日期**: 2025-08-03 22:31:43  
**版本**: v1.0.0

好嘞，我明白了！基于对当前项目的深入分析，我为您制定了这份全面的全局开发规范。

---

## 🎯 项目概述

### 项目目标
- 提升微服务稳定性和高可用性
- 优化接口性能，提升响应速度
- 提高代码质量、可读性、可维护性和可测试性
- 支持新业务代码开发和代码重构优化
- 实现需求场景的高可用性

### 项目简介
CRM-Account是一个基于Spring Boot + Dubbo3的客户账户管理微服务系统，采用多模块架构设计，提供客户信息管理、投顾服务、敏感词过滤等核心业务功能。

---

## 🏗️ 技术栈配置

### 核心技术栈
| 技术分类 | 技术选型 | 版本 | 说明 |
|---------|---------|------|------|
| **编程语言** | Java | 1.8 | JDK 1.8 |
| **框架** | Spring Boot | 2.3.7.RELEASE | 主框架 |
| **框架** | Spring Cloud | Hoxton.SR9 | 微服务框架 |
| **框架** | Spring Cloud Alibaba | 2.2.6.RELEASE | 阿里云微服务组件 |
| **RPC框架** | Dubbo | 3.2.12 | 服务间通信 |
| **数据库** | MySQL | - | 主要业务数据库 |
| **数据库** | Oracle | 11g | 遗留系统数据库 |
| **ORM** | MyBatis | 2.2.2 | 数据库操作 |
| **连接池** | Druid | 1.2.8 | 数据库连接池 |
| **缓存** | Redis | 4.3.2 | 分布式缓存 |
| **缓存** | EhCache | - | 本地缓存 |
| **注册中心** | Nacos | - | 服务注册发现 |
| **配置中心** | Nacos | - | 配置管理 |
| **日志** | Log4j2 | 2.15.0 | 日志框架 |
| **工具** | Lombok | - | 代码简化 |
| **校验** | howbuy-commons-validator | 1.0.0-SNAPSHOT | 参数校验 |

### 数据库配置
```properties
# MySQL数据库
mysql.url=***************************************************************************************************************************************************************************************
mysql.username=crmdocusr
mysql.password=crmdocusr

# Oracle数据库
oracle.url=********************************************
oracle.username=docker_cust_dev
oracle.password=cust1
```

---

## 🏛️ 项目架构

### 模块结构
```
crm-account/                     # 父项目
├── crm-account-client/          # 接口定义模块
│   ├── facade/                  # Dubbo接口定义
│   ├── request/                 # 请求参数对象
│   ├── response/                # 响应结果对象
│   ├── enums/                   # 枚举定义
│   └── constant/                # 常量定义
├── crm-account-dao/             # 数据访问层
│   ├── mapper/                  # MyBatis Mapper接口
│   ├── po/                      # 数据库实体对象
│   ├── bo/                      # 查询对象
│   └── config/                  # 数据源配置
├── crm-account-service/         # 业务服务层
│   ├── facade/                  # Dubbo接口实现
│   ├── service/                 # 业务服务
│   ├── business/                # 业务逻辑
│   ├── repository/              # 数据仓储层
│   ├── controller/              # HTTP接口
│   ├── cacheservice/            # 缓存服务
│   ├── outservice/              # 外部服务调用
│   ├── validator/               # 参数校验
│   ├── job/                     # 定时任务
│   └── mq/                      # 消息队列处理
└── crm-account-remote/          # 启动模块
    ├── config/                  # 全局配置
    ├── interceptor/             # 拦截器
    ├── exception/               # 异常处理
    └── CrmAccountApplication    # 启动类
```

### 包命名规范
```java
// 基础包名
com.howbuy.crm.account

// 各模块包名规范
com.howbuy.crm.account.client.*      // 接口定义模块
com.howbuy.crm.account.dao.*         // 数据访问层
com.howbuy.crm.account.service.*     // 业务服务层
com.howbuy.crm.account.remote.*      // 启动模块
```

### 分层调用关系
```
Controller/Facade → Service → Repository → Mapper
Controller/Facade → Business → Repository → Mapper
Service → Validator/CacheService/OutService
Job/MQ → Service/Business
```

---

## 📋 代码规范

### 命名约定
| 类型 | 规范 | 示例 | 说明 |
|------|------|------|------|
| **变量** | camelCase | `userName`, `custId` | 小驼峰命名 |
| **方法** | camelCase | `queryUserInfo()`, `updateCustInfo()` | 小驼峰命名 |
| **类** | PascalCase | `UserService`, `CustInfoFacade` | 大驼峰命名 |
| **常量** | UPPER_SNAKE_CASE | `MAX_RETRY_COUNT`, `DEFAULT_PAGE_SIZE` | 全大写+下划线 |
| **包** | lowercase | `com.howbuy.crm.account.service` | 全小写 |

### 类命名规范
| 类型 | 命名规范 | 示例 |
|------|----------|------|
| **Facade接口** | `{业务名}Facade` | `ConsultantInfoFacade` |
| **Facade实现** | `{业务名}FacadeImpl` | `ConsultantInfoFacadeImpl` |
| **Service类** | `{业务名}Service` | `UserInfoService` |
| **Repository类** | `{业务名}Repository` | `CustInfoRepository` |
| **Controller类** | `{业务名}Controller` | `UserController` |
| **Request对象** | `{接口名}Request` | `QueryConsultantInfoRequest` |
| **Response对象** | `{接口名}VO` | `SensitiveWordsVO` |
| **内部VO** | `{业务名}VO` | `CmConsultantInfo` |
| **PO对象** | `{表名}PO` | `CmCustInfoPO` |
| **BO对象** | `{业务名}BO` | `CustQueryBO` |

### 代码格式规范
```java
// 缩进：4个空格
public class UserService {
    
    // 每行最大长度：120个字符
    public Response<UserInfoVO> queryUserInfo(QueryUserInfoRequest request) {
        // 方法体
    }
}
```

### 导包顺序
```java
// 1. 静态导入
import static com.howbuy.commons.util.CommonUtil.*;

// 2. Java标准库
import java.util.List;
import java.util.Map;

// 3. Javax库
import javax.annotation.Resource;

// 4. 第三方库 (org)
import org.springframework.stereotype.Service;
import org.apache.dubbo.config.annotation.DubboService;

// 5. 公司内部库 (com.howbuy)
import com.howbuy.crm.account.client.facade.UserFacade;
```

---

## 📝 注释规范

### 类注释规范
```java
/**
 * @description 用户信息管理服务
 * <AUTHOR>
 * @date 2025-08-03 22:31:43
 */
public class UserInfoService {
}
```

### 方法注释规范
```java
/**
 * @description 查询用户基本信息
 * @param request 查询请求参数
 * @return 用户信息响应结果
 */
public Response<UserInfoVO> queryUserInfo(QueryUserInfoRequest request) {
}
```

### APIDOC注释规范
```java
/**
 * @api {DUBBO} com.howbuy.crm.account.client.facade.user.UserFacade.queryUserInfo(request) queryUserInfo()
 * @apiVersion 1.0.0
 * @apiGroup UserFacade
 * @apiName queryUserInfo()
 * @apiDescription 查询用户信息接口
 * @apiParam (请求体) {String} custId 客户ID
 * @apiParam (请求体) {String} custType 客户类型(01-个人，02-企业)
 * @apiParamExample 请求体示例
 * {"custId": "123456", "custType": "01"}
 * @apiSuccess (响应结果) {String} code 状态码
 * @apiSuccess (响应结果) {String} description 描述信息
 * @apiSuccess (响应结果) {Object} data 数据封装
 * @apiSuccess (响应结果) {String} data.custName 客户姓名
 * @apiSuccess (响应结果) {String} data.custPhone 客户手机号
 * @apiSuccessExample 响应结果示例
 * {"code": "0", "description": "成功", "data": {"custName": "张三", "custPhone": "138****8888"}}
 */
Response<UserInfoVO> queryUserInfo(QueryUserInfoRequest request);
```

### 注释要求
- **语言**：所有注释使用中文
- **完整性**：Controller层、Service层、Repository层和关键业务逻辑必须添加注释
- **准确性**：注释需清晰、简洁、准确，避免冗余和模糊
- **同步性**：代码变更时同步更新注释，保持一致性
- **作者日期**：如果类或方法已有注释，不要更新或覆盖原有的@author和@date值

---

## 🔌 Dubbo接口规范

### 接口定义规范
```java
// 1. 接口目录结构
com.howbuy.crm.account.client.facade.{业务类型}.{业务名}Facade
com.howbuy.crm.account.service.facade.{业务类型}.{业务名}FacadeImpl

// 2. 请求对象目录
com.howbuy.crm.account.client.request.{业务类型}.{接口名}Request

// 3. 响应对象目录
com.howbuy.crm.account.client.response.{业务类型}.{接口名}VO
```

### Request对象规范
```java
/**
 * @description 用户信息查询请求对象
 * <AUTHOR>
 * @date 2025-08-03 22:31:43
 */
@Getter
@Setter
public class QueryUserInfoRequest extends Request implements Serializable {
    
    private static final long serialVersionUID = 1L;
    
    /**
     * 客户ID
     */
    @MyValidation(validatorType = ValidatorTypeEnum.String, fieldName = "客户ID", isRequired = true)
    private String custId;
    
    /**
     * 客户类型
     * 01-个人客户
     * 02-企业客户
     */
    @MyValidation(validatorType = ValidatorTypeEnum.String, fieldName = "客户类型", isRequired = false)
    private String custType;
}
```

### VO对象规范
```java
/**
 * @description 用户信息响应对象
 * <AUTHOR>
 * @date 2025-08-03 22:31:43
 */
@Getter
@Setter
public class UserInfoVO implements Serializable {
    
    private static final long serialVersionUID = 1L;
    
    /**
     * 客户姓名
     */
    private String custName;
    
    /**
     * 客户手机号
     */
    private String custPhone;
    
    /**
     * 客户邮箱
     */
    private String custEmail;
}
```

### Facade实现规范
```java
/**
 * @description 用户信息管理接口实现
 * <AUTHOR>
 * @date 2025-08-03 22:31:43
 */
@Slf4j
@DubboService
public class UserInfoFacadeImpl implements UserInfoFacade {

    @Resource
    private UserInfoService userInfoService;
    
    /**
     * @description 查询用户信息
     * @param request 查询参数
     * @return 用户信息
     */
    @Override
    public Response<UserInfoVO> queryUserInfo(QueryUserInfoRequest request) {
        log.info("查询用户信息开始，请求参数：{}", request);
        
        try {
            UserInfoVO result = userInfoService.queryUserInfo(request);
            log.info("查询用户信息结束，响应结果：{}", result);
            return Response.ok(result);
        } catch (Exception e) {
            log.error("查询用户信息异常", e);
            return Response.fail("查询用户信息失败：" + e.getMessage());
        }
    }
}
```

### Response规范
```java
// 成功返回
return Response.ok(data);
return Response.ok("操作成功", data);

// 失败返回
return Response.fail("操作失败");
return Response.fail("E001", "参数校验失败");
return Response.fail(ExceptionCodeEnum.PARAM_ERROR);
```

---

## 🗄️ 数据库规范

### 数据库连接配置
```yaml
# 使用Druid连接池
spring:
  datasource:
    type: com.alibaba.druid.pool.DruidDataSource
    druid:
      initial-size: 5
      min-idle: 5
      max-active: 20
      max-wait: 60000
      validation-query: SELECT 1
      test-while-idle: true
```

### MyBatis配置
```java
// Mapper接口
@Mapper
public interface UserInfoMapper {
    
    /**
     * @description 查询用户信息
     * @param custId 客户ID
     * @return 用户信息
     */
    UserInfoPO selectByCustId(@Param("custId") String custId);
}
```

### 事务管理规范
```java
// 1. 类级别事务注解
@Transactional(rollbackFor = Exception.class, propagation = Propagation.SUPPORTS)
public class UserInfoService {
    
    // 2. 增删改方法事务注解
    @Transactional(rollbackFor = Exception.class, propagation = Propagation.REQUIRED)
    public void updateUserInfo(UserInfoRequest request) {
        // 业务逻辑
    }
}
```

---

## 📊 日志规范

### 日志配置
- **框架**：Log4j2
- **级别**：info
- **格式**：JSON格式，包含时间、链路ID、线程、级别、类行号、消息
- **脱敏**：身份证号、银行卡号等敏感信息自动脱敏

### 日志使用规范
```java
@Slf4j
public class UserInfoService {
    
    public UserInfoVO queryUserInfo(QueryUserInfoRequest request) {
        // 1. 记录入参
        log.info("查询用户信息开始，请求参数：{}", request);
        
        try {
            // 业务逻辑
            UserInfoVO result = businessLogic(request);
            
            // 2. 记录出参
            log.info("查询用户信息结束，响应结果：{}", result);
            return result;
            
        } catch (Exception e) {
            // 3. 记录异常
            log.error("查询用户信息异常，请求参数：{}", request, e);
            throw e;
        }
    }
}
```

### 日志级别使用
- **ERROR**：系统异常、业务异常
- **WARN**：警告信息、降级信息
- **INFO**：关键业务节点、入参出参
- **DEBUG**：调试信息（生产环境关闭）

---

## ✅ 测试规范

### 单元测试
```java
// 使用JUnit + Mockito + PowerMock
@RunWith(PowerMockRunner.class)
@PrepareForTest({UserInfoService.class})
public class UserInfoServiceTest {
    
    @Mock
    private UserInfoRepository userInfoRepository;
    
    @InjectMocks
    private UserInfoService userInfoService;
    
    @Test
    public void testQueryUserInfo() {
        // Given
        QueryUserInfoRequest request = new QueryUserInfoRequest();
        request.setCustId("123456");
        
        UserInfoPO userInfoPO = new UserInfoPO();
        userInfoPO.setCustName("张三");
        
        when(userInfoRepository.selectByCustId("123456")).thenReturn(userInfoPO);
        
        // When
        UserInfoVO result = userInfoService.queryUserInfo(request);
        
        // Then
        assertNotNull(result);
        assertEquals("张三", result.getCustName());
    }
}
```

### 测试要求
- **覆盖率**：单元测试覆盖率 ≥ 99%
- **Controller测试**：使用MockMVC测试Controller层
- **Service测试**：使用PowerMock、Mockito和JUnit测试Service层
- **集成测试**：关键业务流程的集成测试

---

## 🔧 配置管理

### Nacos配置
```properties
# bootstrap.properties
spring.application.name=crm-account-remote
spring.cloud.nacos.config.server-addr=nacos1.inner.howbuy.com:8848,nacos2.inner.howbuy.com:8848,nacos3.inner.howbuy.com:8848
spring.cloud.nacos.discovery.server-addr=nacos1.inner.howbuy.com:8848,nacos2.inner.howbuy.com:8848,nacos3.inner.howbuy.com:8848
spring.cloud.nacos.config.namespace=dev
spring.cloud.nacos.config.group=2.0.3.0
spring.profiles.active=dev
```

### 配置优先级
1. **Nacos配置**：动态配置，支持热更新
2. **application.yml**：应用配置
3. **bootstrap.properties**：启动配置

---

## 🚀 部署规范

### 启动脚本
```bash
#!/bin/bash
# start.sh
java -jar crm-account-remote.jar --spring.profiles.active=prod
```

### 健康检查
```java
@RestController
public class HealthController {
    
    @GetMapping("/health")
    public String health() {
        return "OK";
    }
}
```

---

## 💡 开发最佳实践

### 并发编程
```java
// 使用线程池
@Configuration
public class ThreadPoolConfig {
    
    @Bean
    public ThreadPoolTaskExecutor taskExecutor() {
        ThreadPoolTaskExecutor executor = new ThreadPoolTaskExecutor();
        executor.setCorePoolSize(5);
        executor.setMaxPoolSize(10);
        executor.setQueueCapacity(100);
        executor.setThreadNamePrefix("crm-account-");
        return executor;
    }
}
```

### 异常处理
```java
@ControllerAdvice
public class GlobalExceptionHandler {
    
    @ExceptionHandler(Exception.class)
    public Response<Void> handleException(Exception e) {
        log.error("系统异常", e);
        return Response.fail("系统异常，请稍后重试");
    }
}
```

### 缓存策略
```java
@Service
public class UserCacheService {
    
    @Cacheable(value = "userInfo", key = "#custId")
    public UserInfoVO getUserInfo(String custId) {
        // 查询数据库
        return queryFromDatabase(custId);
    }
}
```

---

## 🚫 开发禁忌

### 严格禁止
1. **禁止使用**：`BeanUtils.copyProperties()` 做类属性复制
2. **禁止复用**：不同接口的Request和VO对象禁止复用
3. **禁止使用**：`@Data` 注解，统一使用 `@Getter/@Setter`
4. **禁止修改**：已有注释的 `@author` 和 `@date` 值

### 注意事项
- 集合对象（List、Map）不使用 `@MyValidation` 校验，通过代码校验
- 超时设置：接口调用需实现超时、不重试机制
- 日志脱敏：敏感信息自动脱敏处理
- 性能优化：考虑缓存策略，避免重复查询

---

## 📚 参考文档

- [Spring Boot官方文档](https://spring.io/projects/spring-boot)
- [Dubbo官方文档](https://dubbo.apache.org/)
- [MyBatis官方文档](https://mybatis.org/mybatis-3/)
- [项目命名规范](http://dms.intelnal.howbuy.com/pages/viewpage.action?pageId=********)

---

**版权声明**：© 2025 好买基金. 保留所有权利.

此规范适用于 CRM-Account 项目的所有开发活动，所有团队成员都应严格遵守。如有疑问或建议，请联系项目负责人。