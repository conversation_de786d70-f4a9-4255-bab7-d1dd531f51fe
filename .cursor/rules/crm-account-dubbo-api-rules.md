---
description: CRM-Account项目Dubbo接口定义规范
globs: 
alwaysApply: false
---
# CRM-Account项目Dubbo接口定义规范
# 作者: hongdong.xie
# 日期: 2025-08-03 22:28:07

#==================== 项目架构概述 ====================

# 项目结构
project_structure:
  - crm-account-client: Dubbo接口定义、入参、出参、枚举、常量
  - crm-account-service: Dubbo接口实现、业务逻辑
  - crm-account-dao: 数据库访问层
  - crm-account-remote: 启动类、全局配置、拦截器

# 分层调用关系
layer_architecture:
  - 接口层(Facade) > 服务层(Service) > 仓储层(Repository)
  - 接口层(Facade) > 业务层(Business) > 仓储层(Repository)
  - 服务层可调用: validator、cacheservice、outservice

#==================== 接口定义规范 ====================

# 接口定义规范
interface_definition:
  - 接口目录:
    - 接口统一放在com.howbuy.crm.account.client.facade包目录，再按照业务类型区分子目录
    - 接口实现统一放在com.howbuy.crm.account.service.facade包目录，再按照业务类型区分子目录
  - 入参:
    - 入参XXXRequest接口统一继承Request父类
    - 入参统一放在com.howbuy.crm.account.client.request包目录，再按照业务类型区分子目录
  - 出参:
    - 出参统一放在com.howbuy.crm.account.client.response包目录，再按照业务类型区分子目录
  - 注解:
    - 所有接口实现使用@Slf4j、@DubboService注解
  - 返回值:
    - 统一使用Response.ok(VO)输出返回结果

# 接口修改/新增规则
interface_modify_rules:
  - 判断依据:
    - 设计文档中接口名后标注"——修改"的，表示在原有接口修改
    - 设计文档中接口名后标注"——新增"的，表示新增接口
    - 设计文档中接口名后无标注的，表示新增接口
  - 修改原则:
    - 修改接口时，保持原有的方法名和参数不变
    - 修改接口时，需要兼容原有的功能
    - 修改接口时，新增字段采用追加方式，不要删除或修改原有字段
    - 修改接口时，保持原有的注释格式，仅更新内容

# VO和Request对象复用规则
vo_request_rules:
  - 基本原则:
    - 即使不同接口的入参或出参结构完全一样，也不要复用VO和Request类
    - 每个接口都应该有自己独立的Request和VO类
    - 避免多个接口共用同一个Request或VO类
  - 命名规范:
    - Request类命名: 接口名+Request
    - VO类命名: 接口名+VO 或 业务名+VO
    - 内部VO类命名: 业务名+VO

# 类和方法命名规范
naming_rules:
  - Facade接口: 业务名Facade (例: ConsultantInfoFacade)
  - Facade实现: 业务名FacadeImpl (例: ConsultantInfoFacadeImpl)
  - Request对象: 接口名+Request (例: QueryConsultantInfoRequest)
  - 响应VO: 接口名+VO (例: SensitiveWordsVO)
  - 内部VO: 业务名+VO (例: CmConsultantInfo)
  - 所有类和方法必须提供完整注释

#==================== 对象规范 ====================

# Request对象规范
request_rules:
  - 继承关系: 必须继承com.howbuy.crm.account.client.request.Request基类
  - 序列化: 必须实现Serializable接口，添加serialVersionUID
  - 注解: 使用@Getter/@Setter (不用@Data)
  - 校验: 必填字段使用@MyValidation(validatorType=类型, fieldName=中文名, isRequired=true)
  - 校验注解: 使用com.howbuy.commons.validator.MyValidation
  - 校验类型: 使用com.howbuy.commons.validator.util.ValidatorTypeEnum
  - 字段类型: 优先使用String类型，添加详细中文注释说明
  - 集合校验: List、Map等集合对象不使用@MyValidation，通过代码校验

# VO对象规范
vo_rules:
  - 序列化: 必须实现Serializable接口，添加serialVersionUID
  - 注解: 使用@Getter/@Setter (不用@Data)
  - 字段类型: 优先使用String类型，特殊情况可使用具体类型
  - 注释: 所有字段必须添加中文注释说明
  - 复杂对象: 
    - 列表使用List<专用VO>类型
    - 嵌套对象创建专用VO类
    - 字段命名体现业务含义

# Response规范
response_rules:
  - 基类: 统一使用com.howbuy.crm.account.client.response.Response<T>
  - 返回格式: 包含code、description、data三个字段
  - 成功返回: Response.ok(data)
  - 失败返回: Response.fail(description) 或 Response.fail(code, description)

#==================== 注释规范 ====================

# 类注释规范
class_comment:
  ```
  /**
   * @description 类的功能描述
   * <AUTHOR>
   * @date yyyy-MM-dd HH:mm:ss
   */
  ```

# 方法注释规范
method_comment:
  ```
  /**
   * @description 方法功能描述
   * @param 参数名 参数说明
   * @return 返回值说明
   */
  ```

# APIDOC规范
apidoc_rules:
  - 基本信息:
    - @api: {DUBBO} 完整接口路径.方法名(参数) 方法名()
    - @apiVersion: 接口版本号，例如 1.0.0
    - @apiGroup: 所属接口类名称
    - @apiName: 方法名称，需要带括号
    - @apiDescription: 接口功能描述
  - 请求参数:
    - @apiParam: 使用 (请求体) 标识，包含参数类型和说明
    - @apiParamExample: 请求参数示例，使用JSON格式
  - 响应结果:
    - @apiSuccess: 使用 (响应结果) 标识，包含以下固定字段:
      - code: 返回码
      - description: 返回描述
      - data: 返回内容
    - @apiSuccessExample: 响应结果示例，使用JSON格式
  - 注释格式:
    - 所有注释使用中文
    - 参数说明要包含可选值说明，例如 Y-是，N-否
    - 示例数据要符合实际业务场景

#==================== 实现规范 ====================

# Facade实现类规范
facade_impl_rules:
  - 注解:
    - @Slf4j: 用于日志记录
    - @DubboService: 标识为Dubbo服务提供者
    - 可选@Service: 某些实现类会加此注解
  - 依赖注入:
    - 使用@Resource注解注入依赖
    - 优先注入Service层服务
  - 异常处理:
    - 必须进行异常捕获和处理
    - 异常时返回Response.fail()
    - 记录异常日志
  - 日志记录:
    - 记录入参和出参日志
    - 异常时记录错误日志
    - 使用log.info()、log.error()等

# 代码风格规范
code_style:
  - 变量命名: camelCase (小驼峰)
  - 方法命名: camelCase (小驼峰)
  - 类命名: PascalCase (大驼峰)
  - 常量命名: UPPERCASE_WITH_UNDERSCORES
  - 缩进: 4个空格
  - 每行最大长度: 120个字符
  - 使用lombok注解简化代码
  - 禁止使用BeanUtils.copyProperties

#==================== 示例 ====================

# 接口定义示例
example_interface:
  ```java
  /**
   * @description 敏感词查询接口
   * <AUTHOR>
   * @date 2025-08-03 22:28:07
   */
  public interface SensitiveWordsFacade {

      /**
       * @api {DUBBO} com.howbuy.crm.account.client.facade.sensitive.SensitiveWordsFacade.querySensitiveWords(request) querySensitiveWords()
       * @apiVersion 1.0.0
       * @apiGroup SensitiveWordsFacade
       * @apiName querySensitiveWords()
       * @apiDescription 敏感词查询接口
       * @apiParam (请求体) {String} module 功能模块(01-表示客服来电处理模块 02-表示资产配置报告模块)
       * @apiParamExample 请求体示例
       * {"module": "01"}
       * @apiSuccess (响应结果) {String} code 状态码
       * @apiSuccess (响应结果) {String} description 描述信息
       * @apiSuccess (响应结果) {Object} data 数据封装
       * @apiSuccess (响应结果) {Array} data.sensitiveWords 敏感词列表
       * @apiSuccessExample 响应结果示例
       * {"code": "0", "description": "成功", "data": {"sensitiveWords": ["敏感词1", "敏感词2"]}}
       */
      Response<SensitiveWordsVO> querySensitiveWords(SensitiveWordsRequest request);
  }
  ```

# Request对象示例
request_example:
  ```java
  /**
   * @description 敏感词查询请求对象
   * <AUTHOR>
   * @date 2025-08-03 22:28:07
   */
  @Getter
  @Setter
  public class SensitiveWordsRequest extends Request implements Serializable {
      
      private static final long serialVersionUID = 1L;
      
      /**
       * 功能模块
       * 01-表示客服来电处理模块
       * 02-表示资产配置报告模块
       */
      @MyValidation(validatorType = ValidatorTypeEnum.String, fieldName = "功能模块", isRequired = true)
      private String module;
  }
  ```

# VO对象示例
vo_example:
  ```java
  /**
   * @description 敏感词查询响应对象
   * <AUTHOR>
   * @date 2025-08-03 22:28:07
   */
  @Getter
  @Setter
  public class SensitiveWordsVO implements Serializable {
      
      private static final long serialVersionUID = 1L;
      
      /**
       * 敏感词列表
       */
      private List<String> sensitiveWords;
  }
  ```

# 实现类示例
facade_impl_example:
  ```java
  /**
   * @description 敏感词查询接口实现
   * <AUTHOR>
   * @date 2025-08-03 22:28:07
   */
  @Slf4j
  @DubboService
  public class SensitiveWordsFacadeImpl implements SensitiveWordsFacade {

      @Resource
      private SensitiveWordsService sensitiveWordsService;
      
      /**
       * @description 查询敏感词
       * @param request 查询参数
       * @return 敏感词列表
       */
      @Override
      public Response<SensitiveWordsVO> querySensitiveWords(SensitiveWordsRequest request) {
          log.info("查询敏感词列表开始，请求参数：{}", request);
          
          try {
              SensitiveWordsVO result = sensitiveWordsService.querySensitiveWords(request);
              log.info("查询敏感词列表结束，响应结果：{}", result);
              return Response.ok(result);
          } catch (Exception e) {
              log.error("查询敏感词列表异常", e);
              return Response.fail("查询敏感词列表失败：" + e.getMessage());
          }
      }
  }
  ```

#==================== 技术栈信息 ====================

# 技术栈
tech_stack:
  - 编程语言: Java 1.8
  - 框架: Spring Boot, Spring Cloud
  - RPC框架: Dubbo3
  - 数据库: MySQL、Oracle 11g
  - 缓存: Redis
  - ORM: MyBatis
  - 连接池: Druid
  - 工具: Lombok
  - 校验: howbuy-commons-validator

# 关键依赖包
key_dependencies:
  - com.howbuy.commons.validator.MyValidation: 参数校验注解
  - com.howbuy.commons.validator.util.ValidatorTypeEnum: 校验类型枚举
  - org.apache.dubbo.config.annotation.DubboService: Dubbo服务注解
  - lombok.*: 代码简化注解

#==================== 最佳实践 ====================

# 开发最佳实践
best_practices:
  - 接口设计: 遵循单一职责原则，每个接口只做一件事
  - 参数校验: 使用注解进行参数校验，业务层进行逻辑校验
  - 异常处理: 统一异常处理，返回友好的错误信息
  - 日志记录: 记录关键节点的日志信息，便于问题排查
  - 代码复用: 避免VO和Request对象在不同接口间复用
  - 性能优化: 考虑缓存策略，避免重复查询
  - 事务管理: 合理使用事务，保证数据一致性

# 文档维护
documentation:
  - 接口变更时同步更新APIDOC注释
  - 重要业务逻辑添加详细注释
  - 保持代码注释与实际功能一致
  - 定期检查和更新过时的注释