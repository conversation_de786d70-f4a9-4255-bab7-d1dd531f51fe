# GEMINI.md

## 项目概述

这是一个名为 “crm-account” 的基于 Java 的 CRM (客户关系管理) 应用。它是一个多模块的 Maven 项目，其结构包含一个父 `pom.xml` 和几个子模块：

*   `crm-account-client`: 可能包含客户端代码，例如 API 接口 (facades)、数据传输对象 (DTO) 和枚举。
*   `crm-account-dao`: 处理数据访问，可能使用 MyBatis 进行持久化。它定义了数据访问对象 (DAO)、简单的 Java 对象 (PO) 和映射器 (mapper)。
*   `crm-account-service`: 包含应用的核心业务逻辑。它使用 Spring Boot 并集成了 Dubbo (用于 RPC)、Nacos (用于服务发现和配置) 和 Feign (用于声明式 REST 客户端) 等服务。
*   `crm-account-remote`: 该模块似乎是应用的入口点，负责启动 Spring Boot 应用并暴露服务。

该项目使用了多种技术，包括：

*   **后端:** Java 8, Spring Boot, Spring Cloud (Nacos, OpenFeign), Dubbo
*   **数据访问:** MyBatis, Oracle, MySQL
*   **缓存:** Redis, Caffeine
*   **消息传递:** RocketMQ
*   **构建:** Maven

## 构建和运行

要构建项目，您可以使用 Maven。可以在根目录中运行以下命令：

```bash
mvn clean package
```

要运行该应用，您可以执行 `start.sh` 脚本或运行 `crm-account-remote` 模块中的 `CrmAccountApplication` 类。

**待办事项:** `start.sh` 脚本未提供，因此运行应用的确切命令是未知的。

## 开发约定

*   项目遵循标准的 Maven 项目结构。
*   它使用 Lombok 来减少样板代码。
*   代码使用了 `@RestController`、`@EnableDubbo`、`@EnableFeignClients` 等注解，这表明它是一个现代的微服务架构。
*   项目使用 Log4j2 进行日志记录。
*   存在使用 TestNG 和 PowerMock 编写的单元测试。