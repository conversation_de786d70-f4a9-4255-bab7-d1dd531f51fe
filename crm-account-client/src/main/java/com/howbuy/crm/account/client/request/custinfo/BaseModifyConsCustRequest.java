/**
 * Copyright (c) 2023, <PERSON><PERSON><PERSON><PERSON> HOWBUY INVESTMENT MANAGEMENT Co., Ltd.
 * All right reserved.
 * <p>
 * THIS IS UNPUBLISHED PROPRIETARY SOURCE CODE OF HOWBUY INVESTMENT
 * MANAGEMENT CO., LTD.  THE CONTENTS OF THIS FILE MAY NOT BE DISCLOSED
 * TO THIRD PARTIES, COPIED OR DUPLICATED IN ANY FORM, IN WHOLE OR IN PART,
 * WITHOUT THE PRIOR WRITTEN PERMISSION OF HOWBUY INVESTMENT MANAGEMENT
 * CO., LTD.
 */
package com.howbuy.crm.account.client.request.custinfo;

import com.howbuy.commons.validator.MyValidation;
import com.howbuy.commons.validator.util.ValidatorTypeEnum;
import lombok.Data;

/**
 * @description: (新增、修改  操作投顾客户信息  base  请求类 )
 * <AUTHOR>
 * @date 2023/12/18 17:15
 * @since JDK 1.8
 */
@Data
public class BaseModifyConsCustRequest {

    /**
     * 非必须
     * 操作来源 [1-MQ]时，为[异常来源]，参考枚举类[{@link com.howbuy.crm.account.client.enums.custinfo.FullCustSourceEnum}]
     *        [2-菜单页面]时，为菜单名称
     */
    @MyValidation(validatorType = ValidatorTypeEnum.String, fieldName = "操作来源", isRequired = true)
    private String operateSource;

    /**
     * 非必须
     * 操作通道 1-MQ  2-菜单页面
     */
    @MyValidation(validatorType = ValidatorTypeEnum.String, fieldName = "操作通道", isRequired = true)
    private String operateChannel;

    /**
     *  操作人
     */
    @MyValidation(validatorType = ValidatorTypeEnum.String, fieldName = "操作人", isRequired = true)
    private String operator;

    /**
     * 客户类型枚举 0 机构客户 1 个人客户 2 产品客户
     */
    @MyValidation(validatorType = ValidatorTypeEnum.String, fieldName = "客户类型", isRequired = true)
    private String invsttype;

    /**
     * 客户名称
     */
    private String custname;

    /**
     * 手机号码-明文
     */
    private String mobile;

    /**
     * 手机号码2-明文
     */
    private String mobile2;


    /**
     * 邮箱-明文
     */
    private String email;

    /**
     * 邮箱2-明文
     */
    private String email2;

    /**
     * 座机号-明文
     */
    private String telNo;
    /**
     * 传真
     */
    private String fax;

    /**
     * 地址-明文
     */
    private String addr;

    /**
     * 地址2 -明文
     */
    private String addr2;
    /**
     * 邮政编码
     */
    private String postcode;

    /**
     * 邮政2 编码
     */
    private String postcode2;


    /**
     * 证件类型
     */
    private String idtype;

    /**
     * 证件号码-明文
     */
    private String idNo;

    /**
     * 证件地区码
     */
    private String idSignAreaCode;

    /**
     * 微信号
     */
    private String wechatcode;

    /**
     * 联系人姓名
     */
    private String linkman;

    /**
     * 联系人 座机号-明文
     */
    private String linkTel;
    /**
     * 联系人 手机号码-明文
     */
    private String linkMobile;
    /**
     * 联系人 邮箱-明文
     */
    private String linkEmail;
    /**
     * 联系人 地址-明文
     */
    private String linkAddr;

    /**
     * 经办人邮政编码
     */
    private String linkpostcode;

    /**
     * 省份代码
     */
    private String provcode;

    /**
     * 城市代码
     */
    private String citycode;

    /**
     * 县代码
     */
    private String countyCode;

    /**
     * 知道好买
     */
    private String knowhowbuy;
    /**
     * 知道好买细分
     */
    private String subknow;
    /**
     * 知道好买细分分类
     */
    private String subknowtype;

    /**
     *客户来源： PH0806F18
     * [数据库字段：newsourceno]
     */
    @MyValidation(validatorType = ValidatorTypeEnum.String, fieldName = "客户来源", isRequired = true)
    private String custsource;
    /**
     * 客户来源备注-投顾开发-电话营销
     */
    private String custSourceRemark;
    /**
     * 投资者性别(0：女；1：男)
     */
    private String gender;
    /**
     * 婚否
     */
    private String married;
    /**
     * 投顾客户评分
     */
    private String conscustgrade;
    /**
     * 沟通频率 Eg:2
     */
    private String visitfqcy;
    /**
     * 投资者学历
     */
    private String edulevel;
    /**
     * 投资者职业代码
     */
    private String vocation;
    /**
     * 个人年收入
     */
    private String pincome;
    /**
     * 家庭年收入
     */
    private String fincome;
    /**
     * 是否投资决策人
     */
    private String decisionflag;

    /**
     * 投资者生日
     */
    private String birthday;


    /**
     * 公司
     */
    private String company;
    /**
     * 投资者 单位电话
     */
    private String officetelno;
    /**
     * 兴趣爱好
     */
    private String interests;
    /**
     * 方便联系时段
     */
    private String contacttime;
    /**
     * 备注
     */
    private String remark;
    /**
     * 想了解的其他投资品种
     */
    private String otherinvest;

    /**
     * 希望参加的沙龙[hb_constant.PIncome]
     */
    private String salon;
    /**
     * 销售方向
     */
    private String saledirection;
    /**
     * 之前投资品种
     */
    private String beforeinvest;
    /**
     * 证件有限期
     * 是否长期有效0-否,1-是
     */
    private String validity;
    /**
     * 性质
     */
    private String nature;
    /**
     * 资质
     */
    private String aptitude;
    /**
     * 证件有限期日期
     */
    private String validitydt;

    /**
     * 证件有效起始日
     */
    private String validityst;

    /**
     * 经营范围
     */
    private String scopebusiness;


    /**
     * 投顾客户等级
     */
    private String conscustlvl;


    /**
     * 手机 地区码
     */
    private String mobileAreaCode;

    /**
     * 手机2 地区码
     */
    private String mobile2AreaCode;

    /**
     * 联系人手机 地区码
     */
    private String linkmobileAreaCode;



    /**
     * 国籍
     */
    private String nationCode;

    /**
     * 客户登记日期
     */
    private String acregdt;




}