package com.howbuy.crm.account.client.enums.hkcustinfo;

/**
 * @description: 香港客户资金来源国家代码枚举
 * <AUTHOR>
 * @date 2024/01/05 18:06
 * @since JDK 1.8
 */
public enum HkCustFundSourceCountryEnum {

	/**
	 * 01-香港
	 */
	BAR("01", "香港"),

	/**
	 * 02-中国
	 */
	WEB("02", "中国"),

	/**
	 * 03-美国
	 */
	PHONE("03", "美国"),

	/**
	 * 04-其他
	 */
	WAP("04", "其他"),
	;


	/**
	 * 编码
	 */
	private String code;

	/**
	 * 描述
	 */
	private String description;

	 HkCustFundSourceCountryEnum(String code, String description) {
		this.code = code;
		this.description = description;
	}


	/**
	 * 通过code获得
	 * 
	 * @param code
	 *            系统返回参数编码
	 * @return description 描述
	 */
	public static String getDescription(String code) {
		HkCustFundSourceCountryEnum statusEnum=getEnum(code);
		return statusEnum==null?null :statusEnum.getDescription();
	}

	/**
	 * 通过code直接返回 整个枚举类型
	 * 
	 * @param code
	 *            系统返回参数编码
	 * @return PreOccupyTypeEnum
	 */
	public static HkCustFundSourceCountryEnum getEnum(String code) {
		for(HkCustFundSourceCountryEnum statusEnum : HkCustFundSourceCountryEnum.values()){
			if(statusEnum.getCode().equals(code)){
				return statusEnum;
			}
		}
		return null;
	}




	public String getCode() {
		return code;
	}


	public String getDescription() {
		return description;
	}


}
