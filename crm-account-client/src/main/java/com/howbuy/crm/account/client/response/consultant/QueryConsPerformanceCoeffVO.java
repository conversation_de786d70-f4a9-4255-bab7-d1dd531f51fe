/**
 * Copyright (c) 2024, <PERSON><PERSON><PERSON><PERSON> HOWBUY INVESTMENT MANAGEMENT Co., Ltd.
 * All right reserved.
 * <p>
 * THIS IS UNPUBLISHED PROPRIETARY SOURCE CODE OF HOWBUY INVESTMENT
 * MANAGEMENT CO., LTD.  THE CONTENTS OF THIS FILE MAY NOT BE DISCLOSED
 * TO THIRD PARTIES, COPIED OR DUPLICATED IN ANY FORM, IN WHOLE OR IN PART,
 * WITHOUT THE PRIOR WRITTEN PERMISSION OF HOWBUY INVESTMENT MANAGEMENT
 * CO., LTD.
 */
package com.howbuy.crm.account.client.response.consultant;

import lombok.Getter;
import lombok.Setter;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;

/**
 * @description: 获取人员绩效系数表响应对象
 * <AUTHOR>
 * @date 2025-07-02 16:15:20
 * @since JDK 1.8
 */
@Getter
@Setter
public class QueryConsPerformanceCoeffVO implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 分配时间
     */
    private Date assignTime;

    /**
     * 投顾客户号
     */
    private String consCustNo;

    /**
     * 投顾code
     */
    private String consCode;

    /**
     * 来源类型
     */
    private String sourceType;

    /**
     * 客户折算系数
     */
    private BigDecimal custConversionCoeff;
    /**
     * 佣金系数起点
     */
    private String commissionCoeffStart;

    /**
     * 管理系数-分总
     */
    private BigDecimal manageCoeffSubtotal;

    /**
     * 管理系数-区副
     */
    private BigDecimal manageCoeffRegionalsubtotal;

    /**
     * 管理系数-区总
     */
    private BigDecimal manageCoeffRegionaltotal;

    /**
     * 存续B
     */
    private String cxb;

    /**
     * 是否计提公募 1：是，0：否
     */
    private String isBigV;
} 