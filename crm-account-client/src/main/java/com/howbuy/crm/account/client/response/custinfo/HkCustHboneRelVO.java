/**
 * Copyright (c) 2024, <PERSON><PERSON><PERSON><PERSON> HOWBUY INVESTMENT MANAGEMENT Co., Ltd.
 * All right reserved.
 * <p>
 * THIS IS UNPUBLISHED PROPRIETARY SOURCE CODE OF HOWBUY INVESTMENT
 * MANAGEMENT CO., LTD.  THE CONTENTS OF THIS FILE MAY NOT BE DISCLOSED
 * TO THIRD PARTIES, COPIED OR DUPLICATED IN ANY FORM, IN WHOLE OR IN PART,
 * WITHOUT THE PRIOR WRITTEN PERMISSION OF HOWBUY INVESTMENT MANAGEMENT
 * CO., LTD.
 */
package com.howbuy.crm.account.client.response.custinfo;

import lombok.Data;

import java.io.Serializable;

/**
 * @description: 香港客户一账通关系VO类
 * <AUTHOR>
 * @date 2024/1/11 14:55
 * @since JDK 1.8
 */

@Data
public class HkCustHboneRelVO implements Serializable {
    private static final long serialVersionUID = 5606000861737418003L;

    /**
     * 香港客户号
     */
    private String hkCustNo;
    /**
     * 一账通号
     */
    private String hboneNo;

}