/**
 * Copyright (c) 2024, <PERSON><PERSON><PERSON><PERSON> HOWBUY INVESTMENT MANAGEMENT Co., Ltd.
 * All right reserved.
 * <p>
 * THIS IS UNPUBLISHED PROPRIETARY SOURCE CODE OF HOWBUY INVESTMENT
 * MANAGEMENT CO., LTD.  THE CONTENTS OF THIS FILE MAY NOT BE DISCLOSED
 * TO THIRD PARTIES, COPIED OR DUPLICATED IN ANY FORM, IN WHOLE OR IN PART,
 * WITHOUT THE PRIOR WRITTEN PERMISSION OF HOWBUY INVESTMENT MANAGEMENT
 * CO., LTD.
 */
package com.howbuy.crm.account.client.response.custinfo;

import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;

import java.io.Serializable;
import java.util.Date;
import java.util.Map;

/**
 * @description: (香港账户中心开户订单操作流水信息VO)
 * <AUTHOR>
 * @date 2024/7/24 14:00
 * @since JDK 1.8
 */
@Data
public class HkAcctOptDealVO implements Serializable {

    private static final long serialVersionUID = 1L;


    /**
     * 订单号
     */
    private String dealNo;
    /**
     * 业务代码
     */
    private String busiCode;
    /**
     * 操作码
     * 操作代码 00-自助 01-申请提交 02-再次提交 03-初审通过 04-驳回至初审 05-驳回至客户 07-审核不通过 08-作废
     */
    private String operateCode;
    /**
     * 操作人
     */
    private String operator;
    /**
     * 香港客户号
     */
    private String hkCustNo;
    /**
     * 创建时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date stimestamp;
    /**
     * 驳回内容 json
     * Map['key-驳回字段', 'value=驳回原因']
     */
    private Map<String, String> rejectContent;
    /**
     * 当时客户状态 0-正常 1-注销 2-休眠 3-注册 4-开户申请成功
     */
    private String curCustState;
    /**
     * 当时订单申请日期
     */
    private String curAppDt;
    /**
     * 当时订单申请时间
     */
    private String curAppTm;

}