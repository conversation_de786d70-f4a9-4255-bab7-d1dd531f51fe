package com.howbuy.crm.account.client.response.consultant;

import lombok.Getter;
import lombok.Setter;
import lombok.ToString;

import java.io.Serializable;
import java.util.List;

/**
 * @description: 批量查询投顾简单信息响应结果
 * <AUTHOR>
 * @date 2025/3/10
 * @since JDK 1.8
 */
@Getter
@Setter
@ToString
public class BatchConsultantSimpleResponse implements Serializable {
    
    private static final long serialVersionUID = 1L;
    
    /**
     * 投顾信息列表
     */
    private List<ConsultantSimpleResponse> consultantList;
} 