/**
 * Copyright (c) 2024, <PERSON><PERSON><PERSON><PERSON> HOWBUY INVESTMENT MANAGEMENT Co., Ltd.
 * All right reserved.
 * <p>
 * THIS IS UNPUBLISHED PROPRIETARY SOURCE CODE OF HOWBUY INVESTMENT
 * MANAGEMENT CO., LTD.  THE CONTENTS OF THIS FILE MAY NOT BE DISCLOSED
 * TO THIRD PARTIES, COPIED OR DUPLICATED IN ANY FORM, IN WHOLE OR IN PART,
 * WITHOUT THE PRIOR WRITTEN PERMISSION OF HOWBUY INVESTMENT MANAGEMENT
 * CO., LTD.
 */
package com.howbuy.crm.account.client.response.custinfo;

import com.howbuy.crm.account.client.enums.hkcustinfo.HkPiggyAgreeSignTypeEnum;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.io.Serializable;
import java.util.List;

/**
 * @description: (客户海外储蓄罐协议流水信息VO)
 * <AUTHOR>
 * @date 2024/7/24 14:00
 * @since JDK 1.8
 */
@Data
@EqualsAndHashCode(callSuper = true)
public class HkPiggyAgreementDealVO extends HkAcctBaseDealVO implements Serializable {

    private static final long serialVersionUID = 1L;


    /**
     * 外部订单号
     */
    private String outOrderId;
    /**
     * 储蓄罐基金代码
     */
    private List<String> piggyFundCodeList;
    /**
     * 协议签署方式 1-线下自主申请开通 2-线上自主申请开通 3-到期自动续期 4-底层基金更换同意
     */
    private String agreementSignType;
    /**
     * 协议签署日期 yyyyMMdd
     */
    private String agreementSignDt;
    /**
     * 协议终止方式 1-线下自主申请关闭 2-线上自主申请关闭 3-底层基金更换未同意 4-底层基金更换不同意
     */
    private String agreementCancelType;
    /**
     * 协议终止日期 yyyyMMdd
     */
    private String agreementCancelDt;
    /**
     * 协议文件列表
     */
    private List<HkAgreementFileVO> filePathList;


    /**
     * 协议签署方式描述
     * @return
     */
    public String getAgreementSignTypeDesc() {
//        取海外储蓄罐协议流水的【协议签署方式】
//        当【业务类型=协议签署】时，枚举值：线下自主申请开通/线上自主申请开通/到期自动续期/同意底层基金更换
//        当【业务类型=协议终止】时，枚举值：线下自主申请关闭/线上自主申请关闭/未同意底层基金更换/不同意底层基金更换
        HkPiggyAgreeSignTypeEnum enumType = HkPiggyAgreeSignTypeEnum.getEnum(agreementSignType);
        if(enumType != null){
            return enumType.translateSignType(getBusiCode());
        }
        return null;
    }

}