package com.howbuy.crm.account.client.facade.consultant;

import com.howbuy.crm.account.client.request.consultant.BatchConsultantSimpleRequest;
import com.howbuy.crm.account.client.response.Response;
import com.howbuy.crm.account.client.response.consultant.BatchConsultantSimpleResponse;

/**
 * @description: 批量查询投顾简单信息服务
 * <AUTHOR>
 * @date 2025/3/10
 * @since JDK 1.8
 */
public interface BatchConsultantSimpleFacade {

    /**
     * @api {DUBBO} com.howbuy.crm.account.client.facade.consultant.BatchConsultantSimpleFacade.batchQueryConsultantSimple() batchQueryConsultantSimple()
     * @apiVersion 1.0.0
     * @apiGroup BatchConsultantSimpleFacade
     * @apiName batchQueryConsultantSimple()
     * @apiDescription 批量查询投顾简单信息
     * @apiParam (请求体) {Array} consCodeList 投顾编号列表
     * @apiParamExample 请求体示例
     * {"consCodeList":["123456","789012"]}
     * @apiSuccess (响应结果) {String} code 状态码
     * @apiSuccess (响应结果) {String} description 描述信息
     * @apiSuccess (响应结果) {Object} data 数据封装
     * @apiSuccess (响应结果) {Array} data.consultantList 投顾信息列表
     * @apiSuccess (响应结果) {String} data.consultantList.consCode 理财顾问代码
     * @apiSuccess (响应结果) {String} data.consultantList.consName 理财顾问名称
     * @apiSuccess (响应结果) {String} data.consultantList.consLevel 理财顾问级别
     * @apiSuccess (响应结果) {String} data.consultantList.consStatus 理财顾问状态1有效，0无效
     * @apiSuccess (响应结果) {String} data.consultantList.isVirtual 是否虚拟投顾（1 是/0 否）
     * @apiSuccess (响应结果) {String} data.consultantList.outletCode 所属理财中心
     * @apiSuccess (响应结果) {String} data.consultantList.teamCode 所属小组
     * @apiSuccess (响应结果) {String} data.consultantList.mobile 手机
     * @apiSuccess (响应结果) {String} data.consultantList.picAddr 投顾照片链接地址
     * @apiSuccess (响应结果) {String} data.consultantList.codePicAddr 企业微信二维码地址
     * @apiSuccess (响应结果) {String} data.consultantList.position 理财顾问职位
     * @apiSuccess (响应结果) {String} data.consultantList.email 电子邮件
     * @apiSuccess (响应结果) {String} data.consultantList.centerOrgCode 所属中心
     * @apiSuccessExample 响应结果示例
     * {"code":"0000","description":"成功","data":{"consultantList":[{"consCode":"123456","consName":"张三","consLevel":"A","consStatus":"1","isVirtual":"0","outletCode":"SH01","teamCode":"T001","mobile":"13800138000","picAddr":"http://xxx","codePicAddr":"http://xxx","position":"高级理财顾问","email":"<EMAIL>","centerOrgCode":"1"},{"consCode":"789012","consName":"李四","consLevel":"B","consStatus":"1","isVirtual":"0","outletCode":"SH02","teamCode":"T002","mobile":"13900139000","picAddr":"http://xxx","codePicAddr":"http://xxx","position":"理财顾问","email":"<EMAIL>","centerOrgCode":"1"}]}}
     */
    Response<BatchConsultantSimpleResponse> batchQueryConsultantSimple(BatchConsultantSimpleRequest request);
} 