package com.howbuy.crm.account.client.response.custinfo;

import com.howbuy.crm.account.client.enums.custinfo.HkAcctCustStatusEnum;
import lombok.Data;

import java.io.Serializable;

/**
 * @description:(港客户信息-单个客户详细信息)
 * @param
 * @return
 * @author: haoran.zhang
 * @date: 2023/12/19 19:17
 * @since JDK 1.8
 */
@Data
public class HkAcctCustDetailInfoVO implements Serializable {

    private static final long serialVersionUID = 3148694702956551060L;

    /**
     * CRM 客户号
     */
    private String custNo;

    /**
     * 香港交易账号
     */
    private String hkTxAcctNo;

    /**
     * ebrokerId
     */
    private String ebrokerId;

    /**
     * 香港客户中文名
     */
    private String custChineseName;

    /**
     * 香港客户英文名
     */
    private String custEnName;

    //账户中心属性


    /**
     * {@link HkAcctCustStatusEnum}
     * 香港客户状态-   客户状态 0-正常 1-注销 2-休眠 3-注册4-开户申请成功
     */
    private String hkCustStatus;

    /**
     *证件号码
     */
    private String idNoDigest;
    /**
     *证件号码
     */
    private String idNoMask;

    /**
     * 证件地区码-身份证签发地区编码
     */
    private String idSignAreaCode;

    /**
     * 证件类型
     */
    private String idType;
    /**
     * 投资者类型 0-机构,1-个人,2-产品户
     */
    private String invstType;
    /**
     * 手机号码区号
     */
    private String mobileAreaCode;
    /**
     * 手机号码
     */
    private String mobileDigest;
    /**
     * 手机号码
     */
    private String mobileMask;
    /**
     * 手机号码 验证状态
     * 0:未验证 1:已验证
     */
    private String mobileVerifyStatus;
    /**
     * 邮件
     */
    private String emailDigest;
    /**
     * 邮件
     */
    private String emailMask;
    /**
     * 邮件 验证状态
     * 0:未验证 1:已验证
     */
    private String emailVerifyStatus;

    /**
     * 关联的一账通号
     */
    private String hboneNo;

    /**
     * 证件有效期
     */
    private String  idValidityEnd	;
    /**
     * 证件是否长期有效 0-否 1-是
     */
    private String  idAlwaysValidFlag	;
    /**
     * 证件图片上传状态0-未上传 1-已上传
     */
    private String  idImageUploadStatus	;
    /**
     * 国籍
     */
    private String  nationality	;
    /**
     * 出生日期
     */
    private String  birthday	;
    /**
     * 性别 0-女，1-男，2-非自然人
     */
    private String  gender	;
    /**
     * 职业
     */
    private String  vocation	;
    /**
     * 客户登录密码状态0正常状态 1重置状态 2-未设置
     */
    private String  custLoginPasswdType	;
    /**
     * 客户交易密码状态0正常状态 1重置状态 2-未设置
     */
    private String  custTxPasswdType	;
    /**
     * 风险承受能力评估级别 0,1,2,3,4,5
     */
    private String  riskToleranceLevel	;
    /**
     * 衍生金融工具知识0-无 1-有
     */
    private String  derivativeKnowledge	;
    /**
     * 	风险评测日期	['yyyyMMdd']
     */
    private String  riskToleranceDate	;
    /**
     * 风险评测有效期	['yyyyMMdd']
     */
    private String  riskToleranceTerm	;
    /**
     * 风险评测分数
     */
    private String  riskToleranceScore	;
    /**
     * 投资者资质PRO-投资者资质专业/NORMAL-投资者资质普通
     */
    private String  investorQualification	;
    /**
     * 投资者资质认证日期
     */
    private String  investorQualificationDate	;
    /**
     * 现住址国家编码
     */
    private String  residenceCountryCode	;
    /**
     * 现住址省份编码
     */
    private String  residenceProvCode	;
    /**
     * 现住址城市编码
     */
    private String  residenceCityCode	;
    /**
     * 现住址地区编码
     */
    private String  residenceCountyCode	;
    /**
     * 现住址的中文地址-摘要
     */
    private String  residenceCnAddrDigest	;
    /**
     * 现住址的中文地址-掩码
     */
    private String  residenceCnAddrMask	;
    /**
     * 现住址英文国家名称
     */
    private String  residenceEnCountry	;
    /**
     * 现住址英文省份名称
     */
    private String  residenceEnProv	;
    /**
     * 现住址英文城市名称
     */
    private String  residenceEnCity	;
    /**
     * 现住址英文地区名称
     */
    private String  residenceEnCounty	;
    /**
     * 现住址城镇名称
     */
    private String  residenceTown	;
    /**
     * 现住址州县名称
     */
    private String  residenceState	;
    /**
     * 现住址的英文地址-摘要
     */
    private String  residenceEnAddrDigest	;
    /**
     * 现住址的英文地址-掩码
     */
    private String  residenceEnAddrMask	;
    /**
     * 通讯地址国家编码
     */
    private String  mailingCountryCode	;
    /**
     * 通讯地址省份编码
     */
    private String  mailingProvCode	;
    /**
     * 通讯地址城市编码
     */
    private String  mailingCityCode	;
    /**
     * 通讯地址地区编码
     */
    private String  mailingCountyCode	;
    /**
     * 通讯地址的中文地址-摘要
     */
    private String  mailingCnAddrDigest	;
    /**
     * 通讯地址的中文地址-掩码
     */
    private String  mailingCnAddrMask	;
    /**
     * 通讯地址英文国家名称
     */
    private String  mailingEnCountry	;
    /**
     * 通讯地址英文省份名称
     */
    private String  mailingEnProv	;
    /**
     * 通讯地址英文城市名称
     */
    private String  mailingEnCity	;
    /**
     * 通讯地址英文地区名称
     */
    private String  mailingEnCounty	;
    /**
     * 通讯地址城镇名称
     */
    private String  mailingTown	;
    /**
     * 通讯地址州县名称
     */
    private String  mailingState	;
    /**
     * 通讯地址的英文地址-摘要
     */
    private String  mailingEnAddrDigest	;
    /**
     * 通讯地址的英文地址-掩码
     */
    private String  mailingEnAddrMask	;
    /**
     * 就业状态	['01-雇主', '02-全职', '03-兼职', '04-主妇', '05-学生', '06-退休', '07-非在职']
     */
    private String  emplStatus	;
    /**
     * 就业年收入	['01', '02', '03', '04', '05']
     */
    private String  emplIncLevel ;

    /**
     * 资产证明有效期
     */
    private String assetCertExpiredDate;



    /**
     * 客户 是否开香港户: 香港客户号不为空 且 (客户状态为“正常” 或 客户状态为“休眠”)
     * @return
     */
    public  boolean isOpHkAcct(){
        return  !"".equals(hkTxAcctNo) && (HkAcctCustStatusEnum.NORMAL.getCode().equals(hkCustStatus) || HkAcctCustStatusEnum.DORMANT.getCode().equals(hkCustStatus));
    }


    /**
     * 客户名称 优先返回中文名 为空则返回英文名
     * @return
     */
    public String getUsedCustName(){
        return  (custChineseName==null || custChineseName.isEmpty()) ? custEnName : custChineseName;
    }

}
