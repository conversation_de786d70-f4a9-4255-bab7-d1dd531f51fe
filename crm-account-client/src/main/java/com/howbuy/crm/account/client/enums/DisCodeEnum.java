package com.howbuy.crm.account.client.enums;

/**
 * @description:(分销渠道 的 枚举)
 * @param 	
 * @return
 * @author: haoran.zhang
 * @date: 2023/12/8 16:43
 * @since JDK 1.8
 */
public enum DisCodeEnum {

	/**
	 * HB000A001-好买
	 */
	HOWBUY("HB000A001", "好买",DisChannelCodeEnum.HOWBUY),

	/**
	 * FOF201710-机构
	 */
	FOF("FOF201710", "机构",DisChannelCodeEnum.HOWBUY),

	/**
	 * HZ000N001-好臻分销
	 */
	HZ("HZ000N001", "好臻",DisChannelCodeEnum.HZ)
	;

	/**
	 * 编码
	 */
	private String code;

	/**
	 * 描述
	 */
	private String description;

    /**
     * channelEnum 该属性 目前不再核心业务中使用。
     * DisCodeEnum vs DisChannelCodeEnum 的关联关系。基于DisCodeUtil来实现
     */
    private DisChannelCodeEnum channelEnum;//产品channelEnum

	private DisCodeEnum(String code, String description,DisChannelCodeEnum channelEnum) {
		this.code = code;
		this.description = description;
		this.channelEnum=channelEnum;
	}

	/**
	 * 通过code获得
	 * 
	 * @param code
	 *            系统返回参数编码
	 * @return description 描述
	 */
	public static String getDescription(String code) {
		DisCodeEnum statusEnum=getEnum(code);
		return statusEnum==null?null :statusEnum.getDescription();
	}

	/**
	 * 通过code直接返回 整个枚举类型
	 * 
	 * @param code
	 *            系统返回参数编码
	 * @return PreOccupyTypeEnum
	 */
	public static DisCodeEnum getEnum(String code) {
		for(DisCodeEnum statusEnum : DisCodeEnum.values()){
			if(statusEnum.getCode().equals(code)){
				return statusEnum;
			}
		}
		return null;
	}


	public String getCode() {
		return code;
	}


	public String getDescription() {
		return description;
	}


    public DisChannelCodeEnum getChannelEnum() {
        return channelEnum;
    }

    public void setChannelEnum(DisChannelCodeEnum channelEnum) {
        this.channelEnum = channelEnum;
    }
}
