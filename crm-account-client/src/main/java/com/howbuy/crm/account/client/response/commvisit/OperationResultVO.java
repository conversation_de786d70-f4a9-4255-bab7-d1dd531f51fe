package com.howbuy.crm.account.client.response.commvisit;

import lombok.Getter;
import lombok.Setter;

import java.io.Serializable;

/**
 * @description: 操作结果VO
 * <AUTHOR>
 * @date 2025-04-08 10:26:00
 * @since JDK 1.8
 */
@Getter
@Setter
public class OperationResultVO implements Serializable {

    private static final long serialVersionUID = 1L;
    /**
     * 是否成功
     */
    private boolean success;
    
    /**
     * 消息
     */
    private String message;
    
    public static OperationResultVO success() {
        OperationResultVO result = new OperationResultVO();
        result.setSuccess(true);
        return result;
    }
    
    public static OperationResultVO fail(String message) {
        OperationResultVO result = new OperationResultVO();
        result.setSuccess(false);
        result.setMessage(message);
        return result;
    }
} 