package com.howbuy.crm.account.client.facade.consultant;

import com.howbuy.crm.account.client.request.consultant.ConsultantSimpleRequest;
import com.howbuy.crm.account.client.response.Response;
import com.howbuy.crm.account.client.response.consultant.ConsultantSimpleResponse;

/**
 * @description: 投顾简单信息查询服务
 * <AUTHOR>
 * @date 2025/3/10
 * @since JDK 1.8
 */
public interface ConsultantSimpleFacade {

    /**
     * @api {DUBBO} com.howbuy.crm.account.client.facade.consultant.ConsultantSimpleFacade.queryConsultantSimple() queryConsultantSimple()
     * @apiVersion 1.0.0
     * @apiGroup ConsultantSimpleFacade
     * @apiName queryConsultantSimple()
     * @apiDescription 查询投顾简单信息
     * @apiParam (请求体) {String} consCode 投顾编号
     * @apiParamExample 请求体示例
     * {"consCode":"123456"}
     * @apiSuccess (响应结果) {String} code 状态码
     * @apiSuccess (响应结果) {String} description 描述信息
     * @apiSuccess (响应结果) {Object} data 数据封装
     * @apiSuccess (响应结果) {String} data.consCode 理财顾问代码
     * @apiSuccess (响应结果) {String} data.consName 理财顾问名称
     * @apiSuccess (响应结果) {String} data.consLevel 理财顾问级别
     * @apiSuccess (响应结果) {String} data.consStatus 理财顾问状态1有效，0无效
     * @apiSuccess (响应结果) {String} data.isVirtual 是否虚拟投顾（1 是/0 否）
     * @apiSuccess (响应结果) {String} data.outletCode 所属理财中心
     * @apiSuccess (响应结果) {String} data.teamCode 所属小组
     * @apiSuccess (响应结果) {String} data.mobile 手机
     * @apiSuccess (响应结果) {String} data.picAddr 投顾照片链接地址
     * @apiSuccess (响应结果) {String} data.codePicAddr 企业微信二维码地址
     * @apiSuccess (响应结果) {String} data.position 理财顾问职位
     * @apiSuccess (响应结果) {String} data.email 电子邮件
     * @apiSuccess (响应结果) {String} data.centerOrgCode 所属中心
     * @apiSuccessExample 响应结果示例
     * {"code":"0000","description":"成功","data":{"consCode":"123456","consName":"张三","consLevel":"A","consStatus":"1","isVirtual":"0","outletCode":"SH01","teamCode":"T001","mobile":"13800138000","picAddr":"http://xxx","codePicAddr":"http://xxx","position":"高级理财顾问","email":"<EMAIL>","centerOrgCode":"1"}}
     */
    Response<ConsultantSimpleResponse> queryConsultantSimple(ConsultantSimpleRequest request);
} 