/**
 * Copyright (c) 2024, <PERSON><PERSON><PERSON><PERSON> HOWBUY INVESTMENT MANAGEMENT Co., Ltd.
 * All right reserved.
 * <p>
 * THIS IS UNPUBLISHED PROPRIETARY SOURCE CODE OF HOWBUY INVESTMENT
 * MANAGEMENT CO., LTD.  THE CONTENTS OF THIS FILE MAY NOT BE DISCLOSED
 * TO THIRD PARTIES, COPIED OR DUPLICATED IN ANY FORM, IN WHOLE OR IN PART,
 * WITHOUT THE PRIOR WRITTEN PERMISSION OF HOWBUY INVESTMENT MANAGEMENT
 * CO., LTD.
 */
package com.howbuy.crm.account.client.facade.consultantinfo;

import com.howbuy.crm.account.client.request.consultantinfo.QueryConsultantInfoRequest;
import com.howbuy.crm.account.client.response.Response;
import com.howbuy.crm.account.client.response.consultantinfo.CmConstantInfoListVO;
import com.howbuy.crm.account.client.response.consultantinfo.CmConsultantInfo;

/**
 * @description: 投顾信息服务
 * <AUTHOR>
 * @date 2024/9/6 15:22
 * @since JDK 1.8
 */
public interface ConsultantInfoFacade {

    /**
     * @api {DUBBO}  com.howbuy.crm.account.client.facade.consultantinfo.ConsultantInfoFacade.queryConsultantInfo(request) queryConsultantInfo()
     * @apiVersion 1.0.0
     * @apiGroup ConsultantInfoFacade
     * @apiName queryConsultantInfo()
     * @apiDescription 查询投顾信息
     * @apiParam (请求体) {String} userId 企业微信账号（企微userId）
     * @apiParam (请求体) {Array} consCodeList 投顾consCodeList
     * @apiParamExample 请求体示例
     * {"userId":"31","consCodeList":["E"]}
     * @apiSuccess (响应结果) {String} code 状态码
     * @apiSuccess (响应结果) {String} description 描述信息
     * @apiSuccess (响应结果) {Object} data 数据封装
     * @apiSuccess (响应结果) {Array} data.cmConsultantInfoList 理财顾问代码
     * @apiSuccess (响应结果) {String} data.cmConsultantInfoList.conscode 理财顾问代码
     * @apiSuccess (响应结果) {String} data.cmConsultantInfoList.consname 理财顾问名称
     * @apiSuccess (响应结果) {String} data.cmConsultantInfoList.wechatconscode 企业微信账号
     * @apiSuccessExample 响应结果示例
     * {"code":"PdNttt","data":{"cmConsultantInfoList":[{"wechatconscode":"QQS0","consname":"B","conscode":"pXYIibBL"}]},"description":"RMieKS"}
     */
    Response<CmConstantInfoListVO> queryConsultantInfo(QueryConsultantInfoRequest request);

}
