/**
 * Copyright (c) 2023, <PERSON><PERSON><PERSON><PERSON> HOWBUY INVESTMENT MANAGEMENT Co., Ltd.
 * All right reserved.
 * <p>
 * THIS IS UNPUBLISHED PROPRIETARY SOURCE CODE OF HOWBUY INVESTMENT
 * MANAGEMENT CO., LTD.  THE CONTENTS OF THIS FILE MAY NOT BE DISCLOSED
 * TO THIRD PARTIES, COPIED OR DUPLICATED IN ANY FORM, IN WHOLE OR IN PART,
 * WITHOUT THE PRIOR WRITTEN PERMISSION OF HOWBUY INVESTMENT MANAGEMENT
 * CO., LTD.
 */
package com.howbuy.crm.account.client.enums;

/**
 * @description: 异常码定义枚举
 * <AUTHOR>
 * @date 2023/11/13 18:06
 * @since JDK 1.8
 */
public enum ExceptionCodeEnum {

    /**
     * "C040000","成功"
     */
    SUCCESS("C040000", "成功"),
    /**
     * "C049999","系统异常"
     */
    SYSTEM_ERROR("C049999", "系统异常"),
    /**
     * "C040999","数据库异常"
     */
    DB_ERROR("C049998", "数据库异常"),

    /*=========业务异常码从 C040000 开始=========*/
    /**
     * "C040001","参数错误"
     */
    PARAMS_ERROR("C040001", "参数错误"),

    /**
     * "C040002","线程被中断"
     */
    THREAD_INTERRUPTED("C040002", "线程被中断")

    ;

    /**
     * 异常码
     */
    private String code;
    /**
     * 异常描述
     */
    private String desc;

    private ExceptionCodeEnum(String code, String desc) {
        this.code = code;
        this.desc = desc;
    }

    public String getCode() {
        return code;
    }

    public String getDesc() {
        return desc;
    }

    /**
     * @description: 根据错误码匹配枚举
     * @param code 异常码
     * @return com.howbuy.crm.account.client.enums.ExceptionCodeEnum
     * @author: hongdong.xie
     * @date: 2023/11/13 18:07
     * @since JDK 1.8
     */
    public static ExceptionCodeEnum getExceptionCodeEnum(String code) {
        for (ExceptionCodeEnum exceptionCodeEnum : ExceptionCodeEnum.values()) {
            if (exceptionCodeEnum.getCode().equals(code)) {
                return exceptionCodeEnum;
            }
        }
        return null;
    }

}
