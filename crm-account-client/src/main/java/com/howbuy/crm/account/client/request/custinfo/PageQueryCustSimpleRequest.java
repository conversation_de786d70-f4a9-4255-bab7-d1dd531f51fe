/**
 * Copyright (c) 2024, <PERSON><PERSON><PERSON><PERSON> HOWBUY INVESTMENT MANAGEMENT Co., Ltd.
 * All right reserved.
 * <p>
 * THIS IS UNPUBLISHED PROPRIETARY SOURCE CODE OF HOWBUY INVESTMENT
 * MANAGEMENT CO., LTD.  THE CONTENTS OF THIS FILE MAY NOT BE DISCLOSED
 * TO THIRD PARTIES, COPIED OR DUPLICATED IN ANY FORM, IN WHOLE OR IN PART,
 * WITHOUT THE PRIOR WRITTEN PERMISSION OF HOWBUY INVESTMENT MANAGEMENT
 * CO., LTD.
 */
package com.howbuy.crm.account.client.request.custinfo;

import com.howbuy.crm.account.client.request.PageRequest;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.io.Serializable;
import java.util.List;

/**
 * <AUTHOR>
 * @description: (请在此添加描述)
 * @date 2024/9/24 17:25
 * @since JDK 1.8
 */
@Data
@EqualsAndHashCode(callSuper = true)
public class PageQueryCustSimpleRequest extends PageRequest implements Serializable {


    private static final long serialVersionUID = 354589065741215264L;

    /**
     * 是否根据部门查询
     */
    private boolean isByDept;

    /**
     * 投顾编号
     */
    private String conscode;

    /**
     * 搜索内容
     */
    private String searchContent;

}
