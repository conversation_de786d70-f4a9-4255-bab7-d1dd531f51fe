package com.howbuy.crm.account.client.enums.custinfo;

import java.util.Arrays;
import java.util.List;
import java.util.stream.Collectors;

/**
 * @description:( 客户消息处理的 来源
 * 其中 异常客户来源渠道  1-香港注册、2-香港开户、3-香港客户信息同步、4-香港客户号/一账通绑定、5-一账通实名、6-一账通注册、7-分销开户、8-一账通客户信息同步、9-香港开户（CRM）)
 * @param
 * @return
 * @author: haoran.zhang
 * @date: 2023/12/8 20:30
 * @since JDK 1.8
 */
public enum FullCustSourceEnum {

    /**
     * 1-香港注册
     */
    HK_REGISTER("1", "香港注册",true),
    /**
     * 2-香港线上开户
     */
    HK_ONLINE_OPEN_ACCOUNT("2", "香港线上开户", true),

    /**
     * 18-香港线下开户
     */
    HK_OFFLINE_OPEN_ACCOUNT("18", "香港线下开户", true),

    /**
     * 3-香港客户信息同步
     */
    HK_CUST_INFO_SYNC("3", "香港客户信息同步",true),
    /**
     * 4-香港客户号/一账通绑定
     */
    HK_CUST_NO_BIND("4", "香港客户号/一账通绑定",true),
    /**
     * 5-实名开通一账通 | 一账通实名
     */
    HBONE_REAL_NAME_REGISTER("5", "一账通实名",true),
    /**
     * 6-一账通注册   | 非实名开通一账通
     */
    HBONE_REGISTER("6", "一账通注册",true),
    /**
     * 7-分销开户
     */
    DISTRIBUTION_OPEN_ACCOUNT("7", "分销开户",true),
    /**
     * 8-一账通客户信息同步
     */
    HBONE_CUST_INFO_SYNC("8", "一账通客户信息同步",true),
    /**
     * 9-香港开户（CRM）
     */
    HK_OPEN_ACCOUNT_CRM("9", "香港开户（CRM）",true),



    /**
     * 10-香港注销
     */
    HK_UN_REGISTER("10", "香港注销",false),


    /**
     * 香港客户手机号变更
     */
    HK_CUST_MOBILE_CHANGE("11", "香港客户手机号变更",true),

    /**
     * 香港客户证件信息变更
     */
    HK_CUST_ID_CHANGE("12", "香港客户证件信息变更",true),


    /**
     * 13-页面菜单 操作
     */
    PAGE_MENU_OPERATE("13", "页面菜单 操作",false),


    /**
     * 一账通 客户手机号变更
     */
    HBONE_CUST_MOBILE_CHANGE("14", "一账通客户手机号变更",true),

    /**
     * 一账通 注销
     * 或
     * 一账通销户
     */
    HBONE_CLOSE("15", "一账通注销",false),

    /**
     * 一账通  信息变更 ， 实名信息变更
     */
    HBONE_CUST_REAL_NAME_CHANGE("16", "一账通客户实名消息",true),

    /**
     * 17-MGM
     */
    MGM("17", "MGM",true),

    ;



    /**
     * 编码
     **/
    private String code;
    /**
     * 描述
     * */
    private String description;

    /**
     * 是否需要 异常 分析
     */
    private boolean needAnalyse;

    FullCustSourceEnum(String code, String description, boolean needAnalyse){
        this.code=code;
        this.description=description;
        this.needAnalyse=needAnalyse;
    }



    /**
     * @description:(获取 需要分析 的  来源枚举列表 )
     * @param
     * @return java.util.List<com.howbuy.crm.account.client.enums.custinfo.AbnormalCustSourceEnum>
     * @author: haoran.zhang
     * @date: 2023/12/22 10:12
     * @since JDK 1.8
     */
    public static List<FullCustSourceEnum> getNeedAnaylyseSourceList(){
        return Arrays.stream(FullCustSourceEnum.values()).filter(s->s.needAnalyse).collect(Collectors.toList());
    }

    /**
     * 通过code获得
     * @param code	系统返回参数编码
     * @return description 描述
     */
    public static String getDescription(String code){
        FullCustSourceEnum statusEnum=getEnum(code);
        return  statusEnum==null?null:statusEnum.getDescription();
    }

    /**
     * 通过code直接返回 整个枚举类型
     * @param code 系统返回参数编码
     * @return BaseConstantEnum
     */
    public static FullCustSourceEnum getEnum(String code){
        for(FullCustSourceEnum b: FullCustSourceEnum.values()){
            if(b.getCode().equals(code)){
                return b;
            }
        }
        return null;
    }

    public String getCode() {
        return code;
    }


    public String getDescription() {
        return description;
    }

    public boolean isNeedAnalyse() {
        return needAnalyse;
    }
}
