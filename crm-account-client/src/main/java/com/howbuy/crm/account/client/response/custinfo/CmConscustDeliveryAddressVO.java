package com.howbuy.crm.account.client.response.custinfo;

import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;

/**
 * 投顾客户收货地址
 */
@Data
public class CmConscustDeliveryAddressVO implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
    * ID
    */
    private BigDecimal id;

    /**
    * 投顾客户号
    */
    private String conscustno;

    /**
    * 收货人姓名
    */
    private String receiverName;

    /**
     * 收货人手机号区号
     */
    private String mobileAreaCode;

    /**
    * 收货人手机号摘要
    */
    private String mobileDigest;

    /**
    * 收货人手机号掩码
    */
    private String mobileMask;

    /**
    * 收货人手机号密文
    */
    private String mobileCipher;

    /**
     * 国家/地区编码
     */
    private String nationCode;

    /**
    * 省编码
    */
    private String provCode;

    /**
    * 市编码
    */
    private String cityCode;

    /**
    * 区编码
    */
    private String countyCode;

    /**
    * 详细地址摘要
    */
    private String addrDigest;

    /**
    * 详细地址掩码
    */
    private String addrMask;

    /**
    * 详细地址密文
    */
    private String addrCipher;

    /**
    * 备注
    */
    private String remark;

    /**
    * 记录有效状态（1-正常  0-删除）
    */
    private String recStat;

    /**
    * 创建人
    */
    private String creator;

    /**
    * 创建时间
    */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date createTimestamp;

    /**
    * 修改人
    */
    private String modifier;

    /**
    * 修改时间
    */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss.SSS", timezone = "GMT+8")
    private Date modifyTimestamp;

    /**
     * 省名称
     */
    private String provName;

    /**
     * 市名称
     */
    private String cityName;

    /**
     * 县名称
     */
    private String countyName;

    /**
     * 国家/地区名称
     */
    private String nationName;

}