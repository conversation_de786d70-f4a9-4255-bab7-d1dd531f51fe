package com.howbuy.crm.account.client.enums.custinfo;

/**
 * @description:
 * (异常客户类型描述  1-匹配到多个投顾客户号、2-匹配到的投顾客户号已被占用、3-手机号相同，证件类型/证件号不匹配、4-证件相同，但手机号不匹配、
 * 7-香港开户证件与一账通证件不一致、6-同时绑定香港客户号时，投顾客户号/香港客户号被占用、5-CRM重复客户预警、8-两边绑定的投顾客户号不一致、
 * 9-客户姓名相同，证件类型/证件号不匹配、10-证件相同，但客户姓名不匹配、11-分销开户证件与香港实名证件不一致、
 * 12-同时绑定一账通时，投顾客户号/一账通号被占用、13-一账通实名证件与香港实名证件不一致、、)
 * @param
 * @return
 * @author: haoran.zhang
 * @date: 2023/12/8 20:30
 * @since JDK 1.8
 */
public enum AbnormaSceneTypeEnum {

    /**
     * 1-匹配到多个投顾客户号
     */
    MATCH_MULTIPLE_CUST_NO("1", "匹配到多个投顾客户号", null),
    /**
     * 2-匹配到的投顾客户号已被占用
     */
    MATCH_CUST_NO_OCCUPIED("2", "匹配到的投顾客户号已被占用", null),
    /**
     * 3-手机号相同，证件类型/证件号不匹配
     */
    PHONE_SAME_ID_TYPE_ID_NO_NOT_MATCH("3", "手机号相同，证件类型/证件号不匹配", null),
    /**
     * 4-证件相同，但手机号不匹配
     */
    ID_TYPE_ID_NO_SAME_PHONE_NOT_MATCH("4", "证件相同，但手机号不匹配", null),
    /**
     * 5-CRM重复客户预警
     */
    CRM_REPEAT_CUST_WARNING("5", "CRM重复客户预警", null),
    /**
     * 6-同时绑定香港客户号时，投顾客户号/香港客户号被占用
     */
    BIND_HK_CUST_NO_OCCUPIED("6", "同时绑定香港客户号时，投顾客户号/香港客户号被占用", null),
    /**
     * 7-香港开户证件与一账通证件不一致
     */
    HK_OPEN_ACCOUNT_ID_TYPE_ID_NO_NOT_MATCH("7", "香港开户证件与一账通证件不一致", null),
    /**
     * 8-两边绑定的投顾客户号不一致
     */
    BIND_CUST_NO_NOT_MATCH("8", "两边绑定的投顾客户号不一致", null),
    /**
     * 9-客户姓名相同，证件类型/证件号不匹配
     */
    CUST_NAME_SAME_ID_TYPE_ID_NO_NOT_MATCH("9", "客户姓名相同，证件类型/证件号不匹配", null),
    /**
     * 10-证件相同，但客户姓名不匹配
     */
    ID_TYPE_ID_NO_SAME_CUST_NAME_NOT_MATCH("10", "证件相同，但客户姓名不匹配", null),
    /**
     * 11-分销开户证件与香港实名证件不一致
     */
    //    DISTRIBUTION_OPEN_ACCOUNT_ID_TYPE_ID_NO_NOT_MATCH("11", "分销开户证件与香港实名证件不一致"),
    /**
     * 12-同时绑定一账通时，投顾客户号/一账通号被占用
     */
    BIND_HBONE_CUST_NO_OCCUPIED("12", "同时绑定一账通时，投顾客户号/一账通号被占用", null),
    /**
     * 13-一账通实名证件与香港实名证件不一致
     */
    HBONE_REAL_NAME_ID_TYPE_ID_NO_NOT_MATCH("13", "一账通实名证件与香港实名证件不一致", null),
    /**
     * 14-一账通找到投顾客户号，一账通手机号未找到投顾客户号
     */
    HBONE_NO_FIND_AND_HBONE_MOBILE_NOT_FIND("14", "一账通找到投顾客户号，一账通手机号未找到投顾客户号", null),

    /**
     * 15-香港客户号已被其他投顾客户号占用
     */
    HK_CUST_NO_OCCUPIED("15", "香港客户号已被其他投顾客户号占用", null),

    /**
     * 开发自定义流程： 历史数据存在hboneNo绑定多个hkTxAcctNo的情况
     * 99-一账通号对应多个客户异常数据
     */
    HBONE_NO_MATCH_MULTIPLE_CUST_NO("99", "一账通号对应多个客户异常数据", null),

    /**
     * 投顾客户号已绑定其他香港客户号
     */
    HK01("HK01", "投顾客户号已绑定其他香港客户号", HkAbnormalLevelEnum.MEDIUM),

    /**
     * 香港绑定一账通与投顾客户号绑定一账通不一致
     */
    HK02("HK02", "香港绑定一账通与投顾客户号绑定一账通不一致", HkAbnormalLevelEnum.HIGH),

    /**
     * 香港注册手机号匹配到多个投顾客户号
     */
    HK03("HK03", "香港注册手机号匹配到多个投顾客户号", HkAbnormalLevelEnum.MEDIUM),

    /**
     * 香港注册手机号与投顾客户号手机号不一致
     */
    HK04("HK04", "香港注册手机号与投顾客户号手机号不一致", HkAbnormalLevelEnum.MEDIUM),

    /**
     * 香港绑定一账通关联投顾客户号与香港手机号关联投顾客户号不一致
     */
    HK05("HK05", "香港绑定一账通关联投顾客户号与香港手机号关联投顾客户号不一致", HkAbnormalLevelEnum.MEDIUM),

    /**
     * 香港注册手机号匹配到多个投顾客户号且与绑定一账通关联投顾客户号不一致
     */
    HK06("HK06", "香港注册手机号匹配到多个投顾客户号且与绑定一账通关联投顾客户号不一致", HkAbnormalLevelEnum.MEDIUM),

    /**
     * 香港开户证件被其他投顾客户号占用
     */
    HK07("HK07", "香港开户证件被其他投顾客户号占用", HkAbnormalLevelEnum.MEDIUM),

    /**
     * 香港开户证件找到多个投顾客户号
     */
    HK08("HK08", "香港开户证件找到多个投顾客户号", HkAbnormalLevelEnum.MEDIUM),

    /**
     * 香港客户号绑定一账通与投顾客户号绑定一账通不一致
     */
    HK09("HK09", "香港客户号绑定一账通与投顾客户号绑定一账通不一致", HkAbnormalLevelEnum.HIGH),

    /**
     * 香港客户号绑定的投顾客户号与香港客户号绑定一账通对应的投顾客户号不一致
     */
    HK10("HK10", "香港客户号绑定的投顾客户号与香港客户号绑定一账通对应的投顾客户号不一致", HkAbnormalLevelEnum.HIGH),

    /**
     * 香港开户证件对应的投顾客户号手机号与香港开户手机号不一致
     */
    HK11("HK11", "香港开户证件对应的投顾客户号手机号与香港开户手机号不一致", HkAbnormalLevelEnum.HIGH),

    /**
     * 香港注册手机号不存在，且香港开户证件找到多个投顾客户号
     */
    HK12("HK12", "香港注册手机号不存在，且香港开户证件找到多个投顾客户号", HkAbnormalLevelEnum.HIGH),

    /**
     * 香港注册手机号找到多个投顾客户号
     */
    HK13("HK13", "香港注册手机号找到多个投顾客户号", HkAbnormalLevelEnum.HIGH),

    /**
     * 香港客户号绑定一账通与香港注册手机号关联投顾客户号绑定的一账通不一致
     */
    HK14("HK14", "香港客户号绑定一账通与香港注册手机号关联投顾客户号绑定的一账通不一致", HkAbnormalLevelEnum.HIGH),

    /**
     * 投顾客户号已被其他香港客户号占用
     */
    HK15("HK15", "投顾客户号已被其他香港客户号占用", HkAbnormalLevelEnum.HIGH),

    /**
     * 香港开户手机号与开户证件关联投顾客户号不一致
     */
    HK16("HK16", "香港开户手机号与开户证件关联投顾客户号不一致", HkAbnormalLevelEnum.HIGH),

    /**
     * 香港开户证件找到多个投顾客户号
     */
    HK17("HK17", "香港开户证件找到多个投顾客户号", HkAbnormalLevelEnum.HIGH),

    /**
     * 投顾客户号已被其他香港客户号占用
     */
    HK18("HK18", "投顾客户号已被其他香港客户号占用", HkAbnormalLevelEnum.HIGH),

    /**
     * 投顾客户号绑定证件与香港开户证件不一致
     */
    HK19("HK19", "投顾客户号绑定证件与香港开户证件不一致", HkAbnormalLevelEnum.HIGH),

    /**
     * 香港开户证件绑定投顾客户号与手机号绑定投顾客户号不一致
     */
    HK20("HK20", "香港开户证件绑定投顾客户号与手机号绑定投顾客户号不一致", HkAbnormalLevelEnum.HIGH),

    /**
     * 香港开户证件找到多个投顾客户号
     */
    HK21("HK21", "香港开户证件找到多个投顾客户号", HkAbnormalLevelEnum.HIGH),

    /**
     * 香港绑定一账通关联的投顾客户号与香港开户信息不一致
     */
    HK22("HK22", "香港绑定一账通关联的投顾客户号与香港开户信息不一致", HkAbnormalLevelEnum.HIGH),

    /**
     * 香港注册手机号与投顾客户号绑定手机号不一致
     */
    HK23("HK23", "香港注册手机号与投顾客户号绑定手机号不一致", HkAbnormalLevelEnum.HIGH),

    /**
     * 香港一账通绑定投顾客户号与香港开户证件绑定投顾客户号不一致
     */
    HK24("HK24", "香港一账通绑定投顾客户号与香港开户证件绑定投顾客户号不一致", HkAbnormalLevelEnum.HIGH),

    /**
     * 香港开户证件找到多个投顾客户号
     */
    HK25("HK25", "香港开户证件找到多个投顾客户号", HkAbnormalLevelEnum.HIGH),

    /**
     * 香港注册手机号找到多个投顾客户号
     */
    HK26("HK26", "香港注册手机号找到多个投顾客户号", HkAbnormalLevelEnum.HIGH),

    /**
     * 香港一账通绑定投顾客户号的手机号与香港注册手机号不一致
     */
    HK27("HK27", "香港一账通绑定投顾客户号的手机号与香港注册手机号不一致", HkAbnormalLevelEnum.HIGH),

    /**
     * 香港一账通绑定投顾客户号与香港开户信息对应的投顾客户号不一致
     */
    HK28("HK28", "香港一账通绑定投顾客户号与香港开户信息对应的投顾客户号不一致", HkAbnormalLevelEnum.HIGH),

    /**
     * 香港一账通绑定投顾客户号与香港开户信息对应的投顾客户号不一致（多个投顾客户号）
     */
    HK29("HK29", "香港一账通绑定投顾客户号与香港开户信息对应的投顾客户号不一致", HkAbnormalLevelEnum.HIGH),

    /**
     * 香港开户证件找到多个投顾客户号
     */
    HK30("HK30", "香港开户证件找到多个投顾客户号", HkAbnormalLevelEnum.HIGH),

    /**
     * 投顾客户号已被其他香港客户号占用
     */
    HK31("HK31", "投顾客户号已被其他香港客户号占用", HkAbnormalLevelEnum.HIGH),

    ;



    /**
     * 编码
     **/
    private String code;
    /**
     * 描述
     * */
    private String description;

    /**
     * 异常等级
     */
    private HkAbnormalLevelEnum level;

    AbnormaSceneTypeEnum(String code, String description, HkAbnormalLevelEnum level) {
        this.code=code;
        this.description=description;
        this.level=level;
    }

    /**
     * 通过code获得
     * @param code	系统返回参数编码
     * @return description 描述
     */
    public static String getDescription(String code){
        AbnormaSceneTypeEnum statusEnum=getEnum(code);
        return  statusEnum==null?null:statusEnum.getDescription();
    }

    /**
     * 通过code直接返回 整个枚举类型
     * @param code 系统返回参数编码
     * @return BaseConstantEnum
     */
    public static AbnormaSceneTypeEnum getEnum(String code){
        for(AbnormaSceneTypeEnum b: AbnormaSceneTypeEnum.values()){
            if(b.getCode().equals(code)){
                return b;
            }
        }
        return null;
    }

    public String getCode() {
        return code;
    }


    public String getDescription() {
        return description;
    }

    public HkAbnormalLevelEnum getLevel() {
        return level;
    }
}
