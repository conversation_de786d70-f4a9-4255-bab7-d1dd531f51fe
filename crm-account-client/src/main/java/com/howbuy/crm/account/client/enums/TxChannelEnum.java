/**
 * Copyright (c) 2023, <PERSON><PERSON><PERSON><PERSON> HOWBUY INVESTMENT MANAGEMENT Co., Ltd.
 * All right reserved.
 * <p>
 * THIS IS UNPUBLISHED PROPRIETARY SOURCE CODE OF HOWBUY INVESTMENT
 * MANAGEMENT CO., LTD.  THE CONTENTS OF THIS FILE MAY NOT BE DISCLOSED
 * TO THIRD PARTIES, COPIED OR DUPLICATED IN ANY FORM, IN WHOLE OR IN PART,
 * WITHOUT THE PRIOR WRITTEN PERMISSION OF HOWBUY INVESTMENT MANAGEMENT
 * CO., LTD.
 */
package com.howbuy.crm.account.client.enums;

/**
 * @description: 渠道枚举
 * <AUTHOR>
 * @date 2023/6/6 19:23
 * @since JDK 1.8
 */
public enum TxChannelEnum {
    /**
     * 柜台
     */
    COUNTER("1", "柜台"),
    /**
     * 网站
     */
    WEBSITE("2", "网站"),
    /**
     * 电话
     */
    TELEPHONE("3", "电话"),
    /**
     * Wap
     */
    WAP("4", "Wap"),
    /**
     * App
     */
    APP("5", "App"),
    /**
     * 机构
     */
    INST("6", "机构"),
    /**
     * TA
     */
    TA("7", "TA");

    private String code;
    private String name;

    TxChannelEnum(String code, String name) {
        this.code = code;
        this.name = name;
    }

    public String getCode() {
        return code;
    }

    public String getName() {
        return name;
    }

    public static String getName(String code) {
        for (TxChannelEnum enumObj : TxChannelEnum.values()) {
            if (code != null && code.equals(enumObj.getCode())) {
                return enumObj.getName();
            }
        }
        return null;
    }
}
