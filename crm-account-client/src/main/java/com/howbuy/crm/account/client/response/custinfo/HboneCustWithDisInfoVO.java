/**
 * Copyright (c) 2024, <PERSON><PERSON><PERSON><PERSON> HOWBUY INVESTMENT MANAGEMENT Co., Ltd.
 * All right reserved.
 * <p>
 * THIS IS UNPUBLISHED PROPRIETARY SOURCE CODE OF HOWBUY INVESTMENT
 * MANAGEMENT CO., LTD.  THE CONTENTS OF THIS FILE MAY NOT BE DISCLOSED
 * TO THIRD PARTIES, COPIED OR DUPLICATED IN ANY FORM, IN WHOLE OR IN PART,
 * WITHOUT THE PRIOR WRITTEN PERMISSION OF HOWBUY INVESTMENT MANAGEMENT
 * CO., LTD.
 */
package com.howbuy.crm.account.client.response.custinfo;

import lombok.Data;

import java.io.Serializable;
import java.util.List;

/**
 * @description: (一账通  客户信息 与 分销客户   信息属性 VO )
 * 来源：[com.howbuy.acccenter.facade.query.queryallcustinfoanddiscustinfo.QueryAllCustInfoAndDisCustInfoResponse]
 * <AUTHOR>
 * @date 2024/1/27 10:55
 * @since JDK 1.8
 */
@Data
public class HboneCustWithDisInfoVO implements Serializable {

      private static final long serialVersionUID = 1L;


    /**
     * 客户信息
     */
    private HboneDisMainCustInfoVO mainCustInfo;

     /**
     * 分销层 信息
     */
     private List<HboneDisCustInfoVO> disCustInfoList;

}