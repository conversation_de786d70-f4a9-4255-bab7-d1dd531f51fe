package com.howbuy.crm.account.client.enums.custinfo;

import java.util.Arrays;
import java.util.HashSet;
import java.util.List;
import java.util.Set;
import java.util.stream.Collectors;

/**
 * @description: [香港异常客户] 场景 枚举类
 *
 *   使用项目：crm-hb
 *
 * @author: haoran.zhang
 * @date: 2023/12/22
 * @since JDK 1.8
 */
public enum HkAbnormalSceneEnum {

    /**
     * 线上
     */
    ONLINE("1", "线上", new String[]{
            FullCustSourceEnum.HK_REGISTER.getCode(),
            FullCustSourceEnum.HK_ONLINE_OPEN_ACCOUNT.getCode()
    }),

    /**
     * 线下
     */
    OFFLINE("2", "线下", new String[]{
            FullCustSourceEnum.HK_OFFLINE_OPEN_ACCOUNT.getCode(),
            FullCustSourceEnum.HK_OPEN_ACCOUNT_CRM.getCode()
    }),

    /**
     * 其他
     */
    OTHER("3", "其他", new String[]{
            FullCustSourceEnum.HK_CUST_INFO_SYNC.getCode(),
            FullCustSourceEnum.HK_CUST_NO_BIND.getCode(),
            FullCustSourceEnum.HBONE_REAL_NAME_REGISTER.getCode(),
            FullCustSourceEnum.HBONE_REGISTER.getCode(),
            FullCustSourceEnum.DISTRIBUTION_OPEN_ACCOUNT.getCode(),
            FullCustSourceEnum.HBONE_CUST_INFO_SYNC.getCode(),
            FullCustSourceEnum.HK_CUST_MOBILE_CHANGE.getCode(),
            FullCustSourceEnum.HK_CUST_ID_CHANGE.getCode(),
            FullCustSourceEnum.PAGE_MENU_OPERATE.getCode(),
            FullCustSourceEnum.HBONE_CUST_MOBILE_CHANGE.getCode(),
            FullCustSourceEnum.HBONE_CUST_REAL_NAME_CHANGE.getCode(),
            FullCustSourceEnum.MGM.getCode()
    });

    /**
     * 编码
     */
    private String code;

    /**
     * 描述
     */
    private String description;

    /**
     * 对应的异常来源编码集合
     */
    private String[] sourceCodeArray;

    HkAbnormalSceneEnum(String code, String description, String[] sourceCodeArray) {
        this.code = code;
        this.description = description;
        this.sourceCodeArray = sourceCodeArray;
    }

    /**
     * 根据场景编码获取对应的异常来源编码集合
     *
     * @param sceneCode 场景编码
     * @return 异常来源编码集合
     */
    public static Set<String> getSourceCodesBySceneCode(String sceneCode) {
        for (HkAbnormalSceneEnum scene : HkAbnormalSceneEnum.values()) {
            if (scene.getCode().equals(sceneCode)) {
                return new HashSet<>(Arrays.asList(scene.getSourceCodeArray()));
            }
        }
        return new HashSet<>();
    }

    /**
     * 根据多个场景编码获取对应的异常来源编码集合
     *
     * @param sceneCodes 场景编码数组
     * @return 异常来源编码集合
     */
    public static Set<String> getSourceCodesBySceneCodes(String[] sceneCodes) {
        Set<String> sourceCodes = new HashSet<>();
        if (sceneCodes == null) {
            return sourceCodes;
        }

        for (String sceneCode : sceneCodes) {
            sourceCodes.addAll(getSourceCodesBySceneCode(sceneCode));
        }
        return sourceCodes;
    }

    /**
     * 根据异常来源编码获取对应的场景编码
     *
     * @param sourceCode 异常来源编码
     * @return 场景编码
     */
    public static String getSceneCodeBySourceCode(String sourceCode) {
        for (HkAbnormalSceneEnum scene : HkAbnormalSceneEnum.values()) {
            if (Arrays.asList(scene.getSourceCodeArray()).contains(sourceCode)) {
                return scene.getCode();
            }
        }
        return OTHER.getCode(); // 默认返回"其他"
    }

    /**
     * 通过code获得描述
     *
     * @param code 编码
     * @return 描述
     */
    public static String getDescription(String code) {
        HkAbnormalSceneEnum sceneEnum = getEnum(code);
        return sceneEnum == null ? null : sceneEnum.getDescription();
    }

    /**
     * 通过code获取枚举对象
     *
     * @param code 编码
     * @return 枚举对象
     */
    public static HkAbnormalSceneEnum getEnum(String code) {
        for (HkAbnormalSceneEnum scene : HkAbnormalSceneEnum.values()) {
            if (scene.getCode().equals(code)) {
                return scene;
            }
        }
        return null;
    }

    /**
     * 获取所有场景编码
     *
     * @return 场景编码列表
     */
    public static List<String> getAllSceneCodes() {
        return Arrays.stream(HkAbnormalSceneEnum.values())
                .map(HkAbnormalSceneEnum::getCode)
                .collect(Collectors.toList());
    }

    public String getCode() {
        return code;
    }

    public String getDescription() {
        return description;
    }

    public String[] getSourceCodeArray() {
        return sourceCodeArray;
    }
} 