package com.howbuy.crm.account.client.enums.commvisit;

/**
 * <AUTHOR>
 * @description: 反馈类型枚举
 * @date 2025/4/10 14:13
 * @since JDK 1.8
 */
public enum FeedbackTypeEnum {

    /**
     * 已处理
     */
    ACCOMPANYING("1","陪访人"),

    /**
     * 无需处理
     */
    MANAGE("2","主管")
    ;

    /**
     * 编码
     **/
    private String code;
    /**
     * 描述
     * */
    private String description;

    FeedbackTypeEnum(String code, String description){
        this.code=code;
        this.description=description;
    }

    public String getCode() {
        return code;
    }

    public void setCode(String code) {
        this.code = code;
    }

    public String getDescription() {
        return description;
    }

    public void setDescription(String description) {
        this.description = description;
    }
}