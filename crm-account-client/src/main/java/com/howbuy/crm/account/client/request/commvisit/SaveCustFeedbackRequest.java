package com.howbuy.crm.account.client.request.commvisit;

import lombok.Getter;
import lombok.Setter;

import java.io.Serializable;
import java.util.List;

/**
 * @description: 修改陪访人/主管请求
 * <AUTHOR>
 * @date 2025-04-08 10:26:00
 * @since JDK 1.8
 */
@Getter
@Setter
public class SaveCustFeedbackRequest implements Serializable {

    private static final long serialVersionUID = 1L;
    /**
     * 拜访纪要ID
     */
    private String visitMinutesId;
    
    /**
     * 陪访人列表
     */
    private List<AccompanyingRequest> accompanyingList;
    
    /**
     * 客户参与角色
     */
    private String attendRole;
    
    /**
     * 对产品或服务的具体反馈
     */
    private String productServiceFeedback;
    
    /**
     * 对于IPS报告反馈
     */
    private String ipsFeedback;
    
    /**
     * 人民币金额
     */
    private String addAmtRmb;
    
    /**
     * 外币金额
     */
    private String addAmtForeign;
    
    /**
     * 近期关注的资产类别或具体产品
     */
    private String focusAsset;
    
    /**
     * 评估客户需求
     */
    private String estimateNeedBusiness;
    
    /**
     * 下一步工作计划
     */
    private String nextPlan;
    /**
     * 当前用户ID
     */
    private String currentUserId;

    @Getter
    @Setter
    public static class AccompanyingRequest implements Serializable {

        private static final long serialVersionUID = 1L;
        /**
         * 陪访人类型
         */
        private String accompanyingType;
        
        /**
         * 陪访人Id
         */
        private String accompanyingUserId;
    }
} 