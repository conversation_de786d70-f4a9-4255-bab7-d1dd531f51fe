package com.howbuy.crm.account.client.request.consultant;

import lombok.Getter;
import lombok.Setter;
import lombok.ToString;

import java.io.Serializable;

/**
 * @description: 重新加载全量投顾缓存请求参数
 * <AUTHOR>
 * @date 2025/3/10
 * @since JDK 1.8
 */
@Getter
@Setter
@ToString
public class ConsultantCacheReloadAllRequest implements Serializable {
    
    private static final long serialVersionUID = 1L;
} 