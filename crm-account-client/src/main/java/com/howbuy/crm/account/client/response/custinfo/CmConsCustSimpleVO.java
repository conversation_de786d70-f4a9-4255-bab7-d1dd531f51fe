package com.howbuy.crm.account.client.response.custinfo;

import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;

import java.io.Serializable;
import java.util.Date;

/**
 * @description: (投顾客户信息 简单信息，包含 : 客户基本信息、hboneNo、香港客户信息、投顾code)
 * <AUTHOR>
 * @date 2023/12/8 20:57
 * @since JDK 1.8
 */
@Data
public class CmConsCustSimpleVO implements Serializable {

    private static final long serialVersionUID = 1L;
    /**
     * 投顾客户编号
     */
    private String conscustno;


    /**
     * 证件类型
     */
    private String idtype;

    /**
     * 投资者名称
     */
    private String custname;

    /**
     * 客户投资类型 0 机构客户 1 个人客户 2 产品客户
     */
    private String invsttype;

    /**
     * 一账通账号
     */
    private String hboneNo;


    /**
     * 投顾客户手机核查对应状态
     */
    private String custstatus;

    /**
     * 证件号摘要
     */
    private String idnoDigest;

    /**
     * 证件号掩码
     */
    private String idnoMask;


    /**
     * 手机号摘要
     */
    private String mobileDigest;

    /**
     * 手机号掩码
     */
    private String mobileMask;

    /**
     * 手机 地区码
     */
    private String mobileAreaCode;


    /**
     * 证件地区码
     */
    private String idSignAreaCode;

    /**
     * 国籍
     */
    private String nationCode;

    /**
     * 一账通号关联时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date hboneTimestamp;

    //以下为 香港客户信息

    /**
     * 客户香港id (ebrokerID)
     */
    private String hkcustid;

    /**
     * 香港交易账号
     */
    private String hkTxAcctNo;

    /**
     * 注册日期 yyyyMMDD
     */
    private String regDt;

    //以下为  所属投顾信息

    /**
     * 所属投顾
     */
    private String  consCode;





}