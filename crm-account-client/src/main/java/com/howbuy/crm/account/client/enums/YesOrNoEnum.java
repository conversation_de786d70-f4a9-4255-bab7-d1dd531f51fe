/**
 * Copyright (c) 2023, <PERSON><PERSON><PERSON><PERSON> HOWBUY INVESTMENT MANAGEMENT Co., Ltd.
 * All right reserved.
 * <p>
 * THIS IS UNPUBLISHED PROPRIETARY SOURCE CODE OF HOWBUY INVESTMENT
 * MANAGEMENT CO., LTD.  THE CONTENTS OF THIS FILE MAY NOT BE DISCLOSED
 * TO THIRD PARTIES, COPIED OR DUPLICATED IN ANY FORM, IN WHOLE OR IN PART,
 * WITHOUT THE PRIOR WRITTEN PERMISSION OF HOWBUY INVESTMENT MANAGEMENT
 * CO., LTD.
 */
package com.howbuy.crm.account.client.enums;

/**
 * @description: (是否的 枚举  1-是 0-否)
 * <AUTHOR>
 * @date 2023/3/9 15:38
 * @since JDK 1.8
 */
public enum YesOrNoEnum {

    /**
     * 1-是
     */
    YES("1", "是","Y"),
    /**
     * 0-否
     */
    NO("0", "否","N");

    /**
     * code值
     */
    private String code;
    /**
     * 翻译信息 [是|否]
     */
    private String description;

    /**
     * 标识 [Y|N]
     */
    private String signal;

    YesOrNoEnum(String code, String description,String signal) {
        this.code = code;
        this.description = description;
        this.signal=signal;
    }

    public String getDescription() {
        return description;
    }

    public String getCode() {
        return code;
    }

    public String getSignal() {
        return signal;
    }

    public static YesOrNoEnum getEnum(String key) {
        for(YesOrNoEnum yesNoEnum : values()){
            if(yesNoEnum.getCode().equals(key)){
                return yesNoEnum;
            }
        }
        return null;
    }

    /**
     * 通过code获得描述信息
     * @param code
     * @return description 描述
     */
    public static String getDescription(String code) {
        YesOrNoEnum yesNoEnum = getEnum(code);
        return yesNoEnum == null ? null :yesNoEnum.getDescription();
    }

}