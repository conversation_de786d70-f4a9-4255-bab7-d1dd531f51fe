/**
 * Copyright (c) 2023, <PERSON><PERSON><PERSON><PERSON> HOWBUY INVESTMENT MANAGEMENT Co., Ltd.
 * All right reserved.
 * <p>
 * THIS IS UNPUBLISHED PROPRIETARY SOURCE CODE OF HOWBUY INVESTMENT
 * MANAGEMENT CO., LTD.  THE CONTENTS OF THIS FILE MAY NOT BE DISCLOSED
 * TO THIRD PARTIES, COPIED OR DUPLICATED IN ANY FORM, IN WHOLE OR IN PART,
 * WITHOUT THE PRIOR WRITTEN PERMISSION OF HOWBUY INVESTMENT MANAGEMENT
 * CO., LTD.
 */
package com.howbuy.crm.account.client.request.custinfo;

import com.howbuy.commons.validator.MyValidation;
import com.howbuy.commons.validator.util.ValidatorTypeEnum;
import lombok.Data;

/**
 * @description: (更新 一账通 海外资产隔离标志 请求类 )
 * <AUTHOR>
 * @date 2023/12/18 17:15
 * @since JDK 1.8
 */
@Data
public class UpdateHkAssetIsolateRequest {

    /**
     * 香港交易账号
     */
    @MyValidation(validatorType = ValidatorTypeEnum.String, fieldName = "一账通号", isRequired = true)
    private String hboneNo;

    /**
     * 操作员
     */
    @MyValidation(validatorType = ValidatorTypeEnum.String, fieldName = "操作人", isRequired = true)
    private String operator;




    /**
     * 海外资产隔离标志更新时间	['yyyyMMddHHmmss']
     */
    @MyValidation(validatorType = ValidatorTypeEnum.String, fieldName = "海外资产隔离标志更新时间", isRequired = true)
    private String hkAssetIsolateFlagUpDt;


    /**
     * 海外资产隔离标志	['0-否', '1-是']
     */
    @MyValidation(validatorType = ValidatorTypeEnum.String, fieldName = "海外资产隔离标志", isRequired = true)
    private String hkAssetIsolateFlag;


}