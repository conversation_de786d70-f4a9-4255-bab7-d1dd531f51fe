/**
 * Copyright (c) 2024, <PERSON>gH<PERSON> HOWBUY INVESTMENT MANAGEMENT Co., Ltd.
 * All right reserved.
 * <p>
 * THIS IS UNPUBLISHED PROPRIETARY SOURCE CODE OF HOWBUY INVESTMENT
 * MANAGEMENT CO., LTD.  THE CONTENTS OF THIS FILE MAY NOT BE DISCLOSED
 * TO THIRD PARTIES, COPIED OR DUPLICATED IN ANY FORM, IN WHOLE OR IN PART,
 * WITHOUT THE PRIOR WRITTEN PERMISSION OF HOWBUY INVESTMENT MANAGEMENT
 * CO., LTD.
 */
package com.howbuy.crm.account.client.response.custinfo;

import lombok.Data;

import java.io.Serializable;
import java.util.List;

/**
 * @description: (海外储蓄罐协议签署信息VO)
 * <AUTHOR>
 * @date 2024/7/24 14:00
 * @since JDK 1.8
 */
@Data
public class HkCustPiggyAgreementVO  implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 签署的储蓄罐基金代码
      */
    private List<String> piggyFundCodeList;
    /**
     * 海外储蓄罐协议状态 0-未签署、1-已签署、2-已终止， 空值转为0-未签署
      */
    private String agreementState;
    /**
     * 协议签署方式 1-线下自主申请开通 2-线上自主申请开通 3-到期自动续期 4-底层基金更换同意
     */
    private String agreementSignType;

    /**
     * 协议签署日期
     */
    private String agreementSignDt;


    /**
     * 协议有效期
     */
    private String agreementSignExpiredDt;

    /**
     * 协议终止方式 1-线下自主申请关闭 2-线上自主申请关闭 3-底层基金更换未同意 4-底层基金更换不同意
     */
    private String agreementCancelType;

    /**
     * 协议终止日期
     */
    private String agreementCancelDt;

}