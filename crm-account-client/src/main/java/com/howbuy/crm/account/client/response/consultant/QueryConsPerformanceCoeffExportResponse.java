package com.howbuy.crm.account.client.response.consultant;

import lombok.Getter;
import lombok.Setter;

import java.io.Serializable;

/**
 * @description: 导出人员绩效系数响应对象（返回导出文件信息） 
 * <AUTHOR>
 * @date 2025-07-11 16:00:00
 * @since JDK 1.8
 */
@Getter
@Setter
public class QueryConsPerformanceCoeffExportResponse implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 导出文件URL
     */
    private String fileUrl;

    /**
     * 导出文件名
     */
    private String fileName;

    /**
     * 导出数据总数
     */
    private Long totalCount;
    /**
     * 导出错误信息
     */
    private String errorMsg;
}