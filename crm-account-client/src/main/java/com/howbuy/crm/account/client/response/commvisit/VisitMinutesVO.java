package com.howbuy.crm.account.client.response.commvisit;

import lombok.Data;

import java.io.Serializable;

/**
 * 拜访纪要信息
 */
@Data
public class VisitMinutesVO implements Serializable {

    private static final long serialVersionUID = 1L;
    /**
     * 主键
     */
    private String id;
    /**
     * 创建日期 格式YYYYMMDD HH:MM:SS
     */
    private String createTime;
    
    /**
     * 拜访日期 格式YYYYMMDD
     */
    private String visitDt;
    
    /**
     * 拜访目的
     */
    private String visitPurpose;
    
    /**
     * 投顾客户号
     */
    private String consCustNo;
    
    /**
     * 客户姓名
     */
    private String custName;
    
    /**
     * 纪要创建人姓名
     */
    private String creatorName;
    
    /**
     * 所属中心
     */
    private String centerName;
    
    /**
     * 所属区域
     */
    private String areaName;
    
    /**
     * 所属分公司
     */
    private String branchName;
    
    /**
     * 沟通方式
     */
    private String visitType;
    
    /**
     * 客户存量
     */
    private String marketVal;
    
    /**
     * 客户综合健康度
     */
    private String healthAvgStar;
    
    /**
     * 提供资料
     */
    private String giveInformation;
    
    /**
     * 客户参与人员及角色
     */
    private String attendRole;
    
    /**
     * 对产品或服务的具体反馈
     */
    private String productServiceFeedback;
    
    /**
     * 对于IPS报告反馈
     */
    private String ipsFeedback;
    
    /**
     * 近期可用于加仓的金额
     */
    private String addAmount;
    
    /**
     * 近期关注的资产类别或具体产品
     */
    private String focusAsset;
    
    /**
     * 评估客户需求
     */
    private String estimateNeedBusiness;
    
    /**
     * 下一步工作计划
     */
    private String nextPlan;
    
    /**
     * 陪访人类型
     */
    private String accompanyingType;
    
    /**
     * 陪访人
     */
    private String accompanyingUser;
    
    /**
     * 陪访人反馈-概要
     */
    private String accompanySummary;
    
    /**
     * 陪访人反馈-建议
     */
    private String accompanySuggestion;
    
    /**
     * 上级主管
     */
    private String managerName;
    
    /**
     * 主管反馈-概要
     */
    private String managerSummary;
    
    /**
     * 主管反馈-建议
     */
    private String managerSuggestion;
    
    /**
     * 陪访人反馈权限
     */
    private Boolean canAccompanyFeedback;
    
    /**
     * 主管反馈权限
     */
    private Boolean canManagerFeedback;
} 