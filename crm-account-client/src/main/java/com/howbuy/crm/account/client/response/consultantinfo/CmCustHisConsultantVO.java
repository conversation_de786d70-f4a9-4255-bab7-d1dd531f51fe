/**
 * Copyright (c) 2024, <PERSON><PERSON><PERSON><PERSON> HOWBUY INVESTMENT MANAGEMENT Co., Ltd.
 * All right reserved.
 * <p>
 * THIS IS UNPUBLISHED PROPRIETARY SOURCE CODE OF HOWBUY INVESTMENT
 * MANAGEMENT CO., LTD.  THE CONTENTS OF THIS FILE MAY NOT BE DISCLOSED
 * TO THIRD PARTIES, COPIED OR DUPLICATED IN ANY FORM, IN WHOLE OR IN PART,
 * WITHOUT THE PRIOR WRITTEN PERMISSION OF HOWBUY INVESTMENT MANAGEMENT
 * CO., LTD.
 */
package com.howbuy.crm.account.client.response.consultantinfo;

import java.io.Serializable;
import java.util.List;

/**
 * <AUTHOR>
 * @Description 客户历史所属投顾VO
 * @Date 2024/9/12 16:33
 */
public class CmCustHisConsultantVO implements Serializable {

    private static final long serialVersionUID = 9087529471651119795L;

    /**
     * 投顾客户号
     */
    private List<CmCustHisConsultant> custHisConsultantList;

    public List<CmCustHisConsultant> getCustHisConsultantList() {
        return custHisConsultantList;
    }

    public void setCustHisConsultantList(List<CmCustHisConsultant> custHisConsultantList) {
        this.custHisConsultantList = custHisConsultantList;
    }
}
