/**
 * Copyright (c) 2024, <PERSON><PERSON><PERSON><PERSON> HOWBUY INVESTMENT MANAGEMENT Co., Ltd.
 * All right reserved.
 * <p>
 * THIS IS UNPUBLISHED PROPRIETARY SOURCE CODE OF HOWBUY INVESTMENT
 * MANAGEMENT CO., LTD.  THE CONTENTS OF THIS FILE MAY NOT BE DISCLOSED
 * TO THIRD PARTIES, COPIED OR DUPLICATED IN ANY FORM, IN WHOLE OR IN PART,
 * WITHOUT THE PRIOR WRITTEN PERMISSION OF HOWBUY INVESTMENT MANAGEMENT
 * CO., LTD.
 */
package com.howbuy.crm.account.client.request.consultant;

import com.howbuy.crm.account.client.request.Request;
import lombok.Getter;
import lombok.Setter;
import lombok.ToString;

import java.io.Serializable;

/**
 * @description: 获取人员绩效系数表请求对象
 * <AUTHOR>
 * @date 2025-07-02 16:15:20
 * @since JDK 1.8
 */
@<PERSON><PERSON>
@Setter
@ToString
public class QueryConsPerformanceCoeffRequest extends Request implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 投顾客户号
     */
    private String consCustNo;

    /**
     * 投顾code
     */
    private String consCode;
} 