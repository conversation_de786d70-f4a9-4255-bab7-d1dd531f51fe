/**
 * Copyright (c) 2024, <PERSON><PERSON><PERSON><PERSON> HOWBUY INVESTMENT MANAGEMENT Co., Ltd.
 * All right reserved.
 * <p>
 * THIS IS UNPUBLISHED PROPRIETARY SOURCE CODE OF HOWBUY INVESTMENT
 * MANAGEMENT CO., LTD.  THE CONTENTS OF THIS FILE MAY NOT BE DISCLOSED
 * TO THIRD PARTIES, COPIED OR DUPLICATED IN ANY FORM, IN WHOLE OR IN PART,
 * WITHOUT THE PRIOR WRITTEN PERMISSION OF HOWBUY INVESTMENT MANAGEMENT
 * CO., LTD.
 */
package com.howbuy.crm.account.client.response.kycinfo;

import lombok.Data;

import java.io.Serializable;
import java.math.BigDecimal;

/**
 * @description: 香港客户风险测评问卷VO
 * <AUTHOR>
 * @date 2024/1/11 15:17
 * @since JDK 1.8
 */
@Data
public class HkKycInfoVO implements Serializable {
    private static final long serialVersionUID = -2727302622906789320L;

    /**
     * 风险承受能力 1-低风险等级 2-中低风险等级 3-中风险等级 4-中高风险等级 5-高风险等级
     */
    private String riskToleranceLevel;
    /**
     * 衍生金融工具知识0-无 1-有
     */
    private String derivativeKnowledge;
    /**
     * 风测日期
     */
    private String riskToleranceDate;
    /**
     * 风险评测过期日期 YYYYMMDD
     */
    private String riskToleranceTerm;
    /**
     * 分数
     */
    private BigDecimal riskToleranceScore;
}