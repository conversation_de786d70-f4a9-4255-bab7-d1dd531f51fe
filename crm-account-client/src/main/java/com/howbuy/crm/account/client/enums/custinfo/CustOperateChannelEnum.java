package com.howbuy.crm.account.client.enums.custinfo;

/**
 * @description:( 操作通道 1-MQ  2-菜单页面 )
 * @param
 * @return
 * @author: haoran.zhang
 * @date: 2023/12/19 13:43
 * @since JDK 1.8
 */
public enum CustOperateChannelEnum {

    /**
     * 1-MQ
     * */
    MQ("1","MQ"),
    /**
     * 2-菜单页面
     * */
    MENU("2","菜单页面")

    ;


    /**
     * 编码
     * */
    private String code;
    /**
     * 描述
     * */
    private String description;

    private CustOperateChannelEnum(String code, String description){
        this.code=code;
        this.description=description;
    }

    /**
     * 通过code获得
     * @param code	系统返回参数编码
     * @return description 描述
     */
    public static String getDescription(String code){
        for(CustOperateChannelEnum b: CustOperateChannelEnum.values()){
            if(b.getCode().equals(code)){
                return b.description;
            }
        }
        return null;
    }

    /**
     * 通过code直接返回 整个枚举类型
     * @param code 系统返回参数编码
     * @return BaseConstantEnum
     */
    public static CustOperateChannelEnum getEnum(String code){
        if(code != null && !"".equals(code)){
            for(CustOperateChannelEnum b: CustOperateChannelEnum.values()){
                if(b.getCode().equals(code)){
                    return b;
                }
            }
        }
        return null;
    }

    public String getCode() {
        return code;
    }


    public String getDescription() {
        return description;
    }
}
