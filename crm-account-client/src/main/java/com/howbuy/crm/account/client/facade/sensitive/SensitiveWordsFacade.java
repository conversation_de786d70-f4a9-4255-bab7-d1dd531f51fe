/**
 * Copyright (c) 2025, <PERSON><PERSON><PERSON><PERSON> HOWBUY INVESTMENT MANAGEMENT Co., Ltd.
 * All right reserved.
 * <p>
 * THIS IS UNPUBLISHED PROPRIETARY SOURCE CODE OF HOWBUY INVESTMENT
 * MANAGEMENT CO., LTD.  THE CONTENTS OF THIS FILE MAY NOT BE DISCLOSED
 * TO THIRD PARTIES, COPIED OR DUPLICATED IN ANY FORM, IN WHOLE OR IN PART,
 * WITHOUT THE PRIOR WRITTEN PERMISSION OF HOWBUY INVESTMENT MANAGEMENT
 * CO., LTD.
 */
package com.howbuy.crm.account.client.facade.sensitive;

import com.howbuy.crm.account.client.request.sensitive.SensitiveWordsRequest;
import com.howbuy.crm.account.client.response.Response;
import com.howbuy.crm.account.client.response.sensitive.SensitiveWordsVO;

/**
 * @description 敏感词查询接口
 * <AUTHOR>
 * @date 2024-03-19 14:30:45
 */
public interface SensitiveWordsFacade {

    /**
     * @api {DUBBO} com.howbuy.crm.account.client.facade.sensitive.SensitiveWordsFacade.querySensitiveWords(request) querySensitiveWords()
     * @apiVersion 1.0.0
     * @apiGroup SensitiveWordsFacade
     * @apiName querySensitiveWords()
     * @apiDescription 敏感词查询接口
     * @apiParam (请求参数) {String} module 功能模块(01-表示客服来电处理模块 02-表示资产配置报告模块)
     * @apiParamExample 请求参数示例
     * {
     *   "module": "01"
     * }
     * @apiSuccess (响应结果) {String} code 状态码
     * @apiSuccess (响应结果) {String} description 描述信息
     * @apiSuccess (响应结果) {Array} data.sensitiveWords 敏感词列表
     * @apiSuccessExample 响应结果示例
     * {
     *   "code": "0",
     *   "description": "成功",
     *   "data": {
     *     "sensitiveWords": ["敏感词1", "敏感词2"]
     *   }
     * }
     */
    Response<SensitiveWordsVO> querySensitiveWords(SensitiveWordsRequest request);
} 