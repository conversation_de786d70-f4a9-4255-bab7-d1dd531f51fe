package com.howbuy.crm.account.client.utils;

import org.apache.commons.lang.StringUtils;

import java.time.LocalDate;
import java.time.LocalDateTime;
import java.time.Period;
import java.time.format.DateTimeFormatter;
import java.time.temporal.ChronoUnit;
import java.time.temporal.TemporalAdjuster;
import java.time.temporal.TemporalAdjusters;
import java.time.temporal.TemporalUnit;
import java.util.function.Function;

/**
 * 日期工具
 *
 * <AUTHOR>
 * @date 2024/8/21 15:35
 */
public class LocalDateUtil {
    /**
     * 日期格式化
     */
    public static final DateTimeFormatter DTF = DateTimeFormatter.ofPattern("yyyyMMdd");
    /**
     * 带破折号-的天格式
     */
    public static final DateTimeFormatter DTF_DASH= DateTimeFormatter.ofPattern("yyyy-MM-dd");
    /**
     * 带斜杠/的天格式
     */
    public static final DateTimeFormatter DTF_SLASH= DateTimeFormatter.ofPattern("yyyy/MM/dd");
    /**
     * 带破折号-的时间格式化
     */
    public static final DateTimeFormatter DTF_TIME_DASH= DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss");
    /**
     * 带斜杠/的时间格式化
     */
    public static final DateTimeFormatter DTF_TIME_SLASH= DateTimeFormatter.ofPattern("yyyy/MM/dd HH:mm:ss");
    /**
     * 时间格式化
     */
    public static final DateTimeFormatter DTF_TIME= DateTimeFormatter.ofPattern("yyyyMMdd HH:mm:ss");
    /**
     * 日期格式化（月）
     */
    public static final DateTimeFormatter DTF_MONTH = DateTimeFormatter.ofPattern("yyyyMM");

    /**
     * 日期 -> 数字
     *
     * @param date
     * @return
     */
    public static Integer localDateToInteger(LocalDate date) {
        return Integer.valueOf(localDateToString(date));
    }

    /**
     * 数字 -> 日期
     *
     * @param date
     * @return
     */
    public static LocalDate integerToLocalDate(Integer date) {
        return stringToLocalDate(date.toString());
    }

    /**
     * 字符串 -> 日期
     *
     * @param date
     * @return
     */
    public static LocalDate stringToLocalDate(String date) {
        return LocalDate.parse(date, DTF);
    }

    /**
     * 日期 -> 字符串
     *
     * @param date
     * @return
     */
    public static String localDateToStringByFormatter(LocalDate date, DateTimeFormatter formatter) {
        if (date == null) {
            return null;
        }
        return date.format(formatter);
    }

    /**
     * 日期 -> 字符串
     *
     * @param date
     * @return
     */
    public static String localDateToString(LocalDate date) {
        return date.format(DTF);
    }

    /**
     * 根据给定日期h获得上月(YYYYMM)
     * @param date
     * @return
     */
    public static String getPreMonth(LocalDate date){
        return LocalDateUtil.localDateToStringByFormatter(LocalDateUtil.plus(date, -1, ChronoUnit.MONTHS),
                LocalDateUtil.DTF_MONTH);
    }

	/*
     * 时间格式化
     * @param dateTime 时间
     * @param formatter 时间格式
     * @return
     */
    public static String localDateToString(LocalDate dateTime, DateTimeFormatter formatter) {
        if(dateTime == null){
            return null;
        }
        if(formatter == null){
            return dateTime.format(DTF_DASH);
        }
        return dateTime.format(formatter);
    }

    /**
     * 时间格式化
     * @param dateTime 时间
     * @param formatter 时间格式
     * @return
     */
    public static String localDateTimeToString(LocalDateTime dateTime, DateTimeFormatter formatter) {
        if(dateTime == null){
            return null;
        }
        if(formatter == null){
            return dateTime.format(DTF_DASH);
        }
        return dateTime.format(formatter);
    }


    /**
     * 返回两个日期相差天数
     * @param startDate
     * @param endDate
     * @return
     */
    public static Integer betweenDayByDay(String startDate, String endDate){
        return Period.between(LocalDate.parse(startDate, DTF), LocalDate.parse(endDate, DTF)).getDays();
    }

    /**
     * 日期1是否大于日期2
     * @param firstDate
     * @param secondDate
     * @return
     */
    public static Boolean firstDateIsAfterSecondDate(String firstDate, String secondDate){
        return LocalDate.parse(firstDate, DTF).isAfter(LocalDate.parse(secondDate, DTF));
    }

    /**
     * 返回两个日期相差月份
     * @param startDate
     * @param endDate
     * @return
     */
    public static Integer betweenDayByMonthCount(String startDate, String endDate){
        return betweenDayByMonthCount(stringToLocalDate(startDate), stringToLocalDate(endDate));
    }

    /**
     * 返回两个日期相差月份
     * @param startDate
     * @param endDate
     * @return
     */
    public static Integer betweenDayByMonthCount(String startDate, LocalDate endDate){
        return betweenDayByMonthCount(stringToLocalDate(startDate), endDate);
    }

    /**
     * 返回两个日期相差月份
     * @param startDate
     * @param endDate
     * @return
     */
    public static Integer betweenDayByMonthCount(LocalDate startDate, String endDate){
        return betweenDayByMonthCount(startDate, stringToLocalDate(endDate));
    }

    /**
     * 返回两个日期相差月份
     * @param startDate
     * @param endDate
     * @return
     */
    public static Integer betweenDayByMonthCount(LocalDate startDate, LocalDate endDate){
        //年差
        int years = endDate.getYear() - startDate.getYear();
        //月差
        return years * 12 + (endDate.getMonthValue() - startDate.getMonthValue());
    }

    /**
     * 加N天
     *
     * @param date
     * @param num
     * @return
     */
    public static Integer plusDays(Integer date, int num) {
        return plus(date, num, ChronoUnit.DAYS);
    }

    /**
     * 加N时间段
     *
     * @param date
     * @param num
     * @param unit
     * @return
     */
    public static LocalDate plus(LocalDate date, int num, TemporalUnit unit) {
        if (date == null) {
            return null;
        }
        return date.plus(num, unit);
    }

    /**
     * 加N时间段
     *
     * @param date
     * @param num
     * @param unit
     * @return
     */
    public static Integer plus(Integer date, int num, TemporalUnit unit) {
        if (date == null) {
            return null;
        }
        LocalDate ld = integerToLocalDate(date).plus(num, unit);
        return localDateToInteger(ld);
    }

    /**
     * 转化计算日期
     *
     * @param date
     * @param func
     * @return
     */
    public static Integer apply(Integer date, Function<LocalDate, LocalDate> func) {
        LocalDate ld = integerToLocalDate(date);
        ld = func.apply(ld);
        return localDateToInteger(ld);
    }

    /**
     * 季末日期
     *
     * @return
     */
    public static TemporalAdjuster lastDayOfQuarter() {
        return TemporalAdjusters.ofDateAdjuster(f -> {
            int m = f.getMonth().getValue() % 3;
            if (m == 1) {
                // m = 1 (1、4、7、10)月 季初月
                return f.plusMonths(2).with(TemporalAdjusters.lastDayOfMonth());
            }
            if (m == 2) {
                // m = 2 (2、5、8、11)月 季中月
                return f.plusMonths(1).with(TemporalAdjusters.lastDayOfMonth());
            }
            // m = 0 (3、6、9、12)月 季末月
            return f.with(TemporalAdjusters.lastDayOfMonth());
        });
    }

    /**
     * 季末日期
     *
     * @return
     */
    public static TemporalAdjuster firstDayOfQuarter() {
        return TemporalAdjusters.ofDateAdjuster(f -> {
            int m = f.getMonth().getValue() % 3;
            if (m == 1) {
                // m = 1 (1、4、7、10)月 季初月
                return f.with(TemporalAdjusters.firstDayOfMonth());
            }
            if (m == 2) {
                // m = 2 (2、5、8、11)月 季中月
                return f.minusMonths(1).with(TemporalAdjusters.firstDayOfMonth());
            }
            // m = 0 (3、6、9、12)月 季末月
            return f.minusMonths(2).with(TemporalAdjusters.firstDayOfMonth());
        });
    }

    /**
     * 获取季末日期
     *
     * @param ld
     * @return
     */
    public static LocalDate lastDayOfQuarter(LocalDate ld) {
        return ld.with(lastDayOfQuarter());
    }

    /**
     * 获取季末日期
     *
     * @param date
     * @return
     */
    public static Integer lastDayOfQuarter(Integer date) {
        LocalDate ld = integerToLocalDate(date);
        ld = ld.with(lastDayOfQuarter());
        return localDateToInteger(ld);
    }

    /**
     * 加 num个季度
     *
     * @return 返回季末日期
     */
    public static Integer plusQuarter(Integer date, long num) {
        LocalDate ld = integerToLocalDate(date);
        ld = ld.plusMonths(num * 3).with(lastDayOfQuarter());
        return localDateToInteger(ld);
    }

    /**
     * 减 num个季度
     *
     * @return 返回季末日期
     */
    public static Integer minusQuarter(Integer date, long num) {
        LocalDate ld = integerToLocalDate(date);
        ld = minusQuarter(ld, num);
        return localDateToInteger(ld);
    }

    /**
     * 减 num个季度
     *
     * @return 返回季末日期
     */
    public static LocalDate minusQuarter(LocalDate date, long num) {
        return date.minusMonths(num * 3).with(lastDayOfQuarter());
    }

    /**
     * 返回今天日期
     */
    public static Integer now() {
        return localDateToInteger(LocalDate.now());
    }

    /**
     * @description 将yyyyMMdd日期转换为yyyy-MM-dd格式
     * @param date
     * @return java.lang.String
     * @author: jianjian.yang
     * @date: 2024/11/20 14:07
     * @since JDK 1.8
     */
    public static String convertDashDate(String date){
        int simpleDateLength = 8;
        if(StringUtils.isNotBlank(date) && date.length() == simpleDateLength){
            return date.substring(0, 4) + "-" + date.substring(4, 6) + "-" + date.substring(6, 8);
        }else{
            return date;
        }
    }

    /**
     * @description 获取上个月的最后一天
     * @param
     * @return java.time.LocalDate
     * @author: jianjian.yang
     * @date: 2024/12/16 20:03
     * @since JDK 1.8
     */
    public static LocalDate getLastDayOfPreviousMonth() {
        return LocalDate.now().minusMonths(1).with(TemporalAdjusters.lastDayOfMonth());
    }

    /**
     * @description 获取上个月的最后一天，并格式化为字符串
     * @param formatter
     * @return java.lang.String
     * @author: jianjian.yang
     * @date: 2024/12/16 20:03
     * @since JDK 1.8
     */
    public static String getLastDayOfPreviousMonthAsString(DateTimeFormatter formatter) {
        LocalDate lastDayOfPreviousMonth = getLastDayOfPreviousMonth();
        return lastDayOfPreviousMonth.format(formatter);
    }
}
