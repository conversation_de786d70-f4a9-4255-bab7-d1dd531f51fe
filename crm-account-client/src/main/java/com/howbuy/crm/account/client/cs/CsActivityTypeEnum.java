package com.howbuy.crm.account.client.cs;

/**
 * @description:(客服相关-客户活动类型  1060：好买高端KYC , 1170：好臻高端KYC , 1080： 好买香港开户 )
 * @param
 * @return
 * @author: haoran.zhang
 * @date: 2023/12/8 17:46
 * @since JDK 1.8
 */
public enum CsActivityTypeEnum {

	/**
	 * 1060：好买高端KYC
	 */
	HOWBUY_HIGH_KYC("1060", "好买高端KYC"),
	/**
	 * 1170：好臻高端KYC
	 */
	HZ_HIGH_KYC("1170", "好臻高端KYC"),
	/**
	 * 1080： 好买香港开户
	 */
	HOWBUY_HK_OPEN("1080", "好买香港开户")


	//待补充 。。。。。。
	;

	/**
	 * 编码
	 */
	private String code;

	/**
	 * 描述
	 */
	private String description;

	CsActivityTypeEnum(String code, String description) {
		this.code = code;
		this.description = description;
	}

	/**
	 * 通过code获得
	 *
	 * @param code 系统返回参数编码
	 * @return description 描述
	 */
	public static String getDescription(String code) {
		CsActivityTypeEnum statusEnum = getEnum(code);
		return statusEnum == null ? null : statusEnum.getDescription();
	}

	/**
	 * 通过code直接返回 整个枚举类型
	 *
	 * @param code 系统返回参数编码
	 * @return BaseConstantEnum
	 */
	public static CsActivityTypeEnum getEnum(String code) {
		for (CsActivityTypeEnum statusEnum : CsActivityTypeEnum.values()) {
			if (statusEnum.getCode().equals(code)) {
				return statusEnum;
			}
		}
		return null;
	}


	public String getCode() {
		return code;
	}


	public String getDescription() {
		return description;
	}

}
