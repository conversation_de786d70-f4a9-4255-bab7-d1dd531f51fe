/**
 * Copyright (c) 2025, <PERSON><PERSON><PERSON><PERSON> HOWBUY INVESTMENT MANAGEMENT Co., Ltd.
 * All right reserved.
 * <p>
 * THIS IS UNPUBLISHED PROPRIETARY SOURCE CODE OF HOWBUY INVESTMENT
 * MANAGEMENT CO., LTD.  THE CONTENTS OF THIS FILE MAY NOT BE DISCLOSED
 * TO THIRD PARTIES, COPIED OR DUPLICATED IN ANY FORM, IN WHOLE OR IN PART,
 * WITHOUT THE PRIOR WRITTEN PERMISSION OF HOWBUY INVESTMENT MANAGEMENT
 * CO., LTD.
 */
package com.howbuy.crm.account.client.facade.custinfo;

import com.howbuy.crm.account.client.request.custinfo.HkAcctCustInfoRequest;
import com.howbuy.crm.account.client.response.PageVO;
import com.howbuy.crm.account.client.response.Response;
import com.howbuy.crm.account.client.response.custinfo.HkAcctCustInfoVO;
import com.howbuy.crm.account.client.response.custinfo.HkAcctCustSensitiveInfoVO;

/**
 * @description: (请在此添加描述)
 * <AUTHOR>
 * @date 2025/6/25 10:32
 * @since JDK 1.8
 */
public interface HkCustInfoFacade {

    /**
     * @api {DUBBO}  com.howbuy.crm.account.client.facade.custinfo.HkCustInfoFacade.queryHkCustInfoPage(hkCustInfoRequest) queryHkCustInfoPage()
     * @apiVersion 1.0.0
     * @apiGroup HkCustInfoFacade
     * @apiName queryHkCustInfoPage()
     * @apiDescription 分页查询 香港客户列表
     * @apiParam (请求参数) {String} hkCustNo 香港客户号
     * @apiParam (请求参数) {Array} hkCustNoList 香港客户号列表
     * @apiParam (请求参数) {String} mobileAreaCode 手机 区号
     * @apiParam (请求参数) {String} mobileDigest 手机号
     * @apiParam (请求参数) {String} emailDigest 邮箱
     * @apiParam (请求参数) {String} idNoDigest 证件号
     * @apiParam (请求参数) {String} hboneNo 一账通号
     * @apiParam (请求参数) {String} custCnOrEnName 客户中文或英文名称
     * @apiParam (请求参数) {String} invstType 投资者类型
     * @apiParam (请求参数) {String} ebrokerId ebrokerId
     * @apiParam (请求参数) {Array} custStatList 客户状态 列表      {@link com.howbuy.crm.account.client.enums.custinfo.HkAcctCustStatusEnum}
     * @apiParam (请求参数) {String} openType 开户方式 0-线下 1-线上
     * @apiParam (请求参数) {String} openDateStart 开户日期 开始
     * @apiParam (请求参数) {String} openDateEnd 开户日期 结束
     * @apiParam (请求参数) {Number} page 页号
     * @apiParam (请求参数) {Number} rows 每页大小
     * @apiParamExample 请求参数示例
     * hkCustNoList=zGjcHw7O&hkCustNo=I&openDateEnd=bm3S9l&emailDigest=K1uIgvz&idNoDigest=6Xdvh&rows=999&openDateStart=rPh1&ebrokerId=xDj58phP&openType=GHtjIp6u&mobileAreaCode=qFbL&invstType=nlmsYbJGm&mobileDigest=AIUPb4u0&page=2745&hboneNo=G0B90cm9&custCnOrEnName=9ke15eg56L&custStatList=XYa7q
     * @apiSuccess (响应结果) {String} code 状态码
     * @apiSuccess (响应结果) {String} description 描述信息
     * @apiSuccess (响应结果) {Object} data 数据封装
     * @apiSuccess (响应结果) {Number} data.page 当前第几页
     * @apiSuccess (响应结果) {Number} data.size 单页条数
     * @apiSuccess (响应结果) {Number} data.total 总条数
     * @apiSuccess (响应结果) {Array} data.rows 数据对象列表
     * @apiSuccess (响应结果) {String} data.rows.custNo CRM 客户号
     * @apiSuccess (响应结果) {String} data.rows.hkTxAcctNo 香港交易账号
     * @apiSuccess (响应结果) {String} data.rows.ebrokerId ebrokerId
     * @apiSuccess (响应结果) {String} data.rows.custChineseName 香港客户中文名
     * @apiSuccess (响应结果) {String} data.rows.custEnName 香港客户英文名
     * @apiSuccess (响应结果) {String} data.rows.hkCustStatus {@link com.howbuy.crm.account.client.enums.custinfo.HkAcctCustStatusEnum}      香港客户状态-   客户状态 0-正常 1-注销 2-休眠 3-注册4-开户申请成功
     * @apiSuccess (响应结果) {String} data.rows.birthDt 出生日期
     * @apiSuccess (响应结果) {String} data.rows.idNoDigest 证件号码
     * @apiSuccess (响应结果) {String} data.rows.idNoMask 证件号码
     * @apiSuccess (响应结果) {String} data.rows.idSignAreaCode 证件地区码
     * @apiSuccess (响应结果) {String} data.rows.idType 证件类型
     * @apiSuccess (响应结果) {String} data.rows.invstType 投资者类型 0-机构,1-个人,2-产品户
     * @apiSuccess (响应结果) {String} data.rows.mobileAreaCode 手机号码区号
     * @apiSuccess (响应结果) {String} data.rows.mobileDigest 手机号码
     * @apiSuccess (响应结果) {String} data.rows.mobileMask 手机号码
     * @apiSuccess (响应结果) {String} data.rows.mobileVerifyStatus 手机号码 验证状态      0:未验证 1:已验证
     * @apiSuccess (响应结果) {String} data.rows.emailDigest 邮件
     * @apiSuccess (响应结果) {String} data.rows.emailMask 邮件
     * @apiSuccess (响应结果) {String} data.rows.emailVerifyStatus 邮件 验证状态      0:未验证 1:已验证
     * @apiSuccess (响应结果) {String} data.rows.hboneNo 关联的一账通号
     * @apiSuccess (响应结果) {String} data.rows.openType 开户方式 0-线下 1-线上
     * @apiSuccess (响应结果) {String} data.rows.openDate 开户日期
     * @apiSuccessExample 响应结果示例
     * {"code":"R4s16DX","data":{"total":8615,"size":1097,"page":9671,"rows":[{"custNo":"T6mQqst","birthDt":"sjLw1eom","idType":"1x","hkCustStatus":"yzmq","mobileVerifyStatus":"jVj1zjD","idNoDigest":"Hdm38v4NJ","emailDigest":"bUDfe","emailVerifyStatus":"38EyUn","idNoMask":"tEC61","ebrokerId":"Q2T8","openType":"wrcd3qoxt","hkTxAcctNo":"vR3QfVjMD","mobileAreaCode":"up","idSignAreaCode":"z","invstType":"fB69o9Hi","mobileDigest":"77v4X","emailMask":"YrXJWBzGN","custChineseName":"WOX3","openDate":"z","custEnName":"EjceIWwRo","mobileMask":"BG","hboneNo":"Yjno1n"}]},"description":"ke"}
     */
    Response<PageVO<HkAcctCustInfoVO>> queryHkCustInfoPage(HkAcctCustInfoRequest hkCustInfoRequest);


    /**
     * @api {DUBBO}  com.howbuy.crm.account.client.facade.custinfo.HkCustInfoFacade.queryHkCustSensitiveInfo()
     * @apiVersion 1.0.0
     * @apiGroup HkCustInfoFacade
     * @apiName queryHkCustSensitiveInfo()
     * @apiDescription 查询香港客户敏感信息
     * @apiParam (请求参数) {String} hkTxAcctNo
     * @apiParamExample 请求参数示例
     * hkTxAcctNo=M14fd
     * @apiSuccess (响应结果) {String} code 状态码
     * @apiSuccess (响应结果) {String} description 描述信息
     * @apiSuccess (响应结果) {Object} data 数据封装
     * @apiSuccess (响应结果) {String} data.idNo 证件号码
     * @apiSuccess (响应结果) {String} data.email 邮箱
     * @apiSuccess (响应结果) {String} data.statementEmail 结单邮箱
     * @apiSuccess (响应结果) {String} data.residenceCnAddr 现住址的中文地址
     * @apiSuccess (响应结果) {String} data.residenceEnAddr 现住址的英文地址
     * @apiSuccess (响应结果) {String} data.mailingCnAddr 通讯地址的中文地址
     * @apiSuccess (响应结果) {String} data.mailingEnAddr 通讯地址的英文地址
     * @apiSuccess (响应结果) {String} data.birthAddr 出生地地址
     * @apiSuccess (响应结果) {String} data.emplAddr 就业地址
     * @apiSuccessExample 响应结果示例
     * {"code":"XPfo","data":{"residenceCnAddr":"WOPzo6","mailingEnAddr":"oFXOm0","emplAddr":"T","residenceEnAddr":"Ofb8fjV8","idNo":"fg","email":"F3aJEk71H0","statementEmail":"4rve2aYMpt","mailingCnAddr":"9y","birthAddr":"J33r"},"description":"r48Gx"}
     */
    Response<HkAcctCustSensitiveInfoVO> queryHkCustSensitiveInfo(String hkTxAcctNo);

}
