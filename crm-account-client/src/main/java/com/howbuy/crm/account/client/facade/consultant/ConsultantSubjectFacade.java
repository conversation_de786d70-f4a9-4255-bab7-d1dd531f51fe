package com.howbuy.crm.account.client.facade.consultant;

import com.howbuy.crm.account.client.request.consultant.ConsultantSubjectRequest;
import com.howbuy.crm.account.client.response.Response;
import com.howbuy.crm.account.client.response.consultant.ConsultantSubjectResponse;

/**
 * @description 投顾主体信息查询接口
 * <AUTHOR>
 * @date 2024-06-04 17:12:42
 */
public interface ConsultantSubjectFacade {

    /**
     * @api {DUBBO} com.howbuy.crm.account.client.facade.consultant.ConsultantSubjectFacade.queryConsultantSubject() queryConsultantSubject()
     * @apiVersion 1.0.0
     * @apiGroup ConsultantSubjectFacade
     * @apiName queryConsultantSubject()
     * @apiDescription 投顾主体信息查询接口
     * @apiParam (请求体) {String} conscode 投顾编号
     * @apiParamExample 请求体示例
     * {"conscode":"123456"}
     * @apiSuccess (响应结果) {String} code 状态码
     * @apiSuccess (响应结果) {String} description 描述信息
     * @apiSuccess (响应结果) {Object} data 数据封装
     * @apiSuccess (响应结果) {String} data.sszt 投顾所属主体 1-好买基金 2-好买香港
     * @apiSuccessExample 响应结果示例
     * {"code":"0000","description":"成功","data":{"sszt":"1"}}
     */
    Response<ConsultantSubjectResponse> queryConsultantSubject(ConsultantSubjectRequest request);
} 