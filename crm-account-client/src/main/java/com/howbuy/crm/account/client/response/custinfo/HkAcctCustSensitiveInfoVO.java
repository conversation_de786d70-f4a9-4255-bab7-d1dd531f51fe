package com.howbuy.crm.account.client.response.custinfo;

import lombok.Data;

import java.io.Serializable;

/**
 * @description:(香港客户信息-单个客户 敏感加密信息 )
 * @param
 * @return
 * @author: haoran.zhang
 * @date: 2023/12/19 19:17
 * @since JDK 1.8
 */
@Data
public class HkAcctCustSensitiveInfoVO implements Serializable {

    private static final long serialVersionUID = 3155067223030438673L;

    /**
     * 证件号码
     */
    private String idNo;
    /**
     * 邮箱
     */
    private String email;
    /**
     * 结单邮箱
     */
    private String statementEmail;
    /**
     * 现住址的中文地址
     */
    private String residenceCnAddr;
    /**
     * 现住址的英文地址
     */
    private String residenceEnAddr;
    /**
     * 通讯地址的中文地址
     */
    private String mailingCnAddr;
    /**
     * 通讯地址的英文地址
     */
    private String mailingEnAddr;
    /**
     * 出生地地址
     */
    private String birthAddr;
    /**
     * 就业地址
     */
    private String emplAddr;


}
