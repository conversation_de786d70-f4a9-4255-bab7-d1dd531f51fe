/**
 * Copyright (c) 2024, <PERSON><PERSON><PERSON><PERSON> HOWBUY INVESTMENT MANAGEMENT Co., Ltd.
 * All right reserved.
 * <p>
 * THIS IS UNPUBLISHED PROPRIETARY SOURCE CODE OF HOWBUY INVESTMENT
 * MANAGEMENT CO., LTD.  THE CONTENTS OF THIS FILE MAY NOT BE DISCLOSED
 * TO THIRD PARTIES, COPIED OR DUPLICATED IN ANY FORM, IN WHOLE OR IN PART,
 * WITHOUT THE PRIOR WRITTEN PERMISSION OF HOWBUY INVESTMENT MANAGEMENT
 * CO., LTD.
 */
package com.howbuy.crm.account.client.request.consultant;

import com.howbuy.crm.account.client.request.PageRequest;
import lombok.Getter;
import lombok.Setter;

import java.io.Serializable;

/**
 * @description: 查询人员绩效系数列表请求对象
 * <AUTHOR>
 * @date 2025-07-11 16:00:00
 * @since JDK 1.8
 */
@Getter
@Setter
public class QueryConsPerformanceCoeffListRequest extends PageRequest implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 部门代码（必填，带数据广度权限）
     */
    private String orgCode;

    /**
     * 投顾code（必填，带数据广度权限）
     */
    private String consCode;

    /**
     * 分配开始时间（YYYYMMDD，可空）
     */
    private String startAssignTime;

    /**
     * 分配结束时间（YYYYMMDD，可空）
     */
    private String endAssignTime;

    /**
     * 投顾客户号（可空）
     */
    private String consCustNo;

    /**
     * 排序字段（可空）
     */
    private String sortField;

    /**
     * 排序方式（asc-升序，desc-降序，可空）
     */
    private String sortOrder;
}