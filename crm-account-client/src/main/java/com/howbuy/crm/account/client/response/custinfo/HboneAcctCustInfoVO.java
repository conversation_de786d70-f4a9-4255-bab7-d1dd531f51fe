package com.howbuy.crm.account.client.response.custinfo;


import lombok.Data;

import java.io.Serializable;

/**
 * 账户中心 侧  客户信息属性
 * 来源：com.howbuy.acccenter.facade.query.queryhboneinfo.QueryAccHboneInfoResponse
 */
@Data
public class HboneAcctCustInfoVO implements Serializable {

    private static final long serialVersionUID = 8901600870079379109L;
    private String userType;
    /**
     * 一账通 客户名称
     */
    private String custName;
    private String username;
    /**
     * 证件类型
     */
    private String idType;

    /**
     * 证件地区码-身份证签发地区编码
     */
    private String idSignAreaCode;
    /**
     * 证件号码
     */
    private String idNoDigest;
    /**
     * 证件号码 mask
     */
    private String idNoMask;



    /**
     * 手机号码区号
     */
    private String mobileAreaCode;

    /**
     * 手机号码
     */
    private String mobileDigest;
    /**
     * 手机号码 mask
     */
    private String mobileMask;
    /**
     * 手机号码验证状态
     */
    private String mobileVerifyStatus;
    /**
     * 注册网点
     */
    private String regOutletCode;
    /**
     * 注册时间
     */
    private String regDt;
    /**
     *  实名网点号
     */
    private String authOutletCode;
    /**
     * 实名日期
     */
    private String authDt;
    /**
     * 是否有登录密码
     */
    private boolean existPassword;
    /**
     *性别 0-女;1-男;
    */
    private String gender;


    /**
     * 一账通号
     */
    private String hboneNo;



}
