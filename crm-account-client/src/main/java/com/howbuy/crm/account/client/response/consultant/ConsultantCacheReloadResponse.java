package com.howbuy.crm.account.client.response.consultant;

import lombok.Getter;
import lombok.Setter;
import lombok.ToString;

import java.io.Serializable;

/**
 * @description: 重新加载投顾缓存响应结果
 * <AUTHOR>
 * @date 2025/3/10
 * @since JDK 1.8
 */
@Getter
@Setter
@ToString
public class ConsultantCacheReloadResponse implements Serializable {
    
    private static final long serialVersionUID = 1L;
    
    /**
     * 是否重新加载成功
     */
    private Boolean success;
    
    /**
     * 重新加载结果描述
     */
    private String message;
} 