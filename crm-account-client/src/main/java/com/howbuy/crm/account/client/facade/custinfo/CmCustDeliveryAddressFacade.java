/**
 * Copyright (c) 2024, <PERSON><PERSON><PERSON><PERSON> HOWBUY INVESTMENT MANAGEMENT Co., Ltd.
 * All right reserved.
 * <p>
 * THIS IS UNPUBLISHED PROPRIETARY SOURCE CODE OF HOWBUY INVESTMENT
 * MANAGEMENT CO., LTD.  THE CONTENTS OF THIS FILE MAY NOT BE DISCLOSED
 * TO THIRD PARTIES, COPIED OR DUPLICATED IN ANY FORM, IN WHOLE OR IN PART,
 * WITHOUT THE PRIOR WRITTEN PERMISSION OF HOWBUY INVESTMENT MANAGEMENT
 * CO., LTD.
 */
package com.howbuy.crm.account.client.facade.custinfo;

import com.howbuy.crm.account.client.request.custinfo.QueryCmCustDeliveryAddrRequest;
import com.howbuy.crm.account.client.response.Response;
import com.howbuy.crm.account.client.response.custinfo.CmCustDeliveryAddrVO;

/**
 * @description: 投顾版客户收货地址
 * <AUTHOR>
 * @date 2024/11/19 10:33
 * @since JDK 1.8
 */

public interface CmCustDeliveryAddressFacade {


    /**
     * @api {DUBBO} com.howbuy.crm.account.client.facade.custinfo.CmCustDeliveryAddressFacade.queryDeliveryAddrInfo(request)
     * @apiVersion 1.0.0
     * @apiGroup CmCustDeliveryAddressFacade
     * @apiName queryDeliveryAddrInfo()
     * @apiDescription 查询投顾版的客户收货地址
     * @apiParam (请求参数) {String} hboneNo 一账通号
     * @apiParamExample 请求参数示例
     * hboneNo=kkGZXm2C
     * @apiSuccess (响应结果) {String} code 状态码
     * @apiSuccess (响应结果) {String} description 描述信息
     * @apiSuccess (响应结果) {Object} data 数据封装
     * @apiSuccess (响应结果) {Number} data.id ID
     * @apiSuccess (响应结果) {String} data.conscustno 投顾客户号
     * @apiSuccess (响应结果) {String} data.hboneNo 一账通号
     * @apiSuccess (响应结果) {String} data.receiverName 收货人姓名
     * @apiSuccess (响应结果) {String} data.mobileDigest 收货人手机号摘要
     * @apiSuccess (响应结果) {String} data.mobileMask 收货人手机号掩码
     * @apiSuccess (响应结果) {String} data.mobileCipher 收货人手机号密文
     * @apiSuccess (响应结果) {String} data.provCode 省编码
     * @apiSuccess (响应结果) {String} data.cityCode 市编码
     * @apiSuccess (响应结果) {String} data.countyCode 区编码
     * @apiSuccess (响应结果) {String} data.addrDigest 详细地址摘要
     * @apiSuccess (响应结果) {String} data.addrMask 详细地址掩码
     * @apiSuccess (响应结果) {String} data.addrCipher 详细地址密文
     * @apiSuccess (响应结果) {String} data.remark 备注
     * @apiSuccess (响应结果) {Number} data.createTimestamp 创建时间
     * @apiSuccess (响应结果) {Number} data.modifyTimestamp 修改时间
     * @apiSuccessExample 响应结果示例
     * {"code":"mA","data":{"provCode":"i","receiverName":"U","cityCode":"lI","addrCipher":"a4o1VK","remark":"Fue37","mobileCipher":"mJ","addrMask":"PX2ZzU","createTimestamp":1585063233142,"modifyTimestamp":46891577599,"conscustno":"gbOrMkt","countyCode":"RVN3GA","mobileDigest":"vlb","addrDigest":"Y5VPdE0M","id":772.9526266777631,"hboneNo":"6lr8j0h","mobileMask":"TL"},"description":"D6CwvnZ5gr"}
     */
    Response<CmCustDeliveryAddrVO> queryDeliveryAddrInfo(QueryCmCustDeliveryAddrRequest request);


}
