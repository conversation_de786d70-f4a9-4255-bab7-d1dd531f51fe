package com.howbuy.crm.account.client.enums.custinfo;

/**
 * @description:(业务性质 :1-会计/法律事务所 2-银行/金融/保险 3-建筑/基建/地产
 * 4-咨询 5-教育/培训 6-政府/公用事业/非盈利组织
 * 7-酒店/旅游 8-信息/技术 9-物流
 * 10-制造 11-媒体/出版社/娱乐 12-医疗/健康服务
 * 13-其他 14-科研 15-批发/零售 16-销售/市场/广告)
 * @param
 * @return
 * @author: haoran.zhang
 * @date: 2023/12/19 13:43
 * @since JDK 1.8
 */
public enum EmplNatureEnum {
    /**
     * 会计/律师事务所
     */
    ACCOUNTANT("1","会计/律师事务所"),
    /**
     * 银行/金融/保险
     */
    BANK("2","银行/金融/保险"),
    /**
     * 建筑/基建/地产
     */
    CONSTRUCTION("3","建筑/基建/地产"),
    /**
     * 咨询
     */
    CONSULTING("4","咨询"),
    /**
     * 教育/培训
     */
    EDUCATION("5","教育/培训"),
    /**
     * 政府/公用事业/非盈利组织
     */
    GOVERNMENT("6","政府/公用事业/非盈利组织"),
    /**
     * 酒店/旅游
     */
    HOTEL("7","酒店/旅游"),
    /**
     * 信息/技术
     */
    INFORMATION("8","信息/技术"),
    /**
     * 物流
     */
    LOGISTICS("9","物流"),
    /**
     * 制造
     */
    MANUFACTURING("10","制造"),
    /**
     * 媒体/出版社/娱乐
     */
    MEDIA("11","媒体/出版社/娱乐"),
    /**
     * 医疗/健康服务
     * */
    MEDICAL("12","医疗/健康服务"),
    /**
     * 其他
     */
    OTHER("13","其他"),
    /**
     * 科研
     */
    RESEARCH("14","科研"),
    /**
     * 批发/零售
     */
    WHOLESALE("15","批发/零售"),
    /**
     * 销售/市场/广告
     */
    SALES("16","销售/市场/广告"),
    ;


    /**编码*/
    private String code;
    /**描述*/
    private String description;

    private EmplNatureEnum(String code, String description){
        this.code=code;
        this.description=description;
    }

    /**
     * 通过code获得
     * @param code	系统返回参数编码
     * @return description 描述
     */
    public static String getDescription(String code){
        for(EmplNatureEnum b: EmplNatureEnum.values()){
            if(b.getCode().equals(code)){
                return b.description;
            }
        }
        return null;
    }

    /**
     * 通过code直接返回 整个枚举类型
     * @param code 系统返回参数编码
     * @return BaseConstantEnum
     */
    public static EmplNatureEnum getEnum(String code){
        if(code != null && !"".equals(code)){
            for(EmplNatureEnum b: EmplNatureEnum.values()){
                if(b.getCode().equals(code)){
                    return b;
                }
            }
        }
        return null;
    }

    public String getCode() {
        return code;
    }


    public String getDescription() {
        return description;
    }
}
