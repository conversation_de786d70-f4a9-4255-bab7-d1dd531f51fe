/**
 * Copyright (c) 2024, <PERSON><PERSON><PERSON><PERSON> HOWBUY INVESTMENT MANAGEMENT Co., Ltd.
 * All right reserved.
 * <p>
 * THIS IS UNPUBLISHED PROPRIETARY SOURCE CODE OF HOWBUY INVESTMENT
 * MANAGEMENT CO., LTD.  THE CONTENTS OF THIS FILE MAY NOT BE DISCLOSED
 * TO THIRD PARTIES, COPIED OR DUPLICATED IN ANY FORM, IN WHOLE OR IN PART,
 * WITHOUT THE PRIOR WRITTEN PERMISSION OF HOWBUY INVESTMENT MANAGEMENT
 * CO., LTD.
 */
package com.howbuy.crm.account.client.response.commvisit;

import java.io.Serializable;
import java.util.List;

/**
 * @description: 沟通拜访列表响应
 * <AUTHOR>
 * @date 2024/10/24 9:36
 * @since JDK 1.8
 */
public class CsCommVisitListVO implements Serializable {

    private static final long serialVersionUID = -3113882273165828206L;

    /**
     * 沟通拜访记录列表
     */
    private List<CsCommVisitVO> commVisitVOList;

    public List<CsCommVisitVO> getCommVisitVOList() {
        return commVisitVOList;
    }

    public void setCommVisitVOList(List<CsCommVisitVO> commVisitVOList) {
        this.commVisitVOList = commVisitVOList;
    }
}
