/**
 * Copyright (c) 2023, <PERSON><PERSON><PERSON><PERSON> HOWBUY INVESTMENT MANAGEMENT Co., Ltd.
 * All right reserved.
 * <p>
 * THIS IS UNPUBLISHED PROPRIETARY SOURCE CODE OF HOWBUY INVESTMENT
 * MANAGEMENT CO., LTD.  THE CONTENTS OF THIS FILE MAY NOT BE DISCLOSED
 * TO THIRD PARTIES, COPIED OR DUPLICATED IN ANY FORM, IN WHOLE OR IN PART,
 * WITHOUT THE PRIOR WRITTEN PERMISSION OF HOWBUY INVESTMENT MANAGEMENT
 * CO., LTD.
 */
package com.howbuy.crm.account.client.response.dictionary;

import lombok.Data;

/**
 * @description: 省市区名称实体类，用于存放省市区名称信息
 * <AUTHOR>
 * @date 2023/12/28 17:15
 * @since JDK 1.8
 */

@Data
public class ProvCityCountyNameVO {

    private String provName = "";

    private String cityName = "";

    private String countyName = "";

}