package com.howbuy.crm.account.client.response.commvisit;

import lombok.Getter;
import lombok.Setter;

import java.io.Serializable;
import java.util.List;

/**
 * @description: 用户搜索响应
 * @author: jianjian.yang
 * @date: 2025-04-09 16:04:00
 */
@Getter
@Setter
public class SearchUserVO implements Serializable {

    private List<UserItem> userList;

    @Getter
    @Setter
    public static class UserItem implements Serializable {

        private static final long serialVersionUID = 1L;
        /**
         * 用户ID
         */
        private String userId;

        /**
         * 用户名
         */
        private String userName;
    }
} 