/**
 * Copyright (c) 2023, <PERSON><PERSON><PERSON><PERSON> HOWBUY INVESTMENT MANAGEMENT Co., Ltd.
 * All right reserved.
 * <p>
 * THIS IS UNPUBLISHED PROPRIETARY SOURCE CODE OF HOWBUY INVESTMENT
 * MANAGEMENT CO., LTD.  THE CONTENTS OF THIS FILE MAY NOT BE DISCLOSED
 * TO THIRD PARTIES, COPIED OR DUPLICATED IN ANY FORM, IN WHOLE OR IN PART,
 * WITHOUT THE PRIOR WRITTEN PERMISSION OF HOWBUY INVESTMENT MANAGEMENT
 * CO., LTD.
 */
package com.howbuy.crm.account.client.request.custinfo;

import com.howbuy.crm.account.client.request.PageRequest;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.io.Serializable;
import java.util.List;

/**
 * @description: (查询[香港/一账通]  异常客户  request 参数对象)
 * <AUTHOR>
 * @date 2023/12/11 13:17
 * @since JDK 1.8
 */
@Data
@EqualsAndHashCode(callSuper = true)
public class AbnormalCustInfoRequest extends PageRequest implements Serializable {

    private static final long serialVersionUID = 1L;


    /**
     * 客户姓名
     */
    private String custName;

    /**
     * 香港客户号
     */
    private String hkTxAcctNo;


    /**
     * 一账通号
     */
    private String hboneNo;


    /**
     * 客户号
     */
    private String custNo;



    /**
     * 手机号
     */
    private String mobileDigest;


    /**
     * 证件号
     */
    private String idNoDigest;



    /**
     * 异常来源：1-香港注册2-香港开户 7-好买开户 [暂时同于  7-分销开户]3-香港客户信息同步4-香港客户号/一账通绑定5-一账通实名
     */
    private String abnormalSource;



    /**
     * 处理状态：0-未处理 1-已处理 2-无需处理
     */
    private String dealStatus;



    /**
     * 创建时间-开始
     * yyyyMMdd
     */
    private String createBginDdate;

    /**
     * 创建时间-结束
     * yyyyMMdd
     */
    private String createEndDate;


    /**
     * 异常id
     */
    private String abnormalId;

    /**
     * 异常等级
     */
    private String abnormalLevel;

}