package com.howbuy.crm.account.client.enums;

/**
 * @description: 交易渠道的枚举  1-柜台；2-网站；3-电话；4-Wap(小程序)；5-储蓄罐；9-CRM-PC；10-CRM移动端，11-APP，12-H5
 * <AUTHOR>
 * @date 2024年8月12日
 * @since JDK 1.8
 */
@Deprecated
public enum ChannelEnum {

	/**
	 * 1-柜台
	 */
	BAR("1", "柜台"),

	/**
	 * 2-网站
	 */
	WEB("2", "网站"),

	/**
	 * 3-电话
	 */
	PHONE("3", "电话"),

	/**
	 * 4-WAP
	 */
	WAP("4", "Wap"),

	/**
	 * 5-储蓄罐
	 */
	PIGGY("5", "储蓄罐"),

	/**
	 * 9-CRM-PC
	 */
	CRM_PC("9", "CRM-PC"),

	/**
	 * 10-CRM-移动端
	 */
	CRM_MOBILE("10", "CRM移动端"),

	/**
	 * 11-小程序
	 */
	APP("11", "小程序"),
	/**
	 * 12-H5
	 */
	H5("12", "H5")
	;


	/**
	 * 编码
	 */
	private String code;

	/**
	 * 描述
	 */
	private String description;

	 ChannelEnum(String code, String description) {
		this.code = code;
		this.description = description;
	}


	/**
	 * 通过code获得
	 * 
	 * @param code
	 *            系统返回参数编码
	 * @return description 描述
	 */
	public static String getDescription(String code) {
		ChannelEnum statusEnum=getEnum(code);
		return statusEnum==null?null :statusEnum.getDescription();
	}

	/**
	 * 通过code直接返回 整个枚举类型
	 * 
	 * @param code
	 *            系统返回参数编码
	 * @return PreOccupyTypeEnum
	 */
	public static ChannelEnum getEnum(String code) {
		for(ChannelEnum statusEnum : ChannelEnum.values()){
			if(statusEnum.getCode().equals(code)){
				return statusEnum;
			}
		}
		return null;
	}




	public String getCode() {
		return code;
	}


	public String getDescription() {
		return description;
	}


}
