package com.howbuy.crm.account.client.enums.hkcustinfo;

/**
 * @description:(香港账户中心 全局的审核状态 0-不需审核 1-等待审核 2-等待复核 3-审核通过 4-审核不通过/作废 5-驳回至初审 6-驳回至客户  7-作废)
 * @return
 * @author: haoran.zhang
 * @date: 2024/7/24 14:44
 * @since JDK 1.8
 */
public enum HkAcctTxChkFlagEnum {

    /**
     * 0-不需审核
     */
    NO_NEED_CHECK("0", "不需审核"),
	/**
	 * 1-等待审核
	 */
	WAIT_CHECK("1", "等待审核"),
    /**
     * 2-等待复核
     */
    WAIT_REVIEW("2", "等待复核"),
    /**
     * 3-审核通过
     */
    CHECK_PASS("3", "审核通过"),
    /**
     * 4-审核不通过/作废
     */
    CHECK_FAIL("4", "审核不通过/作废"),
    /**
     * 5-驳回至初审
     */
    REJECT_TO_FIRST("5", "驳回至初审"),
    /**
     * 6-驳回至客户
     */
    REJECT_TO_CUST("6", "驳回至客户"),

	/**
	 * 7-作废
	 */
	INVALID("7", "作废");
	;


	/**
	 * 编码
	 */
	private String code;

	/**
	 * 描述
	 */
	private String description;

	 HkAcctTxChkFlagEnum(String code, String description) {
		this.code = code;
		this.description = description;
	}


	/**
	 * 通过code获得
	 * 
	 * @param code
	 *            系统返回参数编码
	 * @return description 描述
	 */
	public static String getDescription(String code) {
		HkAcctTxChkFlagEnum statusEnum=getEnum(code);
		return statusEnum==null?null :statusEnum.getDescription();
	}

	/**
	 * 通过code直接返回 整个枚举类型
	 * 
	 * @param code
	 *            系统返回参数编码
	 * @return PreOccupyTypeEnum
	 */
	public static HkAcctTxChkFlagEnum getEnum(String code) {
		for(HkAcctTxChkFlagEnum statusEnum : HkAcctTxChkFlagEnum.values()){
			if(statusEnum.getCode().equals(code)){
				return statusEnum;
			}
		}
		return null;
	}




	public String getCode() {
		return code;
	}


	public String getDescription() {
		return description;
	}


}
