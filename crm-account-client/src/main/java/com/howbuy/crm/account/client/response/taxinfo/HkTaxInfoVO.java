/**
 * Copyright (c) 2024, <PERSON><PERSON><PERSON><PERSON> HOWBUY INVESTMENT MANAGEMENT Co., Ltd.
 * All right reserved.
 * <p>
 * THIS IS UNPUBLISHED PROPRIETARY SOURCE CODE OF HOWBUY INVESTMENT
 * MANAGEMENT CO., LTD.  THE CONTENTS OF THIS FILE MAY NOT BE DISCLOSED
 * TO THIRD PARTIES, COPIED OR DUPLICATED IN ANY FORM, IN WHOLE OR IN PART,
 * WITHOUT THE PRIOR WRITTEN PERMISSION OF HOWBUY INVESTMENT MANAGEMENT
 * CO., LTD.
 */
package com.howbuy.crm.account.client.response.taxinfo;

import lombok.Data;

import java.io.Serializable;

/**
 * @description: 香港税务信息VO类
 * <AUTHOR>
 * @date 2024/1/11 14:52
 * @since JDK 1.8
 */
@Data
public class HkTaxInfoVO implements Serializable {

    private static final long serialVersionUID = -2526649822436401377L;

    /**
     * 就业状况 01-雇主 02-全职 03-兼职 04-主妇 05-学生 06-退休 07-非在职
     */
    private String emplStatus;

    /**
     * 就业公司名称
     */
    private String emplCompanyName;

    /**
     * 就业公司地址-国家/地区代码
     */
    private String emplCountryCode;

    /**
     * 就业公司地址-省代码
     */
    private String emplProvCode;

    /**
     * 就业公司地址-市代码
     */
    private String emplCityCode;

    /**
     * 就业公司地址-县代码
     */
    private String emplCountyCode;

    /**
     * 就业公司地址（英文）-城/镇
     */
    private String emplTown;

    /**
     * 就业公司地址（英文）-省/州
     */
    private String emplState;

    /**
     * 就业公司地址-详细地址摘要
     */
    private String emplAddrDigest;

    /**
     * 就业公司地址-详细地址掩码
     */
    private String emplAddrMask;

    /**
     * 就业公司业务性质
     */
    private String emplNatureOfBusiness;

    /**
     * 就业公司业务性质说明
     */
    private String emplNatureOfBusinessDesc;

    /**
     * 就业职位/称衔
     */
    private String emplDesignation;

    /**
     * 就业每年收入 01-≦HK$500000 02-HK$500001-HK$1000000 03-HK$1000001-HK$2000000 04-HK$2000001-HK$5000000 05->HK$5000000
     */
    private String emplIncLevel;

    /**
     * 税务管辖区国家代码
     */
    private String taxJurisdiction;

    /**
     * 是否有税务编号 0-否 1-是
     */
    private String hasTin;

    /**
     * 无税务编号理由 01-账户持有人的居留司法税务管辖区并没有向其居民发出税务编号 02-账户持有人不能取得税务编号 03-账户持有人无须提供税务编号
     */
    private String noTinReason;

    /**
     * 不能取的税务编号的原因
     */
    private String noObtainTinReason;

    /**
     * 税务编号类型 01-身份证 02-其他
     */
    private String tinType;

    /**
     * 税务编号类型说明
     */
    private String tinTypeDesc;

    /**
     * 税务编号
     */
    private String tin;
}