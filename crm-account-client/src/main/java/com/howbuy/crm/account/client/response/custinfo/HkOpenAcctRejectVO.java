/**
 * Copyright (c) 2024, <PERSON><PERSON><PERSON><PERSON> HOWBUY INVESTMENT MANAGEMENT Co., Ltd.
 * All right reserved.
 * <p>
 * THIS IS UNPUBLISHED PROPRIETARY SOURCE CODE OF HOWBUY INVESTMENT
 * MANAGEMENT CO., LTD.  THE CONTENTS OF THIS FILE MAY NOT BE DISCLOSED
 * TO THIRD PARTIES, COPIED OR DUPLICATED IN ANY FORM, IN WHOLE OR IN PART,
 * WITHOUT THE PRIOR WRITTEN PERMISSION OF HOWBUY INVESTMENT MANAGEMENT
 * CO., LTD.
 */
package com.howbuy.crm.account.client.response.custinfo;

import lombok.Data;

import java.io.Serializable;
import java.util.Map;

/**
 * <AUTHOR>
 * @description: (请在此添加描述)
 * @date 2024/1/12 14:09
 * @since JDK 1.8
 */
@Data
public class HkOpenAcctRejectVO implements Serializable {

    private static final long serialVersionUID = -6365163911373333130L;

    /**  *********************  客户基础信息的拒绝信息  *********************  */
    /**
     * 客户中文姓名
     */
    private String custChineseName;
    /**
     * 中文姓氏
     */
    private String cnSurname;
    /**
     * 中文名
     */
    private String cnGivenName;
    /**
     * 英文姓名
     */
    private String custEnName;
    /**
     * 英文姓氏
     */
    private String enSurname;
    /**
     * 英文名
     */
    private String enGivenName;
    /**
     * 性别 0-女 1-男 2-非自然人
     */
    private String gender;
    /**
     * 曾用中文名
     */
    private String cnFormerName;
    /**
     * 曾用英文名
     */
    private String enFormerName;
    /**
     * 出生日期
     */
    private String birthday;
    /**
     * 国籍
     */
    private String nationality;
    /**
     * 婚姻状况 0-未婚 1-已婚 2-其他
     */
    private String marriageStat;
    /**
     * 婚姻状况描述
     */
    private String marriageStatDesc;
    /**
     * 教育水平 01-小学或以下 02-中学 03-大专或预科 04-大学或本科 05-硕士或以上
     */
    private String eduLevel;
    /**
     * 手机号码的地区编码
     */
    private String mobileAreaCode;
    /**
     * 手机号码
     */
    private String mobile;
    /**
     * 电子邮件地址
     */
    private String email;
    /**
     * 对账电子邮件地址
     */
    private String statementEmail;
    /**
     * 证件签发地区编码
     */
    private String idSignAreaCode;
    /**
     * 身份证类型
     */
    private String idType;
    /**
     * 身份证号码
     */
    private String idNo;
    /**
     * 证件是否长期有效
     */
    private String idAlwaysValidFlag;
    /**
     * 身份证有效期结束时间
     */
    private String idValidityEnd;
    /**
     * 现住址国家编码
     */
    private String residenceCountryCode;
    /**
     * 现住址英文国家名称
     */
    private String residenceEnCountry;

    /**
     * 通讯地址国家编码
     */
    private String mailingCountryCode;
    /**
     * 通讯地址英文国家名称
     */
    private String mailingEnCountry;
    /**
     * 出生地国家编码
     */
    private String birthCountryCode;

    /**  *********************  银行账户的拒绝信息  *********************  */

    /**
     * 银行名称
     */
    private String bankName;

    /**
     * 是否为联名账户 0-否 1-是
     */
    private String jointAccount;

    /**
     * 银行账户
     */
    private String bankAcct;

    /**
     * 银行账户名称
     */
    private String bankAcctName;

    /**
     * SWIFT代码
     */
    private String swiftCode;
    /**
     * 代理银行SWIFT编码
     */
    private String correspondentSwiftCode;

    /**
     * 代理银行名称
     */
    private String correspondentBankName;

    /**
     * 代理银行账户
     */
    private String correspondentBankAcct;

    /**
     * 币种代码列表
     */
    private String currencyCodes;

    /**  *********************  声明信息的拒绝信息  *********************  */
    /**
     * 声明信息列表   key对应 DeclarationInfoDTO.declarationSort  value对应 拒绝信息
     */
    private Map<String, String> declarationInfo;

    /**  *********************  个人税收的拒绝信息  *********************  */

    /**
     * 就业状态 01-雇主 02-全职 03-兼职 04-主妇 05-学生 06-退休 07-非在职
     */
    private String emplStatus;

    /**
     * 就业公司名称
     */
    private String emplCompanyName;

    /**
     * 就业国家代码
     */
    private String emplCountryCode;

    /**
     * 就业业务性质
     */
    private String emplNatureOfBusiness;

    /**
     * 就业业务性质描述
     */
    private String emplNatureOfBusinessDesc;

    /**
     * 就业职位
     */
    private String emplDesignation;

    /**
     * 就业年收入
     * 01-≦HK$500,000
     * 02-HK$500,001 - HK$1,000,000
     * 03-HK$1,000,001 - HK$2,000,000
     * 04-HK$2,000,001 - HK$5,000,000
     * 05->HK$5,000,000
     */
    private String emplIncLevel;

    /**
     * 税务管辖权
     */
    private String taxJurisdiction;
    /**
     * 是否有税务编号
     */
    private String hasTin;
    /**
     * 未获取TIN的原因
     * 01-账户持有人的居留司法税务管辖区并没有向其居民发出税务编号。
     * 02-账户持有人不能取得税务编号。
     * 03-账户持有人无须提供税务编号。
     */
    private String noTinReason;

    /**
     * 未取得TIN的原因
     */
    private String noObtainTinReason;

    /**
     * TIN类型 01-身份证 02-其他
     */
    private String tinType;

    /**
     * TIN类型说明
     */
    private String tinTypeDesc;

    /**
     * TIN号码
     */
    private String tin;

    /**  *********************  投资经验的拒绝信息  *********************  */
    /**
     * 投资者类型 PRO-投资者资质专业 NORMAL-投资者资质普通
     */
    private String investorQualification;


    /**
     * 电子签名文件路径
     */
    private String esignPicturePaths;
    /**
     * 资产证明日期（审核人用）
     */
    private String assetCertDate;
}