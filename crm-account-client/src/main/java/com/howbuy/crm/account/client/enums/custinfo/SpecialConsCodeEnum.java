package com.howbuy.crm.account.client.enums.custinfo;

/**
 * @description:(特殊处理的投顾编码枚举类 ：
 * HWKH-WWH： HW开户-未维护，
 * ZSJJN_CSLS:掌上基金-未维护
 * gatkh: 关爱通客户
 * JGKH_ORG :机构客户
 * CXGN_CSLS : 储蓄罐-未维护
 * HZN_CSLS:合作-未维护
 * WZQTN_CSLS:网站及其他-未维护
 * )
 * @param
 * @return
 * @author: haoran.zhang
 * @date: 2023/12/8 17:46
 * @since JDK 1.8
 */
public enum SpecialConsCodeEnum {

   	/**
	 * HWKH-WWH： HW开户-未维护
	 */
	HWKH_WWH ("HWKH-WWH","HW开户-未维护"),
	/**
	 * ZSJJN_CSLS:掌上基金-未维护
	 */
	ZSJJN_CSLS("ZSJJN_CSLS","掌上基金-未维护"),

	/**
	 * gatkh: 关爱通客户
	 */
	GAT_KH("gatkh","关爱通客户"),
	/**
	 * JGKH_ORG :机构客户
	 */
	JG_KH_ORG("JGKH_ORG","机构客户"),
	/**
	 * CXGN_CSLS : 储蓄罐-未维护
	 */
	CXGN_CSLS("CXGN_CSLS","储蓄罐-未维护"),
	/**
	 * HZN_CSLS:合作-未维护
	 */
	HZN_CSLS("HZN_CSLS","合作-未维护"),

	/**
	 * WZQTN_CSLS:网站及其他-未维护
	 */
	WZQTN_CSLS("WZQTN_CSLS","网站及其他-未维护")


	//待补充 。。。。。。
	;

	/**
	 * 编码
	 */
	private String code;

	/**
	 * 描述
	 */
	private String description;

	SpecialConsCodeEnum(String code, String description) {
		this.code = code;
		this.description = description;
	}

	/**
	 * 通过code获得
	 *
	 * @param code 系统返回参数编码
	 * @return description 描述
	 */
	public static String getDescription(String code) {
		SpecialConsCodeEnum statusEnum = getEnum(code);
		return statusEnum == null ? null : statusEnum.getDescription();
	}

	/**
	 * 通过code直接返回 整个枚举类型
	 *
	 * @param code 系统返回参数编码
	 * @return BaseConstantEnum
	 */
	public static SpecialConsCodeEnum getEnum(String code) {
		for (SpecialConsCodeEnum statusEnum : SpecialConsCodeEnum.values()) {
			if (statusEnum.getCode().equals(code)) {
				return statusEnum;
			}
		}
		return null;
	}


	public String getCode() {
		return code;
	}


	public String getDescription() {
		return description;
	}

}
