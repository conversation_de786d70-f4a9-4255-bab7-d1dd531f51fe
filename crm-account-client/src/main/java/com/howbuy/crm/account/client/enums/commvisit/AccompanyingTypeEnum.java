package com.howbuy.crm.account.client.enums.commvisit;

/**
 * <AUTHOR>
 * @description: 陪访人类型枚举 1-项目经理 2-主管 3-总部业资 4-其他
 * @date 2025/4/30 13:34
 * @since JDK 1.8
 */
public enum AccompanyingTypeEnum {
    PROJECT_MANAGER("1", "项目经理"),
    MANAGER("2", "主管"),
    HEADQUARTER_BUSINESS_DEVELOPMENT("3", "总部业资"),
    OTHER("4", "其他");

    private String code;
    private String desc;

    AccompanyingTypeEnum(String code, String desc) {
        this.code = code;
        this.desc = desc;
    }

    /**
     * @description 查名称
     * @param code
     * @return java.lang.String
     * @author: jianjian.yang
     * @date: 2025/5/6 19:48
     * @since JDK 1.8
     */
    public static String getNameByCode(String code){
        for(AccompanyingTypeEnum item:values()){
            if(item.getCode().equals(code)){
                return item.getDesc();
            }
        }
        return null;
    }

    public String getCode() {
        return code;
    }

    public String getDesc() {
        return desc;
    }
}