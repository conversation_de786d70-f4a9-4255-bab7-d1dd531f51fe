/**
 * Copyright (c) 2024, <PERSON><PERSON><PERSON><PERSON> HOWBUY INVESTMENT MANAGEMENT Co., Ltd.
 * All right reserved.
 * <p>
 * THIS IS UNPUBLISHED PROPRIETARY SOURCE CODE OF HOWBUY INVESTMENT
 * MANAGEMENT CO., LTD.  THE CONTENTS OF THIS FILE MAY NOT BE DISCLOSED
 * TO THIRD PARTIES, COPIED OR DUPLICATED IN ANY FORM, IN WHOLE OR IN PART,
 * WITHOUT THE PRIOR WRITTEN PERMISSION OF HOWBUY INVESTMENT MANAGEMENT
 * CO., LTD.
 */
package com.howbuy.crm.account.client.facade.consultant;

import com.howbuy.crm.account.client.request.consultant.MergeConsPerformanceCoeffRequest;
import com.howbuy.crm.account.client.response.Response;

/**
 * @description: 写入人员绩效系数表接口（修改客户）
 * <AUTHOR>
 * @date 2025-06-27 17:05:00
 * @since JDK 1.8
 */
public interface UpdateConsPerformanceCoeffFacade {

    /**
     * @api {DUBBO} com.howbuy.crm.account.client.facade.consultant.UpdateConsPerformanceCoeffFacade.execute() execute()
     * @apiVersion 1.0.0
     * @apiGroup UpdateConsPerformanceCoeffFacade
     * @apiName execute()
     * @apiDescription 更新人员绩效系数表
     * @apiParam (请求体) {String} consCustNo 投顾客户号
     * @apiParam (请求体) {String} consCode 投顾code
     * @apiParam (请求体) {Date} assignTime 分配时间
     * @apiParam (请求体) {String} modifier 修改人
     * @apiParamExample 请求体示例
     * {"consCustNo":"123456","consCode":"CONS001","assignTime":"2024-12-19","modifier":"admin"}
     * @apiSuccess (响应结果) {String} code 状态码
     * @apiSuccess (响应结果) {String} description 描述信息
     * @apiSuccessExample 响应结果示例
     * {"code":"0000","description":"成功"}
     * @param request 请求参数
     * @return com.howbuy.crm.account.client.response.Response<String>
     * @description: 更新人员绩效系数表
     * <AUTHOR>
     * @date 2025-07-17 16:24:26
     * @since JDK 1.8
     */
    Response<String> execute(MergeConsPerformanceCoeffRequest request);
} 