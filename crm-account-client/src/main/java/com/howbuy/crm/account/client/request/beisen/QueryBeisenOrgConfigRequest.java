package com.howbuy.crm.account.client.request.beisen;

import lombok.Getter;
import lombok.Setter;
import lombok.ToString;

import java.io.Serializable;

/**
 * <AUTHOR>
 * @description: 查询列表请求
 * @date 2024/10/22 16:06
 * @since JDK 1.8
 */
@Getter
@Setter
@ToString
public class QueryBeisenOrgConfigRequest implements Serializable {

    private static final long serialVersionUID = 1L;
    /**
     * 架构名称（北森）
     */
    private String orgNameBeisen;

    /**
     * 部门编码
     */
    private String orgCode;

    /**
     * 起始日期
     */
    private String startDate;

    /**
     * 结束日期
     */
    private String endDate;
    /**
     * 页号
     */
    private Integer pageNo;
    /**
     * 分页大小
     */
    private Integer pageSize;
}