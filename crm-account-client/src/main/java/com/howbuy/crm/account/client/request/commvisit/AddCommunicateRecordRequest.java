package com.howbuy.crm.account.client.request.commvisit;

import com.howbuy.crm.account.client.request.Request;
import lombok.Getter;
import lombok.Setter;

import java.io.Serializable;
import java.util.List;

/**
 * @description: 新增客户沟通记录请求对象
 * <AUTHOR>
 * @date 2024-04-07 12:05:00
 * @since JDK 1.8
 */
@Setter
@Getter
public class AddCommunicateRecordRequest extends Request implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 沟通记录信息对象
     */
    private CommunicateReq communicateReq;

    /**
     * 预约信息对象
     */
    private BookingReq bookingReq;

    /**
     * 拜访纪要信息对象
     */
    private VisitMinutesReq visitMinutesReq;



    /**
     * 操作人ID
     */
    private String operator;

    /**
     * 沟通记录信息对象
     */
    @Setter
    @Getter
    public static class CommunicateReq implements Serializable {

        private static final long serialVersionUID = 1L;

        /**
         * 投顾客户号
         */
        private String consCustNo;

        /**
         * 拜访日期
         * 格式YYYYMMDD
         */
        private String visitDate;

        /**
         * 沟通方式
         * 见面、线上会议、电话等
         */
        private String visittype;

        /**
         * 沟通内容摘要
         */
        private String commcontent;

        /**
         * 沟通类型
         */
        private String consulttype;

        /**
         * 拜访分类 {0:正常客户,1:失联客户,2:黑名单,3:屏蔽客户}
         */
        private String visitclassify;
    }

    /**
     * 预约信息对象
     */
    @Setter
    @Getter
    public static class BookingReq implements Serializable {

        private static final long serialVersionUID = 1L;

        /**
         * 是否新增提醒
         */
        private Boolean hasRemind;

        /**
         * 预约方式
         */
        private String nextvisittype;

        /**
         * 预约日期
         * 格式YYYYMMDD
         */
        private String nextdt;

        /**
         * 预约开始时间
         * 格式HH:MM
         */
        private String nextstarttime;

        /**
         * 预约结束时间
         * 格式HH:MM
         */
        private String nextendtime;

        /**
         * 预约内容
         */
        private String nextvisitcontent;
    }

    /**
     * 拜访纪要信息对象
     */
    @Setter
    @Getter
    public static class VisitMinutesReq implements Serializable {

        private static final long serialVersionUID = 1L;


        /**
         * 客户存量
         */
        private String marketVal;

        /**
         * 客户目前综合健康度，1-5星
         */
        private String healthAvgStar;

        /**
         * 拜访目的
         * 多选，逗号分隔，如"1,2,3"
         */
        private String visitPurpose;

        /**
         * 拜访目的其他说明
         * 拜访目的包含"其他"时填写
         */
        private String visitPurposeOther;

        /**
         * IPS报告ID
         * IPS陪访时必填
         */
        private String assetReportId;

        /**
         * 提供资料
         */
        private String giveInformation;

        /**
         * 客户参与人员及角色
         */
        private String attendRole;

        /**
         * 对产品或服务的具体反馈
         * 创新陪访必填
         */
        private String productServiceFeedback;

        /**
         * 对于IPS报告反馈
         * IPS陪访必填
         */
        private String ipsFeedback;

        /**
         * 近期可用于加仓的金额(人民币)
         * IPS陪访必填之一
         */
        private String addAmountRmb;

        /**
         * 近期可用于加仓的金额(外币)
         * IPS陪访必填之一
         */
        private String addAmountForeign;

        /**
         * 近期关注的资产类别或具体产品
         * IPS陪访必填
         */
        private String focusAsset;

        /**
         * 评估客户需求
         * 创新业务等需求评估
         */
        private String estimateNeedBusiness;

        /**
         * 下一步工作计划
         */
        private String nextPlan;

        /**
         * 陪访人列表
         */
        private List<AccompanyingPerson> accompanyingList;

    }

    /**
     * 陪访人信息
     */
    @Setter
    @Getter
    public static class AccompanyingPerson implements Serializable {

        private static final long serialVersionUID = 1L;

        /**
         * 陪访人类型
         * 1-项目经理 2-主管 3-总部业资 4-其他
         */
        private String accompanyingType;

        /**
         * 陪访人用户ID
         */
        private String accompanyingUserId;
    }
} 