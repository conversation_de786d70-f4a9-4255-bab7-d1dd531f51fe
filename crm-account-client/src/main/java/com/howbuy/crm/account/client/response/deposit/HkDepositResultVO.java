/**
 * Copyright (c) 2024, <PERSON><PERSON><PERSON><PERSON> HOWBUY INVESTMENT MANAGEMENT Co., Ltd.
 * All right reserved.
 * <p>
 * THIS IS UNPUBLISHED PROPRIETARY SOURCE CODE OF HOWBUY INVESTMENT
 * MANAGEMENT CO., LTD.  THE CONTENTS OF THIS FILE MAY NOT BE DISCLOSED
 * TO THIRD PARTIES, COPIED OR DUPLICATED IN ANY FORM, IN WHOLE OR IN PART,
 * WITHOUT THE PRIOR WRITTEN PERMISSION OF HOWBUY INVESTMENT MANAGEMENT
 * CO., LTD.
 */
package com.howbuy.crm.account.client.response.deposit;

import lombok.Data;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.List;

/**
 * @description: 香港入金返回结果VO类
 * <AUTHOR>
 * @date 2024/1/15 9:56
 * @since JDK 1.8
 */

@Data
public class HkDepositResultVO implements Serializable {
    private static final long serialVersionUID = -4818850611554896935L;
    /**
     * 香港客户号
     */
    private String hkCustNo;
    /**
     * 香港资金账号
     */
    private String hkCpAcctNo;
    /**
     * 摘要-银行账号
     */
    private String bankAcctDigest;
    /**
     * 掩码-银行账号
     */
    private String bankAcctMask;
    /**
     * 金额
     */
    private BigDecimal amount;
    /**
     * 币种代码
     */
    private String curCode;
    /**
     * 备注
     */
    private String memo;
    /**
     * 打款凭证文件路径列表
     */
    private List<String> payProofFilePaths;
    /**
     * 入金拒绝信息
     */
    private HkDepositRejectVO depositRejectVO;
    /**
     * 审核状态 0-不需审核 1-等待审核 2-等待复核 3-审核通过 4-审核不通过/作废 5-驳回至初审 6-驳回至客户
     */
    private String txChkFlag;
    /**
     * swift编码
     */
    private String swiftCode;
    /**
     * 渠道
     */
    private String channel;
    /**
     * 实际到账金额
     */
    private BigDecimal actualAmount;
    /**
     * 实际到账币种
     */
    private String actualCurCode;
    /**
     * 入账流水号
     */
    private String depositSerialNo;
    /**
     * 参考编号
     */
    private String refNo;
    /**
     * 交易代码
     */
    private String txCode;

}