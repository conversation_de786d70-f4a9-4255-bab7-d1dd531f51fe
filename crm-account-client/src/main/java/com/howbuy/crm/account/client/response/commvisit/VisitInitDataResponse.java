/**
 * Copyright (c) 2024, <PERSON><PERSON><PERSON><PERSON> HOWBUY INVESTMENT MANAGEMENT Co., Ltd.
 * All right reserved.
 * <p>
 * THIS IS UNPUBLISHED PROPRIETARY SOURCE CODE OF HOWBUY INVESTMENT
 * MANAGEMENT CO., LTD.  THE CONTENTS OF THIS FILE MAY NOT BE DISCLOSED
 * TO THIRD PARTIES, COPIED OR DUPLICATED IN ANY FORM, IN WHOLE OR IN PART,
 * WITHOUT THE PRIOR WRITTEN PERMISSION OF HOWBUY INVESTMENT MANAGEMENT
 * CO., LTD.
 */
package com.howbuy.crm.account.client.response.commvisit;

import lombok.Getter;
import lombok.Setter;

import java.io.Serializable;
import java.util.List;

/**
 * @description: 客户沟通记录新增页初始化数据响应
 * <AUTHOR>
 * @date 2025-04-08 13:28:00
 * @since JDK 1.8
 */
@Getter
@Setter
public class VisitInitDataResponse implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 项目经理列表
     */
    private List<UserInfo> projectManagerUserList;

    /**
     * 主管列表
     */
    private List<UserInfo> manageUserList;
    
    /**
     * 总部业资列表
     */
    private List<UserInfo> supervisorUserList;
    
    /**
     * 其他列表
     */
    private List<UserInfo> otherUserList;
    
    /**
     * 用户信息内部类
     */
    @Getter
    @Setter
    public static class UserInfo implements Serializable {
        
        private static final long serialVersionUID = 1L;
        
        /**
         * 用户编码
         */
        private String code;
        
        /**
         * 用户名称
         */
        private String name;
    }
} 