package com.howbuy.crm.account.client.response.custinfo;

import lombok.Data;
import lombok.EqualsAndHashCode;

import java.io.Serializable;

/**
 * @description: (投顾客户信息 包含 密文信息 )
 * <AUTHOR>
 * @date 2023/12/8 20:57
 * @since JDK 1.8
 */
@Data
@EqualsAndHashCode(callSuper = true)
public class CmConsCustWithCipherVO extends CmConsCustVO  implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 证件号密文
     */
    private String idnoCipher;

    /**
     * 地址密文
     */
    private String addrCipher;

    /**
     * 手机号密文
     */
    private String mobileCipher;

    /**
     * 座机号密文
     */
    private String telnoCipher;

    /**
     * 邮箱密文
     */
    private String emailCipher;

    /**
     * 地址2密文
     */
    private String addr2Cipher;

    /**
     * 手机2密文
     */
    private String mobile2Cipher;

    /**
     * 邮箱2密文
     */
    private String email2Cipher;

    /**
     * 联系人姓名密文
     */
    private String linkmanCipher;

    /**
     * 联系人座机密文
     */
    private String linktelCipher;

    /**
     * 联系人手机号密文
     */
    private String linkmobileCipher;

    /**
     * 联系人邮箱密文
     */
    private String linkemailCipher;

    /**
     * 联系人地址密文
     */
    private String linkaddrCipher;
}