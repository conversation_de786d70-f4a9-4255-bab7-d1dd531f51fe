/**
 * Copyright (c) 2024, <PERSON><PERSON><PERSON><PERSON> HOWBUY INVESTMENT MANAGEMENT Co., Ltd.
 * All right reserved.
 * <p>
 * THIS IS UNPUBLISHED PROPRIETARY SOURCE CODE OF HOWBUY INVESTMENT
 * MANAGEMENT CO., LTD.  THE CONTENTS OF THIS FILE MAY NOT BE DISCLOSED
 * TO THIRD PARTIES, COPIED OR DUPLICATED IN ANY FORM, IN WHOLE OR IN PART,
 * WITHOUT THE PRIOR WRITTEN PERMISSION OF HOWBUY INVESTMENT MANAGEMENT
 * CO., LTD.
 */
package com.howbuy.crm.account.client.response.consultant;

import lombok.Getter;
import lombok.Setter;
import lombok.ToString;

import java.io.Serializable;

/**
 * @description: 写入人员绩效系数表接口出参
 * <AUTHOR>
 * @date 2025-06-27 17:05:00
 * @since JDK 1.8
 */
@Getter
@Setter
@ToString
public class MergeConsPerformanceCoeffResponse implements Serializable {
    
    private static final long serialVersionUID = 1L;
    
    /**
     * 操作结果标识
     */
    private String result;
    
    /**
     * 操作结果描述
     */
    private String message;
} 