package com.howbuy.crm.account.client.response.custinfo;


import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;

import java.io.Serializable;
import java.util.Date;

/**
 * 一账通账户中心 侧  [分销层] 相关接口中   分销客户归属的 客户详细 信息属性
 * 来源：DisCustInfoBean
 */
@Data
public class HboneDisMainCustInfoVO implements Serializable {

    private static final long serialVersionUID = -1589204783901728360L;


    /**
     * 证件地区码-身份证签发地区编码
     */
    private String idSignAreaCode;

    /**
     * 手机号码区号
     */
    private String mobileAreaCode;

    private String hboneNo;
    private String custNo;
    private String idNoDigest;
    private String idNoMask;
    private String idType;
    private String custName;
    private String idValidityStart;
    private String idValidityEnd;
    private String idAlwaysValidFlag;
    private String idVrfyStat;
    private String mobileDigest;
    private String mobileMask;
    private String mobileVerifyStatus;
    private String birthday;
    private String gender;
    private String regDt;
    private String canDt;
    private String regOutletCode;
    private String regTradeChan;
    private String regDisCode;
    private String custStat;
    private String corpIdNoDigest;
    private String corpIdNoMask;
    private String corpIdType;
    private String corporation;
    private String corpIdAlwaysValidFlag;
    private String corpIdValidityEnd;
    private String invstType;
    private String corpInvstType;
    /** @deprecated */
    @Deprecated
    private String idImageUploadStatus;
    /** @deprecated */
    @Deprecated
    private String idImageUploadDate;
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date hboneRegDate;
    private String hboneRegOutletCode;
    private String authOutletCode;
    private String dataAuthAgreementSign;
    private String dataAuthAgreementSignDate;
    private String vocation;
    private String nationality;
    private String eduLevel;
    private String incLevel;
    private String officeCityCode;
    private String assetManagerInvstType;
    private String organizationCode;
    private String regAddr;
    private String auxIdFileValidityEnd;
    private String custUuid;
    private String insType;
    private String prodDueDt;
    private String officeProvCode;
    private String instIndustry;
    private String settleCurrency;
    private String regCapital;
    private String auxIdFileNo;
    private String managerIdAlwaysValidFlag;
    private String managerIdValidityEnd;
    private String assetManagerIdType;
    private String assetManagerIdNo;
    private String corpIdNoCipher;
    private String regPostCode;
    private String regProvCode;
    private String regCountry;
    private String qualification;
    private String property;
    private String custType;
    private String custodianCode;
    private String custodianName;
    private String assetManagerName;
    private String fundShortName;
    private String invstProdId;
    private String regCountyCode;
    private String regCityCode;
    private String businessScope;
    private String insTypeQualifiedDate;
    private String officeCountyCode;
    private String auxIdFileAlwaysValidFlag;
    private String auxIdFileType;


}
