/**
 * Copyright (c) 2023, <PERSON><PERSON><PERSON><PERSON> HOWBUY INVESTMENT MANAGEMENT Co., Ltd.
 * All right reserved.
 * <p>
 * THIS IS UNPUBLISHED PROPRIETARY SOURCE CODE OF HOWBUY INVESTMENT
 * MANAGEMENT CO., LTD.  THE CONTENTS OF THIS FILE MAY NOT BE DISCLOSED
 * TO THIRD PARTIES, COPIED OR DUPLICATED IN ANY FORM, IN WHOLE OR IN PART,
 * WITHOUT THE PRIOR WRITTEN PERMISSION OF HOWBUY INVESTMENT MANAGEMENT
 * CO., LTD.
 */
package com.howbuy.crm.account.client.response.dictionary;

import lombok.Data;

import java.io.Serializable;

/**
 * <AUTHOR>
 * @description: (国家类型列表list)
 * @date 2023/11/29 13:06
 * @since JDK 1.8
 */
@Data
public class CountryInfoVO implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 中文名
     */
    private String chineseName;
    /**
     * 拼音首字母
     */
    private String firstPinyin;

    /**
     * iso31662
     */
    private String iso31662;

    /**
     * 代码简称
     */
    private String shortCode;

    /**
     * 英文名
     */
    private String englishName;

}