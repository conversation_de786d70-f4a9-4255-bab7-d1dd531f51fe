package com.howbuy.crm.account.client.enums.custinfo;

/**
 * @description:( * 个人客户   证件类型
 * 0-身份证
 * 1-护照
 * 2-军官证
 * 3-士兵证
 * 4-港澳居民来往内地通行证
 * 5-户口本
 * 6-外国护照
 * 7-其他
 * 8-文职证
 * 9-警官证
 * A-台胞证
 * B-外国人永久居留身份证
 * C-港澳台居民居住证
 * D-香港身份证
 * E-澳门身份证
 * F-台湾身份证
 * )
 * @param
 * @return
 * @author: haoran.zhang
 * @date: 2023/12/19 13:43
 * @since JDK 1.8
 */
public enum IdTypeEnum {

    /**
     * 0：身份证
     */
    ID("0", "身份证"),
    /**
     * 1：护照
     */
    PASSPORT("1", "中国护照"),

    /**
     * 2：军官证
     */
    OFFICERS_CERTIFICATE("2", "军官证"),

    /**
     * 3-士兵证
     */
    SOLDIER("3", "士兵证"),
    /**
     * 4：港澳居民来往内地通行证
     * 又名：回乡证
     */
    HOME_RETURN_PERMIT("4", "港澳居民来往内地通行证"),
    /**
     * 5：户口本
     */
    ACCOUNT_BOOK("5", "户口本"),
    /**
     * 6：外国护照
     */
    FOREIGN_PASSPORT("6", "外国护照"),
    /**
     * 7：其他
     */
    OTHER("7", "其他"),
    /**
     * 8：文职证
     */
    CIVIL_CERTIFICATE("8", "文职证"),
    /**
     * 9：警官证
     */
    POLICE_CARD("9", "警官证"),
    /**
     * A：台胞证
     */
    MTP("A", "台胞证"),

    /**
     * B：外国人永久居留身份证
     */
    FOREIGNERS_ID("B", "外国人永久居留身份证"),

    /**
     * C：港澳台居民居住证
     */
    HMT_ID("C", "港澳台居民居住证"),

    /**
     * D-香港身份证
     */
    HK_ID("D", "香港身份证"),

    /**
     * E-澳门身份证
     */
    MACAO_ID("E", "澳门身份证"),

    /**
     * F-台湾身份证
     */
    TAIWAN_ID("F", "台湾身份证")
    ;


    /**编码*/
    private String code;
    /**描述*/
    private String description;

    private IdTypeEnum(String code, String description){
        this.code=code;
        this.description=description;
    }

    /**
     * 通过code获得
     * @param code	系统返回参数编码
     * @return description 描述
     */
    public static String getDescription(String code){
        for(IdTypeEnum b: IdTypeEnum.values()){
            if(b.getCode().equals(code)){
                return b.description;
            }
        }
        return null;
    }

    /**
     * 通过code直接返回 整个枚举类型
     * @param code 系统返回参数编码
     * @return BaseConstantEnum
     */
    public static IdTypeEnum getEnum(String code){
        if(code != null && !"".equals(code)){
            for(IdTypeEnum b: IdTypeEnum.values()){
                if(b.getCode().equals(code)){
                    return b;
                }
            }
        }
        return null;
    }

    public String getCode() {
        return code;
    }


    public String getDescription() {
        return description;
    }
}
