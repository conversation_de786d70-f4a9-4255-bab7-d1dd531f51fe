/**
 * Copyright (c) 2024, <PERSON><PERSON><PERSON><PERSON> HOWBUY INVESTMENT MANAGEMENT Co., Ltd.
 * All right reserved.
 * <p>
 * THIS IS UNPUBLISHED PROPRIETARY SOURCE CODE OF HOWBUY INVESTMENT
 * MANAGEMENT CO., LTD.  THE CONTENTS OF THIS FILE MAY NOT BE DISCLOSED
 * TO THIRD PARTIES, COPIED OR DUPLICATED IN ANY FORM, IN WHOLE OR IN PART,
 * WITHOUT THE PRIOR WRITTEN PERMISSION OF HOWBUY INVESTMENT MANAGEMENT
 * CO., LTD.
 */
package com.howbuy.crm.account.client.response.custinfo;

import java.io.Serializable;

/**
 * @description: 查询 指定投顾客户 是否为[公司内部员工]
 * <AUTHOR>
 * @date 2024/11/1 18:15
 * @since JDK 1.8
 */
public class QueryCustIsInternalEmployeeVO implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 是否为 [公司内部员工]标识 1-是 0-否
     */
    private String internalEmployeeFlag;

    public String getInternalEmployeeFlag() {
        return internalEmployeeFlag;
    }

    public void setInternalEmployeeFlag(String internalEmployeeFlag) {
        this.internalEmployeeFlag = internalEmployeeFlag;
    }
}