package com.howbuy.crm.account.client.enums.orgnazion;

import java.util.Arrays;
import java.util.List;

/**
 * @description: (中心机构枚举 ORGCODE=0-HOWBUY下的直属二级orgCode枚举 -中心的概念)
 * @param
 * @return
 * @since JDK 1.8
 */
public enum CenterOrgEnum {
	/**
	 * 1-IC
	 */
	IC("1","财富管理中心"),
	/**
	 * 10-HBC
	 */
	OVERSEAS_BRANCH("10","高端业务中心"),
	/**
	 * **********-决策科学中心
	 */
	DECISION_SCIENCE ("**********","决策科学中心"),
	/**
	 * 11-客户服务部-高端
	 */
	CS_GD("11","客户服务部-高端"),
	/**
	 * 12-客户服务部-零售
	 */
	CS_LS("12","客户服务部-零售"),
	/**
	 * 13-零售服务部
	 */
	RS_LS("13","零售服务部"),
	/**
	 * 14-其他分类
	 */
	OTHER("14","其他分类"),
	/**
	 * 15-机构
	 */
	OGRDEP("15","机构"),
	/**
	 * 2-电商中心
	 */
	EC("2","电商中心"),
	/**
	 * *********-OT
	 */
	EC_OT("*********","OT"),
	/**
	 * 3-产品中心
	 */
	PC("3","产品中心"),
	/**
	 * 4-魔数工厂
	 */
	IT("4","魔数工厂"),
	/**
	 * 5-后台中心
	 */
	BC("5","后台中心"),
	/**
	 * 6-AON团队
	 */
	AON("6","AON团队"),
	/**
	 * 7-自管团队
	 */
	OM("7","自管团队"),
	/**
	 * 8-机构业务部
	 */
	BUSDEP("8","机构业务部"),
	/**
	 * 9-企业合作部
	 */
	PCD("9","企业合作部"),

	/**
	 * 1000005627-HKPWM 香港
	 */
	HKPWM("1000005627", "HKPWM"),
	;

	/**
	 * 编码
	 * */
	private String code;
	/**
	 * 描述
	 * */
	private String description;

	CenterOrgEnum(String code, String description){
		this.code=code;
		this.description=description;
	}
	
	/**
	 * 通过code获得
	 * @param code	系统返回参数编码
	 * @return description 描述
	 */
	public static String getDescription(String code){
		for(CenterOrgEnum b: CenterOrgEnum.values()){
			if(b.getCode().equals(code)){
				return b.description;
			}
		}
		return null;
	}
	
	/**
	 * 通过code直接返回 整个枚举类型
	 * @param code 系统返回参数编码
	 * @return BaseConstantEnum
	 */
	public static CenterOrgEnum getEnum(String code){
		if(code != null && !"".equals(code)){
			for(CenterOrgEnum b: CenterOrgEnum.values()){
				if(b.getCode().equals(code)){
					return b;
				}
			}
		}
		return null;
	}

	public String getCode() {
		return code;
	}


	public String getDescription() {
		return description;
	}


	/**
	 * IC/HBC 的orgCode列表
	 * @return
	 */
	public static List<String> getIcHbcList(){
		return Arrays.asList(CenterOrgEnum.IC.getCode(),CenterOrgEnum.OVERSEAS_BRANCH.getCode());
	}

}
