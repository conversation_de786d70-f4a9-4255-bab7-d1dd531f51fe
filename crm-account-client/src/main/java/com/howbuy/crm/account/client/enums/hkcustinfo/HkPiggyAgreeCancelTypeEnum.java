package com.howbuy.crm.account.client.enums.hkcustinfo;

/**
 * @description:(海外储蓄罐协议终止方式 1-线下自主申请关闭 2-线上自主申请关闭 3-底层基金更换未同意 4-底层基金更换不同意)
 * @return
 * @author: haoran.zhang
 * @date: 2024/7/24 14:44
 * @since JDK 1.8
 */
public enum HkPiggyAgreeCancelTypeEnum {

	/**
	 * 线下自主申请关闭
	 */
	OFFLINE_AUTO_CANCEL("1", "线下自主申请关闭"),

	/**
	 * 线上自主申请关闭
	 */
    ONLINE_MANNUAL_CANCEL("2", "线上自主申请关闭"),

	/**
	 * 底层基金更换未同意
	 */
    FUND_CHANGE_NO("3", "底层基金更换未同意"),
	/**
	 * 底层基金更换不同意
	 */
    FUND_CHANGE_NO_AGREE("4", "底层基金更换不同意");


	/**
	 * 编码
	 */
	private String code;

	/**
	 * 描述
	 */
	private String description;

	 HkPiggyAgreeCancelTypeEnum(String code, String description) {
		this.code = code;
		this.description = description;
	}


	/**
	 * 通过code获得
	 * 
	 * @param code
	 *            系统返回参数编码
	 * @return description 描述
	 */
	public static String getDescription(String code) {
		HkPiggyAgreeCancelTypeEnum statusEnum=getEnum(code);
		return statusEnum==null?null :statusEnum.getDescription();
	}

	/**
	 * 通过code直接返回 整个枚举类型
	 * 
	 * @param code
	 *            系统返回参数编码
	 * @return PreOccupyTypeEnum
	 */
	public static HkPiggyAgreeCancelTypeEnum getEnum(String code) {
		for(HkPiggyAgreeCancelTypeEnum statusEnum : HkPiggyAgreeCancelTypeEnum.values()){
			if(statusEnum.getCode().equals(code)){
				return statusEnum;
			}
		}
		return null;
	}




	public String getCode() {
		return code;
	}


	public String getDescription() {
		return description;
	}


}
