package com.howbuy.crm.account.client.response.custinfo;
import com.howbuy.crm.account.client.enums.custinfo.HkAcctCustStatusEnum;
import lombok.Data;

import java.io.Serializable;

/**
 * 香港客户信息
 * <AUTHOR>
 */
@Data
public class HkAcctCustInfoVO implements Serializable {

    private static final long serialVersionUID = 360919284646542496L;

    /**
     * CRM 客户号
     */
    private String custNo;

    /**
     * 香港交易账号
     */
    private String hkTxAcctNo;

    /**
     * ebrokerId
     */
    private String ebrokerId;

    /**
     * 香港客户中文名
     */
    private String custChineseName;

    /**
     * 香港客户英文名
     */
    private String custEnName;

    //账户中心属性


    /**
     * {@link com.howbuy.crm.account.client.enums.custinfo.HkAcctCustStatusEnum}
     * 香港客户状态-   客户状态 0-正常 1-注销 2-休眠 3-注册4-开户申请成功
     */
    private String hkCustStatus;


    /**
     * 出生日期
     */
    private String birthDt;
    /**
     *证件号码
     */
    private String idNoDigest;
    /**
     *证件号码
     */
    private String idNoMask;

    /**
     * 证件地区码
     */
    private String idSignAreaCode;

    /**
     * 证件类型
     */
    private String idType;
    /**
     * 投资者类型 0-机构,1-个人,2-产品户
     */
    private String invstType;
    /**
     * 手机号码区号
     */
    private String mobileAreaCode;
    /**
     * 手机号码
     */
    private String mobileDigest;
    /**
     * 手机号码
     */
    private String mobileMask;
    /**
     * 手机号码 验证状态
     * 0:未验证 1:已验证
     */
    private String mobileVerifyStatus;
    /**
     * 邮件
     */
    private String emailDigest;
    /**
     * 邮件
     */
    private String emailMask;
    /**
     * 邮件 验证状态
     * 0:未验证 1:已验证
     */
    private String emailVerifyStatus;

    /**
     * 关联的一账通号
     */
    private String hboneNo;

    /**
     * 开户方式 0-线下 1-线上
     */
    private String openType;

    /**
     * 开户日期
     */
    private String openDate;



    /**
     * 客户 是否开香港户: 香港客户号不为空 且 (客户状态为“正常” 或 客户状态为“休眠”)
     * @return
     */
    public  boolean isOpHkAcct(){
        return  !"".equals(hkTxAcctNo) && (HkAcctCustStatusEnum.NORMAL.getCode().equals(hkCustStatus) || HkAcctCustStatusEnum.DORMANT.getCode().equals(hkCustStatus));
    }

}
