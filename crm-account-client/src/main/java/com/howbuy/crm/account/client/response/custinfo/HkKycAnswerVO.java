/**
 * Copyright (c) 2023, <PERSON><PERSON><PERSON><PERSON> HOWBUY INVESTMENT MANAGEMENT Co., Ltd.
 * All right reserved.
 * <p>
 * THIS IS UNPUBLISHED PROPRIETARY SOURCE CODE OF HOWBUY INVESTMENT
 * MANAGEMENT CO., LTD.  THE CONTENTS OF THIS FILE MAY NOT BE DISCLOSED
 * TO THIRD PARTIES, COPIED OR DUPLICATED IN ANY FORM, IN WHOLE OR IN PART,
 * WITHOUT THE PRIOR WRITTEN PERMISSION OF HOWBUY INVESTMENT MANAGEMENT
 * CO., LTD.
 */
package com.howbuy.crm.account.client.response.custinfo;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.google.common.collect.Lists;
import lombok.Data;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;
import java.util.List;

/**
 * @description: 香港客户风险测评问卷VO
 * <AUTHOR>
 * @date 2023/12/29 13:18
 * @since JDK 1.8
 */
@Data
public class HkKycAnswerVO implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 风险等级 1-低风险等级 2-中低风险等级 3-中风险等级 4-中高风险等级 5-高风险等级
     */
    private String levelValue;

    /**
     * 计算得到的分数
     */
    private BigDecimal score;

    /**
     * 衍生金融工具知识 0-无 1-有
     */
    private String derivativeKnowledge;

    /**
     * 风险测评日期 YYYYMMDD
     */
    private String riskToleranceDate;

    /**
     * 风险评测过期日期 YYYYMMDD
     */
    private String riskToleranceTerm;

    /**
     * 创建时间戳
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date stimestamp;

    /**
     * 来源编码
     */
    private String outletCode;

    /**
     * 来源名称
     */
    private String outletCodeName;


    /**
     * 不匹配的基金列表
     * 超出风险评测结果的基金列表信息
     */
    private List<HkMismatchFundVO> mismatchFundList= Lists.newArrayList();

    @Data
    public static class HkMismatchFundVO implements Serializable {
        private static final long serialVersionUID = 1L;
        private String fundCode;
        private String fundName;
        private String fundRiskLevel;

    }

}