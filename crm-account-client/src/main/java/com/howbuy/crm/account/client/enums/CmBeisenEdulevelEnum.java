/**
 * Copyright (c) 2024, <PERSON><PERSON><PERSON><PERSON> HOWBUY INVESTMENT MANAGEMENT Co., Ltd.
 * All right reserved.
 * <p>
 * THIS IS UNPUBLISHED PROPRIETARY SOURCE CODE OF HOWBUY INVESTMENT
 * MANAGEMENT CO., LTD.  THE CONTENTS OF THIS FILE MAY NOT BE DISCLOSED
 * TO THIRD PARTIES, COPIED OR DUPLICATED IN ANY FORM, IN WHOLE OR IN PART,
 * WITHOUT THE PRIOR WRITTEN PERMISSION OF HOWBUY INVESTMENT MANAGEMENT
 * CO., LTD.
 */
package com.howbuy.crm.account.client.enums;

/**
 * @description: (北森对应crm学历枚举)
 * <AUTHOR>
 * @date 2024/10/30 18:20
 * @since JDK 1.8
 */
public enum CmBeisenEdulevelEnum {
    EDU_LEVEL_1("高中及以下","1"),
    EDU_LEVEL_2("高中","1"),
    EDU_LEVEL_3("大专","2"),
    EDU_LEVEL_4("本科","3"),
    EDU_LEVEL_5("硕士研究生","4"),
    EDU_LEVEL_6("博士研究生","6");
    /**
     * 北森对应学历
     */
    private String name;
    /**
     * crm对应code
     */
    private String code;

    CmBeisenEdulevelEnum(String name, String code) {
        this.name = name;
        this.code = code;
    }

    public static String getByName(String name) {
        for (CmBeisenEdulevelEnum cmBeisenEdulevelEnum : CmBeisenEdulevelEnum.values()) {
            if (cmBeisenEdulevelEnum.getName().equals(name)) {
                return cmBeisenEdulevelEnum.getCode();
            }
        }
        return null;
    }

    public String getName() {
        return name;
    }

    public String getCode() {
        return code;
    }

}