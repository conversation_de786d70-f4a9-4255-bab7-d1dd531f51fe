package com.howbuy.crm.account.client.response.consultant;

import lombok.Getter;
import lombok.Setter;
import lombok.ToString;

import java.io.Serializable;

/**
 * @description: 投顾简单信息查询响应结果
 * <AUTHOR>
 * @date 2025/3/10
 * @since JDK 1.8
 */
@Getter
@Setter
@ToString
public class ConsultantSimpleResponse implements Serializable {
    
    private static final long serialVersionUID = 1L;
    
    /**
     * 理财顾问代码
     */
    private String consCode;
    
    /**
     * 理财顾问名称
     */
    private String consName;
    
    /**
     * 理财顾问级别
     */
    private String consLevel;
    
    /**
     * 理财顾问状态1有效，0无效
     */
    private String consStatus;
    
    /**
     * 是否虚拟投顾（1 是/0 否）
     */
    private String isVirtual;
    
    /**
     * 所属理财中心
     */
    private String outletCode;
    
    /**
     * 所属小组
     */
    private String teamCode;
    
    /**
     * 手机
     */
    private String mobile;
    
    /**
     * 投顾照片链接地址
     */
    private String picAddr;
    
    /**
     * 企业微信二维码地址
     */
    private String codePicAddr;
    
    /**
     * 理财顾问职位
     */
    private String position;
    
    /**
     * 电子邮件
     */
    private String email;
    
    /**
     * 所属中心
     */
    private String centerOrgCode;
} 