package com.howbuy.crm.account.client.request.consultant;

import com.howbuy.commons.validator.MyValidation;
import com.howbuy.commons.validator.util.ValidatorTypeEnum;
import lombok.Getter;
import lombok.Setter;
import lombok.ToString;

import java.io.Serializable;

/**
 * @description: 投顾简单信息查询请求参数
 * <AUTHOR>
 * @date 2025/3/10
 * @since JDK 1.8
 */
@Getter
@Setter
@ToString
public class ConsultantSimpleRequest implements Serializable {
    
    private static final long serialVersionUID = 1L;
    
    /**
     * 投顾编号
     */
    @MyValidation(validatorType = ValidatorTypeEnum.String, fieldName = "投顾编号", isRequired = true)
    private String consCode;
} 