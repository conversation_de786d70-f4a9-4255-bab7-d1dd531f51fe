package com.howbuy.crm.account.client.enums.custinfo;

/**
 * @description:(性别枚举： 0-女 1-男 2-非自然人)
 * @param
 * @return
 * @author: haoran.zhang
 * @date: 2023/12/19 13:43
 * @since JDK 1.8
 */
public enum GenderEnum {

    /**
     *女
     */
    FEMALE("0","女"),
    /**
     *男
     */
    MALE("1","男"),

    /**
     * 非自然人
     */
    NONNATURAL("2","非自然人")
    ;


    /**编码*/
    private String code;
    /**描述*/
    private String description;

    private GenderEnum(String code, String description){
        this.code=code;
        this.description=description;
    }

    /**
     * 通过code获得
     * @param code	系统返回参数编码
     * @return description 描述
     */
    public static String getDescription(String code){
        for(GenderEnum b: GenderEnum.values()){
            if(b.getCode().equals(code)){
                return b.description;
            }
        }
        return null;
    }

    /**
     * 通过code直接返回 整个枚举类型
     * @param code 系统返回参数编码
     * @return BaseConstantEnum
     */
    public static GenderEnum getEnum(String code){
        if(code != null && !"".equals(code)){
            for(GenderEnum b: GenderEnum.values()){
                if(b.getCode().equals(code)){
                    return b;
                }
            }
        }
        return null;
    }

    public String getCode() {
        return code;
    }


    public String getDescription() {
        return description;
    }
}
