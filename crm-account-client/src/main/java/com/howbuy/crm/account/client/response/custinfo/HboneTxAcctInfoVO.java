package com.howbuy.crm.account.client.response.custinfo;


import lombok.Data;

import java.io.Serializable;

/**
 * 账户中心 侧  交易账号
 * 来源：com.howbuy.acccenter.facade.query.querytxacctbyhbonefacade.QueryTxAcctByHboneResponse
 */
@Data
public class HboneTxAcctInfoVO implements Serializable {

    private static final long serialVersionUID = 1L;


    /**
     *交易账号
     */
    private String txAcctNo;

    /**
     *定义待补充
     */
    private String custNo;

    /**
     *客户状态 0-正常；1-销户
     */
    private String stat;


}
