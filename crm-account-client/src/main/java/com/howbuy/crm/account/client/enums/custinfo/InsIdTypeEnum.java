package com.howbuy.crm.account.client.enums.custinfo;

/**
 * @description:(机构客户   证件类型
 * 0-组织机构代码
 * 1-营业执照
 * 2-行政机关
 * 3-社会团体
 * 4-军队
 * 5-武警
 * 6-下属机构
 * 7-基金会
 * 8-其他
 * 9-登记证书
 * A-批文
 * )
 * @param
 * @return
 * @author: haoran.zhang
 * @date: 2023/12/19 13:45
 * @since JDK 1.8
 */
public enum InsIdTypeEnum {

    /**
     * 0：组织机构代码
     */
    GROUP_INV_CODE("0", "组织机构代码"),
    /**
     * 1：营业执照
     */
    BUSINESS_LICENCE("1", "营业执照"),
    /**
     * 2：行政机关
     */
    ADMINISTRATIVE_ORGANIZATION("2", "行政机关"),
    /**
     * 3：社会团体
     */
    SOCIAL_GROUP("3", "社会团体"),
    /**
     * 4：军队
     */
    ARMY("4", "军队"),
    /**
     * 5：武警
     */
    ARMED_POLICE("5", "武警"),
    /**
     * 6：下属机构
     */
    SUB_BODY("6", "下属机构"),
    /**
     * 7：基金会
     */
    FOUNDATION("7", "基金会"),
    /**
     * 8：其他机构
     */
    OTHER_INV("8", "其他"),
    /**
     * 9：登记证书
     */
    REGISTER_CERT("9", "登记证书"),

    /**
     * A：批文
     */
    AUTHORIZE_FILE("A", "批文");


    /**
     * 编码
     */
    private String code;
    /**
    描述
     */
    private String description;

    private InsIdTypeEnum(String code, String description){
        this.code=code;
        this.description=description;
    }

    /**
     * 通过code获得
     * @param code	系统返回参数编码
     * @return description 描述
     */
    public static String getDescription(String code){
        for(InsIdTypeEnum b: InsIdTypeEnum.values()){
            if(b.getCode().equals(code)){
                return b.description;
            }
        }
        return null;
    }

    /**
     * 通过code直接返回 整个枚举类型
     * @param code 系统返回参数编码
     * @return BaseConstantEnum
     */
    public static InsIdTypeEnum getEnum(String code){
        if(code != null && !"".equals(code)){
            for(InsIdTypeEnum b: InsIdTypeEnum.values()){
                if(b.getCode().equals(code)){
                    return b;
                }
            }
        }
        return null;
    }

    public String getCode() {
        return code;
    }


    public String getDescription() {
        return description;
    }
}
