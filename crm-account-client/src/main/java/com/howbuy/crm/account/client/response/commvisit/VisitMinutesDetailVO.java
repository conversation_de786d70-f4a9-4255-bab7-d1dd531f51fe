package com.howbuy.crm.account.client.response.commvisit;

import lombok.Getter;
import lombok.Setter;

import java.io.Serializable;
import java.util.List;

/**
 * @description: 拜访纪要详情响应
 * <AUTHOR>
 * @date 2025-04-08 10:26:00
 * @since JDK 1.8
 */
@Getter
@Setter
public class VisitMinutesDetailVO implements Serializable {

    private static final long serialVersionUID = 1L;
    /**
     * 客户姓名
     */
    private String custName;
    
    /**
     * 投顾客户号
     */
    private String consCustNo;
    
    /**
     * 拜访日期,格式YYYYMMDD
     */
    private String visitDt;
    
    /**
     * 沟通方式
     */
    private String visitType;
    
    /**
     * 客户存量
     */
    private String marketVal;
    
    /**
     * 客户综合健康度
     */
    private String healthAvgStar;
    
    /**
     * 拜访目的列表
     */
    private List<String> visitPurposeList;
    
    /**
     * 拜访目的其他说明
     */
    private String visitPurposeOther;
    
    /**
     * 报告ID
     */
    private String reportId;
    
    /**
     * 报告标题
     */
    private String reportTitle;
    
    /**
     * 提供资料
     */
    private String giveInformation;
    
    /**
     * 陪访人姓名，多个逗号分隔
     */
    private String accompanyingUser;
    
    /**
     * 客户参与人员及角色
     */
    private String attendRole;
    
    /**
     * 对产品或服务的具体反馈
     */
    private String productServiceFeedback;
    
    /**
     * 对于IPS报告反馈
     */
    private String ipsFeedback;
    
    /**
     * 人民币金额
     */
    private String addAmountRmb;
    
    /**
     * 外币金额
     */
    private String addAmountForeign;
    
    /**
     * 近期关注的资产类别或具体产品
     */
    private String focusAsset;
    
    /**
     * 评估客户需求
     */
    private String estimateNeedBusiness;
    
    /**
     * 下一步工作计划
     */
    private String nextPlan;
    
    /**
     * 陪访人列表
     */
    private List<AccompanyingVO> accompanyingList;
    
    /**
     * 主管姓名
     */
    private String managerName;
    
    /**
     * 数据是否可编辑
     */
    private Boolean canEditData;
} 