package com.howbuy.crm.account.client.response.consultant;

import lombok.Getter;
import lombok.Setter;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;

/**
 * @description: 查询人员绩效系数列表单条数据VO
 * <AUTHOR>
 * @date 2025-07-11 16:00:00
 * @since JDK 1.8
 */
@Getter
@Setter
public class QueryConsPerformanceCoeffListVO implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 分配时间，格式：YYYYMMDD HH:MM:SS
     */
    private String assignTime;

    /**
     * 投顾客户号
     */
    private String consCustNo;

    /**
     * 客户姓名
     */
    private String custName;

    /**
     * 投顾code
     */
    private String consCode;

    /**
     * 分配投顾（投顾姓名）
     */
    private String consName;

    /**
     * 中心-当前
     */
    private String centerOrgCurrent;

    /**
     * 区域-当前
     */
    private String regionCurrent;

    /**
     * 区副-当前
     */
    private String regionViceCurrent;

    /**
     * 分公司-当前
     */
    private String partOrgCurrent;

    /**
     * 来源类型
     */
    private String sourceType;

    /**
     * 佣金系数起点
     */
    private String commissionCoeffStart;

    /**
     * 客户折算系数
     */
    private BigDecimal custConversionCoeff;

    /**
     * 管理系数-分总
     */
    private BigDecimal manageCoeffSubtotal;

    /**
     * 管理系数-区副
     */
    private BigDecimal manageCoeffRegionalsubtotal;

    /**
     * 管理系数-区总
     */
    private BigDecimal manageCoeffRegionaltotal;

    /**
     * 存续B
     */
    private String cxb;

    /**
     * 是否计提公募（是/否）
     */
    private String isBigV;

    /**
     * 中心-分配时
     */
    private String centerOrgAssignTime;

    /**
     * 区域-分配时
     */
    private String regionAssignTime;

    /**
     * 区副-分配时
     */
    private String regionViceAssignTime;

    /**
     * 分公司-分配时
     */
    private String partOrgAssignTime;

    /**
     * 修改人（用户编码）
     */
    private String updateUser;
}