/**
 * Copyright (c) 2024, <PERSON><PERSON><PERSON><PERSON> HOWBUY INVESTMENT MANAGEMENT Co., Ltd.
 * All right reserved.
 * <p>
 * THIS IS UNPUBLISHED PROPRIETARY SOURCE CODE OF HOWBUY INVESTMENT
 * MANAGEMENT CO., LTD.  THE CONTENTS OF THIS FILE MAY NOT BE DISCLOSED
 * TO THIRD PARTIES, COPIED OR DUPLICATED IN ANY FORM, IN WHOLE OR IN PART,
 * WITHOUT THE PRIOR WRITTEN PERMISSION OF HOWBUY INVESTMENT MANAGEMENT
 * CO., LTD.
 */
package com.howbuy.crm.account.client.request.custinfo;

import com.howbuy.crm.account.client.request.PageRequest;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.io.Serializable;
import java.util.List;

/**
 * @description:(批量查询客户信息 request)
 * @return
 * @author: haoran.zhang
 * @date: 2025/3/5 13:18
 * @since JDK 1.8
 */
@EqualsAndHashCode(callSuper = true)
@Data
public class BatchQueryCustSimpleRequest extends PageRequest implements Serializable {
    private static final long serialVersionUID= 1L;

    /**
     * 投顾编号  列表
     */
    private List<String> consCodeList;

    /**
     * 一账通号 列表
     */
    private List<String> hboneNoList;


    /**
     * 香港交易账号 列表
     */
    private List<String> hkTxAcctNoList;

    /**
     * 手机号摘要
     */
    private String mobileDigest;
}
