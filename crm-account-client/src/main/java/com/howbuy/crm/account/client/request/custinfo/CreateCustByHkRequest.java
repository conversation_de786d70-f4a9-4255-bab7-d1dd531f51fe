/**
 * Copyright (c) 2023, <PERSON><PERSON><PERSON><PERSON> HOWBUY INVESTMENT MANAGEMENT Co., Ltd.
 * All right reserved.
 * <p>
 * THIS IS UNPUBLISHED PROPRIETARY SOURCE CODE OF HOWBUY INVESTMENT
 * MANAGEMENT CO., LTD.  THE CONTENTS OF THIS FILE MAY NOT BE DISCLOSED
 * TO THIRD PARTIES, COPIED OR DUPLICATED IN ANY FORM, IN WHOLE OR IN PART,
 * WITHOUT THE PRIOR WRITTEN PERMISSION OF HOWBUY INVESTMENT MANAGEMENT
 * CO., LTD.
 */
package com.howbuy.crm.account.client.request.custinfo;

import com.howbuy.commons.validator.MyValidation;
import com.howbuy.commons.validator.util.ValidatorTypeEnum;
import lombok.Data;

import java.io.Serializable;

/**
 * <AUTHOR>
 * @description: (根据香港账号，创建crm客户信息 request 参数对象)
 * @date 2025年5月19日
 * @since JDK 1.8
 */
@Data
public class CreateCustByHkRequest implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 香港客户号
     */
    @MyValidation(validatorType = ValidatorTypeEnum.String, fieldName = "香港客户号", isRequired = true)
    private String hkTxAcctNo;

    /**
     * 操作来源 [1-MQ]时，为[异常来源]，参考枚举类[{@link com.howbuy.crm.account.client.enums.custinfo.FullCustSourceEnum}]
     * [2-菜单页面]时，为菜单名称
     */
    @MyValidation(validatorType = ValidatorTypeEnum.String, fieldName = "操作来源", isRequired = true)
    private String operateSource;

    /**
     * operateChannel 操作通道 1-MQ  2-菜单页面
     */
    @MyValidation(validatorType = ValidatorTypeEnum.String, fieldName = "操作通道", isRequired = true)
    private String operateChannel;

    /**
     * 客户来源-来源编号
     * {@link com.howbuy.crm.account.client.enums.custinfo.CustSourceCodeEnum}
     */
    @MyValidation(validatorType = ValidatorTypeEnum.String, fieldName = "客户来源", isRequired = true)
    private String custSource;

}