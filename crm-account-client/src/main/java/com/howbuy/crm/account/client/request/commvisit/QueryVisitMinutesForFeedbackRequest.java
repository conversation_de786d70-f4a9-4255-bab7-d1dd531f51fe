package com.howbuy.crm.account.client.request.commvisit;

import lombok.Getter;
import lombok.Setter;

import java.io.Serializable;

/**
 * @description: 查询拜访纪要反馈明细请求
 * <AUTHOR>
 * @date 2025-04-08 10:26:00
 * @since JDK 1.8
 */
@Getter
@Setter
public class QueryVisitMinutesForFeedbackRequest implements Serializable {

    private static final long serialVersionUID = 1L;
    /**
     * 拜访纪要ID
     */
    private String visitMinutesId;
    
    /**
     * 反馈类型 1-陪访人 2-主管
     */
    private String feedbackType;
    
    /**
     * 当前用户ID
     */
    private String currentUserId;
} 