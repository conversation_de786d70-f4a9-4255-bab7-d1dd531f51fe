package com.howbuy.crm.account.client.enums.custinfo;

/**
 * @description:(教育水平 01-小学或以下 02-中学 03-大专或预科 04-大学或本科 05-硕士或以上)
 * @param
 * @return
 * @author: haoran.zhang
 * @date: 2023/12/19 13:43
 * @since JDK 1.8
 */
public enum EduLevelEnum {
    /**
     * 小学或以下
     * */
    PRIMARY_SCHOOL("01","小学或以下"),
    /**
     * 中学
     * */
    MIDDLE_SCHOOL("02","中学"),
    /**
     * 大专或预科
     * */
    COLLEGE("03","大专或预科"),
    /**
     * 大学或本科
     * */
    UNIVERSITY("04","大学或本科"),
    /**
     * 硕士或以上
     */
    MASTER_OR_ABOVE("05","硕士或以上")
    ;


    /**编码*/
    private String code;
    /**描述*/
    private String description;

    private EduLevelEnum(String code, String description){
        this.code=code;
        this.description=description;
    }

    /**
     * 通过code获得
     * @param code	系统返回参数编码
     * @return description 描述
     */
    public static String getDescription(String code){
        for(EduLevelEnum b: EduLevelEnum.values()){
            if(b.getCode().equals(code)){
                return b.description;
            }
        }
        return null;
    }

    /**
     * 通过code直接返回 整个枚举类型
     * @param code 系统返回参数编码
     * @return BaseConstantEnum
     */
    public static EduLevelEnum getEnum(String code){
        if(code != null && !"".equals(code)){
            for(EduLevelEnum b: EduLevelEnum.values()){
                if(b.getCode().equals(code)){
                    return b;
                }
            }
        }
        return null;
    }

    public String getCode() {
        return code;
    }


    public String getDescription() {
        return description;
    }
}
