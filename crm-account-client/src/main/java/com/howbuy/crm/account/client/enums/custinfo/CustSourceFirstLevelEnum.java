package com.howbuy.crm.account.client.enums.custinfo;

/**
 * @description:(客户来源  一级来源 ：
 * M:一致行动人
 * A:howbuy400
 * B:MGM
 * C:储蓄罐app
 * D:好买网站
 * E:内部活动
 * F:其他合作
 * G:其他来源
 * H:腾讯
 * I:投顾开发
 * J:好买微信
 * K:掌上基金app
 * L:百度
 * )
 * @param
 * @return
 * @author: haoran.zhang
 * @date: 2023/12/8 17:46
 * @since JDK 1.8
 */
public enum CustSourceFirstLevelEnum {

//	select * from cm_sourceinfo_dict m where DICT_LEVEL=1
	/**
	 * 一致行动人
	 */
	M("M", "一致行动人"),
	/**
	 * howbuy400
	 */
	A("A", "howbuy400"),
	/**
	 * MGM
	 */
	B("B", "MGM"),
	/**
	 * 储蓄罐app
	 */
	C("C", "储蓄罐app"),
	/**
	 * 好买网站
	 */
	D("D", "好买网站"),
	/**
	 * 内部活动
	 */
	E("E", "内部活动"),
	/**
	 * 其他合作
	 */
	F("F", "其他合作"),
	/**
	 * 其他来源
	 */
	G("G", "其他来源"),
	/**
	 * 腾讯
	 */
	H("H", "腾讯"),
	/**
	 * 投顾开发
	 */
	I("I", "投顾开发"),
	/**
	 * 好买微信
	 */
	J("J", "好买微信"),
	/**
	 * 掌上基金app
	 */
	K("K", "掌上基金app"),
	/**
	 * 百度
	 */
	L("L", "百度")
	;

	/**
	 * 编码
	 */
	private String code;

	/**
	 * 描述
	 */
	private String description;

	CustSourceFirstLevelEnum(String code, String description) {
		this.code = code;
		this.description = description;
	}

	/**
	 * 通过code获得
	 *
	 * @param code 系统返回参数编码
	 * @return description 描述
	 */
	public static String getDescription(String code) {
		CustSourceFirstLevelEnum statusEnum = getEnum(code);
		return statusEnum == null ? null : statusEnum.getDescription();
	}

	/**
	 * 通过code直接返回 整个枚举类型
	 *
	 * @param code 系统返回参数编码
	 * @return BaseConstantEnum
	 */
	public static CustSourceFirstLevelEnum getEnum(String code) {
		for (CustSourceFirstLevelEnum statusEnum : CustSourceFirstLevelEnum.values()) {
			if (statusEnum.getCode().equals(code)) {
				return statusEnum;
			}
		}
		return null;
	}


	public String getCode() {
		return code;
	}


	public String getDescription() {
		return description;
	}

}
