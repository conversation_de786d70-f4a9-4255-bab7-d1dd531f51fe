/**
 * Copyright (c) 2025, <PERSON><PERSON><PERSON><PERSON> HOWBUY INVESTMENT MANAGEMENT Co., Ltd.
 * All right reserved.
 * <p>
 * THIS IS UNPUBLISHED PROPRIETARY SOURCE CODE OF HOWBUY INVESTMENT
 * MANAGEMENT CO., LTD.  THE CONTENTS OF THIS FILE MAY NOT BE DISCLOSED
 * TO THIRD PARTIES, COPIED OR DUPLICATED IN ANY FORM, IN WHOLE OR IN PART,
 * WITHOUT THE PRIOR WRITTEN PERMISSION OF HOWBUY INVESTMENT MANAGEMENT
 * CO., LTD.
 */
package com.howbuy.crm.account.client.request.sensitive;

import com.howbuy.commons.validator.MyValidation;
import com.howbuy.commons.validator.util.ValidatorTypeEnum;
import com.howbuy.crm.account.client.request.Request;
import lombok.Getter;
import lombok.Setter;

import java.io.Serializable;

/**
 * @description 敏感词查询请求对象
 * <AUTHOR>
 * @date 2024-03-19 14:30:45
 */
@Getter
@Setter
public class SensitiveWordsRequest extends Request implements Serializable {
    
    private static final long serialVersionUID = 1L;
    
    /**
     * 功能模块
     * 01-表示客服来电处理模块
     * 02-表示资产配置报告模块
     */
    @MyValidation(validatorType = ValidatorTypeEnum.String, fieldName = "功能模块", isRequired = true)
    private String module;
} 