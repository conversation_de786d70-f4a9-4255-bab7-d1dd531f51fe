package com.howbuy.crm.account.client.request.commvisit;

import lombok.Getter;
import lombok.Setter;

import java.io.Serializable;
import java.util.List;

/**
 * @description: 修改主管请求
 * <AUTHOR>
 * @date 2025-04-08 10:26:00
 * @since JDK 1.8
 */
@Getter
@Setter
public class UpdateManagerRequest implements Serializable {

    private static final long serialVersionUID = 1L;
    /**
     * 拜访纪要ID
     */
    private List<String> visitMinutesIds;
    
    /**
     * 是否清空
     */
    private Boolean isClear;
    
    /**
     * 新用户ID
     */
    private String newUserId;
    
    /**
     * 当前用户ID
     */
    private String currentUserId;
} 