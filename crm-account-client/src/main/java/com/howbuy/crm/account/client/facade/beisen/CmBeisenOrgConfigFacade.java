package com.howbuy.crm.account.client.facade.beisen;

import com.howbuy.crm.account.client.request.beisen.BeisenConfigIdRequest;
import com.howbuy.crm.account.client.request.beisen.InsertBeisenOrgConfigRequest;
import com.howbuy.crm.account.client.request.beisen.QueryBeisenOrgConfigRequest;
import com.howbuy.crm.account.client.request.beisen.UpdateBeisenOrgConfigRequest;
import com.howbuy.crm.account.client.response.ExportToFileVO;
import com.howbuy.crm.account.client.response.PageVO;
import com.howbuy.crm.account.client.response.Response;
import com.howbuy.crm.account.client.response.beisen.CmBeisenOrgConfigDetailVO;
import com.howbuy.crm.account.client.response.beisen.CmBeisenOrgVO;

/**
 * <AUTHOR>
 * @description: 请在此添加描述
 * @date 2024/10/22 19:26
 * @since JDK 1.8
 */
public interface CmBeisenOrgConfigFacade {

    /**
     * @api {DUBBO} com.howbuy.crm.account.client.facade.beisen.CmBeisenOrgConfigFacade.queryBeisenOrgConfigList(queryBeisenOrgConfigRequest)
     * @apiVersion 1.0.0
     * @apiGroup CmBeisenOrgConfigFacade
     * @apiName queryBeisenOrgConfigList()
     * @apiDescription 查询北森架构配置列表
     * @apiParam (请求参数) {String} orgNameBeisen 架构名称（北森）
     * @apiParam (请求参数) {String} orgCode 部门编码
     * @apiParam (请求参数) {String} startDate 起始日期
     * @apiParam (请求参数) {String} endDate 结束日期
     * @apiParam (请求参数) {Number} pageNo 页号
     * @apiParam (请求参数) {Number} pageSize 分页大小
     * @apiParamExample 请求参数示例
     * endDate=yD&orgNameBeisen=4mL2dVRF&orgCode=gJG&pageNo=9283&pageSize=8145&startDate=g1
     * @apiSuccess (响应结果) {String} code 状态码
     * @apiSuccess (响应结果) {String} description 描述信息
     * @apiSuccess (响应结果) {Object} data 数据封装
     * @apiSuccess (响应结果) {Number} data.page 当前第几页
     * @apiSuccess (响应结果) {Number} data.size 单页条数
     * @apiSuccess (响应结果) {Number} data.total 总条数
     * @apiSuccess (响应结果) {Array} data.rows 数据对象列表
     * @apiSuccess (响应结果) {String} data.rows.id 主键
     * @apiSuccess (响应结果) {String} data.rows.orgIdBeisen 架构ID（北森）
     * @apiSuccess (响应结果) {String} data.rows.orgNameBeisen 架构名称（北森）
     * @apiSuccess (响应结果) {String} data.rows.orgCode 所属部门
     * @apiSuccess (响应结果) {String} data.rows.centerOrg 业务中心
     * @apiSuccess (响应结果) {String} data.rows.startDate 起始日期
     * @apiSuccess (响应结果) {String} data.rows.endDate 结束日期
     * @apiSuccessExample 响应结果示例
     * {"code":"Ufg","data":{"total":2469,"size":485,"page":7721,"rows":[{"endDate":"ba9ncQgp","orgNameBeisen":"yfFTzS5","orgCode":"UgDGsT","orgIdBeisen":"Rgk","id":"mo3p8d9f","centerOrg":"HBCL","startDate":"oW6L3"}]},"description":"kz"}
     */
    Response<PageVO<CmBeisenOrgConfigDetailVO>> queryBeisenOrgConfigList(QueryBeisenOrgConfigRequest queryBeisenOrgConfigRequest);


    /**
     * @api {DUBBO} com.howbuy.crm.account.client.facade.beisen.CmBeisenOrgConfigFacade.insertBeisenOrgConfig(insertBeisenOrgConfigRequest)
     * @apiVersion 1.0.0
     * @apiGroup CmBeisenOrgConfigFacade
     * @apiName insertBeisenOrgConfig()
     * @apiDescription 插入
     * @apiParam (请求参数) {String} orgIdBeisen 架构ID（北森）
     * @apiParam (请求参数) {String} orgNameBeisen 架构名称（北森）
     * @apiParam (请求参数) {String} orgCode 部门编码
     * @apiParam (请求参数) {String} centerOrg 业务中心
     * @apiParam (请求参数) {String} startDate 起始日期
     * @apiParam (请求参数) {String} endDate 结束日期
     * @apiParamExample 请求参数示例
     * endDate=nf&orgNameBeisen=JH4Xbhl4GU&orgCode=00iBn1pJ3H&orgIdBeisen=2&centerOrg=6R02Ios7jd&startDate=FkCFz4m
     * @apiSuccess (响应结果) {String} code 状态码
     * @apiSuccess (响应结果) {String} description 描述信息
     * @apiSuccess (响应结果) {String} data 数据封装
     * @apiSuccessExample 响应结果示例
     * {"code":"m","data":{},"description":"laZi"}
     */
    Response<String> insertBeisenOrgConfig(InsertBeisenOrgConfigRequest insertBeisenOrgConfigRequest);

    /**
     * @api {DUBBO} com.howbuy.crm.account.client.facade.beisen.CmBeisenOrgConfigFacade.updateBeisenOrgConfig(updateBeisenOrgConfigRequest)
     * @apiVersion 1.0.0
     * @apiGroup CmBeisenOrgConfigFacade
     * @apiName updateBeisenOrgConfig()
     * @apiDescription 更新
     * @apiParam (请求参数) {String} id 主键
     * @apiParam (请求参数) {String} orgIdBeisen 架构ID（北森）
     * @apiParam (请求参数) {String} orgNameBeisen 架构名称（北森）
     * @apiParam (请求参数) {String} orgCode 部门编码
     * @apiParam (请求参数) {String} centerOrg 业务中心
     * @apiParam (请求参数) {String} startDate 起始日期
     * @apiParam (请求参数) {String} endDate 结束日期
     * @apiParamExample 请求参数示例
     * endDate=nf&orgNameBeisen=JH4Xbhl4GU&orgCode=00iBn1pJ3H&orgIdBeisen=2&centerOrg=6R02Ios7jd&startDate=FkCFz4m
     * @apiSuccess (响应结果) {String} code 状态码
     * @apiSuccess (响应结果) {String} description 描述信息
     * @apiSuccess (响应结果) {String} data 数据封装
     * @apiSuccessExample 响应结果示例
     * {"code":"m","data":{},"description":"laZi"}
     */
    Response<String> updateBeisenOrgConfig(UpdateBeisenOrgConfigRequest updateBeisenOrgConfigRequest);

    /**
     * @api {DUBBO} com.howbuy.crm.account.client.facade.beisen.CmBeisenOrgConfigFacade.deleteBeisenOrgConfig(beisenConfigIdRequest)
     * @apiVersion 1.0.0
     * @apiGroup CmBeisenOrgConfigFacade
     * @apiName deleteBeisenOrgConfig()
     * @apiDescription 删除
     * @apiParam (请求参数) {String} id id
     * @apiParamExample 请求参数示例
     * endDate=nf&orgNameBeisen=JH4Xbhl4GU&orgCode=00iBn1pJ3H&orgIdBeisen=2&centerOrg=6R02Ios7jd&startDate=FkCFz4m
     * @apiSuccess (响应结果) {String} code 状态码
     * @apiSuccess (响应结果) {String} description 描述信息
     * @apiSuccess (响应结果) {String} data 数据封装
     * @apiSuccessExample 响应结果示例
     * {"code":"m","data":{},"description":"laZi"}
     */
    Response<String> deleteBeisenOrgConfig(BeisenConfigIdRequest beisenConfigIdRequest);

    /**
     * @api {DUBBO} com.howbuy.crm.account.client.facade.beisen.CmBeisenOrgConfigFacade.getConfigDetail(beisenConfigIdRequest)
     * @apiVersion 1.0.0
     * @apiGroup CmBeisenOrgConfigFacade
     * @apiName getConfigDetail()
     * @apiDescription 详情
     * @apiParam (请求参数) {String} id id
     * @apiParamExample 请求参数示例
     * endDate=nf&orgNameBeisen=JH4Xbhl4GU&orgCode=00iBn1pJ3H&orgIdBeisen=2&centerOrg=6R02Ios7jd&startDate=FkCFz4m
     * @apiSuccess (响应结果) {String} code 状态码
     * @apiSuccess (响应结果) {String} description 描述信息
     * @apiSuccess (响应结果) {Object} data 数据封装
     * @apiSuccess (响应结果) {String} data.id 主键
     * @apiSuccess (响应结果) {String} data.orgIdBeisen 架构ID（北森）
     * @apiSuccess (响应结果) {String} data.orgNameBeisen 架构名称（北森）
     * @apiSuccess (响应结果) {String} data.orgCode 所属部门
     * @apiSuccess (响应结果) {String} data.centerOrg 业务中心
     * @apiSuccess (响应结果) {String} data.startDate 起始日期
     * @apiSuccess (响应结果) {String} data.endDate 结束日期
     * @apiSuccessExample 响应结果示例
     * {"code":"m","data":{},"description":"laZi"}
     */
    Response<CmBeisenOrgConfigDetailVO> getConfigDetail(BeisenConfigIdRequest beisenConfigIdRequest);

    /**
     * @api {DUBBO} com.howbuy.crm.account.client.facade.beisen.CmBeisenOrgConfigFacade.exportBeisenOrgConfigList(request)
     * @apiVersion 1.0.0
     * @apiGroup CmBeisenOrgConfigFacade
     * @apiName exportBeisenOrgConfigList()
     * @apiDescription 导出
     * @apiParam (请求参数) {String} orgNameBeisen 架构名称（北森）
     * @apiParam (请求参数) {String} orgCode 部门编码
     * @apiParam (请求参数) {String} startDate 起始日期
     * @apiParam (请求参数) {String} endDate 结束日期
     * @apiParam (请求参数) {Number} pageNo 页号
     * @apiParamExample 请求参数示例
     * endDate=nf&orgNameBeisen=JH4Xbhl4GU&orgCode=00iBn1pJ3H&orgIdBeisen=2&centerOrg=6R02Ios7jd&startDate=FkCFz4m
     * @apiSuccess (响应结果) {String} code 状态码
     * @apiSuccess (响应结果) {String} description 描述信息
     * @apiSuccess (响应结果) {Object} data 数据封装
     * @apiSuccess (响应结果) {String} data.fileByte 文件字节数组的base64编码串
     * @apiSuccess (响应结果) {String} data.name 文件名
     * @apiSuccessExample 响应结果示例
     * {"code":"m","data":{},"description":"laZi"}
     */
    Response<ExportToFileVO> exportBeisenOrgConfigList(QueryBeisenOrgConfigRequest request);

    /**
     * @api {DUBBO} com.howbuy.crm.account.client.facade.beisen.CmBeisenOrgConfigFacade.queryBeisenOrg()
     * @apiVersion 1.0.0
     * @apiGroup CmBeisenOrgConfigFacade
     * @apiName queryBeisenOrg()
     * @apiDescription 查北森机构列表
     * @apiSuccess (响应结果) {String} code 状态码
     * @apiSuccess (响应结果) {String} description 描述信息
     * @apiSuccess (响应结果) {Object} data 数据封装
     * @apiSuccess (响应结果) {Array} data.list 明细
     * @apiSuccess (响应结果) {String} data.list.orgIdBeisen 架构ID
     * @apiSuccess (响应结果) {String} data.list.orgNameBeisen 架构名称
     * @apiSuccessExample 响应结果示例
     * {"code":"UjQMNlxAs","data":{"list":[{"orgNameBeisen":"zuFRrx","orgIdBeisen":"OQLEPng"}]},"description":"qaibbhweI"}
     */
    Response<CmBeisenOrgVO> queryBeisenOrg();
}