package com.howbuy.crm.account.client.response.custinfo;

import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.io.Serializable;
import java.util.Date;

/**
 * @description: ([香港/一账通]异常客户待关联表)
 * <AUTHOR>
 * @date 2023/12/11 10:57
 * @since JDK 1.8
 */
@Data
@EqualsAndHashCode(callSuper = true)
public class AbnormalRelatedCustVO  extends  AbnormalRelatedDetailVO implements Serializable {

    private static final long serialVersionUID = 360919284646542496L;



    /**
     * 香港账户侧 客户信息
     */
    private CustKeyAttrInfo hkSideInfo;


    /**
     * 一账通账户侧 客户信息
     */
    private CustKeyAttrInfo hboneSideInfo;


    /**
     *投顾客户号】的【创建日期】
     * yyyy-MM-dd
     */
    private String createDt;

}