package com.howbuy.crm.account.client.enums.custinfo;

import com.howbuy.crm.account.client.enums.DisCodeEnum;

/**
 * @description:(客户类型枚举 0 机构客户 1 个人客户 2 产品客户 )
 * @param
 * @return
 * @author: haoran.zhang
 * @date: 2023/12/8 17:46
 * @since JDK 1.8
 */
public enum ConsCustInvestTypeEnum {

	/**
	 * 0-机构客户
	 */
	INSTITUTION("0", "机构客户", DisCodeEnum.FOF),
	/**
	 * 1-个人客户
	 */
	PERSONAL("1", "个人客户",DisCodeEnum.HOWBUY),
	/**
	 * 2-产品客户
	 */
	PRODUCT("2", "产品客户",DisCodeEnum.FOF);

	/**
	 *  编码
	 */
	private String code;

	/**
	 * 描述
	 */
	private String description;

	/**
	 * 所属分销
	 */
	private DisCodeEnum usedDisCodeEnum;

	ConsCustInvestTypeEnum(String code, String description, DisCodeEnum usedDisCodeEnum) {
		this.code = code;
		this.description = description;
		this.usedDisCodeEnum=usedDisCodeEnum;
	}

	/**
	 * 通过code获得
	 * 
	 * @param code
	 *            系统返回参数编码
	 * @return description 描述
	 */
	public static String getDescription(String code) {
		ConsCustInvestTypeEnum statusEnum=getEnum(code);
		return statusEnum==null?null :statusEnum.getDescription();
	}

	/**
	 * 通过code直接返回 整个枚举类型
	 * 
	 * @param code
	 *            系统返回参数编码
	 * @return BaseConstantEnum
	 */
	public static ConsCustInvestTypeEnum getEnum(String code) {
		for(ConsCustInvestTypeEnum statusEnum : ConsCustInvestTypeEnum.values()){
			if(statusEnum.getCode().equals(code)){
				return statusEnum;
			}
		}
		return null;
	}



	public String getCode() {
		return code;
	}


	public String getDescription() {
		return description;
	}


	public DisCodeEnum getUsedDisCodeEnum() {
		return usedDisCodeEnum;
	}

	public void setUsedDisCodeEnum(DisCodeEnum usedDisCodeEnum) {
		this.usedDisCodeEnum = usedDisCodeEnum;
	}
}
