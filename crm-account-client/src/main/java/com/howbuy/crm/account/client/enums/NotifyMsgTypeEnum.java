/**
 * Project Name:crm-conscard-client
 * File Name:ConscardConstantEnum.java
 * Package Name:com.howbuy.crm.conscard.base
 * Date:2017年5月26日下午5:59:27
 * Copyright (c) 2017, <EMAIL> All Rights Reserved.
 *
 */

package com.howbuy.crm.account.client.enums;


/**
 * @description:(通知类型 1-按照投顾code列表 发送  2-按照角色列表 发送 3-按照 hboneNo 发送  4-按照 hkTxAcctNo 发送)
 * @param
 * @return
 * @author: haoran.zhang
 * @date: 2024/1/26 14:27
 * @since JDK 1.8
 */
public enum NotifyMsgTypeEnum {

	/**
	 * 1-按照投顾code列表 发送
	 */
	CONS_CODE_LIST("1", "按照投顾code列表 发送"),

	/**
	 * 2-按照角色列表 发送
	 */
	ROLE_CODE_LIST("2", "按照角色列表 发送"),

	/**
	 * 3-按照 hboneNo 发送
	 */
	HBONE_NO("3", "按照 hboneNo 发送"),

	/**
	 * 4-按照 hkTxAcctNo 发送
	 */
	HK_TX_ACCT_NO("4", "按照 hkTxAcctNo 发送");

	/**
	 * 系统返回参数编码
	 */
	private String code;

	/**
	 * 描述
	 */
	private String description;

	NotifyMsgTypeEnum(String code, String description) {
		this.code = code;
		this.description = description;
	}

	/**
	 * 通过code获得
	 * 
	 * @param code
	 *            系统返回参数编码
	 * @return description 描述
	 */
	public static String getDescription(String code) {
		NotifyMsgTypeEnum statusEnum=getEnum(code);
		return statusEnum==null?null :statusEnum.getDescription();
	}

	/**
	 * 通过code直接返回 整个枚举类型
	 * 
	 * @param code
	 *            系统返回参数编码
	 * @return BaseConstantEnum
	 */
	public static NotifyMsgTypeEnum getEnum(String code) {
		for(NotifyMsgTypeEnum statusEnum : NotifyMsgTypeEnum.values()){
			if(statusEnum.getCode().equals(code)){
				return statusEnum;
			}
		}
		return null;
	}


	/**
	 * 通过 description 直接返回 整个枚举类型
	 * @param description
	 * @return
	 */
	public static NotifyMsgTypeEnum getEnumByDesc(String description) {
		for(NotifyMsgTypeEnum statusEnum : NotifyMsgTypeEnum.values()){
			if(statusEnum.getDescription().equals(description)){
				return statusEnum;
			}
		}
		return null;
	}

	public String getCode() {
		return code;
	}

	public void setCode(String code) {
		this.code = code;
	}

	public String getDescription() {
		return description;
	}

	public void setDescription(String description) {
		this.description = description;
	}

}
