package com.howbuy.crm.account.client.response.custinfo;


import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;

import java.io.Serializable;
import java.util.Date;

/**
 * 一账通账户中心 侧  客户详细 信息属性
 * 来源：com.howbuy.acccenter.facade.query.queryacccustinfo.QueryAccCustInfoResponse.AccCustInfoBean
 */
@Data
public class HboneAcctCustDetailInfoVO implements Serializable {

    private static final long serialVersionUID = 7917428583810226477L;


    /**
     * 证件地区码-身份证签发地区编码
     */
    private String idSignAreaCode;

    /**
     * 手机号码区号
     */
    private String mobileAreaCode;


    /**
     * 一账通号
     */
    private String hboneNo;
    /**
     * 客户姓名
     */
    private String username;


    //以下属性的 注释 参考 类：HboneAcctCustDetailInfoVO


    /**
     * 手机号码验证状态
     */
    private String mobileVerifyStatus;

    private String authType;
    private Boolean importHbvip;
    private String hboneRegOutletCode;
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date hboneRegDate;
    private String authOutletCode;
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date authDate;
    private String regOutletCode;
    private String regTradeChan;
    private String regDisCode;
    private String regDt;
    private String custStat;
    private String vipFlag;
    private String invstType;
    private String custName;
    private String idType;
    private String idValidityStart;
    private String idValidityEnd;
    private String idAlwaysValidFlag;
    private String idVrfyStat;
    private String birthday;
    private String gender;
    private String canDt;
    private String idNoDigest;
    private String idNoMask;
    private String mobileDigest;
    private String mobileMask;
    private String authCardDigest;
    private String authCardMask;
    private String secondHboneRegOutletCode;
    private String secondRegOutletCode;
    private String minorFlag;
    private String minorId;
    private String eduLevel;
    private String vocation;
    private String incLevel;
    private String nationality;
    private String marriageStat;
    private Boolean esignatureConfirmation;
    private String esignatureConfirmedDate;
    private String custImeiDigest;
    private String custImeiMask;
    private String ipAddress;
    private String macAddress;
    /** @deprecated */
    @Deprecated
    private String idImageUploadStatus;
    /** @deprecated */
    @Deprecated
    private String idImageUploadDate;
    private String dataAuthAgreementSign;
    private String dataAuthAgreementSignDate;


    /**
     * 海外资产隔离标志 0-否 1-是
     */
    private String hkAssetIsolateFlag;
    /**
     * 海外资产隔离标志更新时间 yyyyMMddHHmmss
     */
    private String hkAssetIsolateFlagUpDt;



}
