/**
 * Copyright (c) 2025, <PERSON><PERSON><PERSON><PERSON> HOWBUY INVESTMENT MANAGEMENT Co., Ltd.
 * All right reserved.
 * <p>
 * THIS IS UNPUBLISHED PROPRIETARY SOURCE CODE OF HOWBUY INVESTMENT
 * MANAGEMENT CO., LTD.  THE CONTENTS OF THIS FILE MAY NOT BE DISCLOSED
 * TO THIRD PARTIES, COPIED OR DUPLICATED IN ANY FORM, IN WHOLE OR IN PART,
 * WITHOUT THE PRIOR WRITTEN PERMISSION OF HOWBUY INVESTMENT MANAGEMENT
 * CO., LTD.
 */
package com.howbuy.crm.account.client.enums.custinfo;

/**
 * @description: (香港异常等级枚举)
 * <AUTHOR>
 * @date 2025-04-15 15:06:12
 * @since JDK 1.8
 */
public enum HkAbnormalLevelEnum {

    /**
     * 高级别异常
     */
    HIGH("3", "高"),

    /**
     * 中级别异常
     */
    MEDIUM("2", "中"),

    /**
     * 低级别异常
     */
    LOW("1", "低");

    /**
     * 异常等级编码
     */
    private String code;
    
    /**
     * 异常等级描述
     */
    private String description;

    HkAbnormalLevelEnum(String code, String description) {
        this.code = code;
        this.description = description;
    }

    /**
     * 通过code获得
     * @param code 异常等级编码
     * @return 描述信息
     */
    public static String getDescription(String code) {
        HkAbnormalLevelEnum levelEnum = getEnum(code);
        return levelEnum == null ? null : levelEnum.getDescription();
    }

    /**
     * 通过描述获得编码
     * @param description 异常等级描述
     * @return 编码
     */
    public static String getCodeByDescription(String description) {
        for (HkAbnormalLevelEnum levelEnum : HkAbnormalLevelEnum.values()) {
            if (levelEnum.getDescription().equals(description)) {
                return levelEnum.getCode();
            }
        }
        return null;
    }

    /**
     * 通过code直接返回整个枚举类型
     * @param code 异常等级编码
     * @return 枚举值
     */
    public static HkAbnormalLevelEnum getEnum(String code) {
        for (HkAbnormalLevelEnum levelEnum : HkAbnormalLevelEnum.values()) {
            if (levelEnum.getCode().equals(code)) {
                return levelEnum;
            }
        }
        return null;
    }

    public String getCode() {
        return code;
    }

    public String getDescription() {
        return description;
    }
} 