/**
 * Copyright (c) 2023, <PERSON><PERSON><PERSON><PERSON> HOWBUY INVESTMENT MANAGEMENT Co., Ltd.
 * All right reserved.
 * <p>
 * THIS IS UNPUBLISHED PROPRIETARY SOURCE CODE OF HOWBUY INVESTMENT
 * MANAGEMENT CO., LTD.  THE CONTENTS OF THIS FILE MAY NOT BE DISCLOSED
 * TO THIRD PARTIES, COPIED OR DUPLICATED IN ANY FORM, IN WHOLE OR IN PART,
 * WITHOUT THE PRIOR WRITTEN PERMISSION OF HOWBUY INVESTMENT MANAGEMENT
 * CO., LTD.
 */
package com.howbuy.crm.account.client.request.custinfo;

import com.howbuy.commons.validator.MyValidation;
import com.howbuy.commons.validator.util.ValidatorTypeEnum;
import lombok.Data;

import java.io.Serializable;

/**
 * <AUTHOR>
 * @description: (根据一账通账号，创建crm客户信息 request 参数对象)
 * @date 2025年5月19日
 * @since JDK 1.8
 */
@Data
public class CreateCustByHboneRequest implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 一账通号
     */
    @MyValidation(validatorType = ValidatorTypeEnum.String, fieldName = "一账通号", isRequired = true)
    private String hboneNo;

    /**
     * 操作来源 [1-MQ]时，为[异常来源]，参考枚举类[{@link com.howbuy.crm.account.client.enums.custinfo.FullCustSourceEnum}]
     * [2-菜单页面]时，为菜单名称
     */
    @MyValidation(validatorType = ValidatorTypeEnum.String, fieldName = "操作来源", isRequired = true)
    private String operateSource;

    /**
     * operateChannel 操作通道 1-MQ  2-菜单页面
     */
    @MyValidation(validatorType = ValidatorTypeEnum.String, fieldName = "操作通道", isRequired = true)
    private String operateChannel;

    /**
     * 分销机构号(disCode)
     */
    private String disCode;

}