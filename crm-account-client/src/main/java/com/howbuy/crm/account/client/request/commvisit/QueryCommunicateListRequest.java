package com.howbuy.crm.account.client.request.commvisit;

import lombok.Getter;
import lombok.Setter;

import java.io.Serializable;

/**
 * @description: 查询客户沟通记录列表请求
 * <AUTHOR>
 * @date 2025-04-08 10:26:00
 * @since JDK 1.8
 */
@Getter
@Setter
public class QueryCommunicateListRequest implements Serializable {

    private static final long serialVersionUID = 1L;
    /**
     * 投顾客户号
     */
    private String consCustNo;
    
    /**
     * 分页页码,默认1
     */
    private Integer pageNo = 1;
    
    /**
     * 分页大小,默认20
     */
    private Integer pageSize = 20;
} 