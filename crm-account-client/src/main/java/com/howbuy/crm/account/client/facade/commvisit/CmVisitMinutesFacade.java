package com.howbuy.crm.account.client.facade.commvisit;

import com.howbuy.crm.account.client.request.commvisit.ExportVisitMinutesRequest;
import com.howbuy.crm.account.client.request.commvisit.QueryVisitMinutesDetailRequest;
import com.howbuy.crm.account.client.request.commvisit.QueryVisitMinutesForFeedbackRequest;
import com.howbuy.crm.account.client.request.commvisit.QueryVisitMinutesListRequest;
import com.howbuy.crm.account.client.request.commvisit.SaveFeedbackRequest;
import com.howbuy.crm.account.client.request.commvisit.UpdateManagerRequest;
import com.howbuy.crm.account.client.request.commvisit.SaveCustFeedbackRequest;
import com.howbuy.crm.account.client.request.commvisit.SearchUserRequest;
import com.howbuy.crm.account.client.response.ExportToFileVO;
import com.howbuy.crm.account.client.response.Response;
import com.howbuy.crm.account.client.response.commvisit.OperationResultVO;
import com.howbuy.crm.account.client.response.commvisit.VisitMinutesDetailVO;
import com.howbuy.crm.account.client.response.commvisit.VisitMinutesForFeedbackVO;
import com.howbuy.crm.account.client.response.commvisit.VisitMinutesListVO;
import com.howbuy.crm.account.client.response.commvisit.SearchUserVO;
/**
 * <AUTHOR>
 * @date 2025-04-08 10:26:00
 * @since JDK 1.8
 */
public interface CmVisitMinutesFacade {

    /**
     * @api {DUBBO} com.howbuy.crm.account.client.facade.commvisit.CmVisitMinutesFacade.getVisitMinutesList() getVisitMinutesList()
     * @apiVersion 1.0.0
     * @apiGroup CmVisitMinutesFacade
     * @apiName getVisitMinutesList()
     * @apiDescription 查询客户拜访纪要列表
     * @apiParam (请求参数) {String} consId 所属投顾ID
     * @apiParam (请求参数) {String} visitDateStart 拜访日期开始(格式YYYYMMDD)
     * @apiParam (请求参数) {String} visitDateEnd 拜访日期结束(格式YYYYMMDD)
     * @apiParam (请求参数) {String} createDateStart 创建日期开始(格式YYYYMMDD)
     * @apiParam (请求参数) {String} createDateEnd 创建日期结束(格式YYYYMMDD)
     * @apiParam (请求参数) {Array} visitPurpose 拜访目的列表,多选
     * @apiParam (请求参数) {String} custName 客户姓名,精确匹配
     * @apiParam (请求参数) {String} consCustNo 投顾客户号,精确匹配
     * @apiParam (请求参数) {String} accompanyingUser 陪访人,精确匹配
     * @apiParam (请求参数) {String} managerId 上级主管,精确匹配
     * @apiParam (请求参数) {Array} feedbackStatus 反馈情况,可选:陪访人未填、上级主管未填、陪访人已填、上级主管已填
     * @apiParam (请求参数) {Integer} pageNo 分页页码,默认1
     * @apiParam (请求参数) {Integer} pageSize 分页大小,可选100/200/500/1000/2000
     * @apiParam (请求参数) {String} currentUserId 当前操作用户ID
     *
     * @apiParamExample 请求参数示例
     * {
     *   "consId": "C001",
     *   "visitDateStart": "********",
     *   "visitDateEnd": "20240407",
     *   "custName": "张三",
     *   "consCustNo": "P001",
     *   "pageNo": 1,
     *   "pageSize": 100
     * }
     *
     * @apiSuccess (响应结果) {Long} total 总记录数
     * @apiSuccess (响应结果) {Array} visitMinutesList 拜访纪要列表
     * @apiSuccess (响应结果) {String} visitMinutesList.createTime 创建日期,格式YYYY-MM-DD HH:MM:SS
     * @apiSuccess (响应结果) {String} visitMinutesList.visitDt 拜访日期,格式YYYY-MM-DD
     * @apiSuccess (响应结果) {String} visitMinutesList.visitPurpose 拜访目的,多个用逗号分隔
     * @apiSuccess (响应结果) {String} visitMinutesList.consCustNo 投顾客户号
     * @apiSuccess (响应结果) {String} visitMinutesList.custName 客户姓名
     * @apiSuccess (响应结果) {String} visitMinutesList.creatorName 纪要创建人姓名
     * @apiSuccess (响应结果) {String} visitMinutesList.centerName 所属中心
     * @apiSuccess (响应结果) {String} visitMinutesList.areaName 所属区域
     * @apiSuccess (响应结果) {String} visitMinutesList.branchName 所属分公司
     * @apiSuccess (响应结果) {String} visitMinutesList.visitType 沟通方式
     * @apiSuccess (响应结果) {String} visitMinutesList.marketVal 客户存量
     * @apiSuccess (响应结果) {String} visitMinutesList.healthAvgStar 客户综合健康度
     * @apiSuccess (响应结果) {String} visitMinutesList.giveInformation 提供资料
     * @apiSuccess (响应结果) {String} visitMinutesList.attendRole 参与人员及角色
     * @apiSuccess (响应结果) {String} visitMinutesList.productServiceFeedback 产品服务反馈
     * @apiSuccess (响应结果) {String} visitMinutesList.ipsFeedback IPS报告反馈
     * @apiSuccess (响应结果) {String} visitMinutesList.addAmount 近期可用于加仓的金额,币种+金额+万,多个逗号分隔
     * @apiSuccess (响应结果) {String} visitMinutesList.focusAsset 关注资产
     * @apiSuccess (响应结果) {String} visitMinutesList.estimateNeedBusiness 客户需求
     * @apiSuccess (响应结果) {String} visitMinutesList.nextPlan 工作计划
     * @apiSuccess (响应结果) {String} visitMinutesList.accompanyingType 陪访人类型
     * @apiSuccess (响应结果) {String} visitMinutesList.accompanyingUser 陪访人姓名,多个逗号分隔
     * @apiSuccess (响应结果) {String} visitMinutesList.accompanySummary 陪访人反馈概要,格式：姓名:内容,多个分号分隔
     * @apiSuccess (响应结果) {String} visitMinutesList.accompanySuggestion 陪访人反馈建议,格式：姓名:内容,多个分号分隔
     * @apiSuccess (响应结果) {String} visitMinutesList.managerName 上级主管姓名
     * @apiSuccess (响应结果) {String} visitMinutesList.managerSummary 主管反馈概要
     * @apiSuccess (响应结果) {String} visitMinutesList.managerSuggestion 主管反馈建议
     * @apiSuccess (响应结果) {Boolean} visitMinutesList.canAccompanyFeedback 是否可陪访人反馈
     * @apiSuccess (响应结果) {Boolean} visitMinutesList.canManagerFeedback 是否可主管反馈
     *
     * @apiSuccessExample 响应结果示例
     * {
     *   "code": "0000",
     *   "description": "成功",
     *   "data": {
     *     "total": 100,
     *     "visitMinutesList": [{
     *       "createTime": "2024-04-07 10:00:00",
     *       "visitDt": "2024-04-07",
     *       "visitPurpose": "产品推介,投资建议",
     *       "consCustNo": "P001",
     *       "custName": "张三",
     *       "creatorName": "李四",
     *       "centerName": "上海中心",
     *       "areaName": "浦东区域",
     *       "branchName": "陆家嘴分公司"
     *     }]
     *   }
     * }
     */
    Response<VisitMinutesListVO> getVisitMinutesList(QueryVisitMinutesListRequest request);
    
    /**
     * @api {DUBBO} com.howbuy.crm.account.client.facade.commvisit.CmVisitMinutesFacade.getVisitMinutesDetail() getVisitMinutesDetail()
     * @apiVersion 1.0.0
     * @apiGroup CmVisitMinutesFacade
     * @apiName getVisitMinutesDetail()
     * @apiDescription 查询客户拜访纪要明细
     * 
     * @apiParam (请求参数) {String} visitMinutesId 拜访纪要ID
     * @apiParam (请求参数) {String} feedbackType 反馈类型 1-陪访人 2-主管
     * @apiParam (请求参数) {String} currentUserId 当前操作用户ID
     * 
     * @apiParamExample 请求参数示例
     * {
     *   "visitMinutesId": "VM001",
     *   "feedbackType": "1",
     *   "currentUserId": "U001"
     * }
     * 
     * @apiSuccess (响应结果) {String} custName 客户姓名
     * @apiSuccess (响应结果) {String} consCustNo 投顾客户号
     * @apiSuccess (响应结果) {String} visitDt 拜访日期,格式YYYYMMDD
     * @apiSuccess (响应结果) {String} visitType 沟通方式
     * @apiSuccess (响应结果) {String} marketVal 客户存量
     * @apiSuccess (响应结果) {String} healthAvgStar 客户综合健康度
     * @apiSuccess (响应结果) {Array} visitPurposeList 拜访目的列表
     * @apiSuccess (响应结果) {String} visitPurposeList.code 拜访目的编码
     * @apiSuccess (响应结果) {String} visitPurposeList.name 拜访目的名称
     * @apiSuccess (响应结果) {String} visitPurposeOther 拜访目的其他说明
     * @apiSuccess (响应结果) {String} reportId 报告ID
     * @apiSuccess (响应结果) {String} reportTitle 报告标题
     * @apiSuccess (响应结果) {String} giveInformation 提供资料
     * @apiSuccess (响应结果) {Array} accompanyingList 陪访人姓名列表
     * @apiSuccess (响应结果) {String} attendRole 参与人员及角色
     * @apiSuccess (响应结果) {String} productServiceFeedback 产品服务反馈
     * @apiSuccess (响应结果) {String} ipsFeedback IPS报告反馈
     * @apiSuccess (响应结果) {String} addAmountRmb 人民币金额
     * @apiSuccess (响应结果) {String} addAmountForeign 外币金额
     * @apiSuccess (响应结果) {String} focusAsset 关注资产
     * @apiSuccess (响应结果) {String} estimateNeedBusiness 客户需求
     * @apiSuccess (响应结果) {String} nextPlan 工作计划
     * @apiSuccess (响应结果) {String} accompanyingUserName 陪访人姓名
     * @apiSuccess (响应结果) {String} accompanyingId 陪访人数据ID
     * @apiSuccess (响应结果) {String} managerName 主管姓名
     * @apiSuccess (响应结果) {String} summary 主管概要
     * @apiSuccess (响应结果) {String} suggestion 主管建议
     * @apiSuccess (响应结果) {Boolean} canEdit 是否可编辑
     * 
     * @apiSuccessExample 响应结果示例
     * {
     *   "code": "0000",
     *   "description": "成功",
     *   "data": {
     *     "custName": "张三",
     *     "consCustNo": "P001",
     *     "visitDt": "20240407",
     *     "visitType": "面谈",
     *     "marketVal": "500万",
     *     "healthAvgStar": "4.5",
     *     "visitPurposeList": [
     *       {
     *         "code": "01",
     *         "name": "产品推介"
     *       },
     *       {
     *         "code": "02",
     *         "name": "投资建议"
     *       }
     *     ],
     *     "visitPurposeOther": "其他说明",
     *     "giveInformation": "市场周报",
     *     "accompanyingList": ["王五", "赵六"],
     *     "attendRole": "客户本人",
     *     "productServiceFeedback": "对产品表示满意",
     *     "ipsFeedback": "对报告表示认可",
     *     "addAmountRmb": "100万",
     *     "addAmountForeign": "USD20万",
     *     "focusAsset": "固收类产品",
     *     "estimateNeedBusiness": "有配置需求",
     *     "nextPlan": "下周再次沟通",
     *     "managerName": "张经理",
     *     "canEdit": true
     *   }
     * }
     */
    Response<VisitMinutesDetailVO> getVisitMinutesDetail(QueryVisitMinutesDetailRequest request);
    
    /**
     * @api {DUBBO} com.howbuy.crm.account.client.facade.commvisit.CmVisitMinutesFacade.saveFeedback() saveFeedback()
     * @apiVersion 1.0.0
     * @apiGroup CmVisitMinutesFacade
     * @apiName saveFeedback()
     * @apiDescription 保存客户反馈
     * @apiParam (请求参数) {SaveFeedbackRequest} request 保存客户反馈请求
     * @apiSuccess (响应结果) {Boolean} success 成功标识
     */
    Response<OperationResultVO> saveFeedback(SaveFeedbackRequest request);
    
    /**
     * @api {DUBBO} com.howbuy.crm.account.client.facade.commvisit.CmVisitMinutesFacade.updateManager() updateManager()
     * @apiVersion 1.0.0
     * @apiGroup CmVisitMinutesFacade
     * @apiName updateManager()
     * @apiDescription 修改主管
     * 
     * @apiParam (请求参数) {String} visitMinutesId 拜访纪要ID
     * @apiParam (请求参数) {Boolean} isClear 是否清空原有人员
     * @apiParam (请求参数) {String} newUserId 新用户ID,不清空时必填
     * @apiParam (请求参数) {String} currentUserId 当前操作用户ID
     * 
     * @apiParamExample 请求参数示例
     * {
     *   "visitMinutesId": "VM001",
     *   "isClear": false,
     *   "newUserId": "U002",
     *   "currentUserId": "U001"
     * }
     * 
     * @apiSuccess (响应结果) {Boolean} success 成功标识,true:成功 false:失败
     * @apiSuccess (响应结果) {String} errorMsg 错误信息
     * 
     * @apiSuccessExample 响应结果示例
     * {
     *   "code": "0000",
     *   "description": "成功",
     *   "data": {
     *     "success": true
     *   }
     * }
     */
    Response<OperationResultVO> updateManager(UpdateManagerRequest request);

    /**
     * @api {DUBBO} com.howbuy.crm.account.client.facade.commvisit.CmVisitMinutesFacade.exportVisitMinutes() exportVisitMinutes()
     * @apiVersion 1.0.0
     * @apiGroup CmVisitMinutesFacade
     * @apiName exportVisitMinutes()
     * @apiDescription 导出客户拜访纪要数据
     * 
     * @apiParam (请求参数) {Object} queryParam 查询条件,与列表查询条件相同
     * @apiParam (请求参数) {String} consId 所属投顾ID
     * @apiParam (请求参数) {String} visitDateStart 拜访日期开始(格式YYYYMMDD)
     * @apiParam (请求参数) {String} visitDateEnd 拜访日期结束(格式YYYYMMDD)
     * @apiParam (请求参数) {String} createDateStart 创建日期开始(格式YYYYMMDD)
     * @apiParam (请求参数) {String} createDateEnd 创建日期结束(格式YYYYMMDD)
     * @apiParam (请求参数) {Array} visitPurpose 拜访目的列表,多选
     * @apiParam (请求参数) {String} custName 客户姓名,精确匹配
     * @apiParam (请求参数) {String} consCustNo 投顾客户号,精确匹配
     * @apiParam (请求参数) {String} accompanyingUser 陪访人,精确匹配
     * @apiParam (请求参数) {String} managerId 上级主管,精确匹配
     * @apiParam (请求参数) {Array} feedbackStatus 反馈情况,可选值列表
     * @apiParam (请求参数) {String} currentUserId 当前操作用户ID
     * 
     * @apiParamExample 请求参数示例
     * {
     *   "consId": "C001",
     *   "visitDateStart": "********",
     *   "visitDateEnd": "20240407",
     *   "custName": "张三",
     *   "currentUserId": "U001"
     * }
     * 
     * @apiSuccess (响应结果) {String} fileByte 文件字节数组的base64编码串
     * @apiSuccess (响应结果) {String} name 文件名
     * @apiSuccess (响应结果) {String} type 文件类型
     * @apiSuccess (响应结果) {String} errorMsg 错误信息
     * 
     * @apiSuccessExample 响应结果示例
     * {
     *   "code": "0000",
     *   "description": "成功",
     *   "data": {
     *     "fileByte": "UEsDBBQABgAIAAAAIQD...(base64编码的Excel文件内容)",
     *     "name": "客户拜访纪要数据_20240408.xlsx",
     *     "type": "application/vnd.openxmlformats-officedocument.spreadsheetml.sheet"
     *   }
     * }
     */
    Response<ExportToFileVO> exportVisitMinutes(ExportVisitMinutesRequest request);

    /**
     * @api {DUBBO} com.howbuy.crm.account.client.facade.commvisit.CmVisitMinutesFacade.getVisitMinutesForFeedback() getVisitMinutesForFeedback()
     * @apiVersion 1.0.0
     * @apiGroup CmVisitMinutesFacade
     * @apiName getVisitMinutesForFeedback()
     * @apiDescription 查询拜访纪要反馈明细
     * 
     * @apiParam (请求参数) {String} visitMinutesId 拜访纪要ID
     * @apiParam (请求参数) {String} feedbackType 反馈类型 1-陪访人 2-主管
     * @apiParam (请求参数) {String} currentUserId 当前操作用户ID
     * 
     * @apiParamExample 请求参数示例
     * {
     *   "visitMinutesId": "VM001",
     *   "feedbackType": "1",
     *   "currentUserId": "U001"
     * }
     * 
     * @apiSuccess (响应结果) {String} custName 客户姓名
     * @apiSuccess (响应结果) {String} consCustNo 投顾客户号
     * @apiSuccess (响应结果) {String} visitDt 拜访日期,格式YYYYMMDD
     * @apiSuccess (响应结果) {String} visitType 沟通方式
     * @apiSuccess (响应结果) {String} marketVal 客户存量
     * @apiSuccess (响应结果) {String} healthAvgStar 客户综合健康度
     * @apiSuccess (响应结果) {Array} visitPurposeList 拜访目的列表
     * @apiSuccess (响应结果) {String} visitPurposeList.code 拜访目的编码
     * @apiSuccess (响应结果) {String} visitPurposeList.name 拜访目的名称
     * @apiSuccess (响应结果) {String} visitPurposeOther 拜访目的其他说明
     * @apiSuccess (响应结果) {String} reportId 报告ID
     * @apiSuccess (响应结果) {String} reportTitle 报告标题
     * @apiSuccess (响应结果) {String} giveInformation 提供资料
     * @apiSuccess (响应结果) {Array} accompanyingList 陪访人姓名列表
     * @apiSuccess (响应结果) {String} attendRole 参与人员及角色
     * @apiSuccess (响应结果) {String} productServiceFeedback 产品服务反馈
     * @apiSuccess (响应结果) {String} ipsFeedback IPS报告反馈
     * @apiSuccess (响应结果) {String} addAmountRmb 人民币金额
     * @apiSuccess (响应结果) {String} addAmountForeign 外币金额
     * @apiSuccess (响应结果) {String} focusAsset 关注资产
     * @apiSuccess (响应结果) {String} estimateNeedBusiness 客户需求
     * @apiSuccess (响应结果) {String} nextPlan 工作计划
     * @apiSuccess (响应结果) {String} accompanyingUserName 陪访人姓名
     * @apiSuccess (响应结果) {String} accompanyingId 陪访人数据ID
     * @apiSuccess (响应结果) {String} managerName 主管姓名
     * @apiSuccess (响应结果) {String} summary 主管概要
     * @apiSuccess (响应结果) {String} suggestion 主管建议
     * @apiSuccess (响应结果) {Boolean} canEdit 是否可编辑
     * 
     * @apiSuccessExample 响应结果示例
     * {
     *   "code": "0000",
     *   "description": "成功",
     *   "data": {
     *     "custName": "张三",
     *     "consCustNo": "P001",
     *     "visitDt": "20240407",
     *     "visitType": "面谈",
     *     "marketVal": "500万",
     *     "healthAvgStar": "4.5",
     *     "visitPurposeList": [
     *       {
     *         "code": "01",
     *         "name": "产品推介"
     *       },
     *       {
     *         "code": "02",
     *         "name": "投资建议"
     *       }
     *     ],
     *     "visitPurposeOther": "其他说明",
     *     "giveInformation": "市场周报",
     *     "accompanyingList": ["王五", "赵六"],
     *     "attendRole": "客户本人",
     *     "productServiceFeedback": "对产品表示满意",
     *     "ipsFeedback": "对报告表示认可",
     *     "addAmountRmb": "100万",
     *     "addAmountForeign": "USD20万",
     *     "focusAsset": "固收类产品",
     *     "estimateNeedBusiness": "有配置需求",
     *     "nextPlan": "下周再次沟通",
     *     "managerName": "张经理",
     *     "canEdit": true
     *   }
     * }
     */
    Response<VisitMinutesForFeedbackVO> getVisitMinutesForFeedback(QueryVisitMinutesForFeedbackRequest request);

    /**
     * @api {DUBBO} com.howbuy.crm.account.client.facade.commvisit.CmVisitMinutesFacade.saveCustFeedback() saveCustFeedback()
     * @apiVersion 1.0.0
     * @apiGroup CmVisitMinutesFacade
     * @apiName saveCustFeedback()
     * @apiDescription 保存客户反馈
     * @apiParam (请求参数) {SaveCustFeedbackRequest} request 修改陪访人/主管请求
     * @apiSuccess (响应结果) {Boolean} success 成功标识
     */
    Response<OperationResultVO> saveCustFeedback(SaveCustFeedbackRequest request);

    /**
     * @api {DUBBO} com.howbuy.crm.account.client.facade.commvisit.CmVisitMinutesFacade.searchUser() searchUser()
     * @apiVersion 1.0.0
     * @apiGroup CmVisitMinutesFacade
     * @apiName searchUser()
     * @apiDescription 用户搜索接口
     * 
     * @apiParam (请求参数) {String} keyword 搜索关键词
     * @apiParam (请求参数) {String} userType 用户类型
     * @apiParam (请求参数) {String} currentUserId 当前操作用户ID
     * 
     * @apiParamExample 请求参数示例
     * {
     *   "keyword": "张",
     *   "userType": "1",
     *   "currentUserId": "U001"
     * }
     * 
     * @apiSuccess (响应结果) {Array} userList 用户列表
     * @apiSuccess (响应结果) {String} userList.userId 用户ID
     * @apiSuccess (响应结果) {String} userList.userName 用户姓名
     * @apiSuccess (响应结果) {String} userList.userType 用户类型
     * @apiSuccess (响应结果) {String} userList.deptName 部门名称
     * 
     * @apiSuccessExample 响应结果示例
     * {
     *   "code": "0000",
     *   "description": "成功",
     *   "data": {
     *     "userList": [{
     *       "userId": "U001",
     *       "userName": "张三",
     *       "userType": "1",
     *       "deptName": "销售部"
     *     }]
     *   }
     * }
     */
    Response<SearchUserVO> searchUser(SearchUserRequest request);

     /**
     * @api {DUBBO} com.howbuy.crm.account.client.facade.commvisit.CmVisitMinutesFacade.pushNoFeedback() pushNoFeedback()
     * @apiVersion 1.0.0
     * @apiGroup CmVisitMinutesFacade
     * @apiName pushNoFeedback()
     * @apiDescription 推送未反馈提醒
     * 
     * @apiParam (请求参数) {String} curPreDayParam 当前日期前N天的日期参数(格式YYYYMMDD)
     * 
     * @apiParamExample 请求参数示例
     * "********"
     * 
     * @apiSuccess (响应结果) {Boolean} success 处理结果
     * @apiSuccess (响应结果) {String} message 处理消息
     * 
     * @apiSuccessExample 响应结果示例
     * {
     *   "code": "0000",
     *   "description": "成功",
     *   "data": {
     *     "success": true,
     *     "message": "推送成功"
     *   }
     * }
     */
    Response<Object> pushNoFeedback(String curPreDayParam);
} 