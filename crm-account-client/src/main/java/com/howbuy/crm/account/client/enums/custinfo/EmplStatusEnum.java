package com.howbuy.crm.account.client.enums.custinfo;

/**
 * @description:(就业状况: 01-雇主 02-全职 03-兼职 04-主妇 05-学生 06-退休 07-非在职)
 * @param
 * @return
 * @author: haoran.zhang
 * @date: 2023/12/19 13:43
 * @since JDK 1.8
 */
public enum EmplStatusEnum {
    /**
     * 雇主
     */
    EMPLOYER("01","雇主"),
    /**
     * 全职
     */
    FULL_TIME("02","全职"),
    /**
     * 兼职
     */
    PART_TIME("03","兼职"),
    /**
     * 主妇
     */
    HOUSEWIFE("04","主妇"),
    /**
     * 学生
     */
    STUDENT("05","学生"),
    /**
     * 退休
     */
    RETIRE("06","退休"),
    /**
     * 非在职
     */
    NON_EMPLOYED("07","非在职"),
    ;


    /**编码*/
    private String code;
    /**描述*/
    private String description;

    private EmplStatusEnum(String code, String description){
        this.code=code;
        this.description=description;
    }

    /**
     * 通过code获得
     * @param code	系统返回参数编码
     * @return description 描述
     */
    public static String getDescription(String code){
        for(EmplStatusEnum b: EmplStatusEnum.values()){
            if(b.getCode().equals(code)){
                return b.description;
            }
        }
        return null;
    }

    /**
     * 通过code直接返回 整个枚举类型
     * @param code 系统返回参数编码
     * @return BaseConstantEnum
     */
    public static EmplStatusEnum getEnum(String code){
        if(code != null && !"".equals(code)){
            for(EmplStatusEnum b: EmplStatusEnum.values()){
                if(b.getCode().equals(code)){
                    return b;
                }
            }
        }
        return null;
    }

    public String getCode() {
        return code;
    }


    public String getDescription() {
        return description;
    }
}
