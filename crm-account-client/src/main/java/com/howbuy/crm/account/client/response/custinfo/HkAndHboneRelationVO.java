/**
 * Copyright (c) 2023, <PERSON><PERSON><PERSON><PERSON> HOWBUY INVESTMENT MANAGEMENT Co., Ltd.
 * All right reserved.
 * <p>
 * THIS IS UNPUBLISHED PROPRIETARY SOURCE CODE OF HOWBUY INVESTMENT
 * MANAGEMENT CO., LTD.  THE CONTENTS OF THIS FILE MAY NOT BE DISCLOSED
 * TO THIRD PARTIES, COPIED OR DUPLICATED IN ANY FORM, IN WHOLE OR IN PART,
 * WITHOUT THE PRIOR WRITTEN PERMISSION OF HOWBUY INVESTMENT MANAGEMENT
 * CO., LTD.
 */
package com.howbuy.crm.account.client.response.custinfo;

import lombok.Data;

import java.io.Serializable;

/**
 * @description: (香港客户vs 一账通客户绑定关系 返回  )
 * <AUTHOR>
 * @date 2023/12/26 19:59
 * @since JDK 1.8
 */
@Data
public class HkAndHboneRelationVO implements Serializable {
    private static final long serialVersionUID = 1L;


    /**
     *一账通账号
     */
    private String hboneNo;


    /**
     * 香港客户号
     */
    private String hkTxAcctNo;

}