package com.howbuy.crm.account.client.request.commvisit;

import lombok.Getter;
import lombok.Setter;

import java.io.Serializable;

/**
 * @description: 保存陪访人/主管反馈请求
 * <AUTHOR>
 * @date 2025-04-08 10:26:00
 * @since JDK 1.8
 */
@Getter
@Setter
public class SaveFeedbackRequest implements Serializable {

    private static final long serialVersionUID = 1L;
    /**
     * 拜访纪要ID
     */
    private String visitMinutesId;
    
    /**
     * 陪访人ID,如果是陪访人反馈则必填
     */
    private String accompanyingId;
    
    /**
     * 反馈类型 1:陪访人反馈 2:主管反馈
     */
    private String feedbackType;
    
    /**
     * 本次陪访概要经验或教训
     */
    private String summary;
    
    /**
     * 该客户下阶段工作的建议
     */
    private String suggestion;
    
    /**
     * 当前用户ID
     */
    private String currentUserId;
}