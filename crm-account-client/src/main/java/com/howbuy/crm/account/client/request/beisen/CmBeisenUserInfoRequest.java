/**
 * Copyright (c) 2024, <PERSON><PERSON><PERSON><PERSON> HOWBUY INVESTMENT MANAGEMENT Co., Ltd.
 * All right reserved.
 * <p>
 * THIS IS UNPUBLISHED PROPRIETARY SOURCE CODE OF HOWBUY INVESTMENT
 * MANAGEMENT CO., LTD.  THE CONTENTS OF THIS FILE MAY NOT BE DISCLOSED
 * TO THIRD PARTIES, COPIED OR DUPLICATED IN ANY FORM, IN WHOLE OR IN PART,
 * WITHOUT THE PRIOR WRITTEN PERMISSION OF HOWBUY INVESTMENT MANAGEMENT
 * CO., LTD.
 */
package com.howbuy.crm.account.client.request.beisen;

import lombok.Data;

@Data
public class CmBeisenUserInfoRequest {
    /**
     * 工号
     */
    private String ext2;
    /**
     * 北森ID
     */
    private String ext3;
    /**
     * 姓名
     */
    private String ext4;
    /**
     * 工作地点
     */
    private String ext5;
    /**
     * 电子邮箱
     */
    private String ext6;
    /**
     * 所属中心
     */
    private String ext7;
    /**
     * 区域
     */
    private String ext8;
    /**
     * 分公司
     */
    private String ext9;
    /**
     * 性别
     */
    private String ext10;
    /**
     * 出生日期
     */
    private String ext11;
    /**
     * 学历
     */
    private String ext12;
    /**
     * 年龄
     */
    private String ext13;
    /**
     * 员工状态
     */
    private String ext14;
    /**
     * 当月职级
     */
    private String ext15;
    /**
     * 当月职级代码
     */
    private String ext16;
    /**
     * 当月基本工资
     */
    private String ext17;
    /**
     * 当月奖金/绩效
     */
    private String ext18;
    /**
     * 入职日期
     */
    private String ext19;
    /**
     * 入职职级
     */
    private String ext20;
    /**
     * 转正日期
     */
    private String ext21;
    /**
     * 转正职级
     */
    private String ext22;
    /**
     * 转正职级代码
     */
    private String ext23;
    /**
     * 离职日期
     */
    private String ext24;
    /**
     * 离职状态
     */
    private String ext25;
    /**
     * 基金从业资格编码
     */
    private String ext26;
    /**
     * 上家公司
     */
    private String ext27;
    /**
     * 背景来源
     */
    private String ext28;
    /**
     * 上家工作月份
     */
    private String ext29;
    /**
     * 招聘经理
     */
    private String ext30;
    /**
     * 推荐人
     */
    private String ext31;
    /**
     * 推荐人工号
     */
    private String ext32;
    /**
     * 推荐人（文本）
     */
    private String ext33;
    /**
     * 招聘渠道
     */
    private String ext34;
    /**
     * 业务修改时间
     */
    private String ext35;
}