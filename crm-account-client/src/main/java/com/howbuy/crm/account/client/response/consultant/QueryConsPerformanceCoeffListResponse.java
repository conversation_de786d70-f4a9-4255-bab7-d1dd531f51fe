package com.howbuy.crm.account.client.response.consultant;

import lombok.Getter;
import lombok.Setter;

import java.io.Serializable;
import java.util.List;

/**
 * @description: 查询人员绩效系数列表分页响应对象
 * <AUTHOR>
 * @date 2025-07-11 16:00:00
 * @since JDK 1.8
 */
@Getter
@Setter
public class QueryConsPerformanceCoeffListResponse implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 总记录数
     */
    private Long total;

    /**
     * 当前页数据列表
     */
    private List<QueryConsPerformanceCoeffListVO> list;
}