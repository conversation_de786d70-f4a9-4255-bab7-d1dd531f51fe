/**
 * Copyright (c) 2025, <PERSON><PERSON><PERSON><PERSON> HOWBUY INVESTMENT MANAGEMENT Co., Ltd.
 * All right reserved.
 * <p>
 * THIS IS UNPUBLISHED PROPRIETARY SOURCE CODE OF HOWBUY INVESTMENT
 * MANAGEMENT CO., LTD.  THE CONTENTS OF THIS FILE MAY NOT BE DISCLOSED
 * TO THIRD PARTIES, COPIED OR DUPLICATED IN ANY FORM, IN WHOLE OR IN PART,
 * WITHOUT THE PRIOR WRITTEN PERMISSION OF HOWBUY INVESTMENT MANAGEMENT
 * CO., LTD.
 */
package com.howbuy.crm.account.client.enums.consultantexp;

/**
 * <AUTHOR>
 * @description: 花名册 [层级]枚举
 * @date 2025/4/10 16:33
 * @since JDK 1.8
 */
public enum UserLevelEnum {

    FINANCIAL_PLANNER("1", "理财师", 1),

    TEAM_DIRECTOR("2", "团队总监", 2),
    DIVISION_MANAGER("3", "分总", 3),
    REGIONAL_EXECUTIVE_VICE_PRESIDENT("4", "区域执行副总", 4),
    REGIONAL_PRESIDENT("5", "区域总", 5),
    SALES_DIRECTOR("6", "销售总监", 6),
    MIDDLE_BACK_OFFICE("7", "中后台", 7),
    OTHERS("8", "其他", 8),
    IN_TRANSIT("9", "在途", 9),
    RESIGNED("10", "离职", 10);

    private String constCode;

    private String constDesc;

    private Integer constLevel;


    UserLevelEnum(String constCode, String constDesc, Integer constLevel) {
        this.constCode = constCode;
        this.constDesc = constDesc;
        this.constLevel = constLevel;
    }

    public String getConstCode() {
        return constCode;
    }

    public String getConstDesc() {
        return constDesc;
    }

    public Integer getConstLevel() {
        return constLevel;
    }

    public static UserLevelEnum getEnum(String constCode) {
        for (UserLevelEnum userLevelEnum : UserLevelEnum.values()) {
            if (userLevelEnum.getConstCode().equals(constCode)) {
                return userLevelEnum;
            }
        }
        return null;
    }

}
