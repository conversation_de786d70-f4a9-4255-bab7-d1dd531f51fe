/**
 * Copyright (c) 2024, <PERSON><PERSON><PERSON><PERSON> HOWBUY INVESTMENT MANAGEMENT Co., Ltd.
 * All right reserved.
 * <p>
 * THIS IS UNPUBLISHED PROPRIETARY SOURCE CODE OF HOWBUY INVESTMENT
 * MANAGEMENT CO., LTD.  THE CONTENTS OF THIS FILE MAY NOT BE DISCLOSED
 * TO THIRD PARTIES, COPIED OR DUPLICATED IN ANY FORM, IN WHOLE OR IN PART,
 * WITHOUT THE PRIOR WRITTEN PERMISSION OF HOWBUY INVESTMENT MANAGEMENT
 * CO., LTD.
 */
package com.howbuy.crm.account.client.request.deposit;

import com.howbuy.crm.account.client.request.PageRequest;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * @description: 香港入金请求参数VO类
 * <AUTHOR>
 * @date 2024/1/15 10:02
 * @since JDK 1.8
 */

@Data
@EqualsAndHashCode(callSuper = true)
public class HkDepositRequest extends PageRequest {
    private String hkCustNo;
    private String depositType;

}