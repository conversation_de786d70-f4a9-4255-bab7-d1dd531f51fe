/**
 * Copyright (c) 2023, <PERSON><PERSON><PERSON><PERSON> HOWBUY INVESTMENT MANAGEMENT Co., Ltd.
 * All right reserved.
 * <p>
 * THIS IS UNPUBLISHED PROPRIETARY SOURCE CODE OF HOWBUY INVESTMENT
 * MANAGEMENT CO., LTD.  THE CONTENTS OF THIS FILE MAY NOT BE DISCLOSED
 * TO THIRD PARTIES, COPIED OR DUPLICATED IN ANY FORM, IN WHOLE OR IN PART,
 * WITHOUT THE PRIOR WRITTEN PERMISSION OF HOWBUY INVESTMENT MANAGEMENT
 * CO., LTD.
 */
package com.howbuy.crm.account.client.request.custinfo;

import lombok.Data;

import java.math.BigDecimal;

/**
 * @description: 投顾客户收货地址信息 请求类
 * <AUTHOR>
 * @date 2024/4/3 14:15
 * @since JDK 1.8
 */
@Data
public class ConsCustDeliveryAddressRequest {

    /**
     * 主键
     */
    private BigDecimal id;

    /**
     * 投顾客户编号
     */
    private String custNo;

    /**
     * 收货人姓名
     */
    private String deliveryName;

    /**
     * 收货人电话 地区码
     */
    private String deliveryMobileAreaCode;

    /**
     * 收货人电话
     */
    private String deliveryMobile;

    /**
     * 收货国家/地区
     */
    private String deliveryNationCode;

    /**
     * 收货省
     */
    private String deliveryProvCode;

    /**
     * 收货市
     */
    private String deliveryCityCode;
    /**
     * 收货县
     */
    private String deliveryCountyCode;

    /**
     * 收货详细地址
     */
    private String deliveryAddr;

    /**
     * 备注
     */
    private String deliveryRemark;

    /**
     * 操作人
     */
    private String operator;

}