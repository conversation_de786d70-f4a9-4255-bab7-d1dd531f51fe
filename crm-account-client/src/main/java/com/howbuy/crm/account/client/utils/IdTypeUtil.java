/**
 * Copyright (c) 2023, <PERSON><PERSON><PERSON><PERSON> HOWBUY INVESTMENT MANAGEMENT Co., Ltd.
 * All right reserved.
 * <p>
 * THIS IS UNPUBLISHED PROPRIETARY SOURCE CODE OF HOWBUY INVESTMENT
 * MANAGEMENT CO., LTD.  THE CONTENTS OF THIS FILE MAY NOT BE DISCLOSED
 * TO THIRD PARTIES, COPIED OR DUPLICATED IN ANY FORM, IN WHOLE OR IN PART,
 * WITHOUT THE PRIOR WRITTEN PERMISSION OF HOWBUY INVESTMENT MANAGEMENT
 * CO., LTD.
 */
package com.howbuy.crm.account.client.utils;

import com.google.common.collect.Lists;
import com.howbuy.crm.account.client.enums.custinfo.ConsCustInvestTypeEnum;
import com.howbuy.crm.account.client.enums.custinfo.IdTypeEnum;
import com.howbuy.crm.account.client.enums.custinfo.InsIdTypeEnum;

import java.util.List;

/**
 * @description: (证件信息  公共工具类 )
 * <AUTHOR>
 * @date 2023/12/14 18:38
 * @since JDK 1.8
 */
public class IdTypeUtil {



    /**
     * 当前的证件类型=香港身份证/澳门身份证/台湾身份证
     * 背景： crm个人证件类型中，香港身份证/澳门身份证/台湾身份证。在账户中心中 不存在。
     */
    public static final List<String> specialPersonalIdTypeList = Lists.newArrayList(
            IdTypeEnum.HK_ID.getCode(),
            IdTypeEnum.MACAO_ID.getCode(),
            IdTypeEnum.TAIWAN_ID.getCode());

    /**
     * @description:(根据 客户类型，确定证件类型描述)
     * @param investType
     * @param idType
     * @return java.lang.String
     * @author: haoran.zhang
     * @date: 2024/1/17 10:04
     * @since JDK 1.8
     */
    public static String getIdTypeDesc(String investType,String idType){
        if(ConsCustInvestTypeEnum.PERSONAL.getCode().equals(investType)){
            return IdTypeEnum.getDescription(idType);
        }else{
            return InsIdTypeEnum.getDescription(idType);
        }
    }



    /**
     * @description:(与 一账通交互时，根据 客户类型，确定证件类型 转译 一账通的证件类型)
     * @param investType	
     * @param idType
     * @return java.lang.String
     * @author: haoran.zhang
     * @date: 2024/1/22 15:24
     * @since JDK 1.8
     */
    public static String getAcctUsedIdType(String investType,String idType){
        if(ConsCustInvestTypeEnum.PERSONAL.getCode().equals(investType)
                &&  specialPersonalIdTypeList.contains(idType)){
            return IdTypeEnum.OTHER.getCode();
        }
        return idType;
    }






}