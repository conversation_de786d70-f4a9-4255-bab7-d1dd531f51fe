/**
 * Copyright (c) 2024, <PERSON><PERSON><PERSON><PERSON> HOWBUY INVESTMENT MANAGEMENT Co., Ltd.
 * All right reserved.
 * <p>
 * THIS IS UNPUBLISHED PROPRIETARY SOURCE CODE OF HOWBUY INVESTMENT
 * MANAGEMENT CO., LTD.  THE CONTENTS OF THIS FILE MAY NOT BE DISCLOSED
 * TO THIRD PARTIES, COPIED OR DUPLICATED IN ANY FORM, IN WHOLE OR IN PART,
 * WITHOUT THE PRIOR WRITTEN PERMISSION OF HOWBUY INVESTMENT MANAGEMENT
 * CO., LTD.
 */
package com.howbuy.crm.account.client.request.custinfo;

import com.howbuy.crm.account.client.request.Request;
import lombok.EqualsAndHashCode;

import java.io.Serializable;

/**
 * @description: 查询 指定投顾客户 是否为[公司内部员工]
 * <AUTHOR>
 * @date 2024/11/1 18:10
 * @since JDK 1.8
 */

@EqualsAndHashCode(callSuper = true)
public class QueryCustIsInternalEmployeeRequest extends Request implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 投顾客户号
     */
    private String consCustNo;

    public String getConsCustNo() {
        return consCustNo;
    }

    public void setConsCustNo(String consCustNo) {
        this.consCustNo = consCustNo;
    }
}