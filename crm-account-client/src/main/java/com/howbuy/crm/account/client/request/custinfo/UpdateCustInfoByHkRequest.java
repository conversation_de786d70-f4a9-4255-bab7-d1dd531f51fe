/**
 * Copyright (c) 2024, <PERSON><PERSON><PERSON><PERSON> HOWBUY INVESTMENT MANAGEMENT Co., Ltd.
 * All right reserved.
 * <p>
 * THIS IS UNPUBLISHED PROPRIETARY SOURCE CODE OF HOWBUY INVESTMENT
 * MANAGEMENT CO., LTD.  THE CONTENTS OF THIS FILE MAY NOT BE DISCLOSED
 * TO THIRD PARTIES, COPIED OR DUPLICATED IN ANY FORM, IN WHOLE OR IN PART,
 * WITHOUT THE PRIOR WRITTEN PERMISSION OF HOWBUY INVESTMENT MANAGEMENT
 * CO., LTD.
 */
package com.howbuy.crm.account.client.request.custinfo;

import java.io.Serializable;

/**
 * @description: 根据 香港客户信息 更新投顾客户信息
 * <AUTHOR>
 * @date 2024/12/12 17:49
 * @since JDK 1.8
 */
public class UpdateCustInfoByHkRequest implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 香港客户号
     */
    private String hkCustNo;

    /**
     * 操作人员
     */
    private String operator;

    /**
     * 客户号
     */
    private String custNo;

    public String getHkCustNo() {
        return hkCustNo;
    }

    public void setHkCustNo(String hkCustNo) {
        this.hkCustNo = hkCustNo;
    }

    public String getOperator() {
        return operator;
    }

    public void setOperator(String operator) {
        this.operator = operator;
    }

    public String getCustNo() {
        return custNo;
    }

    public void setCustNo(String custNo) {
        this.custNo = custNo;
    }
}