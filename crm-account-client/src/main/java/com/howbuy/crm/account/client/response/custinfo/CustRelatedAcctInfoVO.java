/**
 * Copyright (c) 2023, <PERSON><PERSON><PERSON><PERSON> HOWBUY INVESTMENT MANAGEMENT Co., Ltd.
 * All right reserved.
 * <p>
 * THIS IS UNPUBLISHED PROPRIETARY SOURCE CODE OF HOWBUY INVESTMENT
 * MANAGEMENT CO., LTD.  THE CONTENTS OF THIS FILE MAY NOT BE DISCLOSED
 * TO THIRD PARTIES, COPIED OR DUPLICATED IN ANY FORM, IN WHOLE OR IN PART,
 * WITHOUT THE PRIOR WRITTEN PERMISSION OF HOWBUY INVESTMENT MANAGEMENT
 * CO., LTD.
 */
package com.howbuy.crm.account.client.response.custinfo;

import lombok.Data;

import java.io.Serializable;

/**
 * @description: (crm客户 相关账户信息  )
 * <AUTHOR>
 * @date 2023/12/26 19:59
 * @since JDK 1.8
 */
@Data
public class CustRelatedAcctInfoVO implements Serializable {
    private static final long serialVersionUID = 1L;

    /**
     * CRM客户号
     */
    private String  custNo;

    /**
     * [CRM关联的] 一账通号
     */
    private String hboneNo;

    /**
     * [香港账户中心关联的] 一账通号
     */
    private String hkHboneNo;


    /**
     * 公募交易账号
     */
    private String txAcctNo;

    /**
     * [一账通关联的] 香港交易账号
     */
    private String hboneHkTxAcctNo;

    /**
     * [CRM关联的] 香港交易账号
     */
    private String crmHkTxAcctNo;


    /**
     * 比较 [crm关联的] 香港交易账号 和 [一账通关联的] 香港交易账号 是否一致
     * @return
     */
    public boolean compareHkAccountSame(){
        //比较两边值 是否一致
        return  compareSame(hboneHkTxAcctNo,crmHkTxAcctNo);
    }

    /**
     * 比较 [crm关联的] 一账通号 和 [香港账户中心] 关联的 一账通号 是否一致
     * @return
     */
    public boolean compareHboneAccountSame(){
        //比较两边值 是否一致
        return  compareSame(hboneNo,hkHboneNo);
    }

    /**
     * 比较 两侧账户是否一致
     * @param side1
     * @param side2
     * @return
     */
    private boolean compareSame(String side1,String side2){
        //两边都为空，按一致处理. 所以此处可以删除
//		if(StringUtils.isBlank(side1) && StringUtils.isBlank(side2)){
//			return true;
//		}
        //比较两边值 是否一致
        return  (side1==null?"":side1).equals(side2==null?"":side2);
    }

}