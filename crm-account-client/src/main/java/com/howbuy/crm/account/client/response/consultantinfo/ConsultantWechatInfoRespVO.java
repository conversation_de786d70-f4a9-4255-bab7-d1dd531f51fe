/**
 * Copyright (c) 2023, <PERSON><PERSON><PERSON><PERSON> HOWBUY INVESTMENT MANAGEMENT Co., Ltd.
 * All right reserved.
 * <p>
 * THIS IS UNPUBLISHED PROPRIETARY SOURCE CODE OF HOWBUY INVESTMENT
 * MANAGEMENT CO., LTD.  THE CONTENTS OF THIS FILE MAY NOT BE DISCLOSED
 * TO THIRD PARTIES, COPIED OR DUPLICATED IN ANY FORM, IN WHOLE OR IN PART,
 * WITHOUT THE PRIOR WRITTEN PERMISSION OF HOWBUY INVESTMENT MANAGEMENT
 * CO., LTD.
 */
package com.howbuy.crm.account.client.response.consultantinfo;

import com.howbuy.crm.account.client.response.custinfo.RepeatCustInfo;
import lombok.Data;

import java.io.Serializable;
import java.util.ArrayList;
import java.util.List;

/**
 * @description: 投顾信息 返回信息 类 )
 * <AUTHOR>
 * @date ********
 * @since JDK 1.8
 */
@Data
public class ConsultantWechatInfoRespVO implements Serializable {

    private static final long serialVersionUID = 1L;
    /**
     * 投顾信息
     */
    private List<CmConsultantInfo> consultantWechatInfoList =new ArrayList<>();

}