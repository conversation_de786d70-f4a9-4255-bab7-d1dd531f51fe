package com.howbuy.crm.account.client.response.commvisit;

import lombok.Getter;
import lombok.Setter;

import java.io.Serializable;
import java.util.List;

/**
 * @description: 客户沟通记录列表响应
 * <AUTHOR>
 * @date 2025-04-08 10:26:00
 * @since JDK 1.8
 */
@Getter
@Setter
public class CommunicateListVO implements Serializable {

    private static final long serialVersionUID = 1L;
    /**
     * 总记录数
     */
    private Long total;
    
    /**
     * 沟通记录列表
     */
    private List<CommunicateRecordVO> list;

    @Getter
    @Setter
    public static class CommunicateRecordVO implements Serializable {

        private static final long serialVersionUID = 1L;
        /**
         * 主键ID
         */
        private String pkId;

        /**
         * 拜访时间
         */
        private String visitTime;

        /**
         * 修改标志
         */
        private String modifyFlag;

        /**
         * 历史标志
         */
        private String hisFlag;

        /**
         * 历史ID
         */
        private String hisId;

        /**
         * 拜访类型
         */
        private String visitType;

        /**
         * 咨询类型
         */
        private String consultType;

        /**
         * 来源地址
         */
        private String sourceAddr;

        /**
         * 创建人代码
         */
        private String creator;

        /**
         * 投顾姓名
         */
        private String consName;

        /**
         * 订单ID
         */
        private String orderId;

        /**
         * 呼入ID
         */
        private String callInId;

        /**
         * 拜访总结
         */
        private String visitSummary;

        /**
         * 拜访日期
         */
        private String visitDt; 

        /**
         * 拜访目的
         */
        private String visitPurpose;
        
        /**
         * 拜访纪要ID
         */
        private String visitMinutesId;
    }
}
