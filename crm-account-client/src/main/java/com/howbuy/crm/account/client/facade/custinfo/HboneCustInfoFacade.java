/**
 * Copyright (c) 2025, <PERSON><PERSON><PERSON><PERSON> HOWBUY INVESTMENT MANAGEMENT Co., Ltd.
 * All right reserved.
 * <p>
 * THIS IS UNPUBLISHED PROPRIETARY SOURCE CODE OF HOWBUY INVESTMENT
 * MANAGEMENT CO., LTD.  THE CONTENTS OF THIS FILE MAY NOT BE DISCLOSED
 * TO THIRD PARTIES, COPIED OR DUPLICATED IN ANY FORM, IN WHOLE OR IN PART,
 * WITHOUT THE PRIOR WRITTEN PERMISSION OF HOWBUY INVESTMENT MANAGEMENT
 * CO., LTD.
 */
package com.howbuy.crm.account.client.facade.custinfo;

import com.howbuy.crm.account.client.response.Response;
import com.howbuy.crm.account.client.response.custinfo.HboneAcctCustDetailInfoVO;
import com.howbuy.crm.account.client.response.custinfo.HboneAcctCustSensitiveInfoVO;

/**
 * @description: (请在此添加描述)
 * <AUTHOR>
 * @date 2025/7/29 11:10
 * @since JDK 1.8
 */
public interface HboneCustInfoFacade {


    /**
     * @api {DUBBO}  com.howbuy.crm.account.client.facade.custinfo.HboneCustInfoFacade.queryHboneCustDetailInfo()
     * @apiVersion 1.0.0
     * @apiGroup HboneCustInfoFacade
     * @apiName queryHboneCustDetailInfo()
     * @apiDescription 根据一账通号查询客户详细信息
     * @apiParam (请求参数) {String} hboneNo
     * @apiParamExample 请求参数示例
     * hboneNo=TYwEE1OHSy
     * @apiSuccess (响应结果) {String} code 状态码
     * @apiSuccess (响应结果) {String} description 描述信息
     * @apiSuccess (响应结果) {Object} data 数据封装
     * @apiSuccess (响应结果) {String} data.idSignAreaCode 证件地区码-身份证签发地区编码
     * @apiSuccess (响应结果) {String} data.mobileAreaCode 手机号码区号
     * @apiSuccess (响应结果) {String} data.hboneNo 一账通号
     * @apiSuccess (响应结果) {String} data.username 客户姓名
     * @apiSuccess (响应结果) {String} data.mobileVerifyStatus 手机号码验证状态
     * @apiSuccess (响应结果) {String} data.authType
     * @apiSuccess (响应结果) {Boolean} data.importHbvip
     * @apiSuccess (响应结果) {String} data.hboneRegOutletCode
     * @apiSuccess (响应结果) {Number} data.hboneRegDate
     * @apiSuccess (响应结果) {String} data.authOutletCode
     * @apiSuccess (响应结果) {Number} data.authDate
     * @apiSuccess (响应结果) {String} data.regOutletCode
     * @apiSuccess (响应结果) {String} data.regTradeChan
     * @apiSuccess (响应结果) {String} data.regDisCode
     * @apiSuccess (响应结果) {String} data.regDt
     * @apiSuccess (响应结果) {String} data.custStat
     * @apiSuccess (响应结果) {String} data.vipFlag
     * @apiSuccess (响应结果) {String} data.invstType
     * @apiSuccess (响应结果) {String} data.custName
     * @apiSuccess (响应结果) {String} data.idType
     * @apiSuccess (响应结果) {String} data.idValidityStart
     * @apiSuccess (响应结果) {String} data.idValidityEnd
     * @apiSuccess (响应结果) {String} data.idAlwaysValidFlag
     * @apiSuccess (响应结果) {String} data.idVrfyStat
     * @apiSuccess (响应结果) {String} data.birthday
     * @apiSuccess (响应结果) {String} data.gender
     * @apiSuccess (响应结果) {String} data.canDt
     * @apiSuccess (响应结果) {String} data.idNoDigest
     * @apiSuccess (响应结果) {String} data.idNoMask
     * @apiSuccess (响应结果) {String} data.mobileDigest
     * @apiSuccess (响应结果) {String} data.mobileMask
     * @apiSuccess (响应结果) {String} data.authCardDigest
     * @apiSuccess (响应结果) {String} data.authCardMask
     * @apiSuccess (响应结果) {String} data.secondHboneRegOutletCode
     * @apiSuccess (响应结果) {String} data.secondRegOutletCode
     * @apiSuccess (响应结果) {String} data.minorFlag
     * @apiSuccess (响应结果) {String} data.minorId
     * @apiSuccess (响应结果) {String} data.eduLevel
     * @apiSuccess (响应结果) {String} data.vocation
     * @apiSuccess (响应结果) {String} data.incLevel
     * @apiSuccess (响应结果) {String} data.nationality
     * @apiSuccess (响应结果) {String} data.marriageStat
     * @apiSuccess (响应结果) {Boolean} data.esignatureConfirmation
     * @apiSuccess (响应结果) {String} data.esignatureConfirmedDate
     * @apiSuccess (响应结果) {String} data.custImeiDigest
     * @apiSuccess (响应结果) {String} data.custImeiMask
     * @apiSuccess (响应结果) {String} data.ipAddress
     * @apiSuccess (响应结果) {String} data.macAddress
     * @apiSuccess (响应结果) {String} data.dataAuthAgreementSign
     * @apiSuccess (响应结果) {String} data.dataAuthAgreementSignDate
     * @apiSuccess (响应结果) {String} data.hkAssetIsolateFlag 海外资产隔离标志 0-否 1-是
     * @apiSuccess (响应结果) {String} data.hkAssetIsolateFlagUpDt 海外资产隔离标志更新时间 yyyyMMddHHmmss
     * @apiSuccessExample 响应结果示例
     * {"code":"3YkSE0mSQE","data":{"dataAuthAgreementSignDate":"SLL","authCardDigest":"epq","hkAssetIsolateFlagUpDt":"TFmAC2EtZ","regTradeChan":"S","secondHboneRegOutletCode":"sDo","idValidityEnd":"kEJYxJzWlN","hboneNo":"OyAMNr1","canDt":"zxT","authDate":*************,"idType":"HE","hboneRegDate":2289723996576,"hkAssetIsolateFlag":"bt6GZ","eduLevel":"kUyu0TRdwt","regOutletCode":"F7tx","authCardMask":"rTTNWsxMXs","ipAddress":"mG2x4E5v","authOutletCode":"zh","custImeiDigest":"s0ecjR","idSignAreaCode":"R3ynwQB","regDt":"7","macAddress":"gXoYF","nationality":"8","marriageStat":"xkvMNqIKPk","vipFlag":"YUh41ftW","mobileMask":"hND7","birthday":"e8","secondRegOutletCode":"mEVX","gender":"n8dm","minorId":"MS","mobileVerifyStatus":"z9r","idValidityStart":"syp3","mobileAreaCode":"1iX6","incLevel":"t0X7FLjJv","custStat":"qd","invstType":"xBW484Jfl","mobileDigest":"yEnkp","hboneRegOutletCode":"SAsQ","idVrfyStat":"iPFPkGH","authType":"K7LN","idAlwaysValidFlag":"Kxr","idNoDigest":"7v","custName":"uXd","idNoMask":"H","vocation":"DWMS","regDisCode":"zEwwCgpJUt","dataAuthAgreementSign":"SJp","esignatureConfirmation":false,"importHbvip":true,"minorFlag":"tRB","esignatureConfirmedDate":"pDdC0","custImeiMask":"Qk3wNX","username":"E"},"description":"eFzAk8wNx"}
     */
    Response<HboneAcctCustDetailInfoVO> queryHboneCustDetailInfo(String hboneNo);


    /**
     * @api {DUBBO}  com.howbuy.crm.account.client.facade.custinfo.HboneCustInfoFacade.queryHboneCustSensitiveInfo()
     * @apiVersion 1.0.0
     * @apiGroup HboneCustInfoFacade
     * @apiName queryHboneCustSensitiveInfo()
     * @apiDescription 查询 一账通客户 敏感信息
     * @apiParam (请求参数) {String} hboneNo
     * @apiParamExample 请求参数示例
     * hboneNo=f
     * @apiSuccess (响应结果) {String} code 状态码
     * @apiSuccess (响应结果) {String} description 描述信息
     * @apiSuccess (响应结果) {Object} data 数据封装
     * @apiSuccess (响应结果) {String} data.hboneNo 一账通号
     * @apiSuccess (响应结果) {String} data.custNo 账户中心 客户号
     * @apiSuccess (响应结果) {String} data.custName 没名次
     * @apiSuccess (响应结果) {String} data.idType 证件类型
     * @apiSuccess (响应结果) {String} data.idNo 证件号码
     * @apiSuccess (响应结果) {String} data.corpIdNo 公司idNo
     * @apiSuccess (响应结果) {String} data.corporation
     * @apiSuccess (响应结果) {String} data.mobileMask
     * @apiSuccess (响应结果) {String} data.custIMEI
     * @apiSuccessExample 响应结果示例
     * {"code":"LPqWF8Hfxe","data":{"custNo":"3BoKkR","idType":"VG1","custIMEI":"6R","corporation":"XUK1v","corpIdNo":"PK","custName":"S","idNo":"5FRvFv0IRC","hboneNo":"gZCnCJv","mobileMask":"9v5F"},"description":"pFz5e7f6c"}
     */
    Response<HboneAcctCustSensitiveInfoVO> queryHboneCustSensitiveInfo(String hboneNo);
}