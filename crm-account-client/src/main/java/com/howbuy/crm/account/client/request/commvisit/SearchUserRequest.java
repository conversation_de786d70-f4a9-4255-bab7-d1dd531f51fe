package com.howbuy.crm.account.client.request.commvisit;

import lombok.Getter;
import lombok.Setter;

import java.io.Serializable;

/**
 * @description: 用户搜索请求
 * @author: jianjian.yang
 * @date: 2025-04-09 16:04:00
 */
@Getter
@Setter
public class SearchUserRequest implements Serializable {

    private static final long serialVersionUID = 1L;
    /**
     * 搜索参数
     */
    private String searchParam;
    
    /**
     * 搜索类型 1:项目经理 2:所有正常用户
     */
    private String searchType;
    /**
     * 当前用户id
     */
    private String currentUserId;
} 