package com.howbuy.crm.account.client.response.custinfo;


import lombok.Data;

import java.io.Serializable;

/**
 * 客户kyc信息 对象
 */
@Data
public class HboneCustKycInfoVO implements Serializable {

    private static final long serialVersionUID = -8239368466525813935L ;

    /**
     * 分销 {@link com.howbuy.crm.account.client.enums.DisCodeEnum }
     */
    private String disCode;


    /**
     * 一账通账号
     */
    private String hboneNo;

    /**
     * kyc风险平常类型
     */
    private String riskToleranceExamType;

    /**
     *是否 合格投资人认定（私募） .逻辑[账户中心接口： signFlag=1]
     */
    private boolean signFlag;

    /**
     * 风险测评是否过期
     */
    private Boolean riskToleranceExpire;

    /**
     * kyc风险等级
     */
    private String riskToleranceLevel;


    /**
     *
     */
    private String investorType;


    /**
     * 是否入会 逻辑：
     * 做过问卷，并且问卷类型非零售的 且 已签署合格投资人认定（私募）
     */
    private boolean isJoinClub;

}
