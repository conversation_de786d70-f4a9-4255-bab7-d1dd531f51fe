package com.howbuy.crm.account.client.response.commvisit;

import lombok.Getter;
import lombok.Setter;

import java.io.Serializable;

/**
 * @description: 陪访人VO
 * <AUTHOR>
 * @date 2025-04-08 10:26:00
 * @since JDK 1.8
 */
@Getter
@Setter
public class AccompanyingVO implements Serializable {

    private static final long serialVersionUID = 1L;
    /**
     * 陪访人姓名
     */
    private String userName;
    
    /**
     * 主管姓名
     */
    private String managerName;
    
    /**
     * 本次陪访概要经验或教训
     */
    private String summary;
    
    /**
     * 该客户下阶段工作的建议
     */
    private String suggestion;
} 