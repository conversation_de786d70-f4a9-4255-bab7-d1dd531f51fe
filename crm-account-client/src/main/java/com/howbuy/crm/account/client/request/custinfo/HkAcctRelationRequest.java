/**
 * Copyright (c) 2023, <PERSON><PERSON><PERSON><PERSON> HOWBUY INVESTMENT MANAGEMENT Co., Ltd.
 * All right reserved.
 * <p>
 * THIS IS UNPUBLISHED PROPRIETARY SOURCE CODE OF HOWBUY INVESTMENT
 * MANAGEMENT CO., LTD.  THE CONTENTS OF THIS FILE MAY NOT BE DISCLOSED
 * TO THIRD PARTIES, COPIED OR DUPLICATED IN ANY FORM, IN WHOLE OR IN PART,
 * WITHOUT THE PRIOR WRITTEN PERMISSION OF HOWBUY INVESTMENT MANAGEMENT
 * CO., LTD.
 */
package com.howbuy.crm.account.client.request.custinfo;

import com.howbuy.crm.account.client.request.Request;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.io.Serializable;
import java.math.BigDecimal;

/**
 * @description: (香港客户vs 投顾客户 关联关系 查询 request)
 * <AUTHOR>
 * @date 2023/12/15 9:54
 * @since JDK 1.8
 */
@Data
@EqualsAndHashCode(callSuper = true)
public class HkAcctRelationRequest extends Request implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 主键
     */
    private BigDecimal id;

    /**
     * 投顾客户号
     */
    private String custNo;

    /**
     * 客户香港id (ebrokerID)
     */
    private String ebrokerId;


    /**
     * 香港交易账号
     */
    private String hkTxAcctNo;

}