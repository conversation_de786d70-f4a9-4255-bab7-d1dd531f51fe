/**
 * Copyright (c) 2024, <PERSON><PERSON><PERSON><PERSON> HOWBUY INVESTMENT MANAGEMENT Co., Ltd.
 * All right reserved.
 * <p>
 * THIS IS UNPUBLISHED PROPRIETARY SOURCE CODE OF HOWBUY INVESTMENT
 * MANAGEMENT CO., LTD.  THE CONTENTS OF THIS FILE MAY NOT BE DISCLOSED
 * TO THIRD PARTIES, COPIED OR DUPLICATED IN ANY FORM, IN WHOLE OR IN PART,
 * WITHOUT THE PRIOR WRITTEN PERMISSION OF HOWBUY INVESTMENT MANAGEMENT
 * CO., LTD.
 */
package com.howbuy.crm.account.client.response;

import lombok.Data;

import java.io.Serializable;

/**
 * @description: (文件导出的 通用vo)
 * <AUTHOR>
 * @date 2024/10/29 17:20
 * @since JDK 1.8
 */
@Data
public class ExportToFileVO implements Serializable {

    private static final long serialVersionUID = 1L;
    /**
     * 文件字节数组的base64编码串
     */
    private String fileByte;
    /**
     * 文件名
     */
    private String name;
    /**
     * 文件类型
     */
    private String type;
    /**
     * 错误数据
     */
    private String errorMsg;
}