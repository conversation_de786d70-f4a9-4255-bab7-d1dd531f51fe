/**
 * Copyright (c) 2024, <PERSON><PERSON><PERSON><PERSON> HOWBUY INVESTMENT MANAGEMENT Co., Ltd.
 * All right reserved.
 * <p>
 * THIS IS UNPUBLISHED PROPRIETARY SOURCE CODE OF HOWBUY INVESTMENT
 * MANAGEMENT CO., LTD.  THE CONTENTS OF THIS FILE MAY NOT BE DISCLOSED
 * TO THIRD PARTIES, COPIED OR DUPLICATED IN ANY FORM, IN WHOLE OR IN PART,
 * WITHOUT THE PRIOR WRITTEN PERMISSION OF HOWBUY INVESTMENT MANAGEMENT
 * CO., LTD.
 */
package com.howbuy.crm.account.client.facade.consultant;

import com.howbuy.crm.account.client.request.consultant.MergeConsPerformanceCoeffRequest;
import com.howbuy.crm.account.client.response.Response;

/**
 * @description: 写入人员绩效系数表接口（新增客户和分配投顾）
 * <AUTHOR>
 * @date 2025-06-27 17:05:00
 * @since JDK 1.8
 */
public interface MergeConsPerformanceCoeffFacade {

    /**
     * @api {DUBBO} com.howbuy.crm.account.client.facade.consultant.MergeConsPerformanceCoeffFacade.execute() execute()
     * @apiVersion 1.0.0
     * @apiGroup MergeConsPerformanceCoeffFacade
     * @apiName execute()
     * @apiDescription 写入人员绩效系数表接口（新增客户和分配投顾）
     * @apiParam (请求体) {String} custType 客户类型 1：创建客户，2：分配客户
     * @apiParam (请求体) {Date} assignTime 分配时间
     * @apiParam (请求体) {String} consCustNo 投顾客户号
     * @apiParam (请求体) {String} hboneNo 一账通号 是否计提公募使用
     * @apiParam (请求体) {String} consCode 投顾code
     * @apiParam (请求体) {String} sourceType 来源类型 有值代表人工修改
     * @apiParam (请求体) {String} custConversionCoeff 客户折算系数 有值代表人工修改
     * @apiParam (请求体) {BigDecimal} manageCoeffSubtotal 管理系数-分总 有值代表人工修改
     * @apiParam (请求体) {BigDecimal} manageCoeffRegionalsubtotal 管理系数-区副 有值代表人工修改
     * @apiParam (请求体) {BigDecimal} manageCoeffRegionaltotal 管理系数-区总 有值代表人工修改
     * @apiParam (请求体) {String} modifier 修改人
     * @apiParamExample 请求体示例
     * {"custType":"1","assignTime":"2024-12-19","consCustNo":"123456","hboneNo":"HB001","consCode":"CONS001","sourceType":"公司资源","custConversionCoeff":"1.0","manageCoeffSubtotal":0.8,"manageCoeffRegionalsubtotal":0.6,"manageCoeffRegionaltotal":0.4,"modifier":"admin"}
     * @apiSuccess (响应结果) {String} code 状态码
     * @apiSuccess (响应结果) {String} description 错误信息
     * @apiSuccessExample 响应结果示例
     * {"code":"C040000","description":"成功"}
     * @param request 请求参数
     * @return com.howbuy.crm.account.client.response.Response<String>
     * @description: 写入人员绩效系数表接口（新增客户和分配投顾）
     * <AUTHOR>
     * @date 2025-06-27 17:05:00
     * @since JDK 1.8
     */
    Response<String> execute(MergeConsPerformanceCoeffRequest request);


    /**
     * @api {DUBBO} com.howbuy.crm.account.client.facade.consultant.MergeConsPerformanceCoeffFacade.executeInit() executeInit()
     * @apiVersion 1.0.0
     * @apiGroup MergeConsPerformanceCoeffFacade
     * @apiName executeInit()
     * @apiDescription 初始化写入人员绩效系数表接口（如页面初始化、预填充等场景）
     * @apiParam (请求体) {String} request 初始化参数（JSON字符串或相关标识）
     * @apiParamExample 请求体示例
     * {"initType":"default"}
     * @apiSuccess (响应结果) {String} code 状态码
     * @apiSuccess (响应结果) {String} description 描述信息
     * @apiSuccessExample 响应结果示例
     * {"code":"0000","description":"初始化成功"}
     * @param request 初始化参数
     * @return com.howbuy.crm.account.client.response.Response<String>
     * @description: 初始化写入人员绩效系数表接口
     * <AUTHOR>
     * @date 2025-07-17 16:24:26
     * @since JDK 1.8
     */
    Response<String> executeInit(String request);

} 