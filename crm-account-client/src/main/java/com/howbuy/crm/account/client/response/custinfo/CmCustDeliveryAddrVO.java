/**
 * Copyright (c) 2024, <PERSON><PERSON><PERSON><PERSON> HOWBUY INVESTMENT MANAGEMENT Co., Ltd.
 * All right reserved.
 * <p>
 * THIS IS UNPUBLISHED PROPRIETARY SOURCE CODE OF HOWBUY INVESTMENT
 * MANAGEMENT CO., LTD.  THE CONTENTS OF THIS FILE MAY NOT BE DISCLOSED
 * TO THIRD PARTIES, COPIED OR DUPLICATED IN ANY FORM, IN WHOLE OR IN PART,
 * WITHOUT THE PRIOR WRITTEN PERMISSION OF HOWBUY INVESTMENT MANAGEMENT
 * CO., LTD.
 */
package com.howbuy.crm.account.client.response.custinfo;

import com.fasterxml.jackson.annotation.JsonFormat;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;

/**
 * @description: 查询投顾收货地址的VO
 * <AUTHOR>
 * @date 2024/11/19 10:42
 * @since JDK 1.8
 */
public class CmCustDeliveryAddrVO implements Serializable {

    private static final long serialVersionUID = 1071069822004471067L;


    /**
     * ID
     */
    private BigDecimal id;


    /**
     * 投顾客户号
     */
    private String conscustno;

    /**
     * 一账通号
     */
    private String hboneNo;

    /**
     * 收货人姓名
     */
    private String receiverName;

    /**
     * 收货人手机号摘要
     */
    private String mobileDigest;

    /**
     * 收货人手机号掩码
     */
    private String mobileMask;

    /**
     * 收货人手机号密文
     */
    private String mobileCipher;

    /**
     * 省编码
     */
    private String provCode;

    /**
     * 市编码
     */
    private String cityCode;

    /**
     * 区编码
     */
    private String countyCode;

    /**
     * 详细地址摘要
     */
    private String addrDigest;

    /**
     * 详细地址掩码
     */
    private String addrMask;

    /**
     * 详细地址密文
     */
    private String addrCipher;

    /**
     * 备注
     */
    private String remark;

    /**
     * 创建时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date createTimestamp;


    /**
     * 修改时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss.SSS", timezone = "GMT+8")
    private Date modifyTimestamp;

    public BigDecimal getId() {
        return id;
    }

    public void setId(BigDecimal id) {
        this.id = id;
    }

    public String getConscustno() {
        return conscustno;
    }

    public void setConscustno(String conscustno) {
        this.conscustno = conscustno;
    }

    public String getHboneNo() {
        return hboneNo;
    }

    public void setHboneNo(String hboneNo) {
        this.hboneNo = hboneNo;
    }

    public String getReceiverName() {
        return receiverName;
    }

    public void setReceiverName(String receiverName) {
        this.receiverName = receiverName;
    }

    public String getMobileDigest() {
        return mobileDigest;
    }

    public void setMobileDigest(String mobileDigest) {
        this.mobileDigest = mobileDigest;
    }

    public String getMobileMask() {
        return mobileMask;
    }

    public void setMobileMask(String mobileMask) {
        this.mobileMask = mobileMask;
    }

    public String getMobileCipher() {
        return mobileCipher;
    }

    public void setMobileCipher(String mobileCipher) {
        this.mobileCipher = mobileCipher;
    }

    public String getProvCode() {
        return provCode;
    }

    public void setProvCode(String provCode) {
        this.provCode = provCode;
    }

    public String getCityCode() {
        return cityCode;
    }

    public void setCityCode(String cityCode) {
        this.cityCode = cityCode;
    }

    public String getCountyCode() {
        return countyCode;
    }

    public void setCountyCode(String countyCode) {
        this.countyCode = countyCode;
    }

    public String getAddrDigest() {
        return addrDigest;
    }

    public void setAddrDigest(String addrDigest) {
        this.addrDigest = addrDigest;
    }

    public String getAddrMask() {
        return addrMask;
    }

    public void setAddrMask(String addrMask) {
        this.addrMask = addrMask;
    }

    public String getAddrCipher() {
        return addrCipher;
    }

    public void setAddrCipher(String addrCipher) {
        this.addrCipher = addrCipher;
    }

    public String getRemark() {
        return remark;
    }

    public void setRemark(String remark) {
        this.remark = remark;
    }

    public Date getCreateTimestamp() {
        return createTimestamp;
    }

    public void setCreateTimestamp(Date createTimestamp) {
        this.createTimestamp = createTimestamp;
    }

    public Date getModifyTimestamp() {
        return modifyTimestamp;
    }

    public void setModifyTimestamp(Date modifyTimestamp) {
        this.modifyTimestamp = modifyTimestamp;
    }
}