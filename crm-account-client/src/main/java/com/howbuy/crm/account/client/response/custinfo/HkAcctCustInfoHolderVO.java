/**
 * Copyright (c) 2024, <PERSON><PERSON><PERSON><PERSON> HOWBUY INVESTMENT MANAGEMENT Co., Ltd.
 * All right reserved.
 * <p>
 * THIS IS UNPUBLISHED PROPRIETARY SOURCE CODE OF HOWBUY INVESTMENT
 * MANAGEMENT CO., LTD.  THE CONTENTS OF THIS FILE MAY NOT BE DISCLOSED
 * TO THIRD PARTIES, COPIED OR DUPLICATED IN ANY FORM, IN WHOLE OR IN PART,
 * WITHOUT THE PRIOR WRITTEN PERMISSION OF HOWBUY INVESTMENT MANAGEMENT
 * CO., LTD.
 */
package com.howbuy.crm.account.client.response.custinfo;

import com.howbuy.crm.account.client.response.bankcardinfo.HkBankCardInfoVO;
import com.howbuy.crm.account.client.response.declarationinfo.HkDeclarationInfoVO;
import com.howbuy.crm.account.client.response.kycinfo.HkKycInfoVO;
import com.howbuy.crm.account.client.response.taxinfo.HkTaxInfoVO;
import lombok.Data;

import java.util.List;

/**
 * @description: 香港客户信息VO类 包含客户所有的信息
 * <AUTHOR>
 * @date 2024/1/11 14:44
 * @since JDK 1.8
 */
@Data
public class HkAcctCustInfoHolderVO {

    /**
     * 客户信息
     */
    private HkAcctCustFullInfoVO custInfoVO;
    /**
     * 税务信息
     */
    private HkTaxInfoVO queryIndiTaxInfoVO;
    /**
     * 声明信息列表
     */
    private List<HkDeclarationInfoVO> queryDeclarationInfoVOList;
    /**
     * 银行卡信息
     */
    private List<HkBankCardInfoVO> bankCardInfoVOList;
    /**
     * Kyc信息
     */
    private HkKycInfoVO kycInfoVO;
    /**
     * 香港客户一账通关系
     */
    private HkCustHboneRelVO hkCustHboneRelVO;

}