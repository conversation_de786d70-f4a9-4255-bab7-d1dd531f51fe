package com.howbuy.crm.account.client.enums.hkcustinfo;

/**
 * @description:(海外储蓄罐协议状态 0-未签署、1-已签署、2-已终止)
 * @return
 * @author: haoran.zhang
 * @date: 2024/7/24 14:44
 * @since JDK 1.8
 */
public enum HkPiggyAgreeStateEnum {

	/**
	 * 0-未签署
	 */
    NO_SIGN("0", "未签署"),

	/**
	 * 1-已签署
	 */
	SIGNED("1", "已签署"),

	/**
	 * 2-已终止
	 */
	TERMINATED("2", "已终止");


	/**
	 * 编码
	 */
	private String code;

	/**
	 * 描述
	 */
	private String description;

	 HkPiggyAgreeStateEnum(String code, String description) {
		this.code = code;
		this.description = description;
	}


	/**
	 * 通过code获得
	 * 
	 * @param code
	 *            系统返回参数编码
	 * @return description 描述
	 */
	public static String getDescription(String code) {
		HkPiggyAgreeStateEnum statusEnum=getEnum(code);
		return statusEnum==null?null :statusEnum.getDescription();
	}

	/**
	 * 通过code直接返回 整个枚举类型
	 * 
	 * @param code
	 *            系统返回参数编码
	 * @return PreOccupyTypeEnum
	 */
	public static HkPiggyAgreeStateEnum getEnum(String code) {
		for(HkPiggyAgreeStateEnum statusEnum : HkPiggyAgreeStateEnum.values()){
			if(statusEnum.getCode().equals(code)){
				return statusEnum;
			}
		}
		return null;
	}




	public String getCode() {
		return code;
	}


	public String getDescription() {
		return description;
	}


}
