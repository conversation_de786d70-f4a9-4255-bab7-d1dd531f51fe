package com.howbuy.crm.account.client.enums;

/**
 * @description:(产品分销渠道的枚举)
 * @param
 * @return
 * @author: haoran.zhang
 * @date: 2023/12/8 16:44
 * @since JDK 1.8
 */
public enum DisChannelCodeEnum {

	/**
	 * HB000A001-好买
	 */
	HOWBUY("HB000A001", "好买"),


	/**
	 * HZ000N001-好臻分销
	 */
	HZ("HZ000N001", "好臻");

	/**
	 * 编码
	 */
	private String code;

	/**
	 * 描述
	 */
	private String description;

	 DisChannelCodeEnum(String code, String description) {
		this.code = code;
		this.description = description;
	}


	/**
	 * 通过code获得
	 * 
	 * @param code
	 *            系统返回参数编码
	 * @return description 描述
	 */
	public static String getDescription(String code) {
		DisChannelCodeEnum statusEnum=getEnum(code);
		return statusEnum==null?null :statusEnum.getDescription();
	}

	/**
	 * 通过code直接返回 整个枚举类型
	 * 
	 * @param code
	 *            系统返回参数编码
	 * @return PreOccupyTypeEnum
	 */
	public static DisChannelCodeEnum getEnum(String code) {
		for(DisChannelCodeEnum statusEnum : DisChannelCodeEnum.values()){
			if(statusEnum.getCode().equals(code)){
				return statusEnum;
			}
		}
		return null;
	}




	public String getCode() {
		return code;
	}


	public String getDescription() {
		return description;
	}


}
