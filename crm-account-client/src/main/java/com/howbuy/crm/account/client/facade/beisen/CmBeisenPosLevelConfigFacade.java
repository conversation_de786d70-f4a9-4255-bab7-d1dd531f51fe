/**
 * Copyright (c) 2024, <PERSON><PERSON><PERSON><PERSON> HOWBUY INVESTMENT MANAGEMENT Co., Ltd.
 * All right reserved.
 * <p>
 * THIS IS UNPUBLISHED PROPRIETARY SOURCE CODE OF HOWBUY INVESTMENT
 * MANAGEMENT CO., LTD.  THE CONTENTS OF THIS FILE MAY NOT BE DISCLOSED
 * TO THIRD PARTIES, COPIED OR DUPLICATED IN ANY FORM, IN WHOLE OR IN PART,
 * WITHOUT THE PRIOR WRITTEN PERMISSION OF HOWBUY INVESTMENT MANAGEMENT
 * CO., LTD.
 */
package com.howbuy.crm.account.client.facade.beisen;

import com.howbuy.crm.account.client.request.beisen.BeisenPosLevelConfigRequest;
import com.howbuy.crm.account.client.response.ExportToFileVO;
import com.howbuy.crm.account.client.response.Response;
import com.howbuy.crm.account.client.response.beisen.CmBeisenPosLevelConfigListVO;
import com.howbuy.crm.account.client.response.beisen.CmBeisenPosLevelConfigVO;

/**
 * @description: (好买北森职级映射facade)
 * <AUTHOR>
 * @date 2024/10/24 9:54
 * @since JDK 1.8
 */
public interface CmBeisenPosLevelConfigFacade {

    /**
     * @api {DUBBO} com.howbuy.crm.account.client.facade.beisen.CmBeisenPosLevelConfigFacade.queryBeisenPosLevelConfigList(request) 查询北森职级配置数据
     * @apiVersion 1.0.0
     * @apiGroup beisen
     * @apiName queryBeisenPosLevelConfigList
     * @apiParam (请求参数) {Number} id
     * @apiParam (请求参数) {String} positionsLevelBeisen
     * @apiParam (请求参数) {String} positionsLevelNameBeisen
     * @apiParam (请求参数) {String} userLevelCrm
     * @apiParam (请求参数) {String} userLevelCrmName
     * @apiParam (请求参数) {String} positionsLevelCrm
     * @apiParam (请求参数) {String} positionsLevelCrmName
     * @apiParam (请求参数) {String} subPositionsLevelCrm
     * @apiParam (请求参数) {String} subPositionsLevelCrmName
     * @apiParam (请求参数) {String} startDate
     * @apiParam (请求参数) {String} endDate
     * @apiParam (请求参数) {String} creator
     * @apiParam (请求参数) {String} modifier
     * @apiParam (请求参数) {Number} credt
     * @apiParam (请求参数) {Number} moddt
     * @apiParamExample 请求参数示例
     * positionsLevelNameBeisen=MpNK3cVi&positionsLevelCrmName=PF7A&subPositionsLevelCrm=qNkS2&moddt=2819217517964&creator=eS&userLevelCrmName=55bZRTXkW&endDate=KZM6zwUG5J&modifier=QGOMY0g&userLevelCrm=d2qydS&credt=3517096094078&positionsLevelBeisen=MAe0u&subPositionsLevelCrmName=u8jOs2&positionsLevelCrm=RLS8u&id=3357&startDate=5ZN
     * @apiSuccess (响应结果) {String} code
     * @apiSuccess (响应结果) {String} description
     * @apiSuccess (响应结果) {Object} data
     * @apiSuccess (响应结果) {Array} data.list
     * @apiSuccess (响应结果) {Number} data.list.id
     * @apiSuccess (响应结果) {String} data.list.positionsLevelBeisen
     * @apiSuccess (响应结果) {String} data.list.positionsLevelNameBeisen
     * @apiSuccess (响应结果) {String} data.list.userLevelCrm
     * @apiSuccess (响应结果) {String} data.list.userLevelCrmName
     * @apiSuccess (响应结果) {String} data.list.positionsLevelCrm
     * @apiSuccess (响应结果) {String} data.list.positionsLevelCrmName
     * @apiSuccess (响应结果) {String} data.list.subPositionsLevelCrm
     * @apiSuccess (响应结果) {String} data.list.subPositionsLevelCrmName
     * @apiSuccess (响应结果) {String} data.list.startDate
     * @apiSuccess (响应结果) {String} data.list.endDate
     * @apiSuccess (响应结果) {String} data.list.creator
     * @apiSuccess (响应结果) {String} data.list.modifier
     * @apiSuccess (响应结果) {Number} data.list.credt
     * @apiSuccess (响应结果) {Number} data.list.moddt
     * @apiSuccessExample 响应结果示例
     * {"code":"z1Dmb1A","data":{"list":[{"positionsLevelNameBeisen":"ca4RvqC","positionsLevelCrmName":"Ep","subPositionsLevelCrm":"l7KJ5","moddt":*************,"creator":"kqcU","userLevelCrmName":"DgmGvLyh","endDate":"WtFp","modifier":"9s","userLevelCrm":"0X","credt":*************,"positionsLevelBeisen":"QKE","subPositionsLevelCrmName":"B8oOQEXTN","positionsLevelCrm":"K83skH4WJn","id":6753,"startDate":"CqaPSMG1"}]},"description":"FhaU9G"}
     */
    Response<CmBeisenPosLevelConfigListVO> queryBeisenPosLevelConfigList(BeisenPosLevelConfigRequest request);


    /**
     * @api {DUBBO} com.howbuy.crm.account.client.facade.beisen.CmBeisenPosLevelConfigFacade.saveBeisenPosLevelConfig(request)  保存北森职级配置数据
     * @apiVersion 1.0.0
     * @apiGroup beisen
     * @apiName saveBeisenPosLevelConfig
     * @apiParam (请求参数) {Number} id
     * @apiParam (请求参数) {String} positionsLevelBeisen
     * @apiParam (请求参数) {String} positionsLevelNameBeisen
     * @apiParam (请求参数) {String} userLevelCrm
     * @apiParam (请求参数) {String} userLevelCrmName
     * @apiParam (请求参数) {String} positionsLevelCrm
     * @apiParam (请求参数) {String} positionsLevelCrmName
     * @apiParam (请求参数) {String} subPositionsLevelCrm
     * @apiParam (请求参数) {String} subPositionsLevelCrmName
     * @apiParam (请求参数) {String} startDate
     * @apiParam (请求参数) {String} endDate
     * @apiParam (请求参数) {String} creator
     * @apiParam (请求参数) {String} modifier
     * @apiParam (请求参数) {Number} credt
     * @apiParam (请求参数) {Number} moddt
     * @apiParamExample 请求参数示例
     * positionsLevelNameBeisen=iY&positionsLevelCrmName=axrP&subPositionsLevelCrm=yM&moddt=2249168752612&creator=AQb&userLevelCrmName=eI6gMEL&endDate=QLYOC&modifier=jIg9kF7HYo&userLevelCrm=Y9w5gryy&credt=632794893618&positionsLevelBeisen=7JU3&subPositionsLevelCrmName=LRQBjIf&positionsLevelCrm=2cn4CFU4wr&id=7772&startDate=g52zGciz4
     * @apiSuccess (响应结果) {String} code
     * @apiSuccess (响应结果) {String} description
     * @apiSuccess (响应结果) {String} data
     * @apiSuccessExample 响应结果示例
     * {"code":"3njM","data":"V3U1","description":"CsIgX2K"}
     */
    Response<String> saveBeisenPosLevelConfig(BeisenPosLevelConfigRequest request);


    /**
     * @api {DUBBO} com.howbuy.crm.account.client.facade.beisen.CmBeisenPosLevelConfigFacade.deleteBeisenPosLevelConfig(request)  删除北森职级配置数据
     * @apiVersion 1.0.0
     * @apiGroup beisen
     * @apiName deleteBeisenPosLevelConfig
     * @apiParam (请求参数) {Number} id
     * @apiParam (请求参数) {String} positionsLevelBeisen
     * @apiParam (请求参数) {String} positionsLevelNameBeisen
     * @apiParam (请求参数) {String} userLevelCrm
     * @apiParam (请求参数) {String} userLevelCrmName
     * @apiParam (请求参数) {String} positionsLevelCrm
     * @apiParam (请求参数) {String} positionsLevelCrmName
     * @apiParam (请求参数) {String} subPositionsLevelCrm
     * @apiParam (请求参数) {String} subPositionsLevelCrmName
     * @apiParam (请求参数) {String} startDate
     * @apiParam (请求参数) {String} endDate
     * @apiParam (请求参数) {String} creator
     * @apiParam (请求参数) {String} modifier
     * @apiParam (请求参数) {Number} credt
     * @apiParam (请求参数) {Number} moddt
     * @apiParamExample 请求参数示例
     * positionsLevelNameBeisen=ug&positionsLevelCrmName=ap5&subPositionsLevelCrm=sC&moddt=188124200203&creator=WD4U&userLevelCrmName=Cv5rrlSY&endDate=C6LCjOGDa&modifier=jd0LT172Cy&userLevelCrm=o64a&credt=2930487233878&positionsLevelBeisen=qeWHCM7&subPositionsLevelCrmName=5S4&positionsLevelCrm=q6pe&id=1863&startDate=g
     * @apiSuccess (响应结果) {String} code
     * @apiSuccess (响应结果) {String} description
     * @apiSuccess (响应结果) {String} data
     * @apiSuccessExample 响应结果示例
     * {"code":"qkY7Uegd","data":"dejVc5","description":"dt02qK6dCg"}
     */
    Response<String> deleteBeisenPosLevelConfig(BeisenPosLevelConfigRequest request);


    /**
     * @api {DUBBO} com.howbuy.crm.account.client.facade.beisen.CmBeisenPosLevelConfigFacade.queryBeisenPosLevelConfigDetail(request)  查询北森职级配置详细数据
     * @apiVersion 1.0.0
     * @apiGroup beisen
     * @apiName queryBeisenPosLevelConfigDetail
     * @apiParam (请求参数) {Number} id 主键
     * @apiParam (请求参数) {String} positionsLevelBeisen 职级编码（北森）
     * @apiParam (请求参数) {String} positionsLevelNameBeisen 职级名称(北森)
     * @apiParam (请求参数) {String} userLevelCrm 层级（crm）
     * @apiParam (请求参数) {String} userLevelCrmName 层级名称（crm）
     * @apiParam (请求参数) {String} positionsLevelCrm 职级编码（crm）
     * @apiParam (请求参数) {String} positionsLevelCrmName 职级名称（crm）
     * @apiParam (请求参数) {String} subPositionsLevelCrm 副职编码（crm）
     * @apiParam (请求参数) {String} subPositionsLevelCrmName 副职名称（crm）
     * @apiParam (请求参数) {String} startDate 起始日期
     * @apiParam (请求参数) {String} endDate 结束日期
     * @apiParam (请求参数) {String} creator 创建人
     * @apiParam (请求参数) {String} modifier 修改人
     * @apiParam (请求参数) {Number} credt 创建时间
     * @apiParam (请求参数) {Number} moddt 修改时间
     * @apiParamExample 请求参数示例
     * positionsLevelNameBeisen=1RTzZkD6kC&positionsLevelCrmName=h07lhwh&subPositionsLevelCrm=WQV&moddt=2541234739108&creator=SVNmhxxS5&userLevelCrmName=eGUYf&endDate=Jd7KgBRC&modifier=8yqsbcWE&userLevelCrm=7O7WaQ&credt=404843963739&positionsLevelBeisen=HawJb&subPositionsLevelCrmName=tyLwxOQ&positionsLevelCrm=j3lZI2w&id=4854&startDate=tTaLXKr
     * @apiSuccess (响应结果) {String} code 状态码
     * @apiSuccess (响应结果) {String} description 描述信息
     * @apiSuccess (响应结果) {Object} data 数据封装
     * @apiSuccess (响应结果) {Number} data.id 主键
     * @apiSuccess (响应结果) {String} data.positionsLevelBeisen 职级编码（北森）
     * @apiSuccess (响应结果) {String} data.positionsLevelNameBeisen 职级名称(北森)
     * @apiSuccess (响应结果) {String} data.userLevelCrm 层级（crm）
     * @apiSuccess (响应结果) {String} data.userLevelCrmName 层级名称（crm）
     * @apiSuccess (响应结果) {String} data.positionsLevelCrm 职级编码（crm）
     * @apiSuccess (响应结果) {String} data.positionsLevelCrmName 职级名称（crm）
     * @apiSuccess (响应结果) {String} data.subPositionsLevelCrm 副职编码（crm）
     * @apiSuccess (响应结果) {String} data.subPositionsLevelCrmName 副职名称（crm）
     * @apiSuccess (响应结果) {String} data.startDate 起始日期
     * @apiSuccess (响应结果) {String} data.endDate 结束日期
     * @apiSuccess (响应结果) {String} data.creator 创建人
     * @apiSuccess (响应结果) {String} data.modifier 修改人
     * @apiSuccess (响应结果) {Number} data.credt 创建时间
     * @apiSuccess (响应结果) {Number} data.moddt 修改时间
     * @apiSuccessExample 响应结果示例
     * {"code":"4Jpc7","data":{"positionsLevelNameBeisen":"q2l2mXHI","positionsLevelCrmName":"WwDn1","subPositionsLevelCrm":"FV","moddt":*************,"creator":"Zkrshz","userLevelCrmName":"JV83jlM0","endDate":"4FteWa","modifier":"VDE","userLevelCrm":"Jz6rOdHPJ","credt":*************,"positionsLevelBeisen":"oKe1TibCHE","subPositionsLevelCrmName":"PliXhu","positionsLevelCrm":"iW","id":9774,"startDate":"z"},"description":"0xppvBW4I"}
     */
    Response<CmBeisenPosLevelConfigVO> queryBeisenPosLevelConfigDetail(BeisenPosLevelConfigRequest request);


    /**
     * @api {DUBBO} com.howbuy.crm.account.client.facade.beisen.CmBeisenPosLevelConfigFacade.exportBeisenPosLevelConfigList(request)  导出北森职级配置数据
     * @apiVersion 1.0.0
     * @apiGroup beisen
     * @apiName exportBeisenPosLevelConfigList
     * @apiParam (请求参数) {Number} id 主键
     * @apiParam (请求参数) {String} positionsLevelBeisen 职级编码（北森）
     * @apiParam (请求参数) {String} positionsLevelNameBeisen 职级名称(北森)
     * @apiParam (请求参数) {String} userLevelCrm 层级（crm）
     * @apiParam (请求参数) {String} userLevelCrmName 层级名称（crm）
     * @apiParam (请求参数) {String} positionsLevelCrm 职级编码（crm）
     * @apiParam (请求参数) {String} positionsLevelCrmName 职级名称（crm）
     * @apiParam (请求参数) {String} subPositionsLevelCrm 副职编码（crm）
     * @apiParam (请求参数) {String} subPositionsLevelCrmName 副职名称（crm）
     * @apiParam (请求参数) {String} startDate 起始日期
     * @apiParam (请求参数) {String} endDate 结束日期
     * @apiParam (请求参数) {String} creator 创建人
     * @apiParam (请求参数) {String} modifier 修改人
     * @apiParam (请求参数) {Number} credt 创建时间
     * @apiParam (请求参数) {Number} moddt 修改时间
     * @apiParamExample 请求参数示例
     * positionsLevelNameBeisen=S&positionsLevelCrmName=gD061ll&subPositionsLevelCrm=VAk8zoWD&moddt=671505893489&creator=lpbEu0yON&userLevelCrmName=5EgMwmmygo&endDate=c1&modifier=Fnhc63&userLevelCrm=6N19Kh&credt=1858947290156&positionsLevelBeisen=WCJqA&subPositionsLevelCrmName=nwpuZqE3&positionsLevelCrm=GyB0&id=7508&startDate=yL
     * @apiSuccess (响应结果) {String} code 状态码
     * @apiSuccess (响应结果) {String} description 描述信息
     * @apiSuccess (响应结果) {Object} data 数据封装
     * @apiSuccess (响应结果) {String} data.fileByte 文件字节数组的base64编码串
     * @apiSuccess (响应结果) {String} data.name 文件名
     * @apiSuccess (响应结果) {String} data.type 文件类型
     * @apiSuccessExample 响应结果示例
     * {"code":"ABDiV","data":{"fileByte":"XnvJr","name":"blUST90t4","type":"9zmlsyzBck"},"description":"MPZ9P"}
     */
    Response<ExportToFileVO> exportBeisenPosLevelConfigList(BeisenPosLevelConfigRequest request);

}