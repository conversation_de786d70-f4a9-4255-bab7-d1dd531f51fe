package com.howbuy.crm.account.client.enums.custinfo;

/**
 * @description:(客户来源-来源编号 ：
 * PH2312W02- 好买香港自助开户、
 * RH1911W01- 研习社[掌H5-商学院引流]
 * RO1509P05- 其他来源-未知
 * RO1903W01- 掌H5-关爱通
 * )
 * @param
 * @return
 * @author: haoran.zhang
 * @date: 2023/12/8 17:46
 * @since JDK 1.8
 */
public enum CustSourceCodeEnum {

	/**
	 * PH2312W02-好买香港自助开户
	 */
	HOWBUY_HK_SELF_OPEN("PH2312W02", "好买香港自助开户"),


	/**
	 * PH2410W01-好买香港-线下柜台开户
	 */
	HOWBUY_HK_COUNTER_OPEN("PH2410W01", "好买香港-线下柜台开户"),

	/**
	 * RH1911W01-研习社[掌H5-商学院引流]
	 */
	YAN_XI_SHE("RH1911W01", "研习社[掌H5-商学院引流]"),

	/**
	 * RO1509P05-其他来源-未知
	 */
	OTHER_SOURCE("RO1509P05", "其他来源-未知"),

	/**
	 * RO1903W01-掌H5-关爱通
	 */
	APP_H5_GUAN_AI_TONG("RO1903W01", "掌H5-关爱通"),

	//待补充 。。。。。。
	;

	/**
	 * 编码
	 */
	private String code;

	/**
	 * 描述
	 */
	private String description;

	CustSourceCodeEnum(String code, String description) {
		this.code = code;
		this.description = description;
	}

	/**
	 * 通过code获得
	 *
	 * @param code 系统返回参数编码
	 * @return description 描述
	 */
	public static String getDescription(String code) {
		CustSourceCodeEnum statusEnum = getEnum(code);
		return statusEnum == null ? null : statusEnum.getDescription();
	}

	/**
	 * 通过code直接返回 整个枚举类型
	 *
	 * @param code 系统返回参数编码
	 * @return BaseConstantEnum
	 */
	public static CustSourceCodeEnum getEnum(String code) {
		for (CustSourceCodeEnum statusEnum : CustSourceCodeEnum.values()) {
			if (statusEnum.getCode().equals(code)) {
				return statusEnum;
			}
		}
		return null;
	}


	public String getCode() {
		return code;
	}


	public String getDescription() {
		return description;
	}

}
