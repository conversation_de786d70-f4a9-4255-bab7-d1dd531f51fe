package com.howbuy.crm.account.client.response.custinfo;


import lombok.Data;

import java.io.Serializable;

/**
 * 一账通账户中心 侧  分销客户敏感信息 信息属性
 * 来源：com.howbuy.acccenter.facade.query.sensitive.discustinfo.QueryDisCustSensitiveInfoResponse.DisCustInfoBean
 */
@Data
public class HboneDisCustSensitiveInfoVO implements Serializable {

    private static final long serialVersionUID = 6509013597100244976L;
    /**
     *分销code
     */
    private String disCode;
    /**
     *一账通号
     */
    private String hboneNo;
    /**
     *分销客户号
     */
    private String custNo;

    /**
     *地址
     */
    private String addr;
    /**
     *手机号
     */
    private String telNo;
    /**
     *联系人
     */
    private String linkMan;
    /**
     *联系人证件号码
     */
    private String linkIdNo;
    /**
     *联系人手机
     */
    private String linkTel;
    /**
     *邮箱
     */
    private String email;
    /**
     *家庭电话
     */
    private String homeTelNo;
    /**
     *手机掩码
     */
    private String mobileMask;
    /**
     *
     */
    private String actualController;
    /**
     *
     */
    private String controllerIdNo;
    /**
     *
     */
    private String stockHolder;
    /**
     *
     */
    private String stockerIdNo;



}
