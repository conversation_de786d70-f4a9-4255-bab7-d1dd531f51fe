/**
 * Copyright (c) 2023, <PERSON><PERSON><PERSON><PERSON> HOWBUY INVESTMENT MANAGEMENT Co., Ltd.
 * All right reserved.
 * <p>
 * THIS IS UNPUBLISHED PROPRIETARY SOURCE CODE OF HOWBUY INVESTMENT
 * MANAGEMENT CO., LTD.  THE CONTENTS OF THIS FILE MAY NOT BE DISCLOSED
 * TO THIRD PARTIES, COPIED OR DUPLICATED IN ANY FORM, IN WHOLE OR IN PART,
 * WITHOUT THE PRIOR WRITTEN PERMISSION OF HOWBUY INVESTMENT MANAGEMENT
 * CO., LTD.
 */
package com.howbuy.crm.account.client.response;

import com.howbuy.crm.account.client.enums.ExceptionCodeEnum;

import java.io.Serializable;

/**
 * @description: 返回基础类
 * <AUTHOR>
 * @date 2023/11/13 18:04
 * @since JDK 1.8
 */
public class Response<T> implements Serializable {
    private static final long serialVersionUID = 5564251526036762430L;
    /**
     * 状态码
     */
    private String code;
    /**
     * 描述信息
     */
    private String description;
    /**
     * 数据封装
     */
    private T data;

    public Response() {
    }

    public Response(String code, String description, T data) {
        this.code = code;
        this.description = description;
        this.data = data;
    }

    /**
     * @description: 成功返回结果
     * @return com.howbuy.crm.account.client.response.Response<T>
     * @author: hongdong.xie
     * @date: 2023/11/13 18:11
     * @since JDK 1.8
     */
    public static <T> Response<T> ok() {
        return ok(null);
    }

    /**
     * @description: 成功返回结果
     * @param data	 获取的数据
     * @return com.howbuy.crm.account.client.response.Response<T>
     * @author: hongdong.xie
     * @date: 2023/11/13 18:12
     * @since JDK 1.8
     */
    public static <T> Response<T> ok(T data) {
        return new Response<T>(ExceptionCodeEnum.SUCCESS.getCode(), ExceptionCodeEnum.SUCCESS.getDesc(), data);
    }

    /**
     * @description: 成功返回结果
     * @param description 描述信息
     * @param data 获取的数据
     * @return com.howbuy.crm.account.client.response.Response<T>
     * @author: hongdong.xie
     * @date: 2023/11/13 18:12
     * @since JDK 1.8
     */
    public static <T> Response<T> ok(String description, T data) {
        return new Response<T>(ExceptionCodeEnum.SUCCESS.getCode(), description, data);
    }

    /**
     * @description: 失败返回结果
     * @param code 状态码
     * @param description 描述信息
     * @param data 数据封装
     * @return com.howbuy.crm.account.client.response.Response<T>
     * @author: hongdong.xie
     * @date: 2023/11/13 18:13
     * @since JDK 1.8
     */
    public static <T> Response<T> fail(String code, String description, T data) {
        return new Response<T>(code, description, data);
    }

    /**
     * @description: 失败返回结果
     * @param code 状态码
     * @param description 描述信息
     * @return com.howbuy.crm.account.client.response.Response<T>
     * @author: hongdong.xie
     * @date: 2023/11/13 18:13
     * @since JDK 1.8
     */
    public static <T> Response<T> fail(String code, String description) {
        return new Response<T>(code, description, null);
    }

    /**
     * @description: 失败返回结果
     * @param description 描述信息
     * @return com.howbuy.crm.account.client.response.Response<T>
     * @author: hongdong.xie
     * @date: 2023/11/13 18:13
     * @since JDK 1.8
     */
    public static <T> Response<T> fail(String description) {
        return fail(ExceptionCodeEnum.SYSTEM_ERROR.getCode(), description, null);
    }

    /**
     * @description: 失败返回结果
     * @param resultCode 描述信息枚举
     * @return com.howbuy.crm.account.client.response.Response<T>
     * @date: 2023/11/13 18:14
     * @since JDK 1.8
     */
    public static <T> Response<T> fail(ExceptionCodeEnum resultCode) {
        return fail(resultCode.getCode(), resultCode.getDesc(), null);
    }

    /**
     * @description: 失败返回结果
     * @param resultCode 描述信息枚举
     * @param data 数据封装
     * @return com.howbuy.crm.account.client.response.Response<T>
     * @date: 2023/11/13 18:14
     * @since JDK 1.8
     */
    public static <T> Response<T> fail(ExceptionCodeEnum resultCode, T data) {
        return fail(resultCode.getCode(), resultCode.getDesc(), data);
    }

    /**
     * @description: 系统异常
     * @return com.howbuy.crm.account.client.response.Response<T>
     * @date: 2023/11/13 18:14
     * @since JDK 1.8
     */
    public static <T> Response<T> fail() {
        return fail(ExceptionCodeEnum.SYSTEM_ERROR.getCode());
    }

    /**
     * @description: 是否成功
     * @return
     * @author: hongdong.xie
     * @date: 2023/11/13 18:16
     * @since JDK 1.8
     */

    public boolean isSuccess(){
        return ExceptionCodeEnum.SUCCESS.getCode().equals(this.getCode());
    }

    public String getCode() {
        return code;
    }

    public void setCode(String code) {
        this.code = code;
    }

    public String getDescription() {
        return description;
    }

    public void setDescription(String description) {
        this.description = description;
    }

    public T getData() {
        return data;
    }

    public void setData(T data) {
        this.data = data;
    }
}