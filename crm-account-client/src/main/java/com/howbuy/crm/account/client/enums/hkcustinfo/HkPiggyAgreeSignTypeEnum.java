package com.howbuy.crm.account.client.enums.hkcustinfo;

/**
 * @description:(海外储蓄罐协议签署方式 1-线下自主申请开通 2-线上自主申请开通 3-到期自动续期 4-底层基金更换同意)
 * @return
 * @author: haoran.zhang
 * @date: 2024/7/24 14:44
 * @since JDK 1.8
 */
public enum HkPiggyAgreeSignTypeEnum {

	/**
	 * 1-线下自主申请开通
	 */
    OFFLINE("1", "线下自主申请","线下自主申请开通","线下自主申请关闭"),
    /**
     * 2-线上自主申请开通
     */
    ONLINE("2","线上自主申请","线上自主申请开通","线上自主申请关闭"),
    /**
     * 3-到期自动续期
     */
    AUTO_RENEW("3","到期自动续期", "到期自动续期","未同意底层基金更换"),
    /**
     * 4-底层基金更换同意
     */
    FUND_CHANGE("4","底层基金更换同意", "同意底层基金更换","不同意底层基金更换");


	/**
	 * 编码
	 */
	private String code;

	/**
	 * 描述
	 */
	private String description;

	/**
	 * busiCode 海外储蓄罐协议流水 127-签署
	 * 的描述信息
	 * 当【业务类型=协议签署】时，枚举值：线下自主申请开通/线上自主申请开通/到期自动续期/同意底层基金更换
	 */
	private String signDesc;

	/**
	 * busiCode 海外储蓄罐协议流水 128-取消
	 * 的描述信息
	 * 当【业务类型=协议终止】时，枚举值：线下自主申请关闭/线上自主申请关闭/未同意底层基金更换/不同意底层基金更换
 	 */
	private String cancelDesc;

	/**
	 * busiCode 海外储蓄罐协议流水 127-签署
	 */
	private static final String BUSI_CODE_SIGN = "127";

	/**
	 *busiCode 海外储蓄罐协议流水  128-终止
	 */
	private static final String BUSI_CODE_CANCEL = "128";

	 HkPiggyAgreeSignTypeEnum(String code, String description,String signDesc,String cancelDesc) {
		this.code = code;
		this.description = description;
		this.signDesc=signDesc;
		this.cancelDesc=cancelDesc;
	}


	/**
	 * 通过code获得
	 * 
	 * @param code
	 *            系统返回参数编码
	 * @return description 描述
	 */
	public static String getDescription(String code) {
		HkPiggyAgreeSignTypeEnum statusEnum=getEnum(code);
		return statusEnum==null?null :statusEnum.getDescription();
	}

	/**
	 * 通过code直接返回 整个枚举类型
	 * 
	 * @param code
	 *            系统返回参数编码
	 * @return PreOccupyTypeEnum
	 */
	public static HkPiggyAgreeSignTypeEnum getEnum(String code) {
		for(HkPiggyAgreeSignTypeEnum statusEnum : HkPiggyAgreeSignTypeEnum.values()){
			if(statusEnum.getCode().equals(code)){
				return statusEnum;
			}
		}
		return null;
	}




	public String getCode() {
		return code;
	}


	public String getDescription() {
		return description;
	}

	public String getSignDesc() {
        return signDesc;
    }
	public String getCancelDesc() {
        return cancelDesc;
    }


	/**
	 * 协议签署方式描述
	 * 依赖 busiCode . 翻译为不同的描述信息
	 * @return
	 */
	public String translateSignType(String busiCode) {
		if(BUSI_CODE_SIGN.equals(busiCode)){
			return getSignDesc();
		}else if(BUSI_CODE_CANCEL.equals(busiCode)){
			return getCancelDesc();
		}
		return null;
	}


}
