/**
 * Copyright (c) 2023, <PERSON><PERSON><PERSON><PERSON> HOWBUY INVESTMENT MANAGEMENT Co., Ltd.
 * All right reserved.
 * <p>
 * THIS IS UNPUBLISHED PROPRIETARY SOURCE CODE OF HOWBUY INVESTMENT
 * MANAGEMENT CO., LTD.  THE CONTENTS OF THIS FILE MAY NOT BE DISCLOSED
 * TO THIRD PARTIES, COPIED OR DUPLICATED IN ANY FORM, IN WHOLE OR IN PART,
 * WITHOUT THE PRIOR WRITTEN PERMISSION OF HOWBUY INVESTMENT MANAGEMENT
 * CO., LTD.
 */
package com.howbuy.crm.account.client.request.custinfo;

import lombok.Data;

import java.io.Serializable;

/**
 * <AUTHOR>
 * @description: (创建香港账号 request 参数对象)
 * @date 2023/12/18 13:12
 * @since JDK 1.8
 */
@Data
public class HkAcctCreateOptVO implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 香港客户号
     */
    private String hkCustNo;

    /**
     * 操作人员
     */
    private String operator;

    /**
     * 客户号
     */
    private String custNo;

    /**
     * 关联的一账通号
     */
    private String hboneNo;

}