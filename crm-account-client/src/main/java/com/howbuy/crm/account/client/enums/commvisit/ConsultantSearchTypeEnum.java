package com.howbuy.crm.account.client.enums.commvisit;

/**
 * <AUTHOR>
 * @description: 请在此添加描述
 * @date 2025/4/11 15:45
 * @since JDK 1.8
 */
public enum ConsultantSearchTypeEnum {

    /**
     * 已处理
     */
    PM_NORMAL("1","项目经理"),

    /**
     * 无需处理
     */
    ALL_NORMAL("2","所有正常用户")
    ;

    /**
     * 编码
     **/
    private String code;
    /**
     * 描述
     * */
    private String description;

    ConsultantSearchTypeEnum(String code, String description){
        this.code=code;
        this.description=description;
    }

    public String getCode() {
        return code;
    }

    public void setCode(String code) {
        this.code = code;
    }

    public String getDescription() {
        return description;
    }

    public void setDescription(String description) {
        this.description = description;
    }
}