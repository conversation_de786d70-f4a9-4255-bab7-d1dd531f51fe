package com.howbuy.crm.account.client.request.commvisit;

import lombok.Data;

import java.io.Serializable;
import java.util.List;

/**
 * 查询拜访纪要列表请求
 */
@Data
public class QueryVisitMinutesListRequest implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 组织编码
     */
    private String orgCode;
    /**
     * 所属投顾ID
     */
    private String consCode;
    
    /**
     * 拜访日期开始 格式YYYYMMDD
     */
    private String visitDateStart;
    
    /**
     * 拜访日期结束 格式YYYYMMDD
     */
    private String visitDateEnd;
    
    /**
     * 创建日期开始 格式YYYYMMDD
     */
    private String createDateStart;
    
    /**
     * 创建日期结束 格式YYYYMMDD
     */
    private String createDateEnd;
    
    /**
     * 拜访目的列表
     */
    private List<String> visitPurpose;
    
    /**
     * 客户姓名
     */
    private String custName;
    
    /**
     * 投顾客户号
     */
    private String consCustNo;
    
    /**
     * 陪访人
     */
    private String accompanyingUser;
    
    /**
     * 上级主管
     */
    private String managerId;
    
    /**
     * 反馈情况
     */
    private List<String> feedbackStatus;
    
    /**
     * 分页页码
     */
    private Integer pageNo = 1;
    
    /**
     * 分页大小
     */
    private Integer pageSize = 100;
    
    /**
     * 当前用户ID
     */
    private String currentUserId;
} 