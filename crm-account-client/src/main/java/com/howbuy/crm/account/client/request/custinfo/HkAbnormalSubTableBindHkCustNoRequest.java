/**
 * Copyright (c) 2023, <PERSON><PERSON><PERSON><PERSON> HOWBUY INVESTMENT MANAGEMENT Co., Ltd.
 * All right reserved.
 * <p>
 * THIS IS UNPUBLISHED PROPRIETARY SOURCE CODE OF HOWBUY INVESTMENT
 * MANAGEMENT CO., LTD.  THE CONTENTS OF THIS FILE MAY NOT BE DISCLOSED
 * TO THIRD PARTIES, COPIED OR DUPLICATED IN ANY FORM, IN WHOLE OR IN PART,
 * WITHOUT THE PRIOR WRITTEN PERMISSION OF HOWBUY INVESTMENT MANAGEMENT
 * CO., LTD.
 */
package com.howbuy.crm.account.client.request.custinfo;

import com.howbuy.commons.validator.MyValidation;
import com.howbuy.commons.validator.util.ValidatorTypeEnum;

import java.io.Serializable;

/**
 * @description: (香港异常客户 子表绑定香港客户号 request 参数对象)
 * <AUTHOR>
 * @date 2025/05/23 13:17
 * @since JDK 1.8
 */
public class HkAbnormalSubTableBindHkCustNoRequest implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 异常客户信息id
     */
    @MyValidation(validatorType = ValidatorTypeEnum.String, fieldName = "异常子表id", isRequired = true)
    private String id;

    /**
     * 操作人
     */
    @MyValidation(validatorType = ValidatorTypeEnum.String, fieldName = "操作人", isRequired = true)
    private String operator;


    public String getId() {
        return id;
    }

    public void setId(String id) {
        this.id = id;
    }

    public String getOperator() {
        return operator;
    }

    public void setOperator(String operator) {
        this.operator = operator;
    }
}