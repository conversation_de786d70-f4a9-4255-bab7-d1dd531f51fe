/**
 * Copyright (c) 2024, <PERSON><PERSON><PERSON><PERSON> HOWBUY INVESTMENT MANAGEMENT Co., Ltd.
 * All right reserved.
 * <p>
 * THIS IS UNPUBLISHED PROPRIETARY SOURCE CODE OF HOWBUY INVESTMENT
 * MANAGEMENT CO., LTD.  THE CONTENTS OF THIS FILE MAY NOT BE DISCLOSED
 * TO THIRD PARTIES, COPIED OR DUPLICATED IN ANY FORM, IN WHOLE OR IN PART,
 * WITHOUT THE PRIOR WRITTEN PERMISSION OF HOWBUY INVESTMENT MANAGEMENT
 * CO., LTD.
 */
package com.howbuy.crm.account.client.request.consultant;

import com.howbuy.crm.account.client.request.Request;
import lombok.Getter;
import lombok.Setter;
import lombok.ToString;

import java.io.Serializable;
import java.util.List;

/**
 * @description: 批量获取人员绩效系数表请求对象
 * <AUTHOR>
 * @date 2025-07-17 15:50:04
 * @since JDK 1.8
 */
@Getter
@Setter
@ToString
public class BatchQueryConsPerformanceCoeffRequest extends Request implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 投顾客户号列表
     */
    private List<String> consCustNoList;

    /**
     * 投顾code（可选）
     */
    private String consCode;
} 