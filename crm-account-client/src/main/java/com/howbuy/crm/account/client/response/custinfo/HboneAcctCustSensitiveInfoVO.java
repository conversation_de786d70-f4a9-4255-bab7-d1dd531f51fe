package com.howbuy.crm.account.client.response.custinfo;


import lombok.Data;

import java.io.Serializable;

/**
 * 一账通账户中心 侧  客户敏感信息 信息属性
 * 来源：com.howbuy.acccenter.facade.query.queryacccustinfo.QueryAccCustInfoResponse.AccCustInfoBean
 */
@Data
public class HboneAcctCustSensitiveInfoVO implements Serializable {

    private static final long serialVersionUID = 428014877504198598L;

    /**
     *一账通号
     */
    private String hboneNo;
    /**
     *账户中心 客户号
     */
    private String custNo;
    /**
     *没名次
     */
    private String custName;
    /**
     *证件类型
     */
    private String idType;
    /**
     *证件号码
     */
    private String idNo;
    /**
     *公司idNo
     */
    private String corpIdNo;
    /**
     *
     */
    private String corporation;
    /**
     *
     */
    private String mobileMask;
    /**
     *
     */
    private String custIMEI;




}
