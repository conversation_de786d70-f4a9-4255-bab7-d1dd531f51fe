/**
 * Copyright (c) 2024, <PERSON><PERSON><PERSON><PERSON> HOWBUY INVESTMENT MANAGEMENT Co., Ltd.
 * All right reserved.
 * <p>
 * THIS IS UNPUBLISHED PROPRIETARY SOURCE CODE OF HOWBUY INVESTMENT
 * MANAGEMENT CO., LTD.  THE CONTENTS OF THIS FILE MAY NOT BE DISCLOSED
 * TO THIRD PARTIES, COPIED OR DUPLICATED IN ANY FORM, IN WHOLE OR IN PART,
 * WITHOUT THE PRIOR WRITTEN PERMISSION OF HOWBUY INVESTMENT MANAGEMENT
 * CO., LTD.
 */
package com.howbuy.crm.account.client.request.commvisit;

import com.howbuy.commons.validator.MyValidation;
import com.howbuy.commons.validator.util.ValidatorTypeEnum;
import com.howbuy.crm.account.client.request.Request;

import java.io.Serializable;
import java.util.List;

/**
 * <AUTHOR>
 * @description: 查询沟通拜访列表请求
 * @date 2024/10/24 10:20
 * @since JDK 1.8
 */
public class QueryCommVisitListRequest extends Request implements Serializable {

    private static final long serialVersionUID = -6087853247852821874L;

    /**
     * 投顾客户号
     */
    @MyValidation(validatorType = ValidatorTypeEnum.String, fieldName = "投顾客户号", isRequired = true)
    private String consCustNo;

    /**
     * 拜访类型列表
     */
    private List<String> visitTypeList;

    /**
     * 开始日期
     */
    private String startDt;

    /**
     * 结束日期
     */
    private String endDt;

    public String getConsCustNo() {
        return consCustNo;
    }

    public void setConsCustNo(String consCustNo) {
        this.consCustNo = consCustNo;
    }

    public List<String> getVisitTypeList() {
        return visitTypeList;
    }

    public void setVisitTypeList(List<String> visitTypeList) {
        this.visitTypeList = visitTypeList;
    }

    public String getStartDt() {
        return startDt;
    }

    public void setStartDt(String startDt) {
        this.startDt = startDt;
    }

    public String getEndDt() {
        return endDt;
    }

    public void setEndDt(String endDt) {
        this.endDt = endDt;
    }
}
