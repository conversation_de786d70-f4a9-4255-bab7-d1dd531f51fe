package com.howbuy.crm.account.client.enums.hkcustinfo;

/**
 * @description:( * 就业年收入
 *      * 01-≦HK$500,000
 *      * 02-HK$500,001 - HK$1,000,000
 *      * 03-HK$1,000,001 - HK$2,000,000
 *      * 04-HK$2,000,001 - HK$5,000,000
 *      * 05->HK$5,000,000)
 * @param
 * @return
 * @author: haoran.zhang
 * @date: 2023/12/19 13:43
 * @since JDK 1.8
 */
public enum HkEmplIncLevelEnum {
    /**
     * 01-≦HK$500,000
     */
    HK_EMP_INC_LEVEL_01("01","≦HK$500,000"),
    /**
     * 02-HK$500,001 - HK$1,000,000
     */
    HK_EMP_INC_LEVEL_02("02","HK$500,001 - HK$1,000,000"),
    /**
     * 03-HK$1,000,001 - HK$2,000,000
     */
    HK_EMP_INC_LEVEL_03("03","HK$1,000,001 - HK$2,000,000"),
    /**
     * 04-HK$2,000,001 - HK$5,000,000
     */
    HK_EMP_INC_LEVEL_04("04","HK$2,000,001 - HK$5,000,000"),
    /**
     * 05->HK$5,000,000
     */
    HK_EMP_INC_LEVEL_05("05",">HK$5,000,000");
    ;


    /**编码*/
    private String code;
    /**描述*/
    private String description;

    private HkEmplIncLevelEnum(String code, String description){
        this.code=code;
        this.description=description;
    }

    /**
     * 通过code获得
     * @param code	系统返回参数编码
     * @return description 描述
     */
    public static String getDescription(String code){
        for(HkEmplIncLevelEnum b: HkEmplIncLevelEnum.values()){
            if(b.getCode().equals(code)){
                return b.description;
            }
        }
        return null;
    }

    /**
     * 通过code直接返回 整个枚举类型
     * @param code 系统返回参数编码
     * @return BaseConstantEnum
     */
    public static HkEmplIncLevelEnum getEnum(String code){
        if(code != null && !"".equals(code)){
            for(HkEmplIncLevelEnum b: HkEmplIncLevelEnum.values()){
                if(b.getCode().equals(code)){
                    return b;
                }
            }
        }
        return null;
    }

    public String getCode() {
        return code;
    }


    public String getDescription() {
        return description;
    }
}
