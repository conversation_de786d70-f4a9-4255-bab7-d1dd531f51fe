package com.howbuy.crm.account.client.enums.custinfo;

import java.util.Arrays;
import java.util.HashSet;
import java.util.List;
import java.util.Set;
import java.util.stream.Collectors;

/**
 * @description: [香港异常客户] 业务节点 枚举类
 *
 *    使用项目：crm-hb
 *
 * @author: haoran.zhang
 * @date: 2023/12/22
 * @since JDK 1.8
 */
public enum HkBusinessNodeEnum {

    /**
     * 注册
     */
    REGISTER("1", "注册", new String[]{
            FullCustSourceEnum.HK_REGISTER.getCode()
    }),

    /**
     * 开户
     */
    OPEN_ACCOUNT("2", "开户", new String[]{
            FullCustSourceEnum.HK_ONLINE_OPEN_ACCOUNT.getCode(),
            FullCustSourceEnum.HK_OFFLINE_OPEN_ACCOUNT.getCode(),
            FullCustSourceEnum.HK_OPEN_ACCOUNT_CRM.getCode()
    }),

    /**
     * 其他
     */
    OTHER("3", "其他", new String[]{
            FullCustSourceEnum.HK_CUST_INFO_SYNC.getCode(),
            FullCustSourceEnum.HK_CUST_NO_BIND.getCode(),
            FullCustSourceEnum.HBONE_REAL_NAME_REGISTER.getCode(),
            FullCustSourceEnum.HBONE_REGISTER.getCode(),
            FullCustSourceEnum.DISTRIBUTION_OPEN_ACCOUNT.getCode(),
            FullCustSourceEnum.HBONE_CUST_INFO_SYNC.getCode(),
            FullCustSourceEnum.HK_CUST_MOBILE_CHANGE.getCode(),
            FullCustSourceEnum.HK_CUST_ID_CHANGE.getCode(),
            FullCustSourceEnum.PAGE_MENU_OPERATE.getCode(),
            FullCustSourceEnum.HBONE_CUST_MOBILE_CHANGE.getCode(),
            FullCustSourceEnum.HBONE_CUST_REAL_NAME_CHANGE.getCode(),
            FullCustSourceEnum.MGM.getCode()
    });

    /**
     * 编码
     */
    private String code;

    /**
     * 描述
     */
    private String description;

    /**
     * 对应的异常来源编码集合
     */
    private String[] sourceCodeArray;

    HkBusinessNodeEnum(String code, String description, String[] sourceCodeArray) {
        this.code = code;
        this.description = description;
        this.sourceCodeArray = sourceCodeArray;
    }

    /**
     * 根据业务节点编码获取对应的异常来源编码集合
     *
     * @param nodeCode 业务节点编码
     * @return 异常来源编码集合
     */
    public static Set<String> getSourceCodesByNodeCode(String nodeCode) {
        for (HkBusinessNodeEnum node : HkBusinessNodeEnum.values()) {
            if (node.getCode().equals(nodeCode)) {
                return new HashSet<>(Arrays.asList(node.getSourceCodeArray()));
            }
        }
        return new HashSet<>();
    }

    /**
     * 根据多个业务节点编码获取对应的异常来源编码集合
     *
     * @param nodeCodes 业务节点编码数组
     * @return 异常来源编码集合
     */
    public static Set<String> getSourceCodesByNodeCodes(String[] nodeCodes) {
        Set<String> sourceCodes = new HashSet<>();
        if (nodeCodes == null || nodeCodes.length == 0) {
            return sourceCodes;
        }

        for (String nodeCode : nodeCodes) {
            sourceCodes.addAll(getSourceCodesByNodeCode(nodeCode));
        }
        return sourceCodes;
    }

    /**
     * 根据异常来源编码获取对应的业务节点编码
     *
     * @param sourceCode 异常来源编码
     * @return 业务节点编码
     */
    public static String getNodeCodeBySourceCode(String sourceCode) {
        for (HkBusinessNodeEnum node : HkBusinessNodeEnum.values()) {
            if (Arrays.asList(node.getSourceCodeArray()).contains(sourceCode)) {
                return node.getCode();
            }
        }
        return OTHER.getCode(); // 默认返回"其他"
    }

    /**
     * 通过code获得描述
     *
     * @param code 编码
     * @return 描述
     */
    public static String getDescription(String code) {
        HkBusinessNodeEnum nodeEnum = getEnum(code);
        return nodeEnum == null ? null : nodeEnum.getDescription();
    }

    /**
     * 通过code获取枚举对象
     *
     * @param code 编码
     * @return 枚举对象
     */
    public static HkBusinessNodeEnum getEnum(String code) {
        for (HkBusinessNodeEnum node : HkBusinessNodeEnum.values()) {
            if (node.getCode().equals(code)) {
                return node;
            }
        }
        return null;
    }

    /**
     * 获取所有业务节点编码
     *
     * @return 业务节点编码列表
     */
    public static List<String> getAllNodeCodes() {
        return Arrays.stream(HkBusinessNodeEnum.values())
                .map(HkBusinessNodeEnum::getCode)
                .collect(Collectors.toList());
    }

    public String getCode() {
        return code;
    }

    public String getDescription() {
        return description;
    }

    public String[] getSourceCodeArray() {
        return sourceCodeArray;
    }
} 