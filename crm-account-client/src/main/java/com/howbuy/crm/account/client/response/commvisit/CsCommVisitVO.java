/**
 * Copyright (c) 2024, <PERSON><PERSON><PERSON><PERSON> HOWBUY INVESTMENT MANAGEMENT Co., Ltd.
 * All right reserved.
 * <p>
 * THIS IS UNPUBLISHED PROPRIETARY SOURCE CODE OF HOWBUY INVESTMENT
 * MANAGEMENT CO., LTD.  THE CONTENTS OF THIS FILE MAY NOT BE DISCLOSED
 * TO THIRD PARTIES, COPIED OR DUPLICATED IN ANY FORM, IN WHOLE OR IN PART,
 * WITHOUT THE PRIOR WRITTEN PERMISSION OF HOWBUY INVESTMENT MANAGEMENT
 * CO., LTD.
 */
package com.howbuy.crm.account.client.response.commvisit;

import java.io.Serializable;

/**
 * <AUTHOR>
 * @description: (请在此添加描述)
 * @date 2024/10/24 10:18
 * @since JDK 1.8
 */
public class CsCommVisitVO implements Serializable {

    private static final long serialVersionUID = 6671392096680244045L;

    /**
     * 投顾客户号
     */
    private String conscustno;

    /**
     * 沟通及拜访内容
     */
    private String commcontent;

    /**
     * 拜访方式
     */
    private String visittype;

    /**
     * 创建人
     */
    private String creator;

    /**
     * 创建日期 yyyyMMddHHmmss
     */
    private String creDtStr;

    public String getConscustno() {
        return conscustno;
    }

    public void setConscustno(String conscustno) {
        this.conscustno = conscustno;
    }

    public String getCommcontent() {
        return commcontent;
    }

    public void setCommcontent(String commcontent) {
        this.commcontent = commcontent;
    }

    public String getVisittype() {
        return visittype;
    }

    public void setVisittype(String visittype) {
        this.visittype = visittype;
    }

    public String getCreator() {
        return creator;
    }

    public void setCreator(String creator) {
        this.creator = creator;
    }

    public void setCreDtStr(String creDtStr) {
        this.creDtStr = creDtStr;
    }

    public String getCreDtStr() {
        return creDtStr;
    }
}
