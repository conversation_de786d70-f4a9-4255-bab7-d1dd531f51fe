/**
 * Copyright (c) 2023, <PERSON><PERSON><PERSON><PERSON> HOWBUY INVESTMENT MANAGEMENT Co., Ltd.
 * All right reserved.
 * <p>
 * THIS IS UNPUBLISHED PROPRIETARY SOURCE CODE OF HOWBUY INVESTMENT
 * MANAGEMENT CO., LTD.  THE CONTENTS OF THIS FILE MAY NOT BE DISCLOSED
 * TO THIRD PARTIES, COPIED OR DUPLICATED IN ANY FORM, IN WHOLE OR IN PART,
 * WITHOUT THE PRIOR WRITTEN PERMISSION OF HOWBUY INVESTMENT MANAGEMENT
 * CO., LTD.
 */
package com.howbuy.crm.account.client.request;

import lombok.Data;

/**
 * @description: (分页通用 对象  )
 * <AUTHOR>
 * @date 2023/12/11 13:20
 * @since JDK 1.8
 */
@Data
public class PageRequest extends Request {


    /**
     * 页号
     */
    private int page = 1;

    /**
     * 每页大小
     */
    private int rows = 20;


}