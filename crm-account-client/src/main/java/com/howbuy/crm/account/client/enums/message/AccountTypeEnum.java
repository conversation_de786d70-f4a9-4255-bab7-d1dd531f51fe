package com.howbuy.crm.account.client.enums.message;

/**
 * @description:( AccountCenterMessage AccountType类型：1-一账通号  2-香港客户号 )
 * @param
 * @return
 * @author: haoran.zhang
 * @date: 2023/12/8 17:46
 * @since JDK 1.8
 */
public enum AccountTypeEnum {

	/**
	 * 1-一账通
	 */
	HBONE("HBONE", "一账通号"),
	/**
	 * 2-香港客户
	 */
	HK("HK", "香港客户号");

	/**
	 * 编码
	 */
	private String code;

	/**
	 * 描述
	 */
	private String description;

	AccountTypeEnum(String code, String description) {
		this.code = code;
		this.description = description;
	}

	/**
	 * 通过code获得
	 *
	 * @param code 系统返回参数编码
	 * @return description 描述
	 */
	public static String getDescription(String code) {
		AccountTypeEnum statusEnum = getEnum(code);
		return statusEnum == null ? null : statusEnum.getDescription();
	}

	/**
	 * 通过code直接返回 整个枚举类型
	 *
	 * @param code 系统返回参数编码
	 * @return BaseConstantEnum
	 */
	public static AccountTypeEnum getEnum(String code) {
		for (AccountTypeEnum statusEnum : AccountTypeEnum.values()) {
			if (statusEnum.getCode().equals(code)) {
				return statusEnum;
			}
		}
		return null;
	}


	public String getCode() {
		return code;
	}


	public String getDescription() {
		return description;
	}

}
