package com.howbuy.crm.account.client.cs;

/**
 * @description:(客服相关-无线渠道（1:掌基、2:臻财、3:M站） )
 * @param
 * @return
 * @author: haoran.zhang
 * @date: 2023/12/8 17:46
 * @since JDK 1.8
 */
public enum WirelessChannelEnum {

	/**
	 * 掌基
	 */
	APP("1","掌基"),
	/**
	 * 臻财
	 */
	ZHENCAI("2","臻财"),
	/**
	 * M站
	 */
	M_STATION("3","M站")



	//待补充 。。。。。。
	;

	/**
	 * 编码
	 */
	private String code;

	/**
	 * 描述
	 */
	private String description;

	WirelessChannelEnum(String code, String description) {
		this.code = code;
		this.description = description;
	}

	/**
	 * 通过code获得
	 *
	 * @param code 系统返回参数编码
	 * @return description 描述
	 */
	public static String getDescription(String code) {
		WirelessChannelEnum statusEnum = getEnum(code);
		return statusEnum == null ? null : statusEnum.getDescription();
	}

	/**
	 * 通过code直接返回 整个枚举类型
	 *
	 * @param code 系统返回参数编码
	 * @return BaseConstantEnum
	 */
	public static WirelessChannelEnum getEnum(String code) {
		for (WirelessChannelEnum statusEnum : WirelessChannelEnum.values()) {
			if (statusEnum.getCode().equals(code)) {
				return statusEnum;
			}
		}
		return null;
	}


	public String getCode() {
		return code;
	}


	public String getDescription() {
		return description;
	}

}
