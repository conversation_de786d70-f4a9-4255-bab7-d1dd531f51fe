/**
 * Copyright (c) 2024, <PERSON><PERSON><PERSON><PERSON> HOWBUY INVESTMENT MANAGEMENT Co., Ltd.
 * All right reserved.
 * <p>
 * THIS IS UNPUBLISHED PROPRIETARY SOURCE CODE OF HOWBUY INVESTMENT
 * MANAGEMENT CO., LTD.  THE CONTENTS OF THIS FILE MAY NOT BE DISCLOSED
 * TO THIRD PARTIES, COPIED OR DUPLICATED IN ANY FORM, IN WHOLE OR IN PART,
 * WITHOUT THE PRIOR WRITTEN PERMISSION OF HOWBUY INVESTMENT MANAGEMENT
 * CO., LTD.
 */
package com.howbuy.crm.account.client.response.custinfo;

import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;

import java.io.Serializable;
import java.util.Date;
import java.util.List;

/**
 * @description: 香港客户全量的字段属性
 * <AUTHOR>
 * @date 2024/1/11 15:04
 * @since JDK 1.8
 */

@Data
public class HkAcctCustFullInfoVO implements Serializable {

    private static final long serialVersionUID = 2524452536403507531L;

    /**
     * 香港客户号
     */
    private String hkCustNo;
    /**
     * EBROKER_ID
     */
    private String ebrokerId;
    /**
     * 客户姓名
     */
    private String custName;
    /**
     * 证件号码摘要
     */
    private String idNoDigest;
    /**
     * 证件号码掩码
     */
    private String idNoMask;
    /**
     * 证件类型
     */
    private String idType;
    /**
     * 证件有效期
     */
    private String idValidityEnd;
    /**
     * 证件是否长期有效0-否 1-是
     */
    private String idAlwaysValidFlag;
    /**
     * 证件图片上传状态0-未上传 1-已上传
     */
    private String idImageUploadStatus;
    /**
     * 国籍
     */
    private String nationality;
    /**
     * 出生日期
     */
    private String birthday;
    /**
     * 性别 0-女，1-男，2-非自然人
     */
    private String gender;
    /**
     * 职业
     */
    private String vocation;
    /**
     * 投资者类型0-机构,1-个人,2-产品户
     */
    private String invstType;
    /**
     * 手机号码区号
     */
    private String mobileAreaCode;
    /**
     * 手机号码摘要
     */
    private String mobileDigest;
    /**
     * 手机号码掩码
     */
    private String mobileMask;
    /**
     * 手机验证状态0:未验证 1:已验证
     */
    private String mobileVerifyStatus;
    /**
     * 电子邮箱摘要
     */
    private String emailDigest;
    /**
     * 电子邮箱掩码
     */
    private String emailMask;
    /**
     * 邮箱验证状态0:未验证 1:已验证
     */
    private String emailVerifyStatus;
    /**
     * 客户状态0-正常 1-注销 2-休眠 3-注册 4-开户申请成功
     */
    private String custState;
    /**
     * 客户登录密码状态0正常状态 1重置状态 2-未设置
     */
    private String custLoginPasswdType;
    /**
     * 客户交易密码状态0正常状态 1重置状态 2-未设置
     */
    private String custTxPasswdType;
    /**
     * 开户日期
     */
    private String openDate;
    /**
     * 销户日期
     */
    private String closeDate;
    /**
     * 客户中文名
     */
    private String custChineseName;
    /**
     * 公司注册国家
     */
    private String companyRegCountry;
    /**
     * 公司注册日期
     */
    private String companyRegDate;
    /**
     * 业务性质
     */
    private String natureOfBusiness;
    /**
     * 营业登记编号
     */
    private String businessRegistrationNumber;
    /**
     * 投资者资质PRO-投资者资质专业/NORMAL-投资者资质普通
     */
    private String investorQualification;
    /**
     * 投资者资质认证日期
     */
    private String investorQualificationDate;

    /**
     * 证件所属国家/地区代码
     */
    private String idSignAreaCode;

    /**
     * 注册日期
     */
    private String registerDt;

    /**
     * 开户方式 0-线下 1-线上
     */
    private String openType;

    /**
     * 中文姓
     */
    private String cnSurname;

    /**
     * 中文名
     */
    private String cnGivenName;

    /**
     * 英文名称
     */
    private String custEnName;

    /**
     * 英文姓
     */
    private String enSurname;

    /**
     * 英文名
     */
    private String enGivenName;

    /**
     * 曾用中文名
     */
    private String cnFormerName;

    /**
     * 曾用英文名
     */
    private String enFormerName;

    /**
     * 婚姻状况
     */
    private String marriageStat;

    /**
     * 婚姻状况说明
     */
    private String marriageStatDesc;

    /**
     * 教育程度
     */
    private String eduLevel;

    /**
     * 结单接收电子邮箱摘要
     */
    private String statementEmailDigest;

    /**
     * 结单接收电子邮箱掩码
     */
    private String statementEmailMask;

    /**
     * 现居地址-国家/地区代码
     */
    private String residenceCountryCode;

    /**
     * 现居地址-省代码
     */
    private String residenceProvCode;

    /**
     * 现居地址-市代码
     */
    private String residenceCityCode;

    /**
     * 现居地址-县代码
     */
    private String residenceCountyCode;

    /**
     * 现居地址-中文详细地址摘要
     */
    private String residenceCnAddrDigest;

    /**
     * 现居地址-中文详细地址掩码
     */
    private String residenceCnAddrMask;

    /**
     * 现居地址（英文）-国家/地区
     */
    private String residenceEnCountry;

    /**
     * 现居地址（英文）-省
     */
    private String residenceEnProv;

    /**
     * 现居地址（英文）-市
     */
    private String residenceEnCity;

    /**
     * 现居地址（英文）-县
     */
    private String residenceEnCounty;

    /**
     * 现居地址（英文）-城/镇
     */
    private String residenceTown;

    /**
     * 现居地址（英文）-省/州
     */
    private String residenceState;

    /**
     * 现居地址-英文详细地址摘要
     */
    private String residenceEnAddrDigest;

    /**
     * 现居地址-英文详细地址掩码
     */
    private String residenceEnAddrMask;

    /**
     * 通讯地址-国家/地区代码
     */
    private String mailingCountryCode;

    /**
     * 通讯地址-省代码
     */
    private String mailingProvCode;

    /**
     * 通讯地址-市代码
     */
    private String mailingCityCode;

    /**
     * 通讯地址-县代码
     */
    private String mailingCountyCode;

    /**
     * 通讯地址-中文详细地址摘要
     */
    private String mailingCnAddrDigest;

    /**
     * 通讯地址-中文详细地址掩码
     */
    private String mailingCnAddrMask;
    /**
     * 通讯地址（英文）-国家/地区
     */
    private String mailingEnCountry;

    /**
     * 通讯地址（英文）-省
     */
    private String mailingEnProv;

    /**
     * 通讯地址（英文）-市
     */
    private String mailingEnCity;

    /**
     * 通讯地址（英文）-县
     */
    private String mailingEnCounty;

    /**
     * 通讯地址（英文）-城/镇
     */
    private String mailingTown;

    /**
     * 通讯地址（英文）-省/州
     */
    private String mailingState;

    /**
     * 通讯地址-英文详细地址摘要
     */
    private String mailingEnAddrDigest;

    /**
     * 通讯地址-英文详细地址掩码
     */
    private String mailingEnAddrMask;

    /**
     * 出生地-国家/地区代码
     */
    private String birthCountryCode;

    /**
     * 出生地-省代码
     */
    private String birthProvCode;

    /**
     * 出生地-市代码
     */
    private String birthCityCode;

    /**
     * 出生地-县代码
     */
    private String birthCountyCode;

    /**
     * 出生地（英文）-城/镇
     */
    private String birthTown;

    /**
     * 出生地（英文）-省/州
     */
    private String birthState;

    /**
     * 出生地-详细地址摘要
     */
    private String birthAddrDigest;

    /**
     * 出生地-详细地址掩码
     */
    private String birthAddrMask;

    /**
     * 资产证明日期
     */
    private String assetCertDate;
    /**
     * 资产证明有效期
     */
    private String assetCertExpiredDate;

    /**
     * 财富来源list 01-薪金及/或花红 02-业务收入 03-退休金 04-礼物 05-储蓄 06-投资回报 07-遗赠 -08其他
     */
    private List<String> wealthSources;

    /**
     * 财富来源补充说明
     */
    private String wealthSourceDesc;

    /**
     * 资金来源地list
     */
    private List<String> fundSourceCountries;

    /**
     * 资金来源地补充说明
     */
    private String fundSourceCountryDesc;

    /**
     * 创建时间戳
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date stimestamp;

    /**
     * 更新时间戳
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date updatedStimestamp;


}