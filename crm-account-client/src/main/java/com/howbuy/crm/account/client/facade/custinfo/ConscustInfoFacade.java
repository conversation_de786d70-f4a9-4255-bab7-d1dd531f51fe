/**
 * Copyright (c) 2024, <PERSON><PERSON><PERSON><PERSON> HOWBUY INVESTMENT MANAGEMENT Co., Ltd.
 * All right reserved.
 * <p>
 * THIS IS UNPUBLISHED PROPRIETARY SOURCE CODE OF HOWBUY INVESTMENT
 * MANAGEMENT CO., LTD.  THE CONTENTS OF THIS FILE MAY NOT BE DISCLOSED
 * TO THIRD PARTIES, COPIED OR DUPLICATED IN ANY FORM, IN WHOLE OR IN PART,
 * WITHOUT THE PRIOR WRITTEN PERMISSION OF HOWBUY INVESTMENT MANAGEMENT
 * CO., LTD.
 */
package com.howbuy.crm.account.client.facade.custinfo;

import com.howbuy.crm.account.client.request.custinfo.*;
import com.howbuy.crm.account.client.response.PageVO;
import com.howbuy.crm.account.client.response.Response;
import com.howbuy.crm.account.client.response.consultantinfo.CmCustHisConsultantVO;
import com.howbuy.crm.account.client.response.consultantinfo.CmCustSimpleListVO;
import com.howbuy.crm.account.client.response.custinfo.CmConsCustSimpleVO;
import com.howbuy.crm.account.client.response.custinfo.CustListByConsCodeVO;
import com.howbuy.crm.account.client.response.custinfo.QueryCustIsInternalEmployeeVO;

import java.util.List;

/**
 * <AUTHOR>
 * @description: 投顾客户服务
 * @date 2024/9/9 13:28
 * @since JDK 1.8
 */
public interface ConscustInfoFacade {

    /**
     * @api {DUBBO}  com.howbuy.crm.account.client.facade.custinfo.ConscustInfoFacade.queryCustSimpleByHboneNo(request) queryCustSimpleByHboneNo()
     * @apiVersion 1.0.0
     * @apiGroup ConscustInfoFacade
     * @apiName queryCustSimpleByHboneNo()
     * @apiDescription 根据一账通号查询投顾客户简单信息
     * @apiParam (请求体) {String} hboneNo 一账通号
     * @apiParamExample 请求体示例
     * {"hboneNo":"asQy"}
     * @apiSuccess (响应结果) {String} code 状态码
     * @apiSuccess (响应结果) {String} description 描述信息
     * @apiSuccess (响应结果) {Object} data 数据封装
     * @apiSuccess (响应结果) {Array} data.simpleVOList 投顾客户信息列表
     * @apiSuccess (响应结果) {String} data.simpleVOList.conscustno 投顾客户编号
     * @apiSuccess (响应结果) {String} data.simpleVOList.idtype 证件类型
     * @apiSuccess (响应结果) {String} data.simpleVOList.custname 投资者名称
     * @apiSuccess (响应结果) {String} data.simpleVOList.invsttype 客户投资类型 0 机构客户 1 个人客户 2 产品客户
     * @apiSuccess (响应结果) {String} data.simpleVOList.hboneNo 一账通账号
     * @apiSuccess (响应结果) {String} data.simpleVOList.custstatus 投顾客户手机核查对应状态
     * @apiSuccess (响应结果) {String} data.simpleVOList.idnoDigest 证件号摘要
     * @apiSuccess (响应结果) {String} data.simpleVOList.idnoMask 证件号掩码
     * @apiSuccess (响应结果) {String} data.simpleVOList.mobileDigest 手机号摘要
     * @apiSuccess (响应结果) {String} data.simpleVOList.mobileMask 手机号掩码
     * @apiSuccess (响应结果) {String} data.simpleVOList.mobileAreaCode 手机 地区码
     * @apiSuccess (响应结果) {String} data.simpleVOList.idSignAreaCode 证件地区码
     * @apiSuccess (响应结果) {String} data.simpleVOList.nationCode 国籍
     * @apiSuccess (响应结果) {Number} data.simpleVOList.hboneTimestamp 一账通号关联时间
     * @apiSuccess (响应结果) {String} data.simpleVOList.hkcustid 客户香港id (ebrokerID)
     * @apiSuccess (响应结果) {String} data.simpleVOList.hkTxAcctNo 香港交易账号
     * @apiSuccess (响应结果) {String} data.simpleVOList.regDt 注册日期 yyyyMMDD
     * @apiSuccess (响应结果) {String} data.simpleVOList.consCode 所属投顾
     * @apiSuccessExample 响应结果示例
     * {"code":"aNotY8","data":{"simpleVOList":[{"invsttype":"Ff","idnoMask":"l","hboneTimestamp":*************,"consCode":"Ao6CkuqxMX","custstatus":"pMC8PR4","mobileAreaCode":"iWN04M","hkTxAcctNo":"Xe7B","idtype":"DhmjoSr4","idnoDigest":"wlIzJJu84","idSignAreaCode":"Wm","conscustno":"SlP","mobileDigest":"jgLOlFt","hkcustid":"Nl","custname":"BxPmNv","hboneNo":"QGZGr","mobileMask":"vS","nationCode":"W"}]},"description":"UpCFzApA"}
     */
    Response<CmCustSimpleListVO> queryCustSimpleByHboneNo(QueryCustSimpleRequest request);

    /**
     * @api {DUBBO}  com.howbuy.crm.account.client.facade.custinfo.ConscustInfoFacade.batchQueryCustSimple(request) batchQueryCustSimple()
     * @apiVersion 1.0.0
     * @apiGroup ConscustInfoFacade
     * @apiName batchQueryCustSimple()
     * @apiDescription 批量查询投顾客户简单信息
     * @apiParam (请求体) {Array} hboneNoList 一账通号列表
     * @apiParam (请求体) {Number} page 页号，从1开始
     * @apiParam (请求体) {Number} rows 每页大小
     * @apiParamExample 请求体示例
     * {
     *   "hboneNoList": ["8887775", "8887776"],
     *   "page": 1,
     *   "rows": 10
     * }
     * @apiSuccess (响应结果) {String} code 状态码
     * @apiSuccess (响应结果) {String} description 描述信息
     * @apiSuccess (响应结果) {Object} data 数据封装
     * @apiSuccess (响应结果) {Number} data.total 总记录数
     * @apiSuccess (响应结果) {Array} data.list 投顾客户信息列表
     * @apiSuccess (响应结果) {String} data.list.conscustno 投顾客户编号
     * @apiSuccess (响应结果) {String} data.list.idtype 证件类型
     * @apiSuccess (响应结果) {String} data.list.custname 投资者名称
     * @apiSuccess (响应结果) {String} data.list.invsttype 客户投资类型 0-机构客户 1-个人客户 2-产品客户
     * @apiSuccess (响应结果) {String} data.list.hboneNo 一账通账号
     * @apiSuccess (响应结果) {String} data.list.custstatus 投顾客户手机核查对应状态
     * @apiSuccess (响应结果) {String} data.list.idnoDigest 证件号摘要
     * @apiSuccess (响应结果) {String} data.list.idnoMask 证件号掩码
     * @apiSuccess (响应结果) {String} data.list.mobileDigest 手机号摘要
     * @apiSuccess (响应结果) {String} data.list.mobileMask 手机号掩码
     * @apiSuccess (响应结果) {String} data.list.mobileAreaCode 手机地区码
     * @apiSuccess (响应结果) {String} data.list.idSignAreaCode 证件地区码
     * @apiSuccess (响应结果) {String} data.list.nationCode 国籍
     * @apiSuccess (响应结果) {Number} data.list.hboneTimestamp 一账通号关联时间
     * @apiSuccess (响应结果) {String} data.list.hkcustid 客户香港id (ebrokerID)
     * @apiSuccess (响应结果) {String} data.list.hkTxAcctNo 香港交易账号
     * @apiSuccess (响应结果) {String} data.list.regDt 注册日期 yyyyMMDD
     * @apiSuccess (响应结果) {String} data.list.consCode 所属投顾
     * @apiSuccessExample 响应结果示例
     * {
     *   "code": "0",
     *   "description": "success",
     *   "data": {
     *     "total": 2,
     *     "list": [
     *       {
     *         "conscustno": "8887775",
     *         "idtype": "1",
     *         "custname": "张三",
     *         "invsttype": "1",
     *         "hboneNo": "8887775",
     *         "custstatus": "1",
     *         "idnoDigest": "123456789",
     *         "idnoMask": "123*****9",
     *         "mobileDigest": "13800138000",
     *         "mobileMask": "138****8000",
     *         "mobileAreaCode": "86",
     *         "idSignAreaCode": "110000",
     *         "nationCode": "CN",
     *         "hboneTimestamp": *************,
     *         "hkcustid": "HK8887775",
     *         "hkTxAcctNo": "HK8887775",
     *         "regDt": "********",
     *         "consCode": "CONS001"
     *       }
     *     ]
     *   }
     * }
     */
    Response<PageVO<CmConsCustSimpleVO>> batchQueryCustSimple(BatchQueryCustSimpleRequest request);

    /**
     * @api {DUBBO}  com.howbuy.crm.account.client.facade.custinfo.ConscustInfoFacade.queryCustHisConsultantList(request) queryCustconstantByHboneNo()
     * @apiVersion 1.0.0
     * @apiGroup ConscustInfoFacade
     * @apiName queryCustconstantByHboneNo()
     * @apiDescription 查询客户历史所属投顾
     * @apiParam (请求参数) {String} conscustno 投顾客户号
     * @apiParamExample 请求参数示例
     * hboneNo=p
     * @apiSuccess (响应结果) {String} code 状态码
     * @apiSuccess (响应结果) {String} description 描述信息
     * @apiSuccess (响应结果) {Object} data 数据封装
     * @apiSuccess (响应结果) {Array} data.custHisConsultantList 历史投顾列表
     * @apiSuccess (响应结果) {String} data.custHisConsultantList.conscustno 投顾客户编号
     * @apiSuccess (响应结果) {String} data.custHisConsultantList.custName 投顾客户名称
     * @apiSuccess (响应结果) {String} data.custHisConsultantList.conscode 投顾编号
     * @apiSuccess (响应结果) {String} data.custHisConsultantList.consName 投顾名称
     * @apiSuccess (响应结果) {String} data.custHisConsultantList.startDt 开始日期
     * @apiSuccess (响应结果) {String} data.custHisConsultantList.endDt 结束日期
     * @apiSuccessExample 响应结果示例
     * {"code":"nSF18lILn","data":{"conscustno":"ddXuotJ","consCode":"Pd5nBx","custName":"1Kl","startDt":"1f"},"description":"QrH0SuD"}
     */
    Response<CmCustHisConsultantVO> queryCustHisConsultantList(QueryCustHisConsultantRequest request);

    /**
     * @api {DUBBO}  com.howbuy.crm.account.client.facade.custinfo.ConscustInfoFacade.pageQueryCustSimple(request) pageQueryCustSimple()
     * @apiVersion 1.0.0
     * @apiGroup ConscustInfoFacade
     * @apiName pageQueryCustSimple()
     * @apiDescription 分页查询投顾客户简单信息
     * @apiParam (请求体) {String} conscode 投顾编号
     * @apiParam (请求体) {String} searchContent 搜索内容
     * @apiParam (请求体) {Number} page 页号
     * @apiParam (请求体) {Number} rows 每页大小
     * @apiParamExample 请求体示例
     * {"page":7591,"rows":6212,"conscode":"YAUooG","searchContent":"CmN4C0Qs8a"}
     * @apiSuccess (响应结果) {String} code 状态码
     * @apiSuccess (响应结果) {String} description 描述信息
     * @apiSuccess (响应结果) {Object} data 数据封装
     * @apiSuccess (响应结果) {Array} data.consCustSimpleVOList 客户列表
     * @apiSuccess (响应结果) {String} data.consCustSimpleVOList.conscustno 投顾客户编号
     * @apiSuccess (响应结果) {String} data.consCustSimpleVOList.idtype 证件类型
     * @apiSuccess (响应结果) {String} data.consCustSimpleVOList.custname 投资者名称
     * @apiSuccess (响应结果) {String} data.consCustSimpleVOList.invsttype 客户投资类型 0 机构客户 1 个人客户 2 产品客户
     * @apiSuccess (响应结果) {String} data.consCustSimpleVOList.hboneNo 一账通账号
     * @apiSuccess (响应结果) {String} data.consCustSimpleVOList.custstatus 投顾客户手机核查对应状态
     * @apiSuccess (响应结果) {String} data.consCustSimpleVOList.idnoDigest 证件号摘要
     * @apiSuccess (响应结果) {String} data.consCustSimpleVOList.idnoMask 证件号掩码
     * @apiSuccess (响应结果) {String} data.consCustSimpleVOList.mobileDigest 手机号摘要
     * @apiSuccess (响应结果) {String} data.consCustSimpleVOList.mobileMask 手机号掩码
     * @apiSuccess (响应结果) {String} data.consCustSimpleVOList.mobileAreaCode 手机 地区码
     * @apiSuccess (响应结果) {String} data.consCustSimpleVOList.idSignAreaCode 证件地区码
     * @apiSuccess (响应结果) {String} data.consCustSimpleVOList.nationCode 国籍
     * @apiSuccess (响应结果) {Number} data.consCustSimpleVOList.hboneTimestamp 一账通号关联时间
     * @apiSuccess (响应结果) {String} data.consCustSimpleVOList.hkcustid 客户香港id (ebrokerID)
     * @apiSuccess (响应结果) {String} data.consCustSimpleVOList.hkTxAcctNo 香港交易账号
     * @apiSuccess (响应结果) {String} data.consCustSimpleVOList.regDt 注册日期 yyyyMMDD
     * @apiSuccess (响应结果) {String} data.consCustSimpleVOList.consCode 所属投顾
     * @apiSuccessExample 响应结果示例
     * {"code":"fNdH1","data":{"consCustSimpleVOList":[{"invsttype":"UWTfHdNP7I","idnoMask":"0ZLal8X","hboneTimestamp":3539484409423,"consCode":"GtWCLotO","custstatus":"3a90g2","mobileAreaCode":"WB7pi","hkTxAcctNo":"k4JgDl6","idtype":"tpogeV98","idnoDigest":"Khf","idSignAreaCode":"9uX62G","regDt":"gVw9HjOv","conscustno":"Qa6P3C","mobileDigest":"MSod2YP","hkcustid":"5n7d0y4RDX","custname":"wrTlXRick","hboneNo":"rZllYN","mobileMask":"q2PtmOEU4n","nationCode":"Zk6QF"}]},"description":"M"}
     */
    Response<CustListByConsCodeVO> pageQueryCustSimple(PageQueryCustSimpleRequest request);


    /**
     * @api {DUBBO}  com.howbuy.crm.account.client.facade.custinfo.ConscustInfoFacade.queryCustIsInternalEmployee(request) queryCustIsInternalEmployee()
     * @apiVersion 1.0.0
     * @apiGroup ConscustInfoFacade
     * @apiName queryCustIsInternalEmployee()
     * @apiDescription 查询客户是否为公司内部员工
     * @apiParam (请求参数) {String} consCustNo 投顾客户号
     * @apiParamExample 请求参数示例
     * consCustNo=rdC
     * @apiSuccess (响应结果) {String} code 状态码
     * @apiSuccess (响应结果) {String} description 描述信息
     * @apiSuccess (响应结果) {Object} data 数据封装
     * @apiSuccess (响应结果) {String} data.internalEmployeeFlag 是否为 [公司内部员工]标识 1-是 0-否
     * @apiSuccessExample 响应结果示例
     * {"code":"c","data":{"internalEmployeeFlag":"68DVNtq"},"description":"LiRPL"}
     */
    Response<QueryCustIsInternalEmployeeVO> queryCustIsInternalEmployee(QueryCustIsInternalEmployeeRequest request);

}
