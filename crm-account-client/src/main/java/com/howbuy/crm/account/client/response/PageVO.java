package com.howbuy.crm.account.client.response;

import lombok.Getter;
import lombok.Setter;
import lombok.ToString;

import java.io.Serializable;
import java.util.ArrayList;
import java.util.List;

/**
 * <AUTHOR>
 * @description: 分页返回对象 公共vo
 * @date 2023年12月13日 19:23:17
 * @since JDK 1.8
 */
@Getter
@Setter
@ToString
public class PageVO<T> implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 当前第几页
     */
    private Integer page;
    /**
     * 单页条数
     */
    private Integer size;
    /**
     * 总条数
     */
    private Long total;
    /**
     * 数据对象列表
     */
    private List<T> rows= new ArrayList<>();
}
