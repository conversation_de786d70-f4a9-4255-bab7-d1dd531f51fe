/**
 * Copyright (c) 2023, <PERSON>gH<PERSON> HOWBUY INVESTMENT MANAGEMENT Co., Ltd.
 * All right reserved.
 * <p>
 * THIS IS UNPUBLISHED PROPRIETARY SOURCE CODE OF HOWBUY INVESTMENT
 * MANAGEMENT CO., LTD.  THE CONTENTS OF THIS FILE MAY NOT BE DISCLOSED
 * TO THIRD PARTIES, COPIED OR DUPLICATED IN ANY FORM, IN WHOLE OR IN PART,
 * WITHOUT THE PRIOR WRITTEN PERMISSION OF HOWBUY INVESTMENT MANAGEMENT
 * CO., LTD.
 */
package com.howbuy.crm.account.client.request.custinfo;

import com.howbuy.commons.validator.MyValidation;
import com.howbuy.commons.validator.util.ValidatorTypeEnum;
import com.howbuy.crm.account.client.request.PageRequest;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.util.List;

/**
 * @description: (客户海外储蓄罐协议流水信息 查询请求)
 * <AUTHOR>
 * @date 2024年8月12日
 * @since JDK 1.8
 */
@Data
@EqualsAndHashCode(callSuper = true)
public class HkPiggyAgreementDealRequest  extends PageRequest {

    /**
     * 香港交易账号
     */
    @MyValidation(validatorType = ValidatorTypeEnum.String, fieldName = "香港客户号", isRequired = true)
    private String hkTxAcctNo;
    /**
     * 支持多个，英文逗号隔开，不传即所有
     * 1-柜台；4-Wap（小程序）；9-CRM-PC；10-CRM移动端；11-APP；12-H5
     */
    private List<String> tradeChannels;
    /**
     * 支持多个，英文逗号隔开，不传即所有
     * 0-无需审核、2-等待复核、3-审核通过、4-审核不通过
     */
    private List<String> busiCodes;
    /**
     * 审核状态
     * {@link com.howbuy.crm.account.client.enums.hkcustinfo.HkAcctTxChkFlagEnum}
     */
    private List<String> txChkFlags;
    /**
     * yyyyMMdd
     * 开始更新日期
     */
    private String startUpdateDt;
    /**
     * yyyyMMdd
     * 结束更新日期
     */
    private String endUpdateDt;


}