/**
 * Copyright (c) 2024, <PERSON>gH<PERSON> HOWBUY INVESTMENT MANAGEMENT Co., Ltd.
 * All right reserved.
 * <p>
 * THIS IS UNPUBLISHED PROPRIETARY SOURCE CODE OF HOWBUY INVESTMENT
 * MANAGEMENT CO., LTD.  THE CONTENTS OF THIS FILE MAY NOT BE DISCLOSED
 * TO THIRD PARTIES, COPIED OR DUPLICATED IN ANY FORM, IN WHOLE OR IN PART,
 * WITHOUT THE PRIOR WRITTEN PERMISSION OF HOWBUY INVESTMENT MANAGEMENT
 * CO., LTD.
 */
package com.howbuy.crm.account.client.response.custinfo;

import lombok.Data;

import java.io.Serializable;

/**
 * @description: (客户关键属性信息)
 * <AUTHOR>
 * @date 2024/1/17 14:19
 * @since JDK 1.8
 */
@Data
public class CustKeyAttrInfo implements Serializable {


    /**
     * 香港客户号
     */
    private String hkTxAcctNo;


    /**
     * 一账通号
     */
    private String hboneNo;



    /**
     * 客户名称
     */
    private String custName;


    /**
     * 客户投资者类型
     */
    private String investType;


    /**
     * 客户状态 code   注意香港客户状态、投顾客户状态、一账通客户状态 不一致
     * 其中： 香港客户状态-   客户状态 0-正常 1-注销 2-休眠 3-注册4-开户申请成功
     */
    private String  custStatus;

    /**
     * 客户状态 描述
     */
    private String custStatusDesc;

    /**
     * 客户是否实名： 1-已实名  0-未实名
     */
    private String isRealName;

    /**
     * 是否开户 1-已开户 0-未开户
     */
    private String openAcct;


    /***********************证件信息 相关对象 *********************************************/

    /**
     *证件号码
     */
    private String idNoDigest;

    /**
     * 证件地区码-身份证签发地区编码
     */
    private String idSignAreaCode;

    /**
     * 证件类型
     */
    private String idType;

    /**
     * 证件号码脱敏
     */
    private String idNoMask;


    /**
     * 证件类型描述
     */
    private String idTypeDesc;




    /***********************手机信息 相关对象 *********************************************/

    /**
     * 手机号码区号
     */
    private String mobileAreaCode;
    /**
     * 手机号码
     */
    private String mobileDigest;

    /**
     * 手机号码脱敏
     */
    private String mobileMask;


}