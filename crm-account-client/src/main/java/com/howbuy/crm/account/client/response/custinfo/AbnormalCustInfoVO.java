package com.howbuy.crm.account.client.response.custinfo;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.howbuy.crm.account.client.enums.custinfo.HkAcctCustStatusEnum;
import lombok.Data;

import java.io.Serializable;
import java.util.Date;
import java.util.List;

/**
 * @description: (香港/一账通 客户异常信息表)
 * <AUTHOR>
 * @date 2023/12/11 10:57
 * @since JDK 1.8
 */

/**
    * 香港/一账通 客户异常信息表
    */
@Data
public class AbnormalCustInfoVO implements Serializable {

    private static final long serialVersionUID = 360919284646542496L;


    /**
    * 异常客户数据ID
    */
    private String id;

    /**
    * 消息通知的clientId
    */
    private String messageClientId;

    /**
    * 香港客户号
    */
    private String hkTxAcctNo;

    /**
    * 客户姓名
    */
    private String custName;

    /**
    * 投资者类型
    */
    private String investType;

    /**
    * 手机地区码
    */
    private String mobileAreaCode;

    /**
    * 手机号摘要
    */
    private String mobileDigest;

    /**
    * 手机号掩码
    */
    private String mobileMask;

    /**
    * 手机号密文
    */
    private String mobileCipher;

    /**
    * 证件地区码
    */
    private String idSignAreaCode;

    /**
    * 证件类型
    */
    private String idType;

    /**
    * 证件号码摘要
    */
    private String idNoDigest;

    /**
    * 证件号码掩码
    */
    private String idNoMask;

    /**
    * 证件号码密文
    */
    private String idNoCipher;

    /**
    * 一账通号
    */
    private String hboneNo;

    /**
    * 异常来源：1-香港注册2-香港开户7-好买开户 [暂时同于  7-分销开户]3-香港客户信息同步4-香港客户号/一账通绑定5-一账通实名
    */
    private String abnormalSource;

    /**
    * 异常描述，按异常类别汇总：1-匹配到多个投顾客户号【香港注册、香港开户】2-匹配到的投顾客户号已被占用 【香港注册】3-手机号相同，证件类型/证件号不匹配 【香港开户】4-证件相同，但手机号不匹配【香港开户】5-CRM重复客户预警【香港客户信息同步】6-同时绑定香港客户号时，投顾客户号/香港客户号被占用 【好买开户、一账通实名】7-香港开户证件与一账通证件不一致 【香港开户】8-两边绑定的投顾客户号不一致【香港客户号/一账通绑定】
    */
    private String abnormalSceneType;


    /**
     * 操作通道 1-MQ  2-菜单页面
     */
    private String operateChannel;

    /**
    * 创建人
    */
    private String creator;

    /**
    * 创建时间
    */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date createTimestamp;

    /**
    * 修改人
    */
    private String modifier;

    /**
    * 修改时间
    */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date modifyTimestamp;

    /**
    * 记录有效状态（1-正常  0-删除）
    */
    private String recStat;

    /**
    * 处理状态：0-未处理 1-已处理 2-无需处理
    */
    private String dealStatus;

    /**
    * 处理人
    */
    private String dealOperator;

    /**
    * 处理意见
    */
    private String dealRemark;



    /**
     * 香港账户侧 客户信息
     */
    private CustKeyAttrInfo hkSideInfo;


    /**
     * 一账通账户侧 客户信息
     */
    private CustKeyAttrInfo hboneSideInfo;

    /**
     * crm已关联的 客户号
     */
    private String relatedCustNo;

    /**
    * 处理时间
    */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date dealTimestamp;


    /**
     * 关联客户列表
     */
    private List<AbnormalRelatedCustVO> relatedList;


    /**
     * 证件类型描述
     */
    private String  idTypeDesc;

    /**
     * 异常详细描述 （业务描述）
     */
    private String abnormalSceneDesc;


    /**
     * 具体需求：http://dms.intelnal.howbuy.com/pages/viewpage.action?pageId=94532024#id-39%E3%80%81%E9%A6%99%E6%B8%AF%E8%B4%A6%E6%88%B7%E6%B6%88%E6%81%AF%E5%BC%82%E5%B8%B8%E5%A4%84%E7%90%86%E6%94%B9%E9%80%A0CRM-2.2%E3%80%81%E3%80%90%E4%BC%98%E5%8C%96%E3%80%91%E9%A6%99%E6%B8%AFCRM%E5%BC%80%E6%88%B7%E5%A4%84%E7%90%86%E9%80%BB%E8%BE%91@CRM
     *
     *
     * 枚举值 异常场景描述：线上、线下、其他
     */
    private String hkAbnormalSceneDesc;

    /**
     * 枚举值 香港业务节点描述：注册、开户、其他
     */
    private String hkBusinessNodeDesc;

    /**
     * 异常等级描述
     */
    private String abnormalLevelDesc;

    /**
     * 已关联的 crm投顾客户号，所属投顾
     */
    private String relatedConsCode;

    /**
     * rocketMq 消息ID(仅由账户中心消息产生的异常客户 有值)
     */
    private String messageId;

}