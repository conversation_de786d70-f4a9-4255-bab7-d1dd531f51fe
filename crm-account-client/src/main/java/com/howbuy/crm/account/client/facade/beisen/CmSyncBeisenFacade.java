/**
 * Copyright (c) 2024, <PERSON><PERSON><PERSON><PERSON> HOWBUY INVESTMENT MANAGEMENT Co., Ltd.
 * All right reserved.
 * <p>
 * THIS IS UNPUBLISHED PROPRIETARY SOURCE CODE OF HOWBUY INVESTMENT
 * MANAGEMENT CO., LTD.  THE CONTENTS OF THIS FILE MAY NOT BE DISCLOSED
 * TO THIRD PARTIES, COPIED OR DUPLICATED IN ANY FORM, IN WHOLE OR IN PART,
 * WITHOUT THE PRIOR WRITTEN PERMISSION OF HOWBUY INVESTMENT MANAGEMENT
 * CO., LTD.
 */
package com.howbuy.crm.account.client.facade.beisen;

import com.howbuy.crm.account.client.request.beisen.CmSyncBeisenRequest;
import com.howbuy.crm.account.client.response.Response;

import java.util.List;

/**
 * @description: (北森同步花名册接口)
 * <AUTHOR>
 * @date 2024/11/14 17:58
 * @since JDK 1.8
 */
public interface CmSyncBeisenFacade {

    /**
     * @api {DUBBO} com.howbuy.crm.account.client.facade.beisen.CmSyncBeisenFacade.syncConsultantExpByBeisenUserNoFacade(request)  通过userno同步北森数据
     * @apiVersion 1.0.0
     * @apiGroup beisen
     * @apiName syncConsultantExpByBeisenUserNoFacade
     * @apiParam (请求参数) {String} userNo 工号
     * @apiParam (请求参数) {String} startDate 开始日期
     * @apiParam (请求参数) {String} endDate 结束日期
     * @apiParamExample 请求参数示例
     * endDate=3yO3Podf&userNo=5VDfEKx&startDate=vZbeFa7I0
     * @apiSuccess (响应结果) {Array} response
     * @apiSuccessExample 响应结果示例
     * {"code":"m","data":["VGxi6GIQ"],"description":"laZi"}
     */
    Response<List<String>> syncConsultantExpByBeisenUserNoFacade(CmSyncBeisenRequest request);

    /**
     * @api {DUBBO} com.howbuy.crm.account.client.facade.beisen.CmSyncBeisenFacade.syncConsultantExpByBeisenUserNosFacade(requestList)   通过userno集合同步北森数据
     * @apiVersion 1.0.0
     * @apiGroup beisen
     * @apiName syncConsultantExpByBeisenUserNosFacade
     * @apiParam (请求参数) {String} userNo 工号
     * @apiParam (请求参数) {String} startDate 开始日期
     * @apiParam (请求参数) {String} endDate 结束日期
     * @apiParamExample 请求参数示例
     * endDate=V&userNo=t6WWZ&startDate=b
     * @apiSuccess (响应结果) {Array} response
     * @apiSuccessExample 响应结果示例
     * {"code":"m","data":["dxAPbjAlC"],"description":"laZi"}
     */
    Response<List<String>> syncConsultantExpByBeisenUserNosFacade(List<CmSyncBeisenRequest> requestList);

    /**
     * @api {DUBBO} com.howbuy.crm.account.client.facade.beisen.CmSyncBeisenFacade.syncConsultantExpByCmBeisenDateTimeFacade(startDate, endDate)  通过时间段同步北森数据
     * @apiVersion 1.0.0
     * @apiGroup beisen
     * @apiName syncConsultantExpByCmBeisenDateTimeFacade
     * @apiParam (请求参数) {String} startDate
     * @apiParam (请求参数) {String} endDate
     * @apiParamExample 请求参数示例
     * endDate=EHeLoQH8&startDate=X
     * @apiSuccess (响应结果) {Array} response
     * @apiSuccessExample 响应结果示例
     * {"code":"m","data":["69"],"description":"laZi"}
     */
    Response<List<String>> syncConsultantExpByCmBeisenDateTimeFacade(String startDate, String endDate);


    /**
     * @api {DUBBO} com.howbuy.crm.account.client.facade.beisen.CmSyncBeisenFacade.execSyncBeisenToCrmByJobFacade()  定时调度北森同步花名册
     * @apiVersion 1.0.0
     * @apiGroup beisen
     * @apiName execSyncBeisenToCrmByJobFacade
     * @apiSuccess (响应结果) {Array} response
     * @apiSuccessExample 响应结果示例
     * {"code":"m","data":["yU0qD"],"description":"laZi"}
     */
    Response<List<String>> execSyncBeisenToCrmByJobFacade();
}