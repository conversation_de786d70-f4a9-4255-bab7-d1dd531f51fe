package com.howbuy.crm.account.client.enums.hkcustinfo;

/**
 * @description: 香港客户财富来源枚举
 * <AUTHOR>
 * @date 2024/01/05 18:06
 * @since JDK 1.8
 */
public enum HkCustWealthSourceEnum {

	/**
	 * 01-薪金及/或花红
	 */
	BAR("01", "薪金及/或花红"),

	/**
	 * 02-业务收入
	 */
	WEB("02", "业务收入"),

	/**
	 * 03-退休金
	 */
	PHONE("03", "退休金"),

	/**
	 * 04-礼物
	 */
	WAP("04", "礼物"),

	/**
	 * 05-储蓄
	 */
	PIGGY("05", "储蓄"),

	/**
	 * 06-投资回报
	 */
	CRM_PC("06", "投资回报"),

	/**
	 * 07-遗赠
	 */
	CRM_MOBILE("07", "遗赠"),

	/**
	 * 08-其他
	 */
	APP("08", "其他"),
	;


	/**
	 * 编码
	 */
	private String code;

	/**
	 * 描述
	 */
	private String description;

	 HkCustWealthSourceEnum(String code, String description) {
		this.code = code;
		this.description = description;
	}


	/**
	 * 通过code获得
	 * 
	 * @param code
	 *            系统返回参数编码
	 * @return description 描述
	 */
	public static String getDescription(String code) {
		HkCustWealthSourceEnum statusEnum=getEnum(code);
		return statusEnum==null?null :statusEnum.getDescription();
	}

	/**
	 * 通过code直接返回 整个枚举类型
	 * 
	 * @param code
	 *            系统返回参数编码
	 * @return PreOccupyTypeEnum
	 */
	public static HkCustWealthSourceEnum getEnum(String code) {
		for(HkCustWealthSourceEnum statusEnum : HkCustWealthSourceEnum.values()){
			if(statusEnum.getCode().equals(code)){
				return statusEnum;
			}
		}
		return null;
	}




	public String getCode() {
		return code;
	}


	public String getDescription() {
		return description;
	}


}
