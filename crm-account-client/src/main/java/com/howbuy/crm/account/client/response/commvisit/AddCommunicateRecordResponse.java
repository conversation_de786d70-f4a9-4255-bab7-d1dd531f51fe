package com.howbuy.crm.account.client.response.commvisit;

import lombok.Getter;
import lombok.Setter;

import java.io.Serializable;

/**
 * @description: 新增客户沟通记录响应对象
 * <AUTHOR>
 * @date 2024-04-07 12:05:00
 * @since JDK 1.8
 */
@Getter
@Setter
public class AddCommunicateRecordResponse implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 沟通记录ID
     */
    private String communicateId;

    /**
     * 拜访纪要ID
     * 无拜访纪要时为空
     */
    private String visitMinutesId;
} 