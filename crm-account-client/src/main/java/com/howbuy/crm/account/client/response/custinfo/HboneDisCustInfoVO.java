package com.howbuy.crm.account.client.response.custinfo;


import lombok.Data;

import java.io.Serializable;
import java.util.Date;

/**
 * 一账通账户中心 侧  [分销层]客户详细 信息属性
 * 来源：DisCustInfoBean
 */
@Data
public class HboneDisCustInfoVO implements Serializable {

    private static final long serialVersionUID = -2151662062799443039L;

    private String disCustId;
    private String disName;
    private String disCode;
    private String postCode;
    private String addrDigest;
    private String addrMask;
    private String telNoDigest;
    private String telNoMask;
    private String emailDigest;
    private String emailMask;
    private String vocation;
    private String nationality;
    private String fundManDlvyMode;
    private String fundManDlvyType;
    private String agentDlvyType;
    private String agentDlvyMode;
    private String linkMan;
    private String linkIdType;
    private String linkIdNoDigest;
    private String linkIdNoMask;
    private String linkTelDigest;
    private String linkTelMask;
    private String custRiskLevel;
    private String riskSurveyDt;
    private String actualController;
    private String controllerIdNoDigest;
    private String controllerIdNoMask;
    private String controllerIdType;
    private String controllerIdValidityEnd;
    private String controllerIdAlwaysValidFlag;
    private String mobileDigest;
    private String mobileMask;
    private String residenceCountry;
    private String provCode;
    private String cityCode;
    private String countyCode;
    private String mobileVrfyStat;
    private Date mobileVrfyStimestamp;
    private String eduLevel;
    private String incLevel;
    private String collectProtocolMethod;
    private String activeStat;
    private String regCountry;
    private String regProvCode;
    private String regCityCode;
    private String custType;
    private String property;
    private String qualification;
    private String businessScope;
    private String investorType;
    private String signFlag;
    private String fundFlag;
    private String qualifyFlag;
    private String company;
    private String officeTelNo;
    private String fax;
    private String hasCustLoginPasswd;
    private String hasCustTxPasswd;



}
