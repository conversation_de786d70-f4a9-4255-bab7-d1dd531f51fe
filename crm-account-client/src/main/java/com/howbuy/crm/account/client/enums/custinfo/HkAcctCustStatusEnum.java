package com.howbuy.crm.account.client.enums.custinfo;

/**
 * @description:(香港客户状态  0-正常 1-注销 2-休眠 3-注册4-开户申请成功)
 * @param
 * @return
 * @author: haoran.zhang
 * @date: 2023/12/8 20:30
 * @since JDK 1.8
 */
public enum HkAcctCustStatusEnum {
    /**
     * 0-正常
     */
    NORMAL("0", "正常"),

    /**
     * 1-注销
     */
    CANCEL("1", "注销"),


    /**
     * 2-休眠
     */
    DORMANT("2", "休眠"),

    /**
     * 3-注册
     */
    REGISTER("3", "注册"),

    /**
     * 4-开户申请成功
     */
    OPEN_ACCOUNT_APPLY_SUCCESS("4", "开户申请成功")
  ;



    /**
     * 编码
     **/
    private String code;
    /**
     * 描述
     * */
    private String description;

    HkAcctCustStatusEnum(String code, String description){
        this.code=code;
        this.description=description;
    }

    /**
     * 通过code获得
     * @param code	系统返回参数编码
     * @return description 描述
     */
    public static String getDescription(String code){
        HkAcctCustStatusEnum  statusEnum=getEnum(code);
        return  statusEnum==null?null:statusEnum.getDescription();
    }

    /**
     * 通过code直接返回 整个枚举类型
     * @param code 系统返回参数编码
     * @return BaseConstantEnum
     */
    public static HkAcctCustStatusEnum getEnum(String code){
        for(HkAcctCustStatusEnum b: HkAcctCustStatusEnum.values()){
            if(b.getCode().equals(code)){
                return b;
            }
        }
        return null;
    }

    public String getCode() {
        return code;
    }


    public String getDescription() {
        return description;
    }
}
