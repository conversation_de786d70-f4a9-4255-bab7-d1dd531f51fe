/**
 * Copyright (c) 2024, <PERSON>gH<PERSON> HOWBUY INVESTMENT MANAGEMENT Co., Ltd.
 * All right reserved.
 * <p>
 * THIS IS UNPUBLISHED PROPRIETARY SOURCE CODE OF HOWBUY INVESTMENT
 * MANAGEMENT CO., LTD.  THE CONTENTS OF THIS FILE MAY NOT BE DISCLOSED
 * TO THIRD PARTIES, COPIED OR DUPLICATED IN ANY FORM, IN WHOLE OR IN PART,
 * WITHOUT THE PRIOR WRITTEN PERMISSION OF HOWBUY INVESTMENT MANAGEMENT
 * CO., LTD.
 */
package com.howbuy.crm.account.client.response.custinfo;

import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;

import java.io.Serializable;
import java.util.Date;

/**
 * @description: (海外账户中心 订单层属性 。 对应 {@link com.howbuy.hkacconline.facade.query.queryhkacctdeal.dto.AcctDealDTO})
 *  * 海外账户中心 返回体，对象设计为 HkDealDTO<T> 。 其中T为 指定业务的属性。
 *  * 此处 将设计转换为 extends
 * <AUTHOR>
 * @date 2024/8/12 15:06
 * @since JDK 1.8
 */
@Data
public class HkAcctBaseDealVO implements Serializable {


    /**
     * 订单号
     */
    private String dealNo;

    /**
     * 香港客户号
     */
    private String hkCustNo;

    /**
     * 交易代码
     */
    private String txCode;

    /**
     * 业务代码
     * 海外储蓄罐协议流水 127-签署 128-终止
     */
    private String busiCode;

    /**
     * 渠道1-柜台 2-小程序
     * {@link com.howbuy.crm.account.client.enums.ChannelEnum}
     */
    private String channel;

    /**
     * 客户名称
     */
    @Deprecated
    private String custName;
    /**
     * 客户中文名
     */
    private String custChineseName;
    /**
     * 客户英文名称
     */
    private String custEnName;

    /**
     * 投资者类型0-机构,1-个人,2-产品户
     */
    private String invstType;

    /**
     * 证件类型
     */
    private String idType;

    /**
     * 证件号码摘要
     */
    private String idNoDigest;

    /**
     * 证件号码掩码
     */
    private String idNoMask;

    /**
     * 证件号码密文
     */
    private String idNoCipher;

    /**
     * 申请日期
     */
    private String appDt;

    /**
     * 申请时间
     */
    private String appTm;

    /**
     * 摘要-银行账号
     */
    private String bankAcctDigest;

    /**
     * 掩码-银行账号
     */
    private String bankAcctMask;

    /**
     * 密文-银行账号
     */
    private String bankAcctCipher;

    /**
     * 资金账号
     */
    private String hkCpAcctNo;

    /**
     * 银行卡ID
     */
    private String bankId;

    /**
     * 交易审核标志0-不需审核； 1-等待审核； 2-审核不通过； 3-审核通过；
     */
    private String txChkFlag;

    /**
     * 复核人
     */
    private String checker;

    /**
     * 创建人
     */
    private String creator;

    /**
     * 交易ip地址
     */
    private String txIp;

    /**
     * 创建时间戳
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date stimestamp;

    /**
     * 更新时间戳
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date updatedStimestamp;
    /**
     * 初审人
     */
    private String firstChecker;

    /**
     * 初审操作时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date firstCheckDt;

    /**
     * 审核时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date checkDt;

    /**
     * 邮箱摘要
     */
    private String emailDigest;

    /**
     * 邮箱掩码
     */
    private String emailMask;

    /**
     * 邮箱密文
     */
    private String emailCipher;

    /**
     * 手机号摘要
     */
    private String mobileDigest;

    /**
     * 手机号掩码
     */
    private String mobileMask;
    /**
     * 手机号密文
     */
    private String mobileCipher;
    /**
     * 入金流水号
     */
    private String depositSerialNo;
    /**
     * 审核不通过原因
     */
    private String checkReverseReason;

}