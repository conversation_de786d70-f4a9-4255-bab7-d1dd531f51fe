package com.howbuy.crm.account.client.response.custinfo;

import lombok.Data;

import java.io.Serializable;
import java.math.BigDecimal;

/**
 * 香港客户vs 投顾客户 关联关系
 * <AUTHOR>
 */
@Data
public class HkAcctCustRelationInfoVO implements Serializable {

    private static final long serialVersionUID = -477701081446057100L;

    /**
     * 主键
     */
    private BigDecimal id;

    /**
     * 投顾客户号
     */
    private String custNo;

    /**
     * 客户香港id (ebrokerID)
     */
    private String ebrokerId;


    /**
     * 香港交易账号
     */
    private String hkTxAcctNo;

}
