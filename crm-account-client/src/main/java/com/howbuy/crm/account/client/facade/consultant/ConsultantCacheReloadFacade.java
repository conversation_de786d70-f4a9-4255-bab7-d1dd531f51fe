package com.howbuy.crm.account.client.facade.consultant;

import com.howbuy.crm.account.client.request.consultant.ConsultantCacheReloadAllRequest;
import com.howbuy.crm.account.client.request.consultant.ConsultantCacheReloadRequest;
import com.howbuy.crm.account.client.response.Response;
import com.howbuy.crm.account.client.response.consultant.ConsultantCacheReloadResponse;

/**
 * @description: 重新加载投顾缓存服务
 * <AUTHOR>
 * @date 2025/3/10
 * @since JDK 1.8
 */
public interface ConsultantCacheReloadFacade {

    /**
     * @api {DUBBO} com.howbuy.crm.account.client.facade.consultant.ConsultantCacheReloadFacade.reloadConsultantCache() reloadConsultantCache()
     * @apiVersion 1.0.0
     * @apiGroup ConsultantCacheReloadFacade
     * @apiName reloadConsultantCache()
     * @apiDescription 重新加载投顾缓存信息
     * @apiParam (请求体) {String} consCode 投顾编号
     * @apiParamExample 请求体示例
     * {"consCode":"123456"}
     * @apiSuccess (响应结果) {String} code 状态码
     * @apiSuccess (响应结果) {String} description 描述信息
     * @apiSuccess (响应结果) {Object} data 数据封装
     * @apiSuccess (响应结果) {Boolean} data.success 是否重新加载成功
     * @apiSuccess (响应结果) {String} data.message 重新加载结果描述
     * @apiSuccessExample 响应结果示例
     * {"code":"0000","description":"成功","data":{"success":true,"message":"重新加载投顾缓存成功"}}
     */
    Response<ConsultantCacheReloadResponse> reloadConsultantCache(ConsultantCacheReloadRequest request);

    /**
     * @api {DUBBO} com.howbuy.crm.account.client.facade.consultant.ConsultantCacheReloadFacade.reloadAllConsultantCache() reloadAllConsultantCache()
     * @apiVersion 1.0.0
     * @apiGroup ConsultantCacheReloadFacade
     * @apiName reloadAllConsultantCache()
     * @apiDescription 重新加载全量投顾缓存信息
     * @apiParamExample 请求体示例
     * {}
     * @apiSuccess (响应结果) {String} code 状态码
     * @apiSuccess (响应结果) {String} description 描述信息
     * @apiSuccess (响应结果) {Object} data 数据封装
     * @apiSuccess (响应结果) {Boolean} data.success 是否重新加载成功
     * @apiSuccess (响应结果) {String} data.message 重新加载结果描述
     * @apiSuccessExample 响应结果示例
     * {"code":"0000","description":"成功","data":{"success":true,"message":"重新加载全量投顾缓存成功"}}
     */
    Response<ConsultantCacheReloadResponse> reloadAllConsultantCache(ConsultantCacheReloadAllRequest request);
} 