/**
 * Copyright (c) 2024, <PERSON><PERSON><PERSON><PERSON> HOWBUY INVESTMENT MANAGEMENT Co., Ltd.
 * All right reserved.
 * <p>
 * THIS IS UNPUBLISHED PROPRIETARY SOURCE CODE OF HOWBUY INVESTMENT
 * MANAGEMENT CO., LTD.  THE CONTENTS OF THIS FILE MAY NOT BE DISCLOSED
 * TO THIRD PARTIES, COPIED OR DUPLICATED IN ANY FORM, IN WHOLE OR IN PART,
 * WITHOUT THE PRIOR WRITTEN PERMISSION OF HOWBUY INVESTMENT MANAGEMENT
 * CO., LTD.
 */
package com.howbuy.crm.account.client.response.bankcardinfo;

import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;

import java.io.Serializable;
import java.util.Date;
import java.util.List;

/**
 * <AUTHOR>
 * @description: 香港银行卡信息VO类
 * @date 2024/1/11 14:33
 * @since JDK 1.8
 */

@Data
public class HkBankCardInfoVO implements Serializable {
    private static final long serialVersionUID = -2466897809360910156L;

    /**
     * 香港资金账号
     */
    private String hkCpAcctNo;
    /**
     * 银行代码
     */
    private String bankCode;
    /**
     * 大陆银行编码
     */
    private String mainlandBankCode;
    /**
     * 银行名称
     */
    private String bankName;
    /**
     * 银行中文名称
     */
    private String bankChineseName;
    /**
     * 联行号
     */
    private String bankRegionCode;
    /**
     * 摘要-银行账号
     */
    private String bankAcctDigest;
    /**
     * 掩码-银行账号
     */
    private String bankAcctMask;
    /**
     * 银行账户名称
     */
    private String bankAcctName;
    /**
     * 银行账户状态0-正常 1-注销
     */
    private String bankAcctStatus;
    /**
     * 默认账号0-否 1-是
     */
    private String defaultAccount;
    /**
     * 币种
     */
    private String curCode;
    /**
     * 币种代码列表
     */
    private List<String> currencyCodes;
    /**
     * SWIFT代码
     */
    private String swiftCode;
    /**
     * 是否为联名账户 0-否 1-是
     */
    private String jointAccount;
    /**
     * 代理银行swift编码
     */
    private String correspondentSwiftCode;
    /**
     * 代理银行代码
     */
    private String correspondentBankCode;
    /**
     * 代理银行大陆银行编码
     */
    private String correspondentMainlandBankCode;
    /**
     * 代理银行银行名称
     */
    private String correspondentBankName;
    /**
     * 代理银行中文名称
     */
    private String correspondentBankChineseName;
    /**
     * 代理银行账户-摘要
     */
    private String correspondentBankAcctDigest;
    /**
     * 代理银行账户-掩码
     */
    private String correspondentBankAcctMask;
    /**
     * 入金验证状态 0-未验证 1-已验证
     */
    private String depositsVefyStat;
    /**
     * 创建时间戳
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date stimestamp;
}