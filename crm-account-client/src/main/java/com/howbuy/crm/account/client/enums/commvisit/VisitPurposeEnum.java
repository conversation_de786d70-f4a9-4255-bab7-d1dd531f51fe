package com.howbuy.crm.account.client.enums.commvisit;

/**
 * <AUTHOR>
 * @description: 请在此添加描述
 * @date 2025/4/24 19:48
 * @since JDK 1.8
 */
public enum VisitPurposeEnum {
    /**
     * 初次见面
     */
    FIRST_MEETING("1","初次见面"),

    /**
     * 产品服务
     */
    PRODUCT_SERVICE("2","产品服务"),

    /**
     * IPS陪访
     */
    IPS_VISIT("3","IPS陪访"),

    /**
     * 创新陪访
     */
    BX_VISIT("4","创新陪访"),

    /**
     * Leads及继承客户拜访
     */
    LEADS_VISIT("5","Leads及继承客户拜访"),

    /**
     * 其他
     */
    OTHER("6","其他")
    ;

    /**
     * 编码
     **/
    private String code;
    /**
     * 描述
     * */
    private String description;

    VisitPurposeEnum(String code, String description){
        this.code=code;
        this.description=description;
    }

    public static VisitPurposeEnum getByCode(String code){
        for(VisitPurposeEnum item:values()){
            if(item.getCode().equals(code)){
                return item;
            }
        }
        return null;
    }

    public String getCode() {
        return code;
    }

    public void setCode(String code) {
        this.code = code;
    }

    public String getDescription() {
        return description;
    }

    public void setDescription(String description) {
        this.description = description;
    }
}