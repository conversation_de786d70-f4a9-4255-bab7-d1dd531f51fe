/**
 * Copyright (c) 2024, <PERSON><PERSON><PERSON><PERSON> HOWBUY INVESTMENT MANAGEMENT Co., Ltd.
 * All right reserved.
 * <p>
 * THIS IS UNPUBLISHED PROPRIETARY SOURCE CODE OF HOWBUY INVESTMENT
 * MANAGEMENT CO., LTD.  THE CONTENTS OF THIS FILE MAY NOT BE DISCLOSED
 * TO THIRD PARTIES, COPIED OR DUPLICATED IN ANY FORM, IN WHOLE OR IN PART,
 * WITHOUT THE PRIOR WRITTEN PERMISSION OF HOWBUY INVESTMENT MANAGEMENT
 * CO., LTD.
 */
package com.howbuy.crm.account.client.response.custinfo;

import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;

import java.io.Serializable;
import java.util.Date;

/**
 * @description: 账户中心的客户收货地址
 * <AUTHOR>
 * @date 2024/4/7 14:06
 * @since JDK 1.8
 */

@Data
public class HboneDeliveryAddrInfoVO implements Serializable {

    private static final long serialVersionUID = 1071069822004471067L;

    /**
     * 主键
     */
    private String addrId;

    /**
     * 一账通号
     */
    private String hboneNo;

    /**
     * 分销机构号
     */
    private String disCode;

    /**
     * 网点代码
     */
    private String outletCode;

    /**
     * 收货人姓名
     */
    private String deliveryName;

    /**
     * 收货人手机号区号
     */
    private String deliveryMobileAreaCode;

    /**
     * 收货人手机号-摘要
     */
    private String deliveryMobileDigest;

    /**
     * 收货人手机号-掩码
     */
    private String deliveryMobileMask;

    /**
     * 收货地址-国家/地区代码
     */
    private String deliveryCountryCode;

    /**
     * 收货地址-省份代码
     */
    private String deliveryProvCode;

    /**
     * 收货地址-城市代码
     */
    private String deliveryCityCode;

    /**
     * 收货地址-县代码
     */
    private String deliveryCountyCode;

    /**
     * 收货地址-摘要
     */
    private String deliveryAddrDigest;

    /**
     * 收货地址-掩码
     */
    private String deliveryAddrMask;

    /**
     * 收货地址-加密
     */
    private String deliveryAddrCipher;

    /**
     * 收货人手机号-加密
     */
    private String deliveryMobileCipher;

    /**
     * 默认地址 0-否 1-是
     */
    private String defaultAddr;

    /**
     * 创建时间戳
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss.SSS", timezone = "GMT+8")
    private Date stimestamp;

    /**
     * 更新时间戳
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss.SSS", timezone = "GMT+8")
    private Date updatedStimestamp;


    /**
     * 收货地址-省名称
     */
    private String deliveryProvName;


    /**
     * 收货地址-市名称
     */
    private String deliveryCityName;


    /**
     * 收货地址-县名称
     */
    private String deliveryCountyName;

    /**
     * 收货地址-国家/地区名称
     */
    private String deliveryCountryName;

}