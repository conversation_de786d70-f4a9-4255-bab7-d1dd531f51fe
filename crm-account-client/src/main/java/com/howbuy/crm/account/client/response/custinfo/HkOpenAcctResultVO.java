/**
 * Copyright (c) 2024, <PERSON>gHai HOWBUY INVESTMENT MANAGEMENT Co., Ltd.
 * All right reserved.
 * <p>
 * THIS IS UNPUBLISHED PROPRIETARY SOURCE CODE OF HOWBUY INVESTMENT
 * MANAGEMENT CO., LTD.  THE CONTENTS OF THIS FILE MAY NOT BE DISCLOSED
 * TO THIRD PARTIES, COPIED OR DUPLICATED IN ANY FORM, IN WHOLE OR IN PART,
 * WITHOUT THE PRIOR WRITTEN PERMISSION OF HOWBUY INVESTMENT MANAGEMENT
 * CO., LTD.
 */
package com.howbuy.crm.account.client.response.custinfo;

import lombok.Data;

/**
 * @description: (请在此添加描述)
 * <AUTHOR>
 * @date 2024/1/12 14:08
 * @since JDK 1.8
 */

@Data
public class HkOpenAcctResultVO {


    /**
     * 开户拒绝信息 字段值为对应字段的拒绝原因
     */
    private HkOpenAcctRejectVO openAcctRejectVO;

    /**
     * 审核状态 0-不需审核 1-等待审核 2-等待复核 3-审核通过 4-审核不通过/作废 5-驳回至初审 6-驳回至客户
     */
    private String txChkFlag;
    /**
     * 扩展信息
     */
    private String extInfo;

}