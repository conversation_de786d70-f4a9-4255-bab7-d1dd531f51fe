/**
 * Copyright (c) 2024, <PERSON><PERSON><PERSON><PERSON> HOWBUY INVESTMENT MANAGEMENT Co., Ltd.
 * All right reserved.
 * <p>
 * THIS IS UNPUBLISHED PROPRIETARY SOURCE CODE OF HOWBUY INVESTMENT
 * MANAGEMENT CO., LTD.  THE CONTENTS OF THIS FILE MAY NOT BE DISCLOSED
 * TO THIRD PARTIES, COPIED OR DUPLICATED IN ANY FORM, IN WHOLE OR IN PART,
 * WITHOUT THE PRIOR WRITTEN PERMISSION OF HOWBUY INVESTMENT MANAGEMENT
 * CO., LTD.
 */
package com.howbuy.crm.account.client.response.declarationinfo;

import lombok.Data;

import java.io.Serializable;

/**
 * <AUTHOR>
 * @description: 香港客户声明信息VO类
 * @date 2024/1/11 14:54
 * @since JDK 1.8
 */
@Data
public class HkDeclarationInfoVO implements Serializable {

    private static final long serialVersionUID = -913094027049866177L;
    /**
     * 声明序号
     */
    private Integer sortNum;

    /**
     * 声明结果 0-否 1-是
     */
    private String result;

    /**
     * 声明文件ID
     */
    private String fileId;

    /**
     * 声明内容
     */
    private String content;

}