package com.howbuy.crm.account.client.response.consultantinfo;

import lombok.Data;

import java.io.Serializable;
import java.math.BigDecimal;

/**
 * @description: (请在此添加描述)
 * <AUTHOR>
 * @date 2023/12/26 15:31
 * @since JDK 1.8
 */

/**
    * 理财顾问信息表
    */
@Data
public class CmConsultantInfo implements Serializable {

    private static final long serialVersionUID = -4518904784564517829L;
    /**
    * 理财顾问代码
    */
    private String conscode;

    /**
     * 理财顾问名称
     */
    private String consname;

    /**
    * 企业微信账号
    */
    private String wechatconscode;
}