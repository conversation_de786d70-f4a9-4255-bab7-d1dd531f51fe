/**
 * Copyright (c) 2023, <PERSON>gH<PERSON> HOWBUY INVESTMENT MANAGEMENT Co., Ltd.
 * All right reserved.
 * <p>
 * THIS IS UNPUBLISHED PROPRIETARY SOURCE CODE OF HOWBUY INVESTMENT
 * MANAGEMENT CO., LTD.  THE CONTENTS OF THIS FILE MAY NOT BE DISCLOSED
 * TO THIRD PARTIES, COPIED OR DUPLICATED IN ANY FORM, IN WHOLE OR IN PART,
 * WITHOUT THE PRIOR WRITTEN PERMISSION OF HOWBUY INVESTMENT MANAGEMENT
 * CO., LTD.
 */
package com.howbuy.crm.account.client.request.custinfo;

import com.howbuy.commons.validator.MyValidation;
import com.howbuy.commons.validator.util.ValidatorTypeEnum;
import com.howbuy.crm.account.client.request.Request;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.io.Serializable;

/**
 * @description: (一账通账号 vs 投顾客户号  关联关系操作请求类)
 * <AUTHOR>
 * @date 2023/12/15 13:47
 * @since JDK 1.8
 */
@Data
@EqualsAndHashCode(callSuper = true)
public class HboneAcctRelationOptRequest extends Request implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 投顾客户号
     */
    @MyValidation(validatorType = ValidatorTypeEnum.String, fieldName = "投顾客户号", isRequired = true)
    private String custNo;

    /**
     * 香港交易账号
     */
    @MyValidation(validatorType = ValidatorTypeEnum.String, fieldName = "一账通号", isRequired = true)
    private String hboneNo;

    /**
     * 操作员
     */
    @MyValidation(validatorType = ValidatorTypeEnum.String, fieldName = "操作人", isRequired = true)
    private String operator;

    /**
     * 非必须
     * 操作来源 [1-MQ]时，为[异常来源]， [2-菜单页面]时，为菜单名称
     */
    private String operateSource;

    /**
     * 非必须
     * 操作通道 1-MQ  2-菜单页面
     */
    private String operateChannel;

    /**
     * 备注
     */
    private String remark;


}