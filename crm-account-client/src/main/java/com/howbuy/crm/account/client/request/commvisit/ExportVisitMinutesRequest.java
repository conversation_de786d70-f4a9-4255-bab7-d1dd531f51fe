package com.howbuy.crm.account.client.request.commvisit;

import lombok.Getter;
import lombok.Setter;

import java.io.Serializable;
import java.util.List;

/**
 * @description: 导出客户拜访纪要数据请求
 * <AUTHOR>
 * @date 2025-04-08 10:26:00
 * @since JDK 1.8
 */
@Getter
@Setter
public class ExportVisitMinutesRequest implements Serializable {

    private static final long serialVersionUID = 1L;
    /**
     * 组织编码
     */
    private String orgCode;
    /**
     * 所属投顾ID
     */
    private String consCode;
    
    /**
     * 拜访日期开始(格式YYYYMMDD)
     */
    private String visitDateStart;
    
    /**
     * 拜访日期结束(格式YYYYMMDD)
     */
    private String visitDateEnd;
    
    /**
     * 创建日期开始(格式YYYYMMDD)
     */
    private String createDateStart;
    
    /**
     * 创建日期结束(格式YYYYMMDD)
     */
    private String createDateEnd;
    
    /**
     * 拜访目的列表,多选
     */
    private List<String> visitPurpose;
    
    /**
     * 客户姓名,精确匹配
     */
    private String custName;
    
    /**
     * 投顾客户号,精确匹配
     */
    private String consCustNo;
    
    /**
     * 陪访人,精确匹配
     */
    private String accompanyingUser;
    
    /**
     * 上级主管,精确匹配
     */
    private String managerId;
    
    /**
     * 反馈情况,可选:陪访人未填、上级主管未填、陪访人已填、上级主管已填
     */
    private List<String> feedbackStatus;
    
    /**
     * 当前用户ID
     */
    private String currentUserId;
} 