package com.howbuy.crm.account.client.enums.custinfo;

/**
 * @description:(客户来源  二级来源 ： 013：成交客户介绍，019：非成交客户介绍)
 * @param
 * @return
 * @author: haoran.zhang
 * @date: 2023/12/8 17:46
 * @since JDK 1.8
 */
public enum CustSourceSecondLevelEnum {

	/**
	 * 成交客户介绍
	 */
	DEAL_CUSTOMER_INTRODUCE("013", "成交客户介绍"),
	/**
	 * 非成交客户介绍
	 */
	UNDEAL_CUSTOMER_INTRODUCE("019", "非成交客户介绍")

	//待补充 。。。。。。
	;

	/**
	 * 编码
	 */
	private String code;

	/**
	 * 描述
	 */
	private String description;

	CustSourceSecondLevelEnum(String code, String description) {
		this.code = code;
		this.description = description;
	}

	/**
	 * 通过code获得
	 *
	 * @param code 系统返回参数编码
	 * @return description 描述
	 */
	public static String getDescription(String code) {
		CustSourceSecondLevelEnum statusEnum = getEnum(code);
		return statusEnum == null ? null : statusEnum.getDescription();
	}

	/**
	 * 通过code直接返回 整个枚举类型
	 *
	 * @param code 系统返回参数编码
	 * @return BaseConstantEnum
	 */
	public static CustSourceSecondLevelEnum getEnum(String code) {
		for (CustSourceSecondLevelEnum statusEnum : CustSourceSecondLevelEnum.values()) {
			if (statusEnum.getCode().equals(code)) {
				return statusEnum;
			}
		}
		return null;
	}


	public String getCode() {
		return code;
	}


	public String getDescription() {
		return description;
	}

}
