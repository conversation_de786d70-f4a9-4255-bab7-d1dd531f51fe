/**
 * Copyright (c) 2023, <PERSON>gH<PERSON> HOWBUY INVESTMENT MANAGEMENT Co., Ltd.
 * All right reserved.
 * <p>
 * THIS IS UNPUBLISHED PROPRIETARY SOURCE CODE OF HOWBUY INVESTMENT
 * MANAGEMENT CO., LTD.  THE CONTENTS OF THIS FILE MAY NOT BE DISCLOSED
 * TO THIRD PARTIES, COPIED OR DUPLICATED IN ANY FORM, IN WHOLE OR IN PART,
 * WITHOUT THE PRIOR WRITTEN PERMISSION OF HOWBUY INVESTMENT MANAGEMENT
 * CO., LTD.
 */
package com.howbuy.crm.account.client.request.custinfo;

import com.howbuy.commons.validator.MyValidation;
import com.howbuy.commons.validator.util.ValidatorTypeEnum;
import com.howbuy.crm.account.client.request.PageRequest;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.util.List;

/**
 * @description: (香港账户中心开户订单操作流水查询 查询请求)
 * <AUTHOR>
 * @date 2024年8月12日
 * @since JDK 1.8
 */
@Data
@EqualsAndHashCode(callSuper = true)
public class HkAcctOptDealRequest extends PageRequest {

    /**
     * 香港交易账号
     */
    @MyValidation(validatorType = ValidatorTypeEnum.String, fieldName = "香港客户号", isRequired = true)
    private String hkTxAcctNo;


    /**
     * 操作开始日期 yyyyMMdd
     */
    private String startOperateDt;
    /**
     * 操作结束日期 yyyyMMdd
     */
    private String endOperateDt;
    /**
     * 业务代码
     */
    private List<String> busiCodeList;
    /**
     * 操作代码 00-自助 01-申请提交 02-再次提交 03-初审通过 04-驳回至初审 05-驳回至客户 07-审核不通过 08-作废
     */
    private String operateCode;
}