/**
 * Copyright (c) 2024, <PERSON><PERSON><PERSON><PERSON> HOWBUY INVESTMENT MANAGEMENT Co., Ltd.
 * All right reserved.
 * <p>
 * THIS IS UNPUBLISHED PROPRIETARY SOURCE CODE OF HOWBUY INVESTMENT
 * MANAGEMENT CO., LTD.  THE CONTENTS OF THIS FILE MAY NOT BE DISCLOSED
 * TO THIRD PARTIES, COPIED OR DUPLICATED IN ANY FORM, IN WHOLE OR IN PART,
 * WITHOUT THE PRIOR WRITTEN PERMISSION OF HOWBUY INVESTMENT MANAGEMENT
 * CO., LTD.
 */
package com.howbuy.crm.account.client.response.beisen;

import lombok.Data;

import java.io.Serializable;
import java.util.List;

/**
 * @description: (请在此添加描述)
 * <AUTHOR>
 * @date 2024/10/24 17:39
 * @since JDK 1.8
 */
@Data
public class CmBeisenPosLevelConfigListVO implements Serializable {

    private static final long serialVersionUID = 1L;
    /**
     * 好买北森职级映射表VO集合
     */
    private List<CmBeisenPosLevelConfigVO> list;
}