/**
 * Copyright (c) 2024, <PERSON><PERSON><PERSON><PERSON> HOWBUY INVESTMENT MANAGEMENT Co., Ltd.
 * All right reserved.
 * <p>
 * THIS IS UNPUBLISHED PROPRIETARY SOURCE CODE OF HOWBUY INVESTMENT
 * MANAGEMENT CO., LTD.  THE CONTENTS OF THIS FILE MAY NOT BE DISCLOSED
 * TO THIRD PARTIES, COPIED OR DUPLICATED IN ANY FORM, IN WHOLE OR IN PART,
 * WITHOUT THE PRIOR WRITTEN PERMISSION OF HOWBUY INVESTMENT MANAGEMENT
 * CO., LTD.
 */
package com.howbuy.crm.account.client.request.commvisit;

import com.howbuy.crm.account.client.request.Request;

import lombok.Getter;
import lombok.Setter;

import java.io.Serializable;

/**
 * @description: 客户沟通记录新增页初始化数据请求
 * <AUTHOR>
 * @date 2025-04-08 13:28:00
 * @since JDK 1.8
 */
@Get<PERSON>
@Setter
public class VisitInitDataRequest extends Request implements Serializable {

    private static final long serialVersionUID = 1L;
    
    /**
     * 投顾编号
     */
    private String consCode;
} 