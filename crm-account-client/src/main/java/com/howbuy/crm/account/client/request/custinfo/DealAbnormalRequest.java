/**
 * Copyright (c) 2023, <PERSON><PERSON><PERSON><PERSON> HOWBUY INVESTMENT MANAGEMENT Co., Ltd.
 * All right reserved.
 * <p>
 * THIS IS UNPUBLISHED PROPRIETARY SOURCE CODE OF HOWBUY INVESTMENT
 * MANAGEMENT CO., LTD.  THE CONTENTS OF THIS FILE MAY NOT BE DISCLOSED
 * TO THIRD PARTIES, COPIED OR DUPLICATED IN ANY FORM, IN WHOLE OR IN PART,
 * WITHOUT THE PRIOR WRITTEN PERMISSION OF HOWBUY INVESTMENT MANAGEMENT
 * CO., LTD.
 */
package com.howbuy.crm.account.client.request.custinfo;

import com.howbuy.commons.validator.MyValidation;
import com.howbuy.commons.validator.util.ValidatorTypeEnum;
import lombok.Data;

import java.io.Serializable;

/**
 * @description: (香港|一账通 异常客户  处理的 request 参数对象)
 * <AUTHOR>
 * @date 2023/12/11 13:17
 * @since JDK 1.8
 */
@Data
public class DealAbnormalRequest implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 异常客户信息id
     */
    @MyValidation(validatorType = ValidatorTypeEnum.String, fieldName = "异常客户信息id", isRequired = true)
    private String id;

    /**
     * 操作人
     */
    @MyValidation(validatorType = ValidatorTypeEnum.String, fieldName = "操作人", isRequired = true)
    private String operator;

    /**
     * remark备注说明
     */
    private String remark;


    /**
     * 处理状态：0-未处理 1-已处理 2-无需处理
     */
    @MyValidation(validatorType = ValidatorTypeEnum.String, fieldName = "处理状态", isRequired = true)
    private String dealStatus;




}