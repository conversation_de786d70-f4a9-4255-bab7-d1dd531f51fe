/**
 * Copyright (c) 2024, <PERSON><PERSON><PERSON><PERSON> HOWBUY INVESTMENT MANAGEMENT Co., Ltd.
 * All right reserved.
 * <p>
 * THIS IS UNPUBLISHED PROPRIETARY SOURCE CODE OF HOWBUY INVESTMENT
 * MANAGEMENT CO., LTD.  THE CONTENTS OF THIS FILE MAY NOT BE DISCLOSED
 * TO THIRD PARTIES, COPIED OR DUPLICATED IN ANY FORM, IN WHOLE OR IN PART,
 * WITHOUT THE PRIOR WRITTEN PERMISSION OF HOWBUY INVESTMENT MANAGEMENT
 * CO., LTD.
 */
package com.howbuy.crm.account.client.facade.consultant;

import com.howbuy.crm.account.client.request.consultant.QueryConsPerformanceCoeffExportRequest;
import com.howbuy.crm.account.client.response.Response;
import com.howbuy.crm.account.client.response.consultant.QueryConsPerformanceCoeffExportResponse;

/**
 * @description: 导出人员绩效系数接口
 * <AUTHOR>
 * @date 2025-07-11 15:15:20
 * @since JDK 1.8
 */
public interface QueryConsPerformanceCoeffExportFacade {

    /**
     * @api {DUBBO} com.howbuy.crm.account.client.facade.consultant.QueryConsPerformanceCoeffExportFacade.execute()
     * @apiVersion 1.0.0
     * @apiGroup QueryConsPerformanceCoeffExportFacade
     * @apiName execute()
     * @apiDescription 导出人员绩效系数
     * @apiParam (请求体) {String} deptCode 部门代码（必填，带数据广度权限）
     * @apiParam (请求体) {String} consCode 投顾code（必填，带数据广度权限）
     * @apiParam (请求体) {String} startAssignTime 分配开始时间（YYYYMMDD，可空）
     * @apiParam (请求体) {String} endAssignTime 分配结束时间（YYYYMMDD，可空）
     * @apiParam (请求体) {String} consCustNo 投顾客户号（可空）
     * @apiParamExample 请求体示例
     * {"deptCode":"D001","consCode":"C001","startAssignTime":"********","endAssignTime":"********","consCustNo":"10001"}
     * @apiSuccess (响应结果) {String} code 状态码
     * @apiSuccess (响应结果) {String} description 描述信息
     * @apiSuccess (响应结果) {Object} data 数据封装
     * @apiSuccess (响应结果) {String} data.fileUrl 导出文件URL
     * @apiSuccess (响应结果) {String} data.fileName 导出文件名
     * @apiSuccess (响应结果) {Long} data.totalCount 导出数据总数
     * @apiSuccessExample 响应结果示例
     * {"code":"0000","description":"成功","data":{"fileUrl":"http://example.com/export/performance_coeff_20241219.xlsx","fileName":"人员绩效系数表_20241219.xlsx","totalCount":1000}}
     * @param request 请求参数
     * @return com.howbuy.crm.account.client.response.Response<com.howbuy.crm.account.client.response.consultant.QueryConsPerformanceCoeffExportResponse>
     */
    Response<QueryConsPerformanceCoeffExportResponse> execute(QueryConsPerformanceCoeffExportRequest request);
}