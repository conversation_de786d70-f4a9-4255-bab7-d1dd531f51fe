package com.howbuy.crm.account.client.enums;

/**
 * @description:(处理状态：0-未处理 1-已处理 2-无需处理)
 * @param
 * @return
 * @author: haoran.zhang
 * @date: 2023/12/8 20:30
 * @since JDK 1.8
 */
public enum DealStatusEnum {

    /**
     * 未处理
     */
    UN_DEAL("0","未处理"),

    /**
     * 已处理
     */
    DEAL("1","已处理"),

    /**
     * 无需处理
     */
    NO_NEED_DEAL("2","无需处理")
  ;



    /**
     * 编码
     **/
    private String code;
    /**
     * 描述
     * */
    private String description;

    DealStatusEnum(String code, String description){
        this.code=code;
        this.description=description;
    }

    /**
     * 通过code获得
     * @param code	系统返回参数编码
     * @return description 描述
     */
    public static String getDescription(String code){
        DealStatusEnum statusEnum=getEnum(code);
        return  statusEnum==null?null:statusEnum.getDescription();
    }

    /**
     * 通过code直接返回 整个枚举类型
     * @param code 系统返回参数编码
     * @return BaseConstantEnum
     */
    public static DealStatusEnum getEnum(String code){
        for(DealStatusEnum b: DealStatusEnum.values()){
            if(b.getCode().equals(code)){
                return b;
            }
        }
        return null;
    }

    public String getCode() {
        return code;
    }


    public String getDescription() {
        return description;
    }
}
