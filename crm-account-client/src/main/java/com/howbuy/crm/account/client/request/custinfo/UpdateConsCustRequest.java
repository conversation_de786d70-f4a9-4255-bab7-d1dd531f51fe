/**
 * Copyright (c) 2023, <PERSON><PERSON><PERSON><PERSON> HOWBUY INVESTMENT MANAGEMENT Co., Ltd.
 * All right reserved.
 * <p>
 * THIS IS UNPUBLISHED PROPRIETARY SOURCE CODE OF HOWBUY INVESTMENT
 * MANAGEMENT CO., LTD.  THE CONTENTS OF THIS FILE MAY NOT BE DISCLOSED
 * TO THIRD PARTIES, COPIED OR DUPLICATED IN ANY FORM, IN WHOLE OR IN PART,
 * WITHOUT THE PRIOR WRITTEN PERMISSION OF HOWBUY INVESTMENT MANAGEMENT
 * CO., LTD.
 */
package com.howbuy.crm.account.client.request.custinfo;

import com.howbuy.commons.validator.MyValidation;
import com.howbuy.commons.validator.util.ValidatorTypeEnum;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * @description: (更新 投顾客户信息 请求类 )
 * <AUTHOR>
 * @date 2023/12/18 17:15
 * @since JDK 1.8
 */
@Data
@EqualsAndHashCode(callSuper = true)
public class UpdateConsCustRequest extends BaseModifyConsCustRequest{

    /**
     * 投顾客户编号
     */
    private String custNo;

    /**
     * 省市地址以及邮编有过修改
     */
    private boolean flag;



    /************************his表记录变更 信息  begin **************************************************/

    /**
     * 变更 操作人
     */
    private String specialReason;

    /**
     * 变更 说明
     */
    private String explanation;

    /**
     * 变更 复核人
     */
    private String checker;
/************************his表记录变更 信息  end **************************************************/




}