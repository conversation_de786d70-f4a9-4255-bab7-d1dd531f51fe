/**
 * Copyright (c) 2023, <PERSON><PERSON><PERSON><PERSON> HOWBUY INVESTMENT MANAGEMENT Co., Ltd.
 * All right reserved.
 * <p>
 * THIS IS UNPUBLISHED PROPRIETARY SOURCE CODE OF HOWBUY INVESTMENT
 * MANAGEMENT CO., LTD.  THE CONTENTS OF THIS FILE MAY NOT BE DISCLOSED
 * TO THIRD PARTIES, COPIED OR DUPLICATED IN ANY FORM, IN WHOLE OR IN PART,
 * WITHOUT THE PRIOR WRITTEN PERMISSION OF HOWBUY INVESTMENT MANAGEMENT
 * CO., LTD.
 */
package com.howbuy.crm.account.client.response.custinfo;

import lombok.Data;

import java.io.Serializable;
import java.util.ArrayList;
import java.util.List;

/**
 * @description: (更新 投顾客户信息 返回信息 类 )
 * <AUTHOR>
 * @date 2023/12/27 17:10
 * @since JDK 1.8
 */
@Data
public class UpdateConsCustRespVO implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 客户号
     */
    private String custNo;


    /**
     * 重复客户列表
     */
    private List<RepeatCustInfo> repeatConsCustList=new ArrayList<>();


    /**
     * 潜客操作集合 id
     */
    private String operationid;

}