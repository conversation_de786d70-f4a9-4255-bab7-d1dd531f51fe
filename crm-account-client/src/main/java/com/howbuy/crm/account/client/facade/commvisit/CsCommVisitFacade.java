/**
 * Copyright (c) 2024, <PERSON><PERSON><PERSON><PERSON> HOWBUY INVESTMENT MANAGEMENT Co., Ltd.
 * All right reserved.
 * <p>
 * THIS IS UNPUBLISHED PROPRIETARY SOURCE CODE OF HOWBUY INVESTMENT
 * MANAGEMENT CO., LTD.  THE CONTENTS OF THIS FILE MAY NOT BE DISCLOSED
 * TO THIRD PARTIES, COPIED OR DUPLICATED IN ANY FORM, IN WHOLE OR IN PART,
 * WITHOUT THE PRIOR WRITTEN PERMISSION OF HOWBUY INVESTMENT MANAGEMENT
 * CO., LTD.
 */
package com.howbuy.crm.account.client.facade.commvisit;

import com.howbuy.crm.account.client.request.commvisit.AddCommunicateRecordRequest;
import com.howbuy.crm.account.client.request.commvisit.QueryCommVisitListRequest;
import com.howbuy.crm.account.client.request.commvisit.QueryCommunicateListRequest;
import com.howbuy.crm.account.client.request.commvisit.VisitInitDataRequest;
import com.howbuy.crm.account.client.response.Response;
import com.howbuy.crm.account.client.response.commvisit.AddCommunicateRecordResponse;
import com.howbuy.crm.account.client.response.commvisit.CommunicateListVO;
import com.howbuy.crm.account.client.response.commvisit.CsCommVisitListVO;
import com.howbuy.crm.account.client.response.commvisit.VisitInitDataResponse;

/**
 * <AUTHOR>
 * @description: 沟通拜访接口
 * @date 2024/10/11 19:47
 * @since JDK 1.8
 */
public interface CsCommVisitFacade {

    /**
     * @api {DUBBO}  com.howbuy.crm.account.client.facade.commvisit.CsCommVisitFacade.queryCsCommVisitList(request) queryCsCommVisitList()
     * @apiVersion 1.0.0
     * @apiGroup CsCommVisitFacade
     * @apiName queryCsCommVisitList()
     * @apiDescription 查询沟通拜访列表
     * @apiParam (请求体) {String} consCustNo 投顾客户号
     * @apiParam (请求体) {Array} visitTypeList 拜访类型列表
     * @apiParam (请求体) {String} startDt 开始日期
     * @apiParam (请求体) {String} endDt 结束日期
     * @apiParamExample 请求体示例
     * {"visitTypeList":["LNPOD"],"consCustNo":"W","startDt":"ni","endDt":"MQzwTEl"}
     * @apiSuccess (响应结果) {String} code 状态码
     * @apiSuccess (响应结果) {String} description 描述信息
     * @apiSuccess (响应结果) {Object} data 数据封装
     * @apiSuccess (响应结果) {Array} data.commVisitVOList 沟通拜访记录列表
     * @apiSuccess (响应结果) {String} data.commVisitVOList.conscustno 投顾客户号
     * @apiSuccess (响应结果) {String} data.commVisitVOList.commcontent 沟通及拜访内容
     * @apiSuccess (响应结果) {String} data.commVisitVOList.visittype 拜访方式
     * @apiSuccess (响应结果) {String} data.commVisitVOList.creator 创建人
     * @apiSuccess (响应结果) {Number} data.commVisitVOList.creDtStr 创建日期 yyyyMMddHHmmss
     * @apiSuccessExample 响应结果示例
     * {"code":"j5pNhrl","data":{"commVisitVOList":[{"conscustno":"E23xUnJ3J","creator":"iM3Wk15","visittype":"8X0ZpS0","commcontent":"76EA","credt":************}]},"description":"XOgOoYxyKX"}
     */
    Response<CsCommVisitListVO> queryCsCommVisitList(QueryCommVisitListRequest request);

    /**
     * @api {DUBBO}  com.howbuy.crm.account.client.facade.commvisit.CsCommVisitFacade.queryNewsestCsCommVisit(request) queryNewsestCsCommVisit()
     * @apiVersion 1.0.0
     * @apiGroup CsCommVisitFacade
     * @apiName queryNewsestCsCommVisit()
     * @apiDescription 查询最新沟通拜访
     * @apiParam (请求体) {String} consCustNo 投顾客户号
     * @apiParam (请求体) {Array} visitTypeList 拜访类型列表
     * @apiParamExample 请求体示例
     * {"visitTypeList":["LNPOD"],"consCustNo":"W","startDt":"ni","endDt":"MQzwTEl"}
     * @apiSuccess (响应结果) {String} code 状态码
     * @apiSuccess (响应结果) {String} description 描述信息
     * @apiSuccess (响应结果) {Object} data 数据封装
     * @apiSuccess (响应结果) {Array} data.commVisitVOList 沟通拜访记录列表
     * @apiSuccess (响应结果) {String} data.commVisitVOList.conscustno 投顾客户号
     * @apiSuccess (响应结果) {String} data.commVisitVOList.commcontent 沟通及拜访内容
     * @apiSuccess (响应结果) {String} data.commVisitVOList.visittype 拜访方式
     * @apiSuccess (响应结果) {String} data.commVisitVOList.creator 创建人
     * @apiSuccess (响应结果) {Number} data.commVisitVOList.creDtStr 创建日期 yyyyMMddHHmmss
     * @apiSuccessExample 响应结果示例
     * {"code":"j5pNhrl","data":{"commVisitVOList":[{"conscustno":"E23xUnJ3J","creator":"iM3Wk15","visittype":"8X0ZpS0","commcontent":"76EA","credt":************}]},"description":"XOgOoYxyKX"}
     */
    Response<CsCommVisitListVO> queryNewsestCsCommVisit(QueryCommVisitListRequest request);

    /**
     * @api {DUBBO} com.howbuy.crm.account.client.facade.commvisit.CsCommVisitFacade.getVisitInitData(request) getVisitInitData()
     * @apiVersion 1.0.0
     * @apiGroup CsCommVisitFacade
     * @apiName getVisitInitData()
     * @apiDescription 获取客户沟通记录新增页初始化数据
     * @apiParam (请求参数) {String} consCode 投顾编号
     * @apiParamExample 请求参数示例
     * {
     *   "consCode": "CS001"
     * }
     * @apiSuccess (响应结果) {String} code 状态码
     * @apiSuccess (响应结果) {String} description 描述信息
     * @apiSuccess (响应结果) {Object} data 数据封装
     * @apiSuccess (响应结果) {Array} data.projectManagerList 项目经理列表
     * @apiSuccess (响应结果) {String} data.projectManagerList.code 用户编码
     * @apiSuccess (响应结果) {String} data.projectManagerList.name 用户名称
     * @apiSuccess (响应结果) {Array} data.manageUserList 主管列表
     * @apiSuccess (响应结果) {String} data.manageUserList.code 用户编码
     * @apiSuccess (响应结果) {String} data.manageUserList.name 用户名称
     * @apiSuccess (响应结果) {Array} data.supervisorUserList 总部业资列表
     * @apiSuccess (响应结果) {String} data.supervisorUserList.code 用户编码
     * @apiSuccess (响应结果) {String} data.supervisorUserList.name 用户名称
     * @apiSuccess (响应结果) {Array} data.otherUserList 其他列表
     * @apiSuccess (响应结果) {String} data.otherUserList.code 用户编码
     * @apiSuccess (响应结果) {String} data.otherUserList.name 用户名称
     * @apiSuccessExample 响应结果示例
     * {
     *   "code": "0",
     *   "description": "成功",
     *   "data": {
     *     "projectManagerList": [
     *       {"code": "PM001", "name": "张三"},
     *       {"code": "PM002", "name": "李四"}
     *     ],
     *     "manageUserList": [
     *       {"code": "MG001", "name": "王五"},
     *       {"code": "MG002", "name": "赵六"}
     *     ],
     *     "supervisorUserList": [
     *       {"code": "SV001", "name": "钱七"},
     *       {"code": "SV002", "name": "孙八"}
     *     ],
     *     "otherUserList": [
     *       {"code": "OT001", "name": "周九"},
     *       {"code": "OT002", "name": "吴十"}
     *     ]
     *   }
     * }
     */
    Response<VisitInitDataResponse> getVisitInitData(VisitInitDataRequest request);

    /**
     * @api {DUBBO} com.howbuy.crm.account.client.facade.commvisit.CsCommVisitFacade.addCommunicateRecord(request)
     * @apiVersion 1.0.0
     * @apiGroup CsCommVisitFacade
     * @apiName addCommunicateRecord()
     * @apiDescription 新增客户沟通记录
     * @apiParam (请求参数) {Object} communicateReq 沟通记录信息对象
     * @apiParam (请求参数) {String} communicateReq.consCustNo 投顾客户号
     * @apiParam (请求参数) {String} communicateReq.visitDt 拜访日期(格式YYYYMMDD)
     * @apiParam (请求参数) {String} communicateReq.visittype 沟通方式(见面、线上会议、电话等)
     * @apiParam (请求参数) {String} communicateReq.commcontent 沟通内容摘要
     * @apiParam (请求参数) {String} communicateReq.consulttype 沟通类型(可选)
     * @apiParam (请求参数) {Object} bookingReq 预约信息对象(可选)
     * @apiParam (请求参数) {Boolean} bookingReq.hasRemind 是否新增提醒(可选)
     * @apiParam (请求参数) {String} bookingReq.nextvisittype 预约方式(可选)
     * @apiParam (请求参数) {String} bookingReq.nextdt 预约日期(格式YYYYMMDD)(可选)
     * @apiParam (请求参数) {String} bookingReq.nextstarttime 预约开始时间(格式HH:MM)(可选)
     * @apiParam (请求参数) {String} bookingReq.nextendtime 预约结束时间(格式HH:MM)(可选)
     * @apiParam (请求参数) {String} bookingReq.nextvisitcontent 预约内容(可选)
     * @apiParam (请求参数) {Object} visitMinutesReq 拜访纪要信息对象(可选)
     * @apiParam (请求参数) {String} visitMinutesReq.visitPurpose 拜访目的(多选，逗号分隔，如"1,2,3")
     * @apiParam (请求参数) {String} visitMinutesReq.visitPurposeOther 拜访目的其他说明(拜访目的包含"其他"时填写)(可选)
     * @apiParam (请求参数) {String} visitMinutesReq.assetReportId IPS报告ID(IPS陪访时必填)(可选)
     * @apiParam (请求参数) {String} visitMinutesReq.giveInformation 提供资料
     * @apiParam (请求参数) {String} visitMinutesReq.attendRole 客户参与人员及角色(可选)
     * @apiParam (请求参数) {String} visitMinutesReq.productServiceFeedback 对产品或服务的具体反馈(创新陪访必填)(可选)
     * @apiParam (请求参数) {String} visitMinutesReq.ipsFeedback 对于IPS报告反馈(IPS陪访必填)(可选)
     * @apiParam (请求参数) {String} visitMinutesReq.addAmountRmb 近期可用于加仓的金额(人民币)(IPS陪访必填之一)(可选)
     * @apiParam (请求参数) {String} visitMinutesReq.addAmountForeign 近期可用于加仓的金额(外币)(IPS陪访必填之一)(可选)
     * @apiParam (请求参数) {String} visitMinutesReq.focusAsset 近期关注的资产类别或具体产品(IPS陪访必填)(可选)
     * @apiParam (请求参数) {String} visitMinutesReq.estimateNeedBusiness 评估客户需求(创新业务等需求评估)(可选)
     * @apiParam (请求参数) {String} visitMinutesReq.nextPlan 下一步工作计划
     * @apiParam (请求参数) {Array} accompanyingList 陪访人列表(可选)
     * @apiParam (请求参数) {String} accompanyingList.accompanyingType 陪访人类型(1-项目经理 2-主管 3-总部业资 4-其他)
     * @apiParam (请求参数) {String} accompanyingList.accompanyingUserId 陪访人用户ID
     * @apiParamExample 请求参数示例
     * {
     *   "communicateReq": {
     *     "consCustNo": "CS123456789",
     *     "visitDt": "20240407",
     *     "visittype": "见面",
     *     "commcontent": "讨论投资策略和市场走向",
     *     "consulttype": "投资咨询"
     *   },
     *   "bookingReq": {
     *     "hasRemind": true,
     *     "nextvisittype": "电话",
     *     "nextdt": "20240420",
     *     "nextstarttime": "14:30",
     *     "nextendtime": "15:30",
     *     "nextvisitcontent": "跟进投资进展情况"
     *   },
     *   "visitMinutesReq": {
     *     "visitPurpose": "1,3,5",
     *     "visitPurposeOther": "深入了解客户需求",
     *     "assetReportId": "IPS20240407001",
     *     "giveInformation": "市场周报、产品说明书",
     *     "attendRole": "客户本人、客户家属",
     *     "productServiceFeedback": "对产品收益表现满意，希望增加产品种类",
     *     "ipsFeedback": "认可IPS报告分析结果，建议调整资产配置",
     *     "addAmountRmb": "500000",
     *     "addAmountForeign": "100000",
     *     "focusAsset": "固收类、黄金ETF",
     *     "estimateNeedBusiness": "有海外资产配置需求",
     *     "nextPlan": "准备详细的海外资产配置方案"
     *   },
     *   "accompanyingList": [
     *     {
     *       "accompanyingType": "2",
     *       "accompanyingUserId": "U00123"
     *     },
     *     {
     *       "accompanyingType": "3",
     *       "accompanyingUserId": "U00456"
     *     }
     *   ]
     * }
     * @apiSuccess (响应结果) {String} code 状态码
     * @apiSuccess (响应结果) {String} description 描述信息
     * @apiSuccess (响应结果) {Object} data 数据封装
     * @apiSuccess (响应结果) {String} data.communicateId 沟通记录ID
     * @apiSuccess (响应结果) {String} data.visitMinutesId 拜访纪要ID(无拜访纪要时为空)
     * @apiSuccessExample 响应结果示例
     * {
     *   "code": "0000",
     *   "description": "成功",
     *   "data": {
     *     "communicateId": "CM20240407001",
     *     "visitMinutesId": "VM20240407001"
     *   }
     * }
     */
    Response<AddCommunicateRecordResponse> addCommunicateRecord(AddCommunicateRecordRequest request);

    /**
     * @api {DUBBO} com.howbuy.crm.account.client.facade.commvisit.CsCommVisitFacade.queryCommunicateList(request)
     * @apiVersion 1.0.0
     * @apiGroup CsCommVisitFacade
     * @apiName queryCommunicateList()
     * @apiDescription 查询客户拜访列表
     * @apiParam (请求参数) {String} consCustNo 投顾客户号
     * @apiParam (请求参数) {Integer} pageNo 页码
     * @apiParam (请求参数) {Integer} pageSize 每页条数
     * @apiParamExample 请求参数示例
     * {
     *   "consCustNo": "CS123456789",
     *   "pageNo": 1,
     *   "pageSize": 10
     * }
     * @apiSuccess (响应结果) {String} code 状态码
     * @apiSuccess (响应结果) {String} description 描述信息
     * @apiSuccess (响应结果) {Object} data 数据封装
     * @apiSuccess (响应结果) {Array} data.list 沟通记录列表
     * @apiSuccess (响应结果) {String} data.list.pkId 沟通记录ID
     * @apiSuccess (响应结果) {String} data.list.visitDt 拜访日期
     * @apiSuccess (响应结果) {String} data.list.visitType 拜访方式
     * @apiSuccess (响应结果) {String} data.list.visitSummary 拜访总结
     * @apiSuccess (响应结果) {String} data.list.consultType 沟通类型
     * @apiSuccess (响应结果) {String} data.list.callInId 呼入ID
     * @apiSuccess (响应结果) {String} data.list.sourceAddr 来源地址
     * @apiSuccess (响应结果) {String} data.list.creator 创建人
     * @apiSuccess (响应结果) {String} data.list.consName 客户名称
     * @apiSuccess (响应结果) {String} data.list.orderId 订单ID
     * @apiSuccess (响应结果) {String} data.list.visitPurpose 拜访目的
     * @apiSuccess (响应结果) {String} data.list.visitMinutesId 拜访纪要ID
     * @apiSuccessExample 响应结果示例
     * {
     *   "code": "0000",
     *   "description": "成功",
     *   "data": {
     *     "list": []
     *   }
     * }
     */
    Response<CommunicateListVO> queryCommunicateList(QueryCommunicateListRequest request);
}