/**
 * Copyright (c) 2023, <PERSON><PERSON><PERSON><PERSON> HOWBUY INVESTMENT MANAGEMENT Co., Ltd.
 * All right reserved.
 * <p>
 * THIS IS UNPUBLISHED PROPRIETARY SOURCE CODE OF HOWBUY INVESTMENT
 * MANAGEMENT CO., LTD.  THE CONTENTS OF THIS FILE MAY NOT BE DISCLOSED
 * TO THIRD PARTIES, COPIED OR DUPLICATED IN ANY FORM, IN WHOLE OR IN PART,
 * WITHOUT THE PRIOR WRITTEN PERMISSION OF HOWBUY INVESTMENT MANAGEMENT
 * CO., LTD.
 */
package com.howbuy.crm.account.service.service.custinfo;

import cn.hutool.core.util.RandomUtil;
import com.alibaba.fastjson.JSON;
import com.google.common.collect.Lists;
import com.howbuy.common.utils.Assert;
import com.howbuy.common.utils.StringUtil;
import com.howbuy.crm.account.client.enums.NotifyMsgTypeEnum;
import com.howbuy.crm.account.client.enums.custinfo.AbnormaSceneTypeEnum;
import com.howbuy.crm.account.client.enums.custinfo.FullCustSourceEnum;
import com.howbuy.crm.account.client.enums.message.AccountTypeEnum;
import com.howbuy.crm.account.client.request.custinfo.*;
import com.howbuy.crm.account.client.request.deposit.HkDepositRequest;
import com.howbuy.crm.account.client.response.PageVO;
import com.howbuy.crm.account.client.response.Response;
import com.howbuy.crm.account.client.response.bankcardinfo.HkBankCardInfoVO;
import com.howbuy.crm.account.client.response.custinfo.*;
import com.howbuy.crm.account.client.response.deposit.HkDepositResultVO;
import com.howbuy.crm.account.dao.bo.custinfo.CmConscustForAnalyseBO;
import com.howbuy.crm.account.dao.po.custinfo.CmHkConscustPO;
import com.howbuy.crm.account.dao.po.message.CmAccountCenterMessagePO;
import com.howbuy.crm.account.dao.req.custinfo.CmHkCustReqVO;
import com.howbuy.crm.account.service.business.custinfo.CmCustProcessBusinessService;
import com.howbuy.crm.account.service.business.custinfo.CustMessageOperateExecutor;
import com.howbuy.crm.account.service.business.custinfo.HkCustBindBusinessService;
import com.howbuy.crm.account.service.commom.constant.Constants;
import com.howbuy.crm.account.service.commom.constant.NofityMsgConstans;
import com.howbuy.crm.account.service.message.dto.NofifyMsgDTO;
import com.howbuy.crm.account.service.message.notify.PushNotifyMsgService;
import com.howbuy.crm.account.service.outerservice.acct.HboneAcctInfoOuterService;
import com.howbuy.crm.account.service.outerservice.hkacct.HkCustBasicInfoOuterService;
import com.howbuy.crm.account.service.repository.custinfo.AbnormalCustRepository;
import com.howbuy.crm.account.service.repository.custinfo.CmHkCustInfoRepository;
import com.howbuy.crm.account.service.repository.message.CmAccountCenterMessageRepository;
import com.howbuy.crm.account.service.req.custinfo.HkAcctCustInfoReqVO;
import com.howbuy.crm.account.service.req.custinfo.HkCustRelationOptReqVO;
import com.howbuy.crm.account.service.vo.custinfo.AbnormalAnalyseResultVO;
import com.howbuy.crm.account.service.vo.custinfo.CrmHkOfflineOpenAcctCustInfoVO;
import com.howbuy.crm.account.service.vo.custinfo.HkMessageCustInfoVO;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Objects;

/**
 * @description: (crm客户信息service)
 * <AUTHOR>
 * @date 2023/12/13 16:41
 * @since JDK 1.8
 */
@Service
@Slf4j
public class HkCustInfoService {


    @Autowired
    private CmHkCustInfoRepository hkCustRepository;

    @Autowired
    private HkCustBasicInfoOuterService hkCustInfoOuterService;

    @Autowired
    private AbnormalCustRepository abnormalCustRepository;
    @Autowired
    private CmCustProcessBusinessService procBusinessService;

    @Autowired
    private HkCustBindBusinessService hkCustBindBusinessService;

    @Autowired
    private HboneAcctInfoOuterService hboneAcctInfoOuterService;
    
    @Autowired
    private CustMessageOperateExecutor custMessageOperateExecutor;

    @Autowired
    private CmHkCustInfoRepository cmHkCustInfoRepository;

    @Autowired
    private CmCustProcessBusinessService processBusinessService;

    @Autowired
    private PushNotifyMsgService pushNotifyMsgService;

    @Autowired
    private CmAccountCenterMessageRepository cmAccountCenterMessageRepository;



    /**
     * @description:(绑定 香港交易账号的 关联关系)
     * @param bindOptRequest
     * @return com.howbuy.crm.account.client.response.Response<java.lang.String>
     * @author: haoran.zhang
     * @date: 2023/12/15 11:25
     * @since JDK 1.8
     */
    public Response<String> bindHkTxAcct(HkAcctRelationOptRequest bindOptRequest) {
        return hkCustBindBusinessService.bindHkTxAcct(bindOptRequest);
    }



    /**
     * @description:(解绑 香港交易账号的 关联关系)
     * @param bindOptRequest
     * @return com.howbuy.crm.account.client.response.Response<java.lang.String>
     * @throws Exception
     * @throws
     * @since JDK 1.8
     */
    public Response<String> unBindHkTxAcct(HkAcctRelationOptRequest bindOptRequest) {
        return hkCustBindBusinessService.unBindHkTxAcct(bindOptRequest);
    }



    /**
     * @description:(对象 处理  转移 )
     * @param mergetOptRequest	
     * @return void
     * @author: haoran.zhang
     * @date: 2023/12/15 16:24
     * @since JDK 1.8
     */
    private HkCustRelationOptReqVO getOptVoByMergeRequest(HkAcctMergeOptRequest mergetOptRequest){
        HkCustRelationOptReqVO optVo=new HkCustRelationOptReqVO();
        optVo.setOperator(mergetOptRequest.getOperator());
        optVo.setRemark(mergetOptRequest.getRemark());
        optVo.setOperateChannel(mergetOptRequest.getOperateChannel());
        optVo.setOperateSource(mergetOptRequest.getOperateSource());
        return optVo;
    }

    /**
     * @description:(合并客户带来的 香港交易账号信息的处理)
     * @param mergetOptRequest
     * @return com.howbuy.crm.account.client.response.Response<java.lang.String>
     * @author: haoran.zhang
     * @date: 2023/12/15 16:30
     * @since JDK 1.8
     */
    public Response<String> mergeHkTxAcct(HkAcctMergeOptRequest mergetOptRequest) {
        //待删除客户关系
        List<String> deleteCustNoList= mergetOptRequest.getDeleteCustNoList();
        deleteCustNoList.forEach(deleteCustNo->{
            CmHkConscustPO existAcct=hkCustRepository.selectByCustNo(deleteCustNo);
            if(existAcct!=null){
                HkCustRelationOptReqVO unBindOptVo=getOptVoByMergeRequest(mergetOptRequest);
                unBindOptVo.setCustNo(existAcct.getConscustno());
                unBindOptVo.setHkTxAcctNo(existAcct.getHkTxAcctNo());
                Response<String>  unbindResp= hkCustRepository.executeUnBind( unBindOptVo);
                log.info("解除客户号：{} 的香港交易账号绑定关系, response:{}",deleteCustNo, JSON.toJSONString(unbindResp));
            }
        });

        String  definedHkTxAcctNo=mergetOptRequest.getDefinedHkTxAcctNo();
        //保留的客户 不指定 香港客户号。 无需后续处理
        if(StringUtil.isEmpty(definedHkTxAcctNo)){
            return  Response.ok();
        }

        //保留的客户 指定了 香港客户号。
        String preserveCustNo=mergetOptRequest.getPreserveCustNo();
        CmHkConscustPO  preservedAcct=hkCustRepository.selectByCustNo(preserveCustNo);
        //指定的香港客户号与当前香港客户号一致，无需后续处理
        if(preservedAcct!=null && StringUtil.isEqual(definedHkTxAcctNo,preservedAcct.getHkTxAcctNo())){
            log.info("客户号：{} 当前香港交易账号：{}，无需处理！", preserveCustNo,preservedAcct.getHkTxAcctNo());
            return Response.ok();
        }

        //调用香港账户中心接口  获取 ebrokerId
        HkAcctCustDetailInfoVO hkAcctCustInfoVO=queryHkCustInfo(definedHkTxAcctNo);
        if(hkAcctCustInfoVO==null){
            return Response.fail("该香港交易账号在香港账户中心不存在!");
        }
        //指定的香港客户号与当前香港客户号 不一致
        //先解除 当前存在的关系
        if(preservedAcct!=null){
            HkCustRelationOptReqVO unBindOptVo=getOptVoByMergeRequest(mergetOptRequest);
            unBindOptVo.setCustNo(preservedAcct.getConscustno());
            unBindOptVo.setHkTxAcctNo(preservedAcct.getHkTxAcctNo());
            Response<String>  unbindResp= hkCustRepository.executeUnBind( unBindOptVo);
            log.info("解除客户号：{} 的香港交易账号绑定关系, response:{}",preserveCustNo, JSON.toJSONString(unbindResp));
        }

        //插入新的绑定关系
        HkCustRelationOptReqVO bindOptVo=getOptVoByMergeRequest(mergetOptRequest);
        bindOptVo.setCustNo(preserveCustNo);
        bindOptVo.setHkTxAcctNo(definedHkTxAcctNo);
        bindOptVo.setEbrokerId(hkAcctCustInfoVO.getEbrokerId());
        Response<String>  bindResp= hkCustRepository.executeBind( bindOptVo);
        log.info("新建关系绑定，客户号：{} 香港交易账号：{} , response:{}",preserveCustNo,definedHkTxAcctNo, JSON.toJSONString(bindResp));

        return Response.ok();
    }

    /**
     * @description:((根据 香港客户关联关系查询reqVO 查询 香港交易账户关联关系信息)
     * @param relationRequest
     * @return com.howbuy.crm.account.client.response.custinfo.HkAcctCustRelationInfoVO
     * @author: haoran.zhang
     * @date: 2023/12/15 10:13
     * @since JDK 1.8
     */
    public HkAcctCustRelationInfoVO queryHkAcctCustRelation(HkAcctRelationRequest relationRequest){
        CmHkCustReqVO reqVO=new CmHkCustReqVO();
        reqVO.setHkTxAcctNo(relationRequest.getHkTxAcctNo());
        reqVO.setConscustno(relationRequest.getCustNo());
        reqVO.setHkcustid(relationRequest.getEbrokerId());
        reqVO.setId(relationRequest.getId());
        CmHkConscustPO  hkCustPo=hkCustRepository.selectByReqVO(reqVO);
        if(hkCustPo!=null){
            HkAcctCustRelationInfoVO returnVo=new HkAcctCustRelationInfoVO();
            returnVo.setCustNo(hkCustPo.getConscustno());
            returnVo.setEbrokerId(hkCustPo.getHkcustid());
            returnVo.setHkTxAcctNo(hkCustPo.getHkTxAcctNo());
            returnVo.setId(hkCustPo.getId());
            return returnVo;
        }
        return null;
    }



    /**
     * @description: (分页查询  香港账户中心  香港客户信息)
     * @param hkCustInfoRequest
     * @return
     */
    public PageVO<HkAcctCustInfoVO> queryHkCustInfoPage(HkAcctCustInfoRequest hkCustInfoRequest) {
        HkAcctCustInfoReqVO outerReqVo = new HkAcctCustInfoReqVO();
        BeanUtils.copyProperties(hkCustInfoRequest, outerReqVo);
        PageVO<HkAcctCustInfoVO> pageResp = hkCustInfoOuterService.queryHkCustInfoPage(outerReqVo);
        return pageResp;
    }


    /**
     * @description:(根据 香港交易账号 查询 香港客户信息)
     * @param hkTxAcctNo
     * @return com.howbuy.crm.base.response.CoreReturnMessageDto<com.howbuy.crm.conscust.dto.HkCustInfoDto>
     * @author: haoran.zhang
     * @date: 2023年12月8日 20:41:21
     * @since JDK 1.8
     */
    public HkAcctCustDetailInfoVO queryHkCustInfo(String hkTxAcctNo) {
        if(StringUtil.isEmpty(hkTxAcctNo)){
            return null;
        }
//        Assert.notNull(hkTxAcctNo,"香港交易账号不能为空");
        return hkCustInfoOuterService.queryHkCustDetailInfo(hkTxAcctNo);
    }


    /**
     * @description: crm资料[好买香港-开户]，账户柜台审核通过之后，后续处理
     * @param hkAcctCreateOptVO 香港账号开户请求
     * @return com.howbuy.crm.account.client.response.Response<java.lang.String>
     * @author: jin.wang03
     * @date: 2023/12/18 13:15
     * @since JDK 1.8
     */
    public Response<String> dealAfterCrmCreateHkTxAcct(HkAcctCreateOptVO hkAcctCreateOptVO) {
        // 因香港异常客户页，有按钮【新增客户】需要重跑处理流程，
        // 所以 先将入参VO存到CM_ACCOUNT_CENTER_MESSAGE表中
        // (除CRM资料[好买香港-开户]，其他流程都是由账号中心消息触发的，为保证存入同一张表，将HkAcctCreateOptVO模拟成一个消息)
        String messageId = saveCrmOpenAcctVo(hkAcctCreateOptVO);

        // 分析和处理
        return analyzeAndProcess(hkAcctCreateOptVO, messageId);
    }

    /**
     * @description:(请在此添加描述)
     * @param hkAcctCreateOptVO
     * @param messageId
     * @return com.howbuy.crm.account.client.response.Response<java.lang.String>
     * @author: jin.wang03
     * @date: 2025/6/4 13:55
     * @since JDK 1.8
     */
    public Response<String> analyzeAndProcess(HkAcctCreateOptVO hkAcctCreateOptVO, String messageId) {
        //构建 处理对象。
        CrmHkOfflineOpenAcctCustInfoVO analyseVo = new CrmHkOfflineOpenAcctCustInfoVO();
        analyseVo.setAbnormalSource(FullCustSourceEnum.HK_OPEN_ACCOUNT_CRM.getCode());
        analyseVo.setCreator(hkAcctCreateOptVO.getOperator());
        analyseVo.setOperateChannel(Constants.OPERATE_CHANNEL_MENU);
        analyseVo.setMessageId(messageId);
        analyseVo.setHkTxAcctNo(hkAcctCreateOptVO.getHkCustNo());
        // 上传资料的投顾客户号
        analyseVo.setDealConsCustNo(hkAcctCreateOptVO.getCustNo());
        // 请求香港账户中心，填充信息
        fillHkAcctInfo(analyseVo);

        custMessageOperateExecutor.executeMessageAfterAnalyse(
                analyseVo,
                this::analyzeHkAccount,
                Constants.INTO_ABNORMAL_HK,
                //处理
                resultVo -> {
                    CmConscustForAnalyseBO processCust = resultVo.getProcessedCustInfo();
                    if (processCust != null) {
                        associateHkAcctRelation(processCust, resultVo.getAnalyseVo());
                    }
                    return Response.ok();
                }
        );

        return Response.ok();
    }

    /**
     * @description:(请在此添加描述)
     * @param hkAcctCreateOptVO
     * @return java.lang.String
     * @author: jin.wang03
     * @date: 2025/6/4 13:55
     * @since JDK 1.8
     */
    private String saveCrmOpenAcctVo(HkAcctCreateOptVO hkAcctCreateOptVO) {
        CmAccountCenterMessagePO cmAccountCenterMessagePO = new CmAccountCenterMessagePO();
        // id为CRM_OPEN_ACCT开头，加18位随机数
        String id = Constants.ID_CRM_OPEN_ACCT_PREFIX + RandomUtil.randomNumbers(18);
        cmAccountCenterMessagePO.setId(id);
        cmAccountCenterMessagePO.setAccountType(AccountTypeEnum.HK.getCode());
        cmAccountCenterMessagePO.setTopic(Constants.TOPIC_HK_OPEN_ACCT);
        cmAccountCenterMessagePO.setTag(Constants.CRM_OPEN_ACCT);
        cmAccountCenterMessagePO.setMqMessageText(JSON.toJSONString(hkAcctCreateOptVO));
        cmAccountCenterMessageRepository.insert(cmAccountCenterMessagePO);

        return id;
    }


    /**
     * @description: 请求香港账户中心 填充HK账户信息
     * @param analyseVo
     * @return void
     * @author: jin.wang03
     * @date: 2024/12/17 19:05
     * @since JDK 1.8
     */
    private void fillHkAcctInfo(CrmHkOfflineOpenAcctCustInfoVO analyseVo) {
        HkAcctCustDetailInfoVO hkVo = queryHkCustInfo(analyseVo.getHkTxAcctNo());
        analyseVo.setHboneNo(hkVo.getHboneNo());
        String usedCustName =
                StringUtil.isNotBlank(hkVo.getCustChineseName()) ? hkVo.getCustChineseName() : hkVo.getCustEnName();

        analyseVo.setCustName(usedCustName);
        analyseVo.setInvestType(hkVo.getInvstType());
        analyseVo.setMobileAreaCode(hkVo.getMobileAreaCode());
        analyseVo.setMobileDigest(hkVo.getMobileDigest());
        analyseVo.setMobileMask(hkVo.getMobileMask());
        analyseVo.setIdSignAreaCode(hkVo.getIdSignAreaCode());
        analyseVo.setIdType(hkVo.getIdType());
        analyseVo.setIdNoDigest(hkVo.getIdNoDigest());
        analyseVo.setIdNoMask(hkVo.getIdNoMask());
    }

    /**
     * @description:(请在此添加描述)
     * @param processedCustInfo
     * @param analyseVo
     * @return void
     * @author: jin.wang03
     * @date: 2024/12/6 14:32
     * @since JDK 1.8
     */
    private void associateHkAcctRelation(CmConscustForAnalyseBO processedCustInfo, CrmHkOfflineOpenAcctCustInfoVO analyseVo) {
        String hkTxAcctNoFromResp = analyseVo.getHkTxAcctNo();
        String hboneNoFromResp = analyseVo.getHboneNo();

        String custNo = processedCustInfo.getConscustno();

        //更新 投顾客户信息
        Response<String> updateResp = procBusinessService.updateCustInfoByHk(analyseVo, processedCustInfo);
        log.info("【{}】,hkTxAcctNo:{},更新投顾客户信息:{} ,返回结果:{}",
                FullCustSourceEnum.HK_OPEN_ACCOUNT_CRM.getDescription(), hkTxAcctNoFromResp, custNo, JSON.toJSONString(updateResp));

        // 香港账号中心开户时关联的一账通号
        if (StringUtil.isBlank(processedCustInfo.getHboneNo())) {
            procBusinessService.processExtraHboneAccount(analyseVo);
        }

        //发送 开户成功通知信息
        NofifyMsgDTO nofifyMsgDto = new NofifyMsgDTO();
        nofifyMsgDto.setBusinessId(NofityMsgConstans.HK_ONLINE_OPEN_ACCT_SUCCESS);
        nofifyMsgDto.setNotifyType(NotifyMsgTypeEnum.HK_TX_ACCT_NO.getCode());
        nofifyMsgDto.setHkTxAcctNo(hkTxAcctNoFromResp);

        //模板ID：201483 .  【香港开户成功】客户：${custName}（${custNo}）开户申请成功，请及时关注
        Map<String, String> paramMap = new HashMap<>();
        paramMap.put("custName", processedCustInfo.getCustname());
        paramMap.put("custNo", processedCustInfo.getConscustno());
        nofifyMsgDto.setParamMap(paramMap);
        pushNotifyMsgService.pushNofityMsg(nofifyMsgDto);

    }

    /**
     * @description: 处理[香港客户]关联[投顾客户]异常数据
     * @param analyseVo
     * @return com.howbuy.crm.account.client.response.Response<java.lang.String>
     * @throws Exception
     * @throws
     * @since JDK 1.8
     */
    public AbnormalAnalyseResultVO<CrmHkOfflineOpenAcctCustInfoVO> analyzeHkAccount(CrmHkOfflineOpenAcctCustInfoVO analyseVo) {
        CmConscustForAnalyseBO processedCustInfo = abnormalCustRepository.queryCustBOByCustNo(analyseVo.getDealConsCustNo());
        Assert.notNull(processedCustInfo, "投顾客户不存在！");

        if (StringUtils.isEmpty(processedCustInfo.getHkTxAcctNo())) {
            // 绑定【香港客户号A】和【投顾客户号A】
            Response<String> associatedResponse = processBusinessService.associateHkAcctRelation(processedCustInfo, analyseVo);
            if (!associatedResponse.isSuccess()) {
                // 理论上，【新的香港客户号】绑定投顾客户号 不会出现绑定失败的情况，这里做一个兜底
                return AbnormalAnalyseResultVO.notNormalData(analyseVo,
                        AbnormaSceneTypeEnum.HK_CUST_NO_OCCUPIED,
                        Lists.newArrayList(processedCustInfo));
            }
        } else if (!StringUtils.equals(processedCustInfo.getHkTxAcctNo(), analyseVo.getHkTxAcctNo())) {
            // [投顾客户号A]了绑定香港客户号，且和 账户中心开户返回的 不一致
            // 进异常表
            return AbnormalAnalyseResultVO.notNormalData(analyseVo,
                    AbnormaSceneTypeEnum.HK31,
                    Lists.newArrayList(processedCustInfo));
        }

        // 前置条件：【香港客户号A】已关联【投顾客户号A】
        // 1、判断①：【香港客户号A】绑定的【一账通号A】是否已关联【投顾客户号】
        String hkTxAcctNo = analyseVo.getHkTxAcctNo();
        String hboneNo = analyseVo.getHboneNo();
        String consCustNoA = processedCustInfo.getConscustno();

        // 【一账通号A】是否关联【投顾客户号】
        List<CmConscustForAnalyseBO> hboneCustList = abnormalCustRepository.queryCustBOByHboneNo(hboneNo);

        if (CollectionUtils.isEmpty(hboneCustList)) {
            // (2) 若未关联投顾客户号，则进行判断③
            log.info("一账通号 {} 未关联投顾客户号，检查投顾客户号是否绑定其他一账通号", hboneNo);

            // 3、判断③：【香港客户号A】绑定的【投顾客户号A】是否已关联【一账通号】
            if (StringUtil.isNotBlank(processedCustInfo.getHboneNo())) {
                // (2) 若是，（此时一定是另一个一账通号B），则异常数据进"香港异常客户表"
                log.info("投顾客户号 {} 已绑定一账通号 {}，与香港客户号绑定的一账通号 {} 不一致",
                        consCustNoA, processedCustInfo.getHboneNo(), hboneNo);

                // 异常码：HK09 香港客户号绑定一账通与投顾客户号绑定一账通不一致
                return AbnormalAnalyseResultVO.notNormalData(
                        analyseVo,
                        AbnormaSceneTypeEnum.HK09,
                        Lists.newArrayList(processedCustInfo));
            } else {
                // (1) 若否，则进行判断②
                log.info("投顾客户号 {} 未绑定一账通号，进入证件判断", consCustNoA);
                return checkIdCardMatchAndProcess(analyseVo, processedCustInfo);
            }
        } else {
            CmConscustForAnalyseBO hboneCust = hboneCustList.get(0);
            if (Objects.equals(consCustNoA, hboneCust.getConscustno())) {
                // (1) 若已关联【投顾客户号A】（即香港客户号和一账通号，绑定了同一个投顾客户号），则进行判断②
                log.info("一账通号 {} 已关联投顾客户号 {}，与香港客户号关联的投顾客户号一致，进入证件判断",
                        hboneNo, consCustNoA);
                return checkIdCardMatchAndProcess(analyseVo, processedCustInfo);
            } else {
                // (3) 若已关联其他【投顾客户号B】，则异常数据进"香港异常客户表"
                log.info("一账通号 {} 已关联投顾客户号 {}，与香港客户号关联的投顾客户号 {} 不一致",
                        hboneNo, hboneCust.getConscustno(), consCustNoA);

                // 异常码：HK10 香港客户号绑定的投顾客户号与香港客户号绑定一账通对应的投顾客户号不一致
                List<CmConscustForAnalyseBO> relatedCustList = Lists.newArrayList(processedCustInfo, hboneCust);
                return AbnormalAnalyseResultVO.notNormalData(
                        analyseVo,
                        AbnormaSceneTypeEnum.HK10,
                        relatedCustList);
            }
        }
    }
    
    /**
     * @description: 判断②：香港【证件A】是否已关联【投顾客户号】，并进行相应处理
     * @param analyseVo 分析对象
     * @param processedCustInfo 投顾客户信息
     * @return com.howbuy.crm.account.service.vo.custinfo.AbnormalAnalyseResultVO<com.howbuy.crm.account.service.vo.custinfo.CrmHkOfflineOpenAcctCustInfoVO>
     * @author: haoran.zhang
     * @date: 2024/05/07
     * @since JDK 1.8
     */
    private AbnormalAnalyseResultVO<CrmHkOfflineOpenAcctCustInfoVO> checkIdCardMatchAndProcess(
            CrmHkOfflineOpenAcctCustInfoVO analyseVo, CmConscustForAnalyseBO processedCustInfo) {
        
        String consCustNoA = processedCustInfo.getConscustno();
        String hboneNo = analyseVo.getHboneNo();
        
        // 判断逻辑：取香港的【证件类型+证件号】，查询是否存在全匹配的【投顾客户号】
        List<CmConscustForAnalyseBO> idMatchCustList = queryCustListByIdInfo(analyseVo);
        
        if (CollectionUtils.isEmpty(idMatchCustList)) {
            // (1) 若不存在，说明当前香港客户号和投顾客户号的绑定关系正常
            log.info("香港证件未关联投顾客户号，当前香港客户号和投顾客户号的绑定关系正常");
            return normalProcessing(analyseVo, processedCustInfo);
        } else if (idMatchCustList.size() == 1) {
            CmConscustForAnalyseBO idMatchCust = idMatchCustList.get(0);
            if (Objects.equals(consCustNoA, idMatchCust.getConscustno())) {
                // (2) 若存在，且唯一，且该投顾客户号=投顾客户号A，说明当前香港客户号和投顾客户号的绑定关系正常
                log.info("香港证件关联到投顾客户号 {}，与香港客户号关联的投顾客户号一致，绑定关系正常", consCustNoA);
                return normalProcessing(analyseVo, processedCustInfo);
            } else {
                // (3) 若存在，且唯一，且该投顾客户号≠投顾客户号A，则异常数据进"香港异常客户表"
                log.info("香港证件关联到投顾客户号 {}，与香港客户号关联的投顾客户号 {} 不一致",
                        idMatchCust.getConscustno(), consCustNoA);
                
                // 异常码：HK07 香港开户证件被其他投顾客户号占用
                List<CmConscustForAnalyseBO> relatedCustList = Lists.newArrayList(processedCustInfo, idMatchCust);
                return AbnormalAnalyseResultVO.notNormalData(
                        analyseVo,
                        AbnormaSceneTypeEnum.HK07,
                        relatedCustList);
            }
        } else {
            // (4) 若存在，但不唯一，则异常数据进"香港异常客户表"
            log.info("香港证件关联到多个投顾客户号: {}", idMatchCustList);
            
            // 异常码：HK08 香港开户证件找到多个投顾客户号
            List<CmConscustForAnalyseBO> relatedCustList = Lists.newArrayList(processedCustInfo);
            // 添加除当前客户外的其他匹配客户
            idMatchCustList.stream()
                    .filter(cust -> !Objects.equals(cust.getConscustno(), consCustNoA))
                    .forEach(relatedCustList::add);
            
            return AbnormalAnalyseResultVO.notNormalData(
                    analyseVo,
                    AbnormaSceneTypeEnum.HK08,
                    relatedCustList);
        }
    }
    
    /**
     * @description: 正常处理流程
     * @param analyseVo 分析对象
     * @param processedCustInfo 投顾客户信息
     * @return com.howbuy.crm.account.service.vo.custinfo.AbnormalAnalyseResultVO<com.howbuy.crm.account.service.vo.custinfo.CrmHkOfflineOpenAcctCustInfoVO>
     * @author: haoran.zhang
     * @date: 2024/05/07
     * @since JDK 1.8
     */
    private AbnormalAnalyseResultVO<CrmHkOfflineOpenAcctCustInfoVO> normalProcessing(
            CrmHkOfflineOpenAcctCustInfoVO analyseVo, CmConscustForAnalyseBO processedCustInfo) {
        
        // 返回正常处理结果
        AbnormalAnalyseResultVO<CrmHkOfflineOpenAcctCustInfoVO> result = AbnormalAnalyseResultVO.normalData(analyseVo);
        result.setProcessedCustInfo(processedCustInfo);
        result.setNeedProcess(true);
        return result;
    }
    
    /**
     * @description: 根据证件信息查询客户列表 (Null safe)
     * @param analyseVo
     * @return java.util.List<com.howbuy.crm.account.dao.bo.custinfo.CmConscustForAnalyseBO>
     * @author: haoran.zhang
     * @date: 2024/05/07
     * @since JDK 1.8
     */
    private List<CmConscustForAnalyseBO> queryCustListByIdInfo(CrmHkOfflineOpenAcctCustInfoVO analyseVo) {
        if (StringUtil.isBlank(analyseVo.getIdType()) || StringUtil.isBlank(analyseVo.getIdNoDigest())) {
            return Lists.newArrayList();
        }
        return abnormalCustRepository.queryCustListByIdNo(
                analyseVo.getInvestType(),
                analyseVo.getIdType(),
                analyseVo.getIdSignAreaCode(),
                analyseVo.getIdNoDigest());
    }

    /**
     * @description: 根据 投顾客户号 查询香港账户中心香港客户信息
     * @param custNo 投顾客户号
     * @return com.howbuy.crm.account.client.response.custinfo.HkAcctCustDetailInfoVO 香港客户信息
     * @author: jin.wang03
     * @date: 2023/12/22 13:31
     * @since JDK 1.8
     */
    public HkAcctCustDetailInfoVO queryHkCustInfoByCustNo(String custNo) {
        CmHkCustReqVO custReqVO = new CmHkCustReqVO();
        custReqVO.setConscustno(custNo);
        CmHkConscustPO cmHkConscustPO = hkCustRepository.selectByReqVO(custReqVO);

        if (cmHkConscustPO != null) {
            return queryHkCustInfo(cmHkConscustPO.getHkTxAcctNo());
        }
        return null;
    }


    /**
     * @description: 根据香港交易账号查询香港客户风险测评问卷流水
     * @param hkTxAcctNo 香港客户号
     * @param startDate	开始时间
     * @param endDate 结束时间
     * @return java.util.List<com.howbuy.crm.account.client.response.custinfo.HkKycAnswerVO>
     * @author: jin.wang03
     * @date: 2023/12/29 13:34
     * @since JDK 1.8
     */
    public List<HkKycAnswerVO> queryHkKycAnswerListByHkTxAcctNo(String hkTxAcctNo, String startDate, String endDate) {
        return hkCustInfoOuterService.queryHkKycAnswerListByHkTxAcctNo(hkTxAcctNo, startDate, endDate);
    }

    /**
     * @description: 查询 香港账户中心  客户信息 敏感信息
     * @param hkTxAcctNo 香港客户号
     * @return com.howbuy.crm.account.client.response.custinfo.HkAcctCustDetailInfoVO 香港客户信息 敏感信息
     * @author: jin.wang03
     * @date: 2024/1/4 10:18
     * @since JDK 1.8
     */
    public HkAcctCustSensitiveInfoVO queryHkCustSensitiveInfo(String hkTxAcctNo) {
        return hkCustInfoOuterService.queryHkCustSensitiveInfo(hkTxAcctNo);
    }

    /**
     * @description:(查询 香港账户中心  客户手机号 敏感信息)
     * @param hkTxAcctNo
     * @return java.lang.String
     * @author: haoran.zhang
     * @date: 2023/12/20 13:53
     * @since JDK 1.8
     */
    public String queryHkSensitiveMobile(String hkTxAcctNo) {
        return hkCustInfoOuterService.queryHkSensitiveMobile(hkTxAcctNo);
    }

    /**
     * @description: 根据香港交易账号查询香港客户银行卡信息
     * @param hkTxAcctNo 香港客户号
     * @return java.util.List<com.howbuy.crm.account.client.response.bankcardinfo.HkBankCardInfoVO> 香港客户银行卡信息列表
     * @author: jin.wang03
     * @date: 2024/1/11 14:39
     * @since JDK 1.8
     */
    public List<HkBankCardInfoVO> queryHkBankCardInfoListByHkTxAcctNo(String hkTxAcctNo) {
        return hkCustInfoOuterService.queryHkCustBankCardList(hkTxAcctNo);
    }

    /**
     * @description: 根据香港交易账号查询香港客户全量信息
     * @param hkTxAcctNo 香港客户号
     * @return com.howbuy.crm.account.client.response.custinfo.HkAcctCustInfoHolderVO 香港客户[所有信息]持有者对象，包含客户基本信息、税务信息、声明信息、银行卡信息、kyc信息、一账通关系信息等等
     * @author: jin.wang03
     * @date: 2024/1/11 15:22
     * @since JDK 1.8
     */
    public HkAcctCustInfoHolderVO queryHkCustAllInfoByHkTxAcctNo(String hkTxAcctNo) {
        return hkCustInfoOuterService.queryHkCustAllInfoByHkTxAcctNo(hkTxAcctNo);
    }


    /**
     * @description: 根据香港交易账号查询香港客户[开户结果]
     * @param hkTxAcctNo 香港客户号
     * @return com.howbuy.crm.account.client.response.custinfo.HkOpenAcctResultVO
     * @author: jin.wang03
     * @date: 2024/1/12 14:23
     * @since JDK 1.8
     */
    public HkOpenAcctResultVO queryHkOpenAcctResultByHkTxAcctNo(String hkTxAcctNo) {
        return hkCustInfoOuterService.queryHkOpenAcctResultByHkTxAcctNo(hkTxAcctNo);
    }


    /**
     * @description: 根据条件分页查询香港客户[入金结果]，查询条件HkDepositRequest可继续扩充
     * @param hkDepositRequest 香港客户[入金结果]请求对象，包含香港客户号、入金流水号、入金日期等等
     * @return java.util.List<com.howbuy.crm.account.client.response.deposit.HkDepositResultVO> 香港客户[入金结果]列表
     * @author: jin.wang03
     * @date: 2024/1/15 10:10
     * @since JDK 1.8
     */
    public PageVO<HkDepositResultVO> queryHkDepositResult(HkDepositRequest hkDepositRequest) {
        return hkCustInfoOuterService.queryHkDepositResult(hkDepositRequest);
    }

    /**
     * @description: 根据 香港客户信息 更新投顾客户信息
     * @param updateCustInfoByHkRequest
     * @return com.howbuy.crm.account.client.response.Response<java.lang.String>
     * @author: jin.wang03
     * @date: 2024/12/12 17:52
     * @since JDK 1.8
     */
    public Response<String> updateCustInfoByHk(UpdateCustInfoByHkRequest updateCustInfoByHkRequest) {
        CmConscustForAnalyseBO cmConscustForAnalyseBO = new CmConscustForAnalyseBO();
        cmConscustForAnalyseBO.setConscustno(updateCustInfoByHkRequest.getCustNo());

        HkMessageCustInfoVO hkMessageCustInfoVO = new HkMessageCustInfoVO();
        hkMessageCustInfoVO.setHkTxAcctNo(updateCustInfoByHkRequest.getHkCustNo());
        hkMessageCustInfoVO.setCreator(updateCustInfoByHkRequest.getOperator());
        return processBusinessService.updateCustInfoByHk(hkMessageCustInfoVO, cmConscustForAnalyseBO);
    }



    /**
     * @description: 根据香港账号创建 crm 客户信息
     * @param createCustByHkRequest
     * @return com.howbuy.crm.account.client.response.Response<java.lang.String>
     * @author: haoran.zhang
     * @date: 2025年5月19日
     * @since JDK 1.8
     */
    public Response<String> createCustInfoByHk(CreateCustByHkRequest createCustByHkRequest){
        String hkTxAcctNo=createCustByHkRequest.getHkTxAcctNo();
        String operateSource=createCustByHkRequest.getOperateSource();
        String operateChannel=createCustByHkRequest.getOperateChannel();
        String custSource=createCustByHkRequest.getCustSource();
        return processBusinessService.createCustInfoByHk(hkTxAcctNo,operateSource,operateChannel, custSource);
    }
}