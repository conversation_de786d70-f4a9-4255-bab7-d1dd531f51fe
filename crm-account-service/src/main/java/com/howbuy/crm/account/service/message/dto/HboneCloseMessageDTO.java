/**
 * Copyright (c) 2023, <PERSON><PERSON><PERSON><PERSON> HOWBUY INVESTMENT MANAGEMENT Co., Ltd.
 * All right reserved.
 * <p>
 * THIS IS UNPUBLISHED PROPRIETARY SOURCE CODE OF HOWBUY INVESTMENT
 * MANAGEMENT CO., LTD.  THE CONTENTS OF THIS FILE MAY NOT BE DISCLOSED
 * TO THIRD PARTIES, COPIED OR DUPLICATED IN ANY FORM, IN WHOLE OR IN PART,
 * WITHOUT THE PRIOR WRITTEN PERMISSION OF HOWBUY INVESTMENT MANAGEMENT
 * CO., LTD.
 */
package com.howbuy.crm.account.service.message.dto;

import lombok.Data;

import java.io.Serializable;

/**
 * @description: (账户中心 一账通消息[TOPIC_UPDATE_CUST ]-手机号变更[MOBILE_UPDATE]公共解析 dto)
 * <AUTHOR>
 * @date 2023/12/29 11:23
 * @since JDK 1.8
 */
@Data
public class HboneCloseMessageDTO implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     *  一账通账号(hboneNo)
     */
    private String hboneNo;

    /**
     *   分销机构号(disCode)
     */
    private String disCode;

    /**
    * 手机号摘要(mobileDigest)
     */
    private String mobileDigest;

    /**
     * 手机号掩码(mobileMask)
     */
    private String mobileMask;

    /**
     * 手机号密文(mobileCipher)
     */
    private String mobileCipher;

    /**
     *  证件类型(idType)
     */
    private String idType;


    /**
     * 证件号码摘要(idNoDigest)
     */
    private String idNoDigest;

    /**
     * 证件号码掩码(idNoMask)
     */
    private String idNoMask;

    /**
     * 证件号码密文(idNoCipher)
     */
    private String idNoCipher;

    /**
     * 是否存在交易账号(hasTxAcct)
     * [说明：type=boolean
     * true：交易账号存在；false：交易账号不存在]
     * 该字段 业务已经弃用 但是消息中心还在使用
     */
    @Deprecated
    private Boolean hasTxAcct;

    /**
     * 是否注销一账通号(closeHbone)
     */
    private Boolean closeHbone;


}