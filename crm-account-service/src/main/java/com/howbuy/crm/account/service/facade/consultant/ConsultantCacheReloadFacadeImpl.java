package com.howbuy.crm.account.service.facade.consultant;

import com.alibaba.fastjson.JSON;
import com.howbuy.crm.account.client.facade.consultant.ConsultantCacheReloadFacade;
import com.howbuy.crm.account.client.request.consultant.ConsultantCacheReloadAllRequest;
import com.howbuy.crm.account.client.request.consultant.ConsultantCacheReloadRequest;
import com.howbuy.crm.account.client.response.Response;
import com.howbuy.crm.account.client.response.consultant.ConsultantCacheReloadResponse;
import com.howbuy.crm.account.service.business.consultant.ConsultantBusinessService;
import lombok.extern.slf4j.Slf4j;
import org.apache.dubbo.config.annotation.DubboService;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;

/**
 * @description: 重新加载投顾缓存服务实现
 * <AUTHOR>
 * @date 2025/3/10
 * @since JDK 1.8
 */
@Slf4j
@Service
@DubboService
public class ConsultantCacheReloadFacadeImpl implements ConsultantCacheReloadFacade {

    @Resource
    private ConsultantBusinessService consultantBusinessService;

    @Override
    public Response<ConsultantCacheReloadResponse> reloadConsultantCache(ConsultantCacheReloadRequest request) {
        log.info("重新加载投顾缓存，入参：{}", JSON.toJSONString(request));
        
        try {
            // 调用Service层重新加载投顾缓存
            consultantBusinessService.reloadCacheByConsCode(request.getConsCode());
            
            ConsultantCacheReloadResponse response = new ConsultantCacheReloadResponse();
            response.setSuccess(true);
            response.setMessage("重新加载投顾缓存成功");
            
            log.info("重新加载投顾缓存成功，出参：{}", JSON.toJSONString(response));
            return Response.ok(response);
        } catch (Exception e) {
            log.error("重新加载投顾缓存异常", e);
            return Response.fail("重新加载投顾缓存失败：" + e.getMessage());
        }
    }

    @Override
    public Response<ConsultantCacheReloadResponse> reloadAllConsultantCache(ConsultantCacheReloadAllRequest request) {
        log.info("重新加载全量投顾缓存，入参：{}", JSON.toJSONString(request));
        
        try {
            // 调用Service层重新加载全量投顾缓存
            consultantBusinessService.reloadCache();
            
            ConsultantCacheReloadResponse response = new ConsultantCacheReloadResponse();
            response.setSuccess(true);
            response.setMessage("重新加载全量投顾缓存成功");
            
            log.info("重新加载全量投顾缓存成功，出参：{}", JSON.toJSONString(response));
            return Response.ok(response);
        } catch (Exception e) {
            log.error("重新加载全量投顾缓存异常", e);
            return Response.fail("重新加载全量投顾缓存失败：" + e.getMessage());
        }
    }
} 