/**
 * Copyright (c) 2024, <PERSON><PERSON><PERSON><PERSON> HOWBUY INVESTMENT MANAGEMENT Co., Ltd.
 * All right reserved.
 * <p>
 * THIS IS UNPUBLISHED PROPRIETARY SOURCE CODE OF HOWBUY INVESTMENT
 * MANAGEMENT CO., LTD.  THE CONTENTS OF THIS FILE MAY NOT BE DISCLOSED
 * TO THIRD PARTIES, COPIED OR DUPLICATED IN ANY FORM, IN WHOLE OR IN PART,
 * WITHOUT THE PRIOR WRITTEN PERMISSION OF HOWBUY INVESTMENT MANAGEMENT
 * CO., LTD.
 */
package com.howbuy.crm.account.service.vo.consultant;

import com.alibaba.excel.annotation.ExcelProperty;
import lombok.Getter;
import lombok.Setter;
import lombok.ToString;

import java.io.Serializable;
import java.math.BigDecimal;

/**
 * @description: 人员绩效系数Excel导出VO
 * <AUTHOR>
 * @date 2025-07-11 18:52:00
 * @since JDK 1.8
 */
@Getter
@Setter
@ToString
public class QueryConsPerformanceCoeffExcelVO implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 分配时间
     */
    @ExcelProperty(value = "分配时间", index = 0)
    private String assignTime;

    /**
     * 投顾客户号
     */
    @ExcelProperty(value = "投顾客户号", index = 1)
    private String consCustNo;

    /**
     * 客户姓名
     */
    @ExcelProperty(value = "客户姓名", index = 2)
    private String custName;

    /**
     * 投顾code
     */
    @ExcelProperty(value = "投顾code", index = 3)
    private String consCode;

    /**
     * 分配投顾（投顾姓名）
     */
    @ExcelProperty(value = "分配投顾", index = 4)
    private String consName;

    /**
     * 中心-当前
     */
    @ExcelProperty(value = "中心-当前", index = 5)
    private String centerOrgCurrent;

    /**
     * 区域-当前
     */
    @ExcelProperty(value = "区域-当前", index = 6)
    private String regionCurrent;

    /**
     * 区副-当前
     */
    @ExcelProperty(value = "区副-当前", index = 7)
    private String regionViceCurrent;

    /**
     * 分公司-当前
     */
    @ExcelProperty(value = "分公司-当前", index = 8)
    private String partOrgCurrent;

    /**
     * 来源类型
     */
    @ExcelProperty(value = "来源类型", index = 9)
    private String sourceType;

    /**
     * 佣金系数起点
     */
    @ExcelProperty(value = "佣金系数起点", index = 10)
    private String commissionCoeffStart;

    /**
     * 客户折算系数
     */
    @ExcelProperty(value = "客户折算系数", index = 11)
    private BigDecimal custConversionCoeff;

    /**
     * 管理系数-分总
     */
    @ExcelProperty(value = "管理系数-分总", index = 12)
    private BigDecimal manageCoeffSubtotal;

    /**
     * 管理系数-区副
     */
    @ExcelProperty(value = "管理系数-区副", index = 13)
    private BigDecimal manageCoeffRegionalsubtotal;

    /**
     * 管理系数-区总
     */
    @ExcelProperty(value = "管理系数-区总", index = 14)
    private BigDecimal manageCoeffRegionaltotal;

    /**
     * 存续B
     */
    @ExcelProperty(value = "存续B", index = 15)
    private String cxb;

    /**
     * 是否计提公募
     */
    @ExcelProperty(value = "是否计提公募", index = 16)
    private String isBigV;

    /**
     * 中心-分配时
     */
    @ExcelProperty(value = "中心-分配时", index = 17)
    private String centerOrgAssignTime;

    /**
     * 区域-分配时
     */
    @ExcelProperty(value = "区域-分配时", index = 18)
    private String regionAssignTime;

    /**
     * 区副-分配时
     */
    @ExcelProperty(value = "区副-分配时", index = 19)
    private String regionViceAssignTime;

    /**
     * 分公司-分配时
     */
    @ExcelProperty(value = "分公司-分配时", index = 20)
    private String partOrgAssignTime;

    /**
     * 修改人
     */
    @ExcelProperty(value = "修改人", index = 21)
    private String updateUser;
} 