/**
 * Copyright (c) 2024, <PERSON><PERSON><PERSON><PERSON> HOWBUY INVESTMENT MANAGEMENT Co., Ltd.
 * All right reserved.
 * <p>
 * THIS IS UNPUBLISHED PROPRIETARY SOURCE CODE OF HOWBUY INVESTMENT
 * MANAGEMENT CO., LTD.  THE CONTENTS OF THIS FILE MAY NOT BE DISCLOSED
 * TO THIRD PARTIES, COPIED OR DUPLICATED IN ANY FORM, IN WHOLE OR IN PART,
 * WITHOUT THE PRIOR WRITTEN PERMISSION OF HOWBUY INVESTMENT MANAGEMENT
 * CO., LTD.
 */
package com.howbuy.crm.account.service.repository.consultant;

import com.howbuy.crm.account.client.enums.ExceptionCodeEnum;
import com.howbuy.crm.account.dao.bo.consperformancecoeff.ConsPerformanceCoeffBO;
import com.howbuy.crm.account.dao.bo.consultant.CmCustSourceCoeffBO;
import com.howbuy.crm.account.dao.mapper.consultant.CmConsPerformanceCoeffMapper;
import com.howbuy.crm.account.dao.mapper.consultant.CmConsultantMapper;
import com.howbuy.crm.account.dao.mapper.consultantexp.CmConsultantExpMapper;
import com.howbuy.crm.account.dao.mapper.custinfo.CmCustconstantMapper;
import com.howbuy.crm.account.dao.mapper.customize.consultant.PrpCustSourceCoeffCustomizeMapper;
import com.howbuy.crm.account.dao.mapper.customize.custinfo.ConscustCustomizeMapper;
import com.howbuy.crm.account.dao.po.consultant.CmConsPerformanceCoeffPO;
import com.howbuy.crm.account.dao.po.consultant.CmConsultantPO;
import com.howbuy.crm.account.service.commom.exception.BusinessException;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import org.springframework.transaction.annotation.Propagation;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.util.Date;
import java.util.List;

/**
 * @description: 写入人员绩效系数表数据访问层
 * <AUTHOR>
 * @date 2025-06-27 17:05:00
 * @since JDK 1.8
 */
@Component
@Transactional(rollbackFor = Exception.class, propagation = Propagation.SUPPORTS)
@Slf4j
public class MergeConsPerformanceCoeffRepository {
    /**
     * 计算类型：修改
     */
    private static final String CALC_TYPE_MODIFY = "2";
    @Resource
    private CmConsPerformanceCoeffMapper cmConsPerformanceCoeffMapper;

    @Autowired
    private PrpCustSourceCoeffCustomizeMapper prpCustSourceCoeffCustomizeMapper;

    @Autowired
    private CmConsultantExpMapper cmConsultantExpMapper;

    @Autowired
    private CmConsultantMapper cmConsultantMapper;
    @Resource
    private CmCustconstantMapper cmCustconstantMapper;
    @Resource
    private ConscustCustomizeMapper conscustCustomizeMapper;

    /**
     * 分页查询初始化数据
     * @param startRow 开始行
     * @param endRow 结束行
     * @return List<ConsPerformanceCoeffBO> 初始化数据列表
     */
    public List<ConsPerformanceCoeffBO> queryInitData(int startRow, int endRow) {
        return conscustCustomizeMapper.queryInitData(startRow, endRow);
    }

    /**
     * 获取初始化数据总数
     * @return int 总数
     */
    public int countInitData() {
        return conscustCustomizeMapper.countInitData();
    }

    /**
     * @param consCode 投顾code
     * @return String outletcode
     * @description: 根据投顾code查询CM_CONSULTANT表中的outletcode字段
     * <AUTHOR>
     * @date 2025-07-01 15:00:00
     * @since JDK 1.8
     */
    public String selectOutletCodeByConsCode(String consCode) {
        try {
            CmConsultantPO consultant = cmConsultantMapper.selectByPrimaryKey(consCode);
            if (consultant == null) {
                log.info("未查询到投顾信息，投顾code：{}", consCode);
                return null;
            }
            String outletcode = consultant.getOutletcode();
            log.info("查询投顾outletcode完成，投顾code：{}，outletcode：{}", consCode, outletcode);
            return outletcode;
        } catch (Exception e) {
            log.error("查询投顾outletcode异常", e);
            throw new BusinessException(ExceptionCodeEnum.DB_ERROR);
        }
    }


    /**
     * @param sourceType 来源类型
     * @return com.howbuy.crm.account.dao.bo.consultant.CmCustSourceCoeffBO
     * @description: 根据来源类型查询客户来源系数配置
     * <AUTHOR>
     * @date 2024-12-19 17:05:00
     * @since JDK 1.8
     */
    public CmCustSourceCoeffBO selectCustSourceCoeffByCustType(String sourceType) {
        try {
            return prpCustSourceCoeffCustomizeMapper.selectCustSourceCoeffBySourceType(sourceType);
        } catch (Exception e) {
            log.error("查询客户来源系数配置异常",e);
            throw new BusinessException(ExceptionCodeEnum.DB_ERROR);
        }
    }

    /**
     * @param orgCode 部门code
     * @return String 区副用户ID
     * @description: 根据orgCode获取区副信息
     * @author: shijie.wang
     * @date: 2025/1/17 14:25
     * @since JDK 1.8
     */
    public String getRegionalSubtotalByOrgCode(String orgCode) {
        try {
            if (StringUtils.isBlank(orgCode)) {
                log.info("orgCode为空，无法查询区副信息");
                return null;
            }
            return cmConsultantExpMapper.getRegionalSubtotalByOrgCode(orgCode);
        } catch (Exception e) {
            log.error("查询区副信息异常", e);
            throw new BusinessException(ExceptionCodeEnum.DB_ERROR);
        }
    }

    /**
     * @param consPerformanceCoeffBO 写入人员绩效系数表BO对象
     * @return void
     * @description: 写入人员绩效系数表
     * <AUTHOR>
     * @date 2025-06-27 17:05:00
     * @since JDK 1.8
     */
    @Transactional(rollbackFor = Exception.class, propagation = Propagation.REQUIRED)
    public void execute(ConsPerformanceCoeffBO consPerformanceCoeffBO) {
        log.info("开始写入人员绩效系数表数据");
        try {
            // 根据计算类型判断是新增还是更新
            if (CALC_TYPE_MODIFY.equals(consPerformanceCoeffBO.getCalcType())) {
                // 修改逻辑，更新数据
                updateConsPerformanceCoeff(consPerformanceCoeffBO);
            } else {
                // 创建客户或分配客户，新增数据
                insertConsPerformanceCoeff(consPerformanceCoeffBO);
            }
            
            log.info("写入人员绩效系数表数据完成");
        } catch (Exception e) {
            log.error("写入人员绩效系数表数据异常", e);
            throw new BusinessException(ExceptionCodeEnum.DB_ERROR);
        }
    }

    /**
     * @param consPerformanceCoeffBO 写入人员绩效系数表BO对象
     * @return void
     * @description: 新增人员绩效系数表数据
     * <AUTHOR>
     * @date 2025-06-27 17:05:00
     * @since JDK 1.8
     */
    private void insertConsPerformanceCoeff(ConsPerformanceCoeffBO consPerformanceCoeffBO) {
        CmConsPerformanceCoeffPO po = new CmConsPerformanceCoeffPO();
        // 设置PO对象属性
        po.setConsCustNo(consPerformanceCoeffBO.getConsCustNo());
        po.setConscode(consPerformanceCoeffBO.getConsCode());
        po.setAssignTime(consPerformanceCoeffBO.getAssignTime());
        po.setSourceType(consPerformanceCoeffBO.getSourceType());
        po.setCommissionCoeffStart(consPerformanceCoeffBO.getCommissionCoeffStart());
        po.setCustConversionCoeff(consPerformanceCoeffBO.getCustConversionCoeff());
        po.setManageCoeffSubtotal(consPerformanceCoeffBO.getManageCoeffSubtotal());
        po.setManageCoeffRegionalsubtotal(consPerformanceCoeffBO.getManageCoeffRegionalsubtotal());
        po.setManageCoeffRegionaltotal(consPerformanceCoeffBO.getManageCoeffRegionaltotal());
        po.setCxb(consPerformanceCoeffBO.getCxb());
        po.setIsBigV(consPerformanceCoeffBO.getIsBigV());
        po.setCenterCode(consPerformanceCoeffBO.getCenterCode());
        po.setRegionCode(consPerformanceCoeffBO.getRegionCode());
        po.setRegionalsubtotal(consPerformanceCoeffBO.getRegionalSubtotal());
        po.setOutletCode(consPerformanceCoeffBO.getOutletCode());
        po.setCreator(consPerformanceCoeffBO.getModifier()); // 创建人使用修改人
        po.setCredt(new Date());
        po.setModdt(new Date());
        CmConsPerformanceCoeffPO existPo = cmConsPerformanceCoeffMapper.selectByPrimaryKey(po.getConsCustNo(), po.getConscode(), po.getAssignTime());
        if(existPo != null){
            log.info("数据已经存在，ConsCustNo:{} ", existPo.getConsCustNo());
            return;
        }
        cmConsPerformanceCoeffMapper.insert(po);
    }

    /**
     * @param consPerformanceCoeffBO 写入人员绩效系数表BO对象
     * @return void
     * @description: 更新人员绩效系数表数据
     * <AUTHOR>
     * @date 2025-06-27 17:05:00
     * @since JDK 1.8
     */
    private void updateConsPerformanceCoeff(ConsPerformanceCoeffBO consPerformanceCoeffBO) {
        CmConsPerformanceCoeffPO po = new CmConsPerformanceCoeffPO();
        // 设置PO对象属性
        po.setConsCustNo(consPerformanceCoeffBO.getConsCustNo());
        po.setConscode(consPerformanceCoeffBO.getConsCode());
        po.setAssignTime(consPerformanceCoeffBO.getAssignTime());
        po.setSourceType(consPerformanceCoeffBO.getSourceType());
        po.setCommissionCoeffStart(consPerformanceCoeffBO.getCommissionCoeffStart());
        po.setCustConversionCoeff(consPerformanceCoeffBO.getCustConversionCoeff());
        po.setManageCoeffSubtotal(consPerformanceCoeffBO.getManageCoeffSubtotal());
        po.setManageCoeffRegionalsubtotal(consPerformanceCoeffBO.getManageCoeffRegionalsubtotal());
        po.setManageCoeffRegionaltotal(consPerformanceCoeffBO.getManageCoeffRegionaltotal());
        po.setCxb(consPerformanceCoeffBO.getCxb());
        po.setIsBigV(consPerformanceCoeffBO.getIsBigV());
        po.setCenterCode(consPerformanceCoeffBO.getCenterCode());
        po.setRegionCode(consPerformanceCoeffBO.getRegionCode());
        po.setRegionalsubtotal(consPerformanceCoeffBO.getRegionalSubtotal());
        po.setOutletCode(consPerformanceCoeffBO.getOutletCode());
        po.setModifier(consPerformanceCoeffBO.getModifier());
        po.setModdt(new Date());
        cmConsPerformanceCoeffMapper.updateByPrimaryKeySelective(po);
    }

    /**
     * 查询客户最新的重复类型分配记录
     * @param consCustNo 客户编号
     * @param sourceTypes 来源类型列表
     * @param assignTime
     * @return 最新的重复类型分配记录
     */
    public CmConsPerformanceCoeffPO getLatestRepeatRecord(String consCustNo, List<String> sourceTypes, Date assignTime) {
        return cmConsPerformanceCoeffMapper.selectLatestRepeatRecord(consCustNo, sourceTypes, assignTime);
    }

    /**
     * 查询最新人员绩效系数表记录
     * @param consCustNo 客户编号
     * @return 最新的分配记录
     */
    public CmConsPerformanceCoeffPO getNewRecord(String consCustNo) {
        return cmConsPerformanceCoeffMapper.selectNewRecord(consCustNo);
    }

    /**
     * 查询指定时间范围内的分配记录
     * @param consCustNo 客户编号
     * @param startTime 开始时间
     * @param endTime 结束时间
     * @return 时间范围内的分配记录列表
     */
    public List<CmConsPerformanceCoeffPO> getRecordsByTimeRange(String consCustNo, Date startTime, Date endTime) {
        return cmConsPerformanceCoeffMapper.selectRecordsByTimeRange(consCustNo, startTime, endTime);
    }

    /**
     * @description: 根据投顾客户号查询分配时间
     * @param custNo 投顾客户号
     * @return Date 分配时间
     * @author: shijie.wang
     * @date: 2025-07-09 18:05:00
     * @since JDK 1.8
     */
    public Date selectBindDateByCustNo(String custNo) {
        return cmCustconstantMapper.selectBindDateByCustNo(custNo);
    }

    /**
     * @description:(查询客户历史所属投顾数量，有数据为分配投顾、没有为创建客户)
     * @param conscustno
     * @param consCode
     * @return java.lang.String
     * @author: shijie.wang
     * @date: 2025/7/22 15:13
     * @since JDK 1.8
     */
    public String queryHisCountByConscustno(String conscustno, String consCode) {
        return conscustCustomizeMapper.queryHisCountByConscustno(conscustno, consCode);
    }

} 