/**
 * Copyright (c) 2023, <PERSON><PERSON><PERSON><PERSON> HOWBUY INVESTMENT MANAGEMENT Co., Ltd.
 * All right reserved.
 * <p>
 * THIS IS UNPUBLISHED PROPRIETARY SOURCE CODE OF HOWBUY INVESTMENT
 * MANAGEMENT CO., LTD.  THE CONTENTS OF THIS FILE MAY NOT BE DISCLOSED
 * TO THIRD PARTIES, COPIED OR DUPLICATED IN ANY FORM, IN WHOLE OR IN PART,
 * WITHOUT THE PRIOR WRITTEN PERMISSION OF HOWBUY INVESTMENT MANAGEMENT
 * CO., LTD.
 */
package com.howbuy.crm.account.service.service.custinfo;

import com.github.pagehelper.Page;
import com.github.pagehelper.util.StringUtil;
import com.google.common.collect.Lists;
import com.howbuy.crm.account.client.enums.DealStatusEnum;
import com.howbuy.crm.account.client.enums.custinfo.*;
import com.howbuy.crm.account.client.request.custinfo.*;
import com.howbuy.crm.account.client.response.PageVO;
import com.howbuy.crm.account.client.response.Response;
import com.howbuy.crm.account.client.response.custinfo.AbnormalCustInfoVO;
import com.howbuy.crm.account.client.response.custinfo.AbnormalRelatedCustVO;
import com.howbuy.crm.account.client.response.custinfo.AbnormalRelatedDetailVO;
import com.howbuy.crm.account.client.response.custinfo.HkAcctCustDetailInfoVO;
import com.howbuy.crm.account.client.utils.IdTypeUtil;
import com.howbuy.crm.account.dao.po.custinfo.*;
import com.howbuy.crm.account.dao.req.custinfo.AbnormalCustReqVO;
import com.howbuy.crm.account.dao.req.custinfo.CmHkCustReqVO;
import com.howbuy.crm.account.service.business.message.AccountCenterMessageBuss;
import com.howbuy.crm.account.service.commom.utils.AbnormalCustUtils;
import com.howbuy.crm.account.service.repository.custinfo.CmHkCustInfoRepository;
import com.howbuy.crm.account.service.repository.custinfo.ConsCustInfoRepository;
import com.howbuy.crm.account.service.repository.custinfo.HkAbnormalCustRepository;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.stream.Collectors;

/**
 * @description: (香港 客户  处理异常信息 信息service)
 * <AUTHOR>
 * @date 2023/12/13 16:41
 * @since JDK 1.8
 */
@Service
@Slf4j
public class HkAbnormalCustInfoService  extends  AbstractAbnormalCustInfoService{

    @Autowired
    private HkAbnormalCustRepository hkAbnormalCustRepository;
    @Autowired
    private ConsCustInfoRepository consCustInfoRepository;

    @Autowired
    private CmHkCustInfoRepository hkCustInfoRepository;

    @Autowired
    private HkCustInfoService hkCustInfoService;

    /**
     * 异常处理菜单code
     */
    private static final String HK_ABNORMAL_MENU_CODE = "120426";

    @Autowired
    private AccountCenterMessageBuss accountCenterMessageBuss;



    @Override
    public Response<String> updateDealStatus(DealAbnormalRequest request){
       DealStatusEnum dealStatusEnum=DealStatusEnum.getEnum(request.getDealStatus());
       return hkAbnormalCustRepository.updateDealStatus(
               request.getId(),
               dealStatusEnum,
               request.getOperator(),
               request.getRemark());
    }


    @Override
    public Response<String> batchUpdateDealStatus(BatchDealAbnormalRequest batchRequest){
        return hkAbnormalCustRepository.batchUpdateDealStatus(
                batchRequest.getIdList(),
                DealStatusEnum.getEnum(batchRequest.getDealStatus()),
                batchRequest.getOperator(),
                batchRequest.getRemark());
    }

    /**
     * 处理异常 操作校验
     * @param request
     * @return
     */
    @Override
    public  Response<String> validateSpecificDeal(DealAbnormalRequest request){
        DealStatusEnum dealStatusEnum=DealStatusEnum.getEnum(request.getDealStatus());
        CmHkAbnormalCustInfoPO custInfoPo=hkAbnormalCustRepository.selectById(request.getId());
        if(custInfoPo==null){
            return Response.fail("待处理异常客户信息不存在!");
        }
        //必须为： 未处理 才允许处理
        DealStatusEnum currentDealStatusEnum=DealStatusEnum.getEnum(custInfoPo.getDealStatus());
        if(DealStatusEnum.UN_DEAL!=currentDealStatusEnum){
            return Response.fail(String.format("id为[%s]异常客户信息已处理，处理状态为[%s]!",
                    custInfoPo.getId(),
                    DealStatusEnum.getDescription(custInfoPo.getDealStatus())));
        }

        if(DealStatusEnum.DEAL==dealStatusEnum){
            //已处理校验逻辑：【香港客户号】尚未绑定【投顾客户号】，不可置为：已处理
            if(StringUtils.isNotEmpty(custInfoPo.getHkTxAcctNo())){
                //是否绑定投顾客户号
                CmHkConscustPO hkConscustPo= hkCustInfoRepository.selectByHkTxAcctNo(custInfoPo.getHkTxAcctNo());
                if(hkConscustPo==null){
                    return Response.fail(String.format("香港客户号[%s]未绑定投顾客户号，不可置为已处理!",custInfoPo.getHkTxAcctNo()));
                }
            }
        }
        return Response.ok();
    }


    /**
     * @description:(查询明细列表)
     * @param detailList
     * @return java.util.List<com.howbuy.crm.account.client.response.custinfo.AbnormalRelatedDetailVO>
     * @author: haoran.zhang
     * @date: 2024/1/22 11:03
     * @since JDK 1.8
     */
    public List<AbnormalRelatedDetailVO> queryHkAbnormalDetailList(List<String> detailList){
        if(CollectionUtils.isEmpty(detailList)){
            return Lists.newArrayList();
        }
        List<CmHkAbnormalRelatedCustPO> relatedCustList=hkAbnormalCustRepository.selectRelatedListByDetailList(Lists.partition(detailList,1000));
        return relatedCustList.stream().map(this::constructDetailVo).collect(Collectors.toList());
    }


    /**
     * @description:(分页查询 香港异常客户信息)
     * @param custInfoRequest
     * @return com.howbuy.crm.account.client.response.PageVO<com.howbuy.crm.account.client.response.custinfo.HkAbnormalCustInfoVO>
     * @author: haoran.zhang
     * @date: 2024/1/17 13:05
     * @since JDK 1.8
     */
    public PageVO<AbnormalCustInfoVO> queryHkAbnormalPage(AbnormalCustInfoRequest custInfoRequest){
        //request --> vo 转换
        AbnormalCustReqVO reqVo=buidReqVo(custInfoRequest);

        Page<CmHkAbnormalCustInfoPO>  abnormalPage=hkAbnormalCustRepository.selectPageByVo(reqVo);
        List<CmHkAbnormalCustInfoPO>  dbList=abnormalPage.getResult();

        List<String> abnormalIdList=dbList.stream().map(CmHkAbnormalCustInfoPO::getId).collect(Collectors.toList());
        //查询关联的客户信息
        List<CmHkAbnormalRelatedCustPO> relatedCustList=hkAbnormalCustRepository.selectRelatedListByMainIdList(Lists.partition(abnormalIdList,1000));
        //按照 异常id 分组
        Map<String,List<CmHkAbnormalRelatedCustPO>> relatedCustMap=relatedCustList.stream().collect(Collectors.groupingBy(CmHkAbnormalRelatedCustPO::getAbnormalId));

        //组装对象
        List<AbnormalCustInfoVO> voList=Lists.newArrayList();
       for(CmHkAbnormalCustInfoPO  dbPo :dbList){
           //主表 信息
           AbnormalCustInfoVO custInfoVo= constructDetailWithAcctVo(dbPo);
           //关联客户信息
           if(relatedCustMap.containsKey(dbPo.getId())){
               List<CmHkAbnormalRelatedCustPO> relatedList=relatedCustMap.get(dbPo.getId());
               //构建 明细信息
               custInfoVo.setRelatedList(relatedList.stream().map(this::constructDetailWithAcctVo).collect(Collectors.toList()));
           }
           voList.add(custInfoVo);
       }

        //返回页面对象
        PageVO<AbnormalCustInfoVO> pageVo=new PageVO<>();
        pageVo.setTotal(abnormalPage.getTotal());
        pageVo.setPage(abnormalPage.getPageNum());
        pageVo.setSize(abnormalPage.getPageSize());
        pageVo.setRows(voList);
         return pageVo;
    }


    /**
     * 明细信息 附带  香港、一账通侧信息
     * @param dbPo
     * @return
     */
    private AbnormalRelatedCustVO constructDetailWithAcctVo(CmHkAbnormalRelatedCustPO dbPo){

        AbnormalRelatedDetailVO detailVO=constructDetailVo( dbPo);
        AbnormalRelatedCustVO custVo=new AbnormalRelatedCustVO();
        BeanUtils.copyProperties(detailVO,custVo);

        //一账通侧 信息
        custVo.setHboneSideInfo(getHboneSideInfo(dbPo.getHboneNo()));
        //香港侧信息
        custVo.setHkSideInfo(getHkSideInfo(dbPo.getHkTxAcctNo()));

        String custNo=dbPo.getCustNo();

        //客户创建时间
        if(StringUtil.isNotEmpty(custNo)){
            CmConscustPO custPo=consCustInfoRepository.selectPoByCustNo(custNo);
            if(custPo!=null){
                //客户创建日期
                custVo.setCreateDt(custPo.getRegdt());
            }
        }
        return custVo;
    }


    /**
     * 异常明细对象转换
     * @param dbPo
     * @return
     */
    private AbnormalRelatedDetailVO constructDetailVo(CmHkAbnormalRelatedCustPO dbPo){
        AbnormalRelatedDetailVO detailVo=new AbnormalRelatedDetailVO();

        String custNo=dbPo.getCustNo();
        detailVo.setId(dbPo.getId());
        detailVo.setAbnormalId(dbPo.getAbnormalId());
        detailVo.setCustNo(custNo);
        detailVo.setConsCode(dbPo.getConsCode());
        detailVo.setHkTxAcctNo(dbPo.getHkTxAcctNo());
        detailVo.setCustName(dbPo.getCustName());
        detailVo.setInvestType(dbPo.getInvestType());
        detailVo.setMobileAreaCode(dbPo.getMobileAreaCode());
        detailVo.setMobileDigest(dbPo.getMobileDigest());
        detailVo.setMobileMask(dbPo.getMobileMask());
        detailVo.setMobileCipher(dbPo.getMobileCipher());
        detailVo.setIdSignAreaCode(dbPo.getIdSignAreaCode());
        detailVo.setIdType(dbPo.getIdType());
        detailVo.setIdNoDigest(dbPo.getIdNoDigest());
        detailVo.setIdNoMask(dbPo.getIdNoMask());
        detailVo.setIdNoCipher(dbPo.getIdNoCipher());
        detailVo.setHboneNo(dbPo.getHboneNo());
        detailVo.setCreator(dbPo.getCreator());
        detailVo.setCreateTimestamp(dbPo.getCreateTimestamp());
        detailVo.setModifier(dbPo.getModifier());
        detailVo.setModifyTimestamp(dbPo.getModifyTimestamp());

        //证件类型 翻译描述
        detailVo.setIdTypeDesc(IdTypeUtil.getIdTypeDesc(dbPo.getInvestType(),dbPo.getIdType()));

        return detailVo;
    }


    /**
     * 异常对象转换
     * @param dbPo
     * @return
     */
    private AbnormalCustInfoVO constructDetailWithAcctVo(CmHkAbnormalCustInfoPO dbPo) {
        AbnormalCustInfoVO custInfoVo = new AbnormalCustInfoVO();
        custInfoVo.setId(dbPo.getId());
        custInfoVo.setMessageClientId(dbPo.getMessageClientId());
        custInfoVo.setHkTxAcctNo(dbPo.getHkTxAcctNo());
        custInfoVo.setCustName(dbPo.getCustName());
        custInfoVo.setInvestType(dbPo.getInvestType());
        custInfoVo.setMobileAreaCode(dbPo.getMobileAreaCode());
        custInfoVo.setMobileDigest(dbPo.getMobileDigest());
        custInfoVo.setMobileMask(dbPo.getMobileMask());
        custInfoVo.setMobileCipher(dbPo.getMobileCipher());
        custInfoVo.setIdSignAreaCode(dbPo.getIdSignAreaCode());
        custInfoVo.setIdType(dbPo.getIdType());
        custInfoVo.setIdNoDigest(dbPo.getIdNoDigest());
        custInfoVo.setIdNoMask(dbPo.getIdNoMask());
        custInfoVo.setIdNoCipher(dbPo.getIdNoCipher());
        custInfoVo.setHboneNo(dbPo.getHboneNo());
        custInfoVo.setAbnormalSource(dbPo.getAbnormalSource());
        custInfoVo.setAbnormalSceneType(dbPo.getAbnormalSceneType());
        custInfoVo.setCreator(dbPo.getCreator());
        custInfoVo.setCreateTimestamp(dbPo.getCreateTimestamp());
        custInfoVo.setModifier(dbPo.getModifier());
        custInfoVo.setModifyTimestamp(dbPo.getModifyTimestamp());
        custInfoVo.setRecStat(dbPo.getRecStat());
        custInfoVo.setDealStatus(dbPo.getDealStatus());
        custInfoVo.setDealOperator(dbPo.getDealOperator());
        custInfoVo.setDealRemark(dbPo.getDealRemark());
        custInfoVo.setDealTimestamp(dbPo.getDealTimestamp());
        //证件类型 翻译描述
        custInfoVo.setIdTypeDesc(IdTypeUtil.getIdTypeDesc(dbPo.getInvestType(), dbPo.getIdType()));
        //异常描述
        custInfoVo.setAbnormalSceneDesc(AbnormalCustUtils.translateAbnormalDesc(dbPo.getAbnormalSource(), dbPo.getAbnormalSceneType()));
        custInfoVo.setMessageId(dbPo.getMessageId());

        // 需求confluence链接：http://dms.intelnal.howbuy.com/pages/viewpage.action?pageId=94532024#id-39%E3%80%81%E9%A6%99%E6%B8%AF%E8%B4%A6%E6%88%B7%E6%B6%88%E6%81%AF%E5%BC%82%E5%B8%B8%E5%A4%84%E7%90%86%E6%94%B9%E9%80%A0CRM-2.2%E3%80%81%E3%80%90%E4%BC%98%E5%8C%96%E3%80%91%E9%A6%99%E6%B8%AFCRM%E5%BC%80%E6%88%B7%E5%A4%84%E7%90%86%E9%80%BB%E8%BE%91@CRM
        // 枚举值 异常场景描述：线上、线下、其他
        String abnormalSource = dbPo.getAbnormalSource();
        String sceneCode = HkAbnormalSceneEnum.getSceneCodeBySourceCode(abnormalSource);
        String sceneDesc = HkAbnormalSceneEnum.getDescription(sceneCode);
        custInfoVo.setHkAbnormalSceneDesc(sceneDesc);
        // 枚举值 香港业务节点描述：注册、开户、其他
        String nodeCode = HkBusinessNodeEnum.getNodeCodeBySourceCode(abnormalSource);
        String nodeDesc = HkBusinessNodeEnum.getDescription(nodeCode);
        custInfoVo.setHkBusinessNodeDesc(nodeDesc);

        // 异常等级
        custInfoVo.setAbnormalLevelDesc(HkAbnormalLevelEnum.getDescription(dbPo.getAbnormalLevel()));

        //香港客户信息
        if (StringUtils.isNotEmpty(dbPo.getHkTxAcctNo())) {
            //补充香港信息
            custInfoVo.setHkSideInfo(getHkSideInfo(dbPo.getHkTxAcctNo()));
            //crm关联的客户号
            CmHkCustReqVO reqVO = new CmHkCustReqVO();
            reqVO.setHkTxAcctNo(dbPo.getHkTxAcctNo());
            CmHkConscustPO custPo = hkCustInfoRepository.selectByReqVO(reqVO);
            if (custPo != null) {
                custInfoVo.setRelatedCustNo(custPo.getConscustno());
                CmCustconstantPO cmCustconstantPO =
                        consCustInfoRepository.selectCustConsRelationByCustNo(custPo.getConscustno());
                custInfoVo.setRelatedConsCode(Objects.nonNull(cmCustconstantPO) ?
                        cmCustconstantPO.getConscode() : null);
            }
        }
        return custInfoVo;
    }


    /**
     * @description: 香港异常客户 - 解绑关联客户号
     * @param custInfoRequest
     * @return com.howbuy.crm.account.client.response.Response<java.lang.String>
     * @author: jin.wang03
     * @date: 2025/5/23 17:52
     * @since JDK 1.8
     */
    public Response<String> unbindRelatedCustNo(HkUnbindRelatedCustNoRequest custInfoRequest) {
        CmHkAbnormalCustInfoPO cmHkAbnormalCustInfoPO
                = hkAbnormalCustRepository.selectById(custInfoRequest.getId());
        if (Objects.isNull(cmHkAbnormalCustInfoPO)) {
            return Response.fail("香港异常客户id不存在！");
        }

        CmHkCustReqVO reqVO = new CmHkCustReqVO();
        reqVO.setHkTxAcctNo(cmHkAbnormalCustInfoPO.getHkTxAcctNo());
        CmHkConscustPO custPo = hkCustInfoRepository.selectByReqVO(reqVO);
        if (Objects.isNull(custPo)) {
            return Response.fail("未找到关联的投顾客户，无需解绑！");
        }

        HkAcctRelationOptRequest unbindRequest = new HkAcctRelationOptRequest();
        unbindRequest.setCustNo(custPo.getConscustno());
        unbindRequest.setHkTxAcctNo(cmHkAbnormalCustInfoPO.getHkTxAcctNo());
        unbindRequest.setOperator(custInfoRequest.getOperator());
        unbindRequest.setOperateChannel(CustOperateChannelEnum.MENU.getCode());
        unbindRequest.setOperateSource(HK_ABNORMAL_MENU_CODE);

        return hkCustInfoService.unBindHkTxAcct(unbindRequest);
    }

    /**
     * @description: 创建投顾客户(重新跑 消费消息的流程)
     * @param custInfoRequest
     * @return com.howbuy.crm.account.client.response.Response<java.lang.String>
     * @author: jin.wang03
     * @date: 2025/5/26 10:50
     * @since JDK 1.8
     */
    public Response<String> createCustInfoByHk(HkAbnormalCreateConsCustRequest custInfoRequest) {
        CmHkAbnormalCustInfoPO cmHkAbnormalCustInfoPO = hkAbnormalCustRepository.selectById(custInfoRequest.getId());
        if (Objects.isNull(cmHkAbnormalCustInfoPO)) {
            return Response.fail("异常客户id不存在！");
        }
        if (StringUtils.isBlank(cmHkAbnormalCustInfoPO.getMessageId())) {
            return Response.fail("不支持新增客户！（该异常客户不是由账户中心消息产生的）");
        }
        return accountCenterMessageBuss.consumeAgain(cmHkAbnormalCustInfoPO.getMessageId());
    }

    /**
     * @description:(请在此添加描述)
     * @param custInfoRequest
     * @return com.howbuy.crm.account.client.response.Response<java.lang.String>
     * @author: jin.wang03
     * @date: 2025/5/26 14:10
     * @since JDK 1.8
     */
    public Response<String> subTablebBindHkCustNo(HkAbnormalSubTableBindHkCustNoRequest custInfoRequest) {
        CmHkAbnormalRelatedCustPO cmHkAbnormalRelatedCustPO = hkAbnormalCustRepository.selectSubTableBySubId(custInfoRequest.getId());
        if (Objects.isNull(cmHkAbnormalRelatedCustPO)) {
            return Response.fail("关联客户id不存在！");
        }
        CmHkAbnormalCustInfoPO cmHkAbnormalCustInfoPO = hkAbnormalCustRepository.selectById(cmHkAbnormalRelatedCustPO.getAbnormalId());
        if (Objects.isNull(cmHkAbnormalCustInfoPO)) {
            return Response.fail("异常客户id不存在！");
        }

        CmHkCustReqVO reqVOByHkCustNo = new CmHkCustReqVO();
        reqVOByHkCustNo.setHkTxAcctNo(cmHkAbnormalCustInfoPO.getHkTxAcctNo());
        CmHkConscustPO custPo = hkCustInfoRepository.selectByReqVO(reqVOByHkCustNo);
        if (Objects.nonNull(custPo)) {
            if (StringUtils.equals(cmHkAbnormalRelatedCustPO.getCustNo(), custPo.getConscustno())) {
                return Response.ok("投顾客户号：" + cmHkAbnormalRelatedCustPO.getCustNo() + "和香港客户号：" + cmHkAbnormalCustInfoPO.getHkTxAcctNo() + "已关联！");
            } else {
                return Response.fail("当前香港客户号已绑定投顾客户号，请先解绑");
            }
        }

        CmHkCustReqVO reqVOByCustNo = new CmHkCustReqVO();
        reqVOByCustNo.setConscustno(cmHkAbnormalRelatedCustPO.getCustNo());
        CmHkConscustPO custPoByCustNo = hkCustInfoRepository.selectByReqVO(reqVOByCustNo);
        if (Objects.nonNull(custPoByCustNo)) {
            if (StringUtils.equals(cmHkAbnormalCustInfoPO.getHkTxAcctNo(), custPoByCustNo.getHkTxAcctNo())) {
                return Response.ok("投顾客户号：" + cmHkAbnormalRelatedCustPO.getCustNo() + "和香港客户号：" + cmHkAbnormalCustInfoPO.getHkTxAcctNo() + "已关联！");
            } else {
                return Response.fail("当前投顾客户号已绑定其他香港客户号，请先解绑");
            }
        }

        HkAcctRelationOptRequest hkAcctRelationOptRequest = new HkAcctRelationOptRequest();
        hkAcctRelationOptRequest.setHkTxAcctNo(cmHkAbnormalCustInfoPO.getHkTxAcctNo());
        hkAcctRelationOptRequest.setCustNo(cmHkAbnormalRelatedCustPO.getCustNo());
        hkAcctRelationOptRequest.setOperator(custInfoRequest.getOperator());
        hkAcctRelationOptRequest.setOperateChannel(CustOperateChannelEnum.MENU.getCode());
        hkAcctRelationOptRequest.setOperateSource(HK_ABNORMAL_MENU_CODE);

        return hkCustInfoService.bindHkTxAcct(hkAcctRelationOptRequest);
    }

    /**
     * @description: 香港异常客户页- 使用香港开户信息更新投顾客户 前置校验
     * @param custInfoRequest
     * @return com.howbuy.crm.account.client.response.Response<java.lang.String>
     * @author: jin.wang03
     * @date: 2025/5/26 14:55
     * @since JDK 1.8
     */
    public Response<String> validatebBeforeUpdateCustInfoByHk(HkAbnormalUpdateConsCustRequest custInfoRequest) {
        CmHkAbnormalRelatedCustPO cmHkAbnormalRelatedCustPO = hkAbnormalCustRepository.selectSubTableBySubId(custInfoRequest.getId());
        if (Objects.isNull(cmHkAbnormalRelatedCustPO)) {
            return Response.fail("关联客户id不存在！");
        }
        CmHkAbnormalCustInfoPO cmHkAbnormalCustInfoPO = hkAbnormalCustRepository.selectById(cmHkAbnormalRelatedCustPO.getAbnormalId());
        if (Objects.isNull(cmHkAbnormalCustInfoPO)) {
            return Response.fail("异常客户id不存在！");
        }

        HkAcctCustDetailInfoVO hkAcctCustDetailInfoVO = hkCustInfoService.queryHkCustInfo(cmHkAbnormalCustInfoPO.getHkTxAcctNo());
        // 香港客户状态=开户申请成功/正常 且 香港账户中心的客户姓名/证件号均不为空，则弹窗提示“是否使用香港开户信息更新CRM投顾客户信息？”
        if (Objects.nonNull(hkAcctCustDetailInfoVO)
                && (
                HkAcctCustStatusEnum.NORMAL.getCode().equals(hkAcctCustDetailInfoVO.getHkCustStatus())
                        || HkAcctCustStatusEnum.OPEN_ACCOUNT_APPLY_SUCCESS.getCode().equals(hkAcctCustDetailInfoVO.getHkCustStatus()))
                && StringUtils.isNotBlank(hkAcctCustDetailInfoVO.getUsedCustName())
                && StringUtils.isNotBlank(hkAcctCustDetailInfoVO.getIdNoDigest())) {
            return Response.ok();
        }

        return Response.fail("香港客户状态不是[开户申请成功/正常]，或香港客户信息客户姓名/证件号不全");
    }


    /**
     * @description: 香港异常客户页 使用香港开户信息更新[子表上 投顾客户]
     * @param custInfoRequest
     * @return com.howbuy.crm.account.client.response.Response<java.lang.String>
     * @author: jin.wang03
     * @date: 2025/5/26 15:01
     * @since JDK 1.8
     */
    public Response<String> updateCustInfoByHk(HkAbnormalUpdateConsCustRequest custInfoRequest) {
        Response<String> validateResponse = validatebBeforeUpdateCustInfoByHk(custInfoRequest);
        if (!validateResponse.isSuccess()) {
            return Response.fail(validateResponse.getDescription());
        }

        CmHkAbnormalRelatedCustPO cmHkAbnormalRelatedCustPO = hkAbnormalCustRepository.selectSubTableBySubId(custInfoRequest.getId());
        if (Objects.isNull(cmHkAbnormalRelatedCustPO)) {
            return Response.fail("关联客户id不存在！");
        }
        CmHkAbnormalCustInfoPO cmHkAbnormalCustInfoPO = hkAbnormalCustRepository.selectById(cmHkAbnormalRelatedCustPO.getAbnormalId());
        if (Objects.isNull(cmHkAbnormalCustInfoPO)) {
            return Response.fail("异常客户id不存在！");
        }

        UpdateCustInfoByHkRequest updateCustInfoByHkRequest = new UpdateCustInfoByHkRequest();
        updateCustInfoByHkRequest.setHkCustNo(cmHkAbnormalCustInfoPO.getHkTxAcctNo());
        updateCustInfoByHkRequest.setCustNo(cmHkAbnormalRelatedCustPO.getCustNo());
        updateCustInfoByHkRequest.setOperator(custInfoRequest.getOperator());
        return hkCustInfoService.updateCustInfoByHk(updateCustInfoByHkRequest);
    }

}