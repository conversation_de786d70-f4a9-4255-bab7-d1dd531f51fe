/**
 * Copyright (c) 2023, <PERSON><PERSON><PERSON><PERSON> HOWBUY INVESTMENT MANAGEMENT Co., Ltd.
 * All right reserved.
 * <p>
 * THIS IS UNPUBLISHED PROPRIETARY SOURCE CODE OF HOWBUY INVESTMENT
 * MANAGEMENT CO., LTD.  THE CONTENTS OF THIS FILE MAY NOT BE DISCLOSED
 * TO THIRD PARTIES, COPIED OR DUPLICATED IN ANY FORM, IN WHOLE OR IN PART,
 * WITHOUT THE PRIOR WRITTEN PERMISSION OF HOWBUY INVESTMENT MANAGEMENT
 * CO., LTD.
 */
package com.howbuy.crm.account.service.message.hbonecustinfo;

import com.alibaba.fastjson.JSON;
import com.howbuy.common.utils.StringUtil;
import com.howbuy.crm.account.client.enums.YesOrNoEnum;
import com.howbuy.crm.account.client.enums.custinfo.ConsCustInvestTypeEnum;
import com.howbuy.crm.account.client.enums.custinfo.CustSourceCodeEnum;
import com.howbuy.crm.account.client.enums.custinfo.FullCustSourceEnum;
import com.howbuy.crm.account.client.enums.custinfo.SpecialConsCodeEnum;
import com.howbuy.crm.account.client.request.custinfo.CreateConsCustRequest;
import com.howbuy.crm.account.client.response.Response;
import com.howbuy.crm.account.client.response.custinfo.CreateConsCustRespVO;
import com.howbuy.crm.account.dao.bo.custinfo.CmConscustForAnalyseBO;
import com.howbuy.crm.account.service.business.custinfo.CmCustBusinessService;
import com.howbuy.crm.account.service.business.custinfo.CmCustProcessBusinessService;
import com.howbuy.crm.account.service.commom.constant.Constants;
import com.howbuy.crm.account.service.message.dto.HboneRegisterMessageDTO;
import com.howbuy.crm.account.service.outerservice.auth.EncyptAndDecyptOuterService;
import com.howbuy.crm.account.service.vo.custinfo.AbnormalAnalyseResultVO;
import com.howbuy.crm.account.service.vo.custinfo.HboneMessageCustInfoVO;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;

import java.util.Arrays;
import java.util.List;

/**
 * @description: ([一账通账户]-[一账通注册] 消息处理器[topic.hbone.openAcct])
 * <AUTHOR>
 * @date 2023/12/11 17:04
 * @since JDK 1.8
 */
@Slf4j
@Service
public class HboneRegisterMessageProcessor extends AbstractHboneMessageProcessor<HboneRegisterMessageDTO> {


    /**
     * [TOPIC_OPEN_ACC]  一账通开户
     */
    @Value("${topic.hbone.openAcct}")
    private String topicHboneOpenAcct;


    /**
     * 非实名开通一账通  一账通注册
     * [ORDINARY_REGISTER]
     */
    @Value("${tag.hbone.ordinaryRegister}")
    private String tagName;


    //消息体 示例：

//        {
//            "clientId": "fe369569cfb34e94b204e1f791f8bc12",
//                "content": {
//            "body ": {
//                        "custName": null,
//                        "disCode": "HB000A001",
//                        "hboneNo": "9200727765",
//                        "ip": null,
//                        "mobileCipher": "74Gh9a_LONJdr9CsI4cqCA==01",
//                        "mobileDigest": "7657243fa0466ecdc9337487a88de766",
//                        "mobileMask": "1566997****",
//                        "mobileVerifyStatus": "0",
//                        "regDate": "20240103",
//                        "regOutletCode": "CO2305W01",
//                        "username": null
//            },
//            "head ": {
//                "createTime ": "20240103145911 ",
//                        "eventCode ": "520180 ",
//                        "hboneNo ": "9200727765 ",
//                        "msgKey ": "56 b9e85b7ffce1329377caf56208837e ",
//                        "tag ": "ORDINARY_REGISTER "
//            }
//        },
//            "messageChannel": "TOPIC_OPEN_ACC"
//        }


    @Autowired
    private CmCustProcessBusinessService processBusinessService;


    @Autowired
    private CmCustBusinessService cmBusinessService;

    @Autowired
    private EncyptAndDecyptOuterService encyptAndDecyptOuterService;

    @Override
    public String getQuartMessageChannel() {
        return topicHboneOpenAcct;
    }

    @Override
    public List<String> getSupportTagList() {
        return Arrays.asList(tagName);
    }


    @Override
    HboneMessageCustInfoVO constructByMessage(HboneRegisterMessageDTO  acctMsgDto) {
        HboneMessageCustInfoVO custVo=new HboneMessageCustInfoVO();
        custVo.setHboneNo(acctMsgDto.getHboneNo());
        custVo.setCustName(acctMsgDto.getCustName());
        // 一账通开户消息 默认数据赋值： 客户类别=个人客户
        custVo.setInvestType(ConsCustInvestTypeEnum.PERSONAL.getCode());
        // 一账通开户消息 默认数据赋值： 手机地区码=86
        custVo.setMobileAreaCode(Constants.DEFAULT_MOBILE_AREA_CODE);
        custVo.setMobileDigest(acctMsgDto.getMobileDigest());
        custVo.setMobileMask(acctMsgDto.getMobileMask());
        custVo.setMobileCipher(acctMsgDto.getMobileCipher());
        custVo.setRegOutletCode(acctMsgDto.getRegOutletCode());
        custVo.setRegDate(acctMsgDto.getRegDate());
        custVo.setDisCode(acctMsgDto.getDisCode());
        custVo.setMobileVerifyStatus(acctMsgDto.getMobileVerifyStatus());
        //Constants.DEFAULT_ID_SIGN_AREA_CODE
        custVo.setIdSignAreaCode(null);
        custVo.setIdType(null);
        custVo.setIdNoDigest(null);
        custVo.setIdNoMask(null);
        custVo.setIdNoCipher(null);
        return custVo;
    }

    @Override
    public FullCustSourceEnum getCustSource() {
        return FullCustSourceEnum.HBONE_REGISTER;
    }

    @Override
    Response<String> processHboneMessage(AbnormalAnalyseResultVO<HboneMessageCustInfoVO> resultVo) {
        CmConscustForAnalyseBO processedCustInfo= resultVo.getProcessedCustInfo();
        if(processedCustInfo==null){
            createYxsConscust(resultVo.getAnalyseVo());
            return Response.ok();
        }

        HboneMessageCustInfoVO analyseVo=resultVo.getAnalyseVo();
        //关联 一账通号 vs 投顾客户号
        return processBusinessService.associateHboneAcctRelation(processedCustInfo,analyseVo);
        //TODO: SYNC [saveSyncAcCust] 未做迁移
    }

    /**
     * 根据消息推送body对象，特殊处理客户信息
     * Eg: 研习社渠道号：RH1911W01 -->  新增客户
     * @param info
     */
    public void createYxsConscust(HboneMessageCustInfoVO info){
        //regOutletCode：【RH1911W01】  mobileVerifyStatus='1'
        if(info==null || StringUtil.isEmpty(info.getMobileDigest())){
            return;
        }
        //是否特殊处理逻辑：  研习社渠道用户 &&  手机号已验证
        boolean isDealYxs= CustSourceCodeEnum.YAN_XI_SHE.getCode().equals(info.getRegOutletCode())
                && YesOrNoEnum.YES.getCode().equals(info.getMobileVerifyStatus());
        if(!isDealYxs){
            return;
        }

        CreateConsCustRequest createCustReq=new CreateConsCustRequest();
        //新增客户： 姓名：先生/女士
        createCustReq.setCustname(StringUtil.isEmpty(info.getCustName())?Constants.DEFAULT_CUST_NAME:info.getCustName());
        createCustReq.setInvsttype(info.getInvestType());
        createCustReq.setMobile(encyptAndDecyptOuterService.decrypt(info.getMobileCipher()));
        createCustReq.setAcregdt(info.getRegDate());
        createCustReq.setCustsource(CustSourceCodeEnum.YAN_XI_SHE.getCode());
//        投顾：投顾编码：ZSJJN_CSLS 掌上基金-未维护
        createCustReq.setConsCode(SpecialConsCodeEnum.ZSJJN_CSLS.getCode());
        createCustReq.setOperator(Constants.MQ_YXS_SYS_OPERATOR);
        createCustReq.setOperateSource(info.getAbnormalSource());
        createCustReq.setOperateChannel(info.getOperateChannel());
        Response<CreateConsCustRespVO> createResponse=cmBusinessService.insertCust(createCustReq);
        logger.info("[研习社渠道]新增投顾客户，返回结果：{}", JSON.toJSONString(createResponse));
    }

}