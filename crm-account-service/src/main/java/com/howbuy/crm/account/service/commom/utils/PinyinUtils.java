/**
 * Copyright (c) 2024, <PERSON><PERSON><PERSON><PERSON> HOWBUY INVESTMENT MANAGEMENT Co., Ltd.
 * All right reserved.
 * <p>
 * THIS IS UNPUBLISHED PROPRIETARY SOURCE CODE OF HOWBUY INVESTMENT
 * MANAGEMENT CO., LTD.  THE CONTENTS OF THIS FILE MAY NOT BE DISCLOSED
 * TO THIRD PARTIES, COPIED OR DUPLICATED IN ANY FORM, IN WHOLE OR IN PART,
 * WITHOUT THE PRIOR WRITTEN PERMISSION OF HOWBUY INVESTMENT MANAGEMENT
 * CO., LTD.
 */
package com.howbuy.crm.account.service.commom.utils;

import lombok.extern.slf4j.Slf4j;
import net.sourceforge.pinyin4j.PinyinHelper;
import net.sourceforge.pinyin4j.format.HanyuPinyinCaseType;
import net.sourceforge.pinyin4j.format.HanyuPinyinOutputFormat;
import net.sourceforge.pinyin4j.format.HanyuPinyinToneType;
import net.sourceforge.pinyin4j.format.exception.BadHanyuPinyinOutputFormatCombination;
import org.apache.commons.lang3.StringUtils;

/**
 * <AUTHOR>
 * @description: 汉语拼音工具类
 * @date 2025/05/09 12:05:00
 * @since JDK 1.8
 */
@Slf4j
public class PinyinUtils {

    /**
     * 中文字符的Unicode范围
     */
    private static final String CHINESE_REGEX = "[\\u4E00-\\u9FA5]+";

    /**
     * @description: 获取中文名称的拼音首字母
     * @param chineseName 中文名称
     * @return 拼音首字母字符串
     * <AUTHOR>
     * @date 2024-04-07 12:05:00
     * @since JDK 1.8
     */
    public static String getPinyinFirstLetter(String chineseName) {
        if (StringUtils.isBlank(chineseName)) {
            return "";
        }
        
        StringBuilder pinyinFirstLetters = new StringBuilder();
        for (int i = 0; i < chineseName.length(); i++) {
            char ch = chineseName.charAt(i);
            if (Character.toString(ch).matches(CHINESE_REGEX)) {
                // 中文字符，获取拼音
                String[] pinyinArray = PinyinHelper.toHanyuPinyinStringArray(ch);
                if (pinyinArray != null && pinyinArray.length > 0) {
                    String firstLetter = String.valueOf(pinyinArray[0].charAt(0));
                    pinyinFirstLetters.append(firstLetter);
                }
            } else {
                // 非中文字符，直接添加
                pinyinFirstLetters.append(ch);
            }
        }
        return pinyinFirstLetters.toString();
    }
    
    /**
     * @description: 获取中文名称的完整拼音（不带声调）
     * @param chineseName 中文名称
     * @return 完整拼音字符串
     * <AUTHOR>
     * @date 2024-04-07 12:05:00
     * @since JDK 1.8
     */
    public static String getFullPinyin(String chineseName) {
        if (StringUtils.isBlank(chineseName)) {
            return "";
        }
        
        HanyuPinyinOutputFormat format = new HanyuPinyinOutputFormat();
        // 设置大小写，这里设置为小写
        format.setCaseType(HanyuPinyinCaseType.LOWERCASE);
        // 设置声调，这里设置为不带声调
        format.setToneType(HanyuPinyinToneType.WITHOUT_TONE);
        
        StringBuilder pinyinBuilder = new StringBuilder();
        char[] charArray = chineseName.toCharArray();
        try {
            for (char ch : charArray) {
                if (Character.toString(ch).matches(CHINESE_REGEX)) {
                    // 中文字符，转换为拼音
                    String[] pinyinArray = PinyinHelper.toHanyuPinyinStringArray(ch, format);
                    if (pinyinArray != null && pinyinArray.length > 0) {
                        pinyinBuilder.append(pinyinArray[0]);
                    }
                } else {
                    // 非中文字符，直接添加
                    pinyinBuilder.append(ch);
                }
            }
        } catch (BadHanyuPinyinOutputFormatCombination e) {
            log.error("转换拼音出错", e);
        }
        
        return pinyinBuilder.toString();
    }
    
    /**
     * @description: 将中文字符串转换为拼音首字母缩写
     * 例如：中国人 -> ZGR
     * @param chineseName 中文名称
     * @return 拼音首字母缩写
     * <AUTHOR>
     * @date 2024-04-07 12:05:00
     * @since JDK 1.8
     */
    public static String getPinyinAbbr(String chineseName) {
        if (StringUtils.isBlank(chineseName)) {
            return "";
        }
        
        StringBuilder abbr = new StringBuilder();
        for (int i = 0; i < chineseName.length(); i++) {
            char ch = chineseName.charAt(i);
            if (Character.toString(ch).matches(CHINESE_REGEX)) {
                String[] pinyinArray = PinyinHelper.toHanyuPinyinStringArray(ch);
                if (pinyinArray != null && pinyinArray.length > 0) {
                    abbr.append(pinyinArray[0].charAt(0));
                }
            } else if (Character.isLetter(ch)) {
                // 如果是字母，直接添加
                abbr.append(ch);
            }
        }
        return abbr.toString().toUpperCase();
    }
    
    /**
     * @description: 中文字符串按拼音排序比较器
     * 用于排序时比较两个中文字符串
     * @param str1 字符串1
     * @param str2 字符串2
     * @return 比较结果，负数表示str1小于str2，0表示相等，正数表示str1大于str2
     * <AUTHOR>
     * @date 2024-04-07 12:05:00
     * @since JDK 1.8
     */
    public static int comparePinyin(String str1, String str2) {
        try {
            // 获取名称的拼音首字母
            String pinyin1 = getPinyinFirstLetter(str1);
            String pinyin2 = getPinyinFirstLetter(str2);
            // 按拼音首字母升序排序
            return pinyin1.compareToIgnoreCase(pinyin2);
        } catch (Exception e) {
            log.error("拼音排序异常", e);
            return 0;
        }
    }
} 