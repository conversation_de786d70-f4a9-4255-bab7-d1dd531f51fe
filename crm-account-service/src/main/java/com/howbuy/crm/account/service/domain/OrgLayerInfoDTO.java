package com.howbuy.crm.account.service.domain;

import lombok.Getter;
import lombok.Setter;

import java.io.Serializable;

/**
 * <AUTHOR>
 * @description: 组织架构的层级信息
 * @date 2023/11/22 17:32
 * @since JDK 1.8
 */
@Getter
@Setter
public class OrgLayerInfoDTO implements Serializable {

    private static final long serialVersionUID = 1L;
    /**
     * 部门-组织架构code
     */
    private String orgCode;

    /**
     * 部门-组织架构 名称
     */
    private String orgName;


    /**
     * 区域 -组织架构code
     */
    private String districtCode;

    /**
     * 区域 -组织架构 名称
     */
    private String districtName;

    /**
     * 中心 -组织架构code
     */
    private String centerOrgCode;

    /**
     * 中心 -组织架构 名称
     */
    private String centerOrgName;

    /**
     * 分公司 -组织架构 名称
     */
    private String partOrgName;

    /**
     * 分公司 -组织架构 code
     */
    private String partOrgCode;

}
