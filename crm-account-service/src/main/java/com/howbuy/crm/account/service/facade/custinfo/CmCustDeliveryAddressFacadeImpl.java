/**
 * Copyright (c) 2024, <PERSON><PERSON><PERSON><PERSON> HOWBUY INVESTMENT MANAGEMENT Co., Ltd.
 * All right reserved.
 * <p>
 * THIS IS UNPUBLISHED PROPRIETARY SOURCE CODE OF HOWBUY INVESTMENT
 * MANAGEMENT CO., LTD.  THE CONTENTS OF THIS FILE MAY NOT BE DISCLOSED
 * TO THIRD PARTIES, COPIED OR DUPLICATED IN ANY FORM, IN WHOLE OR IN PART,
 * WITHOUT THE PRIOR WRITTEN PERMISSION OF HOWBUY INVESTMENT MANAGEMENT
 * CO., LTD.
 */
package com.howbuy.crm.account.service.facade.custinfo;

import com.howbuy.crm.account.client.facade.custinfo.CmCustDeliveryAddressFacade;
import com.howbuy.crm.account.client.request.custinfo.QueryCmCustDeliveryAddrRequest;
import com.howbuy.crm.account.client.response.Response;
import com.howbuy.crm.account.client.response.custinfo.CmConsCustVO;
import com.howbuy.crm.account.client.response.custinfo.CmConscustDeliveryAddressVO;
import com.howbuy.crm.account.client.response.custinfo.CmCustDeliveryAddrVO;
import com.howbuy.crm.account.service.service.custinfo.ConsCustInfoService;
import com.howbuy.crm.account.service.service.custinfo.CustDeliveryAddressService;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.dubbo.config.annotation.DubboService;
import org.springframework.beans.BeanUtils;

import javax.annotation.Resource;
import java.util.List;
import java.util.Objects;

/**
 * @description: 投顾版收货地址
 * <AUTHOR>
 * @date 2024/11/19 10:48
 * @since JDK 1.8
 */
@DubboService(timeout = 5000)
public class CmCustDeliveryAddressFacadeImpl implements CmCustDeliveryAddressFacade {

    @Resource
    private CustDeliveryAddressService custDeliveryAddressService;

    @Resource
    private ConsCustInfoService consCustInfoService;

    @Override
    public Response<CmCustDeliveryAddrVO> queryDeliveryAddrInfo(QueryCmCustDeliveryAddrRequest request) {
        CmConsCustVO cmConsCustVO = consCustInfoService.queryCustInfoByHboneNo(request.getHboneNo());
        if (Objects.isNull(cmConsCustVO) || StringUtils.isBlank(cmConsCustVO.getConscustno())) {
            return Response.fail("客户信息不存在");
        }

        Response<List<CmConscustDeliveryAddressVO>> custDeliveryAddrResponse =
                custDeliveryAddressService.getCustDeliveryAddrByCustNo(cmConsCustVO.getConscustno());
        if (custDeliveryAddrResponse.isSuccess()
                && CollectionUtils.isNotEmpty(custDeliveryAddrResponse.getData())) {
            CmConscustDeliveryAddressVO cmConscustDeliveryAddressVO = custDeliveryAddrResponse.getData().get(0);

            CmCustDeliveryAddrVO returnVo = new CmCustDeliveryAddrVO();
            BeanUtils.copyProperties(cmConscustDeliveryAddressVO, returnVo);
            returnVo.setHboneNo(request.getHboneNo());

            return Response.ok(returnVo);
        }

        return Response.ok(null);
    }
}