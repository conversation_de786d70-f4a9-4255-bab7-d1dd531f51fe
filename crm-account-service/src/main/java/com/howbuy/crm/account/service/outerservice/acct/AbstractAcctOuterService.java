/**
 * Copyright (c) 2024, <PERSON><PERSON><PERSON><PERSON> HOWBUY INVESTMENT MANAGEMENT Co., Ltd.
 * All right reserved.
 * <p>
 * THIS IS UNPUBLISHED PROPRIETARY SOURCE CODE OF HOWBUY INVESTMENT
 * MANAGEMENT CO., LTD.  THE CONTENTS OF THIS FILE MAY NOT BE DISCLOSED
 * TO THIRD PARTIES, COPIED OR DUPLICATED IN ANY FORM, IN WHOLE OR IN PART,
 * WITHOUT THE PRIOR WRITTEN PERMISSION OF HOWBUY INVESTMENT MANAGEMENT
 * CO., LTD.
 */
package com.howbuy.crm.account.service.outerservice.acct;

import com.howbuy.acccenter.facade.common.AccBaseResponse;
import com.howbuy.crm.account.client.response.Response;
import com.howbuy.crm.account.service.commom.constant.Constants;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

/**
 * @description: (请在此添加描述)
 * <AUTHOR>
 * @date 2024/5/29 9:52
 * @since JDK 1.8
 */
@Slf4j
@Service
public  abstract class AbstractAcctOuterService {


    /**
     * 账户中心acct  response返回接口 是否成功，统一判断逻辑
     * @param resp
     * @return
     */
    public  static boolean isAcctSuccess(AccBaseResponse resp){
        return  resp!=null &&  Constants.ACCT_CENTER_SUCCESS_CODE.equals(resp.getReturnCode());
    }


    /**
     * 通用 acct返回的对象 --> account的response  方法
     * @param resp
     * @return
     */
    public static Response<String> transferAcctResponse(AccBaseResponse resp){
        if(resp==null){
            return Response.fail("系统错误！");
        }
        if(isAcctSuccess(resp)){
            return Response.ok();
        }
        return Response.fail(resp.getDescription());
    }
}