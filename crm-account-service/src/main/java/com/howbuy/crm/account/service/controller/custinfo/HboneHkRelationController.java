/**
 * Copyright (c) 2023, <PERSON><PERSON><PERSON><PERSON> HOWBUY INVESTMENT MANAGEMENT Co., Ltd.
 * All right reserved.
 * <p>
 * THIS IS UNPUBLISHED PROPRIETARY SOURCE CODE OF HOWBUY INVESTMENT
 * MANAGEMENT CO., LTD.  THE CONTENTS OF THIS FILE MAY NOT BE DISCLOSED
 * TO THIRD PARTIES, COPIED OR DUPLICATED IN ANY FORM, IN WHOLE OR IN PART,
 * WITHOUT THE PRIOR WRITTEN PERMISSION OF HOWBUY INVESTMENT MANAGEMENT
 * CO., LTD.
 */
package com.howbuy.crm.account.service.controller.custinfo;

import com.howbuy.crm.account.client.response.Response;
import com.howbuy.crm.account.client.response.custinfo.CustRelatedAcctInfoVO;
import com.howbuy.crm.account.client.response.custinfo.HkAndHboneRelationVO;
import com.howbuy.crm.account.service.service.custinfo.HkAndHboneRelationService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

/**
 * @description: (账户中心-香港客户 关系管理)
 * <AUTHOR>
 * @date 2023/12/13 16:08
 * @since JDK 1.8
 */
@RestController
@RequestMapping("/hbonehkrelation")
public class HboneHkRelationController {

    @Autowired
    private HkAndHboneRelationService relationService;


    /**
     * @api {POST} /hbonehkrelation/queryrelation queryRelation()
     * @apiVersion 1.0.0
     * @apiGroup HboneHkRelationController
     * @apiName queryRelation()
     * @apiDescription 查询一账通号和香港客户号的关联关系
     * @apiParam (请求体) {String} hboneNo 一账通账号
     * @apiParam (请求体) {String} hkTxAcctNo 香港客户号
     * @apiParamExample 请求体示例
     * {"hkTxAcctNo":"eN","hboneNo":"m1qgXUV"}
     * @apiSuccess (响应结果) {String} code 状态码
     * @apiSuccess (响应结果) {String} description 描述信息
     * @apiSuccess (响应结果) {Object} data 数据封装
     * @apiSuccess (响应结果) {String} data.hboneNo 一账通账号
     * @apiSuccess (响应结果) {String} data.hkTxAcctNo 香港客户号
     * @apiSuccessExample 响应结果示例
     * {"code":"phCb","data":{"hkTxAcctNo":"V3a","hboneNo":"S3NGTRS0CV"},"description":"XPLTsS"}
     */
    @ResponseBody
    @PostMapping("/queryrelation")
    public Response<HkAndHboneRelationVO> queryRelation(@RequestBody HkAndHboneRelationVO relationReq) {
        return Response.ok(relationService.getHkAndHboneRelation(relationReq));
    }


    /**
     * @api {GET} /hbonehkrelation/queryrelatedacctinfo queryCustRelatedAcctInfo()
     * @apiVersion 1.0.0
     * @apiGroup HboneHkRelationController
     * @apiName queryCustRelatedAcctInfo()
     * @apiDescription 根据crm客户号，获取crm客户号关联的一账通号、公募客户号、香港交易账号
     * @apiParam (请求参数) {String} custNo
     * @apiParamExample 请求参数示例
     * custNo=PCNphAM
     * @apiSuccess (响应结果) {String} code 状态码
     * @apiSuccess (响应结果) {String} description 描述信息
     * @apiSuccess (响应结果) {Object} data 数据封装
     * @apiSuccess (响应结果) {String} data.custNo CRM客户号
     * @apiSuccess (响应结果) {String} data.hboneNo [CRM关联的] 一账通号
     * @apiSuccess (响应结果) {String} data.txAcctNo 公募交易账号
     * @apiSuccess (响应结果) {String} data.hboneHkTxAcctNo [一账通关联的] 香港交易账号
     * @apiSuccess (响应结果) {String} data.crmHkTxAcctNo [CRM关联的] 香港交易账号
     * @apiSuccessExample 响应结果示例
     * {"code":"KYaEe","data":{"hboneHkTxAcctNo":"1hdWa2","custNo":"pTtbZ90J8","txAcctNo":"alggRROS","crmHkTxAcctNo":"qnooSdo95","hboneNo":"gC9HT6OiCP"},"description":"j7SBbmNVsJ"}
     */
    @ResponseBody
    @GetMapping("/queryrelatedacctinfo")
    public Response<CustRelatedAcctInfoVO> queryCustRelatedAcctInfo(@RequestParam(name="custNo") String custNo) {
        return Response.ok(relationService.getCustRelatedAcctInfo(custNo));
    }


}