package com.howbuy.crm.account.service.vo.beisen;

import com.alibaba.excel.annotation.ExcelProperty;
import lombok.Getter;
import lombok.Setter;
import lombok.ToString;

import java.io.Serializable;

/**
 * <AUTHOR>
 * @description: 明细
 * @date 2024/10/22 15:53
 * @since JDK 1.8
 */
@Getter
@Setter
@ToString
public class CmBeisenOrgConfigExcelVO implements Serializable {
    private static final long serialVersionUID = 1L;


    /**
     * 架构ID（北森）
     */
    @ExcelProperty(value = "架构ID（北森）", index = 0)
    private String orgIdBeisen;

    /**
     * 架构名称（北森）
     */
    @ExcelProperty(value = "架构名称（北森）", index = 1)
    private String orgNameBeisen;

    /**
     * 中心（crm）
     */
    @ExcelProperty(value = "中心（crm）", index = 2)
    private String u1NameCrm;

    /**
     * 区域（crm）
     */
    @ExcelProperty(value = "区域（crm）", index = 3)
    private String u2NameCrm;

    /**
     * 分公司（crm）
     */
    @ExcelProperty(value = "分公司（crm）", index = 4)
    private String u3NameCrm;
    /**
     * 业务中心
     */
    @ExcelProperty(value = "业务中心", index = 5)
    private String centerOrg;

    /**
     * 起始日期
     */
    @ExcelProperty(value = "起始日期", index = 6)
    private String startDate;

    /**
     * 结束日期
     */
    @ExcelProperty(value = "结束日期", index = 7)
    private String endDate;
}