/**
 * Copyright (c) 2025, <PERSON><PERSON><PERSON><PERSON> HOWBUY INVESTMENT MANAGEMENT Co., Ltd.
 * All right reserved.
 * <p>
 * THIS IS UNPUBLISHED PROPRIETARY SOURCE CODE OF HOWBUY INVESTMENT
 * MANAGEMENT CO., LTD.  THE CONTENTS OF THIS FILE MAY NOT BE DISCLOSED
 * TO THIRD PARTIES, COPIED OR DUPLICATED IN ANY FORM, IN WHOLE OR IN PART,
 * WITHOUT THE PRIOR WRITTEN PERMISSION OF HOWBUY INVESTMENT MANAGEMENT
 * CO., LTD.
 */
package com.howbuy.crm.account.service.repository.sensitive;

import com.howbuy.crm.account.dao.mapper.customize.sensitive.CustomizeCsKeywordMapper;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import org.springframework.transaction.annotation.Propagation;
import org.springframework.transaction.annotation.Transactional;

import java.util.List;

/**
 * @description 敏感词查询接口实现
 * <AUTHOR>
 * @date 2024-03-19 14:30:45
 */
@Component
@Slf4j
@Transactional(rollbackFor = Exception.class, propagation = Propagation.SUPPORTS)
public class CsKeywordRepository {

    @Autowired
    private CustomizeCsKeywordMapper customizeCsKeywordMapper;

    /**
     * <AUTHOR>
     * @description 根据功能模块查询敏感词列表
     * @date 2024-03-27 14:23:45
     * @param moduleType 功能模块
     * @return List<String> 敏感词列表
     */
    public List<String> listKeywordNameByModule(String moduleType) {
        return customizeCsKeywordMapper.listKeywordNameByModule(moduleType);
    }
}
