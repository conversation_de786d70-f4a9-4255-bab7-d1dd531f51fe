/**
 * Copyright (c) 2023, <PERSON><PERSON><PERSON><PERSON> HOWBUY INVESTMENT MANAGEMENT Co., Ltd.
 * All right reserved.
 * <p>
 * THIS IS UNPUBLISHED PROPRIETARY SOURCE CODE OF HOWBUY INVESTMENT
 * MANAGEMENT CO., LTD.  THE CONTENTS OF THIS FILE MAY NOT BE DISCLOSED
 * TO THIRD PARTIES, COPIED OR DUPLICATED IN ANY FORM, IN WHOLE OR IN PART,
 * WITHOUT THE PRIOR WRITTEN PERMISSION OF HOWBUY INVESTMENT MANAGEMENT
 * CO., LTD.
 */
package com.howbuy.crm.account.service.service.custmessage;

import com.google.common.collect.Lists;
import com.howbuy.crm.account.client.enums.custinfo.AbnormaSceneTypeEnum;
import com.howbuy.crm.account.client.enums.custinfo.FullCustSourceEnum;
import com.howbuy.crm.account.dao.bo.custinfo.CmConscustForAnalyseBO;
import com.howbuy.crm.account.service.repository.custinfo.AbnormalCustRepository;
import com.howbuy.crm.account.service.vo.custinfo.AbnormalAnalyseResultVO;
import com.howbuy.crm.account.service.vo.custinfo.HboneMessageCustInfoVO;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * @description: ([一账通账户]-[一账通账户户手机号变更|客户证件信息变更 ] 消息)
 * <AUTHOR>
 * @date 2023/12/14 10:12
 * @since JDK 1.8
 */
@Service
@Slf4j
public class HboneCustInfoChangeAnalyseService implements CustMessageAnalyseService<HboneMessageCustInfoVO> {

    @Autowired
    private AbnormalCustRepository abnormalCustRepository;

    @Override
    public List<FullCustSourceEnum>  getSourceList() {
        return Lists.newArrayList(
                FullCustSourceEnum.HBONE_CUST_MOBILE_CHANGE);
    }

    @Override
    public AbnormalAnalyseResultVO<HboneMessageCustInfoVO> analyze(HboneMessageCustInfoVO analyseVo) {
        AbnormalAnalyseResultVO<HboneMessageCustInfoVO> returnAnalyseVo=AbnormalAnalyseResultVO.normalData(analyseVo);

        String hboneNo=analyseVo.getHboneNo();
        List<CmConscustForAnalyseBO> hbCustList=abnormalCustRepository.queryCustBOByHboneNo(hboneNo);

        if(CollectionUtils.isEmpty(hbCustList)){
            log.info("【{}】消息处理：该一账通号[{}]未绑定投顾客户号！",
                    FullCustSourceEnum.getDescription(analyseVo.getAbnormalSource()),
                    hboneNo);
            //标记为： 无需处理
            returnAnalyseVo.setNeedProcess(false);
            return returnAnalyseVo;
        }
        //开发自定义流程： 历史数据存在hboneNo绑定多个custNo 的情况，此处不做处理
        if(hbCustList.size()>1){
            log.error("【{}】消息处理异常：该一账通号[{}]绑定了多个投顾客户号",
                    FullCustSourceEnum.getDescription(analyseVo.getAbnormalSource()),
                    hboneNo);
            return AbnormalAnalyseResultVO.
                    notNormalData(analyseVo,AbnormaSceneTypeEnum.HBONE_NO_MATCH_MULTIPLE_CUST_NO,hbCustList);
        }
        CmConscustForAnalyseBO processedCustBo=hbCustList.get(0);

//（2）若存在，且存在重复客户，则数据进异常表，不更新投顾客户信息  //20240110更新：先判重，有重复客户则不更新客户数据
//（1）重复客户的匹配范围，需剔除当前的【投顾客户号】
        List<CmConscustForAnalyseBO> repeatList=null;
        String excludeCustNo=processedCustBo.getConscustno();
        //手机号变更
        if(FullCustSourceEnum.HBONE_CUST_MOBILE_CHANGE.getCode().equals(analyseVo.getAbnormalSource())){
            repeatList=analyseMobile(analyseVo,excludeCustNo);
        }
        //HBONE_CUST_REAL_NAME_CHANGE 已调整 。此处不会出现
//        else if(FullCustSourceEnum.HBONE_CUST_REAL_NAME_CHANGE.getCode().equals(analyseVo.getAbnormalSource())){
//            //证件信息变更
//            repeatList=analyseIdNo(analyseVo,excludeCustNo);
//        }
//（3）若存在重复客户，则异常数据进“香港异常客户表”：
//【香港客户号】落“香港异常客户主表”，写入字段：
//        a、异常来源：香港客户手机号变更同步
//        b、异常描述：CRM重复客户预警
//        c、异常时间：取落入异常表的时间，格式yyyymmdd hh:mm:ss
//        当前【投顾客户号】和其他匹配上的【投顾客户号】（重复客户）均落“香港异常客户待关联表”，与主表数据相对应。
        if(CollectionUtils.isNotEmpty(repeatList)){
            //进异常数据表
            repeatList.add(processedCustBo);
            return AbnormalAnalyseResultVO.
                    notNormalData(analyseVo,AbnormaSceneTypeEnum.CRM_REPEAT_CUST_WARNING,repeatList);
        }
        //正常场景 ：  待处理客户对象
        returnAnalyseVo.setProcessedCustInfo(processedCustBo);
        return returnAnalyseVo;
    }


    /**
     * 验证 手机信息重复
     * @param analyseVo
     * @param excludeCustNo
     * @return
     */
    private List<CmConscustForAnalyseBO> analyseMobile(HboneMessageCustInfoVO analyseVo,String excludeCustNo){
        //（2）满足以下任一条件，则认为存在重复客户
//        条件1：【手机区号+手机号】相同
        return abnormalCustRepository.searchMobileExistList(analyseVo.getInvestType(),
                analyseVo.getMobileAreaCode(),
                analyseVo.getMobileDigest(),
                excludeCustNo);
    }

    /**
     * 验证证件信息重复
     * @param analyseVo
     * @param excludeCustNo
     * @return
     */
    private List<CmConscustForAnalyseBO> analyseIdNo(HboneMessageCustInfoVO analyseVo,String excludeCustNo){
        return abnormalCustRepository.searchIdExistList(analyseVo.getInvestType(),
                analyseVo.getIdType(),analyseVo.getIdSignAreaCode(),analyseVo.getIdNoDigest(),
                excludeCustNo);
    }

}