/**
 * Copyright (c) 2023, <PERSON><PERSON><PERSON><PERSON> HOWBUY INVESTMENT MANAGEMENT Co., Ltd.
 * All right reserved.
 * <p>
 * THIS IS UNPUBLISHED PROPRIETARY SOURCE CODE OF HOWBUY INVESTMENT
 * MANAGEMENT CO., LTD.  THE CONTENTS OF THIS FILE MAY NOT BE DISCLOSED
 * TO THIRD PARTIES, COPIED OR DUPLICATED IN ANY FORM, IN WHOLE OR IN PART,
 * WITHOUT THE PRIOR WRITTEN PERMISSION OF HOWBUY INVESTMENT MANAGEMENT
 * CO., LTD.
 */
package com.howbuy.crm.account.service.repository.custinfo;

import com.alibaba.fastjson.JSON;
import com.howbuy.common.utils.DateUtil;
import com.howbuy.common.utils.StringUtil;
import com.howbuy.crm.account.client.response.Response;
import com.howbuy.crm.account.client.response.custinfo.CmConsCustVO;
import com.howbuy.crm.account.client.response.custinfo.CmConsCustWithCipherVO;
import com.howbuy.crm.account.dao.bo.custinfo.CmConsCustSimpleBO;
import com.howbuy.crm.account.dao.mapper.CommonMapper;
import com.howbuy.crm.account.dao.mapper.custinfo.*;
import com.howbuy.crm.account.dao.mapper.customize.custinfo.ConscustCustomizeMapper;
import com.howbuy.crm.account.dao.po.custinfo.*;
import com.howbuy.crm.account.dao.req.custinfo.ConsCustICReqVO;
import com.howbuy.crm.account.dao.req.custinfo.CreateConsCustReqVO;
import com.howbuy.crm.account.dao.req.custinfo.ModifyConsCustReqVO;
import com.howbuy.crm.account.dao.req.custinfo.UpdateConsCustReqVO;
import com.howbuy.crm.account.service.commom.constant.Constants;
import com.howbuy.crm.account.service.commom.constant.SequenceConstants;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import org.springframework.transaction.annotation.Propagation;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.Assert;

import java.util.Date;

/**
 * @description: (投顾客户操作记录 [IC表、划转相关] repository)
 * <AUTHOR>
 * @date 2023/12/8 15:31
 * @since JDK 1.8
 */
@Component
@Slf4j
@Transactional(rollbackFor = Exception.class, propagation = Propagation.SUPPORTS)
public class ConsCustICRepository {

    @Autowired
    private CmConscustMapper conscustMapper;
    @Autowired
    private CmConscustCipherMapper conscustCipherMapper;
    @Autowired
    private CmConscusthisCipherMapper hisCipherMapper;
    @Autowired
    private CmConscusthisMapper hisMapper;
    @Autowired
    private ConscustCustomizeMapper conscustCustomizeMapper;
    @Autowired
    private CommonMapper commonMapper;


    @Autowired
    private CmConscustOperationMapper custOperationMapper;
    @Autowired
    private CmConscustIcMapper icMapper;
    @Autowired
    private CmConscustIcCipherMapper icCipherMapper;


    /**
     * 投顾客户  新增修改 操作集合 表
     * 与划转相关
     */
    public String getCustOpreationId(){
        return  commonMapper.getSeqValue(SequenceConstants.SEQ_CONSCUST_OPERATION);
    }



    
    /**
     * @description:(请在此添加描述)
     * @param reqVo	
     * @param icReqVo
     * @return com.howbuy.crm.account.client.response.Response<java.lang.String>
     * @author: haoran.zhang
     * @date: 2023/12/27 11:13
     * @since JDK 1.8
     */
    @Transactional(rollbackFor = Exception.class, propagation = Propagation.REQUIRED)
    public Response<String> createOperation(ModifyConsCustReqVO reqVo,ConsCustICReqVO icReqVo){
        Assert.notNull(icReqVo.getOperationId(),"划转操作id不能为空");

        //客户信息
        CmConscustPO record=reqVo.getConscustPO();
        //cipher 加密信息
        CmConscustCipherPO cipherPo=reqVo.getCipherPO();

        CmConscustOperationPO operationPo=new CmConscustOperationPO();
        operationPo.setOperationid(icReqVo.getOperationId());
        operationPo.setConscustno(record.getConscustno());
        operationPo.setCitycode(record.getCitycode());
        operationPo.setCustsourceremark(record.getCustsourceremark());
        operationPo.setNewsourceno(record.getNewsourceno());
        operationPo.setConscustlvl(record.getConscustlvl());
        operationPo.setConscustgrade(record.getConscustgrade());
        operationPo.setRegdt(record.getRegdt());
        operationPo.setTransferapply(icReqVo.getTransferapply());
        operationPo.setOpstatus(icReqVo.getOpstatus());
        operationPo.setStatus(icReqVo.getStatus());
        operationPo.setCustname(record.getCustname());
        operationPo.setCreator(reqVo.getOperator());
        operationPo.setCreatdt(new Date());
        operationPo.setProvcode(record.getProvcode());
        operationPo.setOpensource(icReqVo.getOpensource());
        operationPo.setMobileAreaCode(record.getMobileAreaCode());
        operationPo.setMobileDigest(record.getMobileDigest());
        operationPo.setMobileMask(record.getMobileMask());
        operationPo.setMobileCipher(cipherPo.getMobileCipher());
        operationPo.setSourcechannel(icReqVo.getSourcechannel());
        custOperationMapper.insert(operationPo);
        return Response.ok();
    }


    /**
     * 生成客户号
     * @return
     */
    public String generateConsCustNo() {
        String seqValue = commonMapper.getSeqValue(SequenceConstants.SEQ_CM_CONSCUST);
        // 固定长度的值
        String fixSeqValue = StringUtil.fillZero(seqValue, 8);
        return new StringBuffer("1").append(fixSeqValue).append(
                StringUtil.getValiateCode("1" + fixSeqValue)).toString();
    }

/**
 * @description:(请在此添加描述)
 * @param reqVo	
 * @param icReqVo	IC表 属性
 * @param custNo  客户号  允许为空
 * @return java.lang.String
 * @author: haoran.zhang
 * @date: 2023/12/27 13:04
 * @since JDK 1.8
 */
@Transactional(rollbackFor = Exception.class, propagation = Propagation.REQUIRED)
    public Response<String> createCustIc(CreateConsCustReqVO  reqVo,
                                         ConsCustICReqVO icReqVo,
                                         String custNo){

        //存在  不插入客户，直接插入IC表
        if(StringUtil.isBlank(custNo)){
            custNo=generateConsCustNo();
        }

        //客户信息
        CmConscustPO record=reqVo.getConscustPO();
        record.setConscustno(custNo);
        //cipher 加密信息
        CmConscustCipherPO cipherPo=reqVo.getCipherPO();
        cipherPo.setConscustno(custNo);

        CmConscustIcPO icPo=new CmConscustIcPO();

        //特殊字段
        icPo.setTransferapply(icReqVo.getTransferapply());
        icPo.setOpstatus(icReqVo.getOpstatus());
        icPo.setCheckflag(icReqVo.getIcCheckFlag());
        icPo.setCheckremark(null);
        icPo.setCheckman(null);

        //客户表 复制   字段
        icPo.setConscustno(record.getConscustno());
        icPo.setConscustlvl(record.getConscustlvl());
        icPo.setConscustgrade(record.getConscustgrade());
        icPo.setConscuststatus(record.getConscuststatus());
        icPo.setIdtype(record.getIdtype());
        icPo.setIdSignAreaCode(record.getIdSignAreaCode());
        icPo.setCustname(record.getCustname());
        icPo.setNationCode(record.getNationCode());
        icPo.setProvcode(record.getProvcode());
        icPo.setCitycode(record.getCitycode());
        icPo.setCountyCode(record.getCountyCode());
        icPo.setEdulevel(record.getEdulevel());
        icPo.setVocation(record.getVocation());
        icPo.setInclevel(record.getInclevel());
        icPo.setBirthday(record.getBirthday());
        icPo.setGender(record.getGender());
        icPo.setMarried(record.getMarried());
        icPo.setPincome(record.getPincome());
        icPo.setFincome(record.getFincome());
        icPo.setDecisionflag(record.getDecisionflag());
        icPo.setInterests(record.getInterests());
        icPo.setFamilycondition(record.getFamilycondition());
        icPo.setContacttime(record.getContacttime());

        //产线从 20180717 后 历史数据均为空 且无赋值逻辑
        icPo.setContactmethod(null);
        icPo.setSendinfoflag(record.getSendinfoflag());
        icPo.setRecvtelflag(record.getRecvtelflag());
        icPo.setRecvemailflag(record.getRecvemailflag());
        icPo.setRecvmsgflag(record.getRecvmsgflag());
        icPo.setCompany(record.getCompany());
        icPo.setRisklevel(record.getRisklevel());
        icPo.setSelfrisklevel(record.getSelfrisklevel());
        icPo.setPostcode(record.getPostcode());
        icPo.setFax(record.getFax());
        //产线数据  全部为null
        icPo.setHometelno(null);
        icPo.setOfficetelno(record.getOfficetelno());
        //产线数据  全部为null
        icPo.setActcode(null);
        //产线数据  全部为null
        icPo.setIntrcustno(null);
        icPo.setSource(record.getSource());
        icPo.setKnowchan(record.getKnowchan());
        icPo.setOtherchan(record.getOtherchan());
        icPo.setOtherinvest(record.getOtherinvest());
        icPo.setSalon(record.getSalon());
        icPo.setBeforeinvest(record.getBeforeinvest());
        //产线只有历史14条数据 不为空， 且无赋值逻辑
        icPo.setIm(null);
        //产线只有历史1条数据 不为空， 且无赋值逻辑
        icPo.setMsn(null);
        //产线从 20180907 后 历史数据均为空 且无赋值逻辑
        icPo.setAinvestamt(null);
        //产线从 20180907 后 历史数据均为空 且无赋值逻辑
        icPo.setAinvestfamt(null);
        //产线数据  全部为null
        icPo.setSelfdefflag(null);
        icPo.setVisitfqcy(record.getVisitfqcy());
        icPo.setDevdirection(record.getDevdirection());
        icPo.setSaledirection(record.getSaledirection());
        icPo.setSubsource(record.getSubsource());
        icPo.setSubsourcetype(record.getSubsourcetype());
        //产线从 20180705 后 历史数据均为空 且无赋值逻辑
        icPo.setSaleprocess(null);
        //产线数据  全部为null
        icPo.setMergedconscust(null);
        icPo.setPostcode2(record.getPostcode2());
        icPo.setKnowhowbuy(record.getKnowhowbuy());
        icPo.setSubknow(record.getSubknow());
        icPo.setSubknowtype(record.getSubknowtype());
        icPo.setBuyingprod(record.getBuyingprod());
        icPo.setBuyedprod(record.getBuyedprod());
        // 产线从 20211212 后 历史数据均为空 且无赋值逻辑
        icPo.setFreeprod(null);
        icPo.setSpecialflag(record.getSpecialflag());
        icPo.setDlvymode(record.getDlvymode());
        icPo.setRemark(record.getRemark());
        icPo.setRegdt(record.getRegdt());
        icPo.setUddt(record.getUddt());
        icPo.setPririsklevel(record.getPririsklevel());
        icPo.setLinkman(record.getLinkman());
        icPo.setLinkpostcode(record.getLinkpostcode());
        icPo.setCapacity(record.getCapacity());
        //产线数据  全部为null
        icPo.setActivityno(null);
        //产线数据  全部为null
        icPo.setPartnerno(null);
        icPo.setGpsinvestlevel(record.getGpsinvestlevel());
        icPo.setGpsrisklevel(record.getGpsrisklevel());
        icPo.setIsboss(record.getIsboss());
        icPo.setFinanceneed(record.getFinanceneed());
        icPo.setIsjoinclub(record.getIsjoinclub());
        //产线数据  全部为null
        icPo.setTmpBeforeinvest(null);
        //产线数据  全部为null
        icPo.setTmpOtherinvest(null);
        icPo.setIsrisktip(record.getIsrisktip());
        //产线数据  全部为null
        icPo.setTempemailflag(null);
        icPo.setCustsourceremark(record.getCustsourceremark());

        icPo.setCreator(reqVo.getOperator());
        icPo.setInvsttype(record.getInvsttype());
        icPo.setWechatcode(record.getWechatcode());
        icPo.setNewsourceno(record.getNewsourceno());
        icPo.setHopetradetype(record.getHopetradetype());
        icPo.setValidity(record.getValidity());
        icPo.setValiditydt(record.getValiditydt());
        icPo.setNature(record.getNature());
        icPo.setAptitude(record.getAptitude());
        icPo.setScopebusiness(record.getScopebusiness());

        icPo.setIdnoDigest(record.getIdnoDigest());
        icPo.setIdnoMask(record.getIdnoMask());
        icPo.setCustnameDigest(null);
        icPo.setCustnameMask(null);
        icPo.setAddrDigest(record.getAddrDigest());
        icPo.setAddrMask(record.getAddrMask());
        icPo.setMobileAreaCode(record.getMobileAreaCode());
        icPo.setMobileDigest(record.getMobileDigest());
        icPo.setMobileMask(record.getMobileMask());
        icPo.setTelnoDigest(record.getTelnoDigest());
        icPo.setTelnoMask(record.getTelnoMask());
        icPo.setEmailDigest(record.getEmailDigest());
        icPo.setEmailMask(record.getEmailMask());
        icPo.setAddr2Digest(record.getAddr2Digest());
        icPo.setAddr2Mask(record.getAddr2Mask());
        icPo.setMobile2AreaCode(record.getMobile2AreaCode());
        icPo.setMobile2Digest(record.getMobile2Digest());
        icPo.setMobile2Mask(record.getMobile2Mask());
        icPo.setEmail2Digest(record.getEmail2Digest());
        icPo.setEmail2Mask(record.getEmail2Mask());
        icPo.setLinkmanDigest(record.getLinkmanDigest());
        icPo.setLinkmanMask(record.getLinkmanMask());
        icPo.setLinktelDigest(record.getLinktelDigest());
        icPo.setLinktelMask(record.getLinktelMask());
        icPo.setLinkmobileAreaCode(record.getLinkmobileAreaCode());
        icPo.setLinkmobileDigest(record.getLinkmobileDigest());
        icPo.setLinkmobileMask(record.getLinkmobileMask());
        icPo.setLinkemailDigest(record.getLinkemailDigest());
        icPo.setLinkemailMask(record.getLinkemailMask());
        icPo.setLinkaddrDigest(record.getLinkaddrDigest());
        icPo.setLinkaddrMask(record.getLinkaddrMask());

        icMapper.insert(icPo);


        CmConscustIcCipherPO icCipherPo=new CmConscustIcCipherPO();
        icCipherPo.setConscustno(cipherPo.getConscustno());
        icCipherPo.setIdnoCipher(cipherPo.getIdnoCipher());
        icCipherPo.setCustnameCipher(null);
        icCipherPo.setAddrCipher(cipherPo.getAddrCipher());
        icCipherPo.setMobileCipher(cipherPo.getMobileCipher());
        icCipherPo.setTelnoCipher(cipherPo.getTelnoCipher());
        icCipherPo.setEmailCipher(cipherPo.getEmailCipher());
        icCipherPo.setAddr2Cipher(cipherPo.getAddr2Cipher());
        icCipherPo.setMobile2Cipher(cipherPo.getMobile2Cipher());
        icCipherPo.setEmail2Cipher(cipherPo.getEmail2Cipher());
        icCipherPo.setLinkmanCipher(cipherPo.getLinkmanCipher());
        icCipherPo.setLinktelCipher(cipherPo.getLinktelCipher());
        icCipherPo.setLinkmobileCipher(cipherPo.getLinkmobileCipher());
        icCipherPo.setLinkemailCipher(cipherPo.getLinkemailCipher());
        icCipherPo.setLinkaddrCipher(cipherPo.getLinkaddrCipher());

        icCipherMapper.insert(icCipherPo);

        createOperation(reqVo, icReqVo);
        return Response.ok();
    }


}