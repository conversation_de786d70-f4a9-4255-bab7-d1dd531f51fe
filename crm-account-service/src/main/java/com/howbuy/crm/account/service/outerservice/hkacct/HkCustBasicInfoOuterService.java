/**
 * Copyright (c) 2023, <PERSON><PERSON><PERSON><PERSON> HOWBUY INVESTMENT MANAGEMENT Co., Ltd.
 * All right reserved.
 * <p>
 * THIS IS UNPUBLISHED PROPRIETARY SOURCE CODE OF HOWBUY INVESTMENT
 * MANAGEMENT CO., LTD.  THE CONTENTS OF THIS FILE MAY NOT BE DISCLOSED
 * TO THIRD PARTIES, COPIED OR DUPLICATED IN ANY FORM, IN WHOLE OR IN PART,
 * WITHOUT THE PRIOR WRITTEN PERMISSION OF HOWBUY INVESTMENT MANAGEMENT
 * CO., LTD.
 */
package com.howbuy.crm.account.service.outerservice.hkacct;

import com.alibaba.fastjson.JSON;
import com.google.common.collect.Lists;
import com.howbuy.common.date.DateUtil;
import com.howbuy.common.utils.StringUtil;
import com.howbuy.crm.account.client.enums.HkKycTradeChanCodeEnum;
import com.howbuy.crm.account.client.request.custinfo.HkAcctCreateOptVO;
import com.howbuy.crm.account.client.request.deposit.HkDepositRequest;
import com.howbuy.crm.account.client.response.PageVO;
import com.howbuy.crm.account.client.response.Response;
import com.howbuy.crm.account.client.response.bankcardinfo.HkBankCardInfoVO;
import com.howbuy.crm.account.client.response.custinfo.*;
import com.howbuy.crm.account.client.response.declarationinfo.HkDeclarationInfoVO;
import com.howbuy.crm.account.client.response.deposit.HkDepositRejectVO;
import com.howbuy.crm.account.client.response.deposit.HkDepositResultVO;
import com.howbuy.crm.account.client.response.kycinfo.HkKycInfoVO;
import com.howbuy.crm.account.client.response.taxinfo.HkTaxInfoVO;
import com.howbuy.crm.account.service.commom.constant.Constants;
import com.howbuy.crm.account.service.commom.constant.DobboReferenceConstant;
import com.howbuy.crm.account.service.req.custinfo.HkAcctCustInfoReqVO;
import com.howbuy.hkacconline.facade.common.HkAccBaseRequest;
import com.howbuy.hkacconline.facade.query.kyc.kycanswerlist.QueryHkKycAnswerListFacade;
import com.howbuy.hkacconline.facade.query.kyc.kycanswerlist.QueryHkKycAnswerListRequest;
import com.howbuy.hkacconline.facade.query.kyc.kycanswerlist.QueryHkKycAnswerListResponse;
import com.howbuy.hkacconline.facade.query.kyc.kycanswerlist.dto.KycAnswerDTO;
import com.howbuy.hkacconline.facade.query.queryhkallcustinfoforcounter.QueryHkAllCustInfoForCounterFacade;
import com.howbuy.hkacconline.facade.query.queryhkallcustinfoforcounter.QueryHkAllCustInfoForCounterRequest;
import com.howbuy.hkacconline.facade.query.queryhkallcustinfoforcounter.QueryHkAllCustInfoForCounterResponse;
import com.howbuy.hkacconline.facade.query.queryhkallcustinfoforcounter.dto.QueryDeclarationInfoDTO;
import com.howbuy.hkacconline.facade.query.queryhkcustbankcardlist.QueryHkCustBankCardListFacade;
import com.howbuy.hkacconline.facade.query.queryhkcustbankcardlist.QueryHkCustBankCardListRequest;
import com.howbuy.hkacconline.facade.query.queryhkcustbankcardlist.QueryHkCustBankCardListResponse;
import com.howbuy.hkacconline.facade.query.queryhkcustbankcardlist.dto.BankCardInfoDTO;
import com.howbuy.hkacconline.facade.query.queryhkcustbasicinfo.QueryHkCustBasicInfoFacade;
import com.howbuy.hkacconline.facade.query.queryhkcustbasicinfo.QueryHkCustBasicInfoRequest;
import com.howbuy.hkacconline.facade.query.queryhkcustbasicinfo.QueryHkCustBasicInfoResponse;
import com.howbuy.hkacconline.facade.query.queryhkcustbasicinfoforcounter.QueryHkCustBasicInfoForCounterFacade;
import com.howbuy.hkacconline.facade.query.queryhkcustbasicinfoforcounter.QueryHkCustBasicInfoForCounterRequest;
import com.howbuy.hkacconline.facade.query.queryhkcustbasicinfoforcounter.QueryHkCustBasicInfoForCounterResponse;
import com.howbuy.hkacconline.facade.query.queryhkcusthbonerel.QueryHkCustHboneRelFacade;
import com.howbuy.hkacconline.facade.query.queryhkcusthbonerel.QueryHkCustHboneRelRequest;
import com.howbuy.hkacconline.facade.query.queryhkcusthbonerel.QueryHkCustHboneRelResponse;
import com.howbuy.hkacconline.facade.query.queryhkdepositresult.QueryHkDepositResultFacade;
import com.howbuy.hkacconline.facade.query.queryhkdepositresult.QueryHkDepositResultRequest;
import com.howbuy.hkacconline.facade.query.queryhkdepositresult.QueryHkDepositResultResponse;
import com.howbuy.hkacconline.facade.query.queryhkdepositresult.dto.DepositResultDTO;
import com.howbuy.hkacconline.facade.query.queryhkopenacctresult.QueryHkOpenAcctResultFacade;
import com.howbuy.hkacconline.facade.query.queryhkopenacctresult.QueryHkOpenAcctResultRequest;
import com.howbuy.hkacconline.facade.query.queryhkopenacctresult.QueryHkOpenAcctResultResponse;
import com.howbuy.hkacconline.facade.query.querysensitive.*;
import com.howbuy.hkacconline.facade.trade.hkopenacctforcounter.HkOpenAcctForCounterFacade;
import com.howbuy.hkacconline.facade.trade.hkopenacctforcounter.HkOpenAcctForCounterRequest;
import com.howbuy.hkacconline.facade.trade.hkopenacctforcounter.HkOpenAcctForCounterResponse;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.dubbo.config.annotation.DubboReference;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Service;
import org.springframework.util.Assert;

import java.util.ArrayList;
import java.util.List;
import java.util.stream.Collectors;

/**
 * @description: (香港账户中心 outer service)
 * <AUTHOR>
 * @date 2023/12/8 20:15
 * @since JDK 1.8
 */
@Slf4j
@Service
public class HkCustBasicInfoOuterService {

    @DubboReference(registry = DobboReferenceConstant.HK_ACC_CENTER_SERVER, check = false)
    private QueryHkCustBasicInfoFacade queryHkCustBasicInfoFacade;
    @DubboReference(registry = DobboReferenceConstant.HK_ACC_CENTER_SERVER, check = false)
    private QueryHkCustBasicInfoForCounterFacade hkCustBasicInfoFacade;
    @DubboReference(registry = DobboReferenceConstant.HK_ACC_CENTER_SERVER, check = false)
    private QueryHkCustSensitiveInfoFacade queryHkCustSensitiveInfoFacade;
    @DubboReference(registry = DobboReferenceConstant.HK_ACC_CENTER_SERVER, check = false)
    private QueryHkCustMobileSensitiveInfoFacade mobileSensitiveInfoFacade;

    @DubboReference(registry = DobboReferenceConstant.HK_ACC_CENTER_SERVER, check = false)
    private HkOpenAcctForCounterFacade hkOpenAcctForCounterFacade;

    @DubboReference(registry = DobboReferenceConstant.HK_ACC_CENTER_SERVER, check = false)
    private QueryHkKycAnswerListFacade queryHkKycAnswerListFacade;

    @DubboReference(registry = DobboReferenceConstant.HK_ACC_CENTER_SERVER, check = false)
    private QueryHkCustBankCardListFacade queryHkCustBankCardListFacade;

    @DubboReference(registry = DobboReferenceConstant.HK_ACC_CENTER_SERVER, check = false)
    private QueryHkAllCustInfoForCounterFacade queryHkAllCustInfoForCounterFacade;

    @DubboReference(registry = DobboReferenceConstant.HK_ACC_CENTER_SERVER, check = false)
    private QueryHkOpenAcctResultFacade queryHkOpenAcctResultFacade;

    @DubboReference(registry = DobboReferenceConstant.HK_ACC_CENTER_SERVER, check = false)
    private QueryHkDepositResultFacade queryHkDepositResultFacade;

    @DubboReference(registry = DobboReferenceConstant.HK_ACC_CENTER_SERVER, check = false)
    private QueryHkCustHboneRelFacade queryHkCustHboneRelFacade;



    /**
     * @description:(查询 香港账户中心 一账通的 关联关系 )
     * @param vo
     * @return com.howbuy.crm.account.client.response.custinfo.HkAcctCustDetailInfoVO
     */
    public HkAndHboneRelationVO getHkAndHboneRelation(HkAndHboneRelationVO vo){
        HkAndHboneRelationVO returnVo=null;
        QueryHkCustHboneRelRequest request=new QueryHkCustHboneRelRequest();
        request.setHboneNo(vo.getHboneNo());
        request.setHkCustNo(vo.getHkTxAcctNo());
        QueryHkCustHboneRelResponse resp=queryHkCustHboneRelFacade.execute(request);
        if(resp.isSuccess()){
            returnVo = new HkAndHboneRelationVO();
            returnVo.setHboneNo(resp.getHboneNo());
            returnVo.setHkTxAcctNo(resp.getHkCustNo());
        }
        return returnVo;
    }



    /**
     * @description:(香港账户中心  基础参数填充)
     * @param request
     * @param operatorNo
     */
    private void fillBaseInfo(HkAccBaseRequest request, String operatorNo){
        request.setAppDt(DateUtil.getAppDt());
        request.setAppTm(DateUtil.getAppTm());
        request.setTradeChannel(Constants.REQUEST_TRADE_CHANNEL);
        request.setOperatorNo(operatorNo==null?Constants.REQUEST_OPER_NO:operatorNo);
        request.setOperCode(Constants.REQUEST_OPER_CODE);
        request.setOperIp(Constants.REQUEST_OPER_IP);
    }


    /**
     * @description:(查询 香港账户中心  客户手机号 敏感信息)
     * @param hkTxAcctNo
     * @return java.lang.String
     * @author: haoran.zhang
     * @date: 2023/12/20 13:53
     * @since JDK 1.8
     */
    public String queryHkSensitiveMobile(String hkTxAcctNo){
        Assert.notNull(hkTxAcctNo,"香港交易账号不能为空");
        QueryHkCustMobileSensitiveInfoRequest request = new QueryHkCustMobileSensitiveInfoRequest() ;
        request.setHkCustNo(hkTxAcctNo);
        fillBaseInfo(request,"");
        QueryHkCustMobileSensitiveInfoResponse sensitiveInfo=mobileSensitiveInfoFacade.execute(request);
        if(!sensitiveInfo.isSuccess()){
            log.info("根据香港交易账号：{}，查询香港客户[手机敏感]信息失败，返回信息：{}",hkTxAcctNo, JSON.toJSONString(sensitiveInfo));
            return null;
        }
        return sensitiveInfo.getMobile();
    }


    /**
     * @description:(查询 香港账户中心  客户信息 敏感信息)
     * @param hkTxAcctNo
     * @return com.howbuy.crm.account.client.response.custinfo.HkAcctCustSensitiveInfoVO
     * @author: haoran.zhang
     * @date: 2023/12/20 13:41
     * @since JDK 1.8
     */
    public HkAcctCustSensitiveInfoVO queryHkCustSensitiveInfo(String hkTxAcctNo){
        Assert.notNull(hkTxAcctNo,"香港交易账号不能为空");
        QueryHkCustSensitiveInfoRequest request = new QueryHkCustSensitiveInfoRequest() ;
        request.setHkCustNo(hkTxAcctNo);
        fillBaseInfo(request,"");
        QueryHkCustSensitiveInfoResponse sensitiveInfo=queryHkCustSensitiveInfoFacade.execute(request);
        if(!sensitiveInfo.isSuccess()){
            log.info("根据香港交易账号：{}，查询香港客户[敏感信息]失败，返回信息：{}",hkTxAcctNo, JSON.toJSONString(sensitiveInfo));
            return null;
        }
        HkAcctCustSensitiveInfoVO senstiveVo=new HkAcctCustSensitiveInfoVO();

        //证件号码
        senstiveVo.setIdNo(sensitiveInfo.getIdNo());
        //邮箱
        senstiveVo.setEmail(sensitiveInfo.getEmail());
        //结单邮箱
        senstiveVo.setStatementEmail(sensitiveInfo.getStatementEmail());
        //现住址的中文地址
        senstiveVo.setResidenceCnAddr(sensitiveInfo.getResidenceCnAddr());
        //现住址的英文地址
        senstiveVo.setResidenceEnAddr(sensitiveInfo.getResidenceEnAddr());
        //通讯地址的中文地址
        senstiveVo.setMailingCnAddr(sensitiveInfo.getMailingCnAddr());
        //通讯地址的英文地址
        senstiveVo.setMailingEnAddr(sensitiveInfo.getMailingEnAddr());
        //出生地地址
        senstiveVo.setBirthAddr(sensitiveInfo.getBirthAddr());
        //就业地址
        senstiveVo.setEmplAddr(sensitiveInfo.getEmplAddr());
        return senstiveVo;
    }




    /**
     * @description:(香港账户 是否实名 )
     * @param acctInfo
     * @return boolean
     * @author: haoran.zhang
     * @date: 2023/12/14 13:17
     * @since JDK 1.8
     */
    public boolean isRealName(HkAcctCustDetailInfoVO acctInfo){
        return acctInfo!=null && StringUtil.isNotBlank(acctInfo.getIdNoDigest());
    }

   /**
    * @description:(查询 香港账户中心  客户信息 详情)
    * @param hkTxAcctNo
    * @return com.howbuy.crm.account.client.response.custinfo.HkAcctCustDetailInfoVO
    * @author: haoran.zhang
    * @date: 2023/12/20 13:04
    * @since JDK 1.8
    */
    public HkAcctCustDetailInfoVO  queryHkCustDetailInfo(String hkTxAcctNo){
        Assert.notNull(hkTxAcctNo,"香港交易账号不能为空");
        QueryHkCustBasicInfoRequest request = new QueryHkCustBasicInfoRequest() ;
        // 查询的香港客户状态 不能为[注销], 否则返回为空
        request.setHkCustNo(hkTxAcctNo);
        fillBaseInfo(request,"");

        log.info("QueryHkCustBasicInfoRequest：{}", JSON.toJSONString(request));
        QueryHkCustBasicInfoResponse basicHkInfo=queryHkCustBasicInfoFacade.execute(request);
        if(!basicHkInfo.isSuccess()){
            log.info("根据香港交易账号：{}，查询香港客户[详细信息]失败，返回信息：{}",hkTxAcctNo, JSON.toJSONString(basicHkInfo));
            return null;
        }
        HkAcctCustDetailInfoVO custInfoVo=new HkAcctCustDetailInfoVO();
        custInfoVo.setHkTxAcctNo(basicHkInfo.getHkCustNo());
        custInfoVo.setEbrokerId(basicHkInfo.getEbrokerId());

        custInfoVo.setIdNoDigest(basicHkInfo.getIdNoDigest());
        custInfoVo.setIdNoMask(basicHkInfo.getIdNoMask());
        custInfoVo.setIdType(basicHkInfo.getIdType());

        custInfoVo.setIdSignAreaCode(basicHkInfo.getIdSignAreaCode());

        //将 basicHkInfo 中属性  set到 custInfoVo 中
//                客户中文名
        custInfoVo.setCustChineseName(StringUtils.isBlank(basicHkInfo.getCustChineseName())
                ? basicHkInfo.getCustName() : basicHkInfo.getCustChineseName());
//        客户英文名称
        custInfoVo.setCustEnName(basicHkInfo.getCustEnName());

//        证件有效期
        custInfoVo.setIdValidityEnd(basicHkInfo.getIdValidityEnd());
//
//        证件是否长期有效0-否 1-是
        custInfoVo.setIdAlwaysValidFlag(basicHkInfo.getIdAlwaysValidFlag());
//        证件图片上传状态0-未上传 1-已上传
        custInfoVo.setIdImageUploadStatus(basicHkInfo.getIdImageUploadStatus());
//        国籍
        custInfoVo.setNationality(basicHkInfo.getNationality());
//                出生日期
        custInfoVo.setBirthday(basicHkInfo.getBirthday());
//        性别 0-女，1-男，2-非自然人
        custInfoVo.setGender(basicHkInfo.getGender());
//        职业
        custInfoVo.setVocation(basicHkInfo.getVocation());
//        投资者类型0-机构,1-个人,2-产品户
        custInfoVo.setInvstType(basicHkInfo.getInvstType());
//        手机号码区号
        custInfoVo.setMobileAreaCode(basicHkInfo.getMobileAreaCode());
//                手机号码摘要
        custInfoVo.setMobileDigest(basicHkInfo.getMobileDigest());
//        手机号码掩码
        custInfoVo.setMobileMask(basicHkInfo.getMobileMask());
//        手机验证状态0:未验证 1:已验证
        custInfoVo.setMobileVerifyStatus(basicHkInfo.getMobileVerifyStatus());
//                电子邮箱摘要
        custInfoVo.setEmailDigest(basicHkInfo.getEmailDigest());
//        电子邮箱掩码
        custInfoVo.setEmailMask(basicHkInfo.getEmailMask());
//        邮箱验证状态0:未验证 1:已验证
        custInfoVo.setEmailVerifyStatus(basicHkInfo.getEmailVerifyStatus());
//        客户状态0-正常 1-注销 2-休眠 3-注册 4-开户申请成功
        custInfoVo.setHkCustStatus(basicHkInfo.getCustState());
//        客户登录密码状态0正常状态 1重置状态 2-未设置
        custInfoVo.setCustLoginPasswdType(basicHkInfo.getCustLoginPasswdType());
//        客户交易密码状态0正常状态 1重置状态 2-未设置
        custInfoVo.setCustTxPasswdType(basicHkInfo.getCustTxPasswdType());
//        风险承受能力评估级别0,1,2,3,4,5
        custInfoVo.setRiskToleranceLevel(basicHkInfo.getRiskToleranceLevel());
//        衍生金融工具知识0-无 1-有
        custInfoVo.setDerivativeKnowledge(basicHkInfo.getDerivativeKnowledge());
//        风险评测日期
        custInfoVo.setRiskToleranceDate(basicHkInfo.getRiskToleranceDate());
//                风险评测有效期
        custInfoVo.setRiskToleranceTerm(basicHkInfo.getRiskToleranceTerm());
//        风险评测分数
        custInfoVo.setRiskToleranceScore(basicHkInfo.getRiskToleranceScore());
//                Hbone账号
        custInfoVo.setHboneNo(basicHkInfo.getHboneNo());
//        投资者资质PRO-投资者资质专业/NORMAL-投资者资质普通
        custInfoVo.setInvestorQualification(basicHkInfo.getInvestorQualification());
//        投资者资质认证日期
        custInfoVo.setInvestorQualificationDate(basicHkInfo.getInvestorQualificationDate());
//                现住址国家编码
        custInfoVo.setResidenceCountryCode(basicHkInfo.getResidenceCountryCode());
//        现住址省份编码
        custInfoVo.setResidenceProvCode(basicHkInfo.getResidenceProvCode());
//                现住址城市编码
        custInfoVo.setResidenceCityCode(basicHkInfo.getResidenceCityCode());
//        现住址地区编码
        custInfoVo.setResidenceCountyCode(basicHkInfo.getResidenceCountyCode());
//        现住址的中文地址-摘要
        custInfoVo.setResidenceCnAddrDigest(basicHkInfo.getResidenceCnAddrDigest());
//        现住址的中文地址-掩码
        custInfoVo.setResidenceCnAddrMask(basicHkInfo.getResidenceCnAddrMask());
//        现住址英文国家名称
        custInfoVo.setResidenceEnCountry(basicHkInfo.getResidenceEnCountry());
//                现住址英文省份名称
        custInfoVo.setResidenceEnProv(basicHkInfo.getResidenceEnProv());
//        现住址英文城市名称
        custInfoVo.setResidenceEnCity(basicHkInfo.getResidenceEnCity());
//                现住址英文地区名称
        custInfoVo.setResidenceEnCounty(basicHkInfo.getResidenceEnCounty());
//        现住址城镇名称
        custInfoVo.setResidenceTown(basicHkInfo.getResidenceTown());
//                现住址州县名称
        custInfoVo.setResidenceState(basicHkInfo.getResidenceState());
//        现住址的英文地址-摘要
        custInfoVo.setResidenceEnAddrDigest(basicHkInfo.getResidenceEnAddrDigest());
//        现住址的英文地址-掩码
        custInfoVo.setResidenceEnAddrMask(basicHkInfo.getResidenceEnAddrMask());
//        通讯地址国家编码
        custInfoVo.setMailingCountyCode(basicHkInfo.getMailingCountyCode());
//                通讯地址省份编码
        custInfoVo.setMailingProvCode(basicHkInfo.getMailingProvCode());
//        通讯地址城市编码
        custInfoVo.setMailingCityCode(basicHkInfo.getMailingCityCode());
//                通讯地址地区编码
        custInfoVo.setMailingCountyCode(basicHkInfo.getMailingCountyCode());
//        通讯地址的中文地址-摘要
        custInfoVo.setMailingCnAddrDigest(basicHkInfo.getMailingCnAddrDigest());
//        通讯地址的中文地址-掩码
        custInfoVo.setMailingCnAddrMask(basicHkInfo.getMailingCnAddrMask());
//        通讯地址英文国家名称
        custInfoVo.setMailingEnCountry(basicHkInfo.getMailingEnCountry());
//                通讯地址英文省份名称
        custInfoVo.setMailingEnProv(basicHkInfo.getMailingEnProv());
//        通讯地址英文城市名称
        custInfoVo.setMailingEnCity(basicHkInfo.getMailingEnCity());
//                通讯地址英文地区名称
        custInfoVo.setMailingEnCounty(basicHkInfo.getMailingEnCounty());
//        通讯地址城镇名称
        custInfoVo.setMailingTown(basicHkInfo.getMailingTown());
//                通讯地址州县名称
        custInfoVo.setMailingState(basicHkInfo.getMailingState());
//        通讯地址的英文地址-摘要
        custInfoVo.setMailingEnAddrDigest(basicHkInfo.getMailingEnAddrDigest());
//        通讯地址的英文地址-掩码
        custInfoVo.setMailingEnAddrMask(basicHkInfo.getMailingEnAddrMask());
//        就业状态
        custInfoVo.setEmplStatus(basicHkInfo.getEmplStatus());

//                就业年收入
        custInfoVo.setEmplIncLevel(basicHkInfo.getEmplIncLevel());
        // 资产证明有效期
        custInfoVo.setAssetCertExpiredDate(basicHkInfo.getAssetCertExpiredDate());

        return custInfoVo;

    }



    /**
     * @description:(查询 香港账户中心  客户信息 分页信息)
     * @param reqVo
     * @return com.howbuy.crm.account.client.response.PageVO<com.howbuy.crm.account.client.response.custinfo.HkAcctCustInfoVO>
     * @author: haoran.zhang
     * @date: 2023/12/13 19:33
     * @since JDK 1.8
     */
    public PageVO<HkAcctCustInfoVO> queryHkCustInfoPage(HkAcctCustInfoReqVO reqVo){
        PageVO<HkAcctCustInfoVO> returnPageVo=new PageVO<>();
        QueryHkCustBasicInfoForCounterRequest request=new QueryHkCustBasicInfoForCounterRequest();
        fillBaseInfo(request,"");

        request.setCustCnOrEnName(reqVo.getCustCnOrEnName());
        request.setHkCustNo(reqVo.getHkCustNo());
        request.setHkCustNoList(reqVo.getHkCustNoList());
        request.setCustStatList(reqVo.getCustStatList());
        request.setEbrokerId(reqVo.getEbrokerId());
        request.setHboneNo(reqVo.getHboneNo());
        request.setInvstType(reqVo.getInvstType());

        request.setEmailDigest(reqVo.getEmailDigest());


        request.setIdNoDigest(reqVo.getIdNoDigest());
        request.setIdType(reqVo.getIdType());
        request.setIdSignAreaCode(reqVo.getIdSignAreaCode());

        request.setMobileAreaCode(reqVo.getMobileAreaCode());
        request.setMobileDigest(reqVo.getMobileDigest());
        request.setOpenType(reqVo.getOpenType());
        request.setOpenDateStart(reqVo.getOpenDateStart());
        request.setOpenDateEnd(reqVo.getOpenDateEnd());
        //分页参数
        request.setPageNo(reqVo.getPage());
        request.setPageSize(reqVo.getRows());

        QueryHkCustBasicInfoForCounterResponse resp=hkCustBasicInfoFacade.execute(request);
        if(!resp.isSuccess()  || CollectionUtils.isEmpty(resp.getCustBasicInfoDTOList()) ){
            return returnPageVo;
        }
        List<HkAcctCustInfoVO> returnList=Lists.newArrayList();
        resp.getCustBasicInfoDTOList().forEach(basicHkInfo->{
            HkAcctCustInfoVO custInfoVo=null;
            if(basicHkInfo!=null){
                custInfoVo=new HkAcctCustInfoVO();
                custInfoVo.setHkTxAcctNo(basicHkInfo.getHkCustNo());
                custInfoVo.setEbrokerId(basicHkInfo.getEbrokerId());
                custInfoVo.setBirthDt(basicHkInfo.getBirthday());
                //HkCustStatusEnum.getEnum(basicHkInfo.getCustState())
                custInfoVo.setHkCustStatus(basicHkInfo.getCustState());
                custInfoVo.setCustChineseName(basicHkInfo.getCustChineseName());
                custInfoVo.setCustEnName(basicHkInfo.getCustEnName());

                custInfoVo.setIdNoDigest(basicHkInfo.getIdNoDigest());
                custInfoVo.setIdNoMask(basicHkInfo.getIdNoMask());
                custInfoVo.setIdType(basicHkInfo.getIdType());
                custInfoVo.setIdSignAreaCode(basicHkInfo.getIdSignAreaCode());

                custInfoVo.setInvstType(basicHkInfo.getInvstType());

                custInfoVo.setMobileAreaCode(basicHkInfo.getMobileAreaCode());
                custInfoVo.setMobileDigest(basicHkInfo.getMobileDigest());
                custInfoVo.setMobileMask(basicHkInfo.getMobileMask());
                custInfoVo.setMobileVerifyStatus(basicHkInfo.getMobileVerifyStatus());

                custInfoVo.setEmailDigest(basicHkInfo.getEmailDigest());
                custInfoVo.setEmailMask(basicHkInfo.getEmailMask());
                custInfoVo.setEmailVerifyStatus(basicHkInfo.getEmailVerifyStatus());
                custInfoVo.setOpenType(basicHkInfo.getOpenType());
                custInfoVo.setOpenDate(basicHkInfo.getOpenDate());
                custInfoVo.setHboneNo(basicHkInfo.getHboneNo());
            }
            returnList.add(custInfoVo);
        });
        //设置 返回 list
        returnPageVo.setRows(returnList);
        returnPageVo.setTotal(resp.getTotalCount());
        returnPageVo.setPage(Long.valueOf(resp.getPageNo()).intValue());
        //参数传递的 size
        returnPageVo.setSize(reqVo.getRows());

        return returnPageVo;

    }

    /**
     * @description: 根据香港交易账号查询香港客户风险测评问卷流水
     * @param hkTxAcctNo 香港客户号
     * @param startDate	开始时间
     * @param endDate 结束时间
     * @return java.util.List<com.howbuy.crm.account.client.response.custinfo.HkKycAnswerVO>
     * @author: jin.wang03
     * @date: 2023/12/29 13:34
     * @since JDK 1.8
     */
    public List<HkKycAnswerVO>  queryHkKycAnswerListByHkTxAcctNo(String hkTxAcctNo, String startDate, String endDate) {

        QueryHkKycAnswerListRequest queryHkKycAnswerListRequest = new QueryHkKycAnswerListRequest();
        queryHkKycAnswerListRequest.setHkCustNo(hkTxAcctNo);
        queryHkKycAnswerListRequest.setStartDate(startDate);
        queryHkKycAnswerListRequest.setEndDate(endDate);
        QueryHkKycAnswerListResponse response = queryHkKycAnswerListFacade.execute(queryHkKycAnswerListRequest);
        if(response == null || !response.isSuccess()){
            log.info("根据香港交易账号：{}，查询香港客户[风险测评问卷流水信息]失败，返回信息：{}",hkTxAcctNo, JSON.toJSONString(response));
            return Lists.newArrayList();
        }

        List<KycAnswerDTO> kycAnswerDTOList = response.getKycAnswerDTOList();
        if (CollectionUtils.isEmpty(kycAnswerDTOList)) {
            return Lists.newArrayList();
        }

        return kycAnswerDTOList.stream().map(kycAnswerDTO -> {
            HkKycAnswerVO hkKycAnswerVO = new HkKycAnswerVO();
            hkKycAnswerVO.setScore(kycAnswerDTO.getScore());
            hkKycAnswerVO.setLevelValue(kycAnswerDTO.getLevelValue());
            hkKycAnswerVO.setDerivativeKnowledge(kycAnswerDTO.getDerivativeKnowledge());
            hkKycAnswerVO.setRiskToleranceDate(kycAnswerDTO.getRiskToleranceDate());
            hkKycAnswerVO.setRiskToleranceTerm(kycAnswerDTO.getRiskToleranceTerm());
            hkKycAnswerVO.setStimestamp(kycAnswerDTO.getStimestamp());
            hkKycAnswerVO.setOutletCode(kycAnswerDTO.getTradeChan());
            hkKycAnswerVO.setOutletCodeName(HkKycTradeChanCodeEnum.getDescription(kycAnswerDTO.getTradeChan()));

            //赋值 ： 超出风险评测结果的基金列表信息
            if(CollectionUtils.isNotEmpty(kycAnswerDTO.getMismatchFundList())){
                hkKycAnswerVO.setMismatchFundList(
                        kycAnswerDTO.getMismatchFundList().stream().map(mismatchFundDTO -> {
                    HkKycAnswerVO.HkMismatchFundVO mismatchVo = new HkKycAnswerVO.HkMismatchFundVO();
                            mismatchVo.setFundCode(mismatchFundDTO.getFundCode());
                            mismatchVo.setFundName(mismatchFundDTO.getFundName());
                            mismatchVo.setFundRiskLevel(mismatchFundDTO.getFundRiskLevel());
                    return mismatchVo;
                }).collect(Collectors.toList()));
            }
            return hkKycAnswerVO;
        }).collect(Collectors.toList());
    }


    /**
     * @description: 根据香港交易账号查询香港客户银行卡信息
     * @param hkTxAcctNo 香港客户号
     * @return java.util.List<com.howbuy.crm.account.client.response.bankcardinfo.HkBankCardInfoVO> 香港客户银行卡信息列表
     * @author: jin.wang03
     * @date: 2024/1/11 14:37
     * @since JDK 1.8
     */
    public List<HkBankCardInfoVO> queryHkCustBankCardList(String hkTxAcctNo) {
        Assert.notNull(hkTxAcctNo, "香港交易账号不能为空");

        List<HkBankCardInfoVO> resultList = new ArrayList<>();

        QueryHkCustBankCardListRequest request = new QueryHkCustBankCardListRequest();
        request.setHkCustNo(hkTxAcctNo);
        fillBaseInfo(request, "");
        QueryHkCustBankCardListResponse response = queryHkCustBankCardListFacade.execute(request);
        if (response == null || !response.isSuccess()) {
            log.info("根据香港交易账号：{}，查询香港客户[银行卡信息]失败，返回信息：{}", hkTxAcctNo, JSON.toJSONString(response));
            return resultList;
        }

        List<BankCardInfoDTO> bankCardInfoDTOList = response.getBankCardInfoDTOList();

        if (CollectionUtils.isNotEmpty(bankCardInfoDTOList)) {
            for (BankCardInfoDTO bankCardInfoDTO : bankCardInfoDTOList) {
                HkBankCardInfoVO hkBankCardInfoVO = new HkBankCardInfoVO();
                BeanUtils.copyProperties(bankCardInfoDTO, hkBankCardInfoVO);
                resultList.add(hkBankCardInfoVO);
            }
        }

        return resultList;
    }

    /**
     * @description: 根据香港交易账号查询香港客户[所有信息]
     * @param hkTxAcctNo 香港客户号
     * @return com.howbuy.crm.account.client.response.custinfo.HkAcctCustInfoHolderVO 香港客户[所有信息]持有者对象，包含客户基本信息、税务信息、声明信息、银行卡信息、kyc信息、一账通关系信息等等
     * @author: jin.wang03
     * @date: 2024/1/11 15:51
     * @since JDK 1.8
     */
    public HkAcctCustInfoHolderVO queryHkCustAllInfoByHkTxAcctNo(String hkTxAcctNo) {
        Assert.notNull(hkTxAcctNo, "香港交易账号不能为空");

        QueryHkAllCustInfoForCounterRequest queryHkAllCustInfoForCounterRequest = new QueryHkAllCustInfoForCounterRequest();
        queryHkAllCustInfoForCounterRequest.setHkCustNo(hkTxAcctNo);
        fillBaseInfo(queryHkAllCustInfoForCounterRequest, "");
        QueryHkAllCustInfoForCounterResponse response = queryHkAllCustInfoForCounterFacade.execute(queryHkAllCustInfoForCounterRequest);
        if (response == null || !response.isSuccess()) {
            log.info("根据香港交易账号：{}，查询香港客户[所有信息]失败，返回信息：{}", hkTxAcctNo, JSON.toJSONString(response));
            return null;
        }

        HkAcctCustInfoHolderVO hkAcctCustInfoHolderVO = new HkAcctCustInfoHolderVO();

        // 香港客户基本信息（全量）
        HkAcctCustFullInfoVO hkAcctCustFullInfoVO = new HkAcctCustFullInfoVO();
        if (response.getCustInfoDTO() != null) {
            BeanUtils.copyProperties(response.getCustInfoDTO(), hkAcctCustFullInfoVO);
        }
        hkAcctCustInfoHolderVO.setCustInfoVO(hkAcctCustFullInfoVO);

        // 香港客户税务信息
        HkTaxInfoVO hkTaxInfoVO = new HkTaxInfoVO();
        if (response.getQueryIndiTaxInfoDTO() != null) {
            BeanUtils.copyProperties(response.getQueryIndiTaxInfoDTO(), hkTaxInfoVO);
        }
        hkAcctCustInfoHolderVO.setQueryIndiTaxInfoVO(hkTaxInfoVO);

        // 香港客户声明信息列表
        List<HkDeclarationInfoVO> hkTaxDeclareVOList = new ArrayList<>();
        if (CollectionUtils.isNotEmpty(response.getQueryDeclarationInfoDTOList())) {
            for (QueryDeclarationInfoDTO queryIndiTaxDeclareDTO : response.getQueryDeclarationInfoDTOList()) {
                HkDeclarationInfoVO hkDeclarationInfoVO = new HkDeclarationInfoVO();
                BeanUtils.copyProperties(queryIndiTaxDeclareDTO, hkDeclarationInfoVO);
                hkTaxDeclareVOList.add(hkDeclarationInfoVO);
            }
        }
        hkAcctCustInfoHolderVO.setQueryDeclarationInfoVOList(hkTaxDeclareVOList);

        // 香港客户银行卡信息列表
        List<HkBankCardInfoVO> hkBankCardInfoVOList = new ArrayList<>();
        if (CollectionUtils.isNotEmpty(response.getBankCardInfoDTOList())) {
            for (BankCardInfoDTO bankCardInfoDTO : response.getBankCardInfoDTOList()) {
                HkBankCardInfoVO hkBankCardInfoVO = new HkBankCardInfoVO();
                BeanUtils.copyProperties(bankCardInfoDTO, hkBankCardInfoVO);
                hkBankCardInfoVOList.add(hkBankCardInfoVO);
            }
        }
        hkAcctCustInfoHolderVO.setBankCardInfoVOList(hkBankCardInfoVOList);

        // 香港客户kyc信息
        HkKycInfoVO hkKycInfoVO = new HkKycInfoVO();
        if (response.getKycInfoDTO() != null) {
            BeanUtils.copyProperties(response.getKycInfoDTO(), hkKycInfoVO);
        }
        hkAcctCustInfoHolderVO.setKycInfoVO(hkKycInfoVO);

        // 香港客户一账通关系
        HkCustHboneRelVO hkAcctCustRelationVO = new HkCustHboneRelVO();
        if (response.getHkCustHboneRelDTO() != null) {
            BeanUtils.copyProperties(response.getHkCustHboneRelDTO(), hkAcctCustRelationVO);
        }
        hkAcctCustInfoHolderVO.setHkCustHboneRelVO(hkAcctCustRelationVO);

        return hkAcctCustInfoHolderVO;
    }


    /**
     * @description: 根据香港交易账号查询香港客户[开户结果]
     * @param hkTxAcctNo 香港客户号
     * @return com.howbuy.crm.account.client.response.custinfo.HkOpenAcctResultVO 香港客户[开户结果]对象，包含开户拒绝信息、开户审核状态、扩展信息等等
     * @author: jin.wang03
     * @date: 2024/1/12 14:22
     * @since JDK 1.8
     */
    public HkOpenAcctResultVO queryHkOpenAcctResultByHkTxAcctNo(String hkTxAcctNo) {
        Assert.notNull(hkTxAcctNo, "香港交易账号不能为空");

        QueryHkOpenAcctResultRequest request = new QueryHkOpenAcctResultRequest();
        request.setHkCustNo(hkTxAcctNo);
        fillBaseInfo(request, "");
        QueryHkOpenAcctResultResponse response = queryHkOpenAcctResultFacade.execute(request);
        if (response == null || !response.isSuccess()) {
            log.info("根据香港交易账号：{}，查询香港客户[开户结果]失败，返回信息：{}", hkTxAcctNo, JSON.toJSONString(response));
            return null;
        }
        log.info("根据香港交易账号：{}，查询香港客户[开户结果]成功，返回信息：{}", hkTxAcctNo, JSON.toJSONString(response));
        HkOpenAcctResultVO hkOpenAcctResultVO = new HkOpenAcctResultVO();

        hkOpenAcctResultVO.setTxChkFlag(response.getTxChkFlag());
        hkOpenAcctResultVO.setExtInfo(response.getExtInfo());
        HkOpenAcctRejectVO hkOpenAcctRejectVO = new HkOpenAcctRejectVO();
        if (response.getOpenAcctRejectDTO() != null) {
            BeanUtils.copyProperties(response.getOpenAcctRejectDTO(), hkOpenAcctRejectVO);
        }
        hkOpenAcctResultVO.setOpenAcctRejectVO(hkOpenAcctRejectVO);

        return hkOpenAcctResultVO;
    }

    /**
     * @description: 根据条件分页查询香港客户[入金结果]，查询条件HkDepositRequest可继续扩充
     * @param hkDepositRequest 香港客户[入金结果]请求对象，包含香港客户号、入金流水号、入金日期等等
     * @return java.util.List<com.howbuy.crm.account.client.response.deposit.HkDepositResultVO> 香港客户[入金结果]列表
     * @author: jin.wang03
     * @date: 2024/1/15 10:10
     * @since JDK 1.8
     */
    public PageVO<HkDepositResultVO> queryHkDepositResult(HkDepositRequest hkDepositRequest) {
        PageVO<HkDepositResultVO> returnPageVo = new PageVO<>();

        QueryHkDepositResultRequest queryHkDepositResultRequest = new QueryHkDepositResultRequest();
        queryHkDepositResultRequest.setHkCustNo(hkDepositRequest.getHkCustNo());
        queryHkDepositResultRequest.setPageNo(hkDepositRequest.getPage());
        queryHkDepositResultRequest.setPageSize(hkDepositRequest.getRows());
        QueryHkDepositResultResponse response = queryHkDepositResultFacade.execute(queryHkDepositResultRequest);
        if (response == null || !response.isSuccess()) {
            log.info("根据香港交易账号：{}，查询香港客户[入金结果]失败，返回信息：{}", hkDepositRequest.getHkCustNo(), JSON.toJSONString(response));
            return returnPageVo;
        }

        log.info("根据香港交易账号：{}，查询香港客户[入金结果]成功，返回信息：{}", hkDepositRequest.getHkCustNo(), JSON.toJSONString(response));
        List<HkDepositResultVO> hkDepositResultVOS = new ArrayList<>();
        if (CollectionUtils.isNotEmpty(response.getDepositResultList())) {
            for (DepositResultDTO hkDepositResultDTO : response.getDepositResultList()) {
                HkDepositResultVO hkDepositResultVO = new HkDepositResultVO();
                BeanUtils.copyProperties(hkDepositResultDTO, hkDepositResultVO);

                HkDepositRejectVO hkDepositRejectVO = new HkDepositRejectVO();
                if (hkDepositResultDTO.getDepositRejectDTO() != null) {
                    BeanUtils.copyProperties(hkDepositResultDTO.getDepositRejectDTO(), hkDepositRejectVO);
                }
                hkDepositResultVO.setDepositRejectVO(hkDepositRejectVO);
                hkDepositResultVOS.add(hkDepositResultVO);
            }
        }

        //设置 返回 list
        returnPageVo.setRows(hkDepositResultVOS);
        returnPageVo.setTotal(response.getTotalCount());

        returnPageVo.setPage(Long.valueOf(response.getPageNo()).intValue());
        //参数传递的 size
        returnPageVo.setSize(hkDepositRequest.getRows());

        return returnPageVo;
    }

}