/**
 * Copyright (c) 2024, <PERSON><PERSON><PERSON><PERSON> HOWBUY INVESTMENT MANAGEMENT Co., Ltd.
 * All right reserved.
 * <p>
 * THIS IS UNPUBLISHED PROPRIETARY SOURCE CODE OF HOWBUY INVESTMENT
 * MANAGEMENT CO., LTD.  THE CONTENTS OF THIS FILE MAY NOT BE DISCLOSED
 * TO THIRD PARTIES, COPIED OR DUPLICATED IN ANY FORM, IN WHOLE OR IN PART,
 * WITHOUT THE PRIOR WRITTEN PERMISSION OF HOWBUY INVESTMENT MANAGEMENT
 * CO., LTD.
 */
package com.howbuy.crm.account.service.business.beisen;


import com.alibaba.fastjson.JSON;
import com.howbuy.crm.account.client.response.dictionary.CityListVO;
import com.howbuy.crm.account.dao.po.beisen.CmBeisenOrgDO;
import com.howbuy.crm.account.dao.po.beisen.CmBeisenUserInfoPO;
import com.howbuy.crm.account.dao.po.beisenorg.CmBeisenOrgConfigPO;
import com.howbuy.crm.account.dao.po.beisenposlevel.CmBeisenPosLevelConfigPO;
import com.howbuy.crm.account.dao.po.constant.HbConstantPO;
import com.howbuy.crm.account.dao.po.consultantexp.CmConsultantExpPO;
import com.howbuy.crm.account.dao.po.consultantexp.CmConsultantExpModifyFlagPO;
import com.howbuy.crm.account.dao.po.consultantexp.CmConsultantExpZjSalPO;
import com.howbuy.crm.account.service.bo.beisen.AreaBO;
import com.howbuy.crm.account.service.bo.beisen.CmConcultantExpBeisenBO;
import com.howbuy.crm.account.service.commom.constant.Constants;
import com.howbuy.crm.account.service.outerservice.dictionary.DictionaryOuterService;
import com.howbuy.crm.account.service.repository.beisen.CmSyncBeisenRepository;
import com.howbuy.crm.account.service.business.beisen.calcenum.CrmBeisenMappingEnum;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import java.time.LocalDate;
import java.time.format.DateTimeFormatter;
import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * @description: (请在此添加描述)
 * <AUTHOR>
 * @date 2024/11/14 17:51
 * @since JDK 1.8
 */
@Slf4j
@Service
public class CmSyncBeisenServiceBusiness {
    @Autowired
    private DictionaryOuterService dictionaryOuterService;
    @Autowired
    private CmSyncBeisenRepository cmSyncBeisenRepository;

    /**
     * 北森离职状态-生效
     */
    private static final String QUIT_STATUS_EFFECTIVE = "生效";
    /**
     * 员工离职状态-离职
     */
    private static final String WORK_TYPE_RESIGN = "2";
    /**
     * 左括号
     */
    public static final String LEFT_KEY = "(";
    /**
     * 左括号转义
     */
    public static final String LEFT_TRANS_KEY = "\\(";
    /**
     * 右括号
     */
    public static final String RIGHT_KEY = ")";
    /**
     * 右括号转义
     */
    public static final String RIGHT_TRANS_KEY = "\\)";
    /**
     * 日期格式化
     */
    public static final String DATE_FORMAT_PATTERN= "yyyy-MM-dd";

    /**
     * @description:(通过工号查询花名册数据)
     * @param userNos
     * @return java.util.List<com.howbuy.crm.account.dao.po.consultantexp.CmConsultantExp>
     * @author: shijie.wang
     * @date: 2024/11/15 9:52
     * @since JDK 1.8
     */
    public List<CmConsultantExpPO> listCmConsultantExpByUserNos(List<String> userNos) {
        return cmSyncBeisenRepository.listCmConsultantExpByUserNo(userNos);
    }

    /**
     * @description:(同步北森数据-北森数据已更新)
     * @param expList
     * @param beisenUserInfoList
     * @return java.util.List<java.lang.String>
     * @author: shijie.wang
     * @date: 2024/11/14 10:45
     * @since JDK 1.8
     */
    public List<String> syncCmConsultantExpToCmBeisen(List<CmConsultantExpPO> expList, List<CmBeisenUserInfoPO> beisenUserInfoList) {
        List<String> errorUserIdList = new ArrayList<>();
        try{
            CmConcultantExpBeisenBO bo = new CmConcultantExpBeisenBO();
            List<HbConstantPO> hrPositionsLevelList = cmSyncBeisenRepository.listByTypeCode();
            bo.setHrPositionsLevelMap(hrPositionsLevelList.stream().collect(Collectors.toMap(HbConstantPO::getConstcode, Function.identity())));
            //获得所有有效职级薪资数据
            bo.setZjSalMap(getMapByZjSal());
            //北森架构map（key：架构名称，value：架构ID）
            bo.setBeisenOrgDOMap(getBeisenOrgDOMap());
            //crm和北森架构映射map(key: 北森架构ID，value：映射集合)
            bo.setCmBeisenOrgConfigMap(getCmBeisenOrgConfigMap());
            //crm和北森职级映射map(key: 北森职级编码，value：映射集合)
            bo.setCmBeisenPosLevelConfigPOMap(getCmBeisenPosLevelConfigPOMap());
            errorUserIdList = syncBeisenDataByExp(expList, bo, beisenUserInfoList);
        }catch (Exception e){
            log.error("syncCmConsultantExpToCmBeisen| is error: ", e);
        }
        log.info("syncCmConsultantExpToCmBeisen| errorUserIdList:{} ", JSON.toJSONString(errorUserIdList));
        return errorUserIdList;
    }

    /**
     * @description:(通过userno同步北森数据)
     * @param expList
     * @return void
     * @author: shijie.wang
     * @date: 2024/11/6 17:33
     * @since JDK 1.8
     */
    public List<String> syncCmConsultantExpToCmBeisenByUserNos(List<CmConsultantExpPO> expList) {
        List<CmBeisenUserInfoPO> beisenUserInfoList = getBeisenUserInfo(null, null);
        return syncCmConsultantExpToCmBeisen(expList, beisenUserInfoList);
    }

    /**
     * @description:(通过userno同步北森数据)
     * @param expList
     * @return void
     * @author: shijie.wang
     * @date: 2024/11/6 17:33
     * @since JDK 1.8
     */
    public List<String> syncCmConsultantExpToCmBeisen(List<CmConsultantExpPO> expList) {
        List<CmBeisenUserInfoPO> beisenUserInfoList = getBeisenUserInfoByModdt(LocalDate.now().minusDays(1L).format(DateTimeFormatter.ofPattern(DATE_FORMAT_PATTERN)));
        return syncCmConsultantExpToCmBeisen(expList, beisenUserInfoList);
    }

    /**
     * @description:(通过时间段同步北森数据)
     * @param startDate
     * @param endDate
     * @return void
     * @author: shijie.wang
     * @date: 2024/11/6 17:33
     * @since JDK 1.8
     */
    public List<String> syncCmConsultantExpToCmBeisenByDateTime(String startDate, String endDate) {
        List<CmConsultantExpPO> expList = cmSyncBeisenRepository.listCmConsultantExpByUserNo(new ArrayList<>());
        if(CollectionUtils.isEmpty(expList)){
            log.info("syncCmConsultantExpToCmBeisen| expList is null");
            return Collections.emptyList();
        }
        List<CmBeisenUserInfoPO> beisenUserInfoList = getBeisenUserInfo(startDate, endDate);
        if(CollectionUtils.isEmpty(beisenUserInfoList)){
            log.info("syncCmConsultantExpToCmBeisenByDateTime| startDate：{}, endDate:{}, 时间段没有可同步的北森数据", startDate, endDate);
            return Collections.emptyList();
        }
        return syncCmConsultantExpToCmBeisen(expList, beisenUserInfoList);
    }

    /**
     * @description:(执行调度任务的北森同步花名册数据)
     * @param
     * @return void
     * @author: shijie.wang
     * @date: 2024/11/14 17:20
     * @since JDK 1.8
     */
    public List<String> execSyncBeisenToCrmByJob(){
        List<CmConsultantExpPO> expList = cmSyncBeisenRepository.listCmConsultantExpByUserNo(new ArrayList<>());
        return syncCmConsultantExpToCmBeisen(expList);
    }

    /**
     * @description:(获得所有有效职级薪资数据)
     * @param
     * @return java.util.Map<java.lang.String,java.util.List<com.howbuy.crm.hb.domain.system.CmConsultantExpZjSal>>
     * @author: shijie.wang
     * @date: 2024/10/31 14:44
     * @since JDK 1.8
     */
    private Map<String, List<CmConsultantExpZjSalPO>> getMapByZjSal(){
        List<CmConsultantExpZjSalPO> salList = cmSyncBeisenRepository.selectAllData();
        if(CollectionUtils.isEmpty(salList)){
            log.info("getMapByZjSal| salList is null");
            return Collections.emptyMap();
        }
        return salList.stream().collect(Collectors.groupingBy(CmConsultantExpZjSalPO::getRanklevel));
    }

    /**
     * @description:(获取北森组织机构所有数据 - 北森架构map（key：架构名称，value：架构ID）)
     * @param
     * @return java.util.Map<java.lang.String,java.lang.String>
     * @author: shijie.wang
     * @date: 2024/11/4 9:52
     * @since JDK 1.8
     */
    private Map<String, String> getBeisenOrgDOMap(){
        List<CmBeisenOrgDO> beisenOrgList = cmSyncBeisenRepository.queryAllCmBeisenOrgData();
        if(CollectionUtils.isEmpty(beisenOrgList)){
            log.info("getBeisenOrgDOMap| beisenOrgDOList is null");
            return Collections.emptyMap();
        }
        return beisenOrgList.stream().collect(Collectors.toMap(CmBeisenOrgDO::getOrgNameBeisen, CmBeisenOrgDO::getOrgIdBeisen));
    }

    /**
     * @description:(crm和北森架构映射map(key: 北森架构ID，value：映射集合))
     * @param
     * @return java.util.Map<java.lang.String,com.howbuy.crm.hb.domain.beisen.CmBeisenOrgConfigPO>
     * @author: shijie.wang
     * @date: 2024/11/4 10:30
     * @since JDK 1.8
     */
    private Map<String, CmBeisenOrgConfigPO> getCmBeisenOrgConfigMap(){
        List<CmBeisenOrgConfigPO> beisenOrgConfigList = cmSyncBeisenRepository.queryAllCmBeisenOrgConfigData();
        if(CollectionUtils.isEmpty(beisenOrgConfigList)){
            log.info("getBeisenOrgDOMap| beisenOrgConfigList is null");
            return Collections.emptyMap();
        }
        return beisenOrgConfigList.stream().collect(Collectors.toMap(CmBeisenOrgConfigPO::getOrgIdBeisen, Function.identity()));
    }

    /**
     * @description:(crm和北森职级映射map(key: 北森职级编码，value：映射集合))
     * @param
     * @return java.util.Map<java.lang.String,com.howbuy.crm.hb.domain.beisen.CmBeisenPosLevelConfigPO>
     * @author: shijie.wang
     * @date: 2024/11/4 10:49
     * @since JDK 1.8
     */
    private Map<String, CmBeisenPosLevelConfigPO> getCmBeisenPosLevelConfigPOMap(){
        List<CmBeisenPosLevelConfigPO> beisenPosLevelConfigList = cmSyncBeisenRepository.queryCmBeisenPosLevelConfigData();
        if(CollectionUtils.isEmpty(beisenPosLevelConfigList)){
            log.info("getCmBeisenPosLevelConfigPOMap| beisenPosLevelConfigList is null");
            return Collections.emptyMap();
        }
        return beisenPosLevelConfigList.stream().collect(Collectors.toMap(CmBeisenPosLevelConfigPO::getPositionsLevelBeisen, Function.identity()));
    }


    /**
     * @description:(获取人工修改标志所有数据)
     * @param
     * @return java.util.Map<java.lang.String,com.howbuy.crm.hb.domain.system.CmConsultantExpModifyFlag>
     * @author: shijie.wang
     * @date: 2024/10/31 20:10
     * @since JDK 1.8
     */
    private Map<String, CmConsultantExpModifyFlagPO> getCmConsultantExpModifyFlagMap(){
        List<CmConsultantExpModifyFlagPO> modifyFlagList = cmSyncBeisenRepository.selectAll();
        if(CollectionUtils.isEmpty(modifyFlagList)){
            return Collections.emptyMap();
        }
        return modifyFlagList.stream().filter(po -> StringUtils.isNotEmpty(po.getUserid())).collect(Collectors.toMap(CmConsultantExpModifyFlagPO::getUserid, Function.identity()));
    }

    /**
     * @description:(获取北森用户数据)
     * @param startDate
     * @param endDate
     * @return java.util.List<com.howbuy.crm.hb.domain.beisen.CmBeisenUserInfoPO>
     * @author: shijie.wang
     * @date: 2024/11/5 18:25
     * @since JDK 1.8
     */
    private List<CmBeisenUserInfoPO> getBeisenUserInfo(String startDate, String endDate){
        return cmSyncBeisenRepository.getBeisenUserInfo(startDate, endDate);
    }

    /**
     * @description:(通过修改日期查询北森用户信息)
     * @param startDate
     * @return java.util.List<com.howbuy.crm.account.dao.po.beisen.CmBeisenUserInfoPO>
     * @author: shijie.wang
     * @date: 2024/11/26 9:34
     * @since JDK 1.8
     */
    private List<CmBeisenUserInfoPO> getBeisenUserInfoByModdt(String startDate){
        return cmSyncBeisenRepository.getBeisenUserInfoByModdt(startDate);
    }

    /**
     * @description:(根据北森ID或用户ID获得北森用户信息)
     * @param beisenid
     * @param userNo
     * @param beisenIdMap
     * @param userNoMap
     * @return com.howbuy.crm.hb.domain.beisen.CmBeisenUserInfoPO
     * @author: shijie.wang
     * @date: 2024/11/4 13:04
     * @since JDK 1.8
     */
    private CmBeisenUserInfoPO getBeisenUserInfo(String beisenid, String userNo, Map<String, CmBeisenUserInfoPO> beisenIdMap, Map<String, CmBeisenUserInfoPO> userNoMap){
        if(StringUtils.isEmpty(beisenid) && StringUtils.isEmpty(userNo)){
            return null;
        }
        return beisenIdMap.getOrDefault(beisenid, userNoMap.get(userNo));
    }

    /**
     * @description:(根北森用户数据（key: beisenId, value: 映射值）)
     * @param beisenUserInfoList
     * @return java.util.Map<java.lang.String,com.howbuy.crm.hb.domain.beisen.CmBeisenUserInfoPO>
     * @author: shijie.wang
     * @date: 2024/11/4 11:22
     * @since JDK 1.8
     */
    private Map<String, CmBeisenUserInfoPO> getBeisenUserInfoByBeisenId(List<CmBeisenUserInfoPO> beisenUserInfoList){
        if(CollectionUtils.isEmpty(beisenUserInfoList)){
            return Collections.emptyMap();
        }
        return beisenUserInfoList.stream().filter(info -> StringUtils.isNotEmpty(info.getExt3())).collect(Collectors.toMap(CmBeisenUserInfoPO::getExt3, Function.identity()));
    }

    /**
     * @description:(根北森用户数据（key: userId, value: 映射值）)
     * @param beisenUserInfoList
     * @return java.util.Map<java.lang.String,com.howbuy.crm.hb.domain.beisen.CmBeisenUserInfoPO>
     * @author: shijie.wang
     * @date: 2024/11/4 11:22
     * @since JDK 1.8
     */
    private Map<String, CmBeisenUserInfoPO> getBeisenUserInfoByUserNo(List<CmBeisenUserInfoPO> beisenUserInfoList){
        if(CollectionUtils.isEmpty(beisenUserInfoList)){
            return Collections.emptyMap();
        }
        return beisenUserInfoList.stream().collect(Collectors.toMap(CmBeisenUserInfoPO::getExt2, Function.identity()));
    }

    /**
     * @description:(北森数据对象Map（key: 邮箱，value：映射值）)
     * @param beisenUserInfoList
     * @return java.util.Map<java.lang.String,com.howbuy.crm.account.dao.po.beisen.CmBeisenUserInfoPO>
     * @author: shijie.wang
     * @date: 2024/11/25 15:03
     * @since JDK 1.8
     */
    private Map<String, CmBeisenUserInfoPO> getBeisenUserInfoByEmail(List<CmBeisenUserInfoPO> beisenUserInfoList){
        if(CollectionUtils.isEmpty(beisenUserInfoList)){
            return Collections.emptyMap();
        }
        return beisenUserInfoList.stream().filter(po -> StringUtils.isNotEmpty(po.getExt6())).
                collect(Collectors.toMap(CmBeisenUserInfoPO::getExt6, Function.identity()));
    }

    /**
     * @description:(保存北森同步花名册数据和北森同步花名册同步标识数据)
     * @param saveExpList
     * @param saveModifyFlagList
     * @return void
     * @author: shijie.wang
     * @date: 2024/11/4 17:10
     * @since JDK 1.8
     */
    private void save(List<CmConsultantExpPO> saveExpList, List<CmConsultantExpModifyFlagPO> saveModifyFlagList){
        cmSyncBeisenRepository.save(saveExpList, saveModifyFlagList);
    }

    /**
     * @description:(北森同步花名册数据计算逻辑)
     * @param expList
     * @param bo
     * @param beisenUserInfoList
     * @return java.util.List<java.lang.String>
     * @author: shijie.wang
     * @date: 2024/11/25 14:46
     * @since JDK 1.8
     */
    private List<String> syncBeisenDataByExp(List<CmConsultantExpPO> expList, CmConcultantExpBeisenBO bo, List<CmBeisenUserInfoPO> beisenUserInfoList){
        //返回的异常人员
        List<String> errorUserIdList = new ArrayList<>();
        //获取人工修改标志所有数据（key: userid，value：映射值）
        Map<String, CmConsultantExpModifyFlagPO> modifyFlagMap = getCmConsultantExpModifyFlagMap();
        //北森用户数据（key: beisenId, value: 映射值）
        Map<String, CmBeisenUserInfoPO> beisenIdMap = getBeisenUserInfoByBeisenId(beisenUserInfoList);
        //北森用户数据（key: userNo, value: 映射值）
        Map<String, CmBeisenUserInfoPO> userNoMap = getBeisenUserInfoByUserNo(beisenUserInfoList);
        //北森数据对象Map（key: 姓名，value：映射值）
        bo.setCmBeisenUserInfoByEmailMap(getBeisenUserInfoByEmail(beisenUserInfoList));
        //key:市名称， value：市code,省code
        Map<String, AreaBO> cityMapByProv = new HashMap<>();
        //key:区名称， value: 区code, 市code, 省code
        Map<String, AreaBO> countryMapByCity = new HashMap<>();
        transCityCodeMap(cityMapByProv, countryMapByCity);
        bo.setCityMapByProv(cityMapByProv);
        bo.setCountryMapByCity(countryMapByCity);
        for(CmConsultantExpPO exp:expList){
            try{
                bo.setExp(exp);
                CmBeisenUserInfoPO userInfo = getBeisenUserInfo(exp.getBeisenid(), exp.getUserno(), beisenIdMap, userNoMap);
                if(Objects.isNull(userInfo)){
                    log.info("syncCmConsultantExpToCmBeisen| 未找到北森用户数据不更新， beisenId:{}, userId:{}", exp.getBeisenid(), exp.getUserid());
                    continue;
                }
                //花名册员工状态为离职，北森离职状态为生效不同步数据
                if(WORK_TYPE_RESIGN.equals(exp.getWorktype()) && QUIT_STATUS_EFFECTIVE.equals(userInfo.getExt25())){
                    log.info("syncCmConsultantExpToCmBeisen| 花名册员工状态为离职，北森离职状态为生效不同步数据， beisenId:{}, userId:{}", exp.getBeisenid(), exp.getUserid());
                    continue;
                }
                bo.setCmBeisenUserInfoPO(userInfo);
                CmConsultantExpModifyFlagPO modifyFlag = modifyFlagMap.get(exp.getUserid());
                if(Objects.isNull(modifyFlag)){
                    modifyFlag = new CmConsultantExpModifyFlagPO();
                }
                bo.setSyncFlag(modifyFlag);
                //字段同步北森数据
                CrmBeisenMappingEnum.syncBeisenData(bo);
                bo.getExp().setUserid(exp.getUserid());
                bo.getSyncFlag().setUserid(exp.getUserid());
                //保存北森同步花名册数据和北森同步花名册同步标识数据
                log.info("syncBeisenDataByExp| exp:{} ", JSON.toJSONString(bo.getExp()));
                log.info("syncBeisenDataByExp| syncFlag:{} ", JSON.toJSONString(bo.getSyncFlag()));
                save(Collections.singletonList(bo.getExp()), Collections.singletonList(bo.getSyncFlag()));
            }catch (Exception e){
                log.error("syncBeisenDataByExp| userid:"+exp.getUserid()+" is error", e);
                errorUserIdList.add(exp.getUserid());
            }
        }
        return errorUserIdList;
    }

    /**
     * @description:(省市区数据转换)
     * @param cityMapByProv
     * @param countryMapByCity
     * @return void
     * @author: shijie.wang
     * @date: 2024/11/13 10:33
     * @since JDK 1.8
     */
    private void transCityCodeMap(Map<String, AreaBO> cityMapByProv, Map<String, AreaBO> countryMapByCity){
        List<CityListVO> getProvCityList = dictionaryOuterService.getCityListVO();
        getProvCityList.forEach(prov -> {
            String provCode = prov.getDm();
            List<CityListVO> dataListCity= prov.getDataList();
            if(CollectionUtils.isEmpty(dataListCity)){
                return;
            }
            dataListCity.forEach(city ->{
                AreaBO vo1 = new AreaBO();
                vo1.setCityCode(city.getDm());
                vo1.setProvCode(provCode);
                cityMapByProv.put(city.getMc(), vo1);
                List<CityListVO> dataListCountry = city.getDataList();
                if(CollectionUtils.isEmpty(dataListCountry)){
                    return;
                }
                dataListCountry.forEach(country ->{
                    AreaBO vo2 = new AreaBO();
                    vo2.setCityCode(city.getDm());
                    vo2.setProvCode(provCode);
                    vo2.setCountryCode(country.getDm());
                    countryMapByCity.put(country.getMc(), vo2);
                });
            });
        });
    }

}