/**
 * Copyright (c) 2023, <PERSON><PERSON><PERSON><PERSON> HOWBUY INVESTMENT MANAGEMENT Co., Ltd.
 * All right reserved.
 * <p>
 * THIS IS UNPUBLISHED PROPRIETARY SOURCE CODE OF HOWBUY INVESTMENT
 * MANAGEMENT CO., LTD.  THE CONTENTS OF THIS FILE MAY NOT BE DISCLOSED
 * TO THIRD PARTIES, COPIED OR DUPLICATED IN ANY FORM, IN WHOLE OR IN PART,
 * WITHOUT THE PRIOR WRITTEN PERMISSION OF HOWBUY INVESTMENT MANAGEMENT
 * CO., LTD.
 */
package com.howbuy.crm.account.service.service.custinfo;

import com.alibaba.fastjson.JSON;
import com.github.pagehelper.Page;
import com.google.common.collect.Lists;
import com.howbuy.common.utils.Assert;
import com.howbuy.common.utils.StringUtil;
import com.howbuy.crm.account.client.enums.DealStatusEnum;
import com.howbuy.crm.account.client.request.custinfo.*;
import com.howbuy.crm.account.client.response.PageVO;
import com.howbuy.crm.account.client.response.Response;
import com.howbuy.crm.account.client.response.custinfo.*;
import com.howbuy.crm.account.client.utils.IdTypeUtil;
import com.howbuy.crm.account.dao.bo.custinfo.CmConscustForAnalyseBO;
import com.howbuy.crm.account.dao.po.custinfo.CmConscustPO;
import com.howbuy.crm.account.dao.po.custinfo.CmHboneAbnormalCustInfoPO;
import com.howbuy.crm.account.dao.po.custinfo.CmHboneAbnormalRelatedCustPO;
import com.howbuy.crm.account.dao.req.custinfo.AbnormalCustReqVO;
import com.howbuy.crm.account.service.business.custinfo.CmCustBusinessService;
import com.howbuy.crm.account.service.business.custinfo.HboneCustBindBusinessService;
import com.howbuy.crm.account.service.business.message.AccountCenterMessageBuss;
import com.howbuy.crm.account.service.commom.constant.Constants;
import com.howbuy.crm.account.service.commom.utils.AbnormalCustUtils;
import com.howbuy.crm.account.service.outerservice.acct.HboneAcctInfoOuterService;
import com.howbuy.crm.account.service.repository.custinfo.AbnormalCustRepository;
import com.howbuy.crm.account.service.repository.custinfo.ConsCustInfoRepository;
import com.howbuy.crm.account.service.repository.custinfo.HboneAbnormalCustRepository;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.stream.Collectors;

/**
 * @description: (一账通 客户  处理异常信息 信息service)
 * <AUTHOR>
 * @date 2023/12/13 16:41
 * @since JDK 1.8
 */
@Service
@Slf4j
public class HboneAbnormalCustInfoService extends  AbstractAbnormalCustInfoService{

    @Autowired
    private HboneAbnormalCustRepository abnormalCustRepository;
    @Autowired
    private ConsCustInfoRepository consCustInfoRepository;

    @Autowired
    private AbnormalCustRepository custRepository;

    @Autowired
    private HboneCustBindBusinessService hboneBindBusinessService;

    @Autowired
    private HboneAcctInfoOuterService hboneAcctInfoOuterService;

    @Autowired
    private CmCustBusinessService cmBusinessService;

    @Autowired
    private AccountCenterMessageBuss accountCenterMessageBuss;

    @Override
    public Response<String> updateDealStatus(DealAbnormalRequest request){
        DealStatusEnum dealStatusEnum=DealStatusEnum.getEnum(request.getDealStatus());
        return abnormalCustRepository.updateDealStatus(
                request.getId(),
                dealStatusEnum,
                request.getOperator(),
                request.getRemark());
    }


    @Override
    public Response<String> batchUpdateDealStatus(BatchDealAbnormalRequest batchRequest){
        return abnormalCustRepository.batchUpdateDealStatus(
                batchRequest.getIdList(),
                DealStatusEnum.getEnum(batchRequest.getDealStatus()),
                batchRequest.getOperator(),
                batchRequest.getRemark());
    }


    /**
     * @description:(根据 异常明细Id，关联异常客户与异常主表一账通，并更新信息)
     * @param detailId
     * @param operator
     * @return com.howbuy.crm.account.client.response.Response<java.lang.String>
     * @author: haoran.zhang
     * @date: 2024/1/19 11:21
     * @since JDK 1.8
     */
    public Response<String> associateAbnormalHbone(String detailId,String operator){
        //查询异常明细信息
        CmHboneAbnormalRelatedCustPO relatedCustPo=abnormalCustRepository.selectDetailByDetaiId(detailId);
        if(relatedCustPo==null){
            return Response.fail("异常明细信息不存在!");
        }
        //查询异常主表信息
        CmHboneAbnormalCustInfoPO abnormalCustPo=abnormalCustRepository.selectById(relatedCustPo.getAbnormalId());
        if(abnormalCustPo==null){
            return Response.fail("异常主表信息不存在!");
        }

        //关联 一账通账号 vs 投顾客户号
        String custNo=relatedCustPo.getCustNo();
        String hboneNo=abnormalCustPo.getHboneNo();

        //校验 一账通号 是否已经注销 。
        //一账通 明细信息
        HboneAcctCustDetailInfoVO  hboneInfo=hboneAcctInfoOuterService.queryHboneCustDetailInfo(hboneNo);
        if(hboneInfo==null){
            return Response.fail(String.format("一账通号：%s，已注销或不存在！",hboneNo));
        }

        HboneAcctRelationOptRequest bindOptRequest=new HboneAcctRelationOptRequest();
        bindOptRequest.setCustNo(custNo);
        bindOptRequest.setHboneNo(hboneNo);
        bindOptRequest.setOperator(operator);
        bindOptRequest.setRemark(null);
        bindOptRequest.setOperateChannel(Constants.OPERATE_CHANNEL_MENU);
        bindOptRequest.setOperateSource(null);
        Response<String> bindResp= hboneBindBusinessService.bindHboneTxAcct(bindOptRequest);
        log.info("一账通异常id: {} 处理，[关联异常一账通并更新信息]，绑定custNo:{} vs hboneNo:{} ,绑定结果：{}",
                abnormalCustPo.getId(),custNo,hboneNo, JSON.toJSONString(bindResp));
        if(!bindResp.isSuccess()){
            return bindResp;
        }
        //根据 一账通信息 更新 crm客户信息，  更新字段： 客户姓名  手机号  客户类型  证件类型  证件号码
        log.info("一账通异常id: {} 处理，[关联异常一账通并更新信息]，更新投顾客户信息，custNo:{},hboneNo:{},operator:{}",
                abnormalCustPo.getId(), custNo,hboneNo,operator);
        Response<String>  updateResp=updateCustInfoByHbone(custNo,hboneNo,operator);
        if(!updateResp.isSuccess()){
            return updateResp;
        }

        //更新处理状态： 已处理
        DealAbnormalRequest request=new DealAbnormalRequest();
        request.setOperator(operator);
        request.setDealStatus(DealStatusEnum.DEAL.getCode());
        request.setId(abnormalCustPo.getId());
        updateDealStatus( request);
        log.info("一账通异常id: {} 处理，[关联异常一账通并更新信息]，更新处理状态：已处理。operator:{}",
                abnormalCustPo.getId(),operator);

        return Response.ok();

    }


    private  Response<String>  updateCustInfoByHbone(String custNo,String hboneNo,String operator){
            Assert.notNull(custNo,"客户号不能为空！");
            Assert.notNull(hboneNo,"一账通号不能为空！");
            //当前客户信息
            CmConscustPO currentCustPo=consCustInfoRepository.selectPoByCustNo(custNo);
            //一账通 明细信息
            HboneAcctCustDetailInfoVO  hboneInfo=hboneAcctInfoOuterService.queryHboneCustDetailInfo(hboneNo);
            //查找 一账通账户中心  敏感信息 用于更新
            HboneAcctCustSensitiveInfoVO sensitiveInfo = hboneAcctInfoOuterService.queryHboneCustSensitiveInfo(hboneNo);

           //根据 一账通信息 更新 crm客户信息，  更新字段： 客户姓名 、 客户类型 、 手机号 、 证件类型 、 证件号码

            UpdateConsCustRequest updateCustReq=new UpdateConsCustRequest();
            updateCustReq.setCustNo(custNo);
            updateCustReq.setOperator(operator);
            //更新客户类型
            if(StringUtil.isNotBlank(hboneInfo.getInvstType())
                    && Boolean.FALSE.equals(StringUtil.isEqual(currentCustPo.getInvsttype(),hboneInfo.getInvstType()))){
                updateCustReq.setInvsttype(hboneInfo.getInvstType());
            }
            //更新客户姓名
            if(StringUtil.isNotBlank(hboneInfo.getCustName())
                    && Boolean.FALSE.equals(StringUtil.isEqual(currentCustPo.getCustname(),hboneInfo.getCustName()))){
                updateCustReq.setCustname(hboneInfo.getCustName());
            }
            //是否更新手机号 ： 必须要求 手机号码 与  手机区号  有一个不一致
            boolean isUpdateMobile=!(StringUtil.isEqual(currentCustPo.getMobileAreaCode(),hboneInfo.getMobileAreaCode())
                    && StringUtil.isEqual(currentCustPo.getMobileDigest(),hboneInfo.getMobileDigest()));
            if(isUpdateMobile){
                updateCustReq.setMobileAreaCode(hboneInfo.getMobileAreaCode());
                //获取明文手机号
                String mobile=hboneAcctInfoOuterService.queryHboneSensitiveMobile(hboneNo);
                //手机号码 明文信息
                updateCustReq.setMobile(mobile);
            }
            //是否更新 证件信息 ： 必须要求 证件类型 、证件地区码、 证件号 有一个不一致
            boolean isUpdateId=!(StringUtil.isEqual(currentCustPo.getIdtype(),hboneInfo.getIdType())
                    && StringUtil.isEqual(currentCustPo.getIdSignAreaCode(),hboneInfo.getIdSignAreaCode())
                    && StringUtil.isEqual(currentCustPo.getIdnoDigest(),hboneInfo.getIdNoDigest()));
            if(isUpdateId){
                updateCustReq.setIdtype(hboneInfo.getIdType());
                updateCustReq.setIdSignAreaCode(hboneInfo.getIdSignAreaCode());
                //证件号码 明文信息
                updateCustReq.setIdNo(sensitiveInfo.getIdNo());
                //证件有效日期 截止日期
                updateCustReq.setValiditydt(hboneInfo.getIdValidityEnd());
                //证件有效日期 起始日期
                updateCustReq.setValidityst(hboneInfo.getIdValidityStart());
                //证件是否长期有效 1-长期 0-非长期
                updateCustReq.setValidity(hboneInfo.getIdAlwaysValidFlag());
            }
            log.info("一账通异常处理，[关联异常一账通并更新信息]，更新投顾客户信息，custNo:{},更新属性：{}",custNo, JSON.toJSONString(updateCustReq));
            Response<UpdateConsCustRespVO>  updateResp = cmBusinessService.updateCust(updateCustReq);
            log.info("一账通异常处理，[关联异常一账通并更新信息]，custNo:{},更新结果：{}",custNo, JSON.toJSONString(updateResp));
            if(!updateResp.isSuccess()){
                return Response.fail(updateResp.getDescription());
            }

        return Response.ok();
    }

    /**
     * 处理异常 操作校验
     * @param request
     * @return
     */
    @Override
    public Response<String> validateSpecificDeal(DealAbnormalRequest request){
//        DealStatusEnum dealStatusEnum=DealStatusEnum.getEnum(request.getDealStatus());
        CmHboneAbnormalCustInfoPO custInfoPo= abnormalCustRepository.selectById(request.getId());
        if(custInfoPo==null){
            return Response.fail("待处理异常客户信息不存在!");
        }
        //必须为： 未处理 才允许处理
        DealStatusEnum currentDealStatusEnum=DealStatusEnum.getEnum(custInfoPo.getDealStatus());
        if(DealStatusEnum.UN_DEAL!=currentDealStatusEnum){
            return Response.fail(String.format("id为[%s]异常客户信息已处理，处理状态为[%s]!",
                    custInfoPo.getId(),
                    DealStatusEnum.getDescription(custInfoPo.getDealStatus())));
        }
        return Response.ok();
    }




    /**
     * @description:(查询明细列表)
     * @param detailList
     * @return java.util.List<com.howbuy.crm.account.client.response.custinfo.AbnormalRelatedDetailVO>
     * @author: haoran.zhang
     * @date: 2024/1/22 11:03
     * @since JDK 1.8
     */
    public List<AbnormalRelatedDetailVO> queryHboneAbnormalDetailList(List<String> detailList){
        if(CollectionUtils.isEmpty(detailList)){
            return Lists.newArrayList();
        }
        List<CmHboneAbnormalRelatedCustPO> relatedCustList=abnormalCustRepository.selectRelatedListByDetailList(Lists.partition(detailList,1000));
        return relatedCustList.stream().map(this::constructDetailVo).collect(Collectors.toList());
    }


    /**
     * @description:(分页查询 香港异常客户信息)
     * @param custInfoRequest
     * @return com.howbuy.crm.account.client.response.PageVO<com.howbuy.crm.account.client.response.custinfo.AbnormalCustInfoVO>
     * @author: haoran.zhang
     * @date: 2024/1/19 17:21
     * @since JDK 1.8
     */
    public PageVO<AbnormalCustInfoVO> queryHboneAbnormalPage(AbnormalCustInfoRequest custInfoRequest){
        //request --> vo 转换
        AbnormalCustReqVO reqVo=buidReqVo(custInfoRequest);

        Page<CmHboneAbnormalCustInfoPO>  abnormalPage= abnormalCustRepository.selectPageByVo(reqVo);
        List<CmHboneAbnormalCustInfoPO>  dbList=abnormalPage.getResult();

        List<String> abnormalIdList=dbList.stream().map(CmHboneAbnormalCustInfoPO::getId).collect(Collectors.toList());
        //查询关联的客户信息
        List<CmHboneAbnormalRelatedCustPO> relatedCustList= abnormalCustRepository.selectRelatedListByMainIdList(Lists.partition(abnormalIdList,1000));
        //按照 异常id 分组
        Map<String,List<CmHboneAbnormalRelatedCustPO>> relatedCustMap=relatedCustList.stream().collect(Collectors.groupingBy(CmHboneAbnormalRelatedCustPO::getAbnormalId));

        //组装对象
        List<AbnormalCustInfoVO> voList=Lists.newArrayList();
       for(CmHboneAbnormalCustInfoPO  dbPo :dbList){
           //主表 信息
           AbnormalCustInfoVO custInfoVo=constructCustVo(dbPo);
           //关联客户信息
           if(relatedCustMap.containsKey(dbPo.getId())){
               List<CmHboneAbnormalRelatedCustPO> relatedList=relatedCustMap.get(dbPo.getId());
               //构建 明细信息
               custInfoVo.setRelatedList(relatedList.stream().map(this::constructDetailWithAcctVo).collect(Collectors.toList()));
           }
           voList.add(custInfoVo);
       }

        //返回页面对象
        PageVO<AbnormalCustInfoVO> pageVo=new PageVO<>();
        pageVo.setTotal(abnormalPage.getTotal());
        pageVo.setPage(abnormalPage.getPageNum());
        pageVo.setSize(abnormalPage.getPageSize());
        pageVo.setRows(voList);
         return pageVo;
    }



    /**
     * 明细信息 附带  香港、一账通侧信息
     * @param dbPo
     * @return
     */
    private AbnormalRelatedCustVO constructDetailWithAcctVo(CmHboneAbnormalRelatedCustPO dbPo){

        AbnormalRelatedDetailVO detailVO=constructDetailVo( dbPo);
        AbnormalRelatedCustVO custVo=new AbnormalRelatedCustVO();
        BeanUtils.copyProperties(detailVO,custVo);

        //一账通侧 信息
        custVo.setHboneSideInfo(getHboneSideInfo(dbPo.getHboneNo()));
        //香港侧信息
        custVo.setHkSideInfo(getHkSideInfo(dbPo.getHkTxAcctNo()));

        String custNo=dbPo.getCustNo();

        //客户创建时间
        if(com.github.pagehelper.util.StringUtil.isNotEmpty(custNo)){
            CmConscustPO custPo=consCustInfoRepository.selectPoByCustNo(custNo);
            if(custPo!=null){
                //客户创建日期
                custVo.setCreateDt(custPo.getRegdt());
            }
        }
        return custVo;
    }

    /**
     * 异常明细对象转换
     * @param dbPo
     * @return
     */
    private AbnormalRelatedDetailVO constructDetailVo(CmHboneAbnormalRelatedCustPO dbPo){
        AbnormalRelatedDetailVO detailVo=new AbnormalRelatedDetailVO();

        String custNo=dbPo.getCustNo();
        detailVo.setId(dbPo.getId());
        detailVo.setAbnormalId(dbPo.getAbnormalId());
        detailVo.setCustNo(custNo);
        detailVo.setConsCode(dbPo.getConsCode());
        detailVo.setHkTxAcctNo(dbPo.getHkTxAcctNo());
        detailVo.setCustName(dbPo.getCustName());
        detailVo.setInvestType(dbPo.getInvestType());
        detailVo.setMobileAreaCode(dbPo.getMobileAreaCode());
        detailVo.setMobileDigest(dbPo.getMobileDigest());
        detailVo.setMobileMask(dbPo.getMobileMask());
        detailVo.setMobileCipher(dbPo.getMobileCipher());
        detailVo.setIdSignAreaCode(dbPo.getIdSignAreaCode());
        detailVo.setIdType(dbPo.getIdType());
        detailVo.setIdNoDigest(dbPo.getIdNoDigest());
        detailVo.setIdNoMask(dbPo.getIdNoMask());
        detailVo.setIdNoCipher(dbPo.getIdNoCipher());
        detailVo.setHboneNo(dbPo.getHboneNo());
        detailVo.setCreator(dbPo.getCreator());
        detailVo.setCreateTimestamp(dbPo.getCreateTimestamp());
        detailVo.setModifier(dbPo.getModifier());
        detailVo.setModifyTimestamp(dbPo.getModifyTimestamp());

        //证件类型 翻译描述
        detailVo.setIdTypeDesc(IdTypeUtil.getIdTypeDesc(dbPo.getInvestType(),dbPo.getIdType()));

        return detailVo;
    }


    /**
     * 异常对象转换  主表
     * @param dbPo
     * @return
     */
    private AbnormalCustInfoVO constructCustVo(CmHboneAbnormalCustInfoPO dbPo){
        AbnormalCustInfoVO custInfoVo=new AbnormalCustInfoVO();
        custInfoVo.setId(dbPo.getId());
        custInfoVo.setMessageClientId(dbPo.getMessageClientId());
        custInfoVo.setHkTxAcctNo(dbPo.getHkTxAcctNo());
        custInfoVo.setCustName(dbPo.getCustName());
        custInfoVo.setInvestType(dbPo.getInvestType());
        custInfoVo.setMobileAreaCode(dbPo.getMobileAreaCode());
        custInfoVo.setMobileDigest(dbPo.getMobileDigest());
        custInfoVo.setMobileMask(dbPo.getMobileMask());
        custInfoVo.setMobileCipher(dbPo.getMobileCipher());
        custInfoVo.setIdSignAreaCode(dbPo.getIdSignAreaCode());
        custInfoVo.setIdType(dbPo.getIdType());
        custInfoVo.setIdNoDigest(dbPo.getIdNoDigest());
        custInfoVo.setIdNoMask(dbPo.getIdNoMask());
        custInfoVo.setIdNoCipher(dbPo.getIdNoCipher());
        custInfoVo.setHboneNo(dbPo.getHboneNo());
        custInfoVo.setAbnormalSource(dbPo.getAbnormalSource());
        custInfoVo.setAbnormalSceneType(dbPo.getAbnormalSceneType());
        custInfoVo.setCreator(dbPo.getCreator());
        custInfoVo.setCreateTimestamp(dbPo.getCreateTimestamp());
        custInfoVo.setModifier(dbPo.getModifier());
        custInfoVo.setModifyTimestamp(dbPo.getModifyTimestamp());
        custInfoVo.setRecStat(dbPo.getRecStat());
        custInfoVo.setDealStatus(dbPo.getDealStatus());
        custInfoVo.setDealOperator(dbPo.getDealOperator());
        custInfoVo.setDealRemark(dbPo.getDealRemark());
        custInfoVo.setDealTimestamp(dbPo.getDealTimestamp());
        //证件类型 翻译描述
        custInfoVo.setIdTypeDesc(IdTypeUtil.getIdTypeDesc(dbPo.getInvestType(),dbPo.getIdType()));
        //异常描述
        custInfoVo.setAbnormalSceneDesc(AbnormalCustUtils.translateAbnormalDesc(dbPo.getAbnormalSource(),dbPo.getAbnormalSceneType()));
        custInfoVo.setMessageId(dbPo.getMessageId());

        //客户信息
        String hboneNo=dbPo.getHboneNo();
        if(StringUtils.isNotEmpty(hboneNo)){
            //补充 一账通侧信息
            custInfoVo.setHboneSideInfo(getHboneSideInfo(hboneNo));
            //crm关联的客户号
            CmConscustForAnalyseBO  custVo=getCustByHboneNo(hboneNo);
            if(custVo!=null){
                custInfoVo.setRelatedCustNo(custVo.getConscustno());
            }
        }
        return custInfoVo;
    }


    /**
     * 根据 hboneNo 查找关联的客户信息
     * @param hboneNo
     * @return
     */
    private  CmConscustForAnalyseBO getCustByHboneNo(String hboneNo){
        List<CmConscustForAnalyseBO> relatedCustList= custRepository.queryCustBOByHboneNo(hboneNo);
        if(CollectionUtils.isEmpty(relatedCustList)){
            return null;
        }
        if(relatedCustList.size()>1){
            log.warn("一账通号：{}，关联多个客户号：{}",hboneNo,JSON.toJSONString(relatedCustList));
        }
        return relatedCustList.get(0);

    }


    /**
     * @description:(请在此添加描述)
     * @param custInfoRequest
     * @return com.howbuy.crm.account.client.response.Response<java.lang.String>
     * @author: jin.wang03
     * @date: 2025/5/30 17:43
     * @since JDK 1.8
     */
    public Response<String> createCustInfoByHbone(HboneAbnormalCreateConsCustRequest custInfoRequest) {
        CmHboneAbnormalCustInfoPO cmHboneAbnormalCustInfoPO = abnormalCustRepository.selectById(custInfoRequest.getId());
        if (Objects.isNull(cmHboneAbnormalCustInfoPO)) {
            return Response.fail("异常客户id不存在！");
        }
        if (StringUtils.isBlank(cmHboneAbnormalCustInfoPO.getMessageId())) {
            return Response.fail("不支持新增客户！（该异常客户不是由账户中心消息产生的）");
        }
        return accountCenterMessageBuss.consumeAgain(cmHboneAbnormalCustInfoPO.getMessageId());
    }

}