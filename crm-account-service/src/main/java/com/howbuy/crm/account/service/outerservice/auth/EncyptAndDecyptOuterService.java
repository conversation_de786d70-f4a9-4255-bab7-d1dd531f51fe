/**
 * Copyright (c) 2023, <PERSON><PERSON><PERSON><PERSON> HOWBUY INVESTMENT MANAGEMENT Co., Ltd.
 * All right reserved.
 * <p>
 * THIS IS UNPUBLISHED PROPRIETARY SOURCE CODE OF HOWBUY INVESTMENT
 * MANAGEMENT CO., LTD.  THE CONTENTS OF THIS FILE MAY NOT BE DISCLOSED
 * TO THIRD PARTIES, COPIED OR DUPLICATED IN ANY FORM, IN WHOLE OR IN PART,
 * WITHOUT THE PRIOR WRITTEN PERMISSION OF HOWBUY INVESTMENT MANAGEMENT
 * CO., LTD.
 */
package com.howbuy.crm.account.service.outerservice.auth;

import com.howbuy.auth.facade.decrypt.DecryptSingleFacade;
import com.howbuy.auth.facade.encrypt.EncryptSingleFacade;
import com.howbuy.auth.facade.response.CodecSingleResponse;
import lombok.extern.slf4j.Slf4j;
import org.apache.dubbo.config.annotation.DubboReference;
import org.springframework.stereotype.Service;

/**
 * @description: (请在此添加描述)
 * <AUTHOR>
 * @date 2023/12/8 20:03
 * @since JDK 1.8
 */
@Slf4j
@Service
public class EncyptAndDecyptOuterService {

    @DubboReference(registry = "howbuy-auth-service", check = false)
    private EncryptSingleFacade encryptSingleFacade;


    @DubboReference(registry = "howbuy-auth-service", check = false)
    private DecryptSingleFacade decryptSingleFacade;


    /**
     * @description:(加密)
     * @param data
     * @return java.lang.String
     * @throws
     * @since JDK 1.8
     */
    public String encrypt(String data) {
        CodecSingleResponse resp= encryptSingleFacade.encrypt(data);
        return resp==null? null:resp.getCodecText();
    }


    /**
     * @description:(解密)
     * @param data
     * @return java.lang.String
     * @throws
     * @since JDK 1.8
     */
    public String decrypt(String data) {
        CodecSingleResponse resp = decryptSingleFacade.decrypt(data);
        return resp == null ? null : resp.getCodecText();
    }



}