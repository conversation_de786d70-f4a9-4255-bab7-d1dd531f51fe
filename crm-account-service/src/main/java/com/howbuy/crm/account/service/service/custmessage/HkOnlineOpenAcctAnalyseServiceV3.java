///**
// * Copyright (c) 2025, <PERSON><PERSON><PERSON><PERSON> HOWBUY INVESTMENT MANAGEMENT Co., Ltd.
// * All right reserved.
// * <p>
// * THIS IS UNPUBLISHED PROPRIETARY SOURCE CODE OF HOWBUY INVESTMENT
// * MANAGEMENT CO., LTD.  THE CONTENTS OF THIS FILE MAY NOT BE DISCLOSED
// * TO THIRD PARTIES, COPIED OR DUPLICATED IN ANY FORM, IN WHOLE OR IN PART,
// * WITHOUT THE PRIOR WRITTEN PERMISSION OF HOWBUY INVESTMENT MANAGEMENT
// * CO., LTD.
// */
//package com.howbuy.crm.account.service.service.custmessage;
//
//import com.google.common.collect.Lists;
//import com.howbuy.common.utils.StringUtil;
//import com.howbuy.crm.account.client.enums.custinfo.AbnormaSceneTypeEnum;
//import com.howbuy.crm.account.client.enums.custinfo.FullCustSourceEnum;
//import com.howbuy.crm.account.dao.bo.custinfo.CmConscustForAnalyseBO;
//import com.howbuy.crm.account.dao.po.custinfo.CmHkConscustPO;
//import com.howbuy.crm.account.dao.req.custinfo.CmHkCustReqVO;
//import com.howbuy.crm.account.service.repository.custinfo.AbnormalCustRepository;
//import com.howbuy.crm.account.service.repository.custinfo.CmHkCustInfoRepository;
//import com.howbuy.crm.account.service.vo.custinfo.AbnormalAnalyseResultVO;
//import com.howbuy.crm.account.service.vo.custinfo.HkMessageCustInfoVO;
//import lombok.extern.slf4j.Slf4j;
//import org.apache.commons.collections.CollectionUtils;
//import org.springframework.beans.factory.annotation.Autowired;
//import org.springframework.stereotype.Service;
//import org.springframework.util.Assert;
//
//import java.util.List;
//import java.util.Objects;
//import java.util.stream.Collectors;
//
///**
// * @description: (香港账户-香港开户消息处理服务V3 - 基于flow2.md流程图)
// * <AUTHOR>
// * @date 2025-04-16 15:46:33
// * @since JDK 1.8
// */
//@Service
//@Slf4j
//public class HkOnlineOpenAcctAnalyseServiceV3 implements CustMessageAnalyseService<HkMessageCustInfoVO> {
//
//    @Autowired
//    private AbnormalCustRepository abnormalCustRepository;
//
//    @Autowired
//    private CmHkCustInfoRepository cmHkCustInfoRepository;
//
//    @Override
//    public List<FullCustSourceEnum> getSourceList() {
//        // 假设仍然处理线上开户来源，或者根据需要调整为柜台开户来源枚举
//        return Lists.newArrayList(FullCustSourceEnum.HK_ONLINE_OPEN_ACCOUNT);
//    }
//
//    @Override
//    public AbnormalAnalyseResultVO<HkMessageCustInfoVO> analyze(HkMessageCustInfoVO analyseVo) {
//        log.info("香港开户消息处理V3(flow2.md)开始，香港客户号：{}", analyseVo.getHkTxAcctNo());
//        Assert.notNull(analyseVo.getHkTxAcctNo(), "香港客户号不能为空");
//
//        String hkTxAcctNo = analyseVo.getHkTxAcctNo();
//        String hboneNo = analyseVo.getHboneNo();
//
//        // 【香港客户号A】是否关联【投顾客户号】(reg_check)
//        CmHkCustReqVO hkReqVo = new CmHkCustReqVO();
//        hkReqVo.setHkTxAcctNo(hkTxAcctNo);
//        CmHkConscustPO existHkCustInfo = cmHkCustInfoRepository.selectByReqVO(hkReqVo);
//
//        if (existHkCustInfo != null) {
//            // 是，已关联【投顾客户号A】
//            String consCustNoA = existHkCustInfo.getConscustno();
//            log.info("香港客户号 {} 已关联投顾客户号 {}", hkTxAcctNo, consCustNoA);
//            return analyseHkCustExists(analyseVo, consCustNoA, hboneNo);
//        } else {
//            // 否，未关联
//            log.info("香港客户号 {} 未关联投顾客户号", hkTxAcctNo);
//            return analyseHkCustNotExists(analyseVo, hboneNo);
//        }
//    }
//
//    /**
//     * @description: 处理【香港客户号A】已关联【投顾客户号A】的情况 (reg_check -> Yes)
//     * <AUTHOR>
//     * @date 2025-04-16 15:46:33
//     */
//    private AbnormalAnalyseResultVO<HkMessageCustInfoVO> analyseHkCustExists(
//            HkMessageCustInfoVO analyseVo, String consCustNoA, String hboneNo) {
//
//        // 【一账通号A】是否关联【投顾客户号】(bind_check)
//        List<CmConscustForAnalyseBO> hboneCustList = queryCustListByHboneNo(hboneNo);
//
//        if (CollectionUtils.isEmpty(hboneCustList)) {
//            // B: 未关联
//            log.info("一账通号 {} 未关联投顾客户号 (路径 B)", hboneNo);
//            // 【投顾客户号A】是否关联【一账通号】(B1)
//            CmConscustForAnalyseBO custAInfo = abnormalCustRepository.queryCustBOByCustNo(consCustNoA);
//            if (custAInfo != null && StringUtil.isNotBlank(custAInfo.getHboneNo())) {
//                // B12: 关联【一账通号B】
//                log.info("投顾客户号 {} 已关联一账通号 {} (路径 B12)", consCustNoA, custAInfo.getHboneNo());
//                // 场景9：进香港异常表 (B121)
//                return createAbnormalResult(analyseVo, AbnormaSceneTypeEnum.HK09, Lists.newArrayList(custAInfo));
//            } else {
//                // B11: 未关联 -> 转入路径 A 逻辑
//                log.info("投顾客户号 {} 未关联一账通号 (路径 B11)，转入路径 A", consCustNoA);
//                return analyseHkCustExistsPathA(analyseVo, consCustNoA, hboneNo);
//            }
//        } else {
//            // 【一账通号A】已关联投顾客户号
//            CmConscustForAnalyseBO hboneCust = hboneCustList.get(0); // 假设只关联一个
//            if (hboneCustList.size() > 1) {
//                 log.warn("一账通号 {} 关联了多个投顾客户号: {}", hboneNo, hboneCustList);
//                 // TODO: 根据实际业务确定如何处理一账通关联多个投顾的情况，暂时按第一个处理
//            }
//
//            if (Objects.equals(consCustNoA, hboneCust.getConscustno())) {
//                // A: 关联【投顾客户号A】
//                 log.info("一账通号 {} 关联了投顾客户号 {} (路径 A)", hboneNo, consCustNoA);
//                return analyseHkCustExistsPathA(analyseVo, consCustNoA, hboneNo);
//            } else {
//                // C: 关联【投顾客户号B】
//                String consCustNoB = hboneCust.getConscustno();
//                log.info("一账通号 {} 关联了投顾客户号 {} (路径 C)", hboneNo, consCustNoB);
//                 // 场景10：进香港异常表 (C1)
//                List<CmConscustForAnalyseBO> relatedCustList = Lists.newArrayList();
//                CmConscustForAnalyseBO custAInfo = abnormalCustRepository.queryCustBOByCustNo(consCustNoA);
//                if (custAInfo != null) {
//                    relatedCustList.add(custAInfo);
//                }
//                relatedCustList.add(hboneCust); // custB info
//                return createAbnormalResult(analyseVo, AbnormaSceneTypeEnum.HK10, relatedCustList);
//            }
//        }
//    }
//
//    /**
//     * @description: 处理【香港客户号A】已关联【投顾客户号A】 且 【一账通号A】也关联【投顾客户号A】或未关联 (路径 A 逻辑)
//     * <AUTHOR>
//     * @date 2025-04-16 15:46:33
//     */
//    private AbnormalAnalyseResultVO<HkMessageCustInfoVO> analyseHkCustExistsPathA(
//            HkMessageCustInfoVO analyseVo, String consCustNoA, String hboneNo) {
//
//        // 【证件A】是否关联【投顾客户号】(A1)
//        List<CmConscustForAnalyseBO> idMatchCustList = queryCustListByIdInfo(analyseVo);
//
//        if (CollectionUtils.isEmpty(idMatchCustList)) {
//            // A11: 未关联
//            log.info("证件未关联投顾客户号 (路径 A11)");
//            return handleNormalBindingA(analyseVo, consCustNoA, hboneNo);
//        } else if (idMatchCustList.size() == 1) {
//            CmConscustForAnalyseBO idMatchCust = idMatchCustList.get(0);
//            if (Objects.equals(consCustNoA, idMatchCust.getConscustno())) {
//                // A12: 关联【投顾客户号A】
//                log.info("证件关联到投顾客户号 {} (路径 A12)", consCustNoA);
//                return handleNormalBindingA(analyseVo, consCustNoA, hboneNo);
//            } else {
//                // A13: 关联【投顾客户号B】
//                String consCustNoB = idMatchCust.getConscustno();
//                log.info("证件关联到不同的投顾客户号 {} (路径 A13)", consCustNoB);
//                // 场景7：进香港异常表 (A131)
//                 List<CmConscustForAnalyseBO> relatedCustList = Lists.newArrayList();
//                 CmConscustForAnalyseBO custAInfo = abnormalCustRepository.queryCustBOByCustNo(consCustNoA);
//                if (custAInfo != null) {
//                    relatedCustList.add(custAInfo);
//                }
//                 relatedCustList.add(idMatchCust); // custB info
//                 return createAbnormalResult(analyseVo, AbnormaSceneTypeEnum.HK07, relatedCustList);
//            }
//        } else {
//            // A14: 关联多个【投顾客户号】
//            log.info("证件关联到多个投顾客户号 (路径 A14): {}", idMatchCustList);
//            // 场景8：进香港异常表 (A141)
//            List<CmConscustForAnalyseBO> relatedCustList = Lists.newArrayList();
//            CmConscustForAnalyseBO custAInfo = abnormalCustRepository.queryCustBOByCustNo(consCustNoA);
//            if (custAInfo != null) {
//                relatedCustList.add(custAInfo);
//            }
//            // idMatchCustList过滤掉custAInfo
//            relatedCustList.addAll(idMatchCustList.stream()
//                    .filter(cust -> !Objects.equals(cust.getConscustno(), consCustNoA))
//                    .collect(Collectors.toList()));
//            return createAbnormalResult(analyseVo, AbnormaSceneTypeEnum.HK08, relatedCustList);
//        }
//    }
//
//     /**
//     * @description: 处理路径 A 下的正常绑定逻辑 (A111, A12 -> A111)
//     * <AUTHOR>
//     * @date 2025-04-16 15:46:33
//     */
//     private AbnormalAnalyseResultVO<HkMessageCustInfoVO> handleNormalBindingA(
//             HkMessageCustInfoVO analyseVo, String consCustNoA, String hboneNo) {
//         log.info("进入路径 A 的正常绑定处理流程，投顾客户号: {}, 一账通号: {}", consCustNoA, hboneNo);
//         CmConscustForAnalyseBO custAInfo = abnormalCustRepository.queryCustBOByCustNo(consCustNoA);
//         // 正常流程 A111: 香港开户信息更新CRM信息
//         // TODO: Implement normal processing logic - update CRM with analyseVo data for consCustNoA
//
//         // 检查是否需要绑定一账通 (A1111)
//         if (StringUtil.isNotBlank(hboneNo) && (custAInfo == null || StringUtil.isBlank(custAInfo.getHboneNo()))) {
//             log.info("需要为投顾客户号 {} 绑定一账通号 {}", consCustNoA, hboneNo);
//             // TODO: Implement binding logic - bind hboneNo to consCustNoA
//         }
//
//         AbnormalAnalyseResultVO<HkMessageCustInfoVO> result = AbnormalAnalyseResultVO.normalData(analyseVo);
//         result.setProcessedCustInfo(custAInfo); // 设置处理后的客户信息
//         log.info("路径 A 正常处理完成");
//         return result;
//     }
//
//    /**
//     * @description: 处理【香港客户号A】未关联【投顾客户号】的情况 (reg_check -> No)
//     * <AUTHOR>
//     * @date 2025-04-16 15:46:33
//     */
//    private AbnormalAnalyseResultVO<HkMessageCustInfoVO> analyseHkCustNotExists(
//            HkMessageCustInfoVO analyseVo, String hboneNo) {
//
//        // 【一账通号A】是否关联【投顾客户号】(account_check)
//        List<CmConscustForAnalyseBO> hboneCustList = queryCustListByHboneNo(hboneNo);
//
//        if (CollectionUtils.isEmpty(hboneCustList)) {
//            // 否 (D)
//             log.info("一账通号 {} 未关联投顾客户号 (路径 D)", hboneNo);
//            return analyseHkCustNotExistsPathD(analyseVo);
//        } else {
//            // 是，已关联投顾客户号A (H1)
//            CmConscustForAnalyseBO hboneCust = hboneCustList.get(0); // 假设只关联一个
//            String consCustNoA = hboneCust.getConscustno();
//            log.info("一账通号 {} 已关联投顾客户号 {} (路径 H1)", hboneNo, consCustNoA);
//             if (hboneCustList.size() > 1) {
//                 log.warn("一账通号 {} 关联了多个投顾客户号: {}", hboneNo, hboneCustList);
//                 // TODO: 根据实际业务确定如何处理，暂时按第一个处理
//             }
//            return analyseHkCustNotExistsPathH1(analyseVo, hboneCust);
//        }
//    }
//
//    /**
//     * @description: 处理【香港客户号A】未关联 且 【一账通号A】未关联 的情况 (路径 D)
//     * <AUTHOR>
//     * @date 2025-04-16 15:46:33
//     */
//    private AbnormalAnalyseResultVO<HkMessageCustInfoVO> analyseHkCustNotExistsPathD(HkMessageCustInfoVO analyseVo) {
//        // 【手机号A】是否关联【投顾客户号】(D)
//        List<CmConscustForAnalyseBO> mobileMatchCustList = queryCustListByMobile(analyseVo);
//
//        if (CollectionUtils.isEmpty(mobileMatchCustList)) {
//            // D1: 未关联
//            log.info("手机号未关联投顾客户号 (路径 D1)");
//            // 【证件A】是否关联【投顾客户号】(D11)
//            List<CmConscustForAnalyseBO> idMatchCustList = queryCustListByIdInfo(analyseVo);
//            if (CollectionUtils.isEmpty(idMatchCustList)) {
//                // D111: 未关联
//                log.info("证件也未关联投顾客户号 (路径 D111)");
//                // D1111: CRM创建客户，并绑定香港客户号A，并绑定一账通号A
//                log.info("执行创建新客户并绑定香港客户号 {} 和一账通号 {}", analyseVo.getHkTxAcctNo(), analyseVo.getHboneNo());
//                // TODO: Implement normal processing logic - create new customer and bind hkTxAcctNo, hboneNo
//                return AbnormalAnalyseResultVO.normalData(analyseVo); // 标记为正常处理，但无现有客户信息
//            } else if (idMatchCustList.size() == 1) {
//                // D112: 关联【投顾客户号A】
//                String consCustNoA = idMatchCustList.get(0).getConscustno();
//                 log.info("证件关联到投顾客户号 {} (路径 D112)", consCustNoA);
//                 // 场景11：进香港异常表 (D11121)
//                 return createAbnormalResult(analyseVo, AbnormaSceneTypeEnum.HK11, idMatchCustList);
//            } else {
//                // D113: 关联多个【投顾客户号】
//                 log.info("证件关联到多个投顾客户号 (路径 D113): {}", idMatchCustList);
//                 // 场景12：进香港异常表 (D11131)
//                 return createAbnormalResult(analyseVo, AbnormaSceneTypeEnum.HK12, idMatchCustList);
//            }
//        } else if (mobileMatchCustList.size() > 1) {
//            // D2: 关联多个【投顾客户号】
//            log.info("手机号关联到多个投顾客户号 (路径 D2): {}", mobileMatchCustList);
//            // 场景13：进香港异常表 (D21)
//            return createAbnormalResult(analyseVo, AbnormaSceneTypeEnum.HK13, mobileMatchCustList);
//        } else {
//            // D3: 关联【投顾客户号A】
//            CmConscustForAnalyseBO mobileMatchCust = mobileMatchCustList.get(0);
//            String consCustNoA = mobileMatchCust.getConscustno();
//            log.info("手机号关联到投顾客户号 {} (路径 D3)", consCustNoA);
//            return analyseHkCustNotExistsPathD3(analyseVo, mobileMatchCust);
//        }
//    }
//
//     /**
//     * @description: 处理【香港客户号A】未关联, 【一账通号A】未关联, 【手机号A】关联【投顾客户号A】 的情况 (路径 D3)
//     * <AUTHOR>
//     * @date 2025-04-16 15:46:33
//     */
//    private AbnormalAnalyseResultVO<HkMessageCustInfoVO> analyseHkCustNotExistsPathD3(
//             HkMessageCustInfoVO analyseVo, CmConscustForAnalyseBO mobileMatchCust) {
//
//        String consCustNoA = mobileMatchCust.getConscustno();
//        // 【投顾客户号A】是否关联【一账通】(D31)
//        if (StringUtil.isNotBlank(mobileMatchCust.getHboneNo())) {
//            // D311: 关联【一账通B】 (因为 analyseVo.hboneNo 是空的或未关联客户)
//            String hboneNoB = mobileMatchCust.getHboneNo();
//             log.info("投顾客户号 {} 已关联一账通号 {} (路径 D311)", consCustNoA, hboneNoB);
//             // 场景14：进香港异常表 (D3111)
//             return createAbnormalResult(analyseVo, AbnormaSceneTypeEnum.HK14, Lists.newArrayList(mobileMatchCust));
//        } else {
//            // D312: 未关联
//            log.info("投顾客户号 {} 未关联一账通号 (路径 D312)", consCustNoA);
//             // 【证件A】是否关联【投顾客户号】(D3121)
//            List<CmConscustForAnalyseBO> idMatchCustList = queryCustListByIdInfo(analyseVo);
//
//            if (CollectionUtils.isEmpty(idMatchCustList)) {
//                // E1: 未关联
//                log.info("证件未关联投顾客户号 (路径 E1)");
//                return checkHkBindingAndProcessNormalE(analyseVo, mobileMatchCust);
//            } else if (idMatchCustList.size() == 1) {
//                CmConscustForAnalyseBO idMatchCust = idMatchCustList.get(0);
//                if (Objects.equals(consCustNoA, idMatchCust.getConscustno())) {
//                    // E2: 关联【投顾客户号A】
//                    log.info("证件关联到投顾客户号 {} (路径 E2)", consCustNoA);
//                    return checkHkBindingAndProcessNormalE(analyseVo, mobileMatchCust);
//                } else {
//                    // E3: 关联【投顾客户号B】
//                    String consCustNoB = idMatchCust.getConscustno();
//                    log.info("证件关联到不同的投顾客户号 {} (路径 E3)", consCustNoB);
//                    // 场景16：进香港异常表 (E31)
//                    List<CmConscustForAnalyseBO> relatedCustList = Lists.newArrayList(mobileMatchCust, idMatchCust);
//                    return createAbnormalResult(analyseVo, AbnormaSceneTypeEnum.HK16, relatedCustList);
//                }
//            } else {
//                // E4: 关联多个【投顾客户号】
//                log.info("证件关联到多个投顾客户号 (路径 E4): {}", idMatchCustList);
//                // 场景17：进香港异常表 (E41)
//                List<CmConscustForAnalyseBO> relatedCustList = Lists.newArrayList(mobileMatchCust);
//                // 添加其他关联客户,过滤掉 当前客户
//                idMatchCustList.forEach(existCust -> {
//                    if (!Objects.equals(consCustNoA, existCust.getConscustno())) {
//                        relatedCustList.add(existCust);
//                    }
//                });
//                return createAbnormalResult(analyseVo, AbnormaSceneTypeEnum.HK17, relatedCustList);
//            }
//        }
//    }
//
//    /**
//     * @description: 检查客户是否绑定其他香港号并处理正常绑定逻辑 (路径 E1, E2 -> E11)
//     * <AUTHOR>
//     * @date 2025-04-16 15:46:33
//     */
//    private AbnormalAnalyseResultVO<HkMessageCustInfoVO> checkHkBindingAndProcessNormalE(
//            HkMessageCustInfoVO analyseVo, CmConscustForAnalyseBO custInfoA) {
//
//        String consCustNoA = custInfoA.getConscustno();
//        // 【投顾客户号A】是否绑定【其它香港客户号】(E11)
//        CmHkCustReqVO hkReqVo = new CmHkCustReqVO();
//        hkReqVo.setConscustno(consCustNoA);
//        CmHkConscustPO existHkCust = cmHkCustInfoRepository.selectByReqVO(hkReqVo);
//
//        if (existHkCust == null) {
//            // 否 (E111)
//            log.info("投顾客户号 {} 未绑定其他香港客户号 (路径 E111)", consCustNoA);
//             // 投顾客户号A与香港客户号A绑定 投顾客户号A与一账通A绑定 香港开户信息更新CRM信息
//             log.info("执行绑定：香港客户号 {} <-> 投顾客户号 {} <-> 一账通号 {}",
//                     analyseVo.getHkTxAcctNo(), consCustNoA, analyseVo.getHboneNo());
//             // TODO: Implement normal processing logic - bind hkTxAcctNo, hboneNo to consCustNoA and update CRM
//             AbnormalAnalyseResultVO<HkMessageCustInfoVO> result = AbnormalAnalyseResultVO.normalData(analyseVo);
//             result.setProcessedCustInfo(custInfoA);
//             return result;
//        } else {
//            // 是 (E112)
//            String existingHkNo = existHkCust.getHkTxAcctNo();
//            log.info("投顾客户号 {} 已绑定其他香港客户号 {} (路径 E112)", consCustNoA, existingHkNo);
//            // 场景15：进香港异常表 (E112)
//            return createAbnormalResult(analyseVo, AbnormaSceneTypeEnum.HK15, Lists.newArrayList(custInfoA));
//        }
//    }
//
//
//    /**
//     * @description: 处理【香港客户号A】未关联 且 【一账通号A】关联【投顾客户号A】的情况 (路径 H1)
//     * <AUTHOR>
//     * @date 2025-04-16 15:46:33
//     */
//    private AbnormalAnalyseResultVO<HkMessageCustInfoVO> analyseHkCustNotExistsPathH1(
//            HkMessageCustInfoVO analyseVo, CmConscustForAnalyseBO hboneCust) {
//
//        // 【投顾客户号A】绑定手机号是否等于【手机号A】(H1)
//        boolean mobileMatch = isMobileMatch(hboneCust, analyseVo.getMobileAreaCode(), analyseVo.getMobileDigest());
//
//        if (mobileMatch) {
//            // 是 (H11)
//            log.info("投顾客户号 {} 的手机号与消息手机号匹配 (路径 H11)", hboneCust.getConscustno());
//            return analyseHkCustNotExistsPathH11(analyseVo, hboneCust);
//        } else {
//            // 否 (H12)
//            log.info("投顾客户号 {} 的手机号与消息手机号不匹配 (路径 H12)", hboneCust.getConscustno());
//            return analyseHkCustNotExistsPathH12(analyseVo, hboneCust);
//        }
//    }
//
//    /**
//     * @description: 处理路径 H1 -> 是 (H11) 的情况
//     * <AUTHOR>
//     * @date 2025-04-16 15:46:33
//     */
//    private AbnormalAnalyseResultVO<HkMessageCustInfoVO> analyseHkCustNotExistsPathH11(
//            HkMessageCustInfoVO analyseVo, CmConscustForAnalyseBO hboneCust) {
//
//        String consCustNoA = hboneCust.getConscustno();
//        // 【证件A】是否关联【投顾客户号】(H11)
//        List<CmConscustForAnalyseBO> idMatchCustList = queryCustListByIdInfo(analyseVo);
//
//        if (CollectionUtils.isNotEmpty(idMatchCustList)) {
//            if (idMatchCustList.size() == 1) {
//                CmConscustForAnalyseBO idMatchCust = idMatchCustList.get(0);
//                if (Objects.equals(consCustNoA, idMatchCust.getConscustno())) {
//                    // H111: 关联【投顾客户号A】
//                    log.info("证件关联到投顾客户号 {} (路径 H111)", consCustNoA);
//                    return checkHkBindingAndProcessNormalH(analyseVo, hboneCust);
//                } else {
//                    // H113: 关联【投顾客户号B】
//                    String consCustNoB = idMatchCust.getConscustno();
//                     log.info("证件关联到不同的投顾客户号 {} (路径 H113)", consCustNoB);
//                     // 场景20：进香港异常表 (H1131)
//                    List<CmConscustForAnalyseBO> related = Lists.newArrayList(hboneCust, idMatchCust);
//                    return createAbnormalResult(analyseVo, AbnormaSceneTypeEnum.HK20, related);
//                }
//            } else {
//                // H114: 关联多个【投顾客户号】
//                log.info("证件关联到多个投顾客户号 (路径 H114): {}", idMatchCustList);
//                // 场景21：进香港异常表 (H1141)
//                List<CmConscustForAnalyseBO> related = Lists.newArrayList(hboneCust);
//                idMatchCustList.stream().filter(cust -> !Objects.equals(consCustNoA, cust.getConscustno())).forEach(related::add);
//                return createAbnormalResult(analyseVo, AbnormaSceneTypeEnum.HK21, related);
//            }
//        } else {
//            // H112: 未关联
//            log.info("证件未关联投顾客户号 (路径 H112)");
//            // 【投顾客户号A】是否关联【证件号】(H121)
//            boolean custHasId = StringUtil.isNotBlank(hboneCust.getIdtype()) && StringUtil.isNotBlank(hboneCust.getIdnoDigest());
//            if (!custHasId) {
//                // 否 (-> H1111)
//                 log.info("投顾客户号 {} 无证件信息 (路径 H121 -> No)", consCustNoA);
//                 return checkHkBindingAndProcessNormalH(analyseVo, hboneCust);
//            } else {
//                 // 是 (H1211)
//                 log.info("投顾客户号 {} 有证件信息，但与消息证件不匹配 (路径 H121 -> Yes)", consCustNoA);
//                 // 场景19：进香港异常表 (H1211)
//                 return createAbnormalResult(analyseVo, AbnormaSceneTypeEnum.HK19, Lists.newArrayList(hboneCust));
//            }
//        }
//    }
//
//    /**
//     * @description: 检查客户是否绑定其他香港号并处理正常绑定逻辑 (路径 H111, H121 -> No -> H1111)
//     * <AUTHOR>
//     * @date 2025-04-16 15:46:33
//     */
//     private AbnormalAnalyseResultVO<HkMessageCustInfoVO> checkHkBindingAndProcessNormalH(
//             HkMessageCustInfoVO analyseVo, CmConscustForAnalyseBO custInfoA) {
//
//         String consCustNoA = custInfoA.getConscustno();
//         // 【投顾客户号A】是否绑定【其它香港客户号】(H1111)
//         CmHkCustReqVO hkReqVo = new CmHkCustReqVO();
//         hkReqVo.setConscustno(consCustNoA);
//         CmHkConscustPO existHkCust = cmHkCustInfoRepository.selectByReqVO(hkReqVo);
//
//         if (existHkCust == null) {
//             // 否 (H11111)
//             log.info("投顾客户号 {} 未绑定其他香港客户号 (路径 H11111)", consCustNoA);
//             // 投顾客户号A与香港客户号A绑定 香港开户信息更新CRM信息
//             log.info("执行绑定：香港客户号 {} <-> 投顾客户号 {}，并更新CRM信息",
//                     analyseVo.getHkTxAcctNo(), consCustNoA);
//             // TODO: Implement normal processing logic - bind hkTxAcctNo to consCustNoA and update CRM
//             AbnormalAnalyseResultVO<HkMessageCustInfoVO> result = AbnormalAnalyseResultVO.normalData(analyseVo);
//             result.setProcessedCustInfo(custInfoA);
//             return result;
//         } else {
//             // 是 (H11112)
//             String existingHkNo = existHkCust.getHkTxAcctNo();
//             log.info("投顾客户号 {} 已绑定其他香港客户号 {} (路径 H11112)", consCustNoA, existingHkNo);
//             // 场景18：进香港异常表 (H11112)
//             return createAbnormalResult(analyseVo, AbnormaSceneTypeEnum.HK18, Lists.newArrayList(custInfoA));
//         }
//     }
//
//
//    /**
//     * @description: 处理路径 H1 -> 否 (H12) 的情况
//     * <AUTHOR>
//     * @date 2025-04-16 15:46:33
//     */
//    private AbnormalAnalyseResultVO<HkMessageCustInfoVO> analyseHkCustNotExistsPathH12(
//            HkMessageCustInfoVO analyseVo, CmConscustForAnalyseBO hboneCust) {
//
//        // 【手机号A】是否关联【投顾客户号】(H12)
//        List<CmConscustForAnalyseBO> mobileMatchCustList = queryCustListByMobile(analyseVo);
//
//        if (CollectionUtils.isEmpty(mobileMatchCustList)) {
//            // F1: 未关联
//            log.info("消息手机号未关联其他投顾客户号 (路径 F1)");
//            return analyseHkCustNotExistsPathF1(analyseVo, hboneCust);
//        } else if (mobileMatchCustList.size() > 1) {
//            // F2: 关联多个【投顾客户号】
//            log.info("消息手机号关联到多个投顾客户号 (路径 F2): {}", mobileMatchCustList);
//            // 场景26：进香港异常表 (F21)
//             List<CmConscustForAnalyseBO> related = Lists.newArrayList(hboneCust);
//             related.addAll(mobileMatchCustList);
//            return createAbnormalResult(analyseVo, AbnormaSceneTypeEnum.HK26, related);
//        } else {
//            // F3: 关联【投顾客户号B】
//             CmConscustForAnalyseBO mobileMatchCustB = mobileMatchCustList.get(0);
//             String consCustNoB = mobileMatchCustB.getConscustno();
//             log.info("消息手机号关联到投顾客户号 B:{} (路径 F3)", consCustNoB);
//             // 需要确保这个客户 B 不是客户 A
//             if (Objects.equals(hboneCust.getConscustno(), consCustNoB)) {
//                 log.warn("手机号匹配到的客户 B ({}) 与一账通关联的客户 A ({}) 相同，逻辑回到 F1 处理", consCustNoB, hboneCust.getConscustno());
//                 // 理论上 H1 判断手机号不匹配时，这里 mobileMatchCustList 不应包含 hboneCust，但为保险起见
//                 return analyseHkCustNotExistsPathF1(analyseVo, hboneCust);
//             }
//            return analyseHkCustNotExistsPathF3(analyseVo, hboneCust, mobileMatchCustB);
//        }
//    }
//
//    /**
//     * @description: 处理路径 H12 -> F1 的情况
//     * <AUTHOR>
//     * @date 2025-04-16 15:46:33
//     */
//    private AbnormalAnalyseResultVO<HkMessageCustInfoVO> analyseHkCustNotExistsPathF1(
//            HkMessageCustInfoVO analyseVo, CmConscustForAnalyseBO hboneCust) {
//
//        String consCustNoA = hboneCust.getConscustno();
//        // 【证件A】是否关联【投顾客户号】(F11)
//        List<CmConscustForAnalyseBO> idMatchCustList = queryCustListByIdInfo(analyseVo);
//
//        if (CollectionUtils.isEmpty(idMatchCustList)) {
//            // F112: 未关联
//            log.info("证件未关联投顾客户号 (路径 F112)");
//            // 场景22：进香港异常表 (F1121)
//            return createAbnormalResult(analyseVo, AbnormaSceneTypeEnum.HK22, Lists.newArrayList(hboneCust));
//        } else if (idMatchCustList.size() == 1) {
//             CmConscustForAnalyseBO idMatchCust = idMatchCustList.get(0);
//             if (Objects.equals(consCustNoA, idMatchCust.getConscustno())) {
//                 // F113: 关联【投顾客户号A】
//                 log.info("证件关联到投顾客户号 A:{} (路径 F113)", consCustNoA);
//                 // 场景23：进香港异常表 (F1131)
//                 return createAbnormalResult(analyseVo, AbnormaSceneTypeEnum.HK23, Lists.newArrayList(hboneCust));
//             } else {
//                 // F114: 关联【投顾客户号B】
//                 String consCustNoB = idMatchCust.getConscustno();
//                 log.info("证件关联到投顾客户号 B:{} (路径 F114)", consCustNoB);
//                 // 场景24：进香港异常表 (F1141)
//                  List<CmConscustForAnalyseBO> related = Lists.newArrayList(hboneCust, idMatchCust);
//                 return createAbnormalResult(analyseVo, AbnormaSceneTypeEnum.HK24, related);
//             }
//        } else {
//            // F115: 关联多个【投顾客户号】
//            log.info("证件关联到多个投顾客户号 (路径 F115): {}", idMatchCustList);
//            // 场景25：进香港异常表 (F1151)
//            List<CmConscustForAnalyseBO> related = Lists.newArrayList(hboneCust);
//            idMatchCustList.stream().filter(idMatchCust -> !Objects.equals(idMatchCust.getConscustno(), consCustNoA)).forEach(related::add);
//            return createAbnormalResult(analyseVo, AbnormaSceneTypeEnum.HK25, related);
//        }
//    }
//
//    /**
//     * @description: 处理路径 H12 -> F3 的情况
//     * <AUTHOR>
//     * @date 2025-04-16 15:46:33
//     */
//    private AbnormalAnalyseResultVO<HkMessageCustInfoVO> analyseHkCustNotExistsPathF3(
//            HkMessageCustInfoVO analyseVo, CmConscustForAnalyseBO hboneCustA, CmConscustForAnalyseBO mobileMatchCustB) {
//
//        String consCustNoA = hboneCustA.getConscustno();
//        String consCustNoB = mobileMatchCustB.getConscustno();
//
//        // 【证件A】是否关联【投顾客户号】(F31)
//        List<CmConscustForAnalyseBO> idMatchCustList = queryCustListByIdInfo(analyseVo);
//
//        if (CollectionUtils.isEmpty(idMatchCustList)) {
//            // F311: 未关联
//            log.info("证件未关联投顾客户号 (路径 F311)");
//            // 场景27：进香港异常表 (F3111)
//            List<CmConscustForAnalyseBO> related = Lists.newArrayList(hboneCustA, mobileMatchCustB);
//            return createAbnormalResult(analyseVo, AbnormaSceneTypeEnum.HK27, related);
//        } else if (idMatchCustList.size() == 1) {
//            CmConscustForAnalyseBO idMatchCust = idMatchCustList.get(0);
//            String idMatchCustNo = idMatchCust.getConscustno();
//            if (Objects.equals(consCustNoA, idMatchCustNo)) {
//                // F312: 关联【投顾客户号A】
//                log.info("证件关联到投顾客户号 A:{} (路径 F312)", consCustNoA);
//                // -> 场景27 (F3111)
//                List<CmConscustForAnalyseBO> related = Lists.newArrayList(hboneCustA, mobileMatchCustB);
//                return createAbnormalResult(analyseVo, AbnormaSceneTypeEnum.HK27, related);
//            } else if (Objects.equals(consCustNoB, idMatchCustNo)) {
//                // F313: 关联【投顾客户号B】
//                 log.info("证件关联到投顾客户号 B:{} (路径 F313)", consCustNoB);
//                 // 场景28：进香港异常表 (F3131)
//                 List<CmConscustForAnalyseBO> related = Lists.newArrayList(hboneCustA, mobileMatchCustB);
//                 return createAbnormalResult(analyseVo, AbnormaSceneTypeEnum.HK28, related);
//            } else {
//                // F314: 关联【投顾客户号C】
//                String consCustNoC = idMatchCust.getConscustno();
//                log.info("证件关联到投顾客户号 C:{} (路径 F314)", consCustNoC);
//                // 场景29：进香港异常表 (F3141)
//                List<CmConscustForAnalyseBO> related = Lists.newArrayList(hboneCustA, mobileMatchCustB, idMatchCust);
//                return createAbnormalResult(analyseVo, AbnormaSceneTypeEnum.HK29, related);
//            }
//        } else {
//             // F315: 关联多个【投顾客户号】
//             log.info("证件关联到多个投顾客户号 (路径 F315): {}", idMatchCustList);
//             // 场景30：进香港异常表 (F3151)
//             List<CmConscustForAnalyseBO> related = Lists.newArrayList(hboneCustA, mobileMatchCustB);
//             idMatchCustList.stream().filter(idMatchCust -> !Objects.equals(idMatchCust.getConscustno(), consCustNoA)
//                     && !Objects.equals(idMatchCust.getConscustno(), consCustNoB)).forEach(related::add);
//             return createAbnormalResult(analyseVo, AbnormaSceneTypeEnum.HK30, related);
//        }
//    }
//
//
//    // --- Helper Methods ---
//
//    /**
//     * @description: 根据一账通号查询客户列表 (Null safe &NotBlank check)
//     * <AUTHOR>
//     * @date 2025-04-16 15:46:33
//     */
//    private List<CmConscustForAnalyseBO> queryCustListByHboneNo(String hboneNo) {
//        if (StringUtil.isBlank(hboneNo)) {
//            return Lists.newArrayList();
//        }
//        return abnormalCustRepository.queryCustBOByHboneNo(hboneNo);
//    }
//
//    /**
//     * @description: 根据手机信息查询客户列表 (Null safe)
//     * <AUTHOR>
//     * @date 2025-04-16 15:46:33
//     */
//    private List<CmConscustForAnalyseBO> queryCustListByMobile(HkMessageCustInfoVO analyseVo) {
//        if (StringUtil.isBlank(analyseVo.getMobileAreaCode()) || StringUtil.isBlank(analyseVo.getMobileDigest())) {
//            return Lists.newArrayList();
//        }
//        return abnormalCustRepository.queryCustListByMobile(
//                analyseVo.getInvestType(), analyseVo.getMobileAreaCode(), analyseVo.getMobileDigest());
//    }
//
//    /**
//     * @description: 根据证件信息查询客户列表 (Null safe)
//     * <AUTHOR>
//     * @date 2025-04-16 15:46:33
//     */
//    private List<CmConscustForAnalyseBO> queryCustListByIdInfo(HkMessageCustInfoVO analyseVo) {
//         if (StringUtil.isBlank(analyseVo.getIdType()) || StringUtil.isBlank(analyseVo.getIdNoDigest())) {
//             return Lists.newArrayList();
//         }
//        return abnormalCustRepository.queryCustListByIdNo(
//                analyseVo.getInvestType(),
//                analyseVo.getIdType(),
//                analyseVo.getIdSignAreaCode(), // 注意: V2代码使用了idSignAreaCode，流程图未明确，保持一致
//                analyseVo.getIdNoDigest());
//    }
//
//    /**
//     * @description: 判断手机号是否匹配
//     * <AUTHOR>
//     * @date 2025-04-16 15:46:33
//     */
//    private boolean isMobileMatch(CmConscustForAnalyseBO custInfo, String mobileAreaCode, String mobileDigest) {
//        // 确保比较时处理 null 情况
//        return custInfo != null &&
//               Objects.equals(mobileAreaCode, custInfo.getMobileAreaCode()) &&
//               Objects.equals(mobileDigest, custInfo.getMobileDigest());
//    }
//
//    /**
//     * @description: 创建异常结果对象
//     * <AUTHOR>
//     * @date 2025-04-16 15:46:33
//     */
//    private AbnormalAnalyseResultVO<HkMessageCustInfoVO> createAbnormalResult(
//            HkMessageCustInfoVO analyseVo,
//            AbnormaSceneTypeEnum sceneType,
//            List<CmConscustForAnalyseBO> relatedCustList) {
//
//        log.warn("香港开户消息处理V3发现异常场景：{}，香港客户号：{}, 关联客户：{}",
//                sceneType, analyseVo.getHkTxAcctNo(), relatedCustList);
//
//        return AbnormalAnalyseResultVO.notNormalData(
//                analyseVo,
//                sceneType, // 使用 flow2.md 中定义的场景枚举
//                relatedCustList);
//    }
//}