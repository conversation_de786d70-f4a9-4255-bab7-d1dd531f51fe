/**
 * Copyright (c) 2023, <PERSON><PERSON><PERSON><PERSON> HOWBUY INVESTMENT MANAGEMENT Co., Ltd.
 * All right reserved.
 * <p>
 * THIS IS UNPUBLISHED PROPRIETARY SOURCE CODE OF HOWBUY INVESTMENT
 * MANAGEMENT CO., LTD.  THE CONTENTS OF THIS FILE MAY NOT BE DISCLOSED
 * TO THIRD PARTIES, COPIED OR DUPLICATED IN ANY FORM, IN WHOLE OR IN PART,
 * WITHOUT THE PRIOR WRITTEN PERMISSION OF HOWBUY INVESTMENT MANAGEMENT
 * CO., LTD.
 */
package com.howbuy.crm.account.service.message.hkcustinfo;

import com.howbuy.crm.account.client.enums.custinfo.FullCustSourceEnum;
import com.howbuy.crm.account.client.response.Response;
import com.howbuy.crm.account.dao.bo.custinfo.CmConscustForAnalyseBO;
import com.howbuy.crm.account.service.business.custinfo.CmCustProcessBusinessService;
import com.howbuy.crm.account.service.message.dto.HkUpdateCustMessageDTO;
import com.howbuy.crm.account.service.vo.custinfo.AbnormalAnalyseResultVO;
import com.howbuy.crm.account.service.vo.custinfo.HkMessageCustInfoVO;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;

import java.util.Arrays;
import java.util.List;

/**
 * @description: ([香港账户]-[香港客户手机号变更] 消息处理器[topic.hk.unRegister])
 * <AUTHOR>
 * @date 2023/12/11 17:04
 * @since JDK 1.8
 */
@Slf4j
@Service
public class HkMobileChangeMessageProcessor extends AbstractHkMessageProcessor<HkUpdateCustMessageDTO> {


    /**
     * [TOPIC_HK_UPDATE_CUST]
     */
    @Value("${topic.hk.updateCust}")
    private String topicUpdateCust;


    /**
     * [MOBILE_UPDATE]
     */
    @Value("${tag.hk.mobileUpdate}")
    private String tagName;


    @Autowired
    private CmCustProcessBusinessService processBusinessService;


    @Override
    public String getQuartMessageChannel() {
        return topicUpdateCust;
    }

    @Override
    public List<String> getSupportTagList() {
        return Arrays.asList(tagName);
    }

    @Override
    HkMessageCustInfoVO constructByMessage(HkUpdateCustMessageDTO acctMsgDto) {
        HkMessageCustInfoVO custVo=new HkMessageCustInfoVO();
        custVo.setHkTxAcctNo(acctMsgDto.getHkCustNo());
        fillHkAcctInfo(custVo);
        return custVo;
    }

    @Override
    public FullCustSourceEnum getCustSource() {
        return FullCustSourceEnum.HK_CUST_MOBILE_CHANGE;
    }

    @Override
    Response<String> processHkMessage(AbnormalAnalyseResultVO<HkMessageCustInfoVO> resultVo) {
        HkMessageCustInfoVO analyseVo=resultVo.getAnalyseVo();

        CmConscustForAnalyseBO processedCustInfo=resultVo.getProcessedCustInfo();
        if(processedCustInfo==null){
            return Response.ok(String.format("香港客户手机号变更同步，根据香港客户号：%s ，查找crm客户不存在！",analyseVo.getHkTxAcctNo()),null);
        }

//（3）若存在，且不存在重复客户，则将【香港客户号】的【手机区号、手机号】更新为香港账户中心推送的数据
//        若账户中心的【手机号】为空，则不更新CRM手机号
//        若账户中心的【手机号】不为空，则取账户中心【手机区号、手机号】覆盖CRM的投顾客户信息
          return processBusinessService.updateMobileByHk(analyseVo.getHkTxAcctNo(),processedCustInfo.getConscustno(),analyseVo.getCreator());
    }

}