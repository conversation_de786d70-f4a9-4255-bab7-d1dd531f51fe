/**
 * Copyright (c) 2024, <PERSON><PERSON><PERSON><PERSON> HOWBUY INVESTMENT MANAGEMENT Co., Ltd.
 * All right reserved.
 * <p>
 * THIS IS UNPUBLISHED PROPRIETARY SOURCE CODE OF HOWBUY INVESTMENT
 * MANAGEMENT CO., LTD.  THE CONTENTS OF THIS FILE MAY NOT BE DISCLOSED
 * TO THIRD PARTIES, COPIED OR DUPLICATED IN ANY FORM, IN WHOLE OR IN PART,
 * WITHOUT THE PRIOR WRITTEN PERMISSION OF HOWBUY INVESTMENT MANAGEMENT
 * CO., LTD.
 */
package com.howbuy.crm.account.service.facade.consultant;

import com.howbuy.crm.account.client.facade.consultant.BatchQueryConsPerformanceCoeffFacade;
import com.howbuy.crm.account.client.request.consultant.BatchQueryConsPerformanceCoeffRequest;
import com.howbuy.crm.account.client.response.Response;
import com.howbuy.crm.account.client.response.consultant.QueryConsPerformanceCoeffVO;
import com.howbuy.crm.account.service.service.consultant.BatchQueryConsPerformanceCoeffService;
import lombok.extern.slf4j.Slf4j;
import org.apache.dubbo.config.annotation.DubboService;

import javax.annotation.Resource;
import java.util.List;

/**
 * @description: 批量获取人员绩效系数表接口实现
 * <AUTHOR>
 * @date 2025-07-17 15:50:04
 * @since JDK 1.8
 */
@Slf4j
@DubboService
public class BatchQueryConsPerformanceCoeffFacadeImpl implements BatchQueryConsPerformanceCoeffFacade {

    @Resource
    private BatchQueryConsPerformanceCoeffService batchQueryConsPerformanceCoeffService;

    /**
     * @param request 请求参数
     * @return com.howbuy.crm.account.client.response.Response<java.util.List<com.howbuy.crm.account.client.response.consultant.QueryConsPerformanceCoeffVO>>
     * @description: 批量获取人员绩效系数表
     * <AUTHOR>
     * @date 2025-07-17 15:50:04
     * @since JDK 1.8
     */
    @Override
    public Response<List<QueryConsPerformanceCoeffVO>> execute(BatchQueryConsPerformanceCoeffRequest request) {
        log.info("批量获取人员绩效系数表，请求参数：{}", request);
        try {
            List<QueryConsPerformanceCoeffVO> result = batchQueryConsPerformanceCoeffService.execute(request);
            log.info("批量获取人员绩效系数表完成，结果数：{}", result.size());
            return Response.ok(result);
        } catch (Exception e) {
            log.error("批量获取人员绩效系数表异常", e);
            return Response.fail("批量获取人员绩效系数表失败：" + e.getMessage());
        }
    }
} 