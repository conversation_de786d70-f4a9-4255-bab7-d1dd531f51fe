/**
 * Copyright (c) 2023, <PERSON><PERSON><PERSON><PERSON> HOWBUY INVESTMENT MANAGEMENT Co., Ltd.
 * All right reserved.
 * <p>
 * THIS IS UNPUBLISHED PROPRIETARY SOURCE CODE OF HOWBUY INVESTMENT
 * MANAGEMENT CO., LTD.  THE CONTENTS OF THIS FILE MAY NOT BE DISCLOSED
 * TO THIRD PARTIES, COPIED OR DUPLICATED IN ANY FORM, IN WHOLE OR IN PART,
 * WITHOUT THE PRIOR WRITTEN PERMISSION OF HOWBUY INVESTMENT MANAGEMENT
 * CO., LTD.
 */
package com.howbuy.crm.account.service.business.custinfo;

import com.alibaba.fastjson.JSON;
import com.howbuy.common.utils.Assert;
import com.howbuy.common.utils.StringUtil;
import com.howbuy.crm.account.client.request.custinfo.HboneAcctRelationOptRequest;
import com.howbuy.crm.account.client.response.Response;
import com.howbuy.crm.account.dao.bo.custinfo.CmConscustForAnalyseBO;
import com.howbuy.crm.account.dao.mapper.customize.custinfo.ConscustCustomizeMapper;
import com.howbuy.crm.account.service.repository.custinfo.AbnormalCustRepository;
import com.howbuy.crm.account.service.repository.custinfo.CmHboneCustRelationRepository;
import com.howbuy.crm.account.service.req.custinfo.HboneCustRelationOptReqVO;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;
import java.util.stream.Collectors;

/**
 * @description: (一账通  客户绑定 service )
 * <AUTHOR>
 * @date 2023/12/25 20:37
 * @since JDK 1.8
 */
@Service
@Slf4j
public class HboneCustBindBusinessService {


    @Autowired
    private CmHboneCustRelationRepository hboneCustRelationRepository;
    @Autowired
    private AbnormalCustRepository abnormalCustRepository;
    @Autowired
    private ConscustCustomizeMapper conscustCustomizeMapper;


    /**
     * @description:(绑定 一账通账号的 关联关系)
     * @param bindOptRequest
     * @return com.howbuy.crm.account.client.response.Response<java.lang.String>
     * @author: haoran.zhang
     * @date: 2023/12/15 11:25
     * @since JDK 1.8
     */
    public Response<String> bindHboneTxAcct(HboneAcctRelationOptRequest bindOptRequest) {
        String custNo=bindOptRequest.getCustNo();
        String hboneNo=bindOptRequest.getHboneNo();

        //该一账通是否已被使用
        List<CmConscustForAnalyseBO>  hboneList
                =abnormalCustRepository.queryCustBOByHboneNo(hboneNo);
        //过滤掉当前客户号
        hboneList=hboneList.stream().filter(item->!item.getConscustno().equals(custNo)).collect(Collectors.toList());
        //提示已被使用
        if(CollectionUtils.isNotEmpty(hboneList)){
            List<String> custNoList=hboneList.stream().map(CmConscustForAnalyseBO::getConscustno).collect(Collectors.toList());
            return Response.fail(String.format("该一账通已被客户号：%s 绑定,无法再次绑定!", JSON.toJSONString(custNoList)));
        }

        CmConscustForAnalyseBO custBO=abnormalCustRepository.queryCustBOByCustNo(custNo);
        if(custBO==null){
            return Response.fail(String.format("客户号：%s 不存在!",custNo));
        }
        //该客户是否已经绑定该一账通
        if(hboneNo.equals(custBO.getHboneNo())){
            return Response.fail(String.format("客户号：%s 已经绑定该一账通,无需再次绑定!",custNo));
        }
        //该客户是否已经绑定其他一账通
        if(StringUtil.isNotBlank(custBO.getHboneNo()) && !hboneNo.equals(custBO.getHboneNo())){
            return Response.fail(String.format("客户号：%s 已经绑定一账通：%s,无法再次绑定!",custNo,custBO.getHboneNo()));
        }
        //校验结束，开始绑定
        HboneCustRelationOptReqVO bindOptVo=transferReqVoByRequest(bindOptRequest);
        return hboneCustRelationRepository.executeBind(bindOptVo);
    }



    /**
     * @description:(解绑 一账通交易账号的 关联关系)
     * @param bindOptRequest
     * @return com.howbuy.crm.account.client.response.Response<java.lang.String>
     * @throws Exception
     * @throws
     * @since JDK 1.8
     */
    public Response<String> unBindHboneTxAcct(HboneAcctRelationOptRequest bindOptRequest) {
        String custNo=bindOptRequest.getCustNo();
        String hboneNo=bindOptRequest.getHboneNo();
        Assert.notNull(custNo,"客户号不能为空");
        Assert.notNull(hboneNo,"一账通账号不能为空");


        CmConscustForAnalyseBO existPo=conscustCustomizeMapper.queryCustBOByCustNoAndHbone(custNo,hboneNo);
        if(existPo==null){
            return Response.fail(String.format("客户号：%s ,一账通账号：%s 不存在绑定关系,无需解绑!",custNo,hboneNo));
        }
        HboneCustRelationOptReqVO unBindOptVo=transferReqVoByRequest(bindOptRequest);
        return hboneCustRelationRepository.executeUnBind(unBindOptVo);
    }


    /**
     * @description:(req对象 转译 为 一账通客户关联关系操作对象)
     * @param bindOptRequest
     * @return com.howbuy.crm.account.service.req.custinfo.HkCustRelationOptReqVO
     * @author: haoran.zhang
     * @date: 2023/12/15 14:16
     * @since JDK 1.8
     */
    private HboneCustRelationOptReqVO transferReqVoByRequest(HboneAcctRelationOptRequest bindOptRequest){
        HboneCustRelationOptReqVO optReqVO=new HboneCustRelationOptReqVO();
        optReqVO.setCustNo(bindOptRequest.getCustNo());
        optReqVO.setHboneNo(bindOptRequest.getHboneNo());
        optReqVO.setOperator(bindOptRequest.getOperator());
        optReqVO.setOperateChannel(bindOptRequest.getOperateChannel());
        optReqVO.setOperateSource(bindOptRequest.getOperateSource());
        optReqVO.setRemark(bindOptRequest.getRemark());
        return optReqVO;
    }




}