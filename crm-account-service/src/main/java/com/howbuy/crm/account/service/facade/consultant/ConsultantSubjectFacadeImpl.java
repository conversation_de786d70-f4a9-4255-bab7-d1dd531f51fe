package com.howbuy.crm.account.service.facade.consultant;

import com.howbuy.crm.account.client.facade.consultant.ConsultantSubjectFacade;
import com.howbuy.crm.account.client.request.consultant.ConsultantSubjectRequest;
import com.howbuy.crm.account.client.response.Response;
import com.howbuy.crm.account.client.response.consultant.ConsultantSubjectResponse;
import lombok.extern.slf4j.Slf4j;
import org.apache.dubbo.config.annotation.DubboService;
import org.springframework.stereotype.Service;

/**
 * @description 投顾主体信息查询接口实现
 * <AUTHOR>
 * @date 2024-06-04 17:12:42
 */
@Slf4j
@Service
@DubboService
public class ConsultantSubjectFacadeImpl implements ConsultantSubjectFacade {
    
    /**
     * @description 查询投顾所属主体信息
     * @param request 投顾主体信息查询请求参数
     * @return 投顾主体信息查询响应结果
     */
    @Override
    public Response<ConsultantSubjectResponse> queryConsultantSubject(ConsultantSubjectRequest request) {
        log.info("查询投顾主体信息，入参：{}", request);
        
        try {
            // 参数校验（框架会自动处理，这里可以添加额外的业务校验）
            
            // 调用Service层查询投顾所属主体
//            String sszt = consultantSubjectService.queryConsultantSubject(request.getConscode());
            
            ConsultantSubjectResponse response = new ConsultantSubjectResponse();
            response.setSszt(null);
            
            log.info("查询投顾主体信息成功，出参：{}", response);
            return Response.ok(response);
        } catch (Exception e) {
            log.error("查询投顾主体信息异常", e);
            return Response.fail("查询投顾主体信息失败：" + e.getMessage());
        }
    }
} 