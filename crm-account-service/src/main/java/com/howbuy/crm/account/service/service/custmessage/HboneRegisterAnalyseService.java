/**
 * Copyright (c) 2023, <PERSON><PERSON><PERSON><PERSON> HOWBUY INVESTMENT MANAGEMENT Co., Ltd.
 * All right reserved.
 * <p>
 * THIS IS UNPUBLISHED PROPRIETARY SOURCE CODE OF HOWBUY INVESTMENT
 * MANAGEMENT CO., LTD.  THE CONTENTS OF THIS FILE MAY NOT BE DISCLOSED
 * TO THIRD PARTIES, COPIED OR DUPLICATED IN ANY FORM, IN WHOLE OR IN PART,
 * WITHOUT THE PRIOR WRITTEN PERMISSION OF HOWBUY INVESTMENT MANAGEMENT
 * CO., LTD.
 */
package com.howbuy.crm.account.service.service.custmessage;

import com.google.common.collect.Lists;
import com.howbuy.common.utils.StringUtil;
import com.howbuy.crm.account.client.enums.custinfo.AbnormaSceneTypeEnum;
import com.howbuy.crm.account.client.enums.custinfo.FullCustSourceEnum;
import com.howbuy.crm.account.dao.bo.custinfo.CmConscustForAnalyseBO;
import com.howbuy.crm.account.service.repository.custinfo.AbnormalCustRepository;
import com.howbuy.crm.account.service.vo.custinfo.AbnormalAnalyseResultVO;
import com.howbuy.crm.account.service.vo.custinfo.HboneMessageCustInfoVO;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * @description: ([一账通账户]-[一账通注册/非实名开通一账通] 消息)
 * <AUTHOR>
 * @date 2023/12/14 10:12
 * @since JDK 1.8
 */
@Service
@Slf4j
public class HboneRegisterAnalyseService implements CustMessageAnalyseService<HboneMessageCustInfoVO> {

    @Autowired
    private AbnormalCustRepository abnormalCustRepository;
    @Override
    public List<FullCustSourceEnum>  getSourceList() {
        return Lists.newArrayList(FullCustSourceEnum.HBONE_REGISTER);
    }

    @Override
    public AbnormalAnalyseResultVO<HboneMessageCustInfoVO> analyze(HboneMessageCustInfoVO analyseVo) {

        AbnormalAnalyseResultVO<HboneMessageCustInfoVO> returnAnalyseVo=AbnormalAnalyseResultVO.normalData(analyseVo);


        String mobileAreaCode= analyseVo.getMobileAreaCode();
        String mobileDigest=analyseVo.getMobileDigest();
        String investType=analyseVo.getInvestType();
//        ①：根据【一账通号A】的【手机区号+手机号】，查询是否存在全匹配的【投顾客户号】
        List<CmConscustForAnalyseBO> existByMobileList= abnormalCustRepository.queryCustListByMobile(investType,mobileAreaCode,mobileDigest);
        //若不存在，则不处理
        if(CollectionUtils.isEmpty(existByMobileList)){
            //MGM  还有  特殊逻辑处理 。
//            returnAnalyseVo.setNeedProcess(false);
            return  returnAnalyseVo;
        }
//        若存在，但不唯一（此时不自动绑定【一账通号】和【投顾客户号】），异常数据进“一账通异常客户表”，写入以下信息：
//        b、异常描述：匹配到多个投顾客户号
//        c、异常时间：取落入异常表的时间，格式yyyymmdd hh:mm:ss
//        匹配到的多个【投顾客户号】落“一账通异常客户待关联表”，与主表数据相对应。
        //存在多个匹配的投顾客户号
        if(existByMobileList.size()>1){
            return AbnormalAnalyseResultVO.notNormalData(analyseVo,AbnormaSceneTypeEnum.MATCH_MULTIPLE_CUST_NO,existByMobileList);
        }

//      判断②：匹配上的唯一【投顾客户号】是否已绑定【一账通号】
//        若已绑定（此时不自动绑定【一账通号】和【投顾客户号】），异常数据进“一账通异常客户表”，写入以下信息：
//        匹配异常的【一账通号】落“一账通异常客户主表”，写入字段：
//        a、异常来源：一账通注册
//        b、异常描述：匹配到的投顾客户号已被占用
//        c、异常时间：取落入异常表的时间，格式yyyymmdd hh:mm:ss
//        匹配到的【投顾客户号】落“一账通异常客户待关联表”，与主表数据相对应。
        CmConscustForAnalyseBO existCustPo=existByMobileList.get(0);
        if(StringUtil.isNotBlank(existCustPo.getHboneNo())){

            if(existCustPo.getHboneNo().equals(analyseVo.getHboneNo())){
                //标记为：无需处理
                returnAnalyseVo.setNeedProcess(false);
                //一账通号已绑定
                return returnAnalyseVo;
            }else{
                return AbnormalAnalyseResultVO.notNormalData(analyseVo,AbnormaSceneTypeEnum.MATCH_CUST_NO_OCCUPIED,existByMobileList);
            }


        }
        //匹配上的唯一【投顾客户号】未绑定【香港客户号】
        returnAnalyseVo.setProcessedCustInfo(existCustPo);
        return returnAnalyseVo;
    }

}