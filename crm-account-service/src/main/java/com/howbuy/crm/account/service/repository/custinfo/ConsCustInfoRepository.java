/**
 * Copyright (c) 2023, <PERSON><PERSON><PERSON><PERSON> HOWBUY INVESTMENT MANAGEMENT Co., Ltd.
 * All right reserved.
 * <p>
 * THIS IS UNPUBLISHED PROPRIETARY SOURCE CODE OF HOWBUY INVESTMENT
 * MANAGEMENT CO., LTD.  THE CONTENTS OF THIS FILE MAY NOT BE DISCLOSED
 * TO THIRD PARTIES, COPIED OR DUPLICATED IN ANY FORM, IN WHOLE OR IN PART,
 * WITHOUT THE PRIOR WRITTEN PERMISSION OF HOWBUY INVESTMENT MANAGEMENT
 * CO., LTD.
 */
package com.howbuy.crm.account.service.repository.custinfo;

import com.alibaba.fastjson.JSON;
import com.github.pagehelper.Page;
import com.github.pagehelper.PageHelper;
import com.howbuy.common.utils.DateUtil;
import com.howbuy.common.utils.StringUtil;
import com.howbuy.crm.account.client.enums.YesOrNoEnum;
import com.howbuy.crm.account.client.response.Response;
import com.howbuy.crm.account.client.response.custinfo.CmConsCustVO;
import com.howbuy.crm.account.client.response.custinfo.CmConsCustWithCipherVO;
import com.howbuy.crm.account.dao.bo.custinfo.CmConsCustSimpleBO;
import com.howbuy.crm.account.dao.bo.custinfo.CmCustHisConsultantBO;
import com.howbuy.crm.account.dao.mapper.CommonMapper;
import com.howbuy.crm.account.dao.mapper.custinfo.*;
import com.howbuy.crm.account.dao.mapper.customize.custinfo.ConscustCustomizeMapper;
import com.howbuy.crm.account.dao.po.custinfo.*;
import com.howbuy.crm.account.dao.req.custinfo.CreateConsCustReqVO;
import com.howbuy.crm.account.dao.req.custinfo.CustSimpleSearchVO;
import com.howbuy.crm.account.dao.req.custinfo.PageCustSimpleReqVO;
import com.howbuy.crm.account.dao.req.custinfo.UpdateConsCustReqVO;
import com.howbuy.crm.account.service.commom.constant.Constants;
import com.howbuy.crm.account.service.commom.constant.SequenceConstants;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.dao.DuplicateKeyException;
import org.springframework.stereotype.Component;
import org.springframework.transaction.annotation.Propagation;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.Assert;

import java.util.Date;
import java.util.List;

/**
 * <AUTHOR>
 * @description: (投顾客户 repository)
 * @date 2023/12/8 15:31
 * @since JDK 1.8
 */
@Component
@Slf4j
@Transactional(rollbackFor = Exception.class, propagation = Propagation.SUPPORTS)
public class ConsCustInfoRepository {

    @Autowired
    private CmConscustMapper conscustMapper;
    @Autowired
    private CmConscustCipherMapper conscustCipherMapper;
    @Autowired
    private CmConscusthisCipherMapper hisCipherMapper;
    @Autowired
    private CmConscusthisMapper hisMapper;
    @Autowired
    private ConscustCustomizeMapper conscustCustomizeMapper;
    @Autowired
    private CmCustconstantMapper custconstantMapper;
    @Autowired
    private CommonMapper commonMapper;


    /**
     * @param custNo
     * @return com.howbuy.crm.account.dao.po.custinfo.CmCustconstantPO
     * @description:(根据客户号 获取 所属投顾 对象)
     * @author: haoran.zhang
     * @date: 2023/12/27 17:15
     * @since JDK 1.8
     */
    public CmCustconstantPO selectCustConsRelationByCustNo(String custNo) {
        return custconstantMapper.selectByPrimaryKey(custNo);
    }


    /**
     * 投顾客户  新增修改 操作集合 表
     * 与划转相关
     */
    public String getCustOpreationId() {
        return commonMapper.getSeqValue(SequenceConstants.SEQ_CONSCUST_OPERATION);
    }


    @Transactional(rollbackFor = Exception.class, propagation = Propagation.REQUIRED)
    public String createConsCust(CreateConsCustReqVO reqVo) {

        CmConscustPO record = reqVo.getConscustPO();
        // 对省市县的处理，针对国籍地区=港/澳/台
        dealProvCityCountyCode(record);

        //生成投顾客户号
        String custNo = generateConsCustNo();
        record.setConscustno(custNo);
        Date curDate = new Date();
        String curDateString = DateUtil.date2String(curDate, DateUtil.SHORT_DATE_PATTERN);
        record.setCreateTimestamp(curDate);
        record.setRegdt(curDateString);
        //正常状态
        record.setConscuststatus(YesOrNoEnum.NO.getCode());
        log.info("创建投顾客户信息，待新增客户属性:{}", JSON.toJSONString(record));
        int insertCount;
        try {
            insertCount = conscustMapper.insert(record);
        }catch (DuplicateKeyException e){
            log.error("创建投顾客户信息插入失败，custNo:" + custNo, e);
            // 重新生成客户号
            custNo = generateConsCustNo();
            record.setConscustno(custNo);
            insertCount = conscustMapper.insert(record);
        }

        //cipher 加密信息
        CmConscustCipherPO cipherPo = reqVo.getCipherPO();
        cipherPo.setConscustno(custNo);
        int insertCipherCount = conscustCipherMapper.insert(cipherPo);

        log.info("创建投顾客户信息，custNo:{},insert客户条数:{},insert客户加密信息条数:{}", custNo, insertCount, insertCipherCount);

        String appserialNo = commonMapper.getSeqValue(SequenceConstants.SEQ_CUST_UPDATE_SERIALNO);
        CmConscusthisPO hisPo = new CmConscusthisPO();
        BeanUtils.copyProperties(record, hisPo);
        //补充 变更相关信息
        hisPo.setConscustno(custNo);
        hisPo.setAppserialno(appserialNo);
        hisPo.setCreator(reqVo.getOperator());
        hisPo.setStimestamp(curDate);
        //插入变更前 数据 备份。
        hisMapper.insert(hisPo);

        CmConscusthisCipherPO hisCipherPo = new CmConscusthisCipherPO();
        BeanUtils.copyProperties(cipherPo, hisCipherPo);
        hisCipherPo.setAppserialno(appserialNo);
        hisCipherPo.setConscustno(custNo);
        //插入变更前 cipher数据 备份。
        hisCipherMapper.insert(hisCipherPo);
        log.info("创建投顾客户信息，客户号：{}， 记录his信息 appSerialNo:{}", custNo, appserialNo);

        //投顾客户关系表
        CmCustconstantPO relationPo = new CmCustconstantPO();
        relationPo.setCustno(custNo);
        relationPo.setConscode(reqVo.getConsCode());
        relationPo.setStartdt(curDateString);
        relationPo.setRecstat(YesOrNoEnum.YES.getCode());
        relationPo.setCreator(reqVo.getOperator());
        relationPo.setCredt(curDateString);
        relationPo.setBinddate(curDate);
        relationPo.setBeforehisid(null);
        relationPo.setOperateDate(curDate);
        custconstantMapper.insert(relationPo);


        return custNo;
    }


    /**
     * @param custNo
     * @return com.howbuy.crm.account.dao.po.custinfo.CmConscustPO
     * @description:(查询投顾客户信息)
     * @author: haoran.zhang
     * @date: 2023/12/19 16:02
     * @since JDK 1.8
     */
    public CmConscustPO selectPoByCustNo(String custNo) {
        return conscustMapper.selectByPrimaryKey(custNo);
    }


    /**
     * @param updatePo
     * @return com.howbuy.crm.account.client.response.Response<java.lang.String>
     * @description:(更新投顾客户)
     * @author: haoran.zhang
     * @date: 2023/12/18 17:15
     * @since JDK 1.8
     */
    @Transactional(rollbackFor = Exception.class, propagation = Propagation.REQUIRED)
    public Response<String> updateConsCust(UpdateConsCustReqVO updatePo) {
        Assert.notNull(updatePo, "更新投顾客户信息参数不能为空");
        Assert.notNull(updatePo.getCustNo(), "更新投顾客户信息参数custNo不能为空");

        String custNo = updatePo.getCustNo();
        Date curDate = new Date();

        CmConscustCipherPO updateCipherPo = updatePo.getCipherPO();
        CmConscustPO updateCustPo = updatePo.getConscustPO();

        // 对省市县的处理，针对国籍地区=港/澳/台
        dealProvCityCountyCode(updateCustPo);

        //历史表备份当前客户数据
        //TODO: 历史更新逻辑。 consumer端查询当前，再全量更新。 所以 备份表保留了全量数据。无法区分变更数据。
        //TODO: 当前逻辑仍需要修改。  担心： 有在his备份表，做业务逻辑处理。
        //TODO:待确认：是否修改当前产线设计。
        String appserialNo = commonMapper.getSeqValue(SequenceConstants.SEQ_CUST_UPDATE_SERIALNO);
        Assert.notNull(updatePo.getOperator());
        CmConscusthisPO hisPo = new CmConscusthisPO();
        BeanUtils.copyProperties(updateCustPo, hisPo);
        //补充 变更相关信息
        hisPo.setConscustno(custNo);
        hisPo.setAppserialno(appserialNo);
        hisPo.setCreator(updatePo.getOperator());
        hisPo.setExplanation(updatePo.getExplanation());
        hisPo.setSpecialreason(updatePo.getSpecialReason());
        hisPo.setChecker(updatePo.getChecker());
        hisPo.setStimestamp(curDate);
        //插入变更前 数据 备份。
        hisMapper.insert(hisPo);

        CmConscusthisCipherPO hisCipherPo = new CmConscusthisCipherPO();
        BeanUtils.copyProperties(updateCipherPo, hisCipherPo);
        hisCipherPo.setAppserialno(appserialNo);
        hisCipherPo.setConscustno(custNo);
        //插入变更前 cipher数据 备份。
        hisCipherMapper.insert(hisCipherPo);


        //更新 投顾客户信息
        updateCustPo.setConscustno(custNo);
        conscustMapper.updateByPrimaryKeySelective(updateCustPo);
        //更新 投顾客户信息--密文
        updateCipherPo.setConscustno(custNo);
        conscustCipherMapper.updateByPrimaryKeySelective(updateCipherPo);
        log.info("客户信息变更，appSerialNo:{},变更属性：{}", appserialNo, JSON.toJSONString(updatePo));

        return Response.ok();

    }


    /**
     * @param updateCustPo
     * @return void
     * @description: 如果入参中，投顾客户的省市县为空，且国籍地区=港/澳/台，则对省市县赋默认值（海外2.3中 补充的特殊需求）
     * @author: jin.wang03
     * @date: 2024/11/8 13:18
     * @since JDK 1.8
     */
    private static void dealProvCityCountyCode(CmConscustPO updateCustPo) {
        if (StringUtils.isEmpty(updateCustPo.getProvcode())) {
            if (Constants.HK.equals(updateCustPo.getNationCode())) {
                updateCustPo.setProvcode(Constants.HK_CODE);
                updateCustPo.setCitycode(Constants.HK_CODE);
                updateCustPo.setCountyCode(Constants.HK_CODE);
            }
            if (Constants.MO.equals(updateCustPo.getNationCode())) {
                updateCustPo.setProvcode(Constants.MO_CODE);
                updateCustPo.setCitycode(Constants.MO_CODE);
                updateCustPo.setCountyCode(Constants.MO_CODE);
            }
            if (Constants.TW.equals(updateCustPo.getNationCode())) {
                updateCustPo.setProvcode(Constants.TW_CODE);
                updateCustPo.setCitycode(Constants.TW_CODE);
                updateCustPo.setCountyCode(Constants.TW_CODE);
            }
        }
    }


    /**
     * 根据sequence生成 --> 客户号
     * 历史逻辑 1+8位数字+1位校验码
     */
    @Transactional(rollbackFor = Exception.class, propagation = Propagation.REQUIRED)
    public String generateConsCustNo() {
        String seqValue = commonMapper.getSeqValue(SequenceConstants.SEQ_CM_CONSCUST);
        log.info("生成客户号，获取seqValue:{}", seqValue);
        // 固定长度的值
        String fixSeqValue = StringUtil.fillZero(seqValue, 8);
        String returnCustNo= new StringBuffer("1").append(fixSeqValue).append(
                StringUtil.getValiateCode("1" + fixSeqValue)).toString();
        log.info("生成客户号，校验位处理后，返回客户号:{}", returnCustNo);
        return returnCustNo;
    }


    /**
     * @param custNo
     * @return java.lang.String
     * @throws
     * @description: (根据客户号查询 一账通号)
     * @since JDK 1.8
     */
    public String selectHboneNoByCustNo(String custNo) {
        return conscustCustomizeMapper.selectHboneNoByCustNo(custNo);
    }

    /**
     * @param custNo
     * @return java.lang.String
     * @throws
     * @description: (根据客户号查询 投顾consCode)
     * @since JDK 1.8
     */
    public String selectConsCodeByCustNo(String custNo) {
        return conscustCustomizeMapper.selectConsCodeByCustNo(custNo);
    }


    /**
     * @param custNo
     * @return com.howbuy.crm.account.client.response.custinfo.CmConsCustVO
     * @description:(简单信息，包含 : 客户基本信息、hboneNo、香港客户信息、投顾code)
     * @author: haoran.zhang
     * @date: 2023/12/13 16:53
     * @since JDK 1.8
     */
    public CmConsCustSimpleBO queryCustSimpleInfo(String custNo) {
        return conscustCustomizeMapper.queryCustSimpleInfo(custNo);
    }


    /**
     * @param custNo
     * @return com.howbuy.crm.account.client.response.custinfo.CmConsCustVO
     * @description:(根据 投顾客户号  查询投顾客户信息)
     * @author: haoran.zhang
     * @date: 2023/12/13 16:53
     * @since JDK 1.8
     */
    public CmConsCustVO queryCustInfoByCustNo(String custNo) {
        CmConscustPO custPo = conscustMapper.selectByPrimaryKey(custNo);
        if (custPo == null) {
            return null;
        }
        CmConsCustVO custVO = new CmConsCustVO();
        BeanUtils.copyProperties(custPo, custVO);
        return custVO;
    }


    /**
     * @param custNo
     * @return com.howbuy.crm.account.client.response.custinfo.CmConsCustWithCipherVO
     * @throws
     * @description: (根据客户号查询 客户信息 包含密文信息)
     * @since JDK 1.8
     */
    public CmConsCustWithCipherVO queryCustWithCipherByCustNo(String custNo) {
        CmConsCustVO custVo = queryCustInfoByCustNo(custNo);
        if (custVo == null) {
            return null;
        }
        CmConsCustWithCipherVO cipherVo = new CmConsCustWithCipherVO();
        BeanUtils.copyProperties(custVo, cipherVo);
        //补充 密文信息
        CmConscustCipherPO cipherPo = conscustCipherMapper.selectByPrimaryKey(custNo);
        if (cipherPo != null) {
            BeanUtils.copyProperties(cipherPo, cipherVo);
        }
        return cipherVo;

    }


    /**
     * @param hboneNo 一账通号
     * @return com.howbuy.crm.account.client.response.custinfo.CmConsCustVO 投顾客户信息
     * @description: 根据一账通号查询投顾客户信息
     * @author: jin.wang03
     * @date: 2023/12/18 19:08
     * @since JDK 1.8
     */
    public CmConsCustVO queryCustInfoByHboneNo(String hboneNo) {
        CmConscustPO custPo = conscustMapper.queryCustByHboneNo(hboneNo);
        if (custPo == null) {
            return null;
        }
        CmConsCustVO custVO = new CmConsCustVO();
        BeanUtils.copyProperties(custPo, custVO);
        return custVO;
    }

    /**
     * @param hboneNo
     * @return com.howbuy.crm.account.dao.bo.custinfo.CmCustconstantSimpleBO
     * @description:根据一账通号查询投顾客户简单信息
     * <AUTHOR>
     * @date 2024/9/9 14:15
     * @since JDK 1.8
     */
    public List<CmConsCustSimpleBO> queryCustSimpleByHboneNo(String hboneNo) {
        return conscustCustomizeMapper.queryCustSimpleByHboneNo(hboneNo);
    }

    /**
     * @param reqVO
     * @return com.howbuy.crm.account.dao.bo.custinfo.CmCustconstantSimpleBO
     * @description:根据查询对象vo查询投顾客户简单信息
     * <AUTHOR>
     * @date 2024/9/9 14:15
     * @since JDK 1.8
     */
    public Page<CmConsCustSimpleBO> queryCustSimpleBySearchVo(CustSimpleSearchVO reqVO) {
        PageHelper.startPage(reqVO.getPage(), reqVO.getRows());
        return conscustCustomizeMapper.queryCustSimpleBySearchVo(reqVO);
    }

    /**
     * 查询客户历史所属投顾列表
     *
     * @param conscustno 客户号
     * @return CmCustHisConsultantBO
     */
    public List<CmCustHisConsultantBO> queryCustHisConsultantList(String conscustno) {
        return conscustCustomizeMapper.queryCustHisConsultantList(conscustno);
    }

    /**
     * @param reqVO
     * @return java.util.List<com.howbuy.crm.account.dao.bo.custinfo.CmConsCustSimpleBO>
     * @description:(根据vo分页查询投顾客户简单信息)
     * <AUTHOR>
     * @date 2024/9/24 18:07
     * @since JDK 1.8
     */
    public List<CmConsCustSimpleBO> pageQueryCustSimpleByVo(PageCustSimpleReqVO reqVO) {
        PageHelper.startPage(reqVO.getPage(), reqVO.getRows());
        return conscustCustomizeMapper.pageQueryCustSimpleByVo(reqVO);
    }

    /**
     * 根据vo分页查询投顾客户简单信息(投顾所属部门&子部门)
     *
     * @param reqVO 请求vo
     * @return List<CmConsCustSimpleBO>
     */
    public List<CmConsCustSimpleBO> pageQueryCustSimpleByVoAndOutletCodes(PageCustSimpleReqVO reqVO) {
        PageHelper.startPage(reqVO.getPage(), reqVO.getRows());
        return conscustCustomizeMapper.pageQueryCustSimpleByVoAndOutletCodes(reqVO);
    }

}