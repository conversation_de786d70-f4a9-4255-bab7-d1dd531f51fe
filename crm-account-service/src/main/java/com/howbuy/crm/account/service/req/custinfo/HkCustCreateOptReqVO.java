/**
 * Copyright (c) 2023, <PERSON><PERSON><PERSON><PERSON> HOWBUY INVESTMENT MANAGEMENT Co., Ltd.
 * All right reserved.
 * <p>
 * THIS IS UNPUBLISHED PROPRIETARY SOURCE CODE OF HOWBUY INVESTMENT
 * MANAGEMENT CO., LTD.  THE CONTENTS OF THIS FILE MAY NOT BE DISCLOSED
 * TO THIRD PARTIES, COPIED OR DUPLICATED IN ANY FORM, IN WHOLE OR IN PART,
 * WITHOUT THE PRIOR WRITTEN PERMISSION OF HOWBUY INVESTMENT MANAGEMENT
 * CO., LTD.
 */
package com.howbuy.crm.account.service.req.custinfo;

import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * @description: 香港交易账号 插入操作请求类
 * <AUTHOR>
 * @date 2023/12/18 14:20
 * @since JDK 1.8
 */
@Data
@EqualsAndHashCode(callSuper = true)
public class HkCustCreateOptReqVO  extends  CustRelationOptReqVO{

    /**
     * 香港交易账号
     */
    private String hkTxAcctNo;


    /**
     * ebrokerId- 不接受参数处理
     */
    private String ebrokerId;

}