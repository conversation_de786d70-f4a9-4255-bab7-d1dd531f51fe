/**
 * Copyright (c) 2023, <PERSON><PERSON><PERSON><PERSON> HOWBUY INVESTMENT MANAGEMENT Co., Ltd.
 * All right reserved.
 * <p>
 * THIS IS UNPUBLISHED PROPRIETARY SOURCE CODE OF HOWBUY INVESTMENT
 * MANAGEMENT CO., LTD.  THE CONTENTS OF THIS FILE MAY NOT BE DISCLOSED
 * TO THIRD PARTIES, COPIED OR DUPLICATED IN ANY FORM, IN WHOLE OR IN PART,
 * WITHOUT THE PRIOR WRITTEN PERMISSION OF HOWBUY INVESTMENT MANAGEMENT
 * CO., LTD.
 */
package com.howbuy.crm.account.service.message.dto;

import lombok.Data;

import java.io.Serializable;

/**
 * @description: (账户中心 一账通消息 [TOPIC_OPEN_ACC-一账通客户开户消息]-[OPEN_TX_ACC-开通分销交易账户]   公共解析 dto)
 * <AUTHOR>
 * @date 2024年1月3日 16:04:03
 * @since JDK 1.8
 */
@Data
public class HboneOpenDisAcctMessageDTO implements Serializable {

    private static final long serialVersionUID = 1L;

//    备注：provCode、cityCode当柜台开通个人交易账户时才有

    /**
     * 一账通账号(hboneNo)
     */
    private String hboneNo;

    /**
     * 分销机构号(disCode)
     */
    private String disCode;

    /**
     * 分销交易账号(disTxAcc)
     */
    private String disTxAcc;

    /**
     * 开户网点号(outletCode)
     */
    private String outletCode;

    /**
     * 省份(provCode)
     */
    private String provCode;

    /**
     * 城市(cityCode)
     */
    private String cityCode;

    /**
     * 开户日期（regDate） yyyyMMdd
     */
    private String regDate;

    /**
     * 香港客户号（hkCustNo）
     */
    private String hkCustNo;

    /**
     * 年收入（incLevel）
     */
    private String incLevel;

     /**
     * 职业代码（vocation）：码值
     */
    private String vocation;

}