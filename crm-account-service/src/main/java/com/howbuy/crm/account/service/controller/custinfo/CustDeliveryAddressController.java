/**
 * Copyright (c) 2023, <PERSON><PERSON><PERSON><PERSON> HOWBUY INVESTMENT MANAGEMENT Co., Ltd.
 * All right reserved.
 * <p>
 * THIS IS UNPUBLISHED PROPRIETARY SOURCE CODE OF HOWBUY INVESTMENT
 * MANAGEMENT CO., LTD.  THE CONTENTS OF THIS FILE MAY NOT BE DISCLOSED
 * TO THIRD PARTIES, COPIED OR DUPLICATED IN ANY FORM, IN WHOLE OR IN PART,
 * WITHOUT THE PRIOR WRITTEN PERMISSION OF HOWBUY INVESTMENT MANAGEMENT
 * CO., LTD.
 */
package com.howbuy.crm.account.service.controller.custinfo;

import com.howbuy.crm.account.client.request.custinfo.ConsCustDeliveryAddressRequest;
import com.howbuy.crm.account.client.response.Response;
import com.howbuy.crm.account.client.response.custinfo.CmConscustDeliveryAddressSensitiveInfoVO;
import com.howbuy.crm.account.client.response.custinfo.CmConscustDeliveryAddressVO;
import com.howbuy.crm.account.client.response.custinfo.HboneDeliveryAddrInfoVO;
import com.howbuy.crm.account.service.service.custinfo.CustDeliveryAddressService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import java.util.List;

/**
 * @description: (crm投顾客户收货地址信息)
 * <AUTHOR>
 * @date 2023/12/13 16:08
 * @since JDK 1.8
 */
@RestController
@RequestMapping("/custdeliveryaddress")
public class CustDeliveryAddressController {


    @Autowired
    private CustDeliveryAddressService custDeliveryAddressService;

    /**
     * @api {POST} /custdeliveryaddress/comparehbonedelivery compareHboneReceiver()
     * @apiVersion 1.0.0
     * @apiGroup CustDeliveryAddressController
     * @apiName compareHboneReceiver()
     * @apiDescription 投顾版收货地址 和 客户版收货地址，比较是否一致
     * @apiParam (请求体) {Number} id 主键
     * @apiParam (请求体) {String} custNo 投顾客户编号
     * @apiParam (请求体) {String} deliveryName 收货人姓名
     * @apiParam (请求体) {String} deliveryMobile 收货人电话
     * @apiParam (请求体) {String} deliveryProvCode 收货省
     * @apiParam (请求体) {String} deliveryCityCode 收货市
     * @apiParam (请求体) {String} deliveryCountyCode 收货县
     * @apiParam (请求体) {String} deliveryAddr 收货详细地址
     * @apiParam (请求体) {String} deliveryRemark 备注
     * @apiParam (请求体) {String} operator 操作人
     * @apiParamExample 请求体示例
     * {"deliveryCityCode":"5yx","deliveryRemark":"ZzrGDcB","custNo":"G8oDbxL","deliveryMobile":"vgLCLGk3","deliveryAddr":"8Z2t0d","id":3398.294646186152,"deliveryProvCode":"nnzEU","deliveryCountyCode":"XeQEX3jrm","deliveryName":"eb","operator":"zf"}
     * @apiSuccess (响应结果) {String} code 状态码
     * @apiSuccess (响应结果) {String} description 描述信息
     * @apiSuccess (响应结果) {String} data 数据封装
     * @apiSuccessExample 响应结果示例
     * {"code":"58DGZxgL0","data":"DiYuyWte7","description":"UrR"}
     */
    @PostMapping("/comparehbonedelivery")
    @ResponseBody
    public Response<String> compareHboneReceiver(@RequestBody ConsCustDeliveryAddressRequest request) {
        return custDeliveryAddressService.compareHboneReceiver(request);
    }


    /**
     * @api {POST} /custdeliveryaddress/savedeliverydata saveDeliveryData()
     * @apiVersion 1.0.0
     * @apiGroup CustDeliveryAddressController
     * @apiName saveDeliveryData()
     * @apiDescription 保存收货地址
     * @apiParam (请求体) {Number} id 主键
     * @apiParam (请求体) {String} custNo 投顾客户编号
     * @apiParam (请求体) {String} deliveryName 收货人姓名
     * @apiParam (请求体) {String} deliveryMobile 收货人电话
     * @apiParam (请求体) {String} deliveryProvCode 收货省
     * @apiParam (请求体) {String} deliveryCityCode 收货市
     * @apiParam (请求体) {String} deliveryCountyCode 收货县
     * @apiParam (请求体) {String} deliveryAddr 收货详细地址
     * @apiParam (请求体) {String} deliveryRemark 备注
     * @apiParam (请求体) {String} operator 操作人
     * @apiParamExample 请求体示例
     * {"deliveryCityCode":"4V8","deliveryRemark":"T4JHZrOpIH","custNo":"gWA9pXN","deliveryMobile":"2","deliveryAddr":"Xm1ULAA","id":7035.647289090536,"deliveryProvCode":"glI","deliveryCountyCode":"VE14ldlnc","deliveryName":"Q1p7","operator":"AdEpL"}
     * @apiSuccess (响应结果) {String} code 状态码
     * @apiSuccess (响应结果) {String} description 描述信息
     * @apiSuccess (响应结果) {String} data 数据封装
     * @apiSuccessExample 响应结果示例
     * {"code":"M6W","data":"0","description":"7mAKn"}
     */
    @PostMapping("/savedeliverydata")
    @ResponseBody
    public Response<String> saveDeliveryData(@RequestBody ConsCustDeliveryAddressRequest request) {
        return custDeliveryAddressService.saveDeliveryData(request);
    }

    /**
     * @api {POST} /custdeliveryaddress/updatedeliverydata updateDeliveryData()
     * @apiVersion 1.0.0
     * @apiGroup CustDeliveryAddressController
     * @apiName updateDeliveryData()
     * @apiDescription 更新收货地址
     * @apiParam (请求体) {Number} id 主键
     * @apiParam (请求体) {String} custNo 投顾客户编号
     * @apiParam (请求体) {String} deliveryName 收货人姓名
     * @apiParam (请求体) {String} deliveryMobile 收货人电话
     * @apiParam (请求体) {String} deliveryProvCode 收货省
     * @apiParam (请求体) {String} deliveryCityCode 收货市
     * @apiParam (请求体) {String} deliveryCountyCode 收货县
     * @apiParam (请求体) {String} deliveryAddr 收货详细地址
     * @apiParam (请求体) {String} deliveryRemark 备注
     * @apiParam (请求体) {String} operator 操作人
     * @apiParamExample 请求体示例
     * {"deliveryCityCode":"2O","deliveryRemark":"o78heBAJ6J","custNo":"rJKuj","deliveryMobile":"i","deliveryAddr":"D","id":8559.307580819694,"deliveryProvCode":"dD2fXiJC","deliveryCountyCode":"xeoVqS2vxw","deliveryName":"wLFz","operator":"OY"}
     * @apiSuccess (响应结果) {String} code 状态码
     * @apiSuccess (响应结果) {String} description 描述信息
     * @apiSuccess (响应结果) {String} data 数据封装
     * @apiSuccessExample 响应结果示例
     * {"code":"hLqO7BLFn2","data":"Vxd","description":"fK1njy"}
     */
    @PostMapping("/updatedeliverydata")
    @ResponseBody
    public Response<String> updateDeliveryData(@RequestBody ConsCustDeliveryAddressRequest request) {
        return custDeliveryAddressService.updateDeliveryData(request);
    }

    /**
     * @api {POST} /custdeliveryaddress/deletedeliverydata deleteDeliveryData()
     * @apiVersion 1.0.0
     * @apiGroup CustDeliveryAddressController
     * @apiName deleteDeliveryData()
     * @apiDescription 删除收货地址
     * @apiParam (请求体) {Number} id 主键
     * @apiParam (请求体) {String} custNo 投顾客户编号
     * @apiParam (请求体) {String} deliveryName 收货人姓名
     * @apiParam (请求体) {String} deliveryMobile 收货人电话
     * @apiParam (请求体) {String} deliveryProvCode 收货省
     * @apiParam (请求体) {String} deliveryCityCode 收货市
     * @apiParam (请求体) {String} deliveryCountyCode 收货县
     * @apiParam (请求体) {String} deliveryAddr 收货详细地址
     * @apiParam (请求体) {String} deliveryRemark 备注
     * @apiParam (请求体) {String} operator 操作人
     * @apiParamExample 请求体示例
     * {"deliveryCityCode":"VjAk5sIj8","deliveryRemark":"x","custNo":"iOTLB3WA","deliveryMobile":"1P","deliveryAddr":"I","id":8509.081722290093,"deliveryProvCode":"AL87Y","deliveryCountyCode":"4vml","deliveryName":"oxdr8x8eg","operator":"sbrnPU"}
     * @apiSuccess (响应结果) {String} code 状态码
     * @apiSuccess (响应结果) {String} description 描述信息
     * @apiSuccess (响应结果) {String} data 数据封装
     * @apiSuccessExample 响应结果示例
     * {"code":"sqeFTYDa0G","data":"bwR1","description":"5Y6nACot"}
     */
    @PostMapping("/deletedeliverydata")
    @ResponseBody
    public Response<String> deleteDeliveryData(@RequestBody ConsCustDeliveryAddressRequest request) {
        return custDeliveryAddressService.deleteDeliveryData(request);
    }

    /**
     * @api {GET} /custdeliveryaddress/gethbonedeliveryaddrbyhboneno getHboneDeliveryAddrByHboneNo()
     * @apiVersion 1.0.0
     * @apiGroup CustDeliveryAddressController
     * @apiName getHboneDeliveryAddrByHboneNo()
     * @apiDescription 根据一账通获取客户收货地址
     * @apiParam (请求参数) {String} hboneNo
     * @apiParamExample 请求参数示例
     * hboneNo=5wQKy04cnm
     * @apiSuccess (响应结果) {String} code 状态码
     * @apiSuccess (响应结果) {String} description 描述信息
     * @apiSuccess (响应结果) {Array} data 数据封装
     * @apiSuccess (响应结果) {String} data.addrId 主键
     * @apiSuccess (响应结果) {String} data.hboneNo 一账通号
     * @apiSuccess (响应结果) {String} data.disCode 分销机构号
     * @apiSuccess (响应结果) {String} data.outletCode 网点代码
     * @apiSuccess (响应结果) {String} data.deliveryName 收货人姓名
     * @apiSuccess (响应结果) {String} data.deliveryMobileAreaCode 收货人手机号区号
     * @apiSuccess (响应结果) {String} data.deliveryMobileDigest 收货人手机号-摘要
     * @apiSuccess (响应结果) {String} data.deliveryMobileMask 收货人手机号-掩码
     * @apiSuccess (响应结果) {String} data.deliveryCountryCode 收货地址-国家/地区代码
     * @apiSuccess (响应结果) {String} data.deliveryProvCode 收货地址-省份代码
     * @apiSuccess (响应结果) {String} data.deliveryCityCode 收货地址-城市代码
     * @apiSuccess (响应结果) {String} data.deliveryCountyCode 收货地址-县代码
     * @apiSuccess (响应结果) {String} data.deliveryAddrDigest 收货地址-摘要
     * @apiSuccess (响应结果) {String} data.deliveryAddrMask 收货地址-掩码
     * @apiSuccess (响应结果) {String} data.defaultAddr 默认地址 0-否 1-是
     * @apiSuccess (响应结果) {Number} data.stimestamp 创建时间戳
     * @apiSuccess (响应结果) {Number} data.updatedStimestamp 更新时间戳
     * @apiSuccess (响应结果) {String} data.deliveryProvName 收货地址-省名称
     * @apiSuccess (响应结果) {String} data.deliveryCityName 收货地址-市名称
     * @apiSuccess (响应结果) {String} data.deliveryCountyName 收货地址-县名称
     * @apiSuccessExample 响应结果示例
     * {"code":"i6Sf17LgY","data":[{"deliveryAddrDigest":"nwCNVVPr4D","deliveryCountryCode":"aJu","stimestamp":109709962894,"updatedStimestamp":399757672107,"disCode":"N6GECf3","deliveryCountyCode":"c0o","deliveryCityName":"yl6V7SDX","deliveryCountyName":"fCUtQ3cgL","deliveryMobileAreaCode":"cD","deliveryMobileMask":"K9rA","deliveryCityCode":"gH0M","deliveryMobileDigest":"7IoPq3YT4","defaultAddr":"F0WSLLog","addrId":"4mJYo","deliveryProvCode":"jXpzC","deliveryName":"S24NA9I","outletCode":"CmGYL3kDve","deliveryAddrMask":"1b4UKro","deliveryProvName":"7Jss94","hboneNo":"O"}],"description":"xz83l9P"}
     */
    @ResponseBody
    @GetMapping("/gethbonedeliveryaddrbyhboneno")
    public Response<List<HboneDeliveryAddrInfoVO>> getHboneDeliveryAddrByHboneNo(@RequestParam(name="hboneNo") String hboneNo) {
        return custDeliveryAddressService.getHboneDeliveryAddrByHboneNo(hboneNo);
    }

    /**
     * @api {GET} /custdeliveryaddress/getcustdeliveryaddrbycustno getCustDeliveryAddrByCustNo()
     * @apiVersion 1.0.0
     * @apiGroup CustDeliveryAddressController
     * @apiName getCustDeliveryAddrByCustNo()
     * @apiDescription 根据投顾客户号获取投顾版收货地址
     * @apiParam (请求参数) {String} custNo
     * @apiParamExample 请求参数示例
     * custNo=l
     * @apiSuccess (响应结果) {String} code 状态码
     * @apiSuccess (响应结果) {String} description 描述信息
     * @apiSuccess (响应结果) {Array} data 数据封装
     * @apiSuccess (响应结果) {Number} data.id ID
     * @apiSuccess (响应结果) {String} data.conscustno 投顾客户号
     * @apiSuccess (响应结果) {String} data.receiverName 收货人姓名
     * @apiSuccess (响应结果) {String} data.mobileDigest 收货人手机号摘要
     * @apiSuccess (响应结果) {String} data.mobileMask 收货人手机号掩码
     * @apiSuccess (响应结果) {String} data.mobileCipher 收货人手机号密文
     * @apiSuccess (响应结果) {String} data.provCode 省编码
     * @apiSuccess (响应结果) {String} data.cityCode 市编码
     * @apiSuccess (响应结果) {String} data.countyCode 区编码
     * @apiSuccess (响应结果) {String} data.addrDigest 详细地址摘要
     * @apiSuccess (响应结果) {String} data.addrMask 详细地址掩码
     * @apiSuccess (响应结果) {String} data.addrCipher 详细地址密文
     * @apiSuccess (响应结果) {String} data.remark 备注
     * @apiSuccess (响应结果) {String} data.recStat 记录有效状态（1-正常  0-删除）
     * @apiSuccess (响应结果) {String} data.creator 创建人
     * @apiSuccess (响应结果) {Number} data.createTimestamp 创建时间
     * @apiSuccess (响应结果) {String} data.modifier 修改人
     * @apiSuccess (响应结果) {Number} data.modifyTimestamp 修改时间
     * @apiSuccess (响应结果) {String} data.provName 省名称
     * @apiSuccess (响应结果) {String} data.cityName 市名称
     * @apiSuccess (响应结果) {String} data.countyName 县名称
     * @apiSuccessExample 响应结果示例
     * {"code":"O","data":[{"creator":"c","provName":"GDwOgjMs","provCode":"8","receiverName":"fSgE","cityCode":"oeWnga6wN","addrCipher":"oizM6r","modifier":"Jl","remark":"1BTbXub06","mobileCipher":"HAN","addrMask":"PSHIuoH","createTimestamp":65790825003,"modifyTimestamp":2041619540365,"conscustno":"tKOISBPIT","countyCode":"L2S","cityName":"V36","mobileDigest":"6fK33FGR","addrDigest":"D","id":4496.8632452332395,"recStat":"LgmIMPFM","mobileMask":"yini0U9hJw","countyName":"QuXZB"}],"description":"Bnada3LWl"}
     */
    @ResponseBody
    @GetMapping("/getcustdeliveryaddrbycustno")
    public Response<List<CmConscustDeliveryAddressVO>> getCustDeliveryAddrByCustNo(@RequestParam(name= "custNo") String custNo) {
        return custDeliveryAddressService.getCustDeliveryAddrByCustNo(custNo);
    }


    /**
     * @api {GET} /custdeliveryaddress/getcustdeliveryaddrsensitiveinfobycustno getCustDeliveryAddrSensitiveInfoByCustNo()
     * @apiVersion 1.0.0
     * @apiGroup CustDeliveryAddressController
     * @apiName getCustDeliveryAddrSensitiveInfoByCustNo()
     * @apiDescription 根据投顾客户号获取投顾版收货地址敏感信息
     * @apiParam (请求参数) {String} custNo
     * @apiParamExample 请求参数示例
     * custNo=M4n7krA9v
     * @apiSuccess (响应结果) {String} code 状态码
     * @apiSuccess (响应结果) {String} description 描述信息
     * @apiSuccess (响应结果) {Array} data 数据封装
     * @apiSuccess (响应结果) {Number} data.id ID
     * @apiSuccess (响应结果) {String} data.conscustno 投顾客户号
     * @apiSuccess (响应结果) {String} data.mobile 手机号明文
     * @apiSuccess (响应结果) {String} data.addr 收货地址明文
     * @apiSuccessExample 响应结果示例
     * {"code":"XJzB2ekJ","data":[{"conscustno":"0yFSI","mobile":"9XXy","id":2627.829714462825,"addr":"RWpqIV"}],"description":"1m7ogFA"}
     */
    @ResponseBody
    @GetMapping("/getcustdeliveryaddrsensitiveinfobycustno")
    public Response<List<CmConscustDeliveryAddressSensitiveInfoVO>> getCustDeliveryAddrSensitiveInfoByCustNo(@RequestParam(name= "custNo") String custNo) {
        return custDeliveryAddressService.getCustDeliveryAddrSensitiveInfoByCustNo(custNo);
    }

}