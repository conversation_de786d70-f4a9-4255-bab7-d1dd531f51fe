/**
 * Copyright (c) 2023, <PERSON><PERSON><PERSON><PERSON> HOWBUY INVESTMENT MANAGEMENT Co., Ltd.
 * All right reserved.
 * <p>
 * THIS IS UNPUBLISHED PROPRIETARY SOURCE CODE OF HOWBUY INVESTMENT
 * MANAGEMENT CO., LTD.  THE CONTENTS OF THIS FILE MAY NOT BE DISCLOSED
 * TO THIRD PARTIES, COPIED OR DUPLICATED IN ANY FORM, IN WHOLE OR IN PART,
 * WITHOUT THE PRIOR WRITTEN PERMISSION OF HOWBUY INVESTMENT MANAGEMENT
 * CO., LTD.
 */
package com.howbuy.crm.account.service.message.hkcustinfo;

import com.howbuy.crm.account.client.enums.custinfo.FullCustSourceEnum;
import com.howbuy.crm.account.client.response.Response;
import com.howbuy.crm.account.dao.bo.custinfo.CmConscustForAnalyseBO;
import com.howbuy.crm.account.service.business.custinfo.CmCustProcessBusinessService;
import com.howbuy.crm.account.service.message.dto.HkOpenAcctMessageDTO;
import com.howbuy.crm.account.service.vo.custinfo.AbnormalAnalyseResultVO;
import com.howbuy.crm.account.service.vo.custinfo.HkMessageCustInfoVO;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;

import java.util.Collections;
import java.util.List;

/**
 * @description: ([香港账户]-[香港注册] 消息处理器[topic.hk.register])
 * <AUTHOR>
 * @date 2023/12/11 17:04
 * @since JDK 1.8
 */
@Slf4j
@Service
public class HkRegisterMessageProcessor extends AbstractHkMessageProcessor<HkOpenAcctMessageDTO> {


    /**
     * [TOPIC_HK_OPEN_ACCT]  香港线上开户
     */
    @Value("${topic.hk.openAcct}")
    private String topicHkOpenAcct;


    /**
     * [VERIFY_CODE_REGISTER]
     */
    @Value("${tag.hk.verifyCodeRegister}")
    private String tagName;


    @Autowired
    private CmCustProcessBusinessService processBusinessService;

    @Override
    public String getQuartMessageChannel() {
        return topicHkOpenAcct;
    }

    @Override
    public List<String> getSupportTagList() {
        return Collections.singletonList(tagName);
    }


    @Override
    HkMessageCustInfoVO constructByMessage(HkOpenAcctMessageDTO acctMsgDto) {
        HkMessageCustInfoVO custVo=new HkMessageCustInfoVO();
        custVo.setHkTxAcctNo(acctMsgDto.getHkCustNo());
        custVo.setOpenOutletCode(acctMsgDto.getOpenOutletCode());

        fillHkAcctInfo(custVo);
        return custVo;
    }

    @Override
    public FullCustSourceEnum getCustSource() {
        return FullCustSourceEnum.HK_REGISTER;
    }

    @Override
    Response<String> processHkMessage(AbnormalAnalyseResultVO<HkMessageCustInfoVO> resultVo) {
        CmConscustForAnalyseBO processedCustInfo= resultVo.getProcessedCustInfo();
        if(processedCustInfo==null){
            return Response.ok();
        }

        HkMessageCustInfoVO analyseVo=resultVo.getAnalyseVo();
        //关联香港客户号 vs 投顾客户号
        return processBusinessService.associateHkAcctRelation(processedCustInfo,analyseVo);
    }

}