/**
 * Copyright (c) 2023, <PERSON><PERSON><PERSON><PERSON> HOWBUY INVESTMENT MANAGEMENT Co., Ltd.
 * All right reserved.
 * <p>
 * THIS IS UNPUBLISHED PROPRIETARY SOURCE CODE OF HOWBUY INVESTMENT
 * MANAGEMENT CO., LTD.  THE CONTENTS OF THIS FILE MAY NOT BE DISCLOSED
 * TO THIRD PARTIES, COPIED OR DUPLICATED IN ANY FORM, IN WHOLE OR IN PART,
 * WITHOUT THE PRIOR WRITTEN PERMISSION OF HOWBUY INVESTMENT MANAGEMENT
 * CO., LTD.
 */
package com.howbuy.crm.account.service.service.dictionary;

import com.howbuy.crm.account.client.response.Response;
import com.howbuy.crm.account.client.response.dictionary.CityListVO;
import com.howbuy.crm.account.client.response.dictionary.CountryInfoVO;
import com.howbuy.crm.account.client.response.dictionary.ParamMobileAreaListVO;
import com.howbuy.crm.account.client.response.dictionary.ProvCityCountyNameVO;
import com.howbuy.crm.account.service.outerservice.dictionary.DictionaryOuterService;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * @description: 字典值服务类
 * <AUTHOR>
 * @date 2023/12/26 9:48
 * @since JDK 1.8
 */

@Slf4j
@Service
public class DictionaryService {

    @Autowired
    private DictionaryOuterService dictionaryOuterService;
    
    
    /**
     * @description: 获取国家列表
     * @return com.howbuy.crm.account.client.response.dictionary.CountryListVO 国家列表
     * @author: jin.wang03
     * @date: 2023/12/26 10:16
     * @since JDK 1.8
     */
    public List<CountryInfoVO> getCountryList() {
        return dictionaryOuterService.getCountryList();
    }

    /**
     * @description: 根据国家代码获取国家名称
     * @param countryCode
     * @return java.lang.String
     * @author: jin.wang03
     * @date: 2024/9/9 16:27
     * @since JDK 1.8
     */
    public String getCountryNameByCode(String countryCode) {
        String countryName = "";

        List<CountryInfoVO> countryList = dictionaryOuterService.getCountryList();
        for (CountryInfoVO countryInfoVO : countryList) {
            if (StringUtils.equals(countryCode, countryInfoVO.getShortCode())) {
                return countryInfoVO.getChineseName();
            }
        }

        return countryName;
    }

    /**
     * @description: 获取省市区列表接口
     * @return java.util.List<com.howbuy.crm.account.client.response.dictionary.CityListVO> 省市区列表
     * @author: jin.wang03
     * @date: 2023/12/26 11:18
     * @since JDK 1.8
     */
    public List<CityListVO> getCityListVO() {
        return dictionaryOuterService.getCityListVO();
    }

    /**
     * @description: 获取手机区号列表
     * @return com.howbuy.crm.account.client.response.dictionary.ParamMobileAreaListVO 手机区号列表数据实体类
     * @author: jin.wang03
     * @date: 2023/12/26 16:46
     * @since JDK 1.8
     */
    public ParamMobileAreaListVO getMobileAreaCodeList() {
        return dictionaryOuterService.getMobileAreaCodeList();
    }

    /**
     * @description:(请在此添加描述)
     * @param
     * @return com.howbuy.crm.account.client.response.Response<com.howbuy.crm.account.client.response.dictionary.ParamMobileAreaListVO>
     * @author: jin.wang03
     * @date: 2024/1/11 14:13
     * @since JDK 1.8
     */
    public Response<ParamMobileAreaListVO> getMobileAreaCodeList2() {
        return dictionaryOuterService.getMobileAreaCodeList2();
    }


    /**
     * @description: 获取省市县名称
     * @param provCode 省代码
     * @param cityCode 市代码
     * @param countyCode 县代码
     * @return com.howbuy.cs.domain.conscust.ProvCityCountyNameVO
     * @author: jin.wang03
     * @date: 2023/12/28 17:16
     * @since JDK 1.8
     */
    public ProvCityCountyNameVO getNamesByCodes(String provCode, String cityCode, String countyCode) {
        log.info("根据省市县代码查询省市县名称，入参：provCode：{}，cityCode：{}，countyCode：{}", provCode, cityCode, countyCode);
        ProvCityCountyNameVO provCityCountyNameVO = new ProvCityCountyNameVO();
        if (StringUtils.isBlank(provCode)) {
            return provCityCountyNameVO;
        }
        List<CityListVO> provCityCountyList = dictionaryOuterService.getCityListVO();

        for (CityListVO prov : provCityCountyList) {
            if (StringUtils.equals(prov.getDm(), provCode)) {
                provCityCountyNameVO.setProvName(prov.getMc());
                if (StringUtils.isBlank(cityCode)) {
                    return provCityCountyNameVO;
                }
                List<CityListVO> cityList = prov.getDataList();
                for (CityListVO city : cityList) {
                    if (StringUtils.equals(city.getDm(), cityCode)) {
                        provCityCountyNameVO.setCityName(city.getMc());
                        if (StringUtils.isBlank(countyCode)) {
                            return provCityCountyNameVO;
                        }
                        List<CityListVO> countyList = city.getDataList();
                        for (CityListVO county : countyList) {
                            if (StringUtils.equals(county.getDm(), countyCode)) {
                                provCityCountyNameVO.setCountyName(county.getMc());
                                break;
                            }
                        }
                        break;
                    }
                }

                break;
            }
        }

        return provCityCountyNameVO;
    }


}