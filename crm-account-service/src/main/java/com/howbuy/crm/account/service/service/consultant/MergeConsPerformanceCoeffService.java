/**
 * Copyright (c) 2024, <PERSON><PERSON><PERSON><PERSON> HOWBUY INVESTMENT MANAGEMENT Co., Ltd.
 * All right reserved.
 * <p>
 * THIS IS UNPUBLISHED PROPRIETARY SOURCE CODE OF HOWBUY INVESTMENT
 * MANAGEMENT CO., LTD.  THE CONTENTS OF THIS FILE MAY NOT BE DISCLOSED
 * TO THIRD PARTIES, COPIED OR DUPLICATED IN ANY FORM, IN WHOLE OR IN PART,
 * WITHOUT THE PRIOR WRITTEN PERMISSION OF HOWBUY INVESTMENT MANAGEMENT
 * CO., LTD.
 */
package com.howbuy.crm.account.service.service.consultant;

import com.howbuy.crm.account.client.enums.ExceptionCodeEnum;
import com.howbuy.crm.account.client.enums.YesOrNoEnum;
import com.howbuy.crm.account.client.request.consultant.MergeConsPerformanceCoeffRequest;
import com.howbuy.crm.account.dao.bo.consperformancecoeff.ConsPerformanceCoeffBO;
import com.howbuy.crm.account.dao.bo.consultant.CmCustSourceCoeffBO;
import com.howbuy.crm.account.dao.po.consultant.CmConsPerformanceCoeffPO;
import com.howbuy.crm.account.service.commom.enums.CustTypeEnum;
import com.howbuy.crm.account.service.commom.exception.BusinessException;
import com.howbuy.crm.account.service.outerservice.center.CenterDataOuterService;
import com.howbuy.crm.account.service.outerservice.crmtd.PreBookOuterService;
import com.howbuy.crm.account.service.repository.consultant.MergeConsPerformanceCoeffRepository;
import com.howbuy.crm.account.service.repository.custinfo.ConsCustInfoRepository;
import com.howbuy.crm.account.service.outerservice.crmcore.OrganazitonOuterService;
import com.howbuy.crm.account.service.domain.OrgLayerInfoDTO;
import crm.howbuy.base.utils.DateTimeUtil;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;
import org.springframework.scheduling.concurrent.ThreadPoolTaskExecutor;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.util.Arrays;
import java.util.List;
import java.util.Date;
import java.util.concurrent.CountDownLatch;
import java.util.concurrent.TimeUnit;
import java.util.concurrent.atomic.AtomicInteger;

/**
 * @description: 写入人员绩效系数表服务
 * <AUTHOR>
 * @date 2025-06-27 17:05:00
 * @since JDK 1.8
 */
@Service
@Slf4j
public class MergeConsPerformanceCoeffService {

    /**
     * 重复类型的来源类型枚举值（7: 公司资源（重复）、10: 投顾资源（潜在-重复）、11: 投顾资源（成交-重复））
     */
    private static final List<String> REPEAT_SOURCE_TYPES = Arrays.asList("10", "11", "7");

    /**
     * 管理系数常量值
     */
    private static final BigDecimal MANAGE_COEFF_DEFAULT = new BigDecimal("1"); // 默认管理系数值

    /**
     * 计算类型：修改
     */
    private static final String CALC_TYPE_MODIFY = "2";

    /**
     * 计算类型：新增
     */
    private static final String CALC_TYPE_ADD = "1";

    /**
     * 数量0
     */
    private static final String COUNT_ZERO = "0";
    /**
     *
     */
    private static final String MODIFIER = "AUTO";

    /**
     * 管理系数类型枚举
     */
    private enum ManageCoeffType {
        SUBTOTAL("分总"),
        REGIONAL_SUBTOTAL("区副"),
        REGIONAL_TOTAL("区总");

        private final String manageTitle;

        ManageCoeffType(String manageTitle) {
            this.manageTitle = manageTitle;
        }
    }

    @Resource
    private MergeConsPerformanceCoeffRepository mergeConsPerformanceCoeffRepository;

    @Resource
    private PreBookOuterService preBookOuterService;

    @Resource
    private CenterDataOuterService centerDataOuterService;

    @Resource
    private ConsCustInfoRepository consCustInfoRepository;

    @Resource
    private OrganazitonOuterService organazitonOuterService;

    @Resource(name = "performanceCoeffInitExecutor")
    private ThreadPoolTaskExecutor performanceCoeffInitExecutor;

    /**
     * @description: 初始化执行方法
     * @return void
     * @author: shijie.wang
     * @date: 2025/7/23 10:15
     * @since JDK 1.8
     */
    public void executeInit() {
        log.info("开始执行人员绩效系数初始化");
        long startTime = System.currentTimeMillis();

        // 获取总数
        int total = mergeConsPerformanceCoeffRepository.countInitData();
        log.info("需要初始化的数据总数：{}", total);
        if (total <= 0) {
            log.info("没有需要初始化的数据");
            return;
        }

        try {
            processAllPages(total);
            logExecutionSummary(startTime);
        } catch (BusinessException e) {
            log.error("人员绩效系数初始化业务异常", e);
            throw e;
        } catch (Exception e) {
            log.error("人员绩效系数初始化系统异常", e);
            throw new BusinessException(ExceptionCodeEnum.SYSTEM_ERROR);
        }
    }

    /**
     * @description: 处理所有分页数据
     * @param total 总记录数
     * @throws BusinessException 业务异常
     * @author: shijie.wang
     * @date: 2025/7/23 10:15
     * @since JDK 1.8
     */
    private void processAllPages(int total) {
        int pageSize = 1000;
        int totalPages = (total + pageSize - 1) / pageSize;
        AtomicInteger successCount = new AtomicInteger(0);
        AtomicInteger failCount = new AtomicInteger(0);

        for (int pageNum = 0; pageNum < totalPages; pageNum++) {
            processOnePage(pageNum, pageSize, total, successCount, failCount);
        }
    }

    /**
     * @description: 处理单页数据
     * @param pageNum 页码
     * @param pageSize 每页大小
     * @param total 总记录数
     * @param successCount 成功计数器
     * @param failCount 失败计数器
     * @throws BusinessException 业务异常
     * @author: shijie.wang
     * @date: 2025/7/23 10:15
     * @since JDK 1.8
     */
    private void processOnePage(int pageNum, int pageSize, int total, AtomicInteger successCount, AtomicInteger failCount) {
        int startRow = pageNum * pageSize;
        int endRow = Math.min((pageNum + 1) * pageSize, total);

        log.info("开始处理第{}页数据，startRow：{}，endRow：{}", pageNum + 1, startRow, endRow);
        List<ConsPerformanceCoeffBO> list = mergeConsPerformanceCoeffRepository.queryInitData(startRow, endRow);

        if (list == null || list.isEmpty()) {
            log.info("第{}页没有查询到数据", pageNum + 1);
            return;
        }

        CountDownLatch pageLatch = new CountDownLatch(list.size());
        executeInitByList(list, successCount, failCount, pageLatch);
        waitForPageCompletion(pageLatch, pageNum);

        log.info("第{}页数据处理完成，处理数量：{}", pageNum + 1, list.size());
    }

    /**
     * @description: 等待页面处理完成
     * @param pageLatch 页面计数器
     * @param pageNum 页码
     * @throws BusinessException 业务异常
     * @author: shijie.wang
     * @date: 2025/7/23 10:15
     * @since JDK 1.8
     */
    private void waitForPageCompletion(CountDownLatch pageLatch, int pageNum) {
        try {
            if (!pageLatch.await(10, TimeUnit.MINUTES)) {
                log.warn("第{}页部分任务执行超时", pageNum + 1);
            }
        } catch (InterruptedException e) {
            log.warn("等待任务完成时被中断，页码：{}", pageNum + 1, e);
            Thread.currentThread().interrupt();
            throw new BusinessException(ExceptionCodeEnum.SYSTEM_ERROR.getCode(), "任务执行被中断");
        }
    }

    /**
     * @description: 记录执行汇总信息
     * @param startTime 开始时间
     * @author: shijie.wang
     * @date: 2025/7/23 10:15
     * @since JDK 1.8
     */
    private void logExecutionSummary(long startTime) {
        log.info("人员绩效系数初始化完成，总耗时：{}ms",
            System.currentTimeMillis() - startTime);
    }

    /**
     * @description:(循环执行集合并并发处理数据)
     * @param list
     * @param successCount
     * @param failCount
     * @param pageLatch
     * @return void
     * @author: shijie.wang
     * @date: 2025/7/23 18:59
     * @since JDK 1.8
     */
    private void executeInitByList(List<ConsPerformanceCoeffBO> list, AtomicInteger successCount, AtomicInteger failCount,
                                   CountDownLatch pageLatch){
        // 处理每一条数据
        for (ConsPerformanceCoeffBO bo : list) {
            performanceCoeffInitExecutor.execute(() -> {
                try {
                    MergeConsPerformanceCoeffRequest request = new MergeConsPerformanceCoeffRequest();
                    request.setConsCustNo(bo.getConsCustNo());
                    request.setConsCode(bo.getConsCode());
                    request.setAssignTime(bo.getAssignTime());
                    request.setSourceType(bo.getSourceType());
                    request.setCustType(bo.getCustType());
                    request.setModifier(MODIFIER);
                    request.setCalcType(CALC_TYPE_ADD);
                    execute(request);
                    successCount.incrementAndGet();
                } catch (Exception e) {
                    failCount.incrementAndGet();
                    log.error("处理数据异常，投顾客户号：{}，异常信息：{}", bo.getConsCustNo(), e.getMessage(), e);
                } finally {
                    pageLatch.countDown();
                }
            });
        }
    }

    /**
     * @description:(初始化投顾代码和客户类型)
     * @param request
     * @return void
     * @author: shijie.wang
     * @date: 2025/7/23 10:15
     * @since JDK 1.8
     */
    public void initConsCodeAndCustType(MergeConsPerformanceCoeffRequest request){
        request.setConsCode(getConsCode(request));
        //查询客户历史所属投顾数量，有数据为分配投顾、没有为创建客户)
        request.setCustType(getCustTypeByHisCount(request.getConsCustNo(), request.getConsCode()));
    }

    /**
     * @param request 写入人员绩效系数表请求参数
     * @return void
     * @description: 写入人员绩效系数表
     * <AUTHOR>
     * @date 2025-06-27 17:05:00
     * @since JDK 1.8
     */
    public void execute(MergeConsPerformanceCoeffRequest request) {
        try {
            // 将Request转换为BO对象
            ConsPerformanceCoeffBO consPerformanceCoeffBO = convertRequestToBO(request);
            //按修改计算还是新增计算
            if(CALC_TYPE_MODIFY.equals(consPerformanceCoeffBO.getCalcType())){
                processDataByCustTypeModify(consPerformanceCoeffBO);
            }else{
                processDataByCustTypeCreateOrAssign(consPerformanceCoeffBO);
            }
            // 调用Repository层处理数据写入
            mergeConsPerformanceCoeffRepository.execute(consPerformanceCoeffBO);

        } catch (Exception e) {
            log.error("写入人员绩效系数表处理异常", e);
            throw new BusinessException(ExceptionCodeEnum.SYSTEM_ERROR);
        }
    }

    /**
     * @description: 将Request对象转换为BO对象
     * @param request 请求参数
     * @return ConsPerformanceCoeffBO BO对象
     * @author: shijie.wang
     * @date: 2025/6/30 13:50
     * @since JDK 1.8
     */
    private ConsPerformanceCoeffBO convertRequestToBO(MergeConsPerformanceCoeffRequest request) {
        ConsPerformanceCoeffBO consPerformanceCoeffBO = new ConsPerformanceCoeffBO();
        // 设置基本字段
        consPerformanceCoeffBO.setCustType(request.getCustType());
        // 处理投顾code：如果request中没有值，则通过投顾客户号查询获取
        consPerformanceCoeffBO.setConsCode(getConsCode(request));
        consPerformanceCoeffBO.setConsCustNo(request.getConsCustNo());
        consPerformanceCoeffBO.setHboneNo(request.getHboneNo());
        consPerformanceCoeffBO.setAssignTime(request.getAssignTime());
        consPerformanceCoeffBO.setSourceType(request.getSourceType());
        consPerformanceCoeffBO.setCustConversionCoeff(request.getCustConversionCoeff());
        consPerformanceCoeffBO.setManageCoeffSubtotal(request.getManageCoeffSubtotal());
        consPerformanceCoeffBO.setManageCoeffRegionalsubtotal(request.getManageCoeffRegionalsubtotal());
        consPerformanceCoeffBO.setManageCoeffRegionaltotal(request.getManageCoeffRegionaltotal());
        consPerformanceCoeffBO.setModifier(request.getModifier());
        consPerformanceCoeffBO.setCalcType(request.getCalcType());
        return consPerformanceCoeffBO;
    }

    /**
     * @description: 获取投顾code，优先使用request中的值，如果为空则通过投顾客户号查询获取
     * @param request 请求参数
     * @return String 投顾code
     * @author: shijie.wang
     * @date: 2025-07-09 14:05:00
     * @since JDK 1.8
     */
    private String getConsCode(MergeConsPerformanceCoeffRequest request) {
        // 优先使用request中的投顾code
        String consCode = request.getConsCode();
        if (StringUtils.isNotBlank(consCode)) {
            log.info("使用request中的投顾code：{}，投顾客户号：{}", consCode, request.getConsCustNo());
            return consCode;
        }
        // 如果request中投顾code为空，通过投顾客户号查询获取
        String consCustNo = request.getConsCustNo();
        consCode = consCustInfoRepository.selectConsCodeByCustNo(consCustNo);
        log.info("通过投顾客户号：{} 查询获取的投顾code：{}", consCustNo, consCode);
        return consCode;
    }

    /**
     * @description:(修改客户-来源类型是否发生变更)
     * @param bo
     * @param latestRecord
     * @return java.lang.Boolean true重算，false不重算
     * @author: shijie.wang
     * @date: 2025/7/22 13:12
     * @since JDK 1.8
     */
    private Boolean recalculateBySourceTypeCustTypeModify(ConsPerformanceCoeffBO bo, CmConsPerformanceCoeffPO latestRecord) {
        //来源类型和最新记录的来源类型一致，则无需重算数据
        if(bo.getSourceType().equals(latestRecord.getSourceType())){
            return Boolean.FALSE;
        }
        return Boolean.TRUE;
    }

    /**
     * @description:(查询客户历史所属投顾数量，有数据为分配投顾、没有为创建客户)
     * @param conscustno
     * @param consCode
     * @return java.lang.String
     * @author: shijie.wang
     * @date: 2025/7/22 15:18
     * @since JDK 1.8
     */
    public String getCustTypeByHisCount(String conscustno, String consCode){
        String count = mergeConsPerformanceCoeffRepository.queryHisCountByConscustno(conscustno, consCode);
        //历史表中没有投顾说明是创建客户，否则为分配投顾
        if(COUNT_ZERO.equals(count)){
            return CustTypeEnum.CUST_TYPE_CREATE.getCustType();
        }
        return CustTypeEnum.CUST_TYPE_ASSIGN.getCustType();
    }

    private void processDataByCustTypeModify(ConsPerformanceCoeffBO consPerformanceCoeffBO) {
        //查询最新人员绩效系数表记录
        CmConsPerformanceCoeffPO newRecord = mergeConsPerformanceCoeffRepository.getNewRecord(consPerformanceCoeffBO.getConsCustNo());
        if(newRecord == null){
            log.error("修改类型，查询最新人员绩效系数表记录为空，客户号：{}", consPerformanceCoeffBO.getConsCustNo());
            throw new BusinessException(ExceptionCodeEnum.DB_ERROR);
        }
        //主键赋值
        consPerformanceCoeffBO.setAssignTime(newRecord.getAssignTime());
        consPerformanceCoeffBO.setConsCode(newRecord.getConscode());
        //来源类型为空，直接把人工修改的数据赋给最新记录
        if(consPerformanceCoeffBO.getSourceType() == null){
            //客户折算系数、管理系数-分总、管理系数-区副、管理系数-区总
            if(consPerformanceCoeffBO.getCustConversionCoeff() ==null){
                consPerformanceCoeffBO.setCustConversionCoeff(newRecord.getCustConversionCoeff());
            }
            if(consPerformanceCoeffBO.getManageCoeffSubtotal() ==null){
                consPerformanceCoeffBO.setManageCoeffSubtotal(newRecord.getManageCoeffSubtotal());
            }
            if(consPerformanceCoeffBO.getManageCoeffRegionalsubtotal() ==null){
                consPerformanceCoeffBO.setManageCoeffRegionalsubtotal(newRecord.getManageCoeffRegionalsubtotal());
            }
            if(consPerformanceCoeffBO.getManageCoeffRegionaltotal() ==null){
                consPerformanceCoeffBO.setManageCoeffRegionaltotal(newRecord.getManageCoeffRegionaltotal());
            }
            return;
        }
        //修改类型-来源类型是否发生变更
        Boolean sourcTypeBoolean = recalculateBySourceTypeCustTypeModify(consPerformanceCoeffBO, newRecord);
        //来源类型变更重新计算--客户折算系数、存续B、是否计提公募、管理系数-分总、管理系数-区副、管理系数-区总（如果客户折算系数、管理系数-分总、管理系数-区副、管理系数-区总有值不重新计算）
        if(Boolean.TRUE.equals(sourcTypeBoolean)){
            //通用计算逻辑
            processDataCommon(consPerformanceCoeffBO);
        }
    }


    /**
     * @description:(数据处理-创建客户和分配客户)
     * @param consPerformanceCoeffBO
     * @return void
     * @author: shijie.wang
     * @date: 2025/7/22 13:21
     * @since JDK 1.8
     */
    private void processDataByCustTypeCreateOrAssign(ConsPerformanceCoeffBO consPerformanceCoeffBO) {
        // 处理分配时间
        long assignTimeStartTime = System.currentTimeMillis();
        processAssignTime(consPerformanceCoeffBO);
        log.info("处理分配时间耗时：{}ms", System.currentTimeMillis() - assignTimeStartTime);
        // 处理来源类型字段
        long sourceTypeStartTime = System.currentTimeMillis();
        processSourceType(consPerformanceCoeffBO);
        log.info("处理来源类型字段耗时：{}ms", System.currentTimeMillis() - sourceTypeStartTime);
        //通用计算逻辑
        processDataCommon(consPerformanceCoeffBO);
    }

    /**
     * @description:(通用计算逻辑)
     * @param consPerformanceCoeffBO
     * @return void
     * @author: shijie.wang
     * @date: 2025/7/22 15:46
     * @since JDK 1.8
     */
    private void processDataCommon(ConsPerformanceCoeffBO consPerformanceCoeffBO) {
        // 提前查询客户来源系数配置
        long sourceCoeffStartTime = System.currentTimeMillis();
        CmCustSourceCoeffBO custSourceCoeff = null;
        if (StringUtils.isNotBlank(consPerformanceCoeffBO.getSourceType())) {
            custSourceCoeff = mergeConsPerformanceCoeffRepository.selectCustSourceCoeffByCustType(consPerformanceCoeffBO.getSourceType());
            log.info("查询客户来源系数配置，来源类型：{}，结果：{}，耗时：{}ms", consPerformanceCoeffBO.getSourceType(), custSourceCoeff, System.currentTimeMillis() - sourceCoeffStartTime);
        }

        // 处理佣金系数起点、客户折算系数、存续B字段
        processPerformanceCoeff(consPerformanceCoeffBO, custSourceCoeff);

        // 处理是否计提公募字段
        processIsBigV(consPerformanceCoeffBO);

        // 处理组织信息
        processOrgInfo(consPerformanceCoeffBO);

        // 处理管理系数-分总字段
        processManageCoeff(consPerformanceCoeffBO, ManageCoeffType.SUBTOTAL, custSourceCoeff);

        // 处理管理系数-区副字段
        processManageCoeff(consPerformanceCoeffBO, ManageCoeffType.REGIONAL_SUBTOTAL, custSourceCoeff);

        // 处理管理系数-区总字段
        processManageCoeff(consPerformanceCoeffBO, ManageCoeffType.REGIONAL_TOTAL, custSourceCoeff);
    }


    /**
     * @description: 处理分配时间，如果为空则从CM_CONSCUST表获取BINDDATE
     * @param consPerformanceCoeffBO BO对象
     * @return void
     * @author: shijie.wang
     * @date: 2024-04-07 12:05:00
     * @since JDK 1.8
     */
    private void processAssignTime(ConsPerformanceCoeffBO consPerformanceCoeffBO) {
        try {
            // 如果已有分配时间，直接返回
            if (consPerformanceCoeffBO.getAssignTime() != null) {
                return;
            }

            String custNo = consPerformanceCoeffBO.getConsCustNo();
            if (StringUtils.isBlank(custNo)) {
                log.warn("投顾客户号为空，无法获取分配时间");
                return;
            }

            Date bindDate = mergeConsPerformanceCoeffRepository.selectBindDateByCustNo(custNo);
            if (bindDate == null) {
                log.warn("投顾客户号：{} 在CM_CONSCUST表中未找到分配时间", custNo);
                return;
            }

            consPerformanceCoeffBO.setAssignTime(bindDate);
            log.info("从CM_CONSCUST表获取到分配时间：{}，投顾客户号：{}", bindDate, custNo);
        } catch (Exception e) {
            log.error("处理分配时间异常", e);
            throw new BusinessException(ExceptionCodeEnum.SYSTEM_ERROR);
        }
    }

    /**
     * @description:(来源类型字段处理)
     * @param consPerformanceCoeffBO BO对象
     * @return void
     * @author: shijie.wang
     * @date: 2025/6/30 13:25
     * @since JDK 1.8
     */
    private void processSourceType(ConsPerformanceCoeffBO consPerformanceCoeffBO) {
        try {
            // 如果来源类型有值（人工修改），直接使用
            if (StringUtils.isNotBlank(consPerformanceCoeffBO.getSourceType())) {
                log.info("来源类型已有值（人工修改），直接使用：{}", consPerformanceCoeffBO.getSourceType());
                return;
            }
            // 如果来源类型为空，通过consCustNo和当前时间调用crm-td-client接口获取
            String consCustNo = consPerformanceCoeffBO.getConsCustNo();
            if (StringUtils.isBlank(consCustNo)) {
                log.info("投顾客户号为空，无法获取来源类型");
                return;
            }
            String sourceType = preBookOuterService.getCustSourceType(consCustNo, DateTimeUtil.getCurrYMD());
            if (StringUtils.isBlank(sourceType)) {
                log.info("crm-td-client未返回来源类型，投顾客户号：{}", consCustNo);
                return;
            }
            consPerformanceCoeffBO.setSourceType(sourceType);
        } catch (Exception e) {

            log.error("处理来源类型字段异常", e);
            throw new BusinessException(ExceptionCodeEnum.SYSTEM_ERROR);
        }
    }

    /**
     * @description:(处理佣金系数起点、客户折算系数、存续B字段)
     * @param consPerformanceCoeffBO BO对象
     * @param custSourceCoeff 客户来源系数配置
     * @return void
     * @author: shijie.wang
     * @date: 2025/6/30 13:29
     * @since JDK 1.8
     */
    private void processPerformanceCoeff(ConsPerformanceCoeffBO consPerformanceCoeffBO, CmCustSourceCoeffBO custSourceCoeff) {
        try {
            log.info("开始处理佣金系数起点、客户折算系数、存续B字段，投顾客户号：{}，来源类型：{}", consPerformanceCoeffBO.getConsCustNo(), consPerformanceCoeffBO.getSourceType());
            String sourceType = consPerformanceCoeffBO.getSourceType();
            if (StringUtils.isBlank(sourceType)) {
                log.info("来源类型为空，无法获取佣金系数起点、客户折算系数、存续B字段值");
                return;
            }
            
            if (custSourceCoeff == null) {
                log.info("未查询到来源类型对应的系数配置，来源类型：{}", sourceType);
                return;
            }

            // 处理佣金系数起点：通过来源类型+当前时间获取值
            if (StringUtils.isNotBlank(custSourceCoeff.getStartPoint())) {
                consPerformanceCoeffBO.setCommissionCoeffStart(custSourceCoeff.getStartPoint());
                log.info("设置佣金系数起点：{}，投顾客户号：{}", custSourceCoeff.getStartPoint(), consPerformanceCoeffBO.getConsCustNo());
            }
            // 处理客户折算系数
            processCustConversionCoeff(consPerformanceCoeffBO, custSourceCoeff.getZbCoeff());
            // 处理存续B：通过来源类型+当前时间获取值
            if (StringUtils.isNotBlank(custSourceCoeff.getCxb())) {
                consPerformanceCoeffBO.setCxb(custSourceCoeff.getCxb());
                log.info("设置存续B：{}，投顾客户号：{}", custSourceCoeff.getCxb(), consPerformanceCoeffBO.getConsCustNo());
            }
        } catch (Exception e) {
            log.error("处理佣金系数起点、客户折算系数、存续B字段异常",e);
            throw new BusinessException(ExceptionCodeEnum.SYSTEM_ERROR);
        }
    }

    /**
     * @description: 处理客户折算系数逻辑
     * @param consPerformanceCoeffBO BO对象
     * @param zbCoeff 客户折算系数
     * @return void
     * @author: shijie.wang
     * @date: 2025/6/30 13:40
     * @since JDK 1.8
     */
    private void processCustConversionCoeff(ConsPerformanceCoeffBO consPerformanceCoeffBO, BigDecimal zbCoeff) {
        // 处理客户折算系数：如果入参有值直接使用，否则通过来源类型+当前时间获取值
        if (consPerformanceCoeffBO.getCustConversionCoeff() == null && zbCoeff != null) {
            consPerformanceCoeffBO.setCustConversionCoeff(zbCoeff);
            log.info("设置客户折算系数：{}，投顾客户号：{}", zbCoeff, consPerformanceCoeffBO.getConsCustNo());
        }
    }


    /**
     * @description: 处理是否计提公募字段
     * @param consPerformanceCoeffBO BO对象
     * @return void
     * @author: shijie.wang
     * @date: 2025/7/01 10:35
     * @since JDK 1.8
     */
    private void processIsBigV(ConsPerformanceCoeffBO consPerformanceCoeffBO) {
        try {
            // 1. 获取一账通号
            getHboneNo(consPerformanceCoeffBO);
            if (StringUtils.isBlank(consPerformanceCoeffBO.getHboneNo())) {
                log.warn("投顾客户号：{} 未获取到一账通号，无法查询是否计提公募", consPerformanceCoeffBO.getConsCustNo());
                consPerformanceCoeffBO.setIsBigV(YesOrNoEnum.YES.getCode());
                return;
            }
            // 2. 通过一账通号从大数据获取是否计提公募
            String isBigV = centerDataOuterService.queryIsBigV(consPerformanceCoeffBO.getHboneNo());
            consPerformanceCoeffBO.setIsBigV(isBigV);
            log.info("投顾客户号：{}，一账通号：{}，是否计提公募：{}", consPerformanceCoeffBO.getConsCustNo(), consPerformanceCoeffBO.getHboneNo(), isBigV);
        } catch (Exception e) {
            log.error("处理是否计提公募字段异常", e);
            throw new BusinessException(ExceptionCodeEnum.SYSTEM_ERROR);
        }
    }

    /**
     * @description: 获取一账通号，优先使用BO中的一账通号，如果为空则通过投顾客户号查询获取
     * @param consPerformanceCoeffBO BO对象
     * @return String 一账通号
     * @author: shijie.wang
     * @date: 2025/7/01 10:40
     * @since JDK 1.8
     */
    private void getHboneNo(ConsPerformanceCoeffBO consPerformanceCoeffBO) {
        try {
            // 1. 优先使用BO中的一账通号
            String hboneNo = consPerformanceCoeffBO.getHboneNo();
            if (StringUtils.isNotBlank(hboneNo)) {
                log.info("使用BO中的一账通号：{}，投顾客户号：{}", hboneNo, consPerformanceCoeffBO.getConsCustNo());
                return;
            }
            // 2. 如果BO中一账通号为空，通过投顾客户号查询获取
            String consCustNo = consPerformanceCoeffBO.getConsCustNo();
            hboneNo = getHboneNoByConsCustNo(consCustNo);
            log.info("通过投顾客户号：{} 查询获取的一账通号：{}", consCustNo, hboneNo);
            consPerformanceCoeffBO.setHboneNo(hboneNo);
        } catch (Exception e) {
            log.error("获取一账通号异常", e);
        }
    }

    /**
     * @description: 根据投顾客户号查询一账通号
     * @param consCustNo 投顾客户号
     * @return String 一账通号
     * @author: shijie.wang
     * @date: 2025/7/01 10:40
     * @since JDK 1.8
     */
    private String getHboneNoByConsCustNo(String consCustNo) {
        try {
            if (StringUtils.isBlank(consCustNo)) {
                log.warn("投顾客户号为空，无法查询一账通号");
                return null;
            }
            String hboneNo = consCustInfoRepository.selectHboneNoByCustNo(consCustNo);
            log.info("投顾客户号：{} 对应的一账通号：{}", consCustNo, hboneNo);
            return hboneNo;
        } catch (Exception e) {
            log.error("根据投顾客户号", e);
            throw new BusinessException(ExceptionCodeEnum.SYSTEM_ERROR);
        }
    }

    /**
     * @description: 处理组织信息（中心-分配时、区域-分配时、分公司-分配时、区副-分配时）
     * @param consPerformanceCoeffBO BO对象
     * @return void
     * @author: shijie.wang
     * @date: 2025/7/01 14:20
     * @since JDK 1.8
     */
    private void processOrgInfo(ConsPerformanceCoeffBO consPerformanceCoeffBO) {
        try {
            String consCode = consPerformanceCoeffBO.getConsCode();
            if (StringUtils.isBlank(consCode)) {
                log.info("投顾code为空，无法获取组织信息");
                return;
            }
            // 通过投顾code查询CM_CONSULTANT表中的outletcode字段
            String outletCode = mergeConsPerformanceCoeffRepository.selectOutletCodeByConsCode(consCode);
            if (StringUtils.isBlank(outletCode)) {
                log.info("投顾code：{} 未获取到outletcode", consCode);
                return;
            }
            log.info("投顾code：{} 对应的outletcode：{}", consCode, outletCode);
            // 根据outletcode调用DUBBO接口OrganazitonOuterService.getOrgLayerInfoContaionsDeleteByOrgCode方法获得数据
            OrgLayerInfoDTO orgLayerInfo = organazitonOuterService.getOrgLayerInfoContaionsDeleteByOrgCode(outletCode);
            if (orgLayerInfo == null) {
                log.info("outletcode：{} 未获取到组织层级信息", outletCode);
                return;
            }
            // 设置组织信息
            // 获取分公司-分配时（部门名称）
            consPerformanceCoeffBO.setOutletCode(orgLayerInfo.getPartOrgCode());
            // 获取中心-分配时
            consPerformanceCoeffBO.setCenterCode(orgLayerInfo.getCenterOrgCode());
            // 获取区域-分配时
            consPerformanceCoeffBO.setRegionCode(orgLayerInfo.getDistrictCode());
            // 获取区副-分配时
            processRegionalSubtotal(consPerformanceCoeffBO, outletCode);
        } catch (Exception e) {
            log.error("处理组织信息异常", e);
            throw new BusinessException(ExceptionCodeEnum.SYSTEM_ERROR);
        }
    }

    /**
     * @description: 处理区副-分配时逻辑
     * @param consPerformanceCoeffBO BO对象
     * @param outletCode 部门code（outletcode）
     * @return void
     * @author: shijie.wang
     * @date: 2025/7/01 14:30
     * @since JDK 1.8
     */
    private void processRegionalSubtotal(ConsPerformanceCoeffBO consPerformanceCoeffBO, String outletCode) {
        try {
            log.info("开始处理区副-分配时，投顾客户号：{}，outletCode：{}", consPerformanceCoeffBO.getConsCustNo(), outletCode);
            // 通过outletCode获取区副信息
            String regionalSubtotal = mergeConsPerformanceCoeffRepository.getRegionalSubtotalByOrgCode(outletCode);
            if (StringUtils.isNotBlank(regionalSubtotal)) {
                consPerformanceCoeffBO.setRegionalSubtotal(regionalSubtotal);
                log.info("设置区副-分配时：{}，投顾客户号：{}", regionalSubtotal, consPerformanceCoeffBO.getConsCustNo());
            } else {
                log.info("未查询到区副信息，outletCode：{}", outletCode);
            }
        } catch (Exception e) {
            log.error("处理区副-分配时异常", e);
            throw new BusinessException(ExceptionCodeEnum.SYSTEM_ERROR);
        }
    }

    /**
     * 处理管理系数
     * @param consPerformanceCoeffBO 绩效系数BO
     * @param coeffType 管理系数类型
     * @param custSourceCoeff 客户来源系数配置
     */
    private void processManageCoeff(ConsPerformanceCoeffBO consPerformanceCoeffBO, ManageCoeffType coeffType, CmCustSourceCoeffBO custSourceCoeff) {
        // 如果已有值直接使用
        if (hasExistingValue(consPerformanceCoeffBO, coeffType)) {
            log.info("管理系数-{}已有值，直接使用", coeffType.manageTitle);
            return;
        }
        // 根据客户类型处理
        if (CustTypeEnum.CUST_TYPE_CREATE.getCustType().equals(consPerformanceCoeffBO.getCustType())) {
            processCreateCustManageCoeff(consPerformanceCoeffBO, coeffType, custSourceCoeff);
        } else if (CustTypeEnum.CUST_TYPE_ASSIGN.getCustType().equals(consPerformanceCoeffBO.getCustType())) {
            processAssignCustManageCoeff(consPerformanceCoeffBO, coeffType, custSourceCoeff);
        }
    }

    /**
     * 检查是否已有管理系数值
     */
    private boolean hasExistingValue(ConsPerformanceCoeffBO consPerformanceCoeffBO, ManageCoeffType coeffType) {
        BigDecimal value = null;
        switch (coeffType) {
            case SUBTOTAL:
                value = consPerformanceCoeffBO.getManageCoeffSubtotal();
                break;
            case REGIONAL_SUBTOTAL:
                value = consPerformanceCoeffBO.getManageCoeffRegionalsubtotal();
                break;
            case REGIONAL_TOTAL:
                value = consPerformanceCoeffBO.getManageCoeffRegionaltotal();
                break;
        }
        return value != null;
    }

    /**
     * 设置管理系数值
     */
    private void setManageCoeffValue(ConsPerformanceCoeffBO consPerformanceCoeffBO, BigDecimal value, ManageCoeffType coeffType) {
        switch (coeffType) {
            case SUBTOTAL:
                consPerformanceCoeffBO.setManageCoeffSubtotal(value);
                log.info("设置管理系数-分总值：{}", value);
                break;
            case REGIONAL_SUBTOTAL:
                consPerformanceCoeffBO.setManageCoeffRegionalsubtotal(value);
                log.info("设置管理系数-区副值：{}", value);
                break;
            case REGIONAL_TOTAL:
                consPerformanceCoeffBO.setManageCoeffRegionaltotal(value);
                log.info("设置管理系数-区总值：{}", value);
                break;
        }
    }

    /**
     * 处理创建客户的管理系数
     */
    private void processCreateCustManageCoeff(ConsPerformanceCoeffBO consPerformanceCoeffBO, ManageCoeffType coeffType, CmCustSourceCoeffBO custSourceCoeff) {
        try {
            if (custSourceCoeff != null) {
                BigDecimal value = null;
                switch (coeffType) {
                    case SUBTOTAL:
                        value = custSourceCoeff.getManageCoeff();
                        break;
                    case REGIONAL_SUBTOTAL:
                        value = custSourceCoeff.getManageCoeffRegionalsubtotal();
                        break;
                    case REGIONAL_TOTAL:
                        value = custSourceCoeff.getManageCoeffRegionaltotal();
                        break;
                }
                setManageCoeffValue(consPerformanceCoeffBO, value, coeffType);
            }
        } catch (Exception e) {
            log.error("处理创建客户管理系数异常", e);
            throw new BusinessException(ExceptionCodeEnum.SYSTEM_ERROR);
        }
    }

    /**
     * 处理分配客户的管理系数
     */
    private void processAssignCustManageCoeff(ConsPerformanceCoeffBO consPerformanceCoeffBO, ManageCoeffType coeffType, CmCustSourceCoeffBO custSourceCoeff) {
        try {
            log.info("开始处理分配客户管理系数-{}，投顾客户号：{}", coeffType.manageTitle, consPerformanceCoeffBO.getConsCustNo());
            // 如果来源类型是重复类型，按创建客户规则取数
            if (REPEAT_SOURCE_TYPES.contains(consPerformanceCoeffBO.getSourceType())) {
                log.info("当前来源类型：{} 为重复类型，按创建客户规则取数", consPerformanceCoeffBO.getSourceType());
                processCreateCustManageCoeff(consPerformanceCoeffBO, coeffType, custSourceCoeff);
                return;
            }
            // 查询最新的重复类型分配记录
            CmConsPerformanceCoeffPO latestRepeatRecord = mergeConsPerformanceCoeffRepository.getLatestRepeatRecord(
                    consPerformanceCoeffBO.getConsCustNo(), REPEAT_SOURCE_TYPES, consPerformanceCoeffBO.getAssignTime());
            // 如果没有重复类型记录，按创建客户规则取数
            if (latestRepeatRecord == null) {
                log.info("未查询到重复类型记录，按创建客户规则取数");
                processCreateCustManageCoeff(consPerformanceCoeffBO, coeffType, custSourceCoeff);
                return;
            }
            log.info("查询到最新重复类型记录，分配时间：{}，来源类型：{}", latestRepeatRecord.getAssignTime(), latestRepeatRecord.getSourceType());
            // 获取从最新重复记录时间到当前分配时间的所有记录
            List<CmConsPerformanceCoeffPO> performanceCoeffByTimeRange = mergeConsPerformanceCoeffRepository.getRecordsByTimeRange(
                    consPerformanceCoeffBO.getConsCustNo(), 
                    latestRepeatRecord.getAssignTime(),
                    consPerformanceCoeffBO.getAssignTime());
            // 检查是否存在跨架构情况(true-存在跨架构，false-不存在跨架构)
            boolean hasCrossStructure = checkCrossStructure(consPerformanceCoeffBO, performanceCoeffByTimeRange, coeffType);
            // 根据是否跨架构设置管理系数值
            if (hasCrossStructure) {
                log.info("存在跨架构情况，按创建客户规则取数");
                processCreateCustManageCoeff(consPerformanceCoeffBO, coeffType, custSourceCoeff);
            } else {
                log.info("不存在跨架构情况，设置管理系数为默认值：{}", MANAGE_COEFF_DEFAULT);
                setManageCoeffValue(consPerformanceCoeffBO, MANAGE_COEFF_DEFAULT, coeffType);
            }
        } catch (Exception e) {
            log.error("处理分配客户管理系数异常", e);
            throw new BusinessException(ExceptionCodeEnum.SYSTEM_ERROR);
        }
    }

    /**
     * @description: 检查是否存在跨架构情况
     * @param consPerformanceCoeffBO BO对象
     * @param performanceCoeffByTimeRange 历史分配记录
     * @param coeffType 管理系数类型
     * @return boolean true-存在跨架构，false-不存在跨架构
     * @author: shijie.wang
     * @date: 2025/6/30 13:40
     * @since JDK 1.8
     */
    private boolean checkCrossStructure(ConsPerformanceCoeffBO consPerformanceCoeffBO, 
                                      List<CmConsPerformanceCoeffPO> performanceCoeffByTimeRange,
                                      ManageCoeffType coeffType) {
        try {
            if (performanceCoeffByTimeRange == null || performanceCoeffByTimeRange.isEmpty()) {
                log.info("没有历史分配记录，不存在跨架构情况");
                return false;
            }
            switch (coeffType) {
                case SUBTOTAL:
                    return checkSubtotalCrossStructure(consPerformanceCoeffBO, performanceCoeffByTimeRange);
                case REGIONAL_SUBTOTAL:
                    return checkRegionalSubtotalCrossStructure(consPerformanceCoeffBO, performanceCoeffByTimeRange);
                case REGIONAL_TOTAL:
                    return checkRegionalTotalCrossStructure(consPerformanceCoeffBO, performanceCoeffByTimeRange);
                default:
                    log.info("未知的管理系数类型：{}", coeffType);
                    return false;
            }
        } catch (Exception e) {
            log.error("检查跨架构情况异常", e);
            throw new BusinessException(ExceptionCodeEnum.SYSTEM_ERROR);
        }
    }

    /**
     * @description: 检查分总是否跨架构
     * @param consPerformanceCoeffBO BO对象
     * @param performanceCoeffByTimeRange 历史分配记录
     * @return boolean true-存在跨架构，false-不存在跨架构
     * @author: shijie.wang
     * @date: 2025/6/30 13:40
     * @since JDK 1.8
     */
    private boolean checkSubtotalCrossStructure(ConsPerformanceCoeffBO consPerformanceCoeffBO,
                                              List<CmConsPerformanceCoeffPO> performanceCoeffByTimeRange) {
        // 分总：检查分公司是否一致
        String currentOutletCode = consPerformanceCoeffBO.getOutletCode();
        String currentRegionCode = consPerformanceCoeffBO.getRegionCode();
        for (CmConsPerformanceCoeffPO performanceCoeffPO : performanceCoeffByTimeRange) {
            // 检查是否在同一个区域内且分公司一致
            boolean sameRegion = StringUtils.equals(currentRegionCode, performanceCoeffPO.getRegionCode());
            boolean sameSubtotal = StringUtils.equals(currentOutletCode, performanceCoeffPO.getOutletCode());
            // 如果不在同一个区域内或分公司不一致，则认为是跨架构
            if (!sameSubtotal || !sameRegion) {
                log.info("分总跨架构：当前区域：{}，历史区域：{}，当前分公司：{}，历史分公司：{}",
                        currentRegionCode, performanceCoeffPO.getRegionCode(),
                        sameRegion, performanceCoeffPO.getOutletCode());
                return true;
            }
        }
        return false;
    }

    /**
     * @description: 检查区副是否跨架构
     * @param consPerformanceCoeffBO BO对象
     * @param performanceCoeffByTimeRange 历史分配记录
     * @return boolean true-存在跨架构，false-不存在跨架构
     * @author: shijie.wang
     * @date: 2025/6/30 13:40
     * @since JDK 1.8
     */
    private boolean checkRegionalSubtotalCrossStructure(ConsPerformanceCoeffBO consPerformanceCoeffBO,
                                                      List<CmConsPerformanceCoeffPO> performanceCoeffByTimeRange) {
        // 区副：检查是否在同一个区域内且区副一致
        String currentRegionCode = consPerformanceCoeffBO.getRegionCode();
        String currentRegionalSubtotal = consPerformanceCoeffBO.getRegionalSubtotal();
        for (CmConsPerformanceCoeffPO performanceCoeffPO : performanceCoeffByTimeRange) {
            // 检查是否在同一个区域内且区副一致
            boolean sameRegion = StringUtils.equals(currentRegionCode, performanceCoeffPO.getRegionCode());
            boolean sameRegionalSubtotal = StringUtils.equals(currentRegionalSubtotal, performanceCoeffPO.getRegionalsubtotal());
            // 如果不在同一个区域内或区副不一致，则认为是跨架构
            if (!sameRegion || !sameRegionalSubtotal) {
                log.info("区副跨架构：当前区域：{}，历史区域：{}，当前区副：{}，历史区副：{}", 
                        currentRegionCode, performanceCoeffPO.getRegionCode(),
                        currentRegionalSubtotal, performanceCoeffPO.getRegionalsubtotal());
                return true;
            }
        }
        return false;
    }

    /**
     * @description: 检查区总是否跨架构
     * @param consPerformanceCoeffBO BO对象
     * @param performanceCoeffByTimeRange 历史分配记录
     * @return boolean true-存在跨架构，false-不存在跨架构
     * @author: shijie.wang
     * @date: 2025/6/30 13:40
     * @since JDK 1.8
     */
    private boolean checkRegionalTotalCrossStructure(ConsPerformanceCoeffBO consPerformanceCoeffBO,
                                                   List<CmConsPerformanceCoeffPO> performanceCoeffByTimeRange) {
        // 区总：检查区域是否一致
        String currentRegionalTotalCode = consPerformanceCoeffBO.getRegionCode();
        for (CmConsPerformanceCoeffPO performanceCoeffPO : performanceCoeffByTimeRange) {
            if (!StringUtils.equals(currentRegionalTotalCode, performanceCoeffPO.getRegionCode())) {
                log.info("区总跨架构：当前区域：{}，历史区域：{}", currentRegionalTotalCode, performanceCoeffPO.getRegionCode());
                return true;
            }
        }
        return false;
    }
} 