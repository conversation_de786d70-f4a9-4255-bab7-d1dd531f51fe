package com.howbuy.crm.account.service.vo.custinfo;

import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * @description: (一账通 客户信息处理 )
 * <AUTHOR>
 * @date 2023/12/11 10:57
 * @since JDK 1.8
 */

/**
    * 香港客户异常信息表
    */
@Data
@EqualsAndHashCode(callSuper = true)
public class HboneMessageCustInfoVO  extends  MessageCustInfoVO{



    /**
     * 分销机构号(disCode)
     */
    private String disCode;



    /**
    * 客户姓名
    */
    private String custName;

    /**
    * 投资者类型
    */
    private String investType;

    /**
    * 手机地区码
    */
    private String mobileAreaCode;

    /**
    * 手机号摘要
    */
    private String mobileDigest;

    /**
    * 手机号掩码
    */
    private String mobileMask;

    /**
    * 手机号密文
    */
    private String mobileCipher;

    /**
    * 证件地区码
    */
    private String idSignAreaCode;

    /**
    * 证件类型
    */
    private String idType;

    /**
    * 证件号码摘要
    */
    private String idNoDigest;

    /**
    * 证件号码掩码
    */
    private String idNoMask;

    /**
    * 证件号码密文
    */
    private String idNoCipher;

    /**
     * 注册网点号(regOutletCode)
     */
    private String regOutletCode;

    /**
     * 注册时间(regDate)
     */
    private String regDate;


    /**
     手机验证状态(mobileVerifyStatus)
     */
    private String mobileVerifyStatus;


    /**
     * 一账通注销 消息使用
     * 是否注销一账通号(closeHbone)
     */
    private Boolean closeHbone;




}