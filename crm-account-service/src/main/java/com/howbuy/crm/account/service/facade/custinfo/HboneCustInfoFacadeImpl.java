package com.howbuy.crm.account.service.facade.custinfo;

import com.howbuy.crm.account.client.facade.custinfo.HboneCustInfoFacade;
import com.howbuy.crm.account.client.response.Response;
import com.howbuy.crm.account.client.response.custinfo.HboneAcctCustDetailInfoVO;
import com.howbuy.crm.account.client.response.custinfo.HboneAcctCustSensitiveInfoVO;
import com.howbuy.crm.account.service.outerservice.acct.HboneAcctInfoOuterService;
import lombok.extern.slf4j.Slf4j;
import org.apache.dubbo.config.annotation.DubboService;

import javax.annotation.Resource;

/**
 * @description: 一账通客户信息服务实现
 * <AUTHOR>
 * @date 2024-04-07 12:05:00
 * @since JDK 1.8
 */
@Slf4j
@DubboService
public class HboneCustInfoFacadeImpl implements HboneCustInfoFacade {

    @Resource
    private HboneAcctInfoOuterService hboneAcctInfoOuterService;

    @Override
    public Response<HboneAcctCustDetailInfoVO> queryHboneCustDetailInfo(String hboneNo) {
        return Response.ok(hboneAcctInfoOuterService.queryHboneCustDetailInfo(hboneNo));
    }

    @Override
    public Response<HboneAcctCustSensitiveInfoVO> queryHboneCustSensitiveInfo(String hboneNo) {
        return Response.ok(hboneAcctInfoOuterService.queryHboneCustSensitiveInfo(hboneNo));
    }
}