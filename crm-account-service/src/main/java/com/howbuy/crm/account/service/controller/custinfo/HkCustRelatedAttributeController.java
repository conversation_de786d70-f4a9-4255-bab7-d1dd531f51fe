/**
 * Copyright (c) 2023, <PERSON><PERSON><PERSON><PERSON> HOWBUY INVESTMENT MANAGEMENT Co., Ltd.
 * All right reserved.
 * <p>
 * THIS IS UNPUBLISHED PROPRIETARY SOURCE CODE OF HOWBUY INVESTMENT
 * MANAGEMENT CO., LTD.  THE CONTENTS OF THIS FILE MAY NOT BE DISCLOSED
 * TO THIRD PARTIES, COPIED OR DUPLICATED IN ANY FORM, IN WHOLE OR IN PART,
 * WITHOUT THE PRIOR WRITTEN PERMISSION OF HOWBUY INVESTMENT MANAGEMENT
 * CO., LTD.
 */
package com.howbuy.crm.account.service.controller.custinfo;

import com.howbuy.crm.account.client.request.custinfo.HkAcctOptDealRequest;
import com.howbuy.crm.account.client.request.custinfo.HkPiggyAgreementDealRequest;
import com.howbuy.crm.account.client.response.PageVO;
import com.howbuy.crm.account.client.response.Response;
import com.howbuy.crm.account.client.response.custinfo.HkAcctOptDealVO;
import com.howbuy.crm.account.client.response.custinfo.HkCustPiggyAgreementVO;
import com.howbuy.crm.account.client.response.custinfo.HkPiggyAgreementDealVO;
import com.howbuy.crm.account.service.service.custinfo.HkCustRelatedAttributeService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import java.util.List;

/**
 * @description: (香港客户 [交易相关属性]  信息接口)
 * <AUTHOR>
 * @date 2024年7月24日
 * @since JDK 1.8
 */
@RestController
@RequestMapping("/hkcustrelatedattribute")
public class HkCustRelatedAttributeController {

    @Autowired
    private HkCustRelatedAttributeService hkCustRelatedAttributeService;


    /**
     * @api {GET} /hkcustrelatedattribute/queryPiggyAgreementByHkTxAcctNo queryPiggyAgreementByHkTxAcctNo()
     * @apiVersion 1.0.0
     * @apiGroup HkCustRelatedAttributeController
     * @apiName queryPiggyAgreementByHkTxAcctNo()
     * @apiDescription 根据 香港交易账号 查询 海外储蓄罐协议签署信息
     * @apiParam (请求参数) {String} hkTxAcctNo
     * @apiParamExample 请求参数示例
     * hkTxAcctNo=SiKnj
     * @apiSuccess (响应结果) {String} code 状态码
     * @apiSuccess (响应结果) {String} description 描述信息
     * @apiSuccess (响应结果) {Object} data 数据封装
     * @apiSuccess (响应结果) {Array} data.piggyFundCodeList 签署的储蓄罐基金代码
     * @apiSuccess (响应结果) {String} data.agreementState 海外储蓄罐协议状态 0-未签署、1-已签署、2-已终止， 空值转为0-未签署
     * @apiSuccess (响应结果) {String} data.agreementSignType 协议签署方式 1-线下自主申请开通 2-线上自主申请开通 3-到期自动续期 4-底层基金更换同意
     * @apiSuccess (响应结果) {String} data.agreementSignDt 协议签署日期
     * @apiSuccess (响应结果) {String} data.agreementSignExpiredDt 协议有效期
     * @apiSuccess (响应结果) {String} data.agreementCancelType 协议终止方式 1-线下自主申请关闭 2-线上自主申请关闭 3-底层基金更换未同意 4-底层基金更换不同意
     * @apiSuccess (响应结果) {String} data.agreementCancelDt 协议终止日期
     * @apiSuccessExample 响应结果示例
     * {"code":"RGK0F1s81z","data":{"agreementCancelType":"hv1SAb9t","agreementCancelDt":"ibmoMgN","piggyFundCodeList":["Vn66"],"agreementState":"MZpXXuT4Xx","agreementSignExpiredDt":"3mSq8","agreementSignDt":"PFNuC","agreementSignType":"Z"},"description":"XBEAD8F"}
     */
    @ResponseBody
    @GetMapping("/queryPiggyAgreementByHkTxAcctNo")
    public Response<HkCustPiggyAgreementVO> queryPiggyAgreementByHkTxAcctNo(@RequestParam(name="hkTxAcctNo") String hkTxAcctNo) {
        return Response.ok(hkCustRelatedAttributeService.getPiggyAgreementByHkTxAcctNo(hkTxAcctNo));
    }

    /**
     * @api {GET} /hkcustrelatedattribute/queryPiggyAgreementByCustNo queryPiggyAgreementByCustNo()
     * @apiVersion 1.0.0
     * @apiGroup HkCustRelatedAttributeController
     * @apiName queryPiggyAgreementByCustNo()
     * @apiDescription 根据 CRM客户号 查询 海外储蓄罐协议签署信息
     * @apiParam (请求参数) {String} custNo
     * @apiParamExample 请求参数示例
     * custNo=NzTGjxFu
     * @apiSuccess (响应结果) {String} code 状态码
     * @apiSuccess (响应结果) {String} description 描述信息
     * @apiSuccess (响应结果) {Object} data 数据封装
     * @apiSuccess (响应结果) {Array} data.piggyFundCodeList 签署的储蓄罐基金代码
     * @apiSuccess (响应结果) {String} data.agreementState 海外储蓄罐协议状态 0-未签署、1-已签署、2-已终止， 空值转为0-未签署
     * @apiSuccess (响应结果) {String} data.agreementSignType 协议签署方式 1-线下自主申请开通 2-线上自主申请开通 3-到期自动续期 4-底层基金更换同意
     * @apiSuccess (响应结果) {String} data.agreementSignDt 协议签署日期
     * @apiSuccess (响应结果) {String} data.agreementSignExpiredDt 协议有效期
     * @apiSuccess (响应结果) {String} data.agreementCancelType 协议终止方式 1-线下自主申请关闭 2-线上自主申请关闭 3-底层基金更换未同意 4-底层基金更换不同意
     * @apiSuccess (响应结果) {String} data.agreementCancelDt 协议终止日期
     * @apiSuccessExample 响应结果示例
     * {"code":"uTm","data":{"agreementCancelType":"oeYGwzi8","agreementCancelDt":"TnQxIen","piggyFundCodeList":["Hxuodqe"],"agreementState":"VkSN","agreementSignExpiredDt":"iGnd","agreementSignDt":"Rcvb","agreementSignType":"Skhu1lO0"},"description":"WVIC"}
     */
    @ResponseBody
    @GetMapping("/queryPiggyAgreementByCustNo")
    public Response<HkCustPiggyAgreementVO> queryPiggyAgreementByCustNo(@RequestParam(name = "custNo") String custNo) {
        return hkCustRelatedAttributeService.getPiggyAgreementByCustNo(custNo);
    }

    /**
     * @api {POST} /hkcustrelatedattribute/getPiggyAgreementDeal getPiggyAgreementDeal()
     * @apiVersion 1.0.0
     * @apiGroup HkCustRelatedAttributeController
     * @apiName getPiggyAgreementDeal()
     * @apiDescription 查询客户海外储蓄罐协议流水信息
     * @apiParam (请求体) {String} hkTxAcctNo 香港交易账号
     * @apiParam (请求体) {Array} tradeChannels 支持多个，英文逗号隔开，不传即所有      1-柜台；4-Wap（小程序）；9-CRM-PC；10-CRM移动端；11-APP；12-H5
     * @apiParam (请求体) {Array} busiCodes 支持多个，英文逗号隔开，不传即所有      0-无需审核、2-等待复核、3-审核通过、4-审核不通过
     * @apiParam (请求体) {Array} txChkFlags 审核状态      {@link com.howbuy.crm.account.client.enums.hkcustinfo.HkAcctTxChkFlagEnum}
     * @apiParam (请求体) {String} startUpdateDt yyyyMMdd      开始更新日期
     * @apiParam (请求体) {String} endUpdateDt yyyyMMdd      结束更新日期
     * @apiParamExample 请求体示例
     * {"hkTxAcctNo":"w3dzqG3","txChkFlags":["A"],"endUpdateDt":"ecJdE90","startUpdateDt":"YSiD6","tradeChannels":["0n7B7qEzSL"],"busiCodes":["hJk08hu9"]}
     * @apiSuccess (响应结果) {Number} page 当前第几页
     * @apiSuccess (响应结果) {Number} size 单页条数
     * @apiSuccess (响应结果) {Number} total 总条数
     * @apiSuccess (响应结果) {Array} rows 数据对象列表
     * @apiSuccess (响应结果) {String} rows.outOrderId 外部订单号
     * @apiSuccess (响应结果) {Array} rows.piggyFundCodeList 储蓄罐基金代码
     * @apiSuccess (响应结果) {String} rows.agreementSignType 协议签署方式 1-线下自主申请开通 2-线上自主申请开通 3-到期自动续期 4-底层基金更换同意
     * @apiSuccess (响应结果) {String} rows.agreementSignDt 协议签署日期 yyyyMMdd
     * @apiSuccess (响应结果) {String} rows.agreementCancelType 协议终止方式 1-线下自主申请关闭 2-线上自主申请关闭 3-底层基金更换未同意 4-底层基金更换不同意
     * @apiSuccess (响应结果) {String} rows.agreementCancelDt 协议终止日期 yyyyMMdd
     * @apiSuccess (响应结果) {Array} rows.filePathList 协议文件列表
     * @apiSuccess (响应结果) {String} rows.dealNo 订单号
     * @apiSuccess (响应结果) {String} rows.hkCustNo 香港客户号
     * @apiSuccess (响应结果) {String} rows.txCode 交易代码
     * @apiSuccess (响应结果) {String} rows.busiCode 业务代码      海外储蓄罐协议流水 127-签署 128-终止
     * @apiSuccess (响应结果) {String} rows.channel 渠道1-柜台 2-小程序      {@link com.howbuy.crm.account.client.enums.ChannelEnum}
     * @apiSuccess (响应结果) {String} rows.custChineseName 客户中文名
     * @apiSuccess (响应结果) {String} rows.custEnName 客户英文名称
     * @apiSuccess (响应结果) {String} rows.invstType 投资者类型0-机构,1-个人,2-产品户
     * @apiSuccess (响应结果) {String} rows.idType 证件类型
     * @apiSuccess (响应结果) {String} rows.idNoDigest 证件号码摘要
     * @apiSuccess (响应结果) {String} rows.idNoMask 证件号码掩码
     * @apiSuccess (响应结果) {String} rows.idNoCipher 证件号码密文
     * @apiSuccess (响应结果) {String} rows.appDt 申请日期
     * @apiSuccess (响应结果) {String} rows.appTm 申请时间
     * @apiSuccess (响应结果) {String} rows.bankAcctDigest 摘要-银行账号
     * @apiSuccess (响应结果) {String} rows.bankAcctMask 掩码-银行账号
     * @apiSuccess (响应结果) {String} rows.bankAcctCipher 密文-银行账号
     * @apiSuccess (响应结果) {String} rows.hkCpAcctNo 资金账号
     * @apiSuccess (响应结果) {String} rows.bankId 银行卡ID
     * @apiSuccess (响应结果) {String} rows.txChkFlag 交易审核标志0-不需审核； 1-等待审核； 2-审核不通过； 3-审核通过；
     * @apiSuccess (响应结果) {String} rows.checker 复核人
     * @apiSuccess (响应结果) {String} rows.creator 创建人
     * @apiSuccess (响应结果) {String} rows.txIp 交易ip地址
     * @apiSuccess (响应结果) {Number} rows.stimestamp 创建时间戳
     * @apiSuccess (响应结果) {Number} rows.updatedStimestamp 更新时间戳
     * @apiSuccess (响应结果) {String} rows.firstChecker 初审人
     * @apiSuccess (响应结果) {Number} rows.firstCheckDt 初审操作时间
     * @apiSuccess (响应结果) {Number} rows.checkDt 审核时间
     * @apiSuccess (响应结果) {String} rows.emailDigest 邮箱摘要
     * @apiSuccess (响应结果) {String} rows.emailMask 邮箱掩码
     * @apiSuccess (响应结果) {String} rows.emailCipher 邮箱密文
     * @apiSuccess (响应结果) {String} rows.mobileDigest 手机号摘要
     * @apiSuccess (响应结果) {String} rows.mobileMask 手机号掩码
     * @apiSuccess (响应结果) {String} rows.mobileCipher 手机号密文
     * @apiSuccess (响应结果) {String} rows.depositSerialNo 入金流水号
     * @apiSuccess (响应结果) {String} rows.checkReverseReason 审核不通过原因
     * @apiSuccessExample 响应结果示例
     * [{"agreementCancelType":"oAu1lxA","txChkFlag":"5","hkCpAcctNo":"R3Jr","filePathList":["7y"],"bankAcctMask":"MptLF","stimestamp":*************,"checkDt":*************,"checkReverseReason":"P51TCt","channel":"RFNkbI","updatedStimestamp":*************,"checker":"4ShkP5","agreementSignType":"p","depositSerialNo":"Ex","invstType":"TVLcH8LCJ","bankAcctCipher":"xr","mobileDigest":"KLGFYGIC","custChineseName":"2nG","appDt":"u2o31fz","custEnName":"13NJL","creator":"6Uc","firstChecker":"4d","idType":"bQL","piggyFundCodeList":["WKVUcD"],"hkCustNo":"xxPacXuqP","busiCode":"dgiyWSuckn","agreementSignDt":"IfKD1J","idNoDigest":"AA9D","emailDigest":"Z","dealNo":"FZPHX","idNoMask":"9fiMM","mobileCipher":"o4irJKDTCW","bankAcctDigest":"Eru5wGmtW","firstCheckDt":*************,"appTm":"5QmEJ","agreementCancelDt":"ST","bankId":"4T","outOrderId":"38","emailMask":"fxk4Yi","txCode":"ly","idNoCipher":"9hlvuPJ27","txIp":"M","emailCipher":"4hUyP","mobileMask":"jHIh"}]
     */
    @ResponseBody
    @PostMapping("/getPiggyAgreementDeal")
    public Response<PageVO<HkPiggyAgreementDealVO>> getPiggyAgreementDeal(@RequestBody HkPiggyAgreementDealRequest queryReq){
        return Response.ok(hkCustRelatedAttributeService.getPiggyAgreementDeal(queryReq));
    }

    /**
     * @api {POST} /hkcustrelatedattribute/getHkAcctOptDeal getHkAcctOptDeal()
     * @apiVersion 1.0.0
     * @apiGroup HkCustRelatedAttributeController
     * @apiName getHkAcctOptDeal()
     * @apiDescription 香港账户中心开户订单流水查询接口
     * @apiParam (请求参数) {String} hkTxAcctNo 香港交易账号
     * @apiParam (请求参数) {String} startOperateDt 操作开始日期 yyyyMMdd
     * @apiParam (请求参数) {String} endOperateDt 操作结束日期 yyyyMMdd
     * @apiParam (请求参数) {Array} busiCodeList 业务代码
     * @apiParam (请求参数) {String} operateCode 操作代码 00-自助 01-申请提交 02-再次提交 03-初审通过 04-驳回至初审 05-驳回至客户 07-审核不通过 08-作废
     * @apiParam (请求参数) {Number} page 页号
     * @apiParam (请求参数) {Number} rows 每页大小
     * @apiParamExample 请求参数示例
     * hkTxAcctNo=1ikW&endOperateDt=iMfi&busiCodeList=VJnu4h&startOperateDt=Z9qlJiQ80&page=9298&rows=807&operateCode=oU7
     * @apiSuccess (响应结果) {Number} page 当前第几页
     * @apiSuccess (响应结果) {Number} size 单页条数
     * @apiSuccess (响应结果) {Number} total 总条数
     * @apiSuccess (响应结果) {Array} rows 数据对象列表
     * @apiSuccess (响应结果) {String} rows.dealNo 订单号
     * @apiSuccess (响应结果) {String} rows.busiCode 业务代码
     * @apiSuccess (响应结果) {String} rows.operateCode 操作码
     * @apiSuccess (响应结果) {String} rows.operator 操作人
     * @apiSuccess (响应结果) {String} rows.hkCustNo 香港客户号
     * @apiSuccess (响应结果) {Number} rows.stimestamp 创建时间
     * @apiSuccess (响应结果) {Object} rows.rejectContent 驳回内容 json
     * @apiSuccess (响应结果) {String} rows.curCustState 当时客户状态 0-正常 1-注销 2-休眠 3-注册 4-开户申请成功
     * @apiSuccess (响应结果) {String} rows.curAppDt 当时订单申请日期
     * @apiSuccess (响应结果) {String} rows.curAppTm 当时订单申请时间
     * @apiSuccessExample 响应结果示例
     * {"total":429,"size":7922,"page":1371,"rows":[{"rejectContent":{},"curAppTm":"jtmnWe","stimestamp":1037341921272,"busiCode":"dw0zOdnpp8","hkCustNo":"ghLNr","curCustState":"e98","dealNo":"SdLU5cY3","operator":"LObNEfT","operateCode":"GtRK","curAppDt":"5ea7W7j"}]}
     */
    @ResponseBody
    @PostMapping("/getHkAcctOptDeal")
    public Response<PageVO<HkAcctOptDealVO>> getHkAcctOptDeal(@RequestBody HkAcctOptDealRequest queryReq){
        return Response.ok(hkCustRelatedAttributeService.getHkAcctOptDeal(queryReq));
    }

}