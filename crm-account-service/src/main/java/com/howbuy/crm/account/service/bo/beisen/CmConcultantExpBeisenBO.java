/**
 * Copyright (c) 2024, <PERSON><PERSON><PERSON><PERSON> HOWBUY INVESTMENT MANAGEMENT Co., Ltd.
 * All right reserved.
 * <p>
 * THIS IS UNPUBLISHED PROPRIETARY SOURCE CODE OF HOWBUY INVESTMENT
 * MANAGEMENT CO., LTD.  THE CONTENTS OF THIS FILE MAY NOT BE DISCLOSED
 * TO THIRD PARTIES, COPIED OR DUPLICATED IN ANY FORM, IN WHOLE OR IN PART,
 * WITHOUT THE PRIOR WRITTEN PERMISSION OF HOWBUY INVESTMENT MANAGEMENT
 * CO., LTD.
 */
package com.howbuy.crm.account.service.bo.beisen;

import com.howbuy.crm.account.dao.po.beisen.CmBeisenUserInfoPO;
import com.howbuy.crm.account.dao.po.beisenorg.CmBeisenOrgConfigPO;
import com.howbuy.crm.account.dao.po.beisenposlevel.CmBeisenPosLevelConfigPO;
import com.howbuy.crm.account.dao.po.constant.HbConstantPO;
import com.howbuy.crm.account.dao.po.consultantexp.CmConsultantExpPO;
import com.howbuy.crm.account.dao.po.consultantexp.CmConsultantExpModifyFlagPO;
import com.howbuy.crm.account.dao.po.consultantexp.CmConsultantExpZjSalPO;
import lombok.Data;

import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * @description: (请在此添加描述)
 * <AUTHOR>
 * @date 2024/11/14 18:32
 * @since JDK 1.8
 */
@Data
public class CmConcultantExpBeisenBO {
    /**
     * 北森数据对象
     */
    private CmBeisenUserInfoPO cmBeisenUserInfoPO;
    /**
     * 字段是否可同步对象
     */
    private CmConsultantExpModifyFlagPO syncFlag;
    /**
     * 北森组织架构map（key：架构名称，value：架构ID）
     */
    private Map<String, String> beisenOrgDOMap;
    /**
     * crm和北森架构映射map(key: 北森架构ID，value：映射值)
     */
    private Map<String, CmBeisenOrgConfigPO> cmBeisenOrgConfigMap;
    /**
     * crm和北森职级映射map(key: 北森职级编码，value：映射值)
     */
    private Map<String, CmBeisenPosLevelConfigPO> cmBeisenPosLevelConfigPOMap;
    /**
     * 职级对应层级代码map(key: 职级代码，value：映射值)
     */
    private Map<String, HbConstantPO> hrPositionsLevelMap;

    /**
     * 当前投顾对应花名册数据
     */
    private CmConsultantExpPO exp;
    /**
     * 有效职级薪资
     */
    private Map<String, List<CmConsultantExpZjSalPO>> zjSalMap;
    /**
     * key:市名称， value：市code,省code
     */
    Map<String, AreaBO> cityMapByProv = new HashMap<>();
    /**
     * key:区名称， value: 区code, 市code, 省code
     */
    Map<String, AreaBO> countryMapByCity = new HashMap<>();

    /**
     * 北森数据对象Map（key: 邮箱，value：映射值）
     */
    private Map<String, CmBeisenUserInfoPO> cmBeisenUserInfoByEmailMap;
}