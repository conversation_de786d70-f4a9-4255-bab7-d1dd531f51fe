/**
 * Copyright (c) 2023, <PERSON><PERSON><PERSON><PERSON> HOWBUY INVESTMENT MANAGEMENT Co., Ltd.
 * All right reserved.
 * <p>
 * THIS IS UNPUBLISHED PROPRIETARY SOURCE CODE OF HOWBUY INVESTMENT
 * MANAGEMENT CO., LTD.  THE CONTENTS OF THIS FILE MAY NOT BE DISCLOSED
 * TO THIRD PARTIES, COPIED OR DUPLICATED IN ANY FORM, IN WHOLE OR IN PART,
 * WITHOUT THE PRIOR WRITTEN PERMISSION OF HOWBUY INVESTMENT MANAGEMENT
 * CO., LTD.
 */
package com.howbuy.crm.account.service.repository.custinfo;

import com.howbuy.crm.account.client.enums.custinfo.CustBindRelationTypeEnum;
import com.howbuy.crm.account.client.response.Response;
import com.howbuy.crm.account.dao.mapper.CommonMapper;
import com.howbuy.crm.account.dao.mapper.custinfo.CmConscustMapper;
import com.howbuy.crm.account.dao.mapper.custinfo.CmHboneHkRelationLogMapper;
import com.howbuy.crm.account.dao.po.custinfo.CmHboneHkRelationLogPO;
import com.howbuy.crm.account.service.commom.constant.Constants;
import com.howbuy.crm.account.service.commom.constant.SequenceConstants;
import com.howbuy.crm.account.service.req.custinfo.HboneCustRelationOptReqVO;
import com.howbuy.crm.account.service.req.custinfo.HkCustRelationOptReqVO;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import org.springframework.transaction.annotation.Propagation;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.Assert;

import java.util.Date;

/**
 * @description: (投顾客户 一账通 绑定关系  repository)
 * <AUTHOR>
 * @date 2023/12/8 15:31
 * @since JDK 1.8
 */
@Component
@Slf4j
@Transactional(rollbackFor = Exception.class, propagation = Propagation.SUPPORTS)
public class CmHboneCustRelationRepository {

    @Autowired
    private CmConscustMapper conscustMapper;

    @Autowired
    private CmHboneHkRelationLogMapper hkHboneRelationLogMapper;

    @Autowired
    private CommonMapper commonMapper;



    /**
     * @description:(插入 一账通账户 vs 投顾客户 绑定关系)
     * @param bindVo
     * @return com.howbuy.crm.account.client.response.Response<java.lang.String>
     * @author: haoran.zhang
     * @date: 2023/12/15 11:26
     * @since JDK 1.8
     */
    @Transactional(rollbackFor = Exception.class, propagation = Propagation.REQUIRED)
    public Response<String> executeBind(HboneCustRelationOptReqVO bindVo) {
        String custNo=bindVo.getCustNo();
        String hboneNo=bindVo.getHboneNo();
        String operator=bindVo.getOperator();


        Assert.notNull(hboneNo,"一账通账号不能为空");
        Assert.notNull(custNo,"投顾客户号不能为空");
        Assert.notNull(operator,"操作人不能为空");


        //更新一账通 与投顾客户关联关系
        int updateCount=conscustMapper.upHboneNoWhenNull(custNo, hboneNo);
        log.info("更新投顾客户号：{} 一账通号：{} 关联关系，更新条数：{}",custNo,hboneNo,updateCount);
        if(updateCount!=1){
            return Response.fail(String.format("custNo:%s, hboneNo:%s 更新关联关系失败！",
                    custNo,hboneNo));
        }

        // 账户关联关系
        // 账户关联关系
        CmHboneHkRelationLogPO relationLog= prepareHboneRelationLogPo(Constants.CUST_RELATION_OPER_TYPE_BIND);
        relationLog.setCustNo(custNo);
        relationLog.setAcctNo(hboneNo);
        relationLog.setCreator(operator);
        //请求来源  非必须项
        relationLog.setOperateSource(bindVo.getOperateSource());
        relationLog.setOperateChannel(bindVo.getOperateChannel());
        relationLog.setRemark(bindVo.getRemark());
        hkHboneRelationLogMapper.insert( relationLog);

        return  Response.ok();
    }


    /**
     * @description:(解除 一账通账户 vs 投顾客户 绑定关系)
     * @param bindVo
     * @return com.howbuy.crm.account.client.response.Response<java.lang.String>
     * @author: haoran.zhang
     * @date: 2024/1/9 10:33
     * @since JDK 1.8
     */
    @Transactional(rollbackFor = Exception.class, propagation = Propagation.REQUIRED)
    public Response<String> executeUnBind(HboneCustRelationOptReqVO bindVo) {
        String custNo=bindVo.getCustNo();
        String hboneNo=bindVo.getHboneNo();
        String operator=bindVo.getOperator();

        Assert.notNull(hboneNo,"一账通账号不能为空");
        Assert.notNull(custNo,"投顾客户号不能为空");
        Assert.notNull(operator,"操作人不能为空");


        //更新 一账通 与投顾客户 数据。  更新为 null
        int updateCount=conscustMapper.upHboneNoToNull(custNo, hboneNo);
        log.info("解除 投顾客户号：{} 一账通号：{} 关联关系，更新条数：{}",custNo,hboneNo,updateCount);
        if(updateCount!=1){
            return Response.fail(String.format("custNo:%s, hboneNo:%s 解除关联关系失败！",
                    custNo,hboneNo));
        }
        // 账户关联关系
        // 账户关联关系
        CmHboneHkRelationLogPO relationLog= prepareHboneRelationLogPo(Constants.CUST_RELATION_OPER_TYPE_UNBIND);
        relationLog.setCustNo(custNo);
        relationLog.setAcctNo(hboneNo);
        relationLog.setCreator(operator);
        //请求来源  非必须项
        relationLog.setOperateSource(bindVo.getOperateSource());
        relationLog.setOperateChannel(bindVo.getOperateChannel());
        relationLog.setRemark(bindVo.getRemark());
        hkHboneRelationLogMapper.insert( relationLog);
        return  Response.ok();
    }



    /**
     * @description:(准备通用的  relation对象 用于记录日志)
     * @param operateType
     * @return com.howbuy.crm.account.dao.po.custinfo.CmHboneHkRelationLogPO
     * @author: haoran.zhang
     * @date: 2023/12/15 14:41
     * @since JDK 1.8
     */
    private CmHboneHkRelationLogPO prepareHboneRelationLogPo(String  operateType){
        CmHboneHkRelationLogPO relationLog=new CmHboneHkRelationLogPO();
        relationLog.setId(commonMapper.getSeqValue(SequenceConstants.SEQ_CM_CUST_RELATION_LOG));
        relationLog.setRelationType(CustBindRelationTypeEnum.HBONE.getCode());
        relationLog.setOperateType(operateType);
        relationLog.setCreateTimestamp(new Date());
        return relationLog;
    }

}