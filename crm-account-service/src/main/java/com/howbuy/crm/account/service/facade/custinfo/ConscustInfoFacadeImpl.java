/**
 * Copyright (c) 2024, <PERSON><PERSON><PERSON><PERSON> HOWBUY INVESTMENT MANAGEMENT Co., Ltd.
 * All right reserved.
 * <p>
 * THIS IS UNPUBLISHED PROPRIETARY SOURCE CODE OF HOWBUY INVESTMENT
 * MANAGEMENT CO., LTD.  THE CONTENTS OF THIS FILE MAY NOT BE DISCLOSED
 * TO THIRD PARTIES, COPIED OR DUPLICATED IN ANY FORM, IN WHOLE OR IN PART,
 * WITHOUT THE PRIOR WRITTEN PERMISSION OF HOWBUY INVESTMENT MANAGEMENT
 * CO., LTD.
 */
package com.howbuy.crm.account.service.facade.custinfo;

import com.google.common.collect.Lists;
import com.howbuy.acc.common.utils.DigestUtil;
import com.howbuy.crm.account.client.facade.custinfo.ConscustInfoFacade;
import com.howbuy.crm.account.client.request.custinfo.*;
import com.howbuy.crm.account.client.response.PageVO;
import com.howbuy.crm.account.client.response.Response;
import com.howbuy.crm.account.client.response.consultantinfo.CmCustHisConsultantVO;
import com.howbuy.crm.account.client.response.consultantinfo.CmCustSimpleListVO;
import com.howbuy.crm.account.client.response.custinfo.*;
import com.howbuy.crm.account.service.outerservice.acct.HboneAcctInfoOuterService;
import com.howbuy.crm.account.service.service.consultantinfo.ConsultantInfoService;
import com.howbuy.crm.account.service.service.custinfo.ConsCustInfoService;
import org.apache.commons.lang3.StringUtils;
import org.apache.dubbo.config.annotation.DubboService;

import javax.annotation.Resource;
import java.util.List;
import java.util.Objects;

/**
 * <AUTHOR>
 * @description: (请在此添加描述)
 * @date 2024/9/9 14:01
 * @since JDK 1.8
 */
@DubboService
public class ConscustInfoFacadeImpl implements ConscustInfoFacade {

    @Resource
    private ConsCustInfoService consCustInfoService;

    @Resource
    private ConsultantInfoService consultantInfoService;

    @Resource
    private HboneAcctInfoOuterService hboneAcctInfoOuterService;

    /**
     * 是内部员工
     */
    private final static String IS_INTERNAL_EMPLOYEE = "1";

    /**
     * 不是内部员工
     */
    private final static String IS_NOT_INTERNAL_EMPLOYEE = "0";


    @Override
    public Response<CmCustSimpleListVO> queryCustSimpleByHboneNo(QueryCustSimpleRequest request) {
        return Response.ok(consCustInfoService.queryCustSimpleByHboneNo(request.getHboneNo()));
    }



    @Override
    public Response<PageVO<CmConsCustSimpleVO>> batchQueryCustSimple(BatchQueryCustSimpleRequest request){
        PageVO<CmConsCustSimpleVO> pageVO=consCustInfoService.queryCustSimpleBySearchVo(request);
        return Response.ok(pageVO);
    }

    @Override
    public Response<CmCustHisConsultantVO> queryCustHisConsultantList(QueryCustHisConsultantRequest request) {
        return Response.ok(consCustInfoService.queryCustHisConsultantList(request.getConscustno()));
    }


    @Override
    public Response<CustListByConsCodeVO> pageQueryCustSimple(PageQueryCustSimpleRequest request) {
        return Response.ok(consCustInfoService.pageQueryCustSimple(request));
    }

    @Override
    public Response<QueryCustIsInternalEmployeeVO> queryCustIsInternalEmployee(QueryCustIsInternalEmployeeRequest request) {
        Response<QueryCustIsInternalEmployeeVO> response = Response.ok();
        QueryCustIsInternalEmployeeVO queryCustIsInternalEmployeeVO = new QueryCustIsInternalEmployeeVO();
        // 默认不是内部员工
        queryCustIsInternalEmployeeVO.setInternalEmployeeFlag(IS_NOT_INTERNAL_EMPLOYEE);
        response.setData(queryCustIsInternalEmployeeVO);

        CmConsCustVO cmConsCustVO = consCustInfoService.queryCustInfoByCustNo(request.getConsCustNo());
        if (Objects.isNull(cmConsCustVO) || StringUtils.isBlank(cmConsCustVO.getMobileDigest())) {
            return response;
        }

        // 查询hbone手机号
        HboneAcctCustDetailInfoVO acctCustDetailInfoVO
                = hboneAcctInfoOuterService.queryHboneCustDetailInfo(cmConsCustVO.getHboneNo());
        if (Objects.isNull(acctCustDetailInfoVO) || StringUtils.isBlank(acctCustDetailInfoVO.getMobileDigest())) {
            return response;
        }

        Boolean consultantFlag = consultantInfoService.queryMobileIsConsultant(acctCustDetailInfoVO.getMobileDigest());
        if (consultantFlag) {
            queryCustIsInternalEmployeeVO.setInternalEmployeeFlag(IS_INTERNAL_EMPLOYEE);
        }

        return response;
    }

}
