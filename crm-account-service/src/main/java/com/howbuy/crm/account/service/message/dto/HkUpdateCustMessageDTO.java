/**
 * Copyright (c) 2023, <PERSON><PERSON><PERSON><PERSON> HOWBUY INVESTMENT MANAGEMENT Co., Ltd.
 * All right reserved.
 * <p>
 * THIS IS UNPUBLISHED PROPRIETARY SOURCE CODE OF HOWBUY INVESTMENT
 * MANAGEMENT CO., LTD.  THE CONTENTS OF THIS FILE MAY NOT BE DISCLOSED
 * TO THIRD PARTIES, COPIED OR DUPLICATED IN ANY FORM, IN WHOLE OR IN PART,
 * WITHOUT THE PRIOR WRITTEN PERMISSION OF HOWBUY INVESTMENT MANAGEMENT
 * CO., LTD.
 */
package com.howbuy.crm.account.service.message.dto;

import lombok.Data;

import java.io.Serializable;

/**
 * @description: (账户中心 香港消息 [TOPIC_HK_UPDATE_CUST]-[客户信息变更消息]公共解析 dto)
 * <AUTHOR>
 * @date 2023/12/29 11:23
 * @since JDK 1.8
 */
@Data
public class HkUpdateCustMessageDTO implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 香港客户号(hkCustNo)
     */
    private String hkCustNo;


    /**
     * 证件类型(idType)
     */
    private String idType;

    /**
     * 证件号码摘要(idNoDigest)
     */
    private String idNoDigest;

    /**
     * 证件号码掩码(idNoMask)
     */
    private String idNoMask;

    /**
     * 证件号码密文(idNoCipher)
     */
    private String idNoCipher;

    //手机号修改  （子标签tag：MOBILE_UPDATE）

    /**
     * 手机号码区号(mobileAreaCode)
     */
    private String mobileAreaCode;

    /**
     *  手机号码摘要(mobileDigest)
     */
    private String mobileDigest;

    /**
     * 手机号码掩码(mobileMask)
     */
    private String mobileMask;

      /**
         * 手机号码密文(mobileCipher)
         */
    private String mobileCipher;

    //证件信息修改  （子标签tag：ID_INFO_UPDATE）

    /**
     * 英文名称(custEnName)
     */
    private String custEnName;

    /**
     * 中文名称(custChineseName)
     */
    private String custChineseName;

}