package com.howbuy.crm.account.service.vo.custinfo;

import lombok.Data;

/**
 * @description: (一账通|香港  客户信息公共属性 处理 )
 * <AUTHOR>
 * @date 2023/12/11 10:57
 * @since JDK 1.8
 */

/**
    * 客户异常信息表
    */
@Data
public class MessageCustInfoVO {

    /**
    * 消息通知的clientId
    */
    private String messageClientId;

    /**
    * rocketMq的消息ID，全局唯一 （messageClientId 记录的是rocketMq客户端id，没有意义）
    */
    private String messageId;


    /**
    * 创建人
    */
    private String creator;


    /**
     * 异常来源：1-香港注册2-香港开户7-好买开户 [暂时同于  7-分销开户]3-香港客户信息同步4-香港客户号/一账通绑定5-一账通实名
     */
    private String abnormalSource;


    /**
     * 操作通道 1-MQ  2-菜单页面
     */
    private String operateChannel;


    /**
     * 香港客户号
     */
    private String hkTxAcctNo;


    /**
     * 一账通号
     */
    private String hboneNo;




}