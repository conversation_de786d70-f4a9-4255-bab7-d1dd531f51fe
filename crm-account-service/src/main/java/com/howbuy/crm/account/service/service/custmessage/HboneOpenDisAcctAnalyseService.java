/**
 * Copyright (c) 2023, <PERSON><PERSON><PERSON><PERSON> HOWBUY INVESTMENT MANAGEMENT Co., Ltd.
 * All right reserved.
 * <p>
 * THIS IS UNPUBLISHED PROPRIETARY SOURCE CODE OF HOWBUY INVESTMENT
 * MANAGEMENT CO., LTD.  THE CONTENTS OF THIS FILE MAY NOT BE DISCLOSED
 * TO THIRD PARTIES, COPIED OR DUPLICATED IN ANY FORM, IN WHOLE OR IN PART,
 * WITHOUT THE PRIOR WRITTEN PERMISSION OF HOWBUY INVESTMENT MANAGEMENT
 * CO., LTD.
 */
package com.howbuy.crm.account.service.service.custmessage;

import com.google.common.collect.Lists;
import com.howbuy.common.utils.StringUtil;
import com.howbuy.crm.account.client.enums.custinfo.AbnormaSceneTypeEnum;
import com.howbuy.crm.account.client.enums.custinfo.FullCustSourceEnum;
import com.howbuy.crm.account.client.response.custinfo.HkAcctCustDetailInfoVO;
import com.howbuy.crm.account.dao.bo.custinfo.CmConscustForAnalyseBO;
import com.howbuy.crm.account.service.business.custinfo.CustSearchBusinessService;
import com.howbuy.crm.account.service.outerservice.hkacct.HkCustBasicInfoOuterService;
import com.howbuy.crm.account.service.repository.custinfo.AbnormalCustRepository;
import com.howbuy.crm.account.service.vo.custinfo.AbnormalAnalyseResultVO;
import com.howbuy.crm.account.service.vo.custinfo.HboneMessageCustInfoVO;
import com.howbuy.crm.account.service.vo.custinfo.SearchCustKeyAttrVO;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * @description: ([一账通账户]-[开通分销交易账户] 消息)
 * <AUTHOR>
 * @date 2023/12/14 10:12
 * @since JDK 1.8
 */
@Service
@Slf4j
public class HboneOpenDisAcctAnalyseService implements CustMessageAnalyseService<HboneMessageCustInfoVO> {

    @Autowired
    private AbnormalCustRepository abnormalCustRepository;
    @Autowired
    private HkCustBasicInfoOuterService hkCustBasicInfoOuterService;

    @Autowired
    private CustSearchBusinessService custSearchBusinessService;
    @Override
    public List<FullCustSourceEnum>  getSourceList() {
        return Lists.newArrayList(FullCustSourceEnum.DISTRIBUTION_OPEN_ACCOUNT,
                FullCustSourceEnum.HBONE_REAL_NAME_REGISTER,
                FullCustSourceEnum.HBONE_CUST_REAL_NAME_CHANGE);
    }

    @Override
    public AbnormalAnalyseResultVO<HboneMessageCustInfoVO> analyze(HboneMessageCustInfoVO analyseVo) {
        String hboneNo=analyseVo.getHboneNo();

//      判断①：开户的【一账通号A】是否已绑定【投顾客户号】
        List<CmConscustForAnalyseBO> existByHboneNoList= abnormalCustRepository.queryCustBOByHboneNo(hboneNo);
        //开发自定义流程： 历史数据存在hboneNo绑定多个custNo的情况，此处不做处理
        if(!CollectionUtils.isEmpty(existByHboneNoList) && existByHboneNoList.size()>1){
            log.error("【{}】消息处理异常：一账通号[{}]绑定了多个投顾客户号",
                    FullCustSourceEnum.getDescription(analyseVo.getAbnormalSource()),
                    hboneNo);
            return AbnormalAnalyseResultVO.
                    notNormalData(analyseVo,AbnormaSceneTypeEnum.HBONE_NO_MATCH_MULTIPLE_CUST_NO,existByHboneNoList);
        }
        CmConscustForAnalyseBO custK=CollectionUtils.isEmpty(existByHboneNoList)?null:existByHboneNoList.get(0);
        //若不存在，则不处理
        if(custK!=null){
            return analyseExistsCust(custK,analyseVo);
        }

        //不存在客户， 尝试根据条件匹配现有 客户。是否能匹配到唯一的【投顾客户】
        SearchCustKeyAttrVO searchAttrInfo=new SearchCustKeyAttrVO();
        searchAttrInfo.setInvstType(analyseVo.getInvestType());
        searchAttrInfo.setCustName(analyseVo.getCustName());
        searchAttrInfo.setIdNoDigest(analyseVo.getIdNoDigest());
        searchAttrInfo.setIdSignAreaCode(analyseVo.getIdSignAreaCode());
        searchAttrInfo.setIdType(analyseVo.getIdType());
        searchAttrInfo.setMobileAreaCode(analyseVo.getMobileAreaCode());
        searchAttrInfo.setMobileDigest(analyseVo.getMobileDigest());
        AbnormalAnalyseResultVO<HboneMessageCustInfoVO>  searchAnalyseVo=custSearchBusinessService.searchMatchedCust(analyseVo, searchAttrInfo);
        if(!searchAnalyseVo.isAnalysePass()){
            return searchAnalyseVo;
        }
        CmConscustForAnalyseBO  matchedOneCust=searchAnalyseVo.getProcessedCustInfo();
        //校验 复杂搜索后的客户对象，是否 满足
//        判断⑤：匹配上的唯一【投顾客户号A】是否已绑定【一账通号】
//（1）若未绑定，则绑定【一账通号A】和【投顾客户号A】，并更新投顾客户信息，详见节点5-CRM更新投顾客户信息（取一账通客户信息），并继续判断⑥
//（2）若已绑定当前开户的【一账通号A】，则继续判断⑥
//（3）若已绑定其他【一账通号B】（此时不会自动换绑），则异常数据进“一账通异常客户表”：
//        匹配异常的【一账通号】落“一账通号异常客户主表”，写入字段：
//        a、异常来源：分销开户
//        b、异常描述：匹配到的投顾客户号已被占用
//        c、异常时间：取落入异常表的时间，格式yyyymmdd hh:mm:ss
//        匹配到的【投顾客户号】落“一账通异常客户待关联表”，与主表数据相对应。
        if(matchedOneCust!=null){
            if(StringUtil.isNotBlank(matchedOneCust.getHboneNo()) &&
                    Boolean.FALSE.equals(StringUtil.isEqual(hboneNo,matchedOneCust.getHboneNo()))){
                return AbnormalAnalyseResultVO.notNormalData(analyseVo,
                        AbnormaSceneTypeEnum.MATCH_CUST_NO_OCCUPIED,
                        Lists.newArrayList(matchedOneCust));
            }
        }
        AbnormalAnalyseResultVO<HboneMessageCustInfoVO> resultVo=AbnormalAnalyseResultVO.normalData(analyseVo);
        //复杂搜索匹配到的客户信息
        resultVo.setProcessedCustInfo(matchedOneCust);
        return resultVo;

    }


    /**
     * @description:(已关联的客户信息分析)
     * @param custK
     * @param analyseVo
     * @return com.howbuy.crm.account.service.vo.custinfo.AbnormalAnalyseResultVO<com.howbuy.crm.account.service.vo.custinfo.HboneMessageCustInfoVO>
     * @author: haoran.zhang
     * @date: 2024/1/5 15:40
     * @since JDK 1.8
     */
    private AbnormalAnalyseResultVO<HboneMessageCustInfoVO> analyseExistsCust(CmConscustForAnalyseBO custK, HboneMessageCustInfoVO analyseVo){
        AbnormalAnalyseResultVO<HboneMessageCustInfoVO> returnAnalyseVo=AbnormalAnalyseResultVO.normalData(analyseVo);

//        2）若已绑定【投顾客户号K】，则判断【投顾客户号K】是否已绑定【实名香港客户号】
        boolean hkRealName=false;
        HkAcctCustDetailInfoVO hkCustDetail=null;
        if(StringUtil.isNotBlank(custK.getHkTxAcctNo())){
            hkCustDetail=hkCustBasicInfoOuterService.queryHkCustDetailInfo(custK.getHkTxAcctNo());
            hkRealName= hkCustBasicInfoOuterService.isRealName(hkCustDetail);
        }
        log.info("处理消息,来源[{}],hboneNo:{},匹配到唯一客户号：{}，绑定的香港客户号：{}，是否实名：{}",
                FullCustSourceEnum.getDescription(analyseVo.getAbnormalSource()),
                analyseVo.getHboneNo(),
                custK.getConscustno(),
                custK.getHkTxAcctNo(),
                hkRealName);
//        若未绑定，则更新投顾客户信息，详见节点5-CRM更新投顾客户信息（取一账通客户信息）（说明香港一定未开户，此时直接用一账通客户信息更新投顾客户信息）
//        若已绑定，则判断【实名香港客户号】证件号和【一账通号A】证件号是否一致
//        a、若一致，则无需处理（此时三类账号关联关系已存在）
//        b、若不一致，则异常数据进“一账通异常客户表”，写入以下信息：
//        匹配异常的【一账通号】落“一账通异常客户主表”，写入字段：
//        a、异常来源：分销开户
//        b、异常描述：分销开户证件与香港实名证件不一致
//        c、异常时间：取落入异常表的时间，格式yyyymmdd hh:mm:ss
//        匹配到的【投顾客户号】落“一账通异常客户待关联表”，与主表数据相对应。

        boolean needProcess=true;
        if(hkRealName){
            if(Boolean.FALSE.equals(StringUtil.isEqual(hkCustDetail.getIdNoDigest(),analyseVo.getIdNoDigest()))){
                return AbnormalAnalyseResultVO.
                        notNormalData(analyseVo,AbnormaSceneTypeEnum.HBONE_REAL_NAME_ID_TYPE_ID_NO_NOT_MATCH,Lists.newArrayList(custK));
            }else{
                //若一致，则无需处理（此时三类账号关联关系已存在）
                // 对于 [7-分销开户]  后续一定做处理
                // 其余 [一账通实名 | 一账通客户实名消息]  后续标记：无需处理
                if(!FullCustSourceEnum.DISTRIBUTION_OPEN_ACCOUNT.getCode().equals(analyseVo.getAbnormalSource())){
                    needProcess=false;
                }
            }
        }

        if(needProcess){
            returnAnalyseVo.setProcessedCustInfo(custK);
        }
        returnAnalyseVo.setNeedProcess(needProcess);
        return returnAnalyseVo;
    }

}