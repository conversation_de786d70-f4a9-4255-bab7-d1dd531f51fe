/**
 * Copyright (c) 2023, <PERSON><PERSON><PERSON><PERSON> HOWBUY INVESTMENT MANAGEMENT Co., Ltd.
 * All right reserved.
 * <p>
 * THIS IS UNPUBLISHED PROPRIETARY SOURCE CODE OF HOWBUY INVESTMENT
 * MANAGEMENT CO., LTD.  THE CONTENTS OF THIS FILE MAY NOT BE DISCLOSED
 * TO THIRD PARTIES, COPIED OR DUPLICATED IN ANY FORM, IN WHOLE OR IN PART,
 * WITHOUT THE PRIOR WRITTEN PERMISSION OF HOWBUY INVESTMENT MANAGEMENT
 * CO., LTD.
 */
package com.howbuy.crm.account.service.outerservice.acct;

import com.howbuy.acccenter.facade.trade.chghkassetisolateflag.ChgHkAssetIsolateFlagFacade;
import com.howbuy.acccenter.facade.trade.chghkassetisolateflag.ChgHkAssetIsolateFlagRequest;
import com.howbuy.acccenter.facade.trade.chghkassetisolateflag.ChgHkAssetIsolateFlagResponse;
import com.howbuy.crm.account.client.request.custinfo.UpdateHkAssetIsolateRequest;
import com.howbuy.crm.account.client.response.Response;
import com.howbuy.crm.account.service.commom.constant.DobboReferenceConstant;
import lombok.extern.slf4j.Slf4j;
import org.apache.dubbo.config.annotation.DubboReference;
import org.springframework.stereotype.Service;

/**
 * @description: (好买账户中心- 一账通  更新一账通相关属性 outer service)
 * <AUTHOR>
 * @date 2023/12/8 20:15
 * @since JDK 1.8
 */
@Slf4j
@Service
public class HboneUpdateOuterService  extends AbstractAcctOuterService {


    @DubboReference(registry = DobboReferenceConstant.ACC_CENTER_SERVER, check = false)
    private ChgHkAssetIsolateFlagFacade chgHkAssetIsolateFlagFacade;



    /**
     * @description:(更新一账通资产隔离标志)
     * @param isolateRequest
     * @return com.howbuy.crm.account.client.response.Response<java.lang.String>
     * @author: haoran.zhang
     * @date: 2024/5/29 9:59
     * @since JDK 1.8
     */
    public Response<String> updateHkAssetIsolateFlag(UpdateHkAssetIsolateRequest isolateRequest){
        ChgHkAssetIsolateFlagRequest request=new ChgHkAssetIsolateFlagRequest();
        request.setHboneNo(isolateRequest.getHboneNo());
        request.setHkAssetIsolateFlag(isolateRequest.getHkAssetIsolateFlag());
        request.setHkAssetIsolateFlagUpDt(isolateRequest.getHkAssetIsolateFlagUpDt());
        ChgHkAssetIsolateFlagResponse resp= chgHkAssetIsolateFlagFacade.execute(request);
        return  transferAcctResponse(resp);
    }




}