/**
 * Copyright (c) 2023, <PERSON><PERSON><PERSON><PERSON> HOWBUY INVESTMENT MANAGEMENT Co., Ltd.
 * All right reserved.
 * <p>
 * THIS IS UNPUBLISHED PROPRIETARY SOURCE CODE OF HOWBUY INVESTMENT
 * MANAGEMENT CO., LTD.  THE CONTENTS OF THIS FILE MAY NOT BE DISCLOSED
 * TO THIRD PARTIES, COPIED OR DUPLICATED IN ANY FORM, IN WHOLE OR IN PART,
 * WITHOUT THE PRIOR WRITTEN PERMISSION OF HOWBUY INVESTMENT MANAGEMENT
 * CO., LTD.
 */
package com.howbuy.crm.account.service.message.dto;

import lombok.Data;

import java.io.Serializable;

/**
 * @description: (账户中心 香港消息 [TOPIC_HK_CUST_HBONE_REL]-[香港/一账通绑定消息]公共解析 dto)
 * <AUTHOR>
 * @date 2023/12/29 11:23
 * @since JDK 1.8
 */
@Data
public class HkHboneRelationMessageDTO implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 香港客户号-hkCustNo
     */
    private String hkCustNo;



    /**
     * 一账通号-hboneNo
     */
    private String hboneNo;


    /**
     * 操作类型(operType) 	0-新增；1-删除
     */
    private String operType;

}