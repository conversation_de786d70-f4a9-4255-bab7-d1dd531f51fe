/**
 * Copyright (c) 2025, <PERSON><PERSON><PERSON><PERSON> HOWBUY INVESTMENT MANAGEMENT Co., Ltd.
 * All right reserved.
 * <p>
 * THIS IS UNPUBLISHED PROPRIETARY SOURCE CODE OF HOWBUY INVESTMENT
 * MANAGEMENT CO., LTD.  THE CONTENTS OF THIS FILE MAY NOT BE DISCLOSED
 * TO THIRD PARTIES, COPIED OR DUPLICATED IN ANY FORM, IN WHOLE OR IN PART,
 * WITHOUT THE PRIOR WRITTEN PERMISSION OF HOWBUY INVESTMENT MANAGEMENT
 * CO., LTD.
 */
package com.howbuy.crm.account.service.service.sensitive;

import com.howbuy.crm.account.client.request.sensitive.SensitiveWordsRequest;
import com.howbuy.crm.account.client.response.sensitive.SensitiveWordsVO;
import com.howbuy.crm.account.service.repository.sensitive.CsKeywordRepository;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.List;

/**
 * @description 资配敏感词服务实现类
 * <AUTHOR>
 * @date 2024-07-03 18:14:15
 */
@Slf4j
@Service
public class SensitiveWordsService {
    
    @Resource
    private CsKeywordRepository csKeywordRepository;
    
    /**
     * @description 查询资配敏感词
     * @param request 查询参数
     * @return 敏感词DTO
     */
    public SensitiveWordsVO querySensitiveWords(SensitiveWordsRequest request) {
        log.info("查询资配敏感词，模块：{}", request.getModule());
        
        // 1. 调用Repository获取敏感词列表
        List<String> sensitiveWords = csKeywordRepository.listKeywordNameByModule(request.getModule());
        
        // 2. 构建返回结果
        SensitiveWordsVO dto = new SensitiveWordsVO();
        dto.setSensitiveWords(sensitiveWords);
        
        log.info("查询资配敏感词完成，模块：{}，敏感词列表：{}", request.getModule(), sensitiveWords);
        return dto;
    }
} 