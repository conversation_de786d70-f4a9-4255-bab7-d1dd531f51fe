/**
 * Copyright (c) 2023, <PERSON><PERSON><PERSON><PERSON> HOWBUY INVESTMENT MANAGEMENT Co., Ltd.
 * All right reserved.
 * <p>
 * THIS IS UNPUBLISHED PROPRIETARY SOURCE CODE OF HOWBUY INVESTMENT
 * MANAGEMENT CO., LTD.  THE CONTENTS OF THIS FILE MAY NOT BE DISCLOSED
 * TO THIRD PARTIES, COPIED OR DUPLICATED IN ANY FORM, IN WHOLE OR IN PART,
 * WITHOUT THE PRIOR WRITTEN PERMISSION OF HOWBUY INVESTMENT MANAGEMENT
 * CO., LTD.
 */
package com.howbuy.crm.account.service.repository.custservice;

import com.howbuy.common.utils.DateUtil;
import com.howbuy.crm.account.client.enums.YesOrNoEnum;
import com.howbuy.crm.account.client.response.Response;
import com.howbuy.crm.account.dao.mapper.CommonMapper;
import com.howbuy.crm.account.dao.mapper.custservice.CmBookingcustMapper;
import com.howbuy.crm.account.dao.po.custservice.CmBookingcustPO;
import com.howbuy.crm.account.service.commom.constant.SequenceConstants;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import org.springframework.transaction.annotation.Propagation;
import org.springframework.transaction.annotation.Transactional;

import java.util.Date;

/**
 * @description: (客服相关 - 网站预约  repository)
 * <AUTHOR>
 * @date 2023/12/28 18:02
 * @since JDK 1.8
 */
@Component
@Slf4j
@Transactional(rollbackFor = Exception.class, propagation = Propagation.SUPPORTS)
public class BookingCustRepository {

    @Autowired
    private CmBookingcustMapper bookingCustMapper;

    @Autowired
    private CommonMapper commonMapper;


    /**
     * @description:(新增 客户 网络预约数据 )
     * @param bookingCustPo
     * @return com.howbuy.crm.account.client.response.Response<java.lang.String>
     * @author: haoran.zhang
     * @date: 2023/12/28 18:15
     * @since JDK 1.8
     */
    @Transactional(rollbackFor = Exception.class, propagation = Propagation.REQUIRED)
     public Response<String> createBookingCust(CmBookingcustPO bookingCustPo){
        Date date=new Date();
        String dateString=DateUtil.date2String(date,DateUtil.SHORT_DATE_PATTERN);

         //获取 sequece
         bookingCustPo.setId(Long.valueOf(commonMapper.getSeqValue(SequenceConstants.SEQ_CM_BOOKING_CUST)));
         bookingCustPo.setBookingdt(dateString);
         // 预约详细时间
         bookingCustPo.setBookingdetaildt(DateUtil.date2String(date,DateUtil.DEFAULT_DATESFM));
         bookingCustPo.setRecstat("0");
         //'处理状态（0：未处理 ，1：已处理）'
        bookingCustPo.setHandlestat(YesOrNoEnum.NO.getCode());
        bookingCustPo.setCredt(dateString);
        bookingCustPo.setSyncDate(date);
        bookingCustPo.setReadflag(Short.parseShort("0"));
        bookingCustMapper.insert(bookingCustPo);
         return Response.ok();
     }

}