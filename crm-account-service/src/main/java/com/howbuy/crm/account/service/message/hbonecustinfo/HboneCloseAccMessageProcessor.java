/**
 * Copyright (c) 2023, <PERSON><PERSON><PERSON><PERSON> HOWBUY INVESTMENT MANAGEMENT Co., Ltd.
 * All right reserved.
 * <p>
 * THIS IS UNPUBLISHED PROPRIETARY SOURCE CODE OF HOWBUY INVESTMENT
 * MANAGEMENT CO., LTD.  THE CONTENTS OF THIS FILE MAY NOT BE DISCLOSED
 * TO THIRD PARTIES, COPIED OR DUPLICATED IN ANY FORM, IN WHOLE OR IN PART,
 * WITHOUT THE PRIOR WRITTEN PERMISSION OF HOWBUY INVESTMENT MANAGEMENT
 * CO., LTD.
 */
package com.howbuy.crm.account.service.message.hbonecustinfo;

import com.alibaba.fastjson.JSON;
import com.howbuy.crm.account.client.enums.custinfo.FullCustSourceEnum;
import com.howbuy.crm.account.client.request.custinfo.HboneAcctRelationOptRequest;
import com.howbuy.crm.account.client.response.Response;
import com.howbuy.crm.account.dao.bo.custinfo.CmConscustForAnalyseBO;
import com.howbuy.crm.account.service.business.custinfo.HboneCustBindBusinessService;
import com.howbuy.crm.account.service.commom.constant.Constants;
import com.howbuy.crm.account.service.message.dto.HboneCloseMessageDTO;
import com.howbuy.crm.account.service.vo.custinfo.AbnormalAnalyseResultVO;
import com.howbuy.crm.account.service.vo.custinfo.HboneMessageCustInfoVO;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;

import java.util.Arrays;
import java.util.List;

/**
 * @description: ([一账通账户]-[客户信息变更消息]-[一账通销户 CLOSE_ACC] 消息处理器[topic.hbone.updateCust])
 * <AUTHOR>
 * @date 2023/12/11 17:04
 * @since JDK 1.8
 */
@Slf4j
@Service
public class HboneCloseAccMessageProcessor extends AbstractHboneMessageProcessor<HboneCloseMessageDTO>{


    /**
     * [TOPIC_UPDATE_CUST]
     */
    @Value("${topic.hbone.updateCust}")
    private String topicUpdateCust;

    /**
     * 一账通销户
     * [CLOSE_ACC]
     */
    @Value("${tag.hbone.closeAcc}")
    private String closeAccTagName;



//    {
//        "body": {
//               "closeHbone": true,
//        "disCode": "HB000A001",
//                "hboneNo": "8000575653",
//                "idNoCipher": "KIOdTcM8J5JpV0_SfsUTOWh_lX3D2_XnOCJcQ7OhIaI=01",
//                "idNoDigest": "1d57c1667a9233e6a84ebdd17b169991",
//                "idNoMask": "13018519830720****",
//                "idType": "0"
//    },
//        "head": {
//        "createTime": "20240104151344",
//                "eventCode": "520029",
//                "hboneNo": "8000575653",
//                "msgKey": "c0ae91763cc28d548c6092b68a67b1c1",
//                "tag": "CLOSE_ACC"
//    }
//    }, "messageChannel": "TOPIC_UPDATE_CUST"
//}

    @Autowired
    private HboneCustBindBusinessService hbBindingService;


    @Override
    public String getQuartMessageChannel() {
        return topicUpdateCust;
    }

    @Override
    public List<String> getSupportTagList() {
        return Arrays.asList(closeAccTagName);
    }

    @Override
    HboneMessageCustInfoVO constructByMessage(HboneCloseMessageDTO  acctMsgDto) {
        HboneMessageCustInfoVO custVo=new HboneMessageCustInfoVO();
        custVo.setHboneNo(acctMsgDto.getHboneNo());
        custVo.setDisCode(acctMsgDto.getDisCode());
        custVo.setMobileAreaCode(Constants.DEFAULT_MOBILE_AREA_CODE);
        custVo.setMobileDigest(acctMsgDto.getMobileDigest());
        custVo.setMobileMask(acctMsgDto.getMobileMask());
        custVo.setIdSignAreaCode(Constants.DEFAULT_ID_SIGN_AREA_CODE);
        custVo.setIdType(acctMsgDto.getIdType());
        custVo.setIdNoDigest(acctMsgDto.getIdNoDigest());
        custVo.setIdNoMask(acctMsgDto.getIdNoMask());
        custVo.setIdNoCipher(acctMsgDto.getIdNoCipher());
        //是否注销一账通号(closeHbone)
        custVo.setCloseHbone(acctMsgDto.getCloseHbone());
        //此处 不需要 从账户中心查询 一账通账户信息

        return custVo;
    }
    @Override
    public FullCustSourceEnum getCustSource() {
        return FullCustSourceEnum.HBONE_CLOSE;
    }

    @Override
    Response<String> processHboneMessage(AbnormalAnalyseResultVO<HboneMessageCustInfoVO> resultVo) {
        HboneMessageCustInfoVO analyseVo=resultVo.getAnalyseVo();
        String hboneNo=analyseVo.getHboneNo();

        //
//        1、判断①：判断【一账通注销】消息中的【是否注销一账通号（closeHbone）】是否为“是”
//（1）若【是否注销一账通号（closeHbone）】=是，则继续判断②
//（2）否则不处理，流程结束
        if(!Boolean.TRUE.equals(analyseVo.getCloseHbone())){
            log.info("【一账通注销】消息处理：一账通号[{}],closeHbone:{} ,无需处理!",hboneNo,analyseVo.getCloseHbone());
            return Response.ok();
        }

//        2、判断②：根据推送的【一账通号】，查询是否存在绑定的【投顾客户号】
//（1）若不存在，则不处理，流程结束
//（2）若存在，则将【一账通号】和【投顾客户号】解绑，此时不更新投顾客户信息
        CmConscustForAnalyseBO custPo=getAnalyseBoByHboneNo(analyseVo);
        if(custPo==null){
            return Response.ok();
        }
         //解绑操作：
        HboneAcctRelationOptRequest bindOptReq=new HboneAcctRelationOptRequest();
        bindOptReq.setCustNo(custPo.getConscustno());
        bindOptReq.setHboneNo(hboneNo);
        bindOptReq.setOperator(analyseVo.getCreator());
        bindOptReq.setOperateChannel(getOperateChannel());
        bindOptReq.setOperateSource(analyseVo.getAbnormalSource());
        bindOptReq.setRemark("");
        //关联 客户 vs 一账通
        Response<String> bindResp=hbBindingService.unBindHboneTxAcct(bindOptReq);
        log.info("【一账通销户】消息处理：一账通号[{}] 解除绑定 投顾客户号[{}],解除绑定结果：{}",
                hboneNo,custPo.getConscustno(), JSON.toJSONString(bindResp));
        return Response.ok();

    }

}