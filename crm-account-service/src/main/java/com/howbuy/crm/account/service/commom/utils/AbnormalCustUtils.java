/**
 * Copyright (c) 2023, <PERSON><PERSON><PERSON><PERSON> HOWBUY INVESTMENT MANAGEMENT Co., Ltd.
 * All right reserved.
 * <p>
 * THIS IS UNPUBLISHED PROPRIETARY SOURCE CODE OF HOWBUY INVESTMENT
 * MANAGEMENT CO., LTD.  THE CONTENTS OF THIS FILE MAY NOT BE DISCLOSED
 * TO THIRD PARTIES, COPIED OR DUPLICATED IN ANY FORM, IN WHOLE OR IN PART,
 * WITHOUT THE PRIOR WRITTEN PERMISSION OF HOWBUY INVESTMENT MANAGEMENT
 * CO., LTD.
 */
package com.howbuy.crm.account.service.commom.utils;

import com.google.common.collect.Lists;
import com.howbuy.crm.account.client.enums.custinfo.*;
import com.howbuy.crm.account.client.utils.IdTypeUtil;
import com.howbuy.crm.account.dao.bo.custinfo.CmConscustForAnalyseBO;
import org.apache.commons.collections.CollectionUtils;

import java.util.Arrays;
import java.util.List;
import java.util.Map;
import java.util.concurrent.ConcurrentHashMap;
import java.util.function.Function;
import java.util.function.Predicate;
import java.util.stream.Collectors;

/**
 * @description: (异常客户分析  公共工具类 )
 * <AUTHOR>
 * @date 2023/12/14 18:38
 * @since JDK 1.8
 */
public class AbnormalCustUtils {


    /**
     * @description: (根据 异常来源、异常场景类型  翻译异常描述)
     * @param abnormalSource
     * @param abnormalSceneType
     * @return
     */
    public static String  translateAbnormalDesc(String abnormalSource,String abnormalSceneType){
        return String.join(":", FullCustSourceEnum.getDescription(abnormalSource),
                AbnormaSceneTypeEnum.getDescription(abnormalSceneType));

    }




    /**
     * @description: (获取有效的【香港客户状态】列表)
     * @return
     */
    public static List<String> getValidHkCustStateList(){
        return Arrays.stream(HkAcctCustStatusEnum.values())
                .filter(s -> !s.equals(HkAcctCustStatusEnum.CANCEL))
                .map(HkAcctCustStatusEnum::getCode).collect(Collectors.toList());
    }


    /**
     * @description:(根据客户类型，确定查询范围： 个人客户，查询个人客户类型数据。[机构|产品]类型，查询[机构|产品]类型数据)
     * @param investType
     * @return java.util.List<java.lang.String>
     * @author: haoran.zhang
     * @date: 2024/1/2 19:39
     * @since JDK 1.8
     */
    public static List<String> getSearchInvestTypeList(String investType){
        ConsCustInvestTypeEnum typeEnum=ConsCustInvestTypeEnum.getEnum(investType);
        if(ConsCustInvestTypeEnum.PERSONAL==typeEnum){
            return Lists.newArrayList(typeEnum.getCode());
        }
        if(ConsCustInvestTypeEnum.INSTITUTION==typeEnum ||
              ConsCustInvestTypeEnum.PRODUCT==typeEnum){
            return Lists.newArrayList(ConsCustInvestTypeEnum.INSTITUTION.getCode(),
                    ConsCustInvestTypeEnum.PRODUCT.getCode());
        }
        //默认查询 全部客户类型
        return Arrays.stream(ConsCustInvestTypeEnum.values()).map(ConsCustInvestTypeEnum::getCode).collect(Collectors.toList());
    }


    /**
     * @description:(证件类型 转译 。背景 ：crm持有的证件类型，在账户中心不存在。)
     * @param investType
     * @return java.util.List<java.lang.String>
     * @author: haoran.zhang
     * @date: 2024/1/22 15:14
     * @since JDK 1.8
     */
    public static List<String> getSearchIdTypeList(String investType,String idType){
        ConsCustInvestTypeEnum investTypeEnumpeEnum=ConsCustInvestTypeEnum.getEnum(investType);
//        （1）当客户类型=个人，且香港账户中心的证件类型=香港身份证时，CRM在证件类型=其他/香港身份证 的客户中进行判重
//        （2）当客户类型=个人，且香港账户中心的证件类型=澳门身份证时，CRM在证件类型=其他/澳门身份证 的客户中进行判重
//        （3）当客户类型=个人，且香港账户中心的证件类型=台湾身份证时，CRM在证件类型=其他/台湾身份证 的客户中进行判重
          if(ConsCustInvestTypeEnum.PERSONAL==investTypeEnumpeEnum){
            if(IdTypeUtil.specialPersonalIdTypeList.contains(idType)){
                return Lists.newArrayList(IdTypeEnum.OTHER.getCode(),idType);
          }
//          （4）当客户类型=个人，且香港账户中心的证件类型=其他时，CRM在证件类型=其他/香港身份证/澳门身份证/台湾身份证 的客户中进行判重
            if(IdTypeEnum.OTHER.getCode().equals(idType)){
                List<String> returnList=Lists.newArrayList(IdTypeUtil.specialPersonalIdTypeList);
                returnList.add(IdTypeEnum.OTHER.getCode());
                return returnList;
            }
        }
        return Lists.newArrayList(idType);
    }


    /**
     * @description: (根据【投顾客户号】去重)
     * @param sourceList
     * @return java.util.List<com.howbuy.crm.account.dao.bo.custinfo.CmConscustForAnalyseBO>
     * @throws
     * @since JDK 1.8
     */
    public static List<CmConscustForAnalyseBO> distinctList(List<CmConscustForAnalyseBO> sourceList){
        return sourceList.stream().filter(distinctByKey(CmConscustForAnalyseBO::getConscustno)).collect(Collectors.toList());
    }

    
    /**
     * @description:(去重操作 返回去重后的列表 )
     * @param elements
     * @return java.util.List<com.howbuy.crm.account.dao.bo.custinfo.CmConscustForAnalyseBO>
     * @author: haoran.zhang
     * @date: 2023/12/29 18:58
     * @since JDK 1.8
     */
    public static List<CmConscustForAnalyseBO> dealDistinct(List<List<CmConscustForAnalyseBO>> elements){
        if(CollectionUtils.isEmpty(elements)){
            return Lists.newArrayList();
        }
        List<CmConscustForAnalyseBO> list=Lists.newArrayList();
        for(List<CmConscustForAnalyseBO>  element:elements){
            if(CollectionUtils.isEmpty(element)){
                continue;
            }
            list.addAll(element);
        }
        return distinctList(list);
    }

    /**
     * @description: (去重)
     * @param keyExtractor
     * @param <T>
     * @return java.util.function.Predicate<T>
     * @throws
     * @since JDK 1.8
     */
    private static <T> Predicate<T> distinctByKey(Function<? super T, ?> keyExtractor) {
        Map<Object, Boolean> seen = new ConcurrentHashMap<>();
        return t -> seen.putIfAbsent(keyExtractor.apply(t), Boolean.TRUE) == null;
    }

}