package com.howbuy.crm.account.service.commom.constant;

/**
 * @description:(序列号 常量类)
 * @param
 * @return
 * @since JDK 1.8
 */
public class SequenceConstants {


    /**
     * 投顾客户序列号
     */
    public static final String SEQ_CM_CONSCUST = "SEQ_CUSTNO";

    /**
     * 投顾客户信息  变动流水表 序列号
     */
    public static  final String SEQ_CUST_UPDATE_SERIALNO="SEQ_CUSTREC";


    /**
     * [香港|一账通] 账户异常客户信息 序列号
     */
    public static final String SEQ_CM_ABNORMAL_CUST ="SEQ_CM_ABNORMAL_CUST";


    /**
     * 香港交易账户 vs 投顾客户关联关系表
     */
    public static final String SEQ_CM_HK_CONSCUST="SEQ_CM_HK_CONSCUST";

    /**
     * 投顾客户关联流水表
     */
    public static final String SEQ_CM_CUST_RELATION_LOG="SEQ_CM_CUST_RELATION_LOG";


    /**
     * 投顾客户  新增修改 操作集合 表
     * 与划转相关
     */
    public static final String SEQ_CONSCUST_OPERATION="CONSCUST_OPERATION_SEQ";

    /**
     * 客服预约 bookingcust表使用
     */
    public static final String  SEQ_CM_BOOKING_CUST="SEQ_CM_BOOKING_CUST";

    /**
     * 拜访纪要表 CM_VISIT_MINUTES
     */
    public static final String SEQ_CM_VISIT_MINUTES = "SEQ_CM_VISIT_MINUTES";

    /**
     * 拜访纪要 陪访人表 SEQ_VISIT_MINUTES_ACCOMPANYING
     */
    public static final String SEQ_VISIT_MINUTES_ACCOMPANYING = "SEQ_VISIT_MINUTES_ACCOMPANYING";


}
