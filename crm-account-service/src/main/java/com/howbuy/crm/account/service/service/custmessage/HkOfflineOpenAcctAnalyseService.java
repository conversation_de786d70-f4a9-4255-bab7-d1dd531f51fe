///**
// * Copyright (c) 2023, <PERSON><PERSON><PERSON><PERSON> HOWBUY INVESTMENT MANAGEMENT Co., Ltd.
// * All right reserved.
// * <p>
// * THIS IS UNPUBLISHED PROPRIETARY SOURCE CODE OF HOWBUY INVESTMENT
// * MANAGEMENT CO., LTD.  THE CONTENTS OF THIS FILE MAY NOT BE DISCLOSED
// * TO THIRD PARTIES, COPIED OR DUPLICATED IN ANY FORM, IN WHOLE OR IN PART,
// * WITHOUT THE PRIOR WRITTEN PERMISSION OF HOWBUY INVESTMENT MANAGEMENT
// * CO., LTD.
// */
//package com.howbuy.crm.account.service.service.custmessage;
//
//import com.google.common.collect.Lists;
//import com.howbuy.common.utils.StringUtil;
//import com.howbuy.crm.account.client.enums.custinfo.AbnormaSceneTypeEnum;
//import com.howbuy.crm.account.client.enums.custinfo.FullCustSourceEnum;
//import com.howbuy.crm.account.client.response.custinfo.HboneAcctCustInfoVO;
//import com.howbuy.crm.account.dao.bo.custinfo.CmConscustForAnalyseBO;
//import com.howbuy.crm.account.dao.po.custinfo.CmHkConscustPO;
//import com.howbuy.crm.account.dao.req.custinfo.CmHkCustReqVO;
//import com.howbuy.crm.account.service.business.custinfo.CmCustProcessBusinessService;
//import com.howbuy.crm.account.service.business.custinfo.CustSearchBusinessService;
//import com.howbuy.crm.account.service.repository.custinfo.AbnormalCustRepository;
//import com.howbuy.crm.account.service.repository.custinfo.CmHkCustInfoRepository;
//import com.howbuy.crm.account.service.service.custinfo.HboneCustInfoService;
//import com.howbuy.crm.account.service.vo.custinfo.AbnormalAnalyseResultVO;
//import com.howbuy.crm.account.service.vo.custinfo.HkMessageCustInfoVO;
//import com.howbuy.crm.account.service.vo.custinfo.SearchCustKeyAttrVO;
//import lombok.extern.slf4j.Slf4j;
//import org.springframework.beans.factory.annotation.Autowired;
//import org.springframework.stereotype.Service;
//import org.springframework.util.Assert;
//
//import java.util.List;
//
///**
// * @description: ([香港账户]-[香港线下开户] 消息 分析 服务)
// * <AUTHOR>
// * @date 2023/12/14 10:03
// * @since JDK 1.8
// */
//@Service
//@Slf4j
//public class HkOfflineOpenAcctAnalyseService implements CustMessageAnalyseService<HkMessageCustInfoVO> {
//
//    @Autowired
//    private AbnormalCustRepository abnormalCustRepository;
//
//    @Autowired
//    private CmHkCustInfoRepository cmHkCustInfoRepository;
//
//    @Autowired
//    private HboneCustInfoService hboneCustInfoService;
//
//    @Autowired
//    private CustSearchBusinessService custSearchBusinessService;
//
//
//    @Autowired
//    private CmCustProcessBusinessService processBusinessService;
//
//    @Override
//    public List<FullCustSourceEnum>  getSourceList() {
//        return Lists.newArrayList(FullCustSourceEnum.HK_OFFLINE_OPEN_ACCOUNT);
//    }
//
//
//
//
//    @Override
//    public AbnormalAnalyseResultVO<HkMessageCustInfoVO> analyze(HkMessageCustInfoVO analyseVo) {
//        Assert.notNull(analyseVo.getHkTxAcctNo(),"参数不能为空");
//        String hkTxAcctNo=analyseVo.getHkTxAcctNo();
////        判断①：开户的【香港客户号A】是否已绑定【投顾客户号】
//        CmHkCustReqVO hkReqVo = new CmHkCustReqVO();
//        hkReqVo.setHkTxAcctNo(hkTxAcctNo);
//        CmHkConscustPO existHkCustInfo=cmHkCustInfoRepository.selectByReqVO(hkReqVo);
//
//        //该 【香港客户号A】已绑定【投顾客户号】
//        if (existHkCustInfo != null) {
//            // 更新投顾客户信息
//            CmConscustForAnalyseBO processedCustInfo = abnormalCustRepository.queryCustBOByCustNo(existHkCustInfo.getConscustno());
//            processBusinessService.updateCustInfoByHk(analyseVo, processedCustInfo);
//            AbnormalAnalyseResultVO<HkMessageCustInfoVO> analyseResultVO = analyseExistsCust(existHkCustInfo, analyseVo);
//            // 标记：在分析过程中，已经对投顾客户信息进行了更新
//            analyseResultVO.setUpdateCustInfoInAnalyseFlag(true);
//            return analyseResultVO;
//        }
//
//        //不存在客户， 尝试根据条件匹配 crm 客户。是否能匹配到唯一的【投顾客户】
//        SearchCustKeyAttrVO searchAttrInfo=new SearchCustKeyAttrVO();
//        searchAttrInfo.setInvstType(analyseVo.getInvestType());
//        searchAttrInfo.setCustName(analyseVo.getCustName());
//        searchAttrInfo.setIdNoDigest(analyseVo.getIdNoDigest());
//        searchAttrInfo.setIdSignAreaCode(analyseVo.getIdSignAreaCode());
//        searchAttrInfo.setIdType(analyseVo.getIdType());
//        searchAttrInfo.setMobileAreaCode(analyseVo.getMobileAreaCode());
//        searchAttrInfo.setMobileDigest(analyseVo.getMobileDigest());
//        AbnormalAnalyseResultVO<HkMessageCustInfoVO>  searchAnalyseVo=custSearchBusinessService.searchMatchedCust(analyseVo, searchAttrInfo);
//        if(!searchAnalyseVo.isAnalysePass()){
//            return searchAnalyseVo;
//        }
//        CmConscustForAnalyseBO  matchedOneCust=searchAnalyseVo.getProcessedCustInfo();
//        //校验 复杂搜索后的客户对象，是否 满足
//        if(matchedOneCust!=null){
//            //匹配上的唯一【投顾客户号A】是否已绑定【香港客户号】
////            若已绑定其他【香港客户号B】（此时不会自动换绑），则异常数据进“香港异常客户表”：
////            匹配异常的【香港客户号】落“香港异常客户主表”，写入字段：
////            a、异常来源：香港开户
////            b、异常描述：匹配到的投顾客户号已被占用
////            c、异常时间：取落入异常表的时间，格式yyyymmdd hh:mm:ss
//            if(StringUtil.isNotBlank(matchedOneCust.getHkTxAcctNo()) &&
//                            Boolean.FALSE.equals(StringUtil.isEqual(hkTxAcctNo,matchedOneCust.getHkTxAcctNo()))){
//                return AbnormalAnalyseResultVO.notNormalData(analyseVo,
//                        AbnormaSceneTypeEnum.MATCH_CUST_NO_OCCUPIED,
//                        Lists.newArrayList(matchedOneCust));
//            }
//        }
//        AbnormalAnalyseResultVO<HkMessageCustInfoVO> resultVo=AbnormalAnalyseResultVO.normalData(analyseVo);
//        //复杂搜索匹配到的客户信息
//        resultVo.setProcessedCustInfo(matchedOneCust);
//        return resultVo;
//    }
//
//    /**
//     * @description:(已关联的客户信息分析)
//     * @param existHkCustInfo
//     * @param analyseVo
//     * @return com.howbuy.crm.account.service.vo.custinfo.AbnormalAnalyseResultVO<com.howbuy.crm.account.service.vo.custinfo.HkMessageCustInfoVO>
//     * @author: haoran.zhang
//     * @date: 2024/1/5 15:39
//     * @since JDK 1.8
//     */
//    private AbnormalAnalyseResultVO<HkMessageCustInfoVO> analyseExistsCust(CmHkConscustPO existHkCustInfo,HkMessageCustInfoVO analyseVo){
//        AbnormalAnalyseResultVO<HkMessageCustInfoVO> resultVo=AbnormalAnalyseResultVO.normalData(analyseVo);
//        String custNo=existHkCustInfo.getConscustno();
//        //查询【投顾客户号】是否 一账通信息
//        HboneAcctCustInfoVO  hboneCustInfo=hboneCustInfoService.queryHboneAcctInfoByCustNo(custNo);
//        //判断 ：该一账通是否实名
//        boolean isRealName=hboneCustInfoService.isRealName(hboneCustInfo);
//        if(isRealName){
//           //验证 一账通证件信息 ！=  香港证件信息  标记为 异常数据
//           if(Boolean.FALSE.equals(StringUtil.isEqual(analyseVo.getIdNoDigest(),hboneCustInfo.getIdNoDigest()))){
////               匹配异常的【香港客户号】落“香港异常客户主表”，写入字段：
////               a、异常来源：香港开户
////               b、异常描述：香港开户证件与一账通证件不一致
////               c、异常时间：取落入异常表的时间，格式yyyymmdd hh:mm:ss
////               匹配到的【投顾客户号】落“香港异常客户待关联表”，与主表数据相对应。
//               //关联客户数据
//               List<CmConscustForAnalyseBO> relatedCustList= abnormalCustRepository.queryCustListByCustNoList(Lists.newArrayList(custNo));
//
//               return AbnormalAnalyseResultVO.notNormalData(
//                       analyseVo,
//                       AbnormaSceneTypeEnum.HK_OPEN_ACCOUNT_ID_TYPE_ID_NO_NOT_MATCH,
//                       relatedCustList);
//           }
////           else{
////                //该【香港客户号A】已绑定【投顾客户号】，且一账通实名，且证件信息一致，不做任何处理
////                //2024年1月27日 调整： 依然需要后续处理
////               resultVo.setNeedProcess(false);
////           }
//        }
//
//        CmConscustForAnalyseBO processedCust=abnormalCustRepository.queryCustBOByCustNo(custNo);
//        resultVo.setProcessedCustInfo(processedCust);
//        resultVo.setNeedProcess(true);
//        return resultVo;
//
//    }
//
//
//
//
//}