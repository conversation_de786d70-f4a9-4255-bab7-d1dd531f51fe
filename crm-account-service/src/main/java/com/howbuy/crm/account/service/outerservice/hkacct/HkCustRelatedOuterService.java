/**
 * Copyright (c) 2023, <PERSON><PERSON><PERSON><PERSON> HOWBUY INVESTMENT MANAGEMENT Co., Ltd.
 * All right reserved.
 * <p>
 * THIS IS UNPUBLISHED PROPRIETARY SOURCE CODE OF HOWBUY INVESTMENT
 * MANAGEMENT CO., LTD.  THE CONTENTS OF THIS FILE MAY NOT BE DISCLOSED
 * TO THIRD PARTIES, COPIED OR DUPLICATED IN ANY FORM, IN WHOLE OR IN PART,
 * WITHOUT THE PRIOR WRITTEN PERMISSION OF HOWBUY INVESTMENT MANAGEMENT
 * CO., LTD.
 */
package com.howbuy.crm.account.service.outerservice.hkacct;

import com.google.common.collect.Lists;
import com.howbuy.crm.account.client.request.custinfo.HkAcctOptDealRequest;
import com.howbuy.crm.account.client.request.custinfo.HkPiggyAgreementDealRequest;
import com.howbuy.crm.account.client.response.PageVO;
import com.howbuy.crm.account.client.response.custinfo.*;
import com.howbuy.hkacconline.facade.query.queryhkacctdeal.*;
import com.howbuy.hkacconline.facade.query.queryhkacctdeal.dto.AcctDealDTO;
import com.howbuy.hkacconline.facade.query.queryhkacctdeal.dto.AcctDealOperateLogDTO;
import com.howbuy.hkacconline.facade.query.queryhkacctdeal.dto.HkDealDTO;
import com.howbuy.hkacconline.facade.query.queryhkcustpiggyagreement.QueryHkCustPiggyAgreementFacade;
import com.howbuy.hkacconline.facade.query.queryhkcustpiggyagreement.QueryHkCustPiggyAgreementRequest;
import com.howbuy.hkacconline.facade.query.queryhkcustpiggyagreement.QueryHkCustPiggyAgreementResponse;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.dubbo.config.annotation.DubboReference;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * @description: (香港账户中心 [交易相关属性] outer service)
 * <AUTHOR>
 * @date 2023/12/8 20:15
 * @since JDK 1.8
 */
@Slf4j
@Service
public class HkCustRelatedOuterService {


    /**
     * 海外储蓄罐协议签署信息 接口
     */
    @DubboReference(registry = "hk-acc-online-service", check = false)
    private QueryHkCustPiggyAgreementFacade hkCustPiggyAgreementFacade;

    /**
     * 海外储蓄罐协议签署 订单流水 接口
     */
    @DubboReference(registry = "hk-acc-online-service", check = false)
    private QueryHkPiggyAgreementDealFacade queryHkPiggyAgreementDealFacade;

    /**
     * 香港账户中心 开户订单流水 接口
     */
    @DubboReference(registry = "hk-acc-online-service", check = false)
    private QueryHkAcctDealOperateLogFacade queryHkAcctDealOperateLogFacade;



    /**
     * @description:(根据 香港交易账号 查询 海外储蓄罐协议签署信息)
     * @param hkTxAcctNo	
     * @return void
     * @author: haoran.zhang
     * @date: 2024/7/24 13:34
     * @since JDK 1.8
     */
    public HkCustPiggyAgreementVO getCustPiggyAgreement(String hkTxAcctNo){
        QueryHkCustPiggyAgreementRequest request=new QueryHkCustPiggyAgreementRequest();
        request.setHkCustNo(hkTxAcctNo);
        QueryHkCustPiggyAgreementResponse resp=hkCustPiggyAgreementFacade.execute(request);
        if(resp.isSuccess()){
            HkCustPiggyAgreementVO vo=new HkCustPiggyAgreementVO();
            vo.setPiggyFundCodeList(resp.getPiggyFundCodeList());
            vo.setAgreementState(resp.getAgreementState());
            vo.setAgreementSignType(resp.getAgreementSignType());
            vo.setAgreementSignDt(resp.getAgreementSignDt());
            vo.setAgreementSignExpiredDt(resp.getAgreementSignExpiredDt());
            vo.setAgreementCancelType(resp.getAgreementCancelType());
            vo.setAgreementCancelDt(resp.getAgreementCancelDt());
            return vo;
        }
        return null;
    }



    /**
     * @description:(客户海外储蓄罐协议流水信息 查询)
     * @param queryReq
     * @return java.util.List<com.howbuy.crm.account.client.response.custinfo.HkPiggyAgreementDealVO>
     * @author: haoran.zhang
     * @date: 2024/8/12 15:41
     * @since JDK 1.8
     */
    public PageVO<HkPiggyAgreementDealVO> getPiggyAgreementDeal(HkPiggyAgreementDealRequest queryReq){
        int pageSize=queryReq.getRows();
        PageVO<HkPiggyAgreementDealVO> returnPage=new PageVO<>();
        returnPage.setSize(pageSize);
        QueryHkPiggyAgreementDealRequest request= new QueryHkPiggyAgreementDealRequest();

        request.setHkCustNo(queryReq.getHkTxAcctNo());
        request.setBusiCodes(queryReq.getBusiCodes());
        request.setTradeChannels(queryReq.getTradeChannels());
        request.setTxChkFlags(queryReq.getTxChkFlags());
        request.setStartUpdateDt(queryReq.getStartUpdateDt());
        request.setEndUpdateDt(queryReq.getEndUpdateDt());

        //分页属性

        request.setPageNo(queryReq.getPage());
        request.setPageSize(pageSize);

        QueryHkPiggyAgreementDealResponse resp=queryHkPiggyAgreementDealFacade.execute(request);
        if(!resp.isSuccess() || CollectionUtils.isEmpty(resp.getDealList())){
            return returnPage;
        }
        returnPage.setTotal(resp.getTotalCount());
        returnPage.setPage((int) resp.getPageNo());
        List<HkDealDTO<QueryHkPiggyAgreementDealResponse.HkPiggyAgreementDeal>> dealList=resp.getDealList();
        dealList.forEach(deal->{
            if(deal.getAcctDealDTO()!=null){
                HkPiggyAgreementDealVO dealVo=new HkPiggyAgreementDealVO();
                //赋值 父类
                HkAcctBaseDealVO baseVo=transferBaseAcct(deal.getAcctDealDTO());
                BeanUtils.copyProperties(baseVo, dealVo);

                //子类属性赋值
                QueryHkPiggyAgreementDealResponse.HkPiggyAgreementDeal subVo=deal.getParamDTO();
                dealVo.setPiggyFundCodeList(subVo.getPiggyFundCodeList());
                dealVo.setAgreementSignType(subVo.getAgreementSignType());
                dealVo.setAgreementSignDt(subVo.getAgreementSignDt());
                dealVo.setAgreementCancelType(subVo.getAgreementCancelType());
                dealVo.setAgreementCancelDt(subVo.getAgreementCancelDt());

                if(CollectionUtils.isNotEmpty(subVo.getFileDTOList())){
                  List<HkAgreementFileVO> fileVoList= Lists.newArrayList();
                    subVo.getFileDTOList().forEach(fileDTO->{;
                        HkAgreementFileVO fileVO=new HkAgreementFileVO();
                        BeanUtils.copyProperties(fileDTO, fileVO);
                        fileVoList.add(fileVO);
                    });
                 dealVo.setFilePathList(fileVoList);
                }
//                dealVo.setOutOrderId(subVo.getOutOrderId());
                returnPage.getRows().add(dealVo);
            }

        });

          return returnPage;

    }


    /**
     *海外账户中心，所有基类 {@link com.howbuy.hkacconline.facade.query.queryhkacctdeal.dto.AcctDealDTO}
     * 转换为
     * {@link com.howbuy.crm.account.client.response.custinfo.HkAcctBaseDealVO}
     * @param dealDTO
     * @return
     */
    private HkAcctBaseDealVO transferBaseAcct(AcctDealDTO dealDTO){
        HkAcctBaseDealVO hkAcctBaseDealVO = new HkAcctBaseDealVO();
        hkAcctBaseDealVO.setDealNo(dealDTO.getDealNo());
        hkAcctBaseDealVO.setHkCustNo(dealDTO.getHkCustNo());
        hkAcctBaseDealVO.setTxCode(dealDTO.getTxCode());
        hkAcctBaseDealVO.setBusiCode(dealDTO.getBusiCode());
        hkAcctBaseDealVO.setChannel(dealDTO.getChannel());
        hkAcctBaseDealVO.setCustName(dealDTO.getCustName());
        hkAcctBaseDealVO.setCustChineseName(dealDTO.getCustChineseName());
        hkAcctBaseDealVO.setCustEnName(dealDTO.getCustEnName());
        hkAcctBaseDealVO.setInvstType(dealDTO.getInvstType());
        hkAcctBaseDealVO.setIdType(dealDTO.getIdType());
        hkAcctBaseDealVO.setIdNoDigest(dealDTO.getIdNoDigest());
        hkAcctBaseDealVO.setIdNoMask(dealDTO.getIdNoMask());
        hkAcctBaseDealVO.setIdNoCipher(dealDTO.getIdNoCipher());
        hkAcctBaseDealVO.setAppDt(dealDTO.getAppDt());
        hkAcctBaseDealVO.setAppTm(dealDTO.getAppTm());
        hkAcctBaseDealVO.setBankAcctDigest(dealDTO.getBankAcctDigest());
        hkAcctBaseDealVO.setBankAcctMask(dealDTO.getBankAcctMask());
        hkAcctBaseDealVO.setBankAcctCipher(dealDTO.getBankAcctCipher());
        hkAcctBaseDealVO.setHkCpAcctNo(dealDTO.getHkCpAcctNo());
        hkAcctBaseDealVO.setBankId(dealDTO.getBankId());
        hkAcctBaseDealVO.setTxChkFlag(dealDTO.getTxChkFlag());
        hkAcctBaseDealVO.setChecker(dealDTO.getChecker());
        hkAcctBaseDealVO.setCreator(dealDTO.getCreator());
        hkAcctBaseDealVO.setTxIp(dealDTO.getTxIp());
        hkAcctBaseDealVO.setStimestamp(dealDTO.getStimestamp());
        hkAcctBaseDealVO.setUpdatedStimestamp(dealDTO.getUpdatedStimestamp());
        hkAcctBaseDealVO.setFirstChecker(dealDTO.getFirstChecker());
        hkAcctBaseDealVO.setFirstCheckDt(dealDTO.getFirstCheckDt());
        hkAcctBaseDealVO.setCheckDt(dealDTO.getCheckDt());
        hkAcctBaseDealVO.setEmailDigest(dealDTO.getEmailDigest());
        hkAcctBaseDealVO.setEmailMask(dealDTO.getEmailMask());
        hkAcctBaseDealVO.setEmailCipher(dealDTO.getEmailCipher());
        hkAcctBaseDealVO.setMobileDigest(dealDTO.getMobileDigest());
        hkAcctBaseDealVO.setMobileMask(dealDTO.getMobileMask());
        hkAcctBaseDealVO.setMobileCipher(dealDTO.getMobileCipher());
        hkAcctBaseDealVO.setDepositSerialNo(dealDTO.getDepositSerialNo());
        hkAcctBaseDealVO.setCheckReverseReason(dealDTO.getCheckReverseReason());
        return hkAcctBaseDealVO;
    }




    /**
     * @description:(香港账户中心开户订单流水查询接口)
     * @param queryReq
     * @return java.util.List<com.howbuy.crm.account.client.response.custinfo.HkPiggyAgreementDealVO>
     * @author: haoran.zhang
     * @date: 2024/8/12 15:41
     * @since JDK 1.8
     */
    public PageVO<HkAcctOptDealVO> getHkAcctOptDeal(HkAcctOptDealRequest queryReq){
        int pageSize=queryReq.getRows();
        PageVO<HkAcctOptDealVO> returnPage=new PageVO<>();
        returnPage.setSize(pageSize);
        QueryHkAcctDealOperateLogRequest request= new QueryHkAcctDealOperateLogRequest();
        request.setHkCustNo(queryReq.getHkTxAcctNo());
        request.setStartOperateDt(queryReq.getStartOperateDt());
        request.setEndOperateDt(queryReq.getEndOperateDt());
        request.setBusiCodeList(queryReq.getBusiCodeList());
        request.setOperateCode(queryReq.getOperateCode());
        //分页属性
        request.setPageNo(queryReq.getPage());
        request.setPageSize(pageSize);

        QueryHkAcctDealOperateLogResponse resp=queryHkAcctDealOperateLogFacade.execute(request);
        if(!resp.isSuccess() || CollectionUtils.isEmpty(resp.getOperateLogList())){
            return returnPage;
        }
        returnPage.setTotal(resp.getTotalCount());
        returnPage.setPage((int) resp.getPageNo());
        List<AcctDealOperateLogDTO> dealList=resp.getOperateLogList();
        dealList.forEach(deal->{
            HkAcctOptDealVO dealVo=new HkAcctOptDealVO();
            dealVo.setDealNo(deal.getDealNo());
            dealVo.setBusiCode(deal.getBusiCode());
            dealVo.setOperateCode(deal.getOperateCode());
            dealVo.setOperator(deal.getOperator());
            dealVo.setHkCustNo(deal.getHkCustNo());
            dealVo.setStimestamp(deal.getStimestamp());
            dealVo.setRejectContent(deal.getRejectContent());
            dealVo.setCurCustState(deal.getCurCustState());
            dealVo.setCurAppDt(deal.getCurAppDt());
            dealVo.setCurAppTm(deal.getCurAppTm());
            returnPage.getRows().add(dealVo);
        });
        return returnPage;
    }

}