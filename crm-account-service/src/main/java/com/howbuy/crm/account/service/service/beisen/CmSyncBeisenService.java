/**
 * Copyright (c) 2024, <PERSON><PERSON><PERSON><PERSON> HOWBUY INVESTMENT MANAGEMENT Co., Ltd.
 * All right reserved.
 * <p>
 * THIS IS UNPUBLISHED PROPRIETARY SOURCE CODE OF HOWBUY INVESTMENT
 * MANAGEMENT CO., LTD.  THE CONTENTS OF THIS FILE MAY NOT BE DISCLOSED
 * TO THIRD PARTIES, COPIED OR DUPLICATED IN ANY FORM, IN WHOLE OR IN PART,
 * WITHOUT THE PRIOR WRITTEN PERMISSION OF HOWBUY INVESTMENT MANAGEMENT
 * CO., LTD.
 */
package com.howbuy.crm.account.service.service.beisen;


import com.howbuy.crm.account.client.request.beisen.CmSyncBeisenRequest;
import com.howbuy.crm.account.dao.po.consultantexp.CmConsultantExpPO;
import com.howbuy.crm.account.service.business.beisen.CmSyncBeisenServiceBusiness;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import java.util.*;
import java.util.stream.Collectors;


/**
 * @description: (请在此添加描述)
 * <AUTHOR>
 * @date 2024/11/14 17:51
 * @since JDK 1.8
 */
@Slf4j
@Service
public class CmSyncBeisenService {
    @Autowired
    private CmSyncBeisenServiceBusiness cmSyncBeisenServiceBusiness;


    /**
     * @description:(通过userno同步北森数据)
     * @param request
     * @return java.util.List<java.lang.String> 返回异常userno集合
     * @author: shijie.wang
     * @date: 2024/11/15 9:57
     * @since JDK 1.8
     */
    public List<String> syncCmConsultantExpToCmBeisenByUserNo(CmSyncBeisenRequest request) {
        String userNo = request.getUserNo();
        if(StringUtils.isEmpty(userNo)){
            return Collections.emptyList();
        }
        return syncCmConsultantExpToCmBeisenByUserNoList(Collections.singletonList(userNo));
    }

    /**
     * @description:(通过userno集合同步北森数据)
     * @param requestList
     * @return java.util.List<java.lang.String>
     * @author: shijie.wang
     * @date: 2024/11/15 10:10
     * @since JDK 1.8
     */
    public List<String> syncCmConsultantExpToCmBeisenByUserNos(List<CmSyncBeisenRequest> requestList) {
        if(CollectionUtils.isEmpty(requestList)){
            return Collections.emptyList();
        }
        return syncCmConsultantExpToCmBeisenByUserNoList(requestList.stream().
                map(CmSyncBeisenRequest::getUserNo).collect(Collectors.toList()));
    }

    /**
     * @description:(通过时间段同步北森数据)
     * @param startDate
     * @param endDate
     * @return void
     * @author: shijie.wang
     * @date: 2024/11/6 17:33
     * @since JDK 1.8
     */
    public List<String> syncCmConsultantExpToCmBeisenByDateTime(String startDate, String endDate) {
        return cmSyncBeisenServiceBusiness.syncCmConsultantExpToCmBeisenByDateTime(startDate, endDate);
    }

    /**
     * @description:(执行调度任务的北森同步花名册数据)
     * @param
     * @return void
     * @author: shijie.wang
     * @date: 2024/11/14 17:20
     * @since JDK 1.8
     */
    public List<String> execSyncBeisenToCrmByJob(){
        log.info("execSyncBeisenToCrmByJob| 执行调度任务的北森同步花名册数据 start!");
        List<String> errorUserIdList = cmSyncBeisenServiceBusiness.execSyncBeisenToCrmByJob();
        log.info("execSyncBeisenToCrmByJob| 执行调度任务的北森同步花名册数据 end!");
        return errorUserIdList;
    }

    /**
     * @description:(请在此添加描述)
     * @param userNoList
     * @return java.util.List<java.lang.String>
     * @author: shijie.wang
     * @date: 2024/11/15 10:09
     * @since JDK 1.8
     */
    private List<String> syncCmConsultantExpToCmBeisenByUserNoList(List<String> userNoList){
        List<CmConsultantExpPO> expList = cmSyncBeisenServiceBusiness.listCmConsultantExpByUserNos(userNoList);
        if(CollectionUtils.isEmpty(expList)){
            return Collections.emptyList();
        }
        return cmSyncBeisenServiceBusiness.syncCmConsultantExpToCmBeisenByUserNos(expList);
    }

}