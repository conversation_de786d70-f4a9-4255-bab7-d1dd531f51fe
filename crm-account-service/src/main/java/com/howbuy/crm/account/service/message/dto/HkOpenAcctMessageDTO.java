/**
 * Copyright (c) 2023, <PERSON><PERSON><PERSON><PERSON> HOWBUY INVESTMENT MANAGEMENT Co., Ltd.
 * All right reserved.
 * <p>
 * THIS IS UNPUBLISHED PROPRIETARY SOURCE CODE OF HOWBUY INVESTMENT
 * MANAGEMENT CO., LTD.  THE CONTENTS OF THIS FILE MAY NOT BE DISCLOSED
 * TO THIRD PARTIES, COPIED OR DUPLICATED IN ANY FORM, IN WHOLE OR IN PART,
 * WITHOUT THE PRIOR WRITTEN PERMISSION OF HOWBUY INVESTMENT MANAGEMENT
 * CO., LTD.
 */
package com.howbuy.crm.account.service.message.dto;

import lombok.Data;

import java.io.Serializable;

/**
 * @description: (账户中心 香港消息 [TOPIC_HK_OPEN_ACCT]-[客户开户消息]公共解析 dto)
 * <AUTHOR>
 * @date 2023/12/29 11:23
 * @since JDK 1.8
 */
@Data
public class HkOpenAcctMessageDTO implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 香港客户号-hkCustNo
     */
    private String hkCustNo;

    /**
     * 手机区号-mobileAreaCode
     */
    private String mobileAreaCode;

    /**
     * 手机号码摘要-mobileDigest
     */
    private String mobileDigest;


    /**
     * 手机号码掩码-mobileMask
     */
    private String mobileMask;

    /**
     * 手机号码密文-mobileCipher
     */
    private String mobileCipher;

    /**
     * 证件所属国家/地区代码-idSignAreaCode
     */
    private String idSignAreaCode;

    /**
     * 证件类型-idType
     */
    private String idType;

    /**
     * 证件号码摘要-idNoDigest
     */
    private String idNoDigest;


      /**
         * 证件号码掩码-idNoMask
         */
    private String idNoMask;

    /**
     * 证件号码密文-idNoCipher
     */
    private String idNoCipher;

    /**
     * 一账通号-hboneNo
     */
    private String hboneNo;


    /**
     * 开户网点代码 openOutletCode
     */
    private String openOutletCode;

    // 验证码注册 （子标签tag：VERIFY_CODE_REGISTER）

    /**
     * 邮箱摘要(emailDigest)	邮箱注册非空
     */
    private String emailDigest;

    /**
     * 邮箱掩码(emailMask)	邮箱注册非空
     */
    private String emailMask;

    /**
     * 邮箱密文(emailCipher)
     */
    private String emailCipher;

}