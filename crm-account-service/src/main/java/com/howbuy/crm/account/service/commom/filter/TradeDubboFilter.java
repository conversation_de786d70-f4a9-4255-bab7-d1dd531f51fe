/**
 * Copyright (c) 2023, <PERSON><PERSON><PERSON><PERSON> HOWBUY INVESTMENT MANAGEMENT Co., Ltd.
 * All right reserved.
 * <p>
 * THIS IS UNPUBLISHED PROPRIETARY SOURCE CODE OF HOWBUY INVESTMENT
 * MANAGEMENT CO., LTD.  THE CONTENTS OF THIS FILE MAY NOT BE DISCLOSED
 * TO THIRD PARTIES, COPIED OR DUPLICATED IN ANY FORM, IN WHOLE OR IN PART,
 * WITHOUT THE PRIOR WRITTEN PERMISSION OF HOWBUY INVESTMENT MANAGEMENT
 * CO., LTD.
 */
package com.howbuy.crm.account.service.commom.filter;


import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.serializer.SerializerFeature;
import com.howbuy.common.utils.DateUtil;
import com.howbuy.crm.account.client.enums.DisCodeEnum;
import com.howbuy.crm.account.client.enums.ExceptionCodeEnum;
import com.howbuy.crm.account.client.enums.TxChannelEnum;
import com.howbuy.crm.account.service.commom.constant.Constants;
import com.howbuy.crm.account.service.commom.filter.trace.TradeParamLocalUtils;
import com.howbuy.crm.account.service.commom.utils.LoggerUtils;
import com.howbuy.crm.account.service.commom.utils.MainLogUtils;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.apache.dubbo.common.constants.CommonConstants;
import org.apache.dubbo.common.extension.Activate;
import org.apache.dubbo.rpc.*;

import java.util.*;
import java.util.function.Function;

/**
 * <AUTHOR>
 * @description: dubbo过滤器
 * @date 2023/6/6 19:47
 * @since JDK 1.8
 */
@Slf4j
@Activate(group = CommonConstants.CONSUMER)
public class TradeDubboFilter implements Filter {

    private static final SerializerFeature[] SERIALIZER = {SerializerFeature.WriteClassName, SerializerFeature.WriteMapNullValue,
            SerializerFeature.WriteNullStringAsEmpty};
    /**
     * 返回结果最大list长度
     */
    private static final int MAX_RESULT_LIST_SIZE = 100;
    /**
     * 接口日志告警毫秒数
     */
    private static final int PRINT_LOG_COST = 200;

    private static final Map<Class<?>, Function<Object, String>> RETURN_CODE_EXTRACTORS = new HashMap<>();
    static {
        RETURN_CODE_EXTRACTORS.put(com.howbuy.common.facade.BaseResponse.class, obj -> ((com.howbuy.common.facade.BaseResponse) obj).getReturnCode());
        RETURN_CODE_EXTRACTORS.put(com.howbuy.cc.center.base.BaseResponse.class, obj -> ((com.howbuy.cc.center.base.BaseResponse) obj).getReturnCode());
        RETURN_CODE_EXTRACTORS.put(com.howbuy.crm.account.client.response.Response.class, obj -> ((com.howbuy.crm.account.client.response.Response) obj).getCode());
    }

    @Override
    public Result invoke(Invoker<?> invoker, Invocation invocation) throws RpcException {
        if (Objects.nonNull(invocation.getArguments()) && invocation.getArguments().length > 0) {
            Object obj = invocation.getArguments()[0];
            if (Objects.nonNull(obj) && obj instanceof com.howbuy.common.facade.BaseRequest) {
                // TP后台
                setTpCommonParams((com.howbuy.common.facade.BaseRequest) obj);
            }
        }
        // dubbo接口请求日志
        String interfaceName = invoker.getInterface().getSimpleName();
        String methodName = invocation.getMethodName();

        // trace
//        RequestChainTrace.deliverTraces();

        // 关联RPC请求返回
        // 同一查询多次调用外部相同接口
        List<Object> args = new ArrayList<Object>();
        if (invocation.getArguments() != null) {
            for (Object obj : invocation.getArguments()) {
                if (obj == null) {
                    continue;
                }
                args.add(obj);
            }
        }
        log.info("DUBBO request:[txInterface:{}.{}, address:{}]{}",
                interfaceName, methodName, RpcContext.getContext().getRemoteAddressString(), JSON.toJSONString(args, SERIALIZER));

        long start = System.currentTimeMillis();
        Result invoke = invoker.invoke(invocation);
        long cost = System.currentTimeMillis() - start;
        Object obj = invoke.getValue();
        try {
            if (obj != null) {
                Object objV = invoke.getValue();
                // 大对象日志优化，对象中List长度大于100的不打印日志
                if (Objects.nonNull(objV) && objV instanceof List && ((List) objV).size() > MAX_RESULT_LIST_SIZE) {
                    log.info("DUBBO response:[txInterface:{}.{}, cost:{}]{}",
                            interfaceName, methodName, cost, ((List) objV).size());
                } else {
                    log.info("DUBBO response:[txInterface:{}.{}, cost:{}]{}",
                            interfaceName, methodName, cost, JSON.toJSONString(invoke.getValue(), SERIALIZER));
                }
            }
        } catch (Exception e) {
            log.error("", e);
        }
        MainLogUtils.dubboCallOut(LoggerUtils.getReqId(), LoggerUtils.getRanNo(), MainLogUtils.getInterfaceName(interfaceName, methodName), getReturnCode(obj), cost);

        // 打印接口超过最大耗时告警日志
        //暂未实现
        if (cost >= PRINT_LOG_COST) {
            interfaceName = invoker.getInterface().getName() + "." + methodName;
//            log.error(ExceptionMarker.getMarker(ExceptionMarker.COMMON_CALL_OUTSERVICE_ERROR_KEY + ":" + interfaceName), "cost:{}", cost);
        }

        return invoke;
    }

    /**
     * @param request
     * @return void
     * @description: 设置后台公共参数
     * @author: hongdong.xie
     * @date: 2023/6/7 11:04
     * @since JDK 1.8
     */
    private void setTpCommonParams(com.howbuy.common.facade.BaseRequest request) {
        String disCode = StringUtils.isEmpty(TradeParamLocalUtils.getDisCode()) ? DisCodeEnum.HOWBUY.getCode() : TradeParamLocalUtils.getDisCode();
        String outletCode = StringUtils.isEmpty(TradeParamLocalUtils.getOutletCode()) ? Constants.DEFAULT_OUTLET_CODE : TradeParamLocalUtils.getOutletCode();
        String txChannel = StringUtils.isEmpty(TradeParamLocalUtils.getTxChannel()) ? TxChannelEnum.WAP.getCode() : TradeParamLocalUtils.getTxChannel();
        String appDt = StringUtils.isEmpty(TradeParamLocalUtils.getAppDt()) ? DateUtil.date2String(new Date(), DateUtil.SHORT_DATEPATTERN) : TradeParamLocalUtils.getAppDt();
        String appTm = StringUtils.isEmpty(TradeParamLocalUtils.getAppTm()) ? DateUtil.date2String(new Date(), DateUtil.StR_PATTERN_HHMMSS) : TradeParamLocalUtils.getAppTm();
        String operId = StringUtils.isEmpty(TradeParamLocalUtils.getCustIp()) ? Constants.DEFAULT_CUST_IP : TradeParamLocalUtils.getCustIp();

        // 后台
        request.setDisCode(StringUtils.isEmpty(request.getDisCode()) ? disCode : request.getDisCode());
        request.setOutletCode(StringUtils.isEmpty(request.getOutletCode()) ? outletCode : request.getOutletCode());
        request.setTradeChannel(StringUtils.isEmpty(request.getTradeChannel()) ? txChannel : request.getTradeChannel());
        request.setOperIp(StringUtils.isEmpty(request.getOperIp()) ? operId : request.getOperIp());
        request.setAppDt(StringUtils.isEmpty(request.getAppDt()) ? appDt : request.getAppDt());
        request.setAppTm(StringUtils.isEmpty(request.getAppTm()) ? appTm : request.getAppTm());

    }

    /**
     * 获取返回码
     * @param obj
     * @return
     */
    private String getReturnCode(Object obj) {
        if (obj == null) {
            return ExceptionCodeEnum.SUCCESS.getCode();
        }

        try {
            for (Map.Entry<Class<?>, Function<Object, String>> entry : RETURN_CODE_EXTRACTORS.entrySet()) {
                if (entry.getKey().isAssignableFrom(obj.getClass())) {
                    return entry.getValue().apply(obj);
                }
            }
        } catch (Exception e) {
            log.error("Error extracting return code", e);
        }

        return ExceptionCodeEnum.SUCCESS.getCode();
    }
}