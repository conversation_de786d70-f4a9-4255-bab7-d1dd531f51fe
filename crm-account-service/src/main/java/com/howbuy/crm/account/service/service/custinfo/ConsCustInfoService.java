/**
 * Copyright (c) 2023, <PERSON><PERSON><PERSON><PERSON> HOWBUY INVESTMENT MANAGEMENT Co., Ltd.
 * All right reserved.
 * <p>
 * THIS IS UNPUBLISHED PROPRIETARY SOURCE CODE OF HOWBUY INVESTMENT
 * MANAGEMENT CO., LTD.  THE CONTENTS OF THIS FILE MAY NOT BE DISCLOSED
 * TO THIRD PARTIES, COPIED OR DUPLICATED IN ANY FORM, IN WHOLE OR IN PART,
 * WITHOUT THE PRIOR WRITTEN PERMISSION OF HOWBUY INVESTMENT MANAGEMENT
 * CO., LTD.
 */
package com.howbuy.crm.account.service.service.custinfo;

import com.github.pagehelper.Page;
import com.google.common.collect.Lists;
import com.howbuy.crm.account.client.enums.custinfo.CustOperateChannelEnum;
import com.howbuy.crm.account.client.enums.custinfo.FullCustSourceEnum;
import com.howbuy.crm.account.client.request.custinfo.BatchQueryCustSimpleRequest;
import com.howbuy.crm.account.client.request.custinfo.CreateConsCustRequest;
import com.howbuy.crm.account.client.request.custinfo.PageQueryCustSimpleRequest;
import com.howbuy.crm.account.client.request.custinfo.UpdateConsCustRequest;
import com.howbuy.crm.account.client.response.PageVO;
import com.howbuy.crm.account.client.response.Response;
import com.howbuy.crm.account.client.response.consultantinfo.CmCustHisConsultant;
import com.howbuy.crm.account.client.response.consultantinfo.CmCustHisConsultantVO;
import com.howbuy.crm.account.client.response.consultantinfo.CmCustSimpleListVO;
import com.howbuy.crm.account.client.response.custinfo.*;
import com.howbuy.crm.account.dao.bo.custinfo.CmConsCustSimpleBO;
import com.howbuy.crm.account.dao.bo.custinfo.CmCustHisConsultantBO;
import com.howbuy.crm.account.dao.req.custinfo.CustSimpleSearchVO;
import com.howbuy.crm.account.dao.req.custinfo.PageCustSimpleReqVO;
import com.howbuy.crm.account.service.business.custinfo.CmCustBusinessService;
import com.howbuy.crm.account.service.business.custinfo.CmCustProcessBusinessService;
import com.howbuy.crm.account.service.commom.constant.Constants;
import com.howbuy.crm.account.service.repository.custinfo.ConsCustInfoRepository;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @description: (crm客户信息service)
 * @date 2023/12/13 16:41
 * @since JDK 1.8
 */
@Service
@Slf4j
public class ConsCustInfoService {


    @Autowired
    private ConsCustInfoRepository consCustInfoRepository;

    @Autowired
    private CmCustBusinessService cmCustBusinessService;

    @Autowired
    private CmCustProcessBusinessService custProcessBusinessService;

    /**
     * @param custNo
     * @return com.howbuy.crm.account.client.response.custinfo.CmConsCustVO
     * @description:(根据 投顾客户号  查询投顾客户信息)
     * @author: haoran.zhang
     * @date: 2023/12/13 16:53
     * @since JDK 1.8
     */
    public CmConsCustVO queryCustInfoByCustNo(String custNo) {
        return consCustInfoRepository.queryCustInfoByCustNo(custNo);
    }

    /**
     * @param hboneNo 一账通号
     * @return com.howbuy.crm.account.client.response.custinfo.CmConsCustVO 投顾客户信息
     * @description: (根据 hboneNo 查询投顾客户信息)
     * @author: haoran.zhang
     * @date: 2023/12/13 16:53
     * @since JDK 1.8
     */
    public CmConsCustVO queryCustInfoByHboneNo(String hboneNo) {
        return consCustInfoRepository.queryCustInfoByHboneNo(hboneNo);
    }

    /**
     * @param custNo
     * @return com.howbuy.crm.account.client.response.custinfo.CmConsCustWithCipherVO
     * @description:(根据客户号查询 客户信息 包含密文信息)
     * @author: haoran.zhang
     * @date: 2023/12/13 17:05
     * @since JDK 1.8
     */
    public CmConsCustWithCipherVO queryCustWithCipherByCustNo(String custNo) {
        return consCustInfoRepository.queryCustWithCipherByCustNo(custNo);
    }


    /**
     * @param custNo
     * @return com.howbuy.crm.account.client.response.custinfo.CmConsCustVO
     * @description:(简单信息，包含 : 客户基本信息、hboneNo、香港客户信息、投顾code)
     * @author: haoran.zhang
     * @date: 2023/12/13 16:53
     * @since JDK 1.8
     */
    public CmConsCustSimpleVO queryCustSimpleInfo(String custNo) {
        CmConsCustSimpleBO custBo = consCustInfoRepository.queryCustSimpleInfo(custNo);
        if (custBo == null) {
            return null;
        }
        CmConsCustSimpleVO simpleVo = new CmConsCustSimpleVO();
        BeanUtils.copyProperties(custBo, simpleVo);
        return simpleVo;
    }

    /**
     * @param createCustReq 新增客户信息请求类
     * @return com.howbuy.crm.account.client.response.Response<com.howbuy.crm.account.client.response.custinfo.CreateConsCustRespVO> 新增客户信息响应类
     * @description: 创建投顾客户
     * @author: jin.wang03
     * @date: 2023/12/27 10:51
     * @since JDK 1.8
     */
    public Response<CreateConsCustRespVO> createCustInfo(CreateConsCustRequest createCustReq) {
        Response<CreateConsCustRespVO> createResp = cmCustBusinessService.insertCust(createCustReq);
        if (createResp.isSuccess()) {
            custProcessBusinessService.analyseHkBindAfterUpdate(createResp.getData().getCustNo(),
                    createCustReq.getOperator(),
                    CustOperateChannelEnum.MENU,
                    FullCustSourceEnum.PAGE_MENU_OPERATE);
        }
        return createResp;
    }

    /**
     * @param updateCustReq 更新投顾客户请求类
     * @return com.howbuy.crm.account.client.response.Response<com.howbuy.crm.account.client.response.custinfo.UpdateConsCustRespVO> 更新投顾客户响应类
     * @description: 更新投顾客户
     * @author: jin.wang03
     * @date: 2023/12/27 17:26
     * @since JDK 1.8
     */
    public Response<UpdateConsCustRespVO> updateCustInfo(UpdateConsCustRequest updateCustReq) {
        //如果证件有限期是空字符串，设为null，解决产线有效期0和2都是非长期，页面选项匹配不到导致传空字符串，这种情况不修改库
        if (StringUtils.isBlank(updateCustReq.getValidity())) {
            updateCustReq.setValidity(null);
        }
        // 字段是否允许修改逻辑
        Response<UpdateConsCustRespVO> response = cmCustBusinessService.checkCustUpdateField(updateCustReq);
        if (!response.isSuccess()) {
            return response;
        }
        Response<UpdateConsCustRespVO> updateResp = cmCustBusinessService.updateCust(updateCustReq);
        if (updateResp.isSuccess()) {
            custProcessBusinessService.analyseHkBindAfterUpdate(updateCustReq.getCustNo(),
                    updateCustReq.getOperator(),
                    CustOperateChannelEnum.MENU,
                    FullCustSourceEnum.PAGE_MENU_OPERATE);
        }
        return updateResp;
    }

    /**
     * @param updateCustReq
     * @return java.lang.String
     * @description: 清除投顾客户的手机号
     * ### 此方法为 兜底方法， 没有任何校验，相当于研发刷数据库开的一个口子
     * ### 极端异常场景才会用这个方法，直接刷数据库，
     * @author: jin.wang03
     * @date: 2024/1/9 20:02
     * @since JDK 1.8
     */
    public Response<String> removeCustMobileByCustNo(UpdateConsCustRequest updateCustReq) {
        return cmCustBusinessService.removeCustMobileByCustNo(updateCustReq);
    }

    /**
     * @param updateCustReq 更新客户信息请求类
     * @return com.howbuy.crm.account.client.response.Response<java.lang.String> 清除指定投顾客户的证件信息响应类
     * @description: 清除投顾客户的证件信息
     * ### 此方法为 兜底方法， 没有任何校验，相当于研发刷数据库开的一个口子
     * ### 极端异常场景才会用这个方法，直接刷数据库，
     * @author: jin.wang03
     * @date: 2024/1/10 13:36
     * @since JDK 1.8
     */
    public Response<String> removeCustIdInfoByCustNo(UpdateConsCustRequest updateCustReq) {
        return cmCustBusinessService.removeCustIdInfoByCustNo(updateCustReq);
    }

    /**
     * @param updateCustReq 更新客户信息请求类
     * @return com.howbuy.crm.account.client.response.Response<com.howbuy.crm.account.client.response.custinfo.UpdateConsCustRespVO> 更新投顾客户响应类
     * @throws
     * @description: 更新投顾客户信息，不校验字段是否允许修改（该方法只在特殊业务的要求下，才能被使用。
     * 正常情况下，请使用updateCustInfo(UpdateConsCustRequest updateCustReq)来更新投顾客户的数据）
     * <p>
     * 目前调用这个接口的按钮： cs-webapp中"同步香港账号信息"按钮、
     * @since JDK 1.8
     */
    public Response<UpdateConsCustRespVO> updateCustInfoWithoutCheckUpdateField(UpdateConsCustRequest updateCustReq) {
        Response<UpdateConsCustRespVO> updateResp = cmCustBusinessService.updateCust(updateCustReq);
        if (updateResp.isSuccess()) {
            custProcessBusinessService.analyseHkBindAfterUpdate(updateCustReq.getCustNo(),
                    updateCustReq.getOperator(),
                    CustOperateChannelEnum.MENU,
                    FullCustSourceEnum.PAGE_MENU_OPERATE);
        }
        return updateResp;
    }

    /**
     * @param hboneNo
     * @return com.howbuy.crm.account.client.response.consultantinfo.CmCustconstantSimpleVO
     * @description:根据一账通号查询投顾客户简单信息
     * <AUTHOR>
     * @date 2024/9/9 14:17
     * @since JDK 1.8
     */
    public CmCustSimpleListVO queryCustSimpleByHboneNo(String hboneNo) {
        CmCustSimpleListVO simpleListVO = new CmCustSimpleListVO();
        // 存在一个一账通号对应多条记录的情况，消费端需要明确该场景并给与提示,所以这里返回list
        List<CmConsCustSimpleBO> simpleBOList = consCustInfoRepository.queryCustSimpleByHboneNo(hboneNo);
        if (CollectionUtils.isEmpty(simpleBOList)) {
            return simpleListVO;
        }
        List<CmConsCustSimpleVO> simpleVOList = Lists.newArrayList();

        for (CmConsCustSimpleBO simpleBO : simpleBOList) {
            CmConsCustSimpleVO simpleVo = new CmConsCustSimpleVO();
            BeanUtils.copyProperties(simpleBO, simpleVo);
            simpleVOList.add(simpleVo);
        }
        simpleListVO.setSimpleVOList(simpleVOList);
        return simpleListVO;
    }



    /**
     * @description:(根据查询条件vo 查询投顾客户简单信息)
     * @param request
     * @return com.howbuy.crm.account.client.response.PageVO<com.howbuy.crm.account.client.response.custinfo.CmConsCustSimpleVO>
     * @author: haoran.zhang
     * @date: 2025/3/5 14:10
     * @since JDK 1.8
     */
    public PageVO<CmConsCustSimpleVO> queryCustSimpleBySearchVo(BatchQueryCustSimpleRequest request){
        //构建查询对象
        CustSimpleSearchVO reqVo = new CustSimpleSearchVO();
        reqVo.setPage(request.getPage());
        reqVo.setRows(request.getRows());

        reqVo.setMobileDigest(request.getMobileDigest());
        reqVo.setConsCodeList(request.getConsCodeList());
        if(CollectionUtils.isNotEmpty(request.getHboneNoList())){
            reqVo.setHboneNoList(Lists.partition(request.getHboneNoList(), Constants.DEFAULT_PAGE_SIZE));
        }
        if(CollectionUtils.isNotEmpty(request.getHkTxAcctNoList())){
            reqVo.setHkTxAcctNoList(Lists.partition(request.getHkTxAcctNoList(),Constants.DEFAULT_PAGE_SIZE));
        }

        Page<CmConsCustSimpleBO>  simpleCustPage =consCustInfoRepository.queryCustSimpleBySearchVo(reqVo);

        List<CmConsCustSimpleVO> voList = Lists.newArrayList();
        simpleCustPage.getResult().forEach(simpleBO -> {
            CmConsCustSimpleVO simpleVo = new CmConsCustSimpleVO();
            BeanUtils.copyProperties(simpleBO, simpleVo);
            voList.add(simpleVo);
        });

        //返回页面对象
        PageVO<CmConsCustSimpleVO> pageVo=new PageVO<>();
        pageVo.setTotal(simpleCustPage.getTotal());
        pageVo.setPage(simpleCustPage.getPageNum());
        pageVo.setSize(simpleCustPage.getPageSize());
        pageVo.setRows(voList);
        return pageVo;
    }

    /**
     * 查询客户历史所属投顾列表
     *
     * @param conscustno 客户号
     * @return CmCustHisConsultantVO
     */
    public CmCustHisConsultantVO queryCustHisConsultantList(String conscustno) {
        CmCustHisConsultantVO cmCustHisConsultantVO = new CmCustHisConsultantVO();
        List<CmCustHisConsultantBO> cmCustHisConsultantBOList = consCustInfoRepository.queryCustHisConsultantList(conscustno);
        if (CollectionUtils.isEmpty(cmCustHisConsultantBOList)) {
            return cmCustHisConsultantVO;
        }

        List<CmCustHisConsultant> list = cmCustHisConsultantBOList.stream()
                .map(cmCustHisConsultantBO -> {
                    CmCustHisConsultant consultant = new CmCustHisConsultant();
                    BeanUtils.copyProperties(cmCustHisConsultantBO, consultant);
                    return consultant;
                })
                .collect(Collectors.toList());
        cmCustHisConsultantVO.setCustHisConsultantList(list);
        return cmCustHisConsultantVO;
    }

    /**
     * @param request
     * @return com.howbuy.crm.account.client.response.custinfo.CustListByConsCodeVO
     * @description:分页查询投顾客户简单信息
     * <AUTHOR>
     * @date 2024/9/24 18:12
     * @since JDK 1.8
     */
    public CustListByConsCodeVO pageQueryCustSimple(PageQueryCustSimpleRequest request) {
        PageCustSimpleReqVO reqVO = new PageCustSimpleReqVO();
        reqVO.setConscode(request.getConscode());
        reqVO.setSearchContent(request.getSearchContent());
        reqVO.setPage(request.getPage());
        reqVO.setRows(request.getRows());

        List<CmConsCustSimpleBO> custSimpleBOList;
        if (request.isByDept()) {
            // 查询投顾同部门所有客户
            custSimpleBOList = consCustInfoRepository.pageQueryCustSimpleByVoAndOutletCodes(reqVO);
        } else {
            // 查询投顾名下客户
            custSimpleBOList = consCustInfoRepository.pageQueryCustSimpleByVo(reqVO);
        }

        CustListByConsCodeVO custListByConsCodeVO = new CustListByConsCodeVO();
        if (CollectionUtils.isNotEmpty(custSimpleBOList)) {
            List<CmConsCustSimpleVO> consCustSimpleVOList = Lists.newArrayList();
            for (CmConsCustSimpleBO cmConsCustSimpleBO : custSimpleBOList) {
                CmConsCustSimpleVO simpleVo = new CmConsCustSimpleVO();
                BeanUtils.copyProperties(cmConsCustSimpleBO, simpleVo);
                consCustSimpleVOList.add(simpleVo);
            }
            custListByConsCodeVO.setConsCustSimpleVOList(consCustSimpleVOList);
        }
        return custListByConsCodeVO;
    }

}