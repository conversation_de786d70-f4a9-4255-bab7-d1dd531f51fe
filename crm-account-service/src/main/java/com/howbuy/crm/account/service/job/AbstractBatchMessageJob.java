/**
 * Copyright (c) 2023, <PERSON><PERSON><PERSON><PERSON> HOWBUY INVESTMENT MANAGEMENT Co., Ltd.
 * All right reserved.
 * <p>
 * THIS IS UNPUBLISHED PROPRIETARY SOURCE CODE OF HOWBUY INVESTMENT
 * MANAGEMENT CO., LTD.  THE CONTENTS OF THIS FILE MAY NOT BE DISCLOSED
 * TO THIRD PARTIES, COPIED OR DUPLICATED IN ANY FORM, IN WHOLE OR IN PART,
 * WITHOUT THE PRIOR WRITTEN PERMISSION OF HOWBUY INVESTMENT MANAGEMENT
 * CO., LTD.
 */
package com.howbuy.crm.account.service.job;

import com.alibaba.fastjson.JSON;
import com.howbuy.cachemanagement.service.CacheService;
import com.howbuy.cachemanagement.service.CacheServiceImpl;
import com.howbuy.crm.account.service.cacheservice.CacheKeyPrefix;
import com.howbuy.crm.account.service.cacheservice.lock.LockService;
import com.howbuy.crm.account.service.commom.enums.QuartzResultEnum;
import com.howbuy.crm.account.service.commom.utils.LoggerUtils;
import com.howbuy.message.MessageService;
import com.howbuy.message.SimpleMessage;
import com.howbuy.message.processor.MessageProcessor;
import com.howbuy.message.utils.FastJsonUtil;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;

import javax.annotation.PostConstruct;
import java.util.HashMap;
import java.util.Map;

/**
 * @description: 调度任务消息接收处理器
 * <AUTHOR>
 * @date 2023/3/8 17:07
 * @since JDK 1.8
 */
@Slf4j
public abstract class AbstractBatchMessageJob extends MessageProcessor {
    /**
     * 调度结果反馈的Key
     */
    public static final String QRTZ_LOG_LIST_KEY = "qrtz_log_list";

    /**
     * 调度日志ID名
     */
    public static final String QRTZ_LOG_ID = "qrtz_log_id";
    /**
     * 定时任务默认锁过期时间600秒
     */
    private static final int DEFAULT_EXPIRE = 600;
    /**
     * 缓存中间件
     */
    protected CacheService cacheService = CacheServiceImpl.getInstance();

    @Autowired
    protected LockService lockService;

    @PostConstruct
    public void init() {
        // 初始化Key
        cacheService.initializeKey(QRTZ_LOG_LIST_KEY);
        // 加载消息处理器
        MessageService.getInstance().addMessageProcessor(this.getQuartMessageChannel(), this);
    }

    /**
     * @description: 处理消息
     * @param message	消息
     * @author: hongdong.xie
     * @date: 2023/3/8 18:04
     * @since JDK 1.8
     */
    @Override
    public void processMessage(SimpleMessage message) {
        long startTime = System.currentTimeMillis();
        LoggerUtils.setUUID();
        log.info("AbstractBatchMessageJob:{} |processMessage start -> message:{}", this.getClass().getSimpleName(), JSON.toJSONString(message));
        int quartzResult = 0;
        String uniqKey = CacheKeyPrefix.LOCK_KEY_PREFIX + getQuartMessageChannel();
        // 运行锁，只有在获取了锁之后才准许执行
        boolean lockFlag = false;

        try {
            lockFlag = lockService.getLock(uniqKey, getExpireSecond());
            // 运行锁，只有在获取了锁之后才准许执行
            if(!lockFlag){
                logger.info("processMessage|get lock fail,uniqKey:{}",uniqKey);
                return;
            }
            this.doProcessMessage(message);
            quartzResult = QuartzResultEnum.SUCCESS.getCode();
        } catch (Exception ex) {
            quartzResult = QuartzResultEnum.FAIL.getCode();
            log.error("Error process Message " + message.getContent(), ex);
        } finally {
            log.info("AbstractBatchMessageJob:{} |processMessage quartzResult:{}, content:{}", this.getClass().getSimpleName(), quartzResult, JSON.toJSONString(message.getContent()));
            if(lockFlag){
                // 释放定时任务锁，只有在获取锁成功才释放锁
                lockService.releaseLock(uniqKey);
            }
            // 返回结果给调度中心
            responseMessage(message, quartzResult);
            log.info("AbstractBatchMessageJob:{} responseMessage write success. costTime:{}", this.getClass().getSimpleName(), System.currentTimeMillis() - startTime);
            LoggerUtils.clearConfig();
        }
    }

    /**
     * @description:针对调度消息的返回结果方法
     * @param message	消息
     * @param quartzResult
     * @author: hongdong.xie
     * @date: 2023/3/8 17:10
     * @since JDK 1.8
     */
    protected void responseMessage(SimpleMessage message, int quartzResult) {
        @SuppressWarnings("unchecked")
        Map<String, Object> requestMap = JSON.parseObject(message.getContent().toString());
        responseQuartzLog(requestMap, quartzResult);
    }

    /**
     * @description:返回结果给调度中心
     * @param requestMap	请求map
     * @param quartzResult	调度结果
     * @author: hongdong.xie
     * @date: 2023/3/8 17:09
     * @since JDK 1.8
     */
    private void responseQuartzLog(Map<String, Object> requestMap, int quartzResult) {
        Map<String, Object> qrtzLogMap = new HashMap<>(3);
        qrtzLogMap.put(QRTZ_LOG_ID, requestMap.get(QRTZ_LOG_ID));
        qrtzLogMap.put("end_time", System.currentTimeMillis());
        qrtzLogMap.put("result", quartzResult);
        cacheService.append(QRTZ_LOG_LIST_KEY, FastJsonUtil.toJson(qrtzLogMap));
    }

    /**
     * @description: 获取定时任务锁过期时间，默认600，如果有其它需求可以重写该方法（单位：秒）
     * @return int 过期秒数
     * @author: hongdong.xie
     * @date: 2023/3/8 17:59
     * @since JDK 1.8
     */
    protected int getExpireSecond(){
        return DEFAULT_EXPIRE;
    }

    /**
     * @description: 获取调度消息队列名
     * @return java.lang.String 调度消息队列名称
     * @author: hongdong.xie
     * @date: 2023/3/8 17:09
     * @since JDK 1.8
     */
    protected abstract String getQuartMessageChannel();

    /**
     * @description:具体执行的回调方法
     * @param message	消息
     * @author: hongdong.xie
     * @date: 2023/3/8 17:08
     * @since JDK 1.8
     */
    protected abstract void doProcessMessage(SimpleMessage message);
}