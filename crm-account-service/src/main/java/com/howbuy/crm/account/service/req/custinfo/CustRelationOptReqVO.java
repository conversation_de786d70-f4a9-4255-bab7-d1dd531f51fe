/**
 * Copyright (c) 2023, <PERSON><PERSON><PERSON><PERSON> HOWBUY INVESTMENT MANAGEMENT Co., Ltd.
 * All right reserved.
 * <p>
 * THIS IS UNPUBLISHED PROPRIETARY SOURCE CODE OF HOWBUY INVESTMENT
 * MANAGEMENT CO., LTD.  THE CONTENTS OF THIS FILE MAY NOT BE DISCLOSED
 * TO THIRD PARTIES, COPIED OR DUPLICATED IN ANY FORM, IN WHOLE OR IN PART,
 * WITHOUT THE PRIOR WRITTEN PERMISSION OF HOWBUY INVESTMENT MANAGEMENT
 * CO., LTD.
 */
package com.howbuy.crm.account.service.req.custinfo;

import lombok.Data;

/**
 * @description: (外部交易账号[hboneNo,HkTxAcctNo] vs 投顾客户号  关联关系操作请求类)
 * <AUTHOR>
 * @date 2023/12/15 13:47
 * @since JDK 1.8
 */
@Data
public class CustRelationOptReqVO {


    /**
     * 投顾客户号
     */
    private String custNo;

    /**
     * 操作员
     */
    private String operator;

    /**
     * 非必须
     * 操作来源 [1-MQ]时，为[异常来源]， [2-菜单页面]时，为菜单名称
     */
    private String operateSource;

    /**
     * 非必须
     * 操作通道 1-MQ  2-菜单页面
     */
    private String operateChannel;

    /**
     * 备注
     */
    private String remark;


}