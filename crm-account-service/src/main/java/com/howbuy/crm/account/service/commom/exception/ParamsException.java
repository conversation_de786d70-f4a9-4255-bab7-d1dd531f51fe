package com.howbuy.crm.account.service.commom.exception;

import com.howbuy.crm.account.client.enums.ExceptionCodeEnum;
import lombok.Getter;

/**
 * <AUTHOR>
 * @description: 参数异常
 * @date 2023/6/8 13:43
 * @since JDK 1.8
 */
@Getter
public class ParamsException extends RuntimeException {
    /**
     * 错误码
     */
    private String code;
    private String desc;

    public ParamsException(ExceptionCodeEnum result) {
        super(result.getDesc());
        this.code = result.getCode();
        this.desc = result.getDesc();
    }

    public ParamsException(String code, String desc) {
        super(desc);
        this.code = code;
        this.desc = desc;
    }

    public ParamsException(ExceptionCodeEnum result, Throwable e) {
        super(result.getDesc(), e);
        this.code = result.getCode();
        this.desc = result.getDesc();
    }
}
