/**
 *Copyright (c) 2016, <PERSON>g<PERSON><PERSON> HOWBUY INVESTMENT MANAGEMENT Co., Ltd.
 *All right reserved.
 *
 *THIS IS UNPUBLISHED PROPRIETARY SOURCE CODE OF HOWBUY INVESTMENT
 *MANAGEMENT CO., LTD.  THE CONTENTS OF THIS FILE MAY NOT BE DISCLOSED
 *TO THIRD PARTIES, COPIED OR DUPLICATED IN ANY FORM, IN WHOLE OR IN PART,
 *WITHOUT THE PRIOR WRITTEN PERMISSION OF HOWBUY INVESTMENT MANAGEMENT
 * CO., LTD.
 */

package com.howbuy.crm.account.service.commom.constant;

/**
 * @description:常量类
 * @reason:
 * <AUTHOR>
 * @date 2023/3/8 17:18
 * @since JDK 1.8
 */
public class Constants {

    /**
     * 日志调用链id
     */
    public static final String TRACE_ID = "traceId";
    /**
     * request请求时间点
     */
    public static final String SERVICE_REQUEST_COST = "requestcost";

    /**
     * 追踪信息
     */
    public static final String XTRACES = "xtraces";


    /**
     * 交易渠道:  9-CRM
     */
    public static final String REQUEST_TRADE_CHANNEL = "9";

    /**
     * 操作员
     */
    public static final String REQUEST_OPER_NO = "www";

    /**
     * 操作 00-查询
     */
    public static final String REQUEST_OPER_CODE = "00";
    /**
     * 操作 IP
     */
    public static final String REQUEST_OPER_IP = "127.0.0.1";


    /**
     * 默认网点号
     */
    public static final String DEFAULT_OUTLET_CODE = "A20150420";
    /**
     * 默认客户IP
     */
    public static final String DEFAULT_CUST_IP = "127.0.0.1";




    /**
     * 客户绑定关系操作类型：0-绑定;
     */
    public static final String CUST_RELATION_OPER_TYPE_BIND = "0";
    /**
     * 客户绑定关系操作类型：1-解绑
     */
    public static final String CUST_RELATION_OPER_TYPE_UNBIND = "1";

    /**
     * 操作通道 1-MQ
     */
    public static final String OPERATE_CHANNEL_MQ="1";
    /**
     * 操作通道 2-菜单页面
     */
    public static final String OPERATE_CHANNEL_MENU="2";

    /**
     * 操作来源 1-异常来源
     */
    public static final String OPERATOR_SYS="sys";

    /**
     * 默认 手机地区码 +86
      */
    public static final String DEFAULT_MOBILE_AREA_CODE="+86";

    /**
     * 默认 身份证签发地区编码
     */
    public static final String DEFAULT_ID_SIGN_AREA_CODE="CN";


    /**
     * IC表  操作来源：1-新增
     */
    public static final String  CUST_IC_OPERATION_CREATE="1";
    /**
     * IC表  操作来源：2-修改
     */
    public static final String  CUST_IC_OPERATION_UPDATE="2";
    /**
     * 0-待审核
     * IC表  审核标志（0：待审核，1：审核通过，2：审核不通过）
     */
    public static final String IC_CHECK_FLAG_PENDING = "0";
    /**
     * 1-审核通过
     * IC表  审核标志（0：待审核，1：审核通过，2：审核不通过）
     */
    public static final String IC_CHECK_FLAG_YES = "1";
    /**
     * 2-审核不通过
     * IC表  审核标志（0：待审核，1：审核通过，2：审核不通过）
     */
    public static final String IC_CHECK_FLAG_NO = "2";


    // RocketMQ 消息体关键字
    public static final String MQ_BODY_KEY = "body";

    /**
     *历史逻辑：  研习社渠道用户 的  一账通注册，新增客户时，创建人
     */
    public static final String  MQ_YXS_SYS_OPERATOR   ="hbmq-yxs-sys";

    /**
     * 默认客户名称
     */
    public static final String DEFAULT_CUST_NAME="先生/女士";

    /**
     * 进入异常表选择 1-香港
     */
    public static final String INTO_ABNORMAL_HK="1";
    /**
     * 进入异常表选择  2-一账通
     */
    public static final String INTO_ABNORMAL_HBONE="2";


    /**
     * 日期时间结束点
     */
    public static final String DATE_TIME_END="235959";
    /**
     * 日期时间开始点
     */
    public static final String DATE_TIME_START="000000";


    /**
     * acccenter-facade返回成功标识
     */
    public static final String  ACCT_CENTER_SUCCESS_CODE="0000000";


    /**
     * 中国国家code
     */
    public static final String NATION_CODE_CN = "CN";

    public static final String CN = "CN";
    public static final String HK = "HK";
    public static final String TW = "TW";
    public static final String MO = "MO";


    public static final String TW_CODE = "710000";
    public static final String HK_CODE = "810000";
    public static final String MO_CODE = "820000";


    public static final String DIVISION_MANAGER_LEVEL = "3";
    public static final String AREA_MANAGER_LEVEL = "5";


    /**
     * 查询条件 IN 默认大小值
     */
    public static final int DEFAULT_PAGE_SIZE = 1000;

    /**
     * http请求成功
     */
    public static final String HTTP_SUCCESS_CODE = "200";


    /**
     * TOPIC：香港开户
     */
    public static final String TOPIC_HK_OPEN_ACCT = "TOPIC_HK_OPEN_ACCT";

    /**
     * tag：CRM资料 香港开户
     */
    public static final String CRM_OPEN_ACCT = "CRM_OPEN_ACCT";

    /**
     * crm开户消息id前缀
     */
    public static final String ID_CRM_OPEN_ACCT_PREFIX = "CrmOpenAcct";




}
