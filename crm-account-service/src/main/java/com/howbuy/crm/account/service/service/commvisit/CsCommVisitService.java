/**
 * Copyright (c) 2024, <PERSON><PERSON><PERSON><PERSON> HOWBUY INVESTMENT MANAGEMENT Co., Ltd.
 * All right reserved.
 * <p>
 * THIS IS UNPUBLISHED PROPRIETARY SOURCE CODE OF HOWBUY INVESTMENT
 * MANAGEMENT CO., LTD.  THE CONTENTS OF THIS FILE MAY NOT BE DISCLOSED
 * TO THIRD PARTIES, COPIED OR DUPLICATED IN ANY FORM, IN WHOLE OR IN PART,
 * WITHOUT THE PRIOR WRITTEN PERMISSION OF HOWBUY INVESTMENT MANAGEMENT
 * CO., LTD.
 */
package com.howbuy.crm.account.service.service.commvisit;

import com.alibaba.fastjson.JSON;
import com.github.pagehelper.PageInfo;
import com.google.common.collect.Lists;
import com.howbuy.common.utils.DateUtil;
import com.howbuy.crm.account.client.enums.commvisit.AccompanyingTypeEnum;
import com.howbuy.crm.account.client.enums.commvisit.VisitPurposeEnum;
import com.howbuy.crm.account.client.request.commvisit.AddCommunicateRecordRequest;
import com.howbuy.crm.account.client.request.commvisit.QueryCommVisitListRequest;
import com.howbuy.crm.account.client.request.commvisit.QueryCommunicateListRequest;
import com.howbuy.crm.account.client.request.commvisit.VisitInitDataRequest;
import com.howbuy.crm.account.client.response.Response;
import com.howbuy.crm.account.client.response.commvisit.*;
import com.howbuy.crm.account.client.utils.LocalDateUtil;
import com.howbuy.crm.account.dao.bo.commvisit.CsCommunicateBO;
import com.howbuy.crm.account.dao.mapper.CommonMapper;
import com.howbuy.crm.account.dao.po.commvisit.CmVisitMinutesAccompanyingPO;
import com.howbuy.crm.account.dao.po.commvisit.CmVisitMinutesPO;
import com.howbuy.crm.account.dao.po.commvisit.CsCommunicateVisitPO;
import com.howbuy.crm.account.dao.po.consultant.CmConsultantPO;
import com.howbuy.crm.account.dao.po.consultant.SearchConsultantDTO;
import com.howbuy.crm.account.dao.po.consultantexp.CmConsultantExpPO;
import com.howbuy.crm.account.service.commom.constant.Constants;
import com.howbuy.crm.account.service.commom.constant.SequenceConstants;
import com.howbuy.crm.account.service.domain.OrgLayerInfoDTO;
import com.howbuy.crm.account.service.domain.consultantexp.ConsultantUserLevelDTO;
import com.howbuy.crm.account.service.outerservice.crmcore.OrganazitonOuterService;
import com.howbuy.crm.account.service.repository.commvisit.CmVisitMinutesAccompanyRepository;
import com.howbuy.crm.account.service.repository.commvisit.CmVisitMinutesRepository;
import com.howbuy.crm.account.service.repository.commvisit.CsCommunicateVisitRepository;
import com.howbuy.crm.account.service.repository.consultant.CmConsultantRepository;
import com.howbuy.crm.account.service.repository.consultantexp.CmConsultantExpRepository;
import com.howbuy.crm.account.service.service.consultantexp.CmConsultantExpService;
import com.howbuy.crm.account.service.service.consultantinfo.ConsultantInfoService;
import crm.howbuy.base.constants.StaticVar;
import crm.howbuy.base.utils.StringUtil;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;

import javax.annotation.PostConstruct;
import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.Date;
import java.util.HashSet;
import java.util.List;
import java.util.Objects;
import java.util.Set;

import com.howbuy.crm.account.service.commom.utils.PinyinUtils;

/**
 * <AUTHOR>
 * @description: (沟通拜访服务)
 * @date 2024/10/24 10:25
 * @since JDK 1.8
 */
@Service
@Slf4j
public class CsCommVisitService {

    @Autowired
    private CsCommunicateVisitRepository csCommunicateVisitRepository;

    @Autowired
    private CmVisitMinutesRepository cmVisitMinutesRepository;

    @Autowired
    private CmVisitMinutesAccompanyRepository cmVisitMinutesAccompanyRepository;

    @Autowired
    private CmConsultantRepository crmConsultantRepository;

    @Autowired
    private CmConsultantExpRepository cmConsultantExpRepository;

    @Autowired
    private CommonMapper commonMapper;

    @Autowired
    private CmConsultantExpService cmConsultantExpService;

    @Autowired
    private CmVisitMinutesService cmVisitMinutesService;

    @Autowired
    private ConsultantInfoService consultantInfoService;

    @Autowired
    private OrganazitonOuterService organizerOuterService;

    /**
     * 历史数据标识 0:拜访表
     */
    private final static String VISIT_FLAG = "0";

    /**
     * value: 拜访纪要，陪访人-总部业资
     */
    @Value("${accompany.headquarters.business.assets}")
    private String headquartersBusinessAssetStr;

    /**
     * 拜访纪要，陪访人-总部业资
     */
    private final List<VisitInitDataResponse.UserInfo> headquartersBusinessAssetList = new ArrayList<>();

    /**
     * value: 拜访纪要，陪访人-其他
     */
    @Value("${accompany.others}")
    private String accompanyOtherStr;

    /**
     * 拜访纪要，陪访人-其他
     */
    private final List<VisitInitDataResponse.UserInfo> accompanyOtherList = new ArrayList<>();


    @PostConstruct
    private void init() {
        if (StringUtils.isNotEmpty(headquartersBusinessAssetStr)) {
            List<VisitInitDataResponse.UserInfo> userInfos = JSON.parseArray(headquartersBusinessAssetStr, VisitInitDataResponse.UserInfo.class);
            userInfos.sort((user1, user2) -> PinyinUtils.comparePinyin(user1.getName(), user2.getName()));
            headquartersBusinessAssetList.addAll(userInfos);
        }
        if (StringUtils.isNotEmpty(accompanyOtherStr)) {
            List<VisitInitDataResponse.UserInfo> userInfos = JSON.parseArray(accompanyOtherStr, VisitInitDataResponse.UserInfo.class);
            userInfos.sort((user1, user2) -> PinyinUtils.comparePinyin(user1.getName(), user2.getName()));
            accompanyOtherList.addAll(userInfos);
        }

    }

    /**
     * @param request
     * @return com.howbuy.crm.account.client.response.commvisit.CmCommVisitListVO
     * @description:查询沟通拜访列表
     * <AUTHOR>
     * @date 2024/10/24 10:25
     * @since JDK 1.8
     */
    public CsCommVisitListVO queryCsCommVisitList(QueryCommVisitListRequest request) {
        CsCommVisitListVO csCommVisitListVO = new CsCommVisitListVO();
        csCommVisitListVO.setCommVisitVOList(Lists.newArrayList());
        List<CsCommunicateVisitPO> csCommunicateVisitPOList =
                csCommunicateVisitRepository.selectByCondition(request.getConsCustNo(), request.getVisitTypeList(),
                        request.getStartDt(), request.getEndDt());
        for (CsCommunicateVisitPO csCommunicateVisitPO : csCommunicateVisitPOList) {
            csCommVisitListVO.getCommVisitVOList().add(buildCsCommVisitVO(csCommunicateVisitPO));
        }
        return csCommVisitListVO;
    }

    /**
     * 查询最新一条沟通拜访列表
     * @param request req
     * @return CsCommVisitListVO
     */
    public CsCommVisitListVO queryNewestCsCommVisitList(QueryCommVisitListRequest request) {
        CsCommVisitListVO csCommVisitListVO = new CsCommVisitListVO();
        csCommVisitListVO.setCommVisitVOList(Lists.newArrayList());
        csCommVisitListVO.setCommVisitVOList(Lists.newArrayList());
        CsCommunicateVisitPO csCommunicateVisitPO = csCommunicateVisitRepository.selectNewestCsCommunicateVisitPO(request.getConsCustNo(), request.getVisitTypeList());
        if (null != csCommunicateVisitPO) {
            csCommVisitListVO.getCommVisitVOList().add(buildCsCommVisitVO(csCommunicateVisitPO));
        }
        return csCommVisitListVO;
    }

    /**
     * @description: 获取客户沟通记录新增页初始化数据
     * @param request 请求参数
     * @return 初始化数据响应
     * <AUTHOR>
     * @date 2025-04-08 13:28:00
     * @since JDK 1.8
     */
    public VisitInitDataResponse getVisitInitData(VisitInitDataRequest request) {
        log.info("获取客户沟通记录新增页初始化数据，投顾编号：{}", request.getConsCode());
        VisitInitDataResponse response = new VisitInitDataResponse();

        List<VisitInitDataResponse.UserInfo> projectManagerUserList = getProjectManagerUserList(request);
        // 项目经理
        response.setProjectManagerUserList(projectManagerUserList);

        // 花名册
        CmConsultantExpPO cmConsultantExp = cmConsultantExpRepository.getCmConsultantExpByConsCode(request.getConsCode());
        // 主管列表：CRM花名册中，当前用户对应的【区域总、区域执行副总、分总】三个字段对应的所有用户：去重，去操作人本人
        List<VisitInitDataResponse.UserInfo> manageUserList = getManageUserList(request, cmConsultantExp);

        response.setManageUserList(manageUserList);
        // 总部业资列表
        response.setSupervisorUserList(headquartersBusinessAssetList);
        // 其他列表
        response.setOtherUserList(accompanyOtherList);
        
        log.info("获取客户沟通记录新增页初始化数据完成");
        return response;
    }

    /**
     * @description: 获取项目经理列表
     * @param request
     * @return java.util.List<com.howbuy.crm.account.client.response.commvisit.VisitInitDataResponse.UserInfo>
     * @author: jin.wang03
     * @date: 2025/5/9 11:08
     * @since JDK 1.8
     */
    private List<VisitInitDataResponse.UserInfo> getProjectManagerUserList(VisitInitDataRequest request) {
        // 查询项目经理
        List<VisitInitDataResponse.UserInfo> projectManagerUserList = new ArrayList<>();

        List<SearchConsultantDTO> consultantList = consultantInfoService.searchConsultant(null, true, request.getConsCode());
        if (CollectionUtils.isNotEmpty(consultantList)) {
            for (SearchConsultantDTO searchConsultantDTO : consultantList) {
                VisitInitDataResponse.UserInfo userInfo = new VisitInitDataResponse.UserInfo();
                userInfo.setCode(searchConsultantDTO.getConsCode());
                userInfo.setName(searchConsultantDTO.getConsName());
                projectManagerUserList.add(userInfo);
            }
        }

        // 剔除当前用户
        projectManagerUserList.removeIf(userInfo -> Objects.equals(userInfo.getCode(), request.getConsCode()));
        
        // 按照中文名称拼音首字母排序
        if (CollectionUtils.isNotEmpty(projectManagerUserList)) {
            projectManagerUserList.sort((user1, user2) -> PinyinUtils.comparePinyin(user1.getName(), user2.getName()));
        }
        
        return projectManagerUserList;
    }


    /**
     * @description: 获取主管列表
     * @param request
     * @param cmConsultantExp
     * @return java.util.List<com.howbuy.crm.account.client.response.commvisit.VisitInitDataResponse.UserInfo>
     * @author: jin.wang03
     * @date: 2025/4/14 15:59
     * @since JDK 1.8
     */
    private List<VisitInitDataResponse.UserInfo> getManageUserList(VisitInitDataRequest request, CmConsultantExpPO cmConsultantExp) {
        List<VisitInitDataResponse.UserInfo> manageUserList = new ArrayList<>();
        if (Objects.isNull(cmConsultantExp)) {
            log.info("投顾编号: {}在花名册中 不存在，无法获取主管列表", request.getConsCode());
            return manageUserList;
        }

        // 获取主管列表
        // 1. 获取分总列表
        String divisionManagerLevel = String.join("|", cmConsultantExpService.listConstantCodeByUserLevel(Constants.DIVISION_MANAGER_LEVEL));
        List<String> divisionManagers = cmConsultantExpService.listLeadersByOrgCodeAndLevel(
                cmConsultantExp.getOutletcode(), divisionManagerLevel);
        log.info("根据outletCode: {}, 获取分总列表：{}", cmConsultantExp.getOutletcode(), JSON.toJSONString(divisionManagers));

        // 2. 获取区域执行副总列表
        List<String> vicePresidents = cmConsultantExpService.listFLeadersByOrgCode(
                cmConsultantExp.getOutletcode());
        log.info("根据outletCode: {}, 获取区域执行副总列表：{}", cmConsultantExp.getOutletcode(), JSON.toJSONString(vicePresidents));

        // 3. 获取区域总列表
        List<String> areaManagers = new ArrayList<>();
        OrgLayerInfoDTO orgLayerInfo = organizerOuterService.getOrgLayerInfoByOrgCode(cmConsultantExp.getOutletcode());
        if (Objects.nonNull(orgLayerInfo) &&
                StringUtil.isNotNullStr(orgLayerInfo.getDistrictCode())) {
            String areaManagerLevel = String.join("|", cmConsultantExpService.listConstantCodeByUserLevel(Constants.AREA_MANAGER_LEVEL));
            areaManagers = cmConsultantExpService.listLeadersByOrgCodeAndLevel(
                    orgLayerInfo.getDistrictCode(), areaManagerLevel);
            log.info("根据areaCode: {}, 获取区域总列表：{}", orgLayerInfo.getDistrictCode(), JSON.toJSONString(areaManagers));
        } else {
            log.info("当前投顾：{}的花名册信息中，areaCode为空，无法获取区域总", cmConsultantExp.getConscode());
        }

        // 4. 合并所有主管列表并去重
        Set<String> managerSet = new HashSet<>();
        managerSet.addAll(divisionManagers);
        managerSet.addAll(vicePresidents);
        managerSet.addAll(areaManagers);

        // 5. 移除操作人本人
        managerSet.remove(request.getConsCode());
        log.info("操作人：{}，合并所有主管列表并去重，移除操作人本人，主管列表：{}", request.getConsCode(), JSON.toJSONString(managerSet));
        if (CollectionUtils.isEmpty(managerSet)) {
            return manageUserList;
        }
        // 6. 转换为UserInfo对象
        List<CmConsultantPO> cmConsultantPOS = crmConsultantRepository.listConsultantByConsCodes(Lists.newArrayList(managerSet));
        log.info("根据操作人：{}，最终主管信息列表：{}", request.getConsCode(), JSON.toJSONString(cmConsultantPOS));
        if (CollectionUtils.isEmpty(cmConsultantPOS)) {
            return manageUserList;
        }
        for (CmConsultantPO manager : cmConsultantPOS) {
            VisitInitDataResponse.UserInfo userInfo = new VisitInitDataResponse.UserInfo();
            userInfo.setCode(manager.getConscode());
            userInfo.setName(manager.getConsname());
            manageUserList.add(userInfo);
        }

        // 7.按照中文名称拼音首字母排序
        if (CollectionUtils.isNotEmpty(manageUserList)) {
            manageUserList.sort((user1, user2) -> PinyinUtils.comparePinyin(user1.getName(), user2.getName()));
        }

        return manageUserList;
    }

    /**
     * @param csCommunicateVisitPO
     * @return com.howbuy.crm.account.client.response.commvisit.CsCommVisitVO
     * @description:构建沟通拜访VO
     * <AUTHOR>
     * @date 2024/10/24 11:09
     * @since JDK 1.8
     */
    public CsCommVisitVO buildCsCommVisitVO(CsCommunicateVisitPO csCommunicateVisitPO) {
        CsCommVisitVO csCommVisitVO = new CsCommVisitVO();
        csCommVisitVO.setConscustno(csCommunicateVisitPO.getConscustno());
        csCommVisitVO.setCommcontent(csCommunicateVisitPO.getCommcontent());
        csCommVisitVO.setVisittype(csCommunicateVisitPO.getVisittype());
        csCommVisitVO.setCreator(csCommunicateVisitPO.getCreator());
        if(Objects.nonNull(csCommunicateVisitPO.getCredt())) {
            csCommVisitVO.setCreDtStr(DateUtil.date2String(csCommunicateVisitPO.getCredt(), DateUtil.STR_PATTERN));
        }
        return csCommVisitVO;
    }


    /**
     * @description: 新增客户沟通记录
     * @param request 新增客户沟通记录请求
     * @return 新增客户沟通记录响应
     * <AUTHOR>
     * @date 2024-04-07 12:05:00
     * @since JDK 1.8
     */
    public Response<AddCommunicateRecordResponse> addCommunicateRecord(AddCommunicateRecordRequest request) {
        log.info("新增客户沟通记录开始，request：{}, ", JSON.toJSONString(request));
        // 业务逻辑实现
        AddCommunicateRecordResponse response = new AddCommunicateRecordResponse();

        // 保存沟通记录信息至CS_COMMUNICATE_VISIT表
        CsCommunicateVisitPO csCommunicateVisitPO = transferCommunicateVisitPO(request);
        log.info("新增客户沟通记录-保存沟通记录信息，ID：{}, 投顾客户号：{}", csCommunicateVisitPO.getId(), csCommunicateVisitPO.getConscustno());
        csCommunicateVisitRepository.insertCommunicateVisit(csCommunicateVisitPO);
        // 保存拜访纪要信息至CM_VISIT_MINUTES表
        String visitMinutesId = saveVisitMinutes(request, csCommunicateVisitPO);

        response.setCommunicateId(csCommunicateVisitPO.getId());
        response.setVisitMinutesId(visitMinutesId);
        log.info("新增客户沟通记录完成，沟通记录ID：{}, 拜访纪要ID：{}", csCommunicateVisitPO.getId(), visitMinutesId);
        return Response.ok(response);
    }

    /**
     * @description: 保存拜访纪要信息至CM_VISIT_MINUTES表
     * @param request 新增客户沟通记录请求
     * @param csCommunicateVisitPO 新增客户沟通记录请求
     * @return java.lang.String
     * @author: jin.wang03
     * @date: 2025/5/6 9:44
     * @since JDK 1.8
     */
    private String saveVisitMinutes(AddCommunicateRecordRequest request, CsCommunicateVisitPO csCommunicateVisitPO) {
        AddCommunicateRecordRequest.VisitMinutesReq visitMinutesReq = request.getVisitMinutesReq();
        String visitMinutesId = null;
        if (isHaveVisitMinutes(visitMinutesReq)) {
            log.info("新增客户沟通记录-存在拜访纪要信息，开始保存拜访纪要");
            // 保存拜访纪要信息至CM_VISIT_MINUTES表
            CmVisitMinutesPO minutesPO = new CmVisitMinutesPO();
            visitMinutesId = commonMapper.getSeqValue(SequenceConstants.SEQ_CM_VISIT_MINUTES);
            log.info("新增客户沟通记录-获取拜访纪要ID：{}", visitMinutesId);
            minutesPO.setId(visitMinutesId);
            minutesPO.setCommunicateId(csCommunicateVisitPO.getId());
            minutesPO.setConsCustNo(csCommunicateVisitPO.getConscustno());
            minutesPO.setVisitPurpose(visitMinutesReq.getVisitPurpose());
            minutesPO.setVisitPurposeOther(visitMinutesReq.getVisitPurposeOther());
            minutesPO.setAssetReportId(visitMinutesReq.getAssetReportId());
            minutesPO.setMarketVal(visitMinutesReq.getMarketVal());
            minutesPO.setHealthAvgStar(visitMinutesReq.getHealthAvgStar());
            minutesPO.setGiveInformation(visitMinutesReq.getGiveInformation());
            minutesPO.setAttendRole(visitMinutesReq.getAttendRole());
            minutesPO.setProductServiceFeedback(visitMinutesReq.getProductServiceFeedback());
            minutesPO.setIpsFeedback(visitMinutesReq.getIpsFeedback());
            minutesPO.setAddAmountRmb(visitMinutesReq.getAddAmountRmb());
            minutesPO.setAddAmountForeign(visitMinutesReq.getAddAmountForeign());
            minutesPO.setFocusAsset(visitMinutesReq.getFocusAsset());
            minutesPO.setEstimateNeedBusiness(visitMinutesReq.getEstimateNeedBusiness());
            minutesPO.setNextPlan(visitMinutesReq.getNextPlan());
            // 上级主管和主管的花名册层级
            ConsultantUserLevelDTO managerUserLevelDto = cmConsultantExpService.getManagerInfoByConsCode(request.getOperator());
            if (Objects.nonNull(managerUserLevelDto)) {
                minutesPO.setManagerId(managerUserLevelDto.getConstCode());
                minutesPO.setManagerUserLevel(managerUserLevelDto.getUserLevel());
            }

            minutesPO.setCreator(request.getOperator());
            minutesPO.setCreateTime(LocalDateTime.now());

            cmVisitMinutesRepository.insert(minutesPO);
            log.info("新增客户沟通记录-保存拜访纪要信息成功，ID：{}", visitMinutesId);

            List<String> pushUserIds = new ArrayList<>();
            // 推送主管
            pushUserIds.add(minutesPO.getManagerId());
            // 保存陪访人信息至CM_VISIT_MINUTES_ACCOMPANYING表
            if (CollectionUtils.isNotEmpty(visitMinutesReq.getAccompanyingList())) {
                log.info("新增客户沟通记录-开始保存陪访人信息，陪访人数量：{}", visitMinutesReq.getAccompanyingList().size());
                // 添加新的陪访人
                for (AddCommunicateRecordRequest.AccompanyingPerson accompanyingPerson : visitMinutesReq.getAccompanyingList()) {
                    CmVisitMinutesAccompanyingPO accompanyingPO = new CmVisitMinutesAccompanyingPO();
                    String visitMinutesAccompanyingId = commonMapper.getSeqValue(SequenceConstants.SEQ_VISIT_MINUTES_ACCOMPANYING);
                    accompanyingPO.setId(visitMinutesAccompanyingId);
                    accompanyingPO.setVisitMinutesId(visitMinutesId);
                    accompanyingPO.setAccompanyingType(accompanyingPerson.getAccompanyingType());
                    accompanyingPO.setAccompanyingUserId(accompanyingPerson.getAccompanyingUserId());
                    // 获取陪访人最高层级
                    String accompanyingUserLevel = cmConsultantExpService.getAccompanyHighestLevelByConsCode(accompanyingPerson.getAccompanyingUserId());
                    accompanyingPO.setAccompanyingUserLevel(accompanyingUserLevel);
                    accompanyingPO.setCreator(request.getOperator());
                    accompanyingPO.setCreateTime(new Date());

                    // 保存陪访人信息
                    cmVisitMinutesAccompanyRepository.insert(accompanyingPO);
                    log.info("新增客户沟通记录-保存陪访人信息成功，ID：{}, 陪访人ID：{}, 陪访类型：{}",
                            visitMinutesAccompanyingId, accompanyingPerson.getAccompanyingUserId(), accompanyingPerson.getAccompanyingType());

                    // 不是其他类型的陪访人推送消息给陪访人
                    if (!Objects.equals(accompanyingPerson.getAccompanyingType(), AccompanyingTypeEnum.OTHER.getCode())) {
                        pushUserIds.add(accompanyingPerson.getAccompanyingUserId());
                    }
                }
                log.info("新增客户沟通记录-保存陪访人信息完成");
            }

            // 拜访目的=IPS陪访、Leads及继承客户拜访
            if (minutesPO.getVisitPurpose().contains(VisitPurposeEnum.IPS_VISIT.getCode())
                    || minutesPO.getVisitPurpose().contains(VisitPurposeEnum.LEADS_VISIT.getCode())) {
                // 发送通知给陪访人和上级主管
                cmVisitMinutesService.addAccompanyingPushMsg(pushUserIds, request.getOperator(), null,
                        csCommunicateVisitPO.getConscustno(), LocalDateTime.now());
            }

        }
        return visitMinutesId;
    }

    /**
     * @description: 是否有拜访纪要信息
     * @param visitMinutesReq
     * @return boolean
     * @author: jin.wang03
     * @date: 2025/4/10 15:13
     * @since JDK 1.8
     */
    private static boolean isHaveVisitMinutes(AddCommunicateRecordRequest.VisitMinutesReq visitMinutesReq) {
        return Objects.nonNull(visitMinutesReq)
                && StringUtils.isNotBlank(visitMinutesReq.getVisitPurpose());
    }


    /**
     * @description: 转换成 沟通记录PO
     * @param request
     * @return com.howbuy.crm.account.dao.po.commvisit.CsCommunicateVisitPO
     * @author: jin.wang03
     * @date: 2025/4/10 15:24
     * @since JDK 1.8
     */
    public CsCommunicateVisitPO transferCommunicateVisitPO(AddCommunicateRecordRequest request) {
        CsCommunicateVisitPO csCommunicateVisit = new CsCommunicateVisitPO();

        // 获取相关id编号
        String id = commonMapper.getSeqValue("SEQ_CS_COMMUNICATE_VISIT_ID");
        // 从序列中获取预约编号;
        String newConsBookingId = commonMapper.getSeqValue("SEQ_PCUSTREC");
        String appSerialNo = commonMapper.getSeqValue("SEQ_PCUSTREC");

        csCommunicateVisit.setId(id);
        csCommunicateVisit.setConsbookingid(newConsBookingId);
        csCommunicateVisit.setHisid(appSerialNo);

        // 投顾客户号
        csCommunicateVisit.setConscustno(request.getCommunicateReq().getConsCustNo());
        // 沟通方式
        csCommunicateVisit.setVisittype(request.getCommunicateReq().getVisittype());
        // 沟通摘要
        csCommunicateVisit.setCommcontent(request.getCommunicateReq().getCommcontent());
        // 拜访日期
        csCommunicateVisit.setVisitDate(request.getCommunicateReq().getVisitDate());
        // 拜访分类
        csCommunicateVisit.setVisitclassify(request.getCommunicateReq().getVisitclassify());

        // 新增提醒------
        if (Objects.nonNull(request.getBookingReq())) {
            // 沟通方式
            csCommunicateVisit.setNextvisittype(request.getBookingReq().getNextvisittype());
            // 预约日期
            csCommunicateVisit.setNextdt(request.getBookingReq().getNextdt());
            csCommunicateVisit.setNextstarttime(request.getBookingReq().getNextstarttime());
            csCommunicateVisit.setNextendtime(request.getBookingReq().getNextendtime());
            // 预约事项
            csCommunicateVisit.setNextvisitcontent(request.getBookingReq().getNextvisitcontent());
        }

        // 创建人
        csCommunicateVisit.setCreator(request.getOperator());
        // 可修改标识 1:内容可编辑, 0:内容不可编辑
        csCommunicateVisit.setModifyflag(StaticVar.NO);
        csCommunicateVisit.setHisflag(VISIT_FLAG);

        return csCommunicateVisit;
    }

    /**
     * @description 查询客户拜访列表
     * @param request 查询客户拜访列表请求
     * @return com.howbuy.crm.account.client.response.commvisit.CommunicateListVO
     * @author: jianjian.yang
     * @date: 2025/4/29 13:27
     * @since JDK 1.8
     */
    public CommunicateListVO queryCommunicateList(QueryCommunicateListRequest request) {
        CommunicateListVO communicateListVO = new CommunicateListVO();
        PageInfo<CsCommunicateBO> csCommunicateBOList = csCommunicateVisitRepository.queryCustVisitListByPage(request.getConsCustNo(),
                request.getPageNo(), request.getPageSize());
        communicateListVO.setTotal(csCommunicateBOList.getTotal());
        if(CollectionUtils.isNotEmpty(csCommunicateBOList.getList())){
            List<CommunicateListVO.CommunicateRecordVO> list = new ArrayList<>();
            csCommunicateBOList.getList().forEach(csCommunicateBO -> {
                list.add(buildCsCommVisitVO(csCommunicateBO));
            });
            communicateListVO.setList(list);
        }
        return communicateListVO;
    }

    /**
     * @description 构建沟通记录VO
     * @param csCommunicateBO
     * @return com.howbuy.crm.account.client.response.commvisit.CommunicateListVO.CommunicateRecordVO
     * @author: jianjian.yang
     * @date: 2025/4/29 13:26
     * @since JDK 1.8
     */
    private CommunicateListVO.CommunicateRecordVO buildCsCommVisitVO(CsCommunicateBO csCommunicateBO) {
        CommunicateListVO.CommunicateRecordVO communicateRecordVO = new CommunicateListVO.CommunicateRecordVO();
        communicateRecordVO.setPkId(csCommunicateBO.getPkId());
        communicateRecordVO.setVisitTime(csCommunicateBO.getVisitTime());
        communicateRecordVO.setModifyFlag(csCommunicateBO.getModifyFlag());
        communicateRecordVO.setHisFlag(csCommunicateBO.getHisFlag());
        communicateRecordVO.setHisId(csCommunicateBO.getHisId());
        communicateRecordVO.setVisitType(csCommunicateBO.getVisitType());
        communicateRecordVO.setConsultType(csCommunicateBO.getConsultType());
        communicateRecordVO.setSourceAddr(csCommunicateBO.getSourceAddr());
        communicateRecordVO.setCreator(csCommunicateBO.getCreator());
        communicateRecordVO.setConsName(csCommunicateBO.getConsName());
        communicateRecordVO.setOrderId(csCommunicateBO.getOrderId());
        communicateRecordVO.setCallInId(csCommunicateBO.getCallInId());
        communicateRecordVO.setVisitSummary(csCommunicateBO.getVisitSummary());
        // 拜访日期 yyyymmdd -> yyyy-mm-dd
        communicateRecordVO.setVisitDt(LocalDateUtil.convertDashDate(csCommunicateBO.getVisitDt()));
        // 拜访目的
        communicateRecordVO.setVisitPurpose(cmVisitMinutesService.getDescriptionsByCodesComma(csCommunicateBO.getVisitPurpose(),
                csCommunicateBO.getVisitPurposeOther()));
        // 拜访纪要ID
        communicateRecordVO.setVisitMinutesId(csCommunicateBO.getVisitMinutesId());
        return communicateRecordVO;
    }
}
