package com.howbuy.crm.account.service.facade.consultant;

import com.alibaba.fastjson.JSON;
import com.howbuy.crm.account.client.facade.consultant.BatchConsultantSimpleFacade;
import com.howbuy.crm.account.client.request.consultant.BatchConsultantSimpleRequest;
import com.howbuy.crm.account.client.response.Response;
import com.howbuy.crm.account.client.response.consultant.BatchConsultantSimpleResponse;
import com.howbuy.crm.account.client.response.consultant.ConsultantSimpleResponse;
import com.howbuy.crm.account.dao.po.consultant.ConsultantSimpleInfoDto;
import com.howbuy.crm.account.service.business.consultant.ConsultantBusinessService;
import lombok.extern.slf4j.Slf4j;
import org.apache.dubbo.config.annotation.DubboService;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;

/**
 * @description: 批量查询投顾简单信息服务实现
 * <AUTHOR>
 * @date 2025/3/10
 * @since JDK 1.8
 */
@Slf4j
@Service
@DubboService
public class BatchConsultantSimpleFacadeImpl implements BatchConsultantSimpleFacade {

    @Resource
    private ConsultantBusinessService consultantBusinessService;

    @Override
    public Response<BatchConsultantSimpleResponse> batchQueryConsultantSimple(BatchConsultantSimpleRequest request) {
        log.info("批量查询投顾简单信息，入参：{}", JSON.toJSONString(request));
        
        try {
            // 调用Service层批量查询投顾信息
            Map<String, ConsultantSimpleInfoDto> consultantMap = consultantBusinessService.getConsultantMapByCodeList(request.getConsCodeList());
            
            BatchConsultantSimpleResponse response = new BatchConsultantSimpleResponse();
            List<ConsultantSimpleResponse> consultantList = new ArrayList<>();
            
            if (!CollectionUtils.isEmpty(consultantMap)) {
                consultantMap.forEach((consCode, infoDto) -> {
                    if (infoDto != null) {
                        ConsultantSimpleResponse consultantResponse = new ConsultantSimpleResponse();
                        BeanUtils.copyProperties(infoDto, consultantResponse);
                        consultantList.add(consultantResponse);
                    }
                });
            }
            
            response.setConsultantList(consultantList);
            log.info("批量查询投顾简单信息成功，出参：{}", JSON.toJSONString(response));
            return Response.ok(response);
        } catch (Exception e) {
            log.error("批量查询投顾简单信息异常", e);
            return Response.fail("批量查询投顾简单信息失败：" + e.getMessage());
        }
    }
} 