package com.howbuy.crm.account.service.business.consultant;

import com.google.common.collect.Maps;
import com.howbuy.crm.account.dao.po.consultant.ConsultantSimpleInfoDto;
import com.howbuy.crm.account.service.cacheservice.consultant.CacheConsultantInfoService;
import com.howbuy.crm.account.service.repository.consultant.CmConsultantRepository;
import crm.howbuy.base.utils.StringUtil;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.util.Assert;

import java.util.List;
import java.util.Map;

/**
 * @description: 投顾信息  businessService
 * <AUTHOR>
 * @time  2022年6月16日 17点38分
 */
@Slf4j
@Service
public class ConsultantBusinessService {

    @Autowired
    private CacheConsultantInfoService cacheConsultantInfoService;

    @Autowired
    private CmConsultantRepository cmConsultantRepository;

//    @Autowired
//    private HbOrganizationServiceBuss orgServiceBuss;

    /**
     * 根据consCode获取投顾信息[cache]
     * @param consCode
     * @return
     */
    public ConsultantSimpleInfoDto getInfoByconsCode(String consCode){
        Assert.notNull(consCode);
        return cacheConsultantInfoService.getInfoByconsCode(consCode);
    }


    /**
     * @api {DUBBO} com.howbuy.crm.account.service.business.consultant.ConsultantBusinessService.getInfoByconsCode(consCode,isInvestAdvisor)
     * @apiVersion 1.0.0
     * @apiGroup ConsultantInfoService
     * @apiName getInfoByconsCode()
     * @apiDescription 根据consiCode获取 投顾 数据
     * @apiParam (consCode) {String} consCode 投顾code
     * @apiParam (isInvestAdvisor) {Boolean} isInvestAdvisor 是否投顾[consLevel非空表示为投顾]
     * @apiParamExample 请求参数示例
     * isInvestAdvisor=true&consCode=k
     * @apiSuccess (响应结果) {String} consCode 理财顾问代码
     * @apiSuccess (响应结果) {String} consName 理财顾问名称
     * @apiSuccess (响应结果) {String} consLevel 理财顾问级别
     * @apiSuccess (响应结果) {String} consStatus 理财顾问状态1有效，0无效
     * @apiSuccess (响应结果) {String} isVirtual 是否虚拟投顾（1 是/0 否）
     * @apiSuccess (响应结果) {String} outletCode 所属理财中心
     * @apiSuccess (响应结果) {String} teamCode 所属小组
     * @apiSuccess (响应结果) {String} mobile 手机
     * @apiSuccess (响应结果) {String} picAddr 投顾照片链接地址
     * @apiSuccess (响应结果) {String} codePicAddr 企业微信二维码地址
     * @apiSuccess (响应结果) {String} position 理财顾问职位
     * @apiSuccess (响应结果) {String} email 电子邮件
     * @apiSuccess (响应结果) {String} centerOrgCode 所属中心	  {@link com.howbuy.crm.base.model.CenterOrgEnum}
     * @apiSuccessExample 响应结果示例
     * {"codePicAddr":"ojoDyrn1Bs","picAddr":"GGp7mVi","mobile":"eaH","teamCode":"0BlJ","consName":"boFuzOT","consCode":"k0XWA","centerOrgCode":"TBp","consStatus":"aeWkNrlt0","consLevel":"P8OhF","isVirtual":"42","position":"ivFoMYk","outletCode":"F","email":"fWYwdh0"}
     */
    public ConsultantSimpleInfoDto getInfoByconsCode(String consCode,boolean isInvestAdvisor){
        ConsultantSimpleInfoDto dto=getInfoByconsCode(consCode);
        return Boolean.compare(isInvestAdvisor, StringUtil.isNotNullStr(dto.getConsLevel()))==0 ?dto:null;
    }



    /**
     * @description:(重载投顾缓存)
     * @param
     * @return void
     * @author: haoran.zhang
     * @date: 2025/3/6 13:43
     * @since JDK 1.8
     */
    public void reloadCache(){
        cacheConsultantInfoService.reloadCache();
    }

    /**
     * @description:(重载投顾缓存-单个投顾)
     * @param consCode
     * @return void
     * @author: haoran.zhang
     * @date: 2025/3/6 13:43
     * @since JDK 1.8
     */
    public void reloadCacheByConsCode(String consCode) {
        cacheConsultantInfoService.reloadCacheByConsCode(consCode);
    }

   /**
    * @description:(获取全量投顾客户Map信息[cache])
    * @param
    * @return java.util.Map<java.lang.String,com.howbuy.crm.account.dao.po.consultant.ConsultantSimpleInfoDto>
    * @author: haoran.zhang
    * @date: 2025/3/6 13:44
    * @since JDK 1.8
    */
    public Map<String,ConsultantSimpleInfoDto> getConsultantMap(){
        return cacheConsultantInfoService.getConsultantMap();
    }

   /**
    * @description:(根据 consCode列表 获取 投顾Map信息)
    * @param conscodeList
    * @return java.util.Map<java.lang.String,com.howbuy.crm.account.dao.po.consultant.ConsultantSimpleInfoDto>
    * @author: haoran.zhang
    * @date: 2025/3/6 13:44
    * @since JDK 1.8
    */
    public Map<String,ConsultantSimpleInfoDto> getConsultantMapByCodeList(List<String> consCodeList){
        Map<String,ConsultantSimpleInfoDto> returnMap= Maps.newHashMap();
        consCodeList.forEach(consCode-> returnMap.put(consCode,getInfoByconsCode(consCode)));
        return returnMap;
    }

   /**
    * @description:(根据 consCode列表 获取 投顾姓名的Map信息)
    * @param consCodeList
    * @return java.util.Map<java.lang.String,java.lang.String>
    * @author: haoran.zhang
    * @date: 2025/3/6 13:45
    * @since JDK 1.8
    */
    public Map<String,String> getConsNameMapByCodeList(List<String> consCodeList){
        Map<String,String> returnMap=Maps.newHashMap();
        consCodeList.forEach(consCode->{
            ConsultantSimpleInfoDto infoDto=getInfoByconsCode(consCode);
            returnMap.put(consCode,infoDto==null?null:infoDto.getConsName());
        });
        return returnMap;
    }

}
