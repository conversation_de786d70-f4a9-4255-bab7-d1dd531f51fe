package com.howbuy.crm.account.service.vo.custinfo;
import lombok.Data;

/**
 * @description:(香港|账户中心  外部消息，尝试 查找符合条件的客户 参数属性)
 * @param
 * @return
 * @author: haoran.zhang
 * @date: 2023/12/19 19:17
 * @since JDK 1.8
 */
@Data
public class SearchCustKeyAttrVO {

    /**
     * 投资者类型  0-机构,1-个人,2-产品户
     * NOT NULL
     */
    private String invstType;


    /**
     * 客户名称
     */
    private String custName;

    /***********************证件信息 相关对象 *********************************************/

    /**
     *证件号码
     */
    private String idNoDigest;

    /**
     * 证件地区码-身份证签发地区编码
     */
    private String idSignAreaCode;

    /**
     * 证件类型
     */
    private String idType;


    /***********************手机信息 相关对象 *********************************************/

    /**
     * 手机号码区号
     */
    private String mobileAreaCode;
    /**
     * 手机号码
     */
    private String mobileDigest;
}
