/**
 * Copyright (c) 2023, <PERSON><PERSON><PERSON><PERSON> HOWBUY INVESTMENT MANAGEMENT Co., Ltd.
 * All right reserved.
 * <p>
 * THIS IS UNPUBLISHED PROPRIETARY SOURCE CODE OF HOWBUY INVESTMENT
 * MANAGEMENT CO., LTD.  THE CONTENTS OF THIS FILE MAY NOT BE DISCLOSED
 * TO THIRD PARTIES, COPIED OR DUPLICATED IN ANY FORM, IN WHOLE OR IN PART,
 * WITHOUT THE PRIOR WRITTEN PERMISSION OF HOWBUY INVESTMENT MANAGEMENT
 * CO., LTD.
 */
package com.howbuy.crm.account.service.controller.custinfo;

import com.howbuy.crm.account.client.request.custinfo.*;
import com.howbuy.crm.account.client.response.PageVO;
import com.howbuy.crm.account.client.response.Response;
import com.howbuy.crm.account.client.response.custinfo.AbnormalCustInfoVO;
import com.howbuy.crm.account.client.response.custinfo.AbnormalRelatedDetailVO;
import com.howbuy.crm.account.service.service.custinfo.HkAbnormalCustInfoService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import java.util.List;

/**
 * <AUTHOR>
 * @description: (香港异常客户处理 controller)
 * @date 2023/12/13 16:08
 * @since JDK 1.8
 */
@RestController
@RequestMapping("/hkabnormal")
public class HkAbnormalController {


    @Autowired
    private HkAbnormalCustInfoService hkAbnormalCustInfoService;

    /**
     * @api {POST} /hkabnormal/queryhkabmoamlpage queryHkAbnormalPage()
     * @apiVersion 1.0.0
     * @apiGroup HkAbnormalController
     * @apiName queryHkAbnormalPage()
     * @apiDescription 分页查询香港客户异常信息列表
     * @apiParam (请求体) {String} custName 客户姓名
     * @apiParam (请求体) {String} hkTxAcctNo 香港客户号
     * @apiParam (请求体) {String} custNo 客户号
     * @apiParam (请求体) {String} mobileDigest 手机号
     * @apiParam (请求体) {String} idNoDigest 证件号
     * @apiParam (请求体) {String} abnormalSource 异常来源：1-香港注册2-香港开户 7-好买开户 [暂时同于  7-分销开户]3-香港客户信息同步4-香港客户号/一账通绑定5-一账通实名
     * @apiParam (请求体) {String} dealStatus 处理状态：0-未处理 1-已处理 2-无需处理
     * @apiParam (请求体) {Number} createBginDdate 创建时间-开始
     * @apiParam (请求体) {Number} createEndDate 创建时间-结束
     * @apiParam (请求体) {Number} page 页号
     * @apiParam (请求体) {Number} size 每页大小
     * @apiParamExample 请求体示例
     * {"hkTxAcctNo":"mJJbQMSgV","custNo":"UWDq70si","abnormalSource":"D5nJj9k","size":7280,"mobileDigest":"hrCC","idNoDigest":"S","dealStatus":"7gMY","createBginDdate":397741604186,"page":1108,"custName":"W","createEndDate":3374798438201}
     * @apiSuccess (响应结果) {String} code 状态码
     * @apiSuccess (响应结果) {String} description 描述信息
     * @apiSuccess (响应结果) {Object} data 数据封装
     * @apiSuccess (响应结果) {Number} data.page 当前第几页
     * @apiSuccess (响应结果) {Number} data.size 单页条数
     * @apiSuccess (响应结果) {Number} data.total 总条数
     * @apiSuccess (响应结果) {Array} data.rows 数据对象列表
     * @apiSuccess (响应结果) {String} data.rows.id 异常客户数据ID
     * @apiSuccess (响应结果) {String} data.rows.messageClientId 消息通知的clientId
     * @apiSuccess (响应结果) {String} data.rows.hkTxAcctNo 香港客户号
     * @apiSuccess (响应结果) {String} data.rows.custName 客户姓名
     * @apiSuccess (响应结果) {String} data.rows.investType 投资者类型
     * @apiSuccess (响应结果) {String} data.rows.mobileAreaCode 手机地区码
     * @apiSuccess (响应结果) {String} data.rows.mobileDigest 手机号摘要
     * @apiSuccess (响应结果) {String} data.rows.mobileMask 手机号掩码
     * @apiSuccess (响应结果) {String} data.rows.mobileCipher 手机号密文
     * @apiSuccess (响应结果) {String} data.rows.idSignAreaCode 证件地区码
     * @apiSuccess (响应结果) {String} data.rows.idType 证件类型
     * @apiSuccess (响应结果) {String} data.rows.idNoDigest 证件号码摘要
     * @apiSuccess (响应结果) {String} data.rows.idNoMask 证件号码掩码
     * @apiSuccess (响应结果) {String} data.rows.idNoCipher 证件号码密文
     * @apiSuccess (响应结果) {String} data.rows.hboneNo 一账通号
     * @apiSuccess (响应结果) {String} data.rows.abnormalSource 异常来源：1-香港注册2-香港开户7-好买开户 [暂时同于  7-分销开户]3-香港客户信息同步4-香港客户号/一账通绑定5-一账通实名
     * @apiSuccess (响应结果) {String} data.rows.abnormalSceneType 异常描述，按异常类别汇总：1-匹配到多个投顾客户号【香港注册、香港开户】2-匹配到的投顾客户号已被占用 【香港注册】3-手机号相同，证件类型/证件号不匹配 【香港开户】4-证件相同，但手机号不匹配【香港开户】5-CRM重复客户预警【香港客户信息同步】6-同时绑定香港客户号时，投顾客户号/香港客户号被占用 【好买开户、一账通实名】7-香港开户证件与一账通证件不一致 【香港开户】8-两边绑定的投顾客户号不一致【香港客户号/一账通绑定】
     * @apiSuccess (响应结果) {String} data.rows.creator 创建人
     * @apiSuccess (响应结果) {Number} data.rows.createTimestamp 创建时间
     * @apiSuccess (响应结果) {String} data.rows.modifier 修改人
     * @apiSuccess (响应结果) {Number} data.rows.modifyTimestamp 修改时间
     * @apiSuccess (响应结果) {String} data.rows.recStat 记录有效状态（1-正常  0-删除）
     * @apiSuccess (响应结果) {String} data.rows.dealStatus 处理状态：0-未处理 1-已处理 2-无需处理
     * @apiSuccess (响应结果) {String} data.rows.dealOperator 处理人
     * @apiSuccess (响应结果) {String} data.rows.dealRemark 处理意见
     * @apiSuccess (响应结果) {String} data.rows.hkCustState 客户状态(香港)
     * @apiSuccess (响应结果) {String} data.rows.hkHboneNo 一账通号(香港)
     * @apiSuccess (响应结果) {String} data.rows.relatedCustNo crm已关联的 客户号
     * @apiSuccess (响应结果) {Number} data.rows.dealTimestamp 处理时间
     * @apiSuccess (响应结果) {Array} data.rows.relatedList 关联客户列表
     * @apiSuccessExample 响应结果示例
     * {"code":"uc6","data":{"total":2461,"size":6172,"page":3397,"rows":[{"modifier":"19Ax3","createTimestamp":2847557079969,"modifyTimestamp":3225884958160,"mobileAreaCode":"dw33bGR","mobileDigest":"w","id":"x0ZATl","dealRemark":"YKKs9i5dd1","hboneNo":"Zg20U","investType":"EWfNl1X","creator":"eXdKVqT","idType":"8XjwWQS1","messageClientId":"4An4wLC2j","dealOperator":"9EKRJ6B","dealTimestamp":3362293158068,"idNoDigest":"y","dealStatus":"Ld7P8kC","custName":"t","mobileCipher":"r4Uo0zhXa","idNoMask":"5o6DTfzVuC","hkCustState":"ooSP4sX","hkTxAcctNo":"b","idSignAreaCode":"4q3j3w","abnormalSource":"5c","abnormalSceneType":"uxCozE","relatedList":[],"hkHboneNo":"F4Xt84K9ak","idNoCipher":"tG9T4p4SvV","relatedCustNo":"yZtBc","recStat":"CWWV","mobileMask":"GN5tx"}]},"description":"wAL2"}
     */
    @PostMapping("/queryhkabmoamlpage")
    @ResponseBody
    public Response<PageVO<AbnormalCustInfoVO>> queryHkAbnormalPage(@RequestBody AbnormalCustInfoRequest custInfoRequest) {
        return Response.ok(hkAbnormalCustInfoService.queryHkAbnormalPage(custInfoRequest));
    }


    /**
     * @api {POST} /hkabnormal/queryhkabmoamldetaillist queryHkAbnormalDetailList()
     * @apiVersion 1.0.0
     * @apiGroup HkAbnormalController
     * @apiName queryHkAbnormalDetailList()
     * @apiDescription 根据香港异常客户关联子表明细id列表查询明细信息
     * @apiParam (请求体) {Array} requestBody
     * @apiParamExample 请求体示例
     * ["jewDcfH"]
     * @apiSuccess (响应结果) {String} code 状态码
     * @apiSuccess (响应结果) {String} description 描述信息
     * @apiSuccess (响应结果) {Array} data 数据封装
     * @apiSuccess (响应结果) {String} data.id 异常客户待关联id
     * @apiSuccess (响应结果) {String} data.abnormalId 异常客户数据ID
     * @apiSuccess (响应结果) {String} data.custNo 客户号
     * @apiSuccess (响应结果) {String} data.custName 客户名称
     * @apiSuccess (响应结果) {String} data.investType 投资者类型
     * @apiSuccess (响应结果) {String} data.mobileAreaCode 手机地区码
     * @apiSuccess (响应结果) {String} data.mobileDigest 手机号摘要
     * @apiSuccess (响应结果) {String} data.mobileMask 手机号掩码
     * @apiSuccess (响应结果) {String} data.mobileCipher 手机号密文
     * @apiSuccess (响应结果) {String} data.idSignAreaCode 证件地区码
     * @apiSuccess (响应结果) {String} data.idType 证件类型
     * @apiSuccess (响应结果) {String} data.idNoDigest 证件号码摘要
     * @apiSuccess (响应结果) {String} data.idNoMask 证件号码掩码
     * @apiSuccess (响应结果) {String} data.idNoCipher 证件号码密文
     * @apiSuccess (响应结果) {String} data.hboneNo 一账通号
     * @apiSuccess (响应结果) {String} data.hkTxAcctNo 香港客户号
     * @apiSuccess (响应结果) {String} data.consCode 客户所属投顾
     * @apiSuccess (响应结果) {String} data.creator 创建人
     * @apiSuccess (响应结果) {Number} data.createTimestamp 创建时间
     * @apiSuccess (响应结果) {String} data.modifier 修改人
     * @apiSuccess (响应结果) {Number} data.modifyTimestamp 修改时间
     * @apiSuccess (响应结果) {String} data.idTypeDesc 证件类型描述
     * @apiSuccess (响应结果) {String} data.createDt 投顾客户号】的【创建日期】      yyyy-MM-dd
     * @apiSuccessExample 响应结果示例
     * {"code":"aZd3JQqrf","data":[{"investType":"BCbfaJadt","custNo":"TM","creator":"HlF6LjIyAP","idType":"wnQQw4mL","modifier":"4dMCcE","idNoDigest":"Ozg8","custName":"8T1oZV","mobileCipher":"aKDxK5UI","idNoMask":"28VKolWYzE","createDt":"JlbRumhOfW","consCode":"xZaC00VC4","createTimestamp":2928064429346,"modifyTimestamp":3713755581046,"mobileAreaCode":"QUbcYd6z","hkTxAcctNo":"tSHHH6s","idSignAreaCode":"cTVAfsDLa","abnormalId":"PQfPUP","mobileDigest":"0f23Lj4Z","id":"iHJ","idTypeDesc":"omppo","idNoCipher":"hI","mobileMask":"c4","hboneNo":"CdalW"}],"description":"g"}
     */
    @PostMapping("/queryhkabmoamldetaillist")
    @ResponseBody
    public Response<List<AbnormalRelatedDetailVO>> queryHkAbnormalDetailList(@RequestBody List<String> detailList) {
        return Response.ok(hkAbnormalCustInfoService.queryHkAbnormalDetailList(detailList));
    }


    /**
     * @api {POST} /hkabnormal/dealabnormal dealAbnormal()
     * @apiVersion 1.0.0
     * @apiGroup HkAbnormalController
     * @apiName dealAbnormal()
     * @apiDescription 香港客户异常信息处理
     * @apiParam (请求体) {String} id 异常客户信息id
     * @apiParam (请求体) {String} operator 操作人
     * @apiParam (请求体) {String} remark remark备注说明
     * @apiParam (请求体) {String} dealStatus 处理状态：0-未处理 1-已处理 2-无需处理
     * @apiParamExample 请求体示例
     * {"remark":"Q7CKNt10je","id":"LruCJ","dealStatus":"zanX7V6bax","operator":"ci0usf"}
     * @apiSuccess (响应结果) {String} code 状态码
     * @apiSuccess (响应结果) {String} description 描述信息
     * @apiSuccess (响应结果) {String} data 数据封装
     * @apiSuccessExample 响应结果示例
     * {"code":"kf1h18QsER","data":"X1S","description":"UWoBqM"}
     */
    @PostMapping("/dealabnormal")
    @ResponseBody
    public Response<String> dealAbnormal(@RequestBody DealAbnormalRequest custInfoRequest) {
        return hkAbnormalCustInfoService.dealAbnormal(custInfoRequest);
    }


    /**
     * @api {POST} /hkabnormal/batchdealabnormal batchDealAbnormal()
     * @apiVersion 1.0.0
     * @apiGroup HkAbnormalController
     * @apiName batchDealAbnormal()
     * @apiDescription 香港客户异常信息批量操作处理
     * @apiParam (请求体) {Array} idList 异常客户信息id
     * @apiParam (请求体) {String} operator 操作人
     * @apiParam (请求体) {String} remark remark备注说明
     * @apiParam (请求体) {String} dealStatus 处理状态：0-未处理 1-已处理 2-无需处理
     * @apiParamExample 请求体示例
     * {"remark":"bo2YF","idList":["qz6nGoM3rg"],"dealStatus":"xtQ","operator":"BA20LDm"}
     * @apiSuccess (响应结果) {String} code 状态码
     * @apiSuccess (响应结果) {String} description 描述信息
     * @apiSuccess (响应结果) {String} data 数据封装
     * @apiSuccessExample 响应结果示例
     * {"code":"TF","data":"Fp","description":"Mge9"}
     */
    @PostMapping("/batchdealabnormal")
    @ResponseBody
    public Response<String> batchDealAbnormal(@RequestBody BatchDealAbnormalRequest batchRequest) {
        return hkAbnormalCustInfoService.batchDealAbnormal(batchRequest);
    }


    /**
     * @api {POST} /hkabnormal/unbindrelatedcustno unbindRelatedCustNo()
     * @apiVersion 1.0.0
     * @apiGroup HkAbnormalController
     * @apiName unbindRelatedCustNo()
     * @apiDescription 香港异常客户，解绑关联的投顾客户
     * @apiParam (请求体) {String} id 异常客户信息id
     * @apiParam (请求体) {String} operator 操作人
     * @apiParamExample 请求体示例
     * {"id":"Mjorg","operator":"SkFJbEwqU"}
     * @apiSuccess (响应结果) {String} code 状态码
     * @apiSuccess (响应结果) {String} description 描述信息
     * @apiSuccess (响应结果) {String} data 数据封装
     * @apiSuccessExample 响应结果示例
     * {"code":"U6pHAdG","data":"JPAVyidxg","description":"XaH5MR"}
     */
    @ResponseBody
    @PostMapping("/unbindrelatedcustno")
    public Response<String> unbindRelatedCustNo(@RequestBody HkUnbindRelatedCustNoRequest custInfoRequest) {
        return hkAbnormalCustInfoService.unbindRelatedCustNo(custInfoRequest);
    }

    /**
     * @api {POST} /hkabnormal/createcustinfobyhk createCustInfoByHk()
     * @apiVersion 1.0.0
     * @apiGroup HkAbnormalController
     * @apiName createCustInfoByHk()
     * @apiDescription 创建投顾客户(重新跑 消费消息的流程)
     * @apiParam (请求体) {String} id 异常客户信息id
     * @apiParam (请求体) {String} operator 操作人
     * @apiParamExample 请求体示例
     * {"id":"1","operator":"n8k"}
     * @apiSuccess (响应结果) {String} code 状态码
     * @apiSuccess (响应结果) {String} description 描述信息
     * @apiSuccess (响应结果) {String} data 数据封装
     * @apiSuccessExample 响应结果示例
     * {"code":"kbZTzRs","data":"fAk2xe","description":"P5sScdg"}
     */
    @ResponseBody
    @PostMapping("/createcustinfobyhk")
    public Response<String> createCustInfoByHk(@RequestBody HkAbnormalCreateConsCustRequest custInfoRequest) {
        return hkAbnormalCustInfoService.createCustInfoByHk(custInfoRequest);
    }


    /**
     * @api {POST} /hkabnormal/subtablebbindhkcustno subTablebBindHkCustNo()
     * @apiVersion 1.0.0
     * @apiGroup HkAbnormalController
     * @apiName subTablebBindHkCustNo()
     * @apiDescription 香港异常客户 子表投顾客户号，绑定香港客户号
     * @apiParam (请求体) {String} id 异常客户信息id
     * @apiParam (请求体) {String} operator 操作人
     * @apiParamExample 请求体示例
     * {"id":"5d","operator":"iTR"}
     * @apiSuccess (响应结果) {String} code 状态码
     * @apiSuccess (响应结果) {String} description 描述信息
     * @apiSuccess (响应结果) {String} data 数据封装
     * @apiSuccessExample 响应结果示例
     * {"code":"NydKkzvc","data":"3iifo","description":"qq"}
     */
    @ResponseBody
    @PostMapping("/subtablebbindhkcustno")
    public Response<String> subTablebBindHkCustNo(@RequestBody HkAbnormalSubTableBindHkCustNoRequest custInfoRequest) {
        return hkAbnormalCustInfoService.subTablebBindHkCustNo(custInfoRequest);
    }


    /**
     * @api {POST} /hkabnormal/validatebeforeupdatecustinfobyhk validatebBeforeUpdateCustInfoByHk()
     * @apiVersion 1.0.0
     * @apiGroup HkAbnormalController
     * @apiName validatebBeforeUpdateCustInfoByHk()
     * @apiDescription 香港异常客户页- 使用香港开户信息更新投顾客户 前置校验
     * @apiParam (请求体) {String} id 异常客户信息id
     * @apiParam (请求体) {String} operator 操作人
     * @apiParamExample 请求体示例
     * {"id":"3BH923RGd","operator":"DSBS84LX"}
     * @apiSuccess (响应结果) {String} code 状态码
     * @apiSuccess (响应结果) {String} description 描述信息
     * @apiSuccess (响应结果) {String} data 数据封装
     * @apiSuccessExample 响应结果示例
     * {"code":"ld","data":"Sy9","description":"v36i2dT"}
     */
    @ResponseBody
    @PostMapping("/validatebeforeupdatecustinfobyhk")
    public Response<String> validatebBeforeUpdateCustInfoByHk(@RequestBody HkAbnormalUpdateConsCustRequest custInfoRequest) {
        return hkAbnormalCustInfoService.validatebBeforeUpdateCustInfoByHk(custInfoRequest);
    }


    /**
     * @api {POST} /hkabnormal/updatecustinfobyhk updateCustInfoByHk()
     * @apiVersion 1.0.0
     * @apiGroup HkAbnormalController
     * @apiName updateCustInfoByHk()
     * @apiDescription 香港异常客户页- 使用香港开户信息更新投顾客户
     * @apiParam (请求体) {String} id 异常客户信息id
     * @apiParam (请求体) {String} operator 操作人
     * @apiParamExample 请求体示例
     * {"id":"XUb1tvkBA","operator":"kKEWK7i0"}
     * @apiSuccess (响应结果) {String} code 状态码
     * @apiSuccess (响应结果) {String} description 描述信息
     * @apiSuccess (响应结果) {String} data 数据封装
     * @apiSuccessExample 响应结果示例
     * {"code":"wGl","data":"5L7SP2NiS","description":"iQTSf0LlZv"}
     */
    @ResponseBody
    @PostMapping("/updatecustinfobyhk")
    public Response<String> updateCustInfoByHk(@RequestBody HkAbnormalUpdateConsCustRequest custInfoRequest) {
        return hkAbnormalCustInfoService.updateCustInfoByHk(custInfoRequest);
    }


}