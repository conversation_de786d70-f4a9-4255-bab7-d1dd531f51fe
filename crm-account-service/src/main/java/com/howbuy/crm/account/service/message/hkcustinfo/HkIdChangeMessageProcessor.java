/**
 * Copyright (c) 2023, <PERSON><PERSON><PERSON><PERSON> HOWBUY INVESTMENT MANAGEMENT Co., Ltd.
 * All right reserved.
 * <p>
 * THIS IS UNPUBLISHED PROPRIETARY SOURCE CODE OF HOWBUY INVESTMENT
 * MANAGEMENT CO., LTD.  THE CONTENTS OF THIS FILE MAY NOT BE DISCLOSED
 * TO THIRD PARTIES, COPIED OR DUPLICATED IN ANY FORM, IN WHOLE OR IN PART,
 * WITHOUT THE PRIOR WRITTEN PERMISSION OF HOWBUY INVESTMENT MANAGEMENT
 * CO., LTD.
 */
package com.howbuy.crm.account.service.message.hkcustinfo;

import com.howbuy.crm.account.client.enums.custinfo.FullCustSourceEnum;
import com.howbuy.crm.account.client.response.Response;
import com.howbuy.crm.account.dao.bo.custinfo.CmConscustForAnalyseBO;
import com.howbuy.crm.account.service.business.custinfo.CmCustProcessBusinessService;
import com.howbuy.crm.account.service.message.dto.HkUpdateCustMessageDTO;
import com.howbuy.crm.account.service.vo.custinfo.AbnormalAnalyseResultVO;
import com.howbuy.crm.account.service.vo.custinfo.HkMessageCustInfoVO;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;

import java.util.Arrays;
import java.util.List;

/**
 * @description: ([香港账户]-[香港客户证件信息变更] 消息处理器[topic.hk.unRegister])
 * <AUTHOR>
 * @date 2023/12/11 17:04
 * @since JDK 1.8
 */
@Slf4j
@Service
public class HkIdChangeMessageProcessor extends AbstractHkMessageProcessor<HkUpdateCustMessageDTO> {


    /**
     * [TOPIC_HK_UPDATE_CUST]
     */
    @Value("${topic.hk.updateCust}")
    private String topicUpdateCust;

    /**
     * [ID_INFO_UPDATE]
     */
    @Value("${tag.hk.idUpdate}")
    private String tagName;

    @Autowired
    private CmCustProcessBusinessService processBusinessService;


    @Override
    public String getQuartMessageChannel() {
        return topicUpdateCust;
    }

    @Override
    public List<String> getSupportTagList() {
        return Arrays.asList(tagName);
    }



    @Override
    HkMessageCustInfoVO constructByMessage(HkUpdateCustMessageDTO acctMsgDto) {
        HkMessageCustInfoVO custVo=new HkMessageCustInfoVO();
        custVo.setHkTxAcctNo(acctMsgDto.getHkCustNo());
        fillHkAcctInfo(custVo);
        return custVo;
    }

    @Override
    public FullCustSourceEnum getCustSource() {
        return FullCustSourceEnum.HK_CUST_ID_CHANGE;
    }

    @Override
    Response<String> processHkMessage(AbnormalAnalyseResultVO<HkMessageCustInfoVO> resultVo) {
        HkMessageCustInfoVO analyseVo=resultVo.getAnalyseVo();

        CmConscustForAnalyseBO processedCustInfo=resultVo.getProcessedCustInfo();
        if(processedCustInfo==null){
            return Response.ok("香港客户证件信息变更同步，根据香港客户号：{} ，查找crm客户不存在！",null);
        }
//        更新逻辑：
//        若账户中心的【证件号】为空，则不更新CRM证件信息
//        若账户中心的【证件号】不为空，则取账户中心【客户姓名，证件类型、证件号、证件有效期】覆盖CRM的投顾客户信息
//        特别注意：更新投顾客户【客户姓名】时，优先取账户中心的【客户姓名(中文)】；若【客户姓名(中文)】为空，则取【客户姓名(英文)】
        return processBusinessService.updateIdByHk(analyseVo.getHkTxAcctNo(),processedCustInfo.getConscustno(),analyseVo.getCreator());
    }

}