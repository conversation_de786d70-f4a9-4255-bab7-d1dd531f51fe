/**
 * Copyright (c) 2025, <PERSON><PERSON><PERSON><PERSON> HOWBUY INVESTMENT MANAGEMENT Co., Ltd.
 * All right reserved.
 * <p>
 * THIS IS UNPUBLISHED PROPRIETARY SOURCE CODE OF HOWBUY INVESTMENT
 * MANAGEMENT CO., LTD.  THE CONTENTS OF THIS FILE MAY NOT BE DISCLOSED
 * TO THIRD PARTIES, COPIED OR DUPLICATED IN ANY FORM, IN WHOLE OR IN PART,
 * WITHOUT THE PRIOR WRITTEN PERMISSION OF HOWBUY INVESTMENT MANAGEMENT
 * CO., LTD.
 */
package com.howbuy.crm.account.service.facade.custinfo;

import com.howbuy.crm.account.client.facade.custinfo.HkCustInfoFacade;
import com.howbuy.crm.account.client.request.custinfo.HkAcctCustInfoRequest;
import com.howbuy.crm.account.client.response.PageVO;
import com.howbuy.crm.account.client.response.Response;
import com.howbuy.crm.account.client.response.custinfo.HkAcctCustInfoVO;
import com.howbuy.crm.account.client.response.custinfo.HkAcctCustSensitiveInfoVO;
import com.howbuy.crm.account.service.service.custinfo.HkCustInfoService;
import lombok.extern.slf4j.Slf4j;
import org.apache.dubbo.config.annotation.DubboService;

import javax.annotation.Resource;

/**
 * <AUTHOR>
 * @description: (请在此添加描述)
 * @date 2025/6/25 10:32
 * @since JDK 1.8
 */
@Slf4j
@DubboService
public class HkCustInfoFacadeImpl implements HkCustInfoFacade {


    @Resource
    private HkCustInfoService hkCustInfoService;

    @Override
    public Response<PageVO<HkAcctCustInfoVO>> queryHkCustInfoPage(HkAcctCustInfoRequest hkCustInfoRequest) {
        return Response.ok(hkCustInfoService.queryHkCustInfoPage(hkCustInfoRequest));
    }

    @Override
    public Response<HkAcctCustSensitiveInfoVO> queryHkCustSensitiveInfo(String hkTxAcctNo) {
        return Response.ok(hkCustInfoService.queryHkCustSensitiveInfo(hkTxAcctNo));
    }

}