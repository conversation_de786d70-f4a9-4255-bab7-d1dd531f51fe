/**
 * Copyright (c) 2023, <PERSON><PERSON><PERSON><PERSON> HOWBUY INVESTMENT MANAGEMENT Co., Ltd.
 * All right reserved.
 * <p>
 * THIS IS UNPUBLISHED PROPRIETARY SOURCE CODE OF HOWBUY INVESTMENT
 * MANAGEMENT CO., LTD.  THE CONTENTS OF THIS FILE MAY NOT BE DISCLOSED
 * TO THIRD PARTIES, COPIED OR DUPLICATED IN ANY FORM, IN WHOLE OR IN PART,
 * WITHOUT THE PRIOR WRITTEN PERMISSION OF HOWBUY INVESTMENT MANAGEMENT
 * CO., LTD.
 */
package com.howbuy.crm.account.service.repository.custinfo;

import com.github.pagehelper.Page;
import com.github.pagehelper.PageHelper;
import com.howbuy.common.utils.StringUtil;
import com.howbuy.crm.account.client.enums.custinfo.CustBindRelationTypeEnum;
import com.howbuy.crm.account.client.response.Response;
import com.howbuy.crm.account.dao.bo.custinfo.HkUpdateEbrokerIdBO;
import com.howbuy.crm.account.dao.mapper.CommonMapper;
import com.howbuy.crm.account.dao.mapper.custinfo.CmHboneHkRelationLogMapper;
import com.howbuy.crm.account.dao.mapper.custinfo.CmHkConscustMapper;
import com.howbuy.crm.account.dao.po.custinfo.CmHboneHkRelationLogPO;
import com.howbuy.crm.account.dao.po.custinfo.CmHkConscustPO;
import com.howbuy.crm.account.dao.req.PageReqVO;
import com.howbuy.crm.account.dao.req.custinfo.CmHkCustReqVO;
import com.howbuy.crm.account.service.commom.constant.Constants;
import com.howbuy.crm.account.service.commom.constant.SequenceConstants;
import com.howbuy.crm.account.service.req.custinfo.HkCustCreateOptReqVO;
import com.howbuy.crm.account.service.req.custinfo.HkCustRelationOptReqVO;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import org.springframework.transaction.annotation.Propagation;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.Assert;

import java.math.BigDecimal;
import java.util.Date;
import java.util.List;

/**
 * @description: (投顾客户香港侧   repository)
 * <AUTHOR>
 * @date 2023/12/8 15:31
 * @since JDK 1.8
 */
@Component
@Slf4j
@Transactional(rollbackFor = Exception.class, propagation = Propagation.SUPPORTS)
public class CmHkCustInfoRepository {

    @Autowired
    private CmHkConscustMapper cmHkConscustMapper;

    @Autowired
    private CmHboneHkRelationLogMapper hkHboneRelationLogMapper;

    @Autowired
    private CommonMapper commonMapper;



    /**
     * @description:(插入 香港账户vs 投顾客户 绑定关系)
     * @param bindVo
     * @return com.howbuy.crm.account.client.response.Response<java.lang.String>
     * @author: haoran.zhang
     * @date: 2023/12/15 11:26
     * @since JDK 1.8
     */
    @Transactional(rollbackFor = Exception.class, propagation = Propagation.REQUIRED)
    public Response<String> executeBind(HkCustRelationOptReqVO bindVo) {
        String custNo=bindVo.getCustNo();
        String hkTxAcctNo=bindVo.getHkTxAcctNo();
        String operator=bindVo.getOperator();


        Assert.notNull(hkTxAcctNo,"香港交易账号不能为空");
        Assert.notNull(custNo,"投顾客户号不能为空");
        Assert.notNull(operator,"操作人不能为空");


        CmHkConscustPO insertPo=new CmHkConscustPO();
        insertPo.setId(new BigDecimal(commonMapper.getSeqValue(SequenceConstants.SEQ_CM_HK_CONSCUST)));
        insertPo.setHkcustid(bindVo.getEbrokerId());
        insertPo.setConscustno(custNo);
        insertPo.setHkTxAcctNo(hkTxAcctNo);
        insertPo.setCreator(operator);
        insertPo.setCredt(new Date());
        cmHkConscustMapper.insert(insertPo);

        // 账户关联关系
        // 账户关联关系
        CmHboneHkRelationLogPO relationLog=prepareHkRelationLogPo(Constants.CUST_RELATION_OPER_TYPE_BIND);
        relationLog.setCustNo(custNo);
        relationLog.setAcctNo(hkTxAcctNo);
        relationLog.setCreator(operator);
        //请求来源  非必须项
        relationLog.setOperateSource(bindVo.getOperateSource());
        relationLog.setOperateChannel(bindVo.getOperateChannel());
        relationLog.setRemark(bindVo.getRemark());
        hkHboneRelationLogMapper.insert( relationLog);

        return  Response.ok();
    }


    /**
     * @description: 插入香港客户记录
     * @param createVo 插入入参
     * @return com.howbuy.crm.account.client.response.Response<java.lang.String> 插入结果
     * @author: jin.wang03
     * @date: 2023/12/18 14:08
     * @since JDK 1.8
     */
    @Transactional(rollbackFor = Exception.class, propagation = Propagation.REQUIRED)
    public Response<String> insert(HkCustCreateOptReqVO createVo) {
        CmHkConscustPO insertPo = new CmHkConscustPO();
        insertPo.setId(new BigDecimal(commonMapper.getSeqValue(SequenceConstants.SEQ_CM_HK_CONSCUST)));
        insertPo.setHkcustid(createVo.getEbrokerId());
        insertPo.setConscustno(createVo.getCustNo());
        insertPo.setHkTxAcctNo(createVo.getHkTxAcctNo());
        insertPo.setCreator(createVo.getOperator());
        insertPo.setCredt(new Date());
        cmHkConscustMapper.insert(insertPo);
        return Response.ok();
    }


    /**
     * @description:(准备通用的  relation对象 用于记录日志)
     * @param operateType
     * @return com.howbuy.crm.account.dao.po.custinfo.CmHboneHkRelationLogPO
     * @author: haoran.zhang
     * @date: 2023/12/15 14:41
     * @since JDK 1.8
     */
    private CmHboneHkRelationLogPO prepareHkRelationLogPo(String  operateType){
        CmHboneHkRelationLogPO relationLog=new CmHboneHkRelationLogPO();
        relationLog.setId(commonMapper.getSeqValue(SequenceConstants.SEQ_CM_CUST_RELATION_LOG));
        relationLog.setRelationType(CustBindRelationTypeEnum.HK.getCode());
        relationLog.setOperateType(operateType);
        relationLog.setCreateTimestamp(new Date());
        return relationLog;
    }



    /**
     * @description:(注销香港交易账户)
     * @param unBindOptVo 香港交易账户解除绑定请求
     * @return com.howbuy.crm.base.response.CoreReturnMessageDto<java.lang.String>
     * @author: haoran.zhang
     * @date: 2023/6/19 20:46
     * @since JDK 1.8
     */
    @Transactional(rollbackFor = Exception.class, propagation = Propagation.REQUIRED)
    public Response<String> executeUnBind(HkCustRelationOptReqVO unBindOptVo){
        String custNo=unBindOptVo.getCustNo();
        String hkTxAcctNo=unBindOptVo.getHkTxAcctNo();
        Assert.notNull(hkTxAcctNo,"香港交易账号不能为空");
        Assert.notNull(custNo,"投顾客户号不能为空");
        Assert.notNull(unBindOptVo.getOperator(),"操作人不能为空");

        CmHkCustReqVO custReqVO=new CmHkCustReqVO();
        custReqVO.setConscustno(custNo);
        custReqVO.setHkTxAcctNo(hkTxAcctNo);
        CmHkConscustPO  existAcct=selectByReqVO(custReqVO);
        if(existAcct==null){
            return  Response.fail(String.format("custNo:%s, hkTxAcctNo:%s 不存在绑定关系！",
                    custNo,hkTxAcctNo));
        }
        //备份
        cmHkConscustMapper.backUpHkAcctById(existAcct.getId());
        //删除
        int deleteCount=cmHkConscustMapper.deleteByPrimaryKey(existAcct.getId());
        if(deleteCount==1){
            // 账户关联关系
            CmHboneHkRelationLogPO relationLog=prepareHkRelationLogPo(Constants.CUST_RELATION_OPER_TYPE_UNBIND);
            relationLog.setCustNo(custNo);
            relationLog.setAcctNo(hkTxAcctNo);
            relationLog.setCreator(unBindOptVo.getOperator());
            //请求来源  非必须项
            relationLog.setOperateSource(unBindOptVo.getOperateSource());
            relationLog.setOperateChannel(unBindOptVo.getOperateChannel());
            relationLog.setRemark(unBindOptVo.getRemark());
            hkHboneRelationLogMapper.insert( relationLog);
        }
        return  Response.ok();
    }
    /**
     * @description:(根据根据  reqVO 查询 香港交易账户)
     * @param reqVO
     * @return com.howbuy.crm.account.dao.po.custinfo.CmHkConscustPO
     * @date: 2023/12/11 17:51
     * @since JDK 1.8
     */
    public CmHkConscustPO selectByReqVO(CmHkCustReqVO reqVO){
        //校验属性  至少有一个不为null
        Assert.isTrue(StringUtil.isNotBlank(reqVO.getConscustno())
                          || StringUtil.isNotBlank(reqVO.getHkcustid())
                          || StringUtil.isNotBlank(reqVO.getHkTxAcctNo())
                          || reqVO.getId()!=null,
                "参数不能全部为空！");

        return cmHkConscustMapper.selectByReqVO(reqVO);
    }

    /**
     * @description:(根据根据  投顾客户号 查询 香港交易账户)
     * @param custNo
     * @return com.howbuy.crm.account.dao.po.custinfo.CmHkConscustPO
     * @author: haoran.zhang
     * @date: 2023/12/15 16:08
     * @since JDK 1.8
     */
    public CmHkConscustPO selectByCustNo(String custNo){
        return cmHkConscustMapper.selectByCustNo(custNo);
    }

    /**
     * @description:(根据根据  香港客户号 查询 香港客户绑定关系)
     * @param hkTxAcctNo
     * @return com.howbuy.crm.account.dao.po.custinfo.CmHkConscustPO
     * @author: haoran.zhang
     * @date: 2024/1/17 13:34
     * @since JDK 1.8
     */
    public CmHkConscustPO selectByHkTxAcctNo(String hkTxAcctNo){
        return cmHkConscustMapper.selectByHkTxAcctNo(hkTxAcctNo);
    }

    /**
     * @description:(根据 eBrokerId 查询香港交易账户)
     * @param eBrokerId eBrokerId
     * @return com.howbuy.crm.account.dao.po.custinfo.CmHkConscustPO 香港交易账户信息
     * @author: haoran.zhang
     * @date: 2023/12/15 16:08
     * @since JDK 1.8
     */
    public CmHkConscustPO selectByEbrokerId(String eBrokerId) {
        return cmHkConscustMapper.selectByEbrokerId(eBrokerId);
    }

    /**
     * @description: 查询 CM_HK_CONSCUST 总数
     * @param
     * @return long
     * @author: jin.wang03
     * @date: 2024/6/4 14:37
     * @since JDK 1.8
     */
    public long count() {
       return cmHkConscustMapper.count();
    }

    /**
     * @description: 分页查询
     * @param vo 查询条件
     * @return com.github.pagehelper.Page<com.howbuy.crm.account.dao.po.custinfo.CmHkConscustPO>
     * @author: jin.wang03
     * @date: 2024/6/4 14:38
     * @since JDK 1.8
     */
    public Page<CmHkConscustPO> selectPageByVo(PageReqVO vo) {
        PageHelper.startPage(vo.getPage(), vo.getRows());
        return cmHkConscustMapper.selectPageByVo(vo);
    }

    /**
     * @description: 批量更新：更新指定hkCustNo的ebrokerId
     * @param list
     * @return void
     * @author: jin.wang03
     * @date: 2024/6/4 14:39
     * @since JDK 1.8
     */
    public void batchUpdateEbrokerIdByHkCustNo(List<HkUpdateEbrokerIdBO> list) {
        cmHkConscustMapper.batchUpdateEbrokerIdByHkCustNo(list);
    }
}