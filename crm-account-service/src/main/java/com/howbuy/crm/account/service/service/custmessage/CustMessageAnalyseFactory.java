/**
 * Copyright (c) 2023, <PERSON><PERSON><PERSON><PERSON> HOWBUY INVESTMENT MANAGEMENT Co., Ltd.
 * All right reserved.
 * <p>
 * THIS IS UNPUBLISHED PROPRIETARY SOURCE CODE OF HOWBUY INVESTMENT
 * MANAGEMENT CO., LTD.  THE CONTENTS OF THIS FILE MAY NOT BE DISCLOSED
 * TO THIRD PARTIES, COPIED OR DUPLICATED IN ANY FORM, IN WHOLE OR IN PART,
 * WITHOUT THE PRIOR WRITTEN PERMISSION OF HOWBUY INVESTMENT MANAGEMENT
 * CO., LTD.
 */
package com.howbuy.crm.account.service.service.custmessage;

import com.alibaba.fastjson.JSON;
import com.google.common.collect.Maps;
import com.howbuy.crm.account.client.enums.YesOrNoEnum;
import com.howbuy.crm.account.client.enums.custinfo.FullCustSourceEnum;
import com.howbuy.crm.account.service.vo.custinfo.AbnormalAnalyseResultVO;
import com.howbuy.crm.account.service.vo.custinfo.MessageCustInfoVO;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.BeansException;
import org.springframework.context.ApplicationContext;
import org.springframework.context.ApplicationContextAware;
import org.springframework.stereotype.Service;
import org.springframework.util.Assert;

import javax.annotation.PostConstruct;
import java.util.List;
import java.util.Map;

/**
 * @description: (消息处理 factory)
 * <AUTHOR>
 * @date 2023/12/13 21:08
 * @since JDK 1.8
 */
@Service
@Slf4j
public class CustMessageAnalyseFactory implements ApplicationContextAware {


    /**
     * 消息构建 服务类 MessageBuildService的所有实现
     */
    private static final Map<FullCustSourceEnum, CustMessageAnalyseService<? extends MessageCustInfoVO>> map = Maps.newConcurrentMap();
    /**
     * spring 上下文
     */
    private static ApplicationContext appCtx;


    /**
     * @description:(香港账户 异常分析 入口)
     * @param messageVo
     * @return com.howbuy.crm.account.service.vo.custinfo.HKAbnormalAnalyseResultVO
     * @author: haoran.zhang
     * @date: 2023/12/15 17:47
     * @since JDK 1.8
     */
    public <T extends MessageCustInfoVO> AbnormalAnalyseResultVO<T> analyze(T messageVo){
        FullCustSourceEnum sourceEnum= FullCustSourceEnum.getEnum(messageVo.getAbnormalSource());
        Assert.notNull(sourceEnum,String.format("不支持的客户消息来源：%s",messageVo.getAbnormalSource()));
        //操作通道 1-MQ  2-菜单页面
        Assert.notNull(messageVo.getOperateChannel(),"操作通道不能为空！");


        //是否需要 异常分析。
        AbnormalAnalyseResultVO<T> resultVo;
        if(sourceEnum.isNeedAnalyse()){
            //分析 是否异常
            resultVo=getAnalyseService(sourceEnum).analyze(messageVo);
        }else{
            //不需要做分析校验
            resultVo=new AbnormalAnalyseResultVO<T>();
            resultVo.setAnalyseVo(messageVo);
            resultVo.setAbnormalEnum(YesOrNoEnum.NO);
        }
        log.info("消息source:{},分析结果：{}", sourceEnum.getDescription(), JSON.toJSONString(resultVo));
        return  resultVo;
    }



    /**
     * @description:(获取 anyase 分析的 处理类)
     * @param sourceEnum
     * @return com.howbuy.crm.account.service.service.custmessage.HkCustMessageAnalyseService
     * @author: haoran.zhang
     * @date: 2023/12/15 17:46
     * @since JDK 1.8
     */
     CustMessageAnalyseService getAnalyseService(FullCustSourceEnum sourceEnum) {
          return map.get(sourceEnum);
    }


    @Override
    public void setApplicationContext(ApplicationContext applicationContext) throws BeansException {
        appCtx = applicationContext;
    }

    @PostConstruct
    public void initMap() {
        Map<String, CustMessageAnalyseService> beansOfType = appCtx.getBeansOfType(CustMessageAnalyseService.class);
        for (CustMessageAnalyseService messageDealService : beansOfType.values()) {
            List<FullCustSourceEnum> supportTypeList = messageDealService.getSourceList();
            for (FullCustSourceEnum classEnum : supportTypeList) {
                map.put(classEnum, messageDealService);
            }
        }
    }
}