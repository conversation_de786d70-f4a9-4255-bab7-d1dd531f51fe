/**
 * Copyright (c) 2024, <PERSON><PERSON><PERSON><PERSON> HOWBUY INVESTMENT MANAGEMENT Co., Ltd.
 * All right reserved.
 * <p>
 * THIS IS UNPUBLISHED PROPRIETARY SOURCE CODE OF HOWBUY INVESTMENT
 * MANAGEMENT CO., LTD.  THE CONTENTS OF THIS FILE MAY NOT BE DISCLOSED
 * TO THIRD PARTIES, COPIED OR DUPLICATED IN ANY FORM, IN WHOLE OR IN PART,
 * WITHOUT THE PRIOR WRITTEN PERMISSION OF HOWBUY INVESTMENT MANAGEMENT
 * CO., LTD.
 */
package com.howbuy.crm.account.service.facade.consultant;

import com.howbuy.crm.account.client.enums.ExceptionCodeEnum;
import com.howbuy.crm.account.client.facade.consultant.UpdateConsPerformanceCoeffFacade;
import com.howbuy.crm.account.client.request.consultant.MergeConsPerformanceCoeffRequest;
import com.howbuy.crm.account.client.response.Response;
import com.howbuy.crm.account.service.service.consultant.MergeConsPerformanceCoeffService;
import lombok.extern.slf4j.Slf4j;
import org.apache.dubbo.config.annotation.DubboService;

import javax.annotation.Resource;

/**
 * @description: 写入人员绩效系数表接口实现（修改客户）
 * <AUTHOR>
 * @date 2025-06-27 17:05:00
 * @since JDK 1.8
 */
@Slf4j
@DubboService
public class UpdateConsPerformanceCoeffFacadeImpl implements UpdateConsPerformanceCoeffFacade {
    /**
     * 计算类型：修改
     */
    private static final String CALC_TYPE_MODIFY = "2";


    @Resource
    private MergeConsPerformanceCoeffService mergeConsPerformanceCoeffService;

    /**
     * @param request 写入人员绩效系数表请求参数（修改客户）
     * @return com.howbuy.crm.account.client.response.Response<String>
     * @description: 写入人员绩效系数表
     * <AUTHOR>
     * @date 2025-06-27 17:05:00
     * @since JDK 1.8
     */
    @Override
    public Response<String> execute(MergeConsPerformanceCoeffRequest request) {
        log.info("写入人员绩效系数表，入参：{}", request);
        try {
            mergeConsPerformanceCoeffService.initConsCodeAndCustType(request);
            request.setCalcType(CALC_TYPE_MODIFY);
            mergeConsPerformanceCoeffService.execute(request);
            log.info("写入人员绩效系数表成功");
            return Response.ok(ExceptionCodeEnum.SUCCESS.getDesc());
        } catch (Exception e) {
            log.error("写入人员绩效系数表异常", e);
            return Response.fail("写入人员绩效系数表失败：" + e.getMessage());
        }
    }
} 