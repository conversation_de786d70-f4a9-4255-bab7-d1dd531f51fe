/**
 * Copyright (c) 2025, <PERSON><PERSON><PERSON><PERSON> HOWBUY INVESTMENT MANAGEMENT Co., Ltd.
 * All right reserved.
 * <p>
 * THIS IS UNPUBLISHED PROPRIETARY SOURCE CODE OF HOWBUY INVESTMENT
 * MANAGEMENT CO., LTD.  THE CONTENTS OF THIS FILE MAY NOT BE DISCLOSED
 * TO THIRD PARTIES, COPIED OR DUPLICATED IN ANY FORM, IN WHOLE OR IN PART,
 * WITHOUT THE PRIOR WRITTEN PERMISSION OF HOWBUY INVESTMENT MANAGEMENT
 * CO., LTD.
 */
package com.howbuy.crm.account.service.repository.consultantexp;

import com.howbuy.crm.account.dao.mapper.consultantexp.CmConsultantExpMapper;
import com.howbuy.crm.account.dao.po.consultantexp.CmConsultantExpPO;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.List;

/**
 * @description: 花名册Repository
 * <AUTHOR>
 * @date 2025/4/8 15:28
 * @since JDK 1.8
 */
@Slf4j
@Component
public class CmConsultantExpRepository {

    @Autowired
    private CmConsultantExpMapper cmConsultantExpMapper;

    /**
     * @description: (通过consCode获得花名册数据)
     * @param consCode 投顾编码
     * @return com.howbuy.crm.account.dao.po.consultantexp.CmConsultantExpPO
     * @author: jin.wang03
     * @date: 2025/4/8 15:43
     * @since JDK 1.8
     */
    public CmConsultantExpPO getCmConsultantExpByConsCode(String consCode) {
        return cmConsultantExpMapper.getCmConsultantExpByConsCode(consCode);
    }

    /**
     * @description: 根据指定orgCode和 指定层级userLevel获取所有leaders
     * @param orgcode
     * @param userlevel
     * @return java.util.List<java.lang.String>
     * @author: jin.wang03
     * @date: 2025/4/14 13:29
     * @since JDK 1.8
     */
    public List<String> listLeadersByOrgCodeAndLevel(String orgcode, String userlevel) {
        return cmConsultantExpMapper.listLeadersByOrgCodeAndLevel(orgcode, userlevel);
    }

    /**
     * @description: 获取指定orgCode的 区域执行副总
     * @param orgCode
     * @return java.util.List<java.lang.String>
     * @author: jin.wang03
     * @date: 2025/4/14 13:29
     * @since JDK 1.8
     */
    public List<String> listFLeadersByOrgCode(String orgCode) {
        return cmConsultantExpMapper.listFLeadersByOrgCode(orgCode);
    }
}