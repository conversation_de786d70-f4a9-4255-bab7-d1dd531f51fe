/**
 * Copyright (c) 2023, <PERSON><PERSON><PERSON><PERSON> HOWBUY INVESTMENT MANAGEMENT Co., Ltd.
 * All right reserved.
 * <p>
 * THIS IS UNPUBLISHED PROPRIETARY SOURCE CODE OF HOWBUY INVESTMENT
 * MANAGEMENT CO., LTD.  THE CONTENTS OF THIS FILE MAY NOT BE DISCLOSED
 * TO THIRD PARTIES, COPIED OR DUPLICATED IN ANY FORM, IN WHOLE OR IN PART,
 * WITHOUT THE PRIOR WRITTEN PERMISSION OF HOWBUY INVESTMENT MANAGEMENT
 * CO., LTD.
 */
package com.howbuy.crm.account.service.message.hkcustinfo;

import com.howbuy.crm.account.client.enums.custinfo.CustSourceCodeEnum;
import com.howbuy.crm.account.client.enums.custinfo.FullCustSourceEnum;
import com.howbuy.crm.account.client.response.Response;
import com.howbuy.crm.account.service.business.custinfo.HkOpenAcctMessageBuss;
import com.howbuy.crm.account.service.message.dto.HkOpenAcctMessageDTO;
import com.howbuy.crm.account.service.vo.custinfo.AbnormalAnalyseResultVO;
import com.howbuy.crm.account.service.vo.custinfo.HkMessageCustInfoVO;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;

import java.util.Collections;
import java.util.List;

/**
 * <AUTHOR>
 * @description: ([香港账户] - [香港线上开户] 消息处理器[topic.hk.openAcct])
 * @date 2023/12/11 17:04
 * @since JDK 1.8
 */
@Slf4j
@Service
public class HkOnlineOpenAcctMessageProcessor extends AbstractHkMessageProcessor<HkOpenAcctMessageDTO> {

    /**
     * [TOPIC_HK_OPEN_ACCT]  香港线上开户
     */
    @Value("${topic.hk.openAcct}")
    private String topicHkOpenAcct;


    /**
     * [ONLINE_OPEN_ACCT]
     */
    @Value("${tag.hk.onlineOpenAcct}")
    private String tagName;


    @Autowired
    private HkOpenAcctMessageBuss hkOpenAcctMessageBuss;


    @Override
    public String getQuartMessageChannel() {
        return topicHkOpenAcct;
    }

    @Override
    public List<String> getSupportTagList() {
        return Collections.singletonList(tagName);
    }

    /**
     * @param acctMsgDto
     * @return com.howbuy.crm.account.service.vo.custinfo.HkMessageCustInfoVO
     * @description: (根据消息体构造分析对象 V3)
     * <AUTHOR>
     * @date 2025-04-17 08:49:30
     * @since JDK 1.8
     */
    @Override
    HkMessageCustInfoVO constructByMessage(HkOpenAcctMessageDTO acctMsgDto) {
        HkMessageCustInfoVO custVo = new HkMessageCustInfoVO();
        custVo.setHkTxAcctNo(acctMsgDto.getHkCustNo());
        custVo.setOpenOutletCode(acctMsgDto.getOpenOutletCode());

        fillHkAcctInfo(custVo);
        return custVo;
    }

    @Override
    public FullCustSourceEnum getCustSource() {
        return FullCustSourceEnum.HK_ONLINE_OPEN_ACCOUNT;
    }


    /**
     * @param resultVo V3分析服务的正常返回结果
     * @return com.howbuy.crm.account.client.response.Response<java.lang.String>
     * @description: (处理V3分析服务返回的正常结果 ， 执行业务逻辑)
     * <AUTHOR>
     * @date 2025-04-17 08:49:30
     * @since JDK 1.8
     */
    @Override
    public Response<String> processHkMessage(AbnormalAnalyseResultVO<HkMessageCustInfoVO> resultVo) {
        return hkOpenAcctMessageBuss.processHkMessage(resultVo, CustSourceCodeEnum.HOWBUY_HK_SELF_OPEN);
    }

}