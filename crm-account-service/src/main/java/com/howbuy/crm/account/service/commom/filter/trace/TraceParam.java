/**
 * Copyright (c) 2023, ShangHai HOWBUY INVESTMENT MANAGEMENT Co., Ltd.
 * All right reserved.
 * <p>
 * THIS IS UNPUBLISHED PROPRIETARY SOURCE CODE OF HOWBUY INVESTMENT
 * MANAGEMENT CO., LTD.  THE CONTENTS OF THIS FILE MAY NOT BE DISCLOSED
 * TO THIRD PARTIES, COPIED OR DUPLICATED IN ANY FORM, IN WHOLE OR IN PART,
 * WITHOUT THE PRIOR WRITTEN PERMISSION OF HOWBUY INVESTMENT MANAGEMENT
 * CO., LTD.
 */
package com.howbuy.crm.account.service.commom.filter.trace;

import lombok.Data;

import java.util.UUID;

/**
 * @description: (请在此添加描述)
 * <AUTHOR>
 * @date 2023/6/7 08:52
 * @since JDK 1.8
 */
@Data
public class TraceParam {
    /**
     * tokenId：app初装时生成
     */
    private String tokenId;
    /**
     * 唤醒Id：每次后台唤醒到前台时生成
     */
    private String awakeId;
    /**
     * 功能Id：点击重要功能时生成（e.g.购买、卖出）
     */
    private String funcId;
    /**
     * 页面Id：每个页面加载时生成
     */
    private String pageId;
    /**
     * 请求Id：每个请求发送时生成
     */
    private String reqId;

    public TraceParam build(){
        String uuid = UUID.randomUUID().toString().replace("-", "");
        this.awakeId = uuid;
        this.funcId = uuid;
        this.pageId = uuid;
        this.reqId = uuid;
        return this;
    }
}