/**
 * Copyright (c) 2024, <PERSON><PERSON><PERSON><PERSON> HOWBUY INVESTMENT MANAGEMENT Co., Ltd.
 * All right reserved.
 * <p>
 * THIS IS UNPUBLISHED PROPRIETARY SOURCE CODE OF HOWBUY INVESTMENT
 * MANAGEMENT CO., LTD.  THE CONTENTS OF THIS FILE MAY NOT BE DISCLOSED
 * TO THIRD PARTIES, COPIED OR DUPLICATED IN ANY FORM, IN WHOLE OR IN PART,
 * WITHOUT THE PRIOR WRITTEN PERMISSION OF HOWBUY INVESTMENT MANAGEMENT
 * CO., LTD.
 */
package com.howbuy.crm.account.service.commom.constant;

/**
 * @description: (请在此添加描述)
 * <AUTHOR>
 * @date 2024/5/29 17:48
 * @since JDK 1.8
 */
public class DobboReferenceConstant {

    private DobboReferenceConstant(){}

    /**
     * 账户中心
     */
    public static final String ACC_CENTER_SERVER = "acc-center-server";


    /**
     * 香港账户中心
     */
    public static final String HK_ACC_CENTER_SERVER="hk-acc-online-service";

    /**
     * crm-td-server
     */
    public static final String CRM_TD_SERVER = "crm-td-server";

    /**
     * 大数据中心
     */
    public static final String CENTER_MEMBER_SERVICE = "center-member-service";

}