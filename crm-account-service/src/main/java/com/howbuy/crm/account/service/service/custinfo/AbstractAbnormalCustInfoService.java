/**
 * Copyright (c) 2023, <PERSON><PERSON><PERSON><PERSON> HOWBUY INVESTMENT MANAGEMENT Co., Ltd.
 * All right reserved.
 * <p>
 * THIS IS UNPUBLISHED PROPRIETARY SOURCE CODE OF HOWBUY INVESTMENT
 * MANAGEMENT CO., LTD.  THE CONTENTS OF THIS FILE MAY NOT BE DISCLOSED
 * TO THIRD PARTIES, COPIED OR DUPLICATED IN ANY FORM, IN WHOLE OR IN PART,
 * WITHOUT THE PRIOR WRITTEN PERMISSION OF HOWBUY INVESTMENT MANAGEMENT
 * CO., LTD.
 */
package com.howbuy.crm.account.service.service.custinfo;

import com.github.pagehelper.util.StringUtil;
import com.google.common.collect.Lists;
import com.howbuy.common.utils.DateUtil;
import com.howbuy.crm.account.client.enums.DealStatusEnum;
import com.howbuy.crm.account.client.enums.YesOrNoEnum;
import com.howbuy.crm.account.client.enums.custinfo.HkAcctCustStatusEnum;
import com.howbuy.crm.account.client.request.custinfo.AbnormalCustInfoRequest;
import com.howbuy.crm.account.client.request.custinfo.BatchDealAbnormalRequest;
import com.howbuy.crm.account.client.request.custinfo.DealAbnormalRequest;
import com.howbuy.crm.account.client.response.PageVO;
import com.howbuy.crm.account.client.response.Response;
import com.howbuy.crm.account.client.response.custinfo.CustKeyAttrInfo;
import com.howbuy.crm.account.client.response.custinfo.HboneAcctCustInfoVO;
import com.howbuy.crm.account.client.response.custinfo.HboneTxAcctInfoVO;
import com.howbuy.crm.account.client.response.custinfo.HkAcctCustInfoVO;
import com.howbuy.crm.account.client.utils.IdTypeUtil;
import com.howbuy.crm.account.dao.req.custinfo.AbnormalCustReqVO;
import com.howbuy.crm.account.service.commom.constant.Constants;
import com.howbuy.crm.account.service.commom.utils.AbnormalCustUtils;
import com.howbuy.crm.account.service.commom.utils.ParamFormatUtil;
import com.howbuy.crm.account.service.outerservice.acct.HboneAcctInfoOuterService;
import com.howbuy.crm.account.service.outerservice.hkacct.HkCustBasicInfoOuterService;
import com.howbuy.crm.account.service.req.custinfo.HkAcctCustInfoReqVO;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * @description: (香港/一账通 异常客户  处理异常信息 信息service)
 * <AUTHOR>
 * @date 2023/12/13 16:41
 * @since JDK 1.8
 */
@Service
@Slf4j
public  abstract class AbstractAbnormalCustInfoService {


    @Autowired
    private HkCustBasicInfoOuterService hkCustInfoOuterService;

    @Autowired
    private HboneAcctInfoOuterService hboneCustInfoOuterService;


    /**
     * 验证  异常处理  [子类特殊校验] 操作
     *
     * @param request
     * @return
     */
   abstract Response<String> validateSpecificDeal(DealAbnormalRequest request);

   /**
    * @description:(更新 异常的状态 )
    * @param request
    * @return com.howbuy.crm.account.client.response.Response<java.lang.String>
    * @author: haoran.zhang
    * @date: 2024/1/18 17:56
    * @since JDK 1.8
    */
   abstract Response<String>  updateDealStatus(DealAbnormalRequest request);

   /**
    * @description:(批量更新 异常的状态 )
    * @param batchRequest
    * @return com.howbuy.crm.account.client.response.Response<java.lang.String>
    * @author: haoran.zhang
    * @date: 2024/1/18 18:00
    * @since JDK 1.8
    */
   abstract Response<String> batchUpdateDealStatus(BatchDealAbnormalRequest batchRequest);





   private Response<String> validateDealAbnormal(DealAbnormalRequest request){
       DealStatusEnum dealStatusEnum=DealStatusEnum.getEnum(request.getDealStatus());
       if (dealStatusEnum == null) {
           return Response.fail("处理状态不能为空!");
       }
       if (DealStatusEnum.UN_DEAL==dealStatusEnum) {
           return Response.fail("参数处理状态非法!");
       }
       //子类 特殊校验逻辑操作
       return validateSpecificDeal(request);
   }

    /**
     * @description:(异常信息处理)
     * @param request
     * @return com.howbuy.crm.account.client.response.Response<java.lang.String>
     * @author: haoran.zhang
     * @date: 2024/1/18 17:48
     * @since JDK 1.8
     */
    public Response<String> dealAbnormal(DealAbnormalRequest request){
        Response<String>  validateResp= validateDealAbnormal(request);
        //校验不通过
        if(!validateResp.isSuccess()){
            return validateResp;
        }

        //更新操作
        return updateDealStatus(request);
    }


    /**
     * @description:(客户异常信息  批量处理)
     * @param request
     * @return com.howbuy.crm.account.client.response.Response<java.lang.String>
     * @author: haoran.zhang
     * @date: 2024/1/18 10:28
     * @since JDK 1.8
     */
    public Response<String> batchDealAbnormal(BatchDealAbnormalRequest request){
        List<String> idList=request.getIdList();
        if(CollectionUtils.isEmpty(idList)){
            return Response.fail("异常id列表不能为空！");
        }
        //校验操作
        for(String id :idList){
            DealAbnormalRequest singleReq = new DealAbnormalRequest();
            singleReq.setId(id);
            singleReq.setOperator(request.getOperator());
            singleReq.setRemark(request.getRemark());
            singleReq.setDealStatus(request.getDealStatus());
            Response<String>  validateResp= validateDealAbnormal(singleReq);
            //校验不通过
            if(!validateResp.isSuccess()){
                return validateResp;
            }
        }
        return batchUpdateDealStatus(request);
    }


    /**
     * @description:(根据 香港客户号 查询 香港侧 客户关键信息)
     * @param hkTxAcctNo
     * @return com.howbuy.crm.account.client.response.custinfo.CustKeyAttrInfo
     * @author: haoran.zhang
     * @date: 2024/1/17 14:51
     * @since JDK 1.8
     */
    public CustKeyAttrInfo getHkSideInfo(String hkTxAcctNo){
        if(StringUtil.isEmpty(hkTxAcctNo)){
            return null;
        }
        HkAcctCustInfoReqVO reqVo=new HkAcctCustInfoReqVO();
        reqVo.setHkCustNo(hkTxAcctNo);
        PageVO<HkAcctCustInfoVO>  pageResp=hkCustInfoOuterService.queryHkCustInfoPage( reqVo);
        if(pageResp==null|| CollectionUtils.isEmpty(pageResp.getRows())){
            return null;
        }
        HkAcctCustInfoVO hkCustInfo=pageResp.getRows().get(0);

        //需要 已注销客户展示。此处调用 page接口。 性能慢
//        HkAcctCustDetailInfoVO  hkCustInfo =hkCustInfoOuterService.queryHkCustDetailInfo(hkTxAcctNo);
        if(hkCustInfo==null){
            return null;
        }
        boolean isRealName= StringUtil.isNotEmpty(hkCustInfo.getIdNoDigest());

        CustKeyAttrInfo custKeyAttrInfo = new CustKeyAttrInfo();
        custKeyAttrInfo.setHkTxAcctNo(hkTxAcctNo);
        custKeyAttrInfo.setHboneNo(hkCustInfo.getHboneNo());

        String usedCustName = StringUtil.isEmpty(hkCustInfo.getCustChineseName()) ? hkCustInfo.getCustEnName() : hkCustInfo.getCustChineseName();
        custKeyAttrInfo.setCustName(usedCustName);
        custKeyAttrInfo.setInvestType(hkCustInfo.getInvstType());
        custKeyAttrInfo.setCustStatus(hkCustInfo.getHkCustStatus());
        custKeyAttrInfo.setCustStatusDesc(HkAcctCustStatusEnum.getDescription(hkCustInfo.getHkCustStatus()));
        custKeyAttrInfo.setIsRealName(isRealName? YesOrNoEnum.YES.getCode():YesOrNoEnum.NO.getCode());

        custKeyAttrInfo.setIdNoDigest(hkCustInfo.getIdNoDigest());
        custKeyAttrInfo.setIdSignAreaCode(hkCustInfo.getIdSignAreaCode());
        custKeyAttrInfo.setIdType(hkCustInfo.getIdType());
        custKeyAttrInfo.setIdNoMask(hkCustInfo.getIdNoMask());
        custKeyAttrInfo.setIdTypeDesc(IdTypeUtil.getIdTypeDesc(hkCustInfo.getInvstType(),hkCustInfo.getIdType()));

        custKeyAttrInfo.setMobileAreaCode(hkCustInfo.getMobileAreaCode());
        custKeyAttrInfo.setMobileDigest(hkCustInfo.getMobileDigest());
        custKeyAttrInfo.setMobileMask(hkCustInfo.getMobileMask());

        custKeyAttrInfo.setOpenAcct(hkCustInfo.isOpHkAcct()?YesOrNoEnum.YES.getCode():YesOrNoEnum.NO.getCode());

        return custKeyAttrInfo;
    }


    /**
     * @description:(根据 一账通号 查询 一账通侧 客户关键信息)
     * @param hboneNo
     * @return com.howbuy.crm.account.client.response.custinfo.CustKeyAttrInfo
     * @author: haoran.zhang
     * @date: 2024/1/17 14:51
     * @since JDK 1.8
     */
    public CustKeyAttrInfo getHboneSideInfo(String hboneNo){
        if(StringUtil.isEmpty(hboneNo)){
            return null;
        }
//      HboneAcctCustDetailInfoVO hboneCustInfo=hboneCustInfoOuterService.queryHboneCustDetailInfo(hboneNo);
        HboneAcctCustInfoVO hboneCustInfo=hboneCustInfoOuterService.queryHboneAcctInfo(hboneNo);
        if(hboneCustInfo==null){
            return null;
        }
        boolean isRealName= StringUtil.isNotEmpty(hboneCustInfo.getIdNoDigest());

        CustKeyAttrInfo custKeyAttrInfo = new CustKeyAttrInfo();

        custKeyAttrInfo.setHkTxAcctNo(getHkTxAcctNoByHboneNo(hboneNo));
        custKeyAttrInfo.setHboneNo(hboneNo);

        custKeyAttrInfo.setCustName(hboneCustInfo.getCustName());
        custKeyAttrInfo.setInvestType(hboneCustInfo.getUserType());
//        custKeyAttrInfo.setCustStatus(hboneCustInfo.getHkCustStatus());
//        custKeyAttrInfo.setCustStatusDesc(HkAcctCustStatusEnum.getDescription(hboneCustInfo.getHkCustStatus()));
        custKeyAttrInfo.setIsRealName(isRealName? YesOrNoEnum.YES.getCode():YesOrNoEnum.NO.getCode());
        custKeyAttrInfo.setIdNoDigest(hboneCustInfo.getIdNoDigest());
        custKeyAttrInfo.setIdSignAreaCode(hboneCustInfo.getIdSignAreaCode());
        custKeyAttrInfo.setIdType(hboneCustInfo.getIdType());
        custKeyAttrInfo.setIdNoMask(hboneCustInfo.getIdNoMask());
        custKeyAttrInfo.setIdTypeDesc(IdTypeUtil.getIdTypeDesc(hboneCustInfo.getUserType(),hboneCustInfo.getIdType()));


        custKeyAttrInfo.setMobileAreaCode(hboneCustInfo.getMobileAreaCode());
        custKeyAttrInfo.setMobileDigest(hboneCustInfo.getMobileDigest());
        custKeyAttrInfo.setMobileMask(hboneCustInfo.getMobileMask());

        //是否开户判断
        boolean isOpenAcct=false;
        HboneTxAcctInfoVO txAcctInfo=hboneCustInfoOuterService.queryTxAcctInfo(hboneNo);
        if(txAcctInfo!=null && StringUtil.isNotEmpty(txAcctInfo.getTxAcctNo())){
            isOpenAcct=true;
        }
        custKeyAttrInfo.setOpenAcct(isOpenAcct?YesOrNoEnum.YES.getCode():YesOrNoEnum.NO.getCode());

        return custKeyAttrInfo;

    }


    /**
     * 获取 一账通  账户中心关联 的  香港客户号
     * @param hboneNo
     * @return
     */
    private String getHkTxAcctNoByHboneNo(String hboneNo){
        //获取  香港客户号
        HkAcctCustInfoVO hkDetailInfo=null;
        HkAcctCustInfoReqVO queryHkVo = new HkAcctCustInfoReqVO();
        queryHkVo.setHboneNo(hboneNo);
        queryHkVo.setCustStatList(AbnormalCustUtils.getValidHkCustStateList());
        PageVO<HkAcctCustInfoVO> fullMatchResp = hkCustInfoOuterService.queryHkCustInfoPage(queryHkVo);
        if (fullMatchResp != null &&
                CollectionUtils.isNotEmpty(fullMatchResp.getRows()) &&
                fullMatchResp.getRows().size() == 1) {
            hkDetailInfo= fullMatchResp.getRows().get(0);
        }
        if(hkDetailInfo!=null){
            return hkDetailInfo.getHkTxAcctNo();
        }

        return null;
    }


    /**
     * 参数 [service --> repository 参数的]转译
     * @param custInfoRequest
     * @return
     */
    public AbnormalCustReqVO buidReqVo(AbnormalCustInfoRequest custInfoRequest){
        AbnormalCustReqVO reqVo=new AbnormalCustReqVO();
        reqVo.setCustNo(custInfoRequest.getCustNo());
        reqVo.setCustName(custInfoRequest.getCustName());
        reqVo.setHkTxAcctNo(custInfoRequest.getHkTxAcctNo());
        reqVo.setHboneNo(custInfoRequest.getHboneNo());
        reqVo.setMobileDigest(custInfoRequest.getMobileDigest());
        reqVo.setIdNoDigest(custInfoRequest.getIdNoDigest());
        reqVo.setAbnormalSourceList(ParamFormatUtil.getParamList(custInfoRequest.getAbnormalSource(), Lists.newArrayList()));
        reqVo.setDealStatusList(ParamFormatUtil.getParamList(custInfoRequest.getDealStatus(), Lists.newArrayList()));
        reqVo.setAbnormalIdList(ParamFormatUtil.getParamList(custInfoRequest.getAbnormalId(), Lists.newArrayList()));
        reqVo.setAbnormalLevelList(ParamFormatUtil.getParamList(custInfoRequest.getAbnormalLevel(), Lists.newArrayList()));

        if(StringUtil.isNotEmpty(custInfoRequest.getCreateBginDdate())) {
            reqVo.setCreateBginDdate(DateUtil.string2Date(
                    String.join("", custInfoRequest.getCreateBginDdate(), Constants.DATE_TIME_START),
                    DateUtil.STR_PATTERN)
            );
        }
        if(StringUtil.isNotEmpty(custInfoRequest.getCreateEndDate())){
            reqVo.setCreateEndDate(DateUtil.string2Date(
                    String.join("",custInfoRequest.getCreateEndDate(), Constants.DATE_TIME_END),
                    DateUtil.STR_PATTERN)
            );
        }
        reqVo.setPage(custInfoRequest.getPage());
        reqVo.setRows(custInfoRequest.getRows());
        return reqVo;
    }

}