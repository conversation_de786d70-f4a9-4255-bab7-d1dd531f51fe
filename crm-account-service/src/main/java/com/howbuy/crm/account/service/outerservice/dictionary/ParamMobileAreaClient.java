package com.howbuy.crm.account.service.outerservice.dictionary;


import com.howbuy.crm.account.client.response.Response;
import com.howbuy.crm.account.client.response.dictionary.ParamMobileAreaDTO;
import com.howbuy.crm.account.service.commom.constant.FeginServerKeyConstant;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.PostMapping;

/**
 * @Author: shuai.zhang
 * @Date: 2023/5/22
 * @Description: 手机地区码
 */
@FeignClient(name = FeginServerKeyConstant.DTMS_PRODUCT_REMOTE, path = "product/mobilearea")
public interface ParamMobileAreaClient {
    /**
     * @description:(查询手机地区码)
     * @param
     * @return com.howbuy.crm.cgi.manager.outerservice.CrmResult<com.howbuy.crm.cgi.manager.domain.dtmsproduct.ParamMobileAreaVO>
     * @author: shuai.zhang
     * @date: 2023/6/8 15:47
     * @since JDK 1.8
     */
    @PostMapping(value = "/query")
    Response<ParamMobileAreaDTO> query();
}
