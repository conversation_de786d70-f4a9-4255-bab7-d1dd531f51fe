/**
 * Copyright (c) 2023, <PERSON><PERSON><PERSON><PERSON> HOWBUY INVESTMENT MANAGEMENT Co., Ltd.
 * All right reserved.
 * <p>
 * THIS IS UNPUBLISHED PROPRIETARY SOURCE CODE OF HOWBUY INVESTMENT
 * MANAGEMENT CO., LTD.  THE CONTENTS OF THIS FILE MAY NOT BE DISCLOSED
 * TO THIRD PARTIES, COPIED OR DUPLICATED IN ANY FORM, IN WHOLE OR IN PART,
 * WITHOUT THE PRIOR WRITTEN PERMISSION OF HOWBUY INVESTMENT MANAGEMENT
 * CO., LTD.
 */
package com.howbuy.crm.account.service.outerservice.acct;

import com.google.common.collect.Lists;
import com.howbuy.acccenter.facade.query.queryallcustinfoanddiscustinfo.QueryAllCustInfoAndDisCustInfoFacade;
import com.howbuy.acccenter.facade.query.queryallcustinfoanddiscustinfo.QueryAllCustInfoAndDisCustInfoRequest;
import com.howbuy.acccenter.facade.query.queryallcustinfoanddiscustinfo.QueryAllCustInfoAndDisCustInfoResponse;
import com.howbuy.acccenter.facade.query.sensitive.discustinfo.QueryDisCustSensitiveInfoFacade;
import com.howbuy.acccenter.facade.query.sensitive.discustinfo.QueryDisCustSensitiveInfoRequest;
import com.howbuy.acccenter.facade.query.sensitive.discustinfo.QueryDisCustSensitiveInfoResponse;
import com.howbuy.crm.account.client.enums.DisCodeEnum;
import com.howbuy.crm.account.client.response.custinfo.HboneCustWithDisInfoVO;
import com.howbuy.crm.account.client.response.custinfo.HboneDisCustInfoVO;
import com.howbuy.crm.account.client.response.custinfo.HboneDisCustSensitiveInfoVO;
import com.howbuy.crm.account.client.response.custinfo.HboneDisMainCustInfoVO;
import com.howbuy.crm.account.service.commom.constant.Constants;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.dubbo.config.annotation.DubboReference;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Service;
import org.springframework.util.Assert;

import java.util.List;

/**
 * @description: (好买账户中心- 一账通   分销层 outer service)
 * <AUTHOR>
 * @date 2023/12/8 20:15
 * @since JDK 1.8
 */
@Slf4j
@Service
public class HboneDisAcctInfoOuterService extends AbstractAcctOuterService {

    @DubboReference(registry = "acc-center-server", check = false)
    private QueryAllCustInfoAndDisCustInfoFacade queryAllCustInfoAndDisCustInfoFacade;



    @DubboReference(registry = "acc-center-server", check = false)
    private QueryDisCustSensitiveInfoFacade queryDisCustMobileFacade;



    /**
     * 查询一账通客户信息 与 分销客户信息
     * @param hboneNo  NOT NULL
     * @param disCodeEnum NOT NULL
     * @return
     */
    public HboneCustWithDisInfoVO queryHboneCustWithDisInfo(String hboneNo,DisCodeEnum disCodeEnum){
        Assert.notNull(hboneNo,"一账通不能为空！");
        Assert.notNull(disCodeEnum,"分销代码不能为空！");
        QueryAllCustInfoAndDisCustInfoRequest disCustInfoRequest
                = new QueryAllCustInfoAndDisCustInfoRequest();
        disCustInfoRequest.setHboneNo(hboneNo);

        //已与acct确认。disCode参数对接口 不生效。此处。获取全量，自己过滤。
//        if(disCodeEnum!=null){
//            disCustInfoRequest.setDisCode(disCodeEnum.getCode());
//        }
        //过滤 dis
        return interactWithDisAcct(disCustInfoRequest,disCodeEnum);
    }


    /**
     * 查询一账通客户信息 与 分销客户信息
     * @param hboneNo
     * @return
     */
    public HboneCustWithDisInfoVO queryHboneCustWithDisInfo(String hboneNo){
        Assert.notNull(hboneNo,"一账通不能为空！");
        QueryAllCustInfoAndDisCustInfoRequest disCustInfoRequest
                = new QueryAllCustInfoAndDisCustInfoRequest();
        disCustInfoRequest.setHboneNo(hboneNo);
        return interactWithDisAcct(disCustInfoRequest,null);
    }


    /**
     * 与账户中心  分销查询接口  交互
     * @param disCustInfoRequest
     * @param disCodeEnum  分销代码 不为空，则只查询该分销
     * @return
     */
    private HboneCustWithDisInfoVO  interactWithDisAcct(
            QueryAllCustInfoAndDisCustInfoRequest disCustInfoRequest,DisCodeEnum disCodeEnum){
        QueryAllCustInfoAndDisCustInfoResponse resp
                = queryAllCustInfoAndDisCustInfoFacade.execute(disCustInfoRequest);
        if(!isAcctSuccess(resp)){
            return null;
        }
        HboneCustWithDisInfoVO returnVO=new HboneCustWithDisInfoVO();

        //账户中心[CustInfoBean] --> [HboneDisMainCustInfoVO]
        if(resp.getCustInfo()!=null){
            HboneDisMainCustInfoVO disMainCustInfoVO=new HboneDisMainCustInfoVO();
            BeanUtils.copyProperties(resp.getCustInfo(),disMainCustInfoVO);
            //hbone信息 默认处理  字段
            disMainCustInfoVO.setMobileAreaCode(Constants.DEFAULT_MOBILE_AREA_CODE);
            disMainCustInfoVO.setIdSignAreaCode(Constants.DEFAULT_ID_SIGN_AREA_CODE);
            returnVO.setMainCustInfo(disMainCustInfoVO);
        }

        //账户中心[DisCustInfoBean] --> [HboneDisCustInfoVO]
        List<HboneDisCustInfoVO> disCustInfoVOList= Lists.newArrayList();
        if(CollectionUtils.isNotEmpty(resp.getDisCustInfoList())){
            resp.getDisCustInfoList()
                    .stream()
                    //如果指定分销代码，则只查询该分销
                    .filter(disCustInfoBean -> disCodeEnum==null || disCodeEnum.getCode().equals(disCustInfoBean.getDisCode()))
                    .forEach(disCustInfoBean -> {
                        HboneDisCustInfoVO disCustInfoVO=new HboneDisCustInfoVO();
                        BeanUtils.copyProperties(disCustInfoBean,disCustInfoVO);
                        disCustInfoVOList.add(disCustInfoVO);
                    });
        }
        returnVO.setDisCustInfoList(disCustInfoVOList);

        return returnVO;
    }

    /**
     * @description:(获取分销层  敏感信息)
     * @param hboneNo
     * @param disCodeEnum
     * @return com.howbuy.crm.account.client.response.custinfo.HboneDisCustSensitiveInfoVO
     * @author: haoran.zhang
     * @date: 2023/12/21 17:47
     * @since JDK 1.8
     */
    public HboneDisCustSensitiveInfoVO queryHboneDisSensitiveInfo(String hboneNo, DisCodeEnum disCodeEnum){
        Assert.notNull(hboneNo,"一账通不能为空！");
        Assert.notNull(disCodeEnum,"分销代码不能为空！");
        QueryDisCustSensitiveInfoRequest sensitiveRequest = new QueryDisCustSensitiveInfoRequest();
        sensitiveRequest.setHboneNo(hboneNo);
        sensitiveRequest.setDisCode(disCodeEnum.getCode());
        QueryDisCustSensitiveInfoResponse resp = queryDisCustMobileFacade.execute(sensitiveRequest);
        if(!isAcctSuccess(resp) || resp.getDisCust()==null){
            return null;
        }
        HboneDisCustSensitiveInfoVO disSensitiveVo=new HboneDisCustSensitiveInfoVO();
        BeanUtils.copyProperties(resp.getDisCust(),disSensitiveVo);
        return disSensitiveVo;
    }


}