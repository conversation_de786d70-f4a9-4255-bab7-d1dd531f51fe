/**
 * Copyright (c) 2023, <PERSON>g<PERSON><PERSON> HOWBUY INVESTMENT MANAGEMENT Co., Ltd.
 * All right reserved.
 * <p>
 * THIS IS UNPUBLISHED PROPRIETARY SOURCE CODE OF HOWBUY INVESTMENT
 * MANAGEMENT CO., LTD.  THE CONTENTS OF THIS FILE MAY NOT BE DISCLOSED
 * TO THIRD PARTIES, COPIED OR DUPLICATED IN ANY FORM, IN WHOLE OR IN PART,
 * WITHOUT THE PRIOR WRITTEN PERMISSION OF HOWBUY INVESTMENT MANAGEMENT
 * CO., LTD.
 */
package com.howbuy.crm.account.service.req.custinfo;

import com.howbuy.crm.account.dao.req.PageReqVO;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.util.List;

/**
 * @description: (查询香港客户  request 参数对象)
 * <AUTHOR>
 * @date 2023/12/11 13:17
 * @since JDK 1.8
 */
@Data
@EqualsAndHashCode(callSuper = true)
public class HkAcctCustInfoReqVO extends PageReqVO {

    /**
     * 香港客户号
     */
    private String hkCustNo;
    /**
     * 香港客户号列表
     */
    private List<String> hkCustNoList;
    /**
     * 手机 区号
     */
    private String mobileAreaCode;
    /**
     * 手机号
     */
    private String mobileDigest;
    /**
     * 邮箱
     */
    private String emailDigest;

    /**
     * 证件号
     */
    private String idNoDigest;

    /**
     * 证件类型
     */
    private String idType;


    /**
     * 证件地区码-身份证签发地区编码
     */
    private String idSignAreaCode;

    /**
     * 一账通号
     */
    private String hboneNo;

    /**
     * 客户中文或英文名称

     */
    private String custCnOrEnName;
    /**
     * 投资者类型
     */
    private String invstType;

    /**
     *ebrokerId
     */
    private String ebrokerId;

    /**
     * 客户状态 列表
     * {@link com.howbuy.crm.account.client.enums.custinfo.HkAcctCustStatusEnum}
     */
    private List<String> custStatList;

    /**
     * 开户方式 0-线下 1-线上
     */
    private String openType;

    /**
     * 开户日期 开始
     */
    private String openDateStart;
    /**
     * 开户日期 结束
     */
    private String openDateEnd;


}