/**
 * Copyright (c) 2023, <PERSON><PERSON><PERSON><PERSON> HOWBUY INVESTMENT MANAGEMENT Co., Ltd.
 * All right reserved.
 * <p>
 * THIS IS UNPUBLISHED PROPRIETARY SOURCE CODE OF HOWBUY INVESTMENT
 * MANAGEMENT CO., LTD.  THE CONTENTS OF THIS FILE MAY NOT BE DISCLOSED
 * TO THIRD PARTIES, COPIED OR DUPLICATED IN ANY FORM, IN WHOLE OR IN PART,
 * WITHOUT THE PRIOR WRITTEN PERMISSION OF HOWBUY INVESTMENT MANAGEMENT
 * CO., LTD.
 */
package com.howbuy.crm.account.service.controller;

import com.howbuy.crm.account.client.request.Request;
import com.howbuy.crm.account.client.response.Response;
import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.ResponseBody;

/**
 * @description: (请在此添加描述)
 * <AUTHOR>
 * @date 2023/11/13 19:17
 * @since JDK 1.8
 */
@RequestMapping("/test")
@Controller
public class TestController {


    /**
     * @api {GET} /test/test1 test()
     * @apiVersion 1.0.0
     * @apiGroup TestController
     * @apiName test()
     * @apiDescription test
     * @apiParam (请求参数) {Object} request
     * @apiParamExample 请求参数示例
     * request=null
     * @apiSuccess (响应结果) {String} code 状态码
     * @apiSuccess (响应结果) {String} description 描述信息
     * @apiSuccess (响应结果) {Object} data 数据封装
     * @apiSuccessExample 响应结果示例
     * {"code":"yI0o9zimY","data":{},"description":"BR9Lpe"}
     */
    @ResponseBody
    @GetMapping("/test1")
    public Response test(Request request) {
        return Response.ok();
    }

    /**
     * @api {GET} /error
     * @apiName spring内部error接口
     */

    /**
     * @api {GET} /index
     * @apiName spring内部index接口
     */

}