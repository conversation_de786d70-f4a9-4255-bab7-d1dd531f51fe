///**
// * Copyright (c) 2025, <PERSON><PERSON><PERSON><PERSON> HOWBUY INVESTMENT MANAGEMENT Co., Ltd.
// * All right reserved.
// * <p>
// * THIS IS UNPUBLISHED PROPRIETARY SOURCE CODE OF HOWBUY INVESTMENT
// * MANAGEMENT CO., LTD.  THE CONTENTS OF THIS FILE MAY NOT BE DISCLOSED
// * TO THIRD PARTIES, COPIED OR DUPLICATED IN ANY FORM, IN WHOLE OR IN PART,
// * WITHOUT THE PRIOR WRITTEN PERMISSION OF HOWBUY INVESTMENT MANAGEMENT
// * CO., LTD.
// */
//package com.howbuy.crm.account.service.service.custmessage;
//
//import com.google.common.collect.Lists;
//import com.howbuy.common.utils.StringUtil;
//import com.howbuy.crm.account.client.enums.custinfo.AbnormaSceneTypeEnum;
//import com.howbuy.crm.account.client.enums.custinfo.FullCustSourceEnum;
//import com.howbuy.crm.account.dao.bo.custinfo.CmConscustForAnalyseBO;
//import com.howbuy.crm.account.service.repository.custinfo.AbnormalCustRepository;
//import com.howbuy.crm.account.service.vo.custinfo.AbnormalAnalyseResultVO;
//import com.howbuy.crm.account.service.vo.custinfo.HkMessageCustInfoVO;
//import lombok.extern.slf4j.Slf4j;
//import org.apache.commons.collections.CollectionUtils;
//import org.springframework.beans.factory.annotation.Autowired;
//import org.springframework.stereotype.Service;
//
//import java.util.List;
//
///**
// * @description: (香港账户-香港注册消息处理服务V2)
// * <AUTHOR>
// * @date 2025-04-15 14:29:08
// * @since JDK 1.8
// */
//@Service
//@Slf4j
//public class HkRegisterAnalyseServiceV2 implements CustMessageAnalyseService<HkMessageCustInfoVO> {
//
//    @Autowired
//    private AbnormalCustRepository abnormalCustRepository;
//
//    @Override
//    public List<FullCustSourceEnum> getSourceList() {
//        return Lists.newArrayList(FullCustSourceEnum.HK_REGISTER);
//    }
//
//    @Override
//    public AbnormalAnalyseResultVO<HkMessageCustInfoVO> analyze(HkMessageCustInfoVO analyseVo) {
//        log.info("香港注册消息处理V2开始，香港客户号：{}，一账通号：{}", analyseVo.getHkTxAcctNo(), analyseVo.getHboneNo());
//
//        // 初始化返回结果对象，默认为正常数据
//        AbnormalAnalyseResultVO<HkMessageCustInfoVO> resultVo = AbnormalAnalyseResultVO.normalData(analyseVo);
//
//        // 香港客户号
//        String hkTxAcctNo = analyseVo.getHkTxAcctNo();
//        // 一账通号（香港账号中心逻辑保证：必然存在一账通号！）
//        String hboneNo = analyseVo.getHboneNo();
//        String mobileAreaCode = analyseVo.getMobileAreaCode();
//        String mobileDigest = analyseVo.getMobileDigest();
//        String investType = analyseVo.getInvestType();
//
//        // 判断①：【香港客户号】绑定的【一账通号】是否已关联【投顾客户号】
//        List<CmConscustForAnalyseBO> hboneCustList = abnormalCustRepository.queryCustBOByHboneNo(hboneNo);
//
//        // 一账通号已关联【投顾客户号】
//        if (CollectionUtils.isNotEmpty(hboneCustList)) {
//            // 投顾客户的一账通号 在数据库层面上存在唯一性约束，故取第一个即可
//            CmConscustForAnalyseBO hboneCust = hboneCustList.get(0);
//
//            // 判断⑤：【投顾客户号】绑定的手机号是否等于香港【手机号】
//            if (isMobileMatch(hboneCust, mobileAreaCode, mobileDigest)) {
//                // 手机号一致，判断④：【投顾客户号】是否已绑定【其他香港客户号】
//                return checkAndBindHkTxAcctNo(resultVo, hboneCust, hkTxAcctNo);
//            } else {
//                // 手机号不一致，判断⑥：香港【手机号】是否已关联【投顾客户号】
//                return checkHkMobileMatchCust(resultVo, hboneCust, mobileAreaCode, mobileDigest, investType, hkTxAcctNo);
//            }
//        }
//
//        // 一账通号未关联投顾客户号，进行判断②：香港【手机号】是否已关联【投顾客户号】
//        List<CmConscustForAnalyseBO> mobileMatchCustList = abnormalCustRepository.queryCustListByMobile(investType, mobileAreaCode, mobileDigest);
//
//        // 不存在手机号匹配的投顾客户号
//        if (CollectionUtils.isEmpty(mobileMatchCustList)) {
//            // 流程结束，不处理
//            resultVo.setNeedProcess(false);
//            log.info("香港注册消息处理V2结束，香港手机号未关联投顾客户号，流程结束：{}", analyseVo.getHkTxAcctNo());
//            return resultVo;
//        }
//
//        // 存在多个手机号匹配的投顾客户号（场景3 - HK03）
//        if (mobileMatchCustList.size() > 1) {
//            log.info("香港注册消息处理V2发现异常：{}，香港客户号：{}", AbnormaSceneTypeEnum.HK03.getDescription(), hkTxAcctNo);
//            return AbnormalAnalyseResultVO.notNormalData(
//                    analyseVo,
//                    AbnormaSceneTypeEnum.HK03,
//                    mobileMatchCustList);
//        }
//
//        // 存在唯一的手机号匹配的投顾客户号
//        CmConscustForAnalyseBO mobileMatchCust = mobileMatchCustList.get(0);
//
//        // 判断③：【投顾客户号】是否已绑定【其他一账通号】（场景2 - HK02）
//        if (StringUtil.isNotBlank(mobileMatchCust.getHboneNo()) &&
//            !mobileMatchCust.getHboneNo().equals(hboneNo)) {
//            log.info("香港注册消息处理V2发现异常：{}，香港客户号：{}", AbnormaSceneTypeEnum.HK02.getDescription(), hkTxAcctNo);
//            return AbnormalAnalyseResultVO.notNormalData(
//                    analyseVo,
//                    AbnormaSceneTypeEnum.HK02,
//                    Lists.newArrayList(mobileMatchCust));
//        }
//
//        // 判断④：【投顾客户号】是否已绑定【其他香港客户号】
//        return checkAndBindHkTxAcctNo(resultVo, mobileMatchCust, hkTxAcctNo);
//    }
//
//    /**
//     * @description: 判断投顾客户号是否已绑定其他香港客户号
//     * @param resultVo 分析结果
//     * @param custBO 投顾客户号信息
//     * @param hkTxAcctNo 香港客户号
//     * @return 处理结果
//     * <AUTHOR>
//     * @date 2025-04-15 15:06:12
//     * @since JDK 1.8
//     */
//    private AbnormalAnalyseResultVO<HkMessageCustInfoVO> checkAndBindHkTxAcctNo(
//            AbnormalAnalyseResultVO<HkMessageCustInfoVO> resultVo,
//            CmConscustForAnalyseBO custBO,
//            String hkTxAcctNo) {
//        // 判断【投顾客户号】是否已绑定【其他香港客户号】
//        if (StringUtil.isNotBlank(custBO.getHkTxAcctNo()) &&
//            !custBO.getHkTxAcctNo().equals(hkTxAcctNo)) {
//            // 投顾客户号已绑定其他香港客户号（场景1 - HK01）
//            log.info("香港注册消息处理V2发现异常：{}，香港客户号：{}", AbnormaSceneTypeEnum.HK01.getDescription(), hkTxAcctNo);
//            return AbnormalAnalyseResultVO.notNormalData(
//                    resultVo.getAnalyseVo(),
//                    AbnormaSceneTypeEnum.HK01,
//                    Lists.newArrayList(custBO));
//        }
//
//        // 投顾客户号未绑定其他香港客户号，绑定当前香港客户号
//        resultVo.setProcessedCustInfo(custBO);
//        log.info("香港注册消息处理V2结束，投顾客户号未被占用，绑定香港客户号，香港客户号：{}，投顾客户号：{}",
//                hkTxAcctNo, custBO.getConscustno());
//        return resultVo;
//    }
//
//    /**
//     * @description: 判断香港手机号是否匹配投顾客户手机号
//     * @param custBO 投顾客户号信息
//     * @param mobileAreaCode 手机区号
//     * @param mobileDigest 手机号
//     * @return 是否匹配
//     * <AUTHOR>
//     * @date 2025-04-15 15:06:12
//     * @since JDK 1.8
//     */
//    private boolean isMobileMatch(CmConscustForAnalyseBO custBO, String mobileAreaCode, String mobileDigest) {
//        return (mobileAreaCode != null && mobileDigest != null &&
//                mobileAreaCode.equals(custBO.getMobileAreaCode()) &&
//                mobileDigest.equals(custBO.getMobileDigest()));
//    }
//
//    /**
//     * @description: 检查香港手机号关联的投顾客户号
//     * @param resultVo 分析结果
//     * @param hboneCust 一账通关联的投顾客户号
//     * @param mobileAreaCode 手机区号
//     * @param mobileDigest 手机号
//     * @param investType 投资者类型
//     * @param hkTxAcctNo 香港客户号
//     * @return 处理结果
//     * <AUTHOR>
//     * @date 2025-04-15 15:06:12
//     * @since JDK 1.8
//     */
//    private AbnormalAnalyseResultVO<HkMessageCustInfoVO> checkHkMobileMatchCust(
//            AbnormalAnalyseResultVO<HkMessageCustInfoVO> resultVo,
//            CmConscustForAnalyseBO hboneCust,
//            String mobileAreaCode,
//            String mobileDigest,
//            String investType,
//            String hkTxAcctNo) {
//
//        // 查询香港手机号匹配的投顾客户号
//        List<CmConscustForAnalyseBO> mobileCustList = abnormalCustRepository.queryCustListByMobile(
//                investType, mobileAreaCode, mobileDigest);
//
//        // 香港手机号不存在匹配的投顾客户号（场景4 - HK04）
//        if (CollectionUtils.isEmpty(mobileCustList)) {
//            log.info("香港注册消息处理V2发现异常：{}，香港客户号：{}", AbnormaSceneTypeEnum.HK04.getDescription(), hkTxAcctNo);
//            return AbnormalAnalyseResultVO.notNormalData(
//                    resultVo.getAnalyseVo(),
//                    AbnormaSceneTypeEnum.HK04,
//                    Lists.newArrayList(hboneCust));
//        }
//
//        // 将两个列表合并去重
//        List<CmConscustForAnalyseBO> mergedList = Lists.newArrayList(hboneCust);
//
//        // 过滤掉已经存在于mergedList中的元素后添加
//        mobileCustList.stream()
//                .filter(cust -> mergedList.stream()
//                        .noneMatch(existing -> existing.getConscustno().equals(cust.getConscustno())))
//                .forEach(mergedList::add);
//
//        // 香港手机号存在唯一匹配的投顾客户号（场景5 - HK05）
//        if (mobileCustList.size() == 1) {
//            log.info("香港注册消息处理V2发现异常：{}，香港客户号：{}", AbnormaSceneTypeEnum.HK05.getDescription(), hkTxAcctNo);
//            return AbnormalAnalyseResultVO.notNormalData(
//                    resultVo.getAnalyseVo(),
//                    AbnormaSceneTypeEnum.HK05,
//                    mergedList);
//        }
//
//        // 香港手机号存在多个匹配的投顾客户号（场景6 - HK06）
//        log.info("香港注册消息处理V2发现异常：{}，香港客户号：{}", AbnormaSceneTypeEnum.HK06.getDescription(), hkTxAcctNo);
//        return AbnormalAnalyseResultVO.notNormalData(
//                resultVo.getAnalyseVo(),
//                AbnormaSceneTypeEnum.HK06,
//                mergedList);
//    }
//}