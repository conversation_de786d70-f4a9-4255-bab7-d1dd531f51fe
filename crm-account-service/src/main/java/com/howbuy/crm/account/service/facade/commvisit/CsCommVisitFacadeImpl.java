/**
 * Copyright (c) 2024, <PERSON><PERSON><PERSON><PERSON> HOWBUY INVESTMENT MANAGEMENT Co., Ltd.
 * All right reserved.
 * <p>
 * THIS IS UNPUBLISHED PROPRIETARY SOURCE CODE OF HOWBUY INVESTMENT
 * MANAGEMENT CO., LTD.  THE CONTENTS OF THIS FILE MAY NOT BE DISCLOSED
 * TO THIRD PARTIES, COPIED OR DUPLICATED IN ANY FORM, IN WHOLE OR IN PART,
 * WITHOUT THE PRIOR WRITTEN PERMISSION OF HOWBUY INVESTMENT MANAGEMENT
 * CO., LTD.
 */
package com.howbuy.crm.account.service.facade.commvisit;

import com.howbuy.crm.account.client.facade.commvisit.CsCommVisitFacade;
import com.howbuy.crm.account.client.request.commvisit.AddCommunicateRecordRequest;
import com.howbuy.crm.account.client.request.commvisit.QueryCommVisitListRequest;
import com.howbuy.crm.account.client.request.commvisit.QueryCommunicateListRequest;
import com.howbuy.crm.account.client.request.commvisit.VisitInitDataRequest;
import com.howbuy.crm.account.client.response.Response;
import com.howbuy.crm.account.client.response.commvisit.AddCommunicateRecordResponse;
import com.howbuy.crm.account.client.response.commvisit.CommunicateListVO;
import com.howbuy.crm.account.client.response.commvisit.CsCommVisitListVO;
import com.howbuy.crm.account.client.response.commvisit.VisitInitDataResponse;
import com.howbuy.crm.account.service.service.commvisit.CsCommVisitService;
import org.apache.dubbo.config.annotation.DubboService;

import javax.annotation.Resource;

/**
 * <AUTHOR>
 * @description: 沟通拜访接口实现
 * @date 2024/10/11 19:47
 * @since JDK 1.8
 */
@DubboService
public class CsCommVisitFacadeImpl implements CsCommVisitFacade {

    @Resource
    private CsCommVisitService csCommVisitService;


    @Override
    public Response<CsCommVisitListVO> queryCsCommVisitList(QueryCommVisitListRequest request) {
        return Response.ok(csCommVisitService.queryCsCommVisitList(request));
    }
    @Override
    public Response<CsCommVisitListVO> queryNewsestCsCommVisit(QueryCommVisitListRequest request) {
        return Response.ok(csCommVisitService.queryNewestCsCommVisitList(request));
    }

    @Override
    public Response<VisitInitDataResponse> getVisitInitData(VisitInitDataRequest request) {
        return Response.ok(csCommVisitService.getVisitInitData(request));
    }

    @Override
    public Response<AddCommunicateRecordResponse> addCommunicateRecord(AddCommunicateRecordRequest request) {
        return csCommVisitService.addCommunicateRecord(request);
    }

    @Override
    public Response<CommunicateListVO> queryCommunicateList(QueryCommunicateListRequest request) {
        return Response.ok(csCommVisitService.queryCommunicateList(request));
    }
}
