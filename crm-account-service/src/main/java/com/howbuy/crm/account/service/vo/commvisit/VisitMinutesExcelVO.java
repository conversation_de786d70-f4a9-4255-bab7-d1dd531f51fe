package com.howbuy.crm.account.service.vo.commvisit;

import com.alibaba.excel.annotation.ExcelProperty;
import lombok.Getter;
import lombok.Setter;
import lombok.ToString;

/**
 * <AUTHOR>
 * @description: 请在此添加描述
 * @date 2025/4/17 9:44
 * @since JDK 1.8
 */
@Getter
@Setter
@ToString
public class VisitMinutesExcelVO {
/**
     * 创建日期
     */
    @ExcelProperty(value = "创建日期", index = 0)
    private String createTime;

    /**
     * 拜访日期
     */
    @ExcelProperty(value = "拜访日期", index = 1)
    private String visitDt;

    /**
     * 拜访目的
     */
    @ExcelProperty(value = "拜访目的", index = 2)
    private String visitPurpose;

    /**
     * 投顾客户号
     */
    @ExcelProperty(value = "投顾客户号", index = 3)
    private String consCustNo;

    /**
     * 客户姓名
     */
    @ExcelProperty(value = "客户姓名", index = 4)
    private String custName;

    /**
     * 纪要创建人姓名
     */
    @ExcelProperty(value = "纪要创建人姓名", index = 5)
    private String creatorName;

    /**
     * 所属中心
     */
    @ExcelProperty(value = "所属中心", index = 6)
    private String centerName;

    /**
     * 所属区域
     */
    @ExcelProperty(value = "所属区域", index = 7)
    private String areaName;

    /**
     * 所属分公司
     */
    @ExcelProperty(value = "所属分公司", index = 8)
    private String branchName;

    /**
     * 沟通方式
     */
    @ExcelProperty(value = "沟通方式", index = 9)
    private String visitType;

    /**
     * 客户存量
     */
    @ExcelProperty(value = "客户存量", index = 10)
    private String marketVal;

    /**
     * 客户综合健康度
     */
    @ExcelProperty(value = "客户综合健康度", index = 11)
    private String healthAvgStar;

    /**
     * 提供资料
     */
    @ExcelProperty(value = "提供资料", index = 12)
    private String giveInformation;

    /**
     * 客户参与人员及角色
     */
    @ExcelProperty(value = "客户参与人员及角色", index = 13)
    private String attendRole;

    /**
     * 对产品或服务的具体反馈
     */
    @ExcelProperty(value = "对产品或服务的具体反馈", index = 14)
    private String productServiceFeedback;

    /**
     * 对于IPS报告反馈
     */
    @ExcelProperty(value = "对于IPS报告反馈", index = 15)
    private String ipsFeedback;

    /**
     * 近期可用于加仓的金额
     */
    @ExcelProperty(value = "近期可用于加仓的金额", index = 16)
    private String addAmount;

    /**
     * 近期关注的资产类别或具体产品
     */
    @ExcelProperty(value = "近期关注的资产类别或具体产品", index = 17)
    private String focusAsset;

    /**
     * 评估客户需求
     */
    @ExcelProperty(value = "评估客户对创新业务、家族信托、身份、法税的需求", index = 18)
    private String estimateNeedBusiness;

    /**
     * 下一步工作计划
     */
    @ExcelProperty(value = "下一步工作计划", index = 19)
    private String nextPlan;

    /**
     * 陪访人类型
     */
    @ExcelProperty(value = "陪访人类型", index = 20)
    private String accompanyingType;

    /**
     * 陪访人
     */
    @ExcelProperty(value = "陪访人", index = 21)
    private String accompanyingUser;

    /**
     * 陪访人反馈-概要
     */
    @ExcelProperty(value = "陪-本次陪访概要经验或教训", index = 22)
    private String accompanySummary;

    /**
     * 陪访人反馈-建议
     */
    @ExcelProperty(value = "陪-该客户下阶段工作的建议", index = 23)
    private String accompanySuggestion;

    /**
     * 上级主管
     */
    @ExcelProperty(value = "上级主管", index = 24)
    private String managerName;

    /**
     * 主管反馈-概要
     */
    @ExcelProperty(value = "管-本次陪访概要经验或教训", index = 25)
    private String managerSummary;

    /**
     * 主管反馈-建议
     */
    @ExcelProperty(value = "管-该客户下阶段工作的建议", index = 26)
    private String managerSuggestion;
}