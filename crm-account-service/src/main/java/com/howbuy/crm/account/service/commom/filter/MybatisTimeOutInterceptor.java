/**
 * Copyright (c) 2021, <PERSON><PERSON><PERSON><PERSON> HOWBUY INVESTMENT MANAGEMENT Co., Ltd.
 * All right reserved.
 * <p>
 * THIS IS UNPUBLISHED PROPRIETARY SOURCE CODE OF HOWBUY INVESTMENT
 * MANAGEMENT CO., LTD.  THE CONTENTS OF THIS FILE MAY NOT BE DISCLOSED
 * TO THIRD PARTIES, COPIED OR DUPLICATED IN ANY FORM, IN WHOLE OR IN PART,
 * WITHOUT THE PRIOR WRITTEN PERMISSION OF HOWBUY INVESTMENT MANAGEMENT
 * CO., LTD.
 */

package com.howbuy.crm.account.service.commom.filter;

import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.math.NumberUtils;
import org.apache.ibatis.cache.CacheKey;
import org.apache.ibatis.executor.Executor;
import org.apache.ibatis.mapping.*;
import org.apache.ibatis.plugin.*;
import org.apache.ibatis.reflection.MetaObject;
import org.apache.ibatis.session.*;
import org.apache.ibatis.type.TypeHandlerRegistry;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;

import java.text.DateFormat;
import java.util.*;

/**
 * <AUTHOR>
 * @description:sql超时拦截器（记录到日志中）
 * @date 2023/02/20 13:20
 * @since JDK 1.8
 */
@Slf4j(topic = "slowSqlLog")
@Component
@Intercepts(value = {
        @Signature(type = Executor.class, method = "update", args = {MappedStatement.class, Object.class}),
        @Signature(type = Executor.class, method = "query", args = {MappedStatement.class, Object.class,
                RowBounds.class, ResultHandler.class, CacheKey.class, BoundSql.class}),
        @Signature(type = Executor.class, method = "query", args = {MappedStatement.class, Object.class,
                RowBounds.class, ResultHandler.class})})
public class MybatisTimeOutInterceptor implements Interceptor {

    @Value("${sql.process.timeout:2000}")
    private String sqlTimeOut;


    @Override
    public Object intercept(Invocation arg0) throws Throwable {
        MappedStatement mappedStatement = (MappedStatement) arg0.getArgs()[0];
        Object parameter;
        if (arg0.getArgs().length > 1) {
            parameter = arg0.getArgs()[1];
        } else {
            //如果invoke 长度小于1 就直接执行
            return arg0.proceed();
        }
        long start = System.currentTimeMillis();
        Object returnValue = arg0.proceed();
        long end = System.currentTimeMillis();
        long time = (end - start);
        //如果不超时 就直接返回
        if (time < NumberUtils.toLong(sqlTimeOut)) {
            return returnValue;
        }
        BoundSql boundSql = mappedStatement.getBoundSql(parameter);
        String sql = this.getActualSql(mappedStatement.getConfiguration(), boundSql,
                mappedStatement.getId(), time);
        log.info(sql);
        return returnValue;
    }

    /**
     * 获取正在执行的sql
     *
     * @param boundSql sql
     * @param sqlId    sqlId
     * @param time     超时时间
     * @return java.lang.String
     * @author: hongdong.xie
     * @date: 2023/02/20 13:33
     * @since: JDK 1.8
     */
    public String getActualSql(Configuration configuration, BoundSql boundSql, String sqlId, long time) {
        String sql = this.showSql(configuration, boundSql);
        return "执行缓慢的SQL：" + sqlId +
                ":" +
                sql +
                "; 执行时间:" +
                time +
                "ms ";
    }

    /**
     * 格式化sql
     *
     * @param boundSql sql
     * @return java.lang.String
     * @author: hongdong.xie
     * @date: 2023/02/20 13:34
     * @since: JDK 1.8
     */
    public String showSql(Configuration configuration, BoundSql boundSql) {
        Object parameterObject = boundSql.getParameterObject();
        List<ParameterMapping> parameterMappings = boundSql.getParameterMappings();
        //替换空格、换行、tab缩进等
        String sql = boundSql.getSql().replace("[\\s]+", " ").replace("\n", " ");
        if (CollectionUtils.isNotEmpty(parameterMappings)) {
            TypeHandlerRegistry typeHandlerRegistry = configuration.getTypeHandlerRegistry();
            if (typeHandlerRegistry.hasTypeHandler(parameterObject.getClass())) {
                sql = sql.replaceFirst("\\?", getParameterValue(parameterObject));
            } else {
                MetaObject metaObject = configuration.newMetaObject(parameterObject);
                for (ParameterMapping parameterMapping : parameterMappings) {
                    String propertyName = parameterMapping.getProperty();
                    if (metaObject.hasGetter(propertyName)) {
                        Object obj = metaObject.getValue(propertyName);
                        sql = sql.replaceFirst("\\?", getParameterValue(obj));
                    } else if (boundSql.hasAdditionalParameter(propertyName)) {
                        Object obj = boundSql.getAdditionalParameter(propertyName);
                        sql = sql.replaceFirst("\\?", getParameterValue(obj));
                    } else {
                        sql = sql.replaceFirst("\\?", "缺失");
                    }
                }
            }
        }
        return sql;
    }

    /**
     * 获取参数值
     *
     * @param obj 参数
     * @return java.lang.String
     * @author: hongdong.xie
     * @date: 2023/02/20 17:31
     * @since: JDK 1.8
     */
    private String getParameterValue(Object obj) {
        if (obj instanceof String) {
            return "'" + obj + "'";
        }
        if (obj instanceof Date) {
            DateFormat formatter = DateFormat.getDateTimeInstance(DateFormat.DEFAULT, DateFormat.DEFAULT, Locale.CHINA);
            return "'" + formatter.format(new Date()) + "'";
        }
        if (obj != null) {
            return obj.toString();
        }
        return StringUtils.EMPTY;
    }


    @Override
    public Object plugin(Object o) {
        return Plugin.wrap(o, this);
    }

}