/**
 * Copyright (c) 2024, <PERSON><PERSON><PERSON><PERSON> HOWBUY INVESTMENT MANAGEMENT Co., Ltd.
 * All right reserved.
 * <p>
 * THIS IS UNPUBLISHED PROPRIETARY SOURCE CODE OF HOWBUY INVESTMENT
 * MANAGEMENT CO., LTD.  THE CONTENTS OF THIS FILE MAY NOT BE DISCLOSED
 * TO THIRD PARTIES, COPIED OR DUPLICATED IN ANY FORM, IN WHOLE OR IN PART,
 * WITHOUT THE PRIOR WRITTEN PERMISSION OF HOWBUY INVESTMENT MANAGEMENT
 * CO., LTD.
 */
package com.howbuy.crm.account.service.service.consultant;

import com.howbuy.crm.account.client.request.consultant.BatchQueryConsPerformanceCoeffRequest;
import com.howbuy.crm.account.client.response.consultant.QueryConsPerformanceCoeffVO;
import com.howbuy.crm.account.dao.po.consultant.CmConsPerformanceCoeffPO;
import com.howbuy.crm.account.service.repository.consultant.QueryConsPerformanceCoeffRepository;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.List;

/**
 * @description: 批量获取人员绩效系数表服务
 * <AUTHOR>
 * @date 2025-07-17 15:50:04
 * @since JDK 1.8
 */
@Service
@Slf4j
public class BatchQueryConsPerformanceCoeffService {

    @Resource
    private QueryConsPerformanceCoeffRepository queryConsPerformanceCoeffRepository;

    /**
     * @param request 请求参数
     * @return java.util.List<QueryConsPerformanceCoeffVO>
     * @description: 批量获取人员绩效系数表
     * <AUTHOR>
     * @date 2025-07-17 15:50:04
     * @since JDK 1.8
     */
    public List<QueryConsPerformanceCoeffVO> execute(BatchQueryConsPerformanceCoeffRequest request) {
        log.info("批量获取人员绩效系数表，请求参数：{}", request);
        List<QueryConsPerformanceCoeffVO> voList = new ArrayList<>();
        if (request == null || request.getConsCustNoList() == null || request.getConsCustNoList().isEmpty()) {
            return voList;
        }
        List<CmConsPerformanceCoeffPO> poList = queryConsPerformanceCoeffRepository.getLatestRecordsByConsCustNoListAndConsCode(request.getConsCustNoList(), request.getConsCode());
        if (poList != null && !poList.isEmpty()) {
            for (CmConsPerformanceCoeffPO po : poList) {
                QueryConsPerformanceCoeffVO vo = new QueryConsPerformanceCoeffVO();
                vo.setConsCustNo(po.getConsCustNo());
                vo.setConsCode(po.getConscode());
                vo.setAssignTime(po.getAssignTime());
                vo.setSourceType(po.getSourceType());
                vo.setCustConversionCoeff(po.getCustConversionCoeff());
                vo.setManageCoeffSubtotal(po.getManageCoeffSubtotal());
                vo.setManageCoeffRegionalsubtotal(po.getManageCoeffRegionalsubtotal());
                vo.setManageCoeffRegionaltotal(po.getManageCoeffRegionaltotal());
                vo.setCommissionCoeffStart(po.getCommissionCoeffStart());
                vo.setCxb(po.getCxb());
                vo.setIsBigV(po.getIsBigV());
                voList.add(vo);
            }
        }
        log.info("批量获取人员绩效系数表完成，结果数：{}", voList.size());
        return voList;
    }
} 