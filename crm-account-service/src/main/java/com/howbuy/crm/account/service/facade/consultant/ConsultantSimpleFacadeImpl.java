package com.howbuy.crm.account.service.facade.consultant;

import com.alibaba.fastjson.JSON;
import com.howbuy.crm.account.client.facade.consultant.ConsultantSimpleFacade;
import com.howbuy.crm.account.client.request.consultant.ConsultantSimpleRequest;
import com.howbuy.crm.account.client.response.Response;
import com.howbuy.crm.account.client.response.consultant.ConsultantSimpleResponse;
import com.howbuy.crm.account.dao.po.consultant.ConsultantSimpleInfoDto;
import com.howbuy.crm.account.service.business.consultant.ConsultantBusinessService;
import lombok.extern.slf4j.Slf4j;
import org.apache.dubbo.config.annotation.DubboService;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;

/**
 * @description: 投顾简单信息查询服务实现
 * <AUTHOR>
 * @date 2025/3/10
 * @since JDK 1.8
 */
@Slf4j
@Service
@DubboService
public class ConsultantSimpleFacadeImpl implements ConsultantSimpleFacade {

    @Resource
    private ConsultantBusinessService consultantBusinessService;

    @Override
    public Response<ConsultantSimpleResponse> queryConsultantSimple(ConsultantSimpleRequest request) {
        log.info("查询投顾简单信息，入参：{}", JSON.toJSONString(request));
        
        try {
            // 调用Service层查询投顾信息
            ConsultantSimpleInfoDto infoDto = consultantBusinessService.getInfoByconsCode(request.getConsCode());

            ConsultantSimpleResponse response =null;
            if (infoDto != null) {
                // 转换响应对象
                response = new ConsultantSimpleResponse();
                BeanUtils.copyProperties(infoDto, response);
            }
            return Response.ok(response);
        } catch (Exception e) {
            log.error("查询投顾简单信息异常", e);
            return Response.fail("查询投顾简单信息失败：" + e.getMessage());
        }
    }
} 