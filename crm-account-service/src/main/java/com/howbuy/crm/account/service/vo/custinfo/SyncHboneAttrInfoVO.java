/**
 * Copyright (c) 2024, <PERSON><PERSON><PERSON><PERSON> HOWBUY INVESTMENT MANAGEMENT Co., Ltd.
 * All right reserved.
 * <p>
 * THIS IS UNPUBLISHED PROPRIETARY SOURCE CODE OF HOWBUY INVESTMENT
 * MANAGEMENT CO., LTD.  THE CONTENTS OF THIS FILE MAY NOT BE DISCLOSED
 * TO THIRD PARTIES, COPIED OR DUPLICATED IN ANY FORM, IN WHOLE OR IN PART,
 * WITHOUT THE PRIOR WRITTEN PERMISSION OF HOWBUY INVESTMENT MANAGEMENT
 * CO., LTD.
 */
package com.howbuy.crm.account.service.vo.custinfo;

import lombok.Data;

/**
 * @description: (一账通同步客户属性-->CRM . 以下字段 区别于个人客户、机构客户。同步属性有所区别)
 * <AUTHOR>
 * @date 2024/9/25 15:58
 * @since JDK 1.8
 */
@Data
public class SyncHboneAttrInfoVO {

    /**
     * 国籍
     */
    private String nationCode;

    /**
     * 省份代码
     */
    private String provCode;

    /**
     * 城市代码
     */
    private String cityCode;

    /**
     * 县代码
     */
    private String countyCode;

    /**
     * 地址 digest
     */
    private String addrDigest;

    /**
     * 地址明文
     */
    private String addr;

    /**
     * 地址掩码
     */
    private String addrMask;

}