/**
 * Copyright (c) 2024, <PERSON><PERSON><PERSON><PERSON> HOWBUY INVESTMENT MANAGEMENT Co., Ltd.
 * All right reserved.
 * <p>
 * THIS IS UNPUBLISHED PROPRIETARY SOURCE CODE OF HOWBUY INVESTMENT
 * MANAGEMENT CO., LTD.  THE CONTENTS OF THIS FILE MAY NOT BE DISCLOSED
 * TO THIRD PARTIES, COPIED OR DUPLICATED IN ANY FORM, IN WHOLE OR IN PART,
 * WITHOUT THE PRIOR WRITTEN PERMISSION OF HOWBUY INVESTMENT MANAGEMENT
 * CO., LTD.
 */
package com.howbuy.crm.account.service.service.beisen;

import com.alibaba.excel.annotation.ExcelProperty;
import com.howbuy.crm.account.client.request.beisen.BeisenPosLevelConfigRequest;
import com.howbuy.crm.account.client.response.ExportToFileVO;
import com.howbuy.crm.account.client.response.beisen.CmBeisenPosLevelConfigListVO;
import com.howbuy.crm.account.client.response.beisen.CmBeisenPosLevelConfigVO;
import com.howbuy.crm.account.dao.bo.beisenposlevel.CmBeisenPosLevelConfigBO;
import com.howbuy.crm.account.dao.po.beisenposlevel.CmBeisenPosLevelConfigPO;
import com.howbuy.crm.account.service.commom.utils.ExportUtil;
import com.howbuy.crm.account.service.repository.beisen.CmBeisenPosLevelConfigRepository;
import lombok.Data;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import java.io.Serializable;
import java.util.ArrayList;
import java.util.List;
import java.util.Objects;
import java.util.stream.Collectors;

/**
 * @description: (好买北森职级映射service)
 * <AUTHOR>
 * @date 2024/10/24 10:12
 * @since JDK 1.8
 */
@Slf4j
@Service
public class CmBeisenPosLevelConfigService {
    @Autowired
    private CmBeisenPosLevelConfigRepository cmBeisenPosLevelConfigRepository;
    /**
     * ID错误信息
     */
    private static final String ERROR_MESSAGE_ID = "id为空";
    /**
     * 操作失败信息
     */
    private static final String ERROR_MESSAGE = "操作失败";
    /**
     * 操作成功信息
     */
    private static final String SUCCESS_MESSAGE = "操作成功";
    /**
     * 职级映射导出菜单名
     */
    private static final String EXPORT_MENU_NAME="职级映射";

    /**
     * @description:(查询北森职级映射列表数据)
     * @param request
     * @return java.util.List<com.howbuy.crm.account.client.response.beisen.CmBeisenPosLevelConfigVO>
     * @author: shijie.wang
     * @date: 2024/10/24 14:06
     * @since JDK 1.8
     */
    public CmBeisenPosLevelConfigListVO queryBeisenPosLevelConfigList(BeisenPosLevelConfigRequest request) {
        CmBeisenPosLevelConfigListVO result = new CmBeisenPosLevelConfigListVO();
        CmBeisenPosLevelConfigPO po = new CmBeisenPosLevelConfigPO();
        transRequestToPo(request, po);
        List<CmBeisenPosLevelConfigBO> boList = cmBeisenPosLevelConfigRepository.queryBeisenPosLevelConfigList(po);
        if(CollectionUtils.isEmpty(boList)){
            log.info("queryBeisenPosLevelConfigList| boList is null");
            return result;
        }
        List<CmBeisenPosLevelConfigVO> resultList = boList.stream().map(bo -> {
                CmBeisenPosLevelConfigVO vo = new CmBeisenPosLevelConfigVO();
                transRequestToPo(bo, vo);
                return vo;
        }).collect(Collectors.toList());
        result.setList(resultList);
        return result;
    }

    /**
     * @description:(保存好买北森职级配置表信息)
     * @param request
     * @return java.lang.String
     * @author: shijie.wang
     * @date: 2024/10/24 14:37
     * @since JDK 1.8
     */
    public String saveBeisenPosLevelConfig(BeisenPosLevelConfigRequest request) {
        try{
            CmBeisenPosLevelConfigPO po = new CmBeisenPosLevelConfigPO();
            transRequestToPo(request, po);
            cmBeisenPosLevelConfigRepository.savePosLevelConfig(po);
        }catch (Exception e){
            log.error("保存好买北森职级配置表信息失败",e);
            return ERROR_MESSAGE;
        }
        return SUCCESS_MESSAGE;
    }

    /**
     * @description:(通过id删除好买北森职级配置表信息)
     * @param request
     * @return java.lang.String
     * @author: shijie.wang
     * @date: 2024/10/24 14:36
     * @since JDK 1.8
     */
    public String deleteBeisenPosLevelConfig(BeisenPosLevelConfigRequest request) {
        try {
            Long id = request.getId();
            if(Objects.isNull(id)){
                return ERROR_MESSAGE_ID;
            }
            cmBeisenPosLevelConfigRepository.deletePosLevelConfig(id);
        }catch (Exception e){
            log.error("通过id删除好买北森职级配置表信息失败",e);
            return ERROR_MESSAGE;
        }
        return SUCCESS_MESSAGE;
    }

    /**
     * @description:(查询详细数据)
     * @param request
     * @return com.howbuy.crm.account.client.response.beisen.CmBeisenPosLevelConfigVO
     * @author: shijie.wang
     * @date: 2024/10/24 17:05
     * @since JDK 1.8
     */
    public CmBeisenPosLevelConfigVO queryBeisenPosLevelConfigDetail(BeisenPosLevelConfigRequest request){
        CmBeisenPosLevelConfigVO vo = new CmBeisenPosLevelConfigVO();
        Long id = request.getId();
        if(Objects.isNull(id)){
            return vo;
        }
        CmBeisenPosLevelConfigBO bo = cmBeisenPosLevelConfigRepository.selectPosLevelConfigById(id);
        if(Objects.isNull(bo)){
            return vo;
        }
        transRequestToPo(bo, vo);
        return vo;
    }

    /**
     * @description:(北森职级导出)
     * @param request
     * @return com.howbuy.crm.account.client.response.ExportToFileVO
     * @author: shijie.wang
     * @date: 2024/10/29 17:28
     * @since JDK 1.8
     */
    public ExportToFileVO export(BeisenPosLevelConfigRequest request) {
        CmBeisenPosLevelConfigListVO results = queryBeisenPosLevelConfigList(request);
        List<CmBeisenPosLevelConfExportVO> exportList = new ArrayList<>();
        List<CmBeisenPosLevelConfigVO> list = results.getList();
        if(CollectionUtils.isEmpty(list)){
            return new ExportToFileVO();
        }
        results.getList().forEach(conf ->{
            CmBeisenPosLevelConfExportVO exportVO = new CmBeisenPosLevelConfExportVO();
            transRequestToPo(conf, exportVO);
            exportList.add(exportVO);
        });
        return ExportUtil.export(exportList, EXPORT_MENU_NAME, CmBeisenPosLevelConfExportVO.class);
    }

    /**
     * @description:(来源数据转成目标数据)
     * @param source
     * @param target
     * @return void
     * @author: shijie.wang
     * @date: 2024/10/24 17:04
     * @since JDK 1.8
     */
    private <T,V> void transRequestToPo(T source, V target) {
        if(Objects.nonNull(source)){
            BeanUtils.copyProperties(source, target);
        }
    }

    /**
     * @description:(导出VO)
     * @param
     * @return
     * @author: shijie.wang
     * @date: 2024/11/13 15:05
     * @since JDK 1.8
     */
    @Data
    public static class CmBeisenPosLevelConfExportVO implements Serializable {
        private static final long serialVersionUID = 1L;
        /**
         * 职级编码（北森）
         */
        @ExcelProperty({"职级编码（北森）"})
        private String positionsLevelBeisen;
        /**
         * 职级名称(北森)
         */
        @ExcelProperty({"职级名称(北森)"})
        private String positionsLevelNameBeisen;
        /**
         * 层级名称（crm）
         */
        @ExcelProperty({"层级名称（crm）"})
        private String userLevelCrmName;
        /**
         * 职级名称（crm）
         */
        @ExcelProperty({"职级名称（crm）"})
        private String positionsLevelCrmName;
        /**
         * 副职名称（crm）
         */
        @ExcelProperty({"副职名称（crm）"})
        private String subPositionsLevelCrmName;
        /**
         * 起始日期
         */
        @ExcelProperty({"起始日期"})
        private String startDate;
        /**
         * 结束日期
         */
        @ExcelProperty({"结束日期"})
        private String endDate;
    }


}