/**
 * Copyright (c) 2023, <PERSON><PERSON><PERSON><PERSON> HOWBUY INVESTMENT MANAGEMENT Co., Ltd.
 * All right reserved.
 * <p>
 * THIS IS UNPUBLISHED PROPRIETARY SOURCE CODE OF HOWBUY INVESTMENT
 * MANAGEMENT CO., LTD.  THE CONTENTS OF THIS FILE MAY NOT BE DISCLOSED
 * TO THIRD PARTIES, COPIED OR DUPLICATED IN ANY FORM, IN WHOLE OR IN PART,
 * WITHOUT THE PRIOR WRITTEN PERMISSION OF HOWBUY INVESTMENT MANAGEMENT
 * CO., LTD.
 */
package com.howbuy.crm.account.service.controller.dictionary;

import com.howbuy.crm.account.client.response.Response;
import com.howbuy.crm.account.client.response.dictionary.CityListVO;
import com.howbuy.crm.account.client.response.dictionary.CountryInfoVO;
import com.howbuy.crm.account.client.response.dictionary.ParamMobileAreaListVO;
import com.howbuy.crm.account.service.service.dictionary.DictionaryService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.ResponseBody;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;

/**
 * @description: 字典值控制层
 * <AUTHOR>
 * @date 2023/12/26 9:40
 * @since JDK 1.8
 */

@RestController
@RequestMapping("/dictionary")
public class DictionaryController {

    @Autowired
    private DictionaryService dictionaryService;


    /**
     * @api {GET} /dictionary/getcountrylist getCountry()
     * @apiVersion 1.0.0
     * @apiGroup DictionaryController
     * @apiName getCountry()
     * @apiSuccess (响应结果) {String} code 状态码
     * @apiSuccess (响应结果) {String} description 描述信息
     * @apiSuccess (响应结果) {Array} data 数据封装
     * @apiSuccess (响应结果) {String} data.chineseName 中文名
     * @apiSuccess (响应结果) {String} data.firstPinyin 拼音首字母
     * @apiSuccess (响应结果) {String} data.iso31662 iso31662
     * @apiSuccess (响应结果) {String} data.shortCode 代码简称
     * @apiSuccess (响应结果) {String} data.englishName 英文名
     * @apiSuccessExample 响应结果示例
     * {"code":"uuYu","data":[{"englishName":"n87oQBv","chineseName":"P4O","firstPinyin":"ZCzOIm9pu","iso31662":"193r5","shortCode":"BC"}],"description":"Q"}
     */
    @GetMapping("/getcountrylist")
    @ResponseBody
    public Response<List<CountryInfoVO>> getCountry() {
        return Response.ok(dictionaryService.getCountryList());
    }


    /**
     * @api {GET} /dictionary/getcitylist getCityList()
     * @apiVersion 1.0.0
     * @apiGroup DictionaryController
     * @apiName getCityList()
     * @apiSuccess (响应结果) {String} code 状态码
     * @apiSuccess (响应结果) {String} description 描述信息
     * @apiSuccess (响应结果) {Array} data 数据封装
     * @apiSuccess (响应结果) {String} data.mc 名称
     * @apiSuccess (响应结果) {String} data.szm 首字母
     * @apiSuccess (响应结果) {String} data.dm 地区代码
     * @apiSuccess (响应结果) {Array} data.dataList 省市区的列表的数据实体类
     * @apiSuccess (响应结果) {String} data.dataList.mc 名称
     * @apiSuccess (响应结果) {String} data.dataList.szm 首字母
     * @apiSuccess (响应结果) {String} data.dataList.dm 地区代码
     * @apiSuccess (响应结果) {Array} data.dataList.dataList 省市区的列表的数据实体类
     * @apiSuccessExample 响应结果示例
     * {"code":"uryU2lVQO","data":[{"mc":"P2F3vA","szm":"p7WPffow","dataList":[{"mc":"E","szm":"IHvd3Q","dataList":[],"dm":"8GV"}],"dm":"5KVxUfP"}],"description":"FijgO"}
     */
    @GetMapping("/getcitylist")
    @ResponseBody
    public Response<List<CityListVO>> getCityList() {
        return Response.ok(dictionaryService.getCityListVO());
    }

    /**
     * @api {GET} /dictionary/getmobileareacodelist getMobileAreaCodeList()
     * @apiVersion 1.0.0
     * @apiGroup DictionaryController
     * @apiName getMobileAreaCodeList()
     * @apiSuccess (响应结果) {String} code 状态码
     * @apiSuccess (响应结果) {String} description 描述信息
     * @apiSuccess (响应结果) {Object} data 数据封装
     * @apiSuccess (响应结果) {Array} data.index 索引
     * @apiSuccess (响应结果) {Array} data.itemList 数据
     * @apiSuccess (响应结果) {String} data.itemList.type 索引
     * @apiSuccess (响应结果) {String} data.itemList.areacode 地区码
     * @apiSuccess (响应结果) {String} data.itemList.areaname 地区名称
     * @apiSuccessExample 响应结果示例
     * {"code":"DBk","data":{"index":["9Sfaz2gs"],"itemList":[{"areaname":"D8jIj","type":"zMk4CfRRe","areacode":"5"}]},"description":"1VmQE5I7"}
     */
    @GetMapping("/getmobileareacodelist")
    @ResponseBody
    public Response<ParamMobileAreaListVO> getMobileAreaCodeList() {
        return dictionaryService.getMobileAreaCodeList2();
    }

}