/**
 * Copyright (c) 2023, <PERSON><PERSON><PERSON><PERSON> HOWBUY INVESTMENT MANAGEMENT Co., Ltd.
 * All right reserved.
 * <p>
 * THIS IS UNPUBLISHED PROPRIETARY SOURCE CODE OF HOWBUY INVESTMENT
 * MANAGEMENT CO., LTD.  THE CONTENTS OF THIS FILE MAY NOT BE DISCLOSED
 * TO THIRD PARTIES, COPIED OR DUPLICATED IN ANY FORM, IN WHOLE OR IN PART,
 * WITHOUT THE PRIOR WRITTEN PERMISSION OF HOWBUY INVESTMENT MANAGEMENT
 * CO., LTD.
 */
package com.howbuy.crm.account.service.service.custinfo;

import com.alibaba.fastjson.JSON;
import com.howbuy.common.utils.StringUtil;
import com.howbuy.crm.account.client.enums.DisCodeEnum;
import com.howbuy.crm.account.client.request.custinfo.CreateCustByHboneRequest;
import com.howbuy.crm.account.client.request.custinfo.HboneAcctRelationOptRequest;
import com.howbuy.crm.account.client.response.Response;
import com.howbuy.crm.account.client.response.custinfo.*;
import com.howbuy.crm.account.dao.bo.custinfo.CmConsCustSimpleBO;
import com.howbuy.crm.account.dao.bo.custinfo.CmConscustForAnalyseBO;
import com.howbuy.crm.account.service.business.custinfo.CmCustProcessBusinessService;
import com.howbuy.crm.account.service.business.custinfo.HboneCustBindBusinessService;
import com.howbuy.crm.account.service.commom.constant.Constants;
import com.howbuy.crm.account.service.outerservice.acct.HboneAcctInfoOuterService;
import com.howbuy.crm.account.service.outerservice.acct.HboneDisAcctInfoOuterService;
import com.howbuy.crm.account.service.repository.custinfo.AbnormalCustRepository;
import com.howbuy.crm.account.service.repository.custinfo.ConsCustInfoRepository;
import com.howbuy.crm.account.service.vo.custinfo.HboneMessageCustInfoVO;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

/**
 * @description: (一账通 客户信息service)
 * <AUTHOR>
 * @date 2023/12/13 16:41
 * @since JDK 1.8
 */
@Service
@Slf4j
public class HboneCustInfoService {


    @Autowired
    private HboneAcctInfoOuterService hboneAcctInfoOuterService;

    @Autowired
    private HboneDisAcctInfoOuterService hboneDisAcctInfoOuterService;

    @Autowired
    private ConsCustInfoRepository consCustInfoRepository;
    @Autowired
    private HboneCustBindBusinessService hboneCustBindBusinessService;

    @Autowired
    private CmCustProcessBusinessService processBusinessService;

    @Autowired
    private AbnormalCustRepository abnormalCustRepository;



    /**
     * @description:(查询 账户中心  客户信息 敏感信息)
     * @param hboneNo
     * @return com.howbuy.crm.account.client.response.custinfo.HboneAcctCustSensitiveInfoVO
     * @author: haoran.zhang
     * @date: 2023/12/21 17:11
     * @since JDK 1.8
     */
    public HboneAcctCustSensitiveInfoVO queryHboneCustSensitiveInfo(String hboneNo){
        return hboneAcctInfoOuterService.queryHboneCustSensitiveInfo(hboneNo);
    }


    /**
     * @description:(获取分销层  敏感信息)
     * @param hboneNo
     * @param disCodeEnum
     * @return com.howbuy.crm.account.client.response.custinfo.HboneDisCustSensitiveInfoVO
     * @author: haoran.zhang
     * @date: 2023/12/21 17:47
     * @since JDK 1.8
     */
    public HboneDisCustSensitiveInfoVO queryHboneDisSensitiveInfo(String hboneNo, DisCodeEnum disCodeEnum){
        return hboneDisAcctInfoOuterService.queryHboneDisSensitiveInfo(hboneNo,disCodeEnum);
    }

    /**
     * @description:(查询 香港账户中心  客户手机号 敏感信息)
     * @param hboneNo
     * @return java.lang.String
     * @author: haoran.zhang
     * @date: 2023/12/21 17:11
     * @since JDK 1.8
     */
    public String queryHboneSensitiveMobile(String hboneNo){
        return hboneAcctInfoOuterService.queryHboneSensitiveMobile(hboneNo);
    }



    /**
     * @description:(根据一账通号 查询账户中心客户信息)
     * @param custNo 投顾客户号
     * @return com.howbuy.crm.account.client.response.Response<com.howbuy.crm.account.client.response.custinfo.HboneAcctCustInfoVO>
     * @author: haoran.zhang
     * @date: 2023/12/13 14:42
     * @since JDK 1.8
     */
    public HboneAcctCustInfoVO queryHboneAcctInfoByCustNo(String custNo){

        //根据投顾客户号查询投顾客户信息
        CmConsCustSimpleBO custInfo=consCustInfoRepository.queryCustSimpleInfo(custNo);
        if(custInfo==null || StringUtil.isEmpty(custInfo.getHboneNo())){
            return null;
        }
        return queryHboneAcctInfo(custInfo.getHboneNo());
    }

   
    /**
     * @description:(根据一账通号 查询账户中心客户信息 )
     * @param hboneNo
     * @return com.howbuy.crm.account.client.response.custinfo.HboneAcctCustDetailInfoVO
     * @author: haoran.zhang
     * @date: 2023/12/20 19:05
     * @since JDK 1.8
     */
    public HboneAcctCustDetailInfoVO queryHboneCustDetailInfo(String hboneNo){
        return hboneAcctInfoOuterService.queryHboneCustDetailInfo(hboneNo);
    }


    /**
     * @description:(根据一账通号 查询账户中心客户信息)
     * @param hboneNo
     * @return com.howbuy.crm.account.client.response.Response<com.howbuy.crm.account.client.response.custinfo.HboneAcctCustInfoVO>
     * @author: haoran.zhang
     * @date: 2023/12/13 14:42
     * @since JDK 1.8
     */
    public HboneAcctCustInfoVO queryHboneAcctInfo(String hboneNo){
        return hboneAcctInfoOuterService.queryHboneAcctInfo(hboneNo);
    }

    /**
     * @description:(根据一账通号 查询账户中心客户的 kyc信息)
     * @param hboneNo
     * @return com.howbuy.crm.account.client.response.Response<com.howbuy.crm.account.client.response.custinfo.HboneAcctCustInfoVO>
     * @throws
     * @since JDK 1.8
     */
    public  Response<HboneCustKycInfoVO>  queryCustKycInfo(String hboneNo, DisCodeEnum disCodeEnum){
        return hboneAcctInfoOuterService.queryCustKycInfo(hboneNo,disCodeEnum);
    }


    /**
     * 客户是否实名
     * 客户号有一账通账号，并且该一账通账号账户中心返回信息中 idNoDigest 不为空
     * @param custNo 投顾客户号
     * @return  boolean
     */
    public boolean isRealName(String custNo) {

        CmConsCustSimpleBO custInfo = consCustInfoRepository.queryCustSimpleInfo(custNo);
        //客户无hboneNo.
        if (custInfo == null || StringUtil.isEmpty(custInfo.getHboneNo())) {
            return false;
        }
        //根据一账通账号查询 账户中心信息
        HboneAcctCustInfoVO acctInfo = queryHboneAcctInfo(custInfo.getHboneNo());
        return isRealName(acctInfo);
    }

    /**
     * @description:(一账通账户 是否实名 )
     * @param acctInfo
     * @return boolean
     * @author: haoran.zhang
     * @date: 2023/12/14 13:17
     * @since JDK 1.8
     */
    public boolean isRealName(HboneAcctCustInfoVO acctInfo){
        return acctInfo!=null && StringUtil.isNotBlank(acctInfo.getIdNoDigest());
    }


    /**
     * @description: 根据投顾客户号查询一账通客户信息
     * @param custNo 投顾客户号
     * @return com.howbuy.crm.account.client.response.custinfo.HboneAcctCustDetailInfoVO 一账通客户信息
     * @author: jin.wang03
     * @date: 2023/12/21 19:35
     * @since JDK 1.8
     */
    public HboneAcctCustDetailInfoVO queryHboneAcctDetailInfoByCustNo(String custNo) {
        //根据投顾客户号查询投顾客户信息
        CmConsCustSimpleBO custInfo = consCustInfoRepository.queryCustSimpleInfo(custNo);
        if (custInfo == null || StringUtil.isEmpty(custInfo.getHboneNo())) {
            return null;
        }

        return hboneAcctInfoOuterService.queryHboneCustDetailInfo(custInfo.getHboneNo());
    }

    
    /**
     * @description:(解绑一账通 关联关系)
     * @param bindOptRequest
     * @return com.howbuy.crm.account.client.response.Response<java.lang.String>
     * @author: haoran.zhang
     * @date: 2024/1/18 9:46
     * @since JDK 1.8
     */
    public Response<String> unBindHkTxAcct(HboneAcctRelationOptRequest bindOptRequest) {
         return hboneCustBindBusinessService.unBindHboneTxAcct(bindOptRequest);
    }


    /**
     * @description:(根据一账通号 从账户中心信息 创建客户)
     * @param request
     * @return com.howbuy.crm.account.client.response.Response<java.lang.String>
     * @author: haoran.zhang
     * @date: 2025/6/5 18:08
     * @since JDK 1.8
     */
    public Response<String>  createCustInfoByHbone(CreateCustByHboneRequest request){
        HboneMessageCustInfoVO analyseVo=new HboneMessageCustInfoVO();
        analyseVo.setDisCode(request.getDisCode());
        analyseVo.setOperateChannel(request.getOperateChannel());
        analyseVo.setAbnormalSource(request.getOperateSource());
        analyseVo.setHboneNo(request.getHboneNo());
        analyseVo.setCreator(Constants.OPERATOR_SYS);
        //新增客户
        Response<String> createResp
                =processBusinessService.createCustInfoByHbone(analyseVo);
        log.info("一账通开户消息处理： 一账通号[{}] 新增客户，执行结果：{}",request.getHboneNo(), JSON.toJSONString(createResp));
        String custNo=createResp.getData();
        if(StringUtil.isEmpty(custNo)){
            return Response.fail(createResp.getDescription());
        }
        //新增客户 传递 作为后续 待处理 对象
        CmConscustForAnalyseBO processedCustInfo=abnormalCustRepository.queryCustBOByCustNo(custNo);

        //关联一账通号 vs 投顾客户号
        processBusinessService.associateHboneAcctRelation(processedCustInfo,analyseVo);

        return  createResp;
    }

}