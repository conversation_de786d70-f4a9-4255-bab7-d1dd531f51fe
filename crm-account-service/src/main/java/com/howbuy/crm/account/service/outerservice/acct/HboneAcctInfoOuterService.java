/**
 * Copyright (c) 2023, <PERSON><PERSON><PERSON><PERSON> HOWBUY INVESTMENT MANAGEMENT Co., Ltd.
 * All right reserved.
 * <p>
 * THIS IS UNPUBLISHED PROPRIETARY SOURCE CODE OF HOWBUY INVESTMENT
 * MANAGEMENT CO., LTD.  THE CONTENTS OF THIS FILE MAY NOT BE DISCLOSED
 * TO THIRD PARTIES, COPIED OR DUPLICATED IN ANY FORM, IN WHOLE OR IN PART,
 * WITHOUT THE PRIOR WRITTEN PERMISSION OF HOWBUY INVESTMENT MANAGEMENT
 * CO., LTD.
 */
package com.howbuy.crm.account.service.outerservice.acct;

import com.howbuy.acccenter.facade.query.queryacccustinfo.QueryAccCustInfoFacade;
import com.howbuy.acccenter.facade.query.queryacccustinfo.QueryAccCustInfoRequest;
import com.howbuy.acccenter.facade.query.queryacccustinfo.QueryAccCustInfoResponse;
import com.howbuy.acccenter.facade.query.queryacccustinfo.bean.AccCustInfoBean;
import com.howbuy.acccenter.facade.query.queryhboneinfo.QueryAccHboneInfoFacade;
import com.howbuy.acccenter.facade.query.queryhboneinfo.QueryAccHboneInfoRequest;
import com.howbuy.acccenter.facade.query.queryhboneinfo.QueryAccHboneInfoResponse;
import com.howbuy.acccenter.facade.query.querytxacctbyhbonefacade.QueryTxAcctByHboneFacade;
import com.howbuy.acccenter.facade.query.querytxacctbyhbonefacade.QueryTxAcctByHboneRequest;
import com.howbuy.acccenter.facade.query.querytxacctbyhbonefacade.QueryTxAcctByHboneResponse;
import com.howbuy.acccenter.facade.query.sensitive.custinfo.QueryCustSensitiveInfoFacade;
import com.howbuy.acccenter.facade.query.sensitive.custinfo.QueryCustSensitiveInfoRequest;
import com.howbuy.acccenter.facade.query.sensitive.custinfo.QueryCustSensitiveInfoResponse;
import com.howbuy.acccenter.facade.query.sensitive.custinfo.bean.CustSensitiveInfo;
import com.howbuy.acccenter.facade.query.sensitive.mobile.QueryCustMobileFacade;
import com.howbuy.acccenter.facade.query.sensitive.mobile.QueryCustMobileRequest;
import com.howbuy.acccenter.facade.query.sensitive.mobile.QueryCustMobileResponse;
import com.howbuy.acccenter.facade.trade.kycinfo.KycInfoFacade;
import com.howbuy.acccenter.facade.trade.kycinfo.KycInfoRequest;
import com.howbuy.acccenter.facade.trade.kycinfo.KycInfoResponse;
import com.howbuy.cc.center.feature.question.domain.ExamType;
import com.howbuy.common.utils.StringUtil;
import com.howbuy.crm.account.client.enums.DisCodeEnum;
import com.howbuy.crm.account.client.response.Response;
import com.howbuy.crm.account.client.response.custinfo.*;
import com.howbuy.crm.account.service.commom.constant.Constants;
import lombok.extern.slf4j.Slf4j;
import org.apache.dubbo.config.annotation.DubboReference;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Service;
import org.springframework.util.Assert;

/**
 * @description: (好买账户中心- 一账通 outer service)
 * <AUTHOR>
 * @date 2023/12/8 20:15
 * @since JDK 1.8
 */
@Slf4j
@Service
public class HboneAcctInfoOuterService  extends AbstractAcctOuterService {

    @DubboReference(registry = "acc-center-server", check = false)
    private QueryAccHboneInfoFacade queryAccHboneInfoFacade;

    @DubboReference(registry = "acc-center-server", check = false)
    private QueryAccCustInfoFacade queryAccCustInfoFacade;

    @DubboReference(registry = "acc-center-server", check = false)
        private KycInfoFacade kycInfoFacade;
    @DubboReference(registry = "acc-center-server", check = false)
    private QueryCustSensitiveInfoFacade queryCustSensitiveInfoFacade;

    @DubboReference(registry = "acc-center-server", check = false)
    private QueryCustMobileFacade queryMobileFacade;


    @DubboReference(registry = "acc-center-server", check = false)
    private QueryTxAcctByHboneFacade queryTxAcctByHboneFacade;


    /**
     * @description:(根据一账通号查询交易账号接口)
     * @param hboneNo
     * @return com.howbuy.crm.account.client.response.custinfo.HboneTxAcctInfoVO
     * @author: haoran.zhang
     * @date: 2024/1/17 16:35
     * @since JDK 1.8
     */
    public HboneTxAcctInfoVO queryTxAcctInfo(String hboneNo){
        QueryTxAcctByHboneRequest request=new QueryTxAcctByHboneRequest();
        request.setHboneNo(hboneNo);
        QueryTxAcctByHboneResponse  acctResp=queryTxAcctByHboneFacade.execute(request);
        if(!isAcctSuccess(acctResp) ){
            return null;
        }
        HboneTxAcctInfoVO infoVo=new HboneTxAcctInfoVO();
        infoVo.setTxAcctNo(acctResp.getTxAcctNo());
        infoVo.setCustNo(acctResp.getCustNo());
        infoVo.setStat(acctResp.getStat());
        return infoVo;
    }





    /**
     * @description:(查询 账户中心  客户信息 敏感信息)
     * @param hboneNo
     * @return com.howbuy.crm.account.client.response.custinfo.HboneAcctCustSensitiveInfoVO
     * @author: haoran.zhang
     * @date: 2023/12/21 17:11
     * @since JDK 1.8
     */
    public HboneAcctCustSensitiveInfoVO queryHboneCustSensitiveInfo(String hboneNo){
        Assert.notNull(hboneNo,"一账通不能为空！");
        QueryCustSensitiveInfoRequest sensitiveRequest = new QueryCustSensitiveInfoRequest();
        sensitiveRequest.setHboneNo(hboneNo);
        QueryCustSensitiveInfoResponse resp = queryCustSensitiveInfoFacade.execute(sensitiveRequest);
        if(!isAcctSuccess(resp) || resp.getCustSensitiveInfo()==null){
            return null;
        }
        return transferSensitiveInfo(resp);
    }

    /**
     * @description:(查询 香港账户中心  客户手机号 敏感信息)
     * @param hboneNo
     * @return java.lang.String
     * @author: haoran.zhang
     * @date: 2023/12/21 17:11
     * @since JDK 1.8
     */
    public String queryHboneSensitiveMobile(String hboneNo){
        Assert.notNull(hboneNo,"一账通不能为空！");
        QueryCustMobileRequest sensitiveRequest = new QueryCustMobileRequest();
        sensitiveRequest.setHboneNo(hboneNo);
        QueryCustMobileResponse resp = queryMobileFacade.execute(sensitiveRequest);
        if(!isAcctSuccess(resp) || resp.getCustMobile()==null){
            return null;
        }
        return resp.getCustMobile().getMobile();
    }

    /**
     * 账户中心 对象 --> crm 对象 转译
     * @param resp
     * @return
     */
    private static HboneAcctCustSensitiveInfoVO transferSensitiveInfo(QueryCustSensitiveInfoResponse resp) {
        CustSensitiveInfo sensitiveInfo= resp.getCustSensitiveInfo();
        HboneAcctCustSensitiveInfoVO infoVo=new HboneAcctCustSensitiveInfoVO();
        infoVo.setHboneNo(sensitiveInfo.getHboneNo());
        infoVo.setCustName(sensitiveInfo.getCustName());
        infoVo.setCustNo(sensitiveInfo.getCustNo());
        infoVo.setIdType(sensitiveInfo.getIdType());
        infoVo.setIdNo(sensitiveInfo.getIdNo());
        infoVo.setCorpIdNo(sensitiveInfo.getCorpIdNo());
        infoVo.setCorporation(sensitiveInfo.getCorporation());
        infoVo.setMobileMask(sensitiveInfo.getMobileMask());
        infoVo.setCustIMEI(sensitiveInfo.getCustIMEI());
        // 将 accCustInfo 所有对象 全部  赋值给 infoVo
//        BeanUtils.copyProperties(sensitiveInfo,infoVo);
        return infoVo;
    }


    /**
     * @description:(根据一账通号 查询 一账通客户详细信息)
     * @param hboneNo
     * @return com.howbuy.crm.account.client.response.custinfo.HboneAcctCustDetailInfoVO
     * @author: haoran.zhang
     * @date: 2023/12/20 19:04
     * @since JDK 1.8
     */
    public HboneAcctCustDetailInfoVO queryHboneCustDetailInfo(String hboneNo){
        Assert.notNull(hboneNo,"一账通不能为空！");
        QueryAccCustInfoRequest queryAccCustInfoRequest = new QueryAccCustInfoRequest();
        queryAccCustInfoRequest.setHboneNo(hboneNo);
        QueryAccCustInfoResponse resp = queryAccCustInfoFacade.execute(queryAccCustInfoRequest);
        if(!isAcctSuccess(resp) || resp.getAccCustInfo()==null){
            return null;
        }
        AccCustInfoBean accCustInfo=resp.getAccCustInfo();
        HboneAcctCustDetailInfoVO infoVo=new HboneAcctCustDetailInfoVO();
        // 将 accCustInfo 所有对象 全部  赋值给 infoVo
        BeanUtils.copyProperties(accCustInfo,infoVo);
        //hbone信息 默认处理  字段
        infoVo.setMobileAreaCode(Constants.DEFAULT_MOBILE_AREA_CODE);
        infoVo.setIdSignAreaCode(Constants.DEFAULT_ID_SIGN_AREA_CODE);
        return infoVo;
    }


    /**
     * @description:(根据一账通号 查询账户中心客户信息)
     * @param hboneNo
     * @return com.howbuy.crm.account.client.response.Response<com.howbuy.crm.account.client.response.custinfo.HboneAcctCustInfoVO>
     * @author: haoran.zhang
     * @date: 2023/12/13 14:42
     * @since JDK 1.8
     */
    public HboneAcctCustInfoVO queryHboneAcctInfo(String hboneNo) {
        Assert.notNull(hboneNo);
        QueryAccHboneInfoRequest request = new QueryAccHboneInfoRequest();
        request.setHboneNo(hboneNo);
        return queryAcctInfoByRequest(request);
    }


    public  HboneAcctCustInfoVO queryAcctInfoByRequest(QueryAccHboneInfoRequest request) {
        QueryAccHboneInfoResponse resp = queryAccHboneInfoFacade.execute(request);
        if(isAcctSuccess(resp)) {
            HboneAcctCustInfoVO infoDto = new HboneAcctCustInfoVO();
            BeanUtils.copyProperties(resp, infoDto);
            //hbone信息 默认处理  字段
            infoDto.setMobileAreaCode(Constants.DEFAULT_MOBILE_AREA_CODE);
            infoDto.setIdSignAreaCode(Constants.DEFAULT_ID_SIGN_AREA_CODE);
            return infoDto;
        }
        return null;
    }


    /**
     * @description:(根据一账通号 查询账户中心客户的 kyc信息)
     * @param hboneNo
     * @return com.howbuy.crm.account.client.response.Response<com.howbuy.crm.account.client.response.custinfo.HboneAcctCustInfoVO>
     * @throws
     * @since JDK 1.8
     */
    public  Response<HboneCustKycInfoVO>  queryCustKycInfo(String hboneNo, DisCodeEnum disCodeEnum){
        KycInfoRequest queryUserRequest = new KycInfoRequest();
        queryUserRequest.setHboneNo(hboneNo);
        queryUserRequest.setDisCode(disCodeEnum.getCode());
        KycInfoResponse kycInfoResponse = kycInfoFacade.execute(queryUserRequest);


        if(kycInfoResponse==null){
            return Response.fail("账户中心kyc信息空！");
        }
        if(!isAcctSuccess(kycInfoResponse)){
            return Response.fail(kycInfoResponse.getDescription());
        }

        HboneCustKycInfoVO infoDto=new HboneCustKycInfoVO();
        infoDto.setHboneNo(hboneNo);
        infoDto.setDisCode(disCodeEnum.getCode());

        String riskToleranceExamType=kycInfoResponse.getRiskToleranceExamType();

        infoDto.setInvestorType(kycInfoResponse.getInvestorType());
        infoDto.setRiskToleranceExamType(riskToleranceExamType);
        infoDto.setRiskToleranceExpire(kycInfoResponse.getRiskToleranceExpire());
        infoDto.setRiskToleranceLevel(kycInfoResponse.getRiskToleranceLevel());

        boolean isSignFlag="1".equals(kycInfoResponse.getSignFlag());
        infoDto.setSignFlag(isSignFlag);


        //入会 做过问卷，并且问卷类型非零售的   且 合格投资人认定（私募）签订
        boolean isJoinClub= StringUtil.isNotBlank(riskToleranceExamType)
                && ( !ExamType.RETAIL.getValue().equals(riskToleranceExamType) )
                && isSignFlag ;
        infoDto.setJoinClub(isJoinClub);
        return Response.ok("",infoDto);
    }


}