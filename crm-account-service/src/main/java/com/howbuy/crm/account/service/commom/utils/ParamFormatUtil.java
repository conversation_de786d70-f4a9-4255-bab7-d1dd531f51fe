package com.howbuy.crm.account.service.commom.utils;


import com.google.common.collect.Lists;
import com.howbuy.acc.common.ListUtil;
import com.howbuy.common.utils.StringUtil;

import java.util.List;

/**
 * 参数处理Util类
 *
 * @ClassName: ParamUtil
 * @Description:TODOs
 * @author: haoran.zhang
 * @date: 2019-6-26 11:28:45
 * @Copyright: 2019 www.howbuy.com Inc. All rights reserved.
 */
public class ParamFormatUtil {

    /**
     * "\\,"
     */
    public static final String SEPARATOR_COMMA = "\\,";

    /**
     * 页面上的日期格式通常为yyyy-MM-dd.
     * 如果参数为空，返回null,不为空，格式 变为yyyyMMdd
     */
    public static String trimDate(String pageDateStr) {
        return StringUtil.isNotBlank(pageDateStr) ? pageDateStr.replace("-", "") : null;
    }

    /**
     * 页面段参数  逗号分隔 多个属性值。返回List表达
     */
    public static List<String> getParamList(String sourceData, List<String> defaultList) {
        return StringUtil.isNotBlank(sourceData) ? Lists.newArrayList(sourceData.split(SEPARATOR_COMMA)) : defaultList;
    }

    /**
     * 多重List
     */
    public static List<List<String>> getMultiLayerList(String sourceData, List<List<String>> defaultList) {
        return StringUtil.isNotBlank(sourceData) ? ListUtil.splitString(sourceData) : defaultList;
    }

}
