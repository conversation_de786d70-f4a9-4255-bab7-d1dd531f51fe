/**
 * Copyright (c) 2025, <PERSON><PERSON><PERSON><PERSON> HOWBUY INVESTMENT MANAGEMENT Co., Ltd.
 * All right reserved.
 * <p>
 * THIS IS UNPUBLISHED PROPRIETARY SOURCE CODE OF HOWBUY INVESTMENT
 * MANAGEMENT CO., LTD.  THE CONTENTS OF THIS FILE MAY NOT BE DISCLOSED
 * TO THIRD PARTIES, COPIED OR DUPLICATED IN ANY FORM, IN WHOLE OR IN PART,
 * WITHOUT THE PRIOR WRITTEN PERMISSION OF HOWBUY INVESTMENT MANAGEMENT
 * CO., LTD.
 */
package com.howbuy.crm.account.service.service.consultantexp;

import com.alibaba.fastjson.JSON;
import com.howbuy.crm.account.client.enums.consultantexp.UserLevelEnum;
import com.howbuy.crm.account.dao.mapper.constant.HbConstantMapper;
import com.howbuy.crm.account.dao.po.constant.HbConstantPO;
import com.howbuy.crm.account.dao.po.consultantexp.CmConsultantExpPO;
import com.howbuy.crm.account.service.commom.constant.Constants;
import com.howbuy.crm.account.service.domain.OrgLayerInfoDTO;
import com.howbuy.crm.account.service.domain.consultantexp.ConsultantUserLevelDTO;
import com.howbuy.crm.account.service.outerservice.crmcore.OrganazitonOuterService;
import com.howbuy.crm.account.service.repository.consultantexp.CmConsultantExpRepository;
import crm.howbuy.base.utils.StringUtil;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.Arrays;
import java.util.Comparator;
import java.util.List;
import java.util.Objects;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @description: 花名册Service
 * @date 2025/4/10 16:11
 * @since JDK 1.8
 */
@Slf4j
@Component
public class CmConsultantExpService {

    @Autowired
    private CmConsultantExpRepository cmConsultantExpRepository;

    @Autowired
    private HbConstantMapper hbConstantMapper;

    @Autowired
    private OrganazitonOuterService organizerOuterService;

    private static final String HR_POSITIONS_LEVEL = "hrpositionslevel";
    private static final String USER_LEVEL = "userlevel";

    private static final String IC_MANAGER_CODE = "selina.huang";
    private static final String HBC_MANAGER_CODE = "pink.li";
    private static final String HK_MANAGER_CODE = "guoying.wang";


    private static final List<UserLevelEnum> USER_LEVEL_ENUMS = Arrays.asList(
            UserLevelEnum.FINANCIAL_PLANNER,
            UserLevelEnum.DIVISION_MANAGER,
            UserLevelEnum.REGIONAL_EXECUTIVE_VICE_PRESIDENT,
            UserLevelEnum.REGIONAL_PRESIDENT,
            UserLevelEnum.SALES_DIRECTOR
    );

    /**
     * @param consCode 投顾编码
     * @return java.lang.String
     * @description: 根据consCode获取投顾的上级主管
     * @author: jin.wang03
     * @date: 2025/4/10 16:13
     * @since JDK 1.8
     */
    public ConsultantUserLevelDTO getManagerInfoByConsCode(String consCode) {
        // 获取花名册信息
        CmConsultantExpPO cmConsultantExp = cmConsultantExpRepository.getCmConsultantExpByConsCode(consCode);
        log.info("根据投顾编号：{}获取 投顾花名册信息: {}", consCode, JSON.toJSONString(cmConsultantExp));
        if (Objects.isNull(cmConsultantExp) || StringUtils.isBlank(cmConsultantExp.getCurmonthlevel())) {
            log.info("投顾编号: {}在花名册中 不存在，无法获取主管列表", consCode);
            return null;
        }

        // 获取最高层级
        UserLevelEnum highestLevel = getHighestUserLevel(cmConsultantExp.getCurmonthlevel());
        log.info("投顾编号：{}，最高层级: {}", consCode, JSON.toJSONString(highestLevel));
        if (Objects.isNull(highestLevel)) {
            log.info("投顾编号：{}，最高层级为空，无法获取主管列表", consCode);
            return null;
        }

        // 根据不同层级获取上级主管
        String managerConsCode = getManagerIdByConsInfo(cmConsultantExp, highestLevel);
        if (StringUtils.isBlank(managerConsCode)) {
            return null;
        }

        String accompanyHighestLevelByConsCode = getAccompanyHighestLevelByConsCode(managerConsCode);
        return new ConsultantUserLevelDTO(managerConsCode, accompanyHighestLevelByConsCode);
    }


    /**
     * @description: 根据不同层级获取上级主管的投顾编码
     * @param cmConsultantExp 投顾花名册信息
     * @param highestLevel 最高层级
     * @return java.lang.String
     * @author: jin.wang03
     * @date: 2025/4/16 11:23
     * @since JDK 1.8
     */
    private String getManagerIdByConsInfo(CmConsultantExpPO cmConsultantExp, UserLevelEnum highestLevel) {
        OrgLayerInfoDTO orgLayerInfo = organizerOuterService.getOrgLayerInfoByOrgCode(cmConsultantExp.getOutletcode());
        switch (highestLevel) {
            case REGIONAL_PRESIDENT:
                return getSalesDirectorByCenter(cmConsultantExp.getOutletcode(), orgLayerInfo);
            case REGIONAL_EXECUTIVE_VICE_PRESIDENT:
                return getManagerForRegionalVP(cmConsultantExp, orgLayerInfo);
            case DIVISION_MANAGER:
                return getManagerForDivisionManager(cmConsultantExp, orgLayerInfo);
            case FINANCIAL_PLANNER:
                return getManagerForFinancialPlanner(cmConsultantExp, orgLayerInfo);
            default:
                return null;
        }
    }




    /**
     * @description: 根据投顾编码获取[陪访人]、[上级主管]的最高层级
     *
     *    [陪访人]、[上级主管]的层级：当「类型=分总/区副/区总」时，根据陪访人在花名册中的层级，落相应的层级数据。若有多个层级，取最高层级
     *    需求链接：http://dms.intelnal.howbuy.com/pages/viewpage.action?pageId=94517722#id-15%E3%80%81%E6%B2%9F%E9%80%9A%E8%AE%B0%E5%BD%95%E6%94%B9%E9%80%A0-2%E3%80%81%E6%B7%BB%E5%8A%A0%E6%8B%9C%E8%AE%BF%E7%BA%AA%E8%A6%81%E6%A8%A1%E5%9D%97
     *
     * @param consCode 投顾编码
     * @return java.lang.String
     * @author: jin.wang03
     * @date: 2025/4/16 10:08
     * @since JDK 1.8
     */
    public String getAccompanyHighestLevelByConsCode(String consCode) {
        // 获取花名册信息
        CmConsultantExpPO cmConsultantExp = cmConsultantExpRepository.getCmConsultantExpByConsCode(consCode);
        log.info("根据投顾编号：{}获取 投顾花名册信息: {}", consCode, JSON.toJSONString(cmConsultantExp));
        if (Objects.isNull(cmConsultantExp) || StringUtils.isBlank(cmConsultantExp.getCurmonthlevel())) {
            log.info("投顾编号: {}在花名册中 不存在，无法获取主管列表", consCode);
            return null;
        }

        // 获取最高层级
        UserLevelEnum highestLevel = getHighestUserLevel(cmConsultantExp.getCurmonthlevel());
        log.info("投顾编号：{}，最高层级: {}", consCode, JSON.toJSONString(highestLevel));
        if (Objects.isNull(highestLevel)) {
            log.info("投顾编号：{}，最高层级为空", consCode);
            return null;
        } else {
            return highestLevel.getConstCode();
        }

    }

    /**
     * 获取用户最高层级
     *
     * @param curMonthLevel 当月层级字符串(多个层级通过,分割)
     * @return 最高层级枚举
     */
    private UserLevelEnum getHighestUserLevel(String curMonthLevel) {
        List<String> curMonthLevelList = Arrays.asList(curMonthLevel.split(","));
        log.info("当月职级: {}", JSON.toJSONString(curMonthLevelList));

        List<UserLevelEnum> userLevelEnums = curMonthLevelList.stream()
                .map(this::getUserLevelByMonthLevel)
                .filter(Objects::nonNull)
                .map(hbConstant -> UserLevelEnum.getEnum(hbConstant.getConstcode()))
                .filter(USER_LEVEL_ENUMS::contains)
                .collect(Collectors.toList());
        log.info("当月职级: {}", JSON.toJSONString(userLevelEnums));

        if (CollectionUtils.isEmpty(userLevelEnums)) {
            log.info("没有符合条件的当月职级");
            return null;
        }

        return userLevelEnums.stream()
                .max(Comparator.comparingInt(UserLevelEnum::getConstLevel))
                .orElse(null);
    }

    /**
     * 根据中心获取销售总监
     *
     * @param outletCode 营业部编码
     * @return 销售总监编码
     */
    private String getSalesDirectorByCenter(String outletCode, OrgLayerInfoDTO orgLayerInfo) {
        if (Objects.isNull(orgLayerInfo) || StringUtils.isBlank(orgLayerInfo.getCenterOrgCode())) {
            log.info("根据outletCode: {}获取 组织信息为空，无法获取销售总监", outletCode);
            return null;
        }

        String centerOrgCode = orgLayerInfo.getCenterOrgCode();
        switch (centerOrgCode) {
            // CenterOrgEnum.IC.getCode()
            case "1":
                log.info("根据outletCode: {}获取 销售总监为IC selina.huang", outletCode);
                return IC_MANAGER_CODE;
            // CenterOrgEnum.OVERSEAS_BRANCH.getCode()
            case "10":
                log.info("根据outletCode: {}获取 销售总监为HBC pink.li", outletCode);
                return HBC_MANAGER_CODE;
            // CenterOrgEnum.HKPWM.getCode()
            case "1000005627":
                log.info("根据outletCode: {}获取 销售总监为HK guoying.wang", outletCode);
                return HK_MANAGER_CODE;
            default:
                log.info("根据centerOrgCode: {}不在指定范围内， 销售总监为空", centerOrgCode);
                return null;
        }
    }

    /**
     * 获取区域执行副总的上级主管
     *
     * @param cmConsultantExp 花名册信息
     * @return 上级主管编码
     */
    private String getManagerForRegionalVP(CmConsultantExpPO cmConsultantExp, OrgLayerInfoDTO orgLayerInfo) {
        // 尝试获取区域总
        if (Objects.nonNull(orgLayerInfo) && StringUtil.isNotNullStr(orgLayerInfo.getDistrictCode())) {
            String userLevel = String.join("|", this.listConstantCodeByUserLevel(Constants.AREA_MANAGER_LEVEL));
            List<String> areaLeaders = this.listLeadersByOrgCodeAndLevel(orgLayerInfo.getDistrictCode(), userLevel);
            log.info("根据areaCode: {}, 获取区域总列表：{}", orgLayerInfo.getDistrictCode(), JSON.toJSONString(areaLeaders));
            if (CollectionUtils.isNotEmpty(areaLeaders)) {
                return areaLeaders.get(0);
            }
        } else {
            log.info("当前投顾：{}的花名册信息中，areaCode为空，无法获取区域执行副总的上级主管", cmConsultantExp.getConscode());
        }
        // 如果没有区域总，则获取销售总监
        log.info("当前投顾：{}的花名册信息中，获取区域执行副总为空，尝试获取销售总监", cmConsultantExp.getConscode());
        return getSalesDirectorByCenter(cmConsultantExp.getOutletcode(), orgLayerInfo);
    }

    /**
     * 获取分总的上级主管
     *
     * @param cmConsultantExp 花名册信息
     * @return 上级主管编码
     */
    private String getManagerForDivisionManager(CmConsultantExpPO cmConsultantExp, OrgLayerInfoDTO orgLayerInfo) {
        // 1. 尝试获取区域执行副总
        List<String> vicePresidents = listFLeadersByOrgCode(cmConsultantExp.getOutletcode());
        log.info("根据outletCode: {}, 获取区域执行副总列表：{}", cmConsultantExp.getOutletcode(), JSON.toJSONString(vicePresidents));
        if (CollectionUtils.isNotEmpty(vicePresidents)) {
            return vicePresidents.get(0);
        }

        // 2. 尝试获取区域总
        if (Objects.nonNull(orgLayerInfo) && StringUtil.isNotNullStr(orgLayerInfo.getDistrictCode())) {
            String userLevel = String.join("|", this.listConstantCodeByUserLevel(Constants.AREA_MANAGER_LEVEL));
            List<String> areaLeaders = this.listLeadersByOrgCodeAndLevel(orgLayerInfo.getDistrictCode(), userLevel);
            log.info("根据areaCode: {}, 获取区域总列表：{}", orgLayerInfo.getDistrictCode(), JSON.toJSONString(areaLeaders));
            if (CollectionUtils.isNotEmpty(areaLeaders)) {
                return areaLeaders.get(0);
            }
        } else {
            log.info("当前投顾：{}的花名册信息中，areaCode为空，无法获取区域总", cmConsultantExp.getConscode());
        }

        // 3. 获取销售总监
        log.info("当前投顾：{}的花名册信息中，获取区域执行副总、区域总为空，尝试获取销售总监", cmConsultantExp.getConscode());
        return getSalesDirectorByCenter(cmConsultantExp.getOutletcode(), orgLayerInfo);
    }

    /**
     * 获取理财师的上级主管
     *
     * @param cmConsultantExp 花名册信息
     * @return 上级主管编码
     */
    private String getManagerForFinancialPlanner(CmConsultantExpPO cmConsultantExp,  OrgLayerInfoDTO orgLayerInfo) {
        // 1. 尝试获取分总
        String userLevel = String.join("|", listConstantCodeByUserLevel(Constants.DIVISION_MANAGER_LEVEL));
        List<String> divisionManagers = listLeadersByOrgCodeAndLevel(cmConsultantExp.getOutletcode(), userLevel);
        log.info("根据outletCode: {}, 获取分总列表：{}", cmConsultantExp.getOutletcode(), JSON.toJSONString(divisionManagers));
        if (CollectionUtils.isNotEmpty(divisionManagers)) {
            return divisionManagers.get(0);
        }

        // 2. 尝试获取区域执行副总
        List<String> vicePresidents = listFLeadersByOrgCode(cmConsultantExp.getOutletcode());
        log.info("根据outletCode: {}, 获取区域执行副总列表：{}", cmConsultantExp.getOutletcode(), JSON.toJSONString(vicePresidents));
        if (CollectionUtils.isNotEmpty(vicePresidents)) {
            return vicePresidents.get(0);
        }

        // 3. 尝试获取区域总
        if (Objects.nonNull(orgLayerInfo) && StringUtil.isNotNullStr(orgLayerInfo.getDistrictCode())) {
            userLevel = String.join("|", this.listConstantCodeByUserLevel(Constants.AREA_MANAGER_LEVEL));
            List<String> areaLeaders = this.listLeadersByOrgCodeAndLevel(orgLayerInfo.getDistrictCode(), userLevel);
            log.info("根据areaCode: {}, 获取区域总列表：{}", orgLayerInfo.getDistrictCode(), JSON.toJSONString(areaLeaders));
            if (CollectionUtils.isNotEmpty(areaLeaders)) {
                return areaLeaders.get(0);
            }
        } else {
            log.info("当前投顾：{}的花名册信息中，areaCode为空，无法获取区域总", cmConsultantExp.getConscode());
        }

        // 4. 获取销售总监
        log.info("当前投顾：{}的花名册信息中，获取分总、区域执行副总、区域总为空，尝试获取销售总监", cmConsultantExp.getConscode());
        return getSalesDirectorByCenter(cmConsultantExp.getOutletcode(), orgLayerInfo);
    }

    /**
     * @param constCode
     * @return com.howbuy.crm.account.dao.po.constant.HbConstantPO
     * @description:(请在此添加描述)
     * @author: jin.wang03
     * @date: 2025/4/10 19:13
     * @since JDK 1.8
     */
    public HbConstantPO getUserLevelByMonthLevel(String constCode) {
        return hbConstantMapper.getConsText2ConstantByCode(constCode,
                HR_POSITIONS_LEVEL, USER_LEVEL);
    }


    /**
     * @description:(请在此添加描述)
     * @param userLevel
     * @return java.util.List<java.lang.String>
     * @author: jin.wang03
     * @date: 2025/4/10 20:02
     * @since JDK 1.8
     */
    public List<String> listConstantCodeByUserLevel(String userLevel) {
        return hbConstantMapper.listConstantCodeByConsText2(userLevel, "hrpositionslevel");
    }


    /**
     * @description: 查询指定orgcode下 指定层级的leaders
     * @param orgcode
     * @param userlevel
     * @return java.util.List<java.lang.String>
     * @author: jin.wang03
     * @date: 2025/4/14 13:26
     * @since JDK 1.8
     */
    public List<String> listLeadersByOrgCodeAndLevel(String orgcode, String userlevel) {
        return cmConsultantExpRepository.listLeadersByOrgCodeAndLevel(orgcode, userlevel);
    }

    /**
     * @description: 查询指定orgCode的 区域执行副总
     * @param orgCode
     * @return java.util.List<java.lang.String>
     * @author: jin.wang03
     * @date: 2025/4/10 20:03
     * @since JDK 1.8
     */
    public List<String> listFLeadersByOrgCode(String orgCode) {
        return cmConsultantExpRepository.listFLeadersByOrgCode(orgCode);
    }


}