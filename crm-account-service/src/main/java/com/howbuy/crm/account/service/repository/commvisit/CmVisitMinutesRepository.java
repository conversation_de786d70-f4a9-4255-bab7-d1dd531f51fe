/**
 * Copyright (c) 2024, <PERSON><PERSON><PERSON><PERSON> HOWBUY INVESTMENT MANAGEMENT Co., Ltd.
 * All right reserved.
 * <p>
 * THIS IS UNPUBLISHED PROPRIETARY SOURCE CODE OF HOWBUY INVESTMENT
 * MANAGEMENT CO., LTD.  THE CONTENTS OF THIS FILE MAY NOT BE DISCLOSED
 * TO THIRD PARTIES, COPIED OR DUPLICATED IN ANY FORM, IN WHOLE OR IN PART,
 * WITHOUT THE PRIOR WRITTEN PERMISSION OF HOWBUY INVESTMENT MANAGEMENT
 * CO., LTD.
 */
package com.howbuy.crm.account.service.repository.commvisit;

import com.github.pagehelper.PageHelper;
import com.github.pagehelper.PageInfo;
import com.howbuy.crm.account.dao.bo.commvisit.CmVisitMinutesBO;
import com.howbuy.crm.account.dao.bo.commvisit.NoFeedbackMsgBO;
import com.howbuy.crm.account.dao.dto.commvisit.VisitMinutesQueryDTO;
import com.howbuy.crm.account.dao.mapper.commvisit.CmVisitMinutesAccompanyingMapper;
import com.howbuy.crm.account.dao.mapper.commvisit.CmVisitMinutesMapper;
import com.howbuy.crm.account.dao.po.commvisit.CmVisitMinutesAccompanyingPO;
import com.howbuy.crm.account.dao.po.commvisit.CmVisitMinutesPO;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Repository;
import org.springframework.transaction.annotation.Propagation;
import org.springframework.transaction.annotation.Transactional;

import java.util.List;

/**
 * @description: 拜访纪要数据访问层
 * <AUTHOR>
 * @date 2025-04-08 10:26:00
 * @since JDK 1.8
 */
@Repository
@Transactional(rollbackFor = Exception.class, propagation = Propagation.SUPPORTS)
public class CmVisitMinutesRepository {

    @Autowired
    private CmVisitMinutesMapper cmVisitMinutesMapper;
    
    @Autowired
    private CmVisitMinutesAccompanyingMapper cmVisitMinutesAccompanyingMapper;

    /**
     * @param queryDTO 查询条件
     * @return com.github.pagehelper.PageInfo<com.howbuy.crm.account.dao.po.commvisit.CmVisitMinutesPO>
     * @description:分页查询拜访纪要列表
     * <AUTHOR>
     * @date 2025-04-08 10:26:00
     * @since JDK 1.8
     */
    public PageInfo<CmVisitMinutesBO> getVisitMinutesList(VisitMinutesQueryDTO queryDTO) {
        PageHelper.startPage(queryDTO.getPageNo(), queryDTO.getPageSize());
        List<CmVisitMinutesBO> list = cmVisitMinutesMapper.getVisitMinutesList(queryDTO);
        return new PageInfo<>(list);
    }
    
    /**
     * @param id 拜访纪要ID
     * @return com.howbuy.crm.account.dao.po.commvisit.CmVisitMinutesPO
     * @description:根据ID查询拜访纪要
     * <AUTHOR>
     * @date 2025-04-08 10:26:00
     * @since JDK 1.8
     */
    public CmVisitMinutesBO getVisitMinutesBoById(String id) {
        return cmVisitMinutesMapper.getVisitMinutesById(id);
    }

    public CmVisitMinutesPO getVisitMinutesById(String id) {
        return cmVisitMinutesMapper.selectByPrimaryKey(id);
    }
    /**
     * @description 查询拜访纪要是否已反馈的数据
     * @param ids
     * @return java.util.List<com.howbuy.crm.account.dao.po.commvisit.CmVisitMinutesPO>
     * @author: jianjian.yang
     * @date: 2025/4/24 16:11
     * @since JDK 1.8
     */
    public int getFeedbackCountById(List<String> ids) {
        return cmVisitMinutesMapper.getFeedbackCountById(ids);
    }
    
    /**
     * @param visitMinutesId 拜访纪要ID
     * @return java.util.List<com.howbuy.crm.account.dao.po.commvisit.CmVisitMinutesAccompanyingPO>
     * @description:查询拜访纪要陪访人列表
     * <AUTHOR>
     * @date 2025-04-08 10:26:00
     * @since JDK 1.8
     */
    public List<CmVisitMinutesAccompanyingPO> getAccompanyingList(String visitMinutesId) {
        return cmVisitMinutesAccompanyingMapper.selectByVisitMinutesId(visitMinutesId);
    }
    
    /**
     * @param record 主管反馈信息
     * @return int
     * @description:更新主管反馈
     * <AUTHOR>
     * @date 2025-04-08 10:26:00
     * @since JDK 1.8
     */
    @Transactional(rollbackFor = Exception.class, propagation = Propagation.REQUIRED)
    public int updateManagerFeedback(CmVisitMinutesPO record) {
        return cmVisitMinutesMapper.updateManagerFeedback(record);
    }
    
    /**
     * @param ids 拜访纪要ID
     * @param managerId 主管ID
     * @param managerName 主管姓名
     * @return int
     * @description:更新主管信息
     * <AUTHOR>
     * @date 2025-04-08 10:26:00
     * @since JDK 1.8
     */
    @Transactional(rollbackFor = Exception.class, propagation = Propagation.REQUIRED)
    public int updateManager(List<String> ids, String managerId, String managerLevel, String managerName) {
        return cmVisitMinutesMapper.updateManager(ids, managerId, managerLevel, managerName);
    }
    
    /**
     * @param id 拜访纪要ID
     * @return int
     * @description:清空主管反馈信息
     * <AUTHOR>
     * @date 2025-04-08 10:26:00
     * @since JDK 1.8
     */
    @Transactional(rollbackFor = Exception.class, propagation = Propagation.REQUIRED)
    public int clearManagerFeedback(String id) {
        return cmVisitMinutesMapper.clearManagerFeedback(id);
    }
    
    /**
     * @param queryDTO 查询条件
     * @return java.util.List<com.howbuy.crm.account.dao.po.commvisit.CmVisitMinutesPO>
     * @description:查询拜访纪要列表(用于导出)
     * <AUTHOR>
     * @date 2025-04-08 10:26:00
     * @since JDK 1.8
     */
    public List<CmVisitMinutesPO> getVisitMinutesListForExport(VisitMinutesQueryDTO queryDTO) {
        return cmVisitMinutesMapper.getVisitMinutesListForExport(queryDTO);
    }

    /**
     * 更新拜访纪要
     */
    @Transactional(rollbackFor = Exception.class, propagation = Propagation.REQUIRED)
    public int updateVisitMinutes(CmVisitMinutesPO po) {
        return cmVisitMinutesMapper.updateMinutesById(po);
    }

    /**
     * @description: 新增拜访纪要
     * @param po 拜访纪要
     * @return int
     * @author: jin.wang03
     * @date: 2025/4/10 14:05
     * @since JDK 1.8
     */
    @Transactional(rollbackFor = Exception.class, propagation = Propagation.REQUIRED)
    public int insert(CmVisitMinutesPO po) {
        return cmVisitMinutesMapper.insert(po);
    }

    /**
     * @description 根据菜单编码查询菜单名称
     * @param menuCode
     * @return java.lang.String
     * @author: jianjian.yang
     * @date: 2025/4/17 10:15
     * @since JDK 1.8
     */
    public String getMenuName(String menuCode) {
        return cmVisitMinutesMapper.getMenuName(menuCode);
    }

    /**
     * @description 查询第6天未反馈的列表
     * @param notPushAccompanyingType 不推送陪访人类型
     * @param curPreDay 当前日期往前推5天的日期
     * @return java.util.List<com.howbuy.crm.account.dao.bo.commvisit.NoFeedbackMsgBO>
     * @author: jianjian.yang
     * @date: 2025/4/29 19:28
     * @since JDK 1.8
     */
    public List<NoFeedbackMsgBO> fetchNoFeedbackList(String notPushAccompanyingType, String curPreDay) {
        return cmVisitMinutesMapper.fetchNoFeedbackList(notPushAccompanyingType, curPreDay);
    }
} 