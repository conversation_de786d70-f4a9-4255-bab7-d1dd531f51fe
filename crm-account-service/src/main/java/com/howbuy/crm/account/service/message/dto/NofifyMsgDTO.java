/**
 * Copyright (c) 2024, <PERSON><PERSON><PERSON><PERSON> HOWBUY INVESTMENT MANAGEMENT Co., Ltd.
 * All right reserved.
 * <p>
 * THIS IS UNPUBLISHED PROPRIETARY SOURCE CODE OF HOWBUY INVESTMENT
 * MANAGEMENT CO., LTD.  THE CONTENTS OF THIS FILE MAY NOT BE DISCLOSED
 * TO THIRD PARTIES, COPIED OR DUPLICATED IN ANY FORM, IN WHOLE OR IN PART,
 * WITHOUT THE PRIOR WRITTEN PERMISSION OF HOWBUY INVESTMENT MANAGEMENT
 * CO., LTD.
 */
package com.howbuy.crm.account.service.message.dto;

import lombok.Data;

import java.io.Serializable;
import java.util.List;
import java.util.Map;

/**
 * @description: (发送消息通知[站内信、企业微信] 对象)
 * <AUTHOR>
 * @date 2024/1/26 13:59
 * @since JDK 1.8
 */
@Data
public class NofifyMsgDTO implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 业务id
     */
    String businessId;


    /**
     * 业务参数
     */
    Map<String,String> paramMap ;


    /**
     * 通知类型
     * 1-按照投顾code列表 发送
     * 2-按照角色列表 发送
     * 3-按照 hboneNo 发送
     * 4-按照 hkTxAcctNo 发送
     */
    private String notifyType;


    /**
     * 1-按照投顾code列表  时
     * 投顾code列表
     */
    private List<String> consCodeList;


    /**
     * 2-按照角色列表  时
     * 角色列表
     */
    private List<String> roleCodeList;



    /**
     * 3-按照 hboneNo  发送
     * hboneNo
     */
    private String hboneNo;

    /**
     * 4-按照 hkTxAcctNo 发送
     * hkTxAcctNo
     */
    private   String hkTxAcctNo;



}