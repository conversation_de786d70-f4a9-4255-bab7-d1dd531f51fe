/**
 * Copyright (c) 2023, <PERSON><PERSON><PERSON><PERSON> HOWBUY INVESTMENT MANAGEMENT Co., Ltd.
 * All right reserved.
 * <p>
 * THIS IS UNPUBLISHED PROPRIETARY SOURCE CODE OF HOWBUY INVESTMENT
 * MANAGEMENT CO., LTD.  THE CONTENTS OF THIS FILE MAY NOT BE DISCLOSED
 * TO THIRD PARTIES, COPIED OR DUPLICATED IN ANY FORM, IN WHOLE OR IN PART,
 * WITHOUT THE PRIOR WRITTEN PERMISSION OF HOWBUY INVESTMENT MANAGEMENT
 * CO., LTD.
 */
package com.howbuy.crm.account.service.job;

import com.howbuy.crm.account.service.business.custinfo.HkCustUpdateBusinessService;
import com.howbuy.message.SimpleMessage;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;

/**
 * <AUTHOR>
 * @description: 定时任务：全量刷新 CM_HK_CONSCUST 表中 HKCUSTID字段
 * 具体做法：根据CM_HK_CONSCUST表中[HK_TX_ACCT_NO]，查询香港账户中心的账户信息，获取到对应的[EbrokerId]，更新CM_HK_CONSCUST表中HKCUSTID字段
 * @date 2023/3/8 17:26
 * @since JDK 1.8
 */
@Slf4j
@Service
public class SyncHkEbrokerIdJob extends AbstractBatchMessageJob {

    /**
     * 调度消息队列，nacos中需要配置test.queue.job对应的队列名称，此处为了程序正常运行默认复制了CSDC_CANCEL_ORDER_QUEUE
     */
    @Value("${sync.TOPIC_CRM_SYNC_HK_EBROKERID}")
    private String queue;

    @Autowired
    private HkCustUpdateBusinessService hkCustUpdateBusinessService;

    @Override
    protected void doProcessMessage(SimpleMessage message) {
        // 调度业务逻辑处理
        log.info("SyncHkEbrokerIdJob process start");

        try {
            // 业务处理逻辑
            hkCustUpdateBusinessService.syncHkEbrokerId();
        } catch (Exception e) {
            log.error("", e);
        }
        log.info("SyncHkEbrokerIdJob process end");
    }

    @Override
    protected String getQuartMessageChannel() {
        // 返回调度配置的队列名
        return queue;
    }
}