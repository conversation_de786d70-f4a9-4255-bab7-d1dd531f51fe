/**
 * Copyright (c) 2023, <PERSON><PERSON><PERSON><PERSON> HOWBUY INVESTMENT MANAGEMENT Co., Ltd.
 * All right reserved.
 * <p>
 * THIS IS UNPUBLISHED PROPRIETARY SOURCE CODE OF HOWBUY INVESTMENT
 * MANAGEMENT CO., LTD.  THE CONTENTS OF THIS FILE MAY NOT BE DISCLOSED
 * TO THIRD PARTIES, COPIED OR DUPLICATED IN ANY FORM, IN WHOLE OR IN PART,
 * WITHOUT THE PRIOR WRITTEN PERMISSION OF HOWBUY INVESTMENT MANAGEMENT
 * CO., LTD.
 */
package com.howbuy.crm.account.service.repository.custinfo;

import com.howbuy.common.utils.StringUtil;
import com.howbuy.crm.account.dao.mapper.customize.custinfo.ConscustCustomizeMapper;
import com.howbuy.crm.account.dao.po.custinfo.CmSourceInfoPO;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import org.springframework.transaction.annotation.Propagation;
import org.springframework.transaction.annotation.Transactional;

/**
 * @description: (投顾客户[来源] repository)
 * <AUTHOR>
 * @date 2023/12/8 15:31
 * @since JDK 1.8
 */
@Component
@Slf4j
@Transactional(rollbackFor = Exception.class, propagation = Propagation.SUPPORTS)
public class ConsCustSourceRepository {
    @Autowired
    private ConscustCustomizeMapper conscustCustomizeMapper;




    /**
     * @description:(根据来源编号 查找来源信息)
     * @param sourceCode 来源编号
     * @return com.howbuy.crm.account.dao.po.custinfo.CmSourceInfoPO
     * @author: haoran.zhang
     * @date: 2023/12/27 16:52
     * @since JDK 1.8
     */
    public CmSourceInfoPO querySourceByCode(String sourceCode){
        return querySourceByCode(sourceCode,null);
    }


    /**
     * @description:(根据来源编号 查找来源信息,如果没有查到,则使用默认来源编号查找)
     * @param sourceCode 来源编号
     * @param defaultSourceCode 如无数据，默认返回的来源编号
     * @return com.howbuy.crm.account.dao.po.custinfo.CmSourceInfoPO
     * @author: haoran.zhang
     * @date: 2024/1/5 20:07
     * @since JDK 1.8
     */
    public CmSourceInfoPO querySourceByCode(String sourceCode,String defaultSourceCode){
        CmSourceInfoPO sourcePo= conscustCustomizeMapper.querySourceByCode(sourceCode);
        if(sourcePo==null && StringUtil.isNotBlank(defaultSourceCode)){
            sourcePo= conscustCustomizeMapper.querySourceByCode(defaultSourceCode);
        }
        return sourcePo;
    }

}