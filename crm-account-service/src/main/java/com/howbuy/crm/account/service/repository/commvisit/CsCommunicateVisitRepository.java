/**
 * Copyright (c) 2024, <PERSON><PERSON><PERSON><PERSON> HOWBUY INVESTMENT MANAGEMENT Co., Ltd.
 * All right reserved.
 * <p>
 * THIS IS UNPUBLISHED PROPRIETARY SOURCE CODE OF HOWBUY INVESTMENT
 * MANAGEMENT CO., LTD.  THE CONTENTS OF THIS FILE MAY NOT BE DISCLOSED
 * TO THIRD PARTIES, COPIED OR DUPLICATED IN ANY FORM, IN WHOLE OR IN PART,
 * WITHOUT THE PRIOR WRITTEN PERMISSION OF HOWBUY INVESTMENT MANAGEMENT
 * CO., LTD.
 */
package com.howbuy.crm.account.service.repository.commvisit;

import com.github.pagehelper.PageHelper;
import com.github.pagehelper.PageInfo;
import com.howbuy.crm.account.dao.bo.commvisit.CsCommunicateBO;
import com.howbuy.crm.account.dao.mapper.CommonMapper;
import com.howbuy.crm.account.dao.mapper.commvisit.CmConsBookingCustMapper;
import com.howbuy.crm.account.dao.mapper.commvisit.CsCommunicateVisitMapper;
import com.howbuy.crm.account.dao.mapper.commvisit.CsVisitNewestMapper;
import com.howbuy.crm.account.dao.mapper.customize.commvisit.CsCommVisitCustomizeMapper;
import com.howbuy.crm.account.dao.po.commvisit.CmConsBookingCustPO;
import com.howbuy.crm.account.dao.po.commvisit.CsCommunicateVisitPO;
import com.howbuy.crm.account.dao.po.commvisit.CsVisitNewestPO;
import crm.howbuy.base.constants.ProcessStatus;
import crm.howbuy.base.utils.DateTimeUtil;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import org.springframework.transaction.annotation.Propagation;
import org.springframework.transaction.annotation.Transactional;

import java.util.Date;
import java.util.List;

/**
 * <AUTHOR>
 * @description: (请在此添加描述)
 * @date 2024/10/24 10:57
 * @since JDK 1.8
 */
@Component
@Transactional(rollbackFor = Exception.class, propagation = Propagation.SUPPORTS)
public class CsCommunicateVisitRepository {

    @Autowired
    private CsCommVisitCustomizeMapper csCommVisitCustomizeMapper;

    @Autowired
    private CsCommunicateVisitMapper csCommunicateVisitMapper;

    @Autowired
    private CsVisitNewestMapper csVisitNewestMapper;

    @Autowired
    private CmConsBookingCustMapper cmConsBookingCustMapper;

    @Autowired
    private CommonMapper commonMapper;


    /**
     * @param consCustNo
     * @param visitTypeList
     * @param startDt
     * @param endDt
     * @return java.util.List<com.howbuy.crm.account.dao.po.commvisit.CsCommunicateVisitPO>
     * @description:根据条件查询沟通拜访列表
     * <AUTHOR>
     * @date 2024/10/24 10:59
     * @since JDK 1.8
     */
    public List<CsCommunicateVisitPO> selectByCondition(String consCustNo,
                                                        List<String> visitTypeList,
                                                        String startDt,
                                                        String endDt) {
        return csCommVisitCustomizeMapper.selectByCondition(consCustNo, visitTypeList, startDt, endDt);
    }

    /**
     * 查询最新一条客户沟通记录
     *
     * @param consCustNo    投顾客户号
     * @param visitTypeList 回访类型
     * @return CsCommunicateVisitPO
     */
    public CsCommunicateVisitPO selectNewestCsCommunicateVisitPO(String consCustNo,
                                                                 List<String> visitTypeList) {
        return csCommVisitCustomizeMapper.selectNewestCsCommunicateVisitPO(consCustNo, visitTypeList);
    }

    @Transactional(rollbackFor = Exception.class, propagation = Propagation.REQUIRED)
    public void insertCommunicateVisit(CsCommunicateVisitPO csCommunicateVisitPO) {
        Date date = new Date();
        csCommunicateVisitPO.setCredt(date);
        csCommunicateVisitPO.setStimestamp(date);
        // 插入沟通拜访表
        csCommunicateVisitMapper.insert(csCommunicateVisitPO);

        CsVisitNewestPO csVisitNewest = new CsVisitNewestPO();
        csVisitNewest.setId(csCommunicateVisitPO.getId());
        csVisitNewest.setConscustno(csCommunicateVisitPO.getConscustno());
        csVisitNewest.setCommcontent(csCommunicateVisitPO.getCommcontent());
        csVisitNewest.setVisittype(csCommunicateVisitPO.getVisittype());
        csVisitNewest.setVisitclassify(csCommunicateVisitPO.getVisitclassify());
        csVisitNewest.setHisid(csCommunicateVisitPO.getHisid());
        csVisitNewest.setHisflag(csCommunicateVisitPO.getHisflag());
        csVisitNewest.setCreator(csCommunicateVisitPO.getCreator());
        csVisitNewest.setCredt(date);
        csVisitNewest.setStimestamp(date);
        // 删除最新拜访表历史记录和新增最新拜访表记录
        csVisitNewestMapper.deleteByConsCustNo(csCommunicateVisitPO.getConscustno());
        csVisitNewestMapper.insert(csVisitNewest);

        // 如果有填写下次预约记录，则保存到投顾预约表
        if (StringUtils.isNotBlank(csCommunicateVisitPO.getNextdt())) {
            CmConsBookingCustPO cmConsBookingCust = new CmConsBookingCustPO();
            cmConsBookingCust.setConsbookingid(commonMapper.getSeqValue("SEQ_PCUSTREC"));
            cmConsBookingCust.setBookingdt(csCommunicateVisitPO.getNextdt());
            cmConsBookingCust.setVisittype(csCommunicateVisitPO.getNextvisittype());
            cmConsBookingCust.setBookingstarttime(csCommunicateVisitPO.getNextstarttime());
            cmConsBookingCust.setBookingendtime(csCommunicateVisitPO.getNextendtime());
            cmConsBookingCust.setContent(csCommunicateVisitPO.getNextvisitcontent());
            cmConsBookingCust.setCreator(csCommunicateVisitPO.getCreator());
            cmConsBookingCust.setBookingstatus(ProcessStatus.Unprocess.getValue());
            cmConsBookingCust.setConscustno(csCommunicateVisitPO.getConscustno());
            cmConsBookingCust.setCredt(DateTimeUtil.getCurrYMD());
            cmConsBookingCust.setBookingcons(csCommunicateVisitPO.getCreator());
            cmConsBookingCust.setStimestamp(date);
            cmConsBookingCustMapper.insert(cmConsBookingCust);
        }

        // 如果已经有预约记录，则置标志位已处理
        if (StringUtils.isNotBlank(csCommunicateVisitPO.getConsbookingid())) {
            CmConsBookingCustPO cmConsBookingCust = cmConsBookingCustMapper.selectByPrimaryKey(csCommunicateVisitPO.getConsbookingid());
            if (null != cmConsBookingCust) {
                cmConsBookingCust.setBookingstatus(ProcessStatus.Processed.getValue());
                cmConsBookingCust.setModdt(DateTimeUtil.getCurrYMD());
                cmConsBookingCust.setStimestamp(date);
                cmConsBookingCustMapper.updateByPrimaryKey(cmConsBookingCust);
            }
        }
    }

    /**
     * @description 查询客户沟通列表
     * @param conscustno
     * @param pageNum
     * @param pageSize
     * @return com.github.pagehelper.PageInfo<com.howbuy.crm.account.dao.bo.commvisit.CsCommunicateBO>
     * @author: jianjian.yang
     * @date: 2025/4/29 14:11
     * @since JDK 1.8
     */
    public PageInfo<CsCommunicateBO> queryCustVisitListByPage(String conscustno, Integer pageNum, Integer pageSize){
        PageHelper.startPage(pageNum, pageSize);
        List<CsCommunicateBO> list = csCommVisitCustomizeMapper.queryCustVisitListByPage(conscustno);
        return new PageInfo<>(list);
    }
}
