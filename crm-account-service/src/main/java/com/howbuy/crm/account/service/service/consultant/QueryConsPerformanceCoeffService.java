/**
 * Copyright (c) 2024, <PERSON><PERSON><PERSON><PERSON> HOWBUY INVESTMENT MANAGEMENT Co., Ltd.
 * All right reserved.
 * <p>
 * THIS IS UNPUBLISHED PROPRIETARY SOURCE CODE OF HOWBUY INVESTMENT
 * MANAGEMENT CO., LTD.  THE CONTENTS OF THIS FILE MAY NOT BE DISCLOSED
 * TO THIRD PARTIES, COPIED OR DUPLICATED IN ANY FORM, IN WHOLE OR IN PART,
 * WITHOUT THE PRIOR WRITTEN PERMISSION OF HOWBUY INVESTMENT MANAGEMENT
 * CO., LTD.
 */
package com.howbuy.crm.account.service.service.consultant;

import com.howbuy.crm.account.client.request.consultant.MergeConsPerformanceCoeffRequest;
import com.howbuy.crm.account.client.request.consultant.QueryConsPerformanceCoeffRequest;
import com.howbuy.crm.account.client.response.consultant.QueryConsPerformanceCoeffVO;
import com.howbuy.crm.account.dao.po.consultant.CmConsPerformanceCoeffPO;
import com.howbuy.crm.account.service.repository.consultant.QueryConsPerformanceCoeffRepository;
import com.howbuy.crm.account.service.repository.custinfo.ConsCustInfoRepository;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;

/**
 * @description: 获取人员绩效系数表服务
 * <AUTHOR>
 * @date 2025-07-02 16:15:20
 * @since JDK 1.8
 */
@Service
@Slf4j
public class QueryConsPerformanceCoeffService {

    @Resource
    private QueryConsPerformanceCoeffRepository queryConsPerformanceCoeffRepository;
    @Resource
    private ConsCustInfoRepository consCustInfoRepository;

    /**
     * @param request 请求参数
     * @return com.howbuy.crm.account.client.response.Response<com.howbuy.crm.account.client.response.consultant.QueryConsPerformanceCoeffVO>
     * @description: 获取人员绩效系数表
     * <AUTHOR>
     * @date 2025-07-02 16:15:20
     * @since JDK 1.8
     */
    public QueryConsPerformanceCoeffVO execute(QueryConsPerformanceCoeffRequest request) {
        log.info("获取人员绩效系数表，请求参数：{}", request);
        // 获取最新记录
        CmConsPerformanceCoeffPO po = queryConsPerformanceCoeffRepository.getLatestRecord(request.getConsCustNo(), getConsCode(request));
        if (po == null) {
            return null;
        }
        // 转换为VO
        QueryConsPerformanceCoeffVO vo = new QueryConsPerformanceCoeffVO();
        vo.setConsCustNo(po.getConsCustNo());
        vo.setConsCode(po.getConscode());
        vo.setAssignTime(po.getAssignTime());
        vo.setSourceType(po.getSourceType());
        vo.setCustConversionCoeff(po.getCustConversionCoeff());
        vo.setManageCoeffSubtotal(po.getManageCoeffSubtotal());
        vo.setManageCoeffRegionalsubtotal(po.getManageCoeffRegionalsubtotal());
        vo.setManageCoeffRegionaltotal(po.getManageCoeffRegionaltotal());
        vo.setCommissionCoeffStart(po.getCommissionCoeffStart());
        vo.setCxb(po.getCxb());
        vo.setIsBigV(po.getIsBigV());

        log.info("获取人员绩效系数表完成，结果：{}", vo);
        return vo;
    }

    private String getConsCode(QueryConsPerformanceCoeffRequest request) {
        // 优先使用request中的投顾code
        String consCode = request.getConsCode();
        if (StringUtils.isNotBlank(consCode)) {
            log.info("使用request中的投顾code：{}，投顾客户号：{}", consCode, request.getConsCustNo());
            return consCode;
        }
        // 如果request中投顾code为空，通过投顾客户号查询获取
        String consCustNo = request.getConsCustNo();
        consCode = consCustInfoRepository.selectConsCodeByCustNo(consCustNo);
        log.info("通过投顾客户号：{} 查询获取的投顾code：{}", consCustNo, consCode);
        return consCode;
    }
} 