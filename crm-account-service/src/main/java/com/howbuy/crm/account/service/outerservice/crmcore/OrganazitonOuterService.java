/**
 * Copyright (c) 2023, <PERSON><PERSON><PERSON><PERSON> HOWBUY INVESTMENT MANAGEMENT Co., Ltd.
 * All right reserved.
 * <p>
 * THIS IS UNPUBLISHED PROPRIETARY SOURCE CODE OF HOWBUY INVESTMENT
 * MANAGEMENT CO., LTD.  THE CONTENTS OF THIS FILE MAY NOT BE DISCLOSED
 * TO THIRD PARTIES, COPIED OR DUPLICATED IN ANY FORM, IN WHOLE OR IN PART,
 * WITHOUT THE PRIOR WRITTEN PERMISSION OF HOWBUY INVESTMENT MANAGEMENT
 * CO., LTD.
 */
package com.howbuy.crm.account.service.outerservice.crmcore;

import com.google.common.collect.Maps;
import com.howbuy.crm.account.service.domain.OrgLayerInfoDTO;
import com.howbuy.crm.organization.dto.OrgLayerInfo;
import com.howbuy.crm.organization.service.HbOrganizationService;
import lombok.extern.slf4j.Slf4j;
import org.apache.dubbo.config.annotation.DubboReference;
import org.springframework.stereotype.Service;

import java.util.List;
import java.util.Map;

/**
 * @description: (部门 outerService)
 * <AUTHOR>
 * @date 2023/9/12 18:25
 * @since JDK 1.8
 */
@Service
@Slf4j
public class OrganazitonOuterService {
    @DubboReference(registry = "crm-core-server", check = false)
    private HbOrganizationService hbOrganizationService;



    /**
     * @description 批量获取组织架构的层级信息
     * @param orgCodeList
     * @return java.util.Map<java.lang.String,com.howbuy.crm.cgi.manager.domain.crmcore.OrgLayerInfoDTO>
     * @author: jianjian.yang
     * @date: 2023/11/23 15:59
     * @since JDK 1.8
     */
    public Map<String, OrgLayerInfoDTO> getOrgLayerInfoByOrgCodeList(List<String> orgCodeList){
        Map<String, OrgLayerInfoDTO>  orgLayerInfoDTOMap = Maps.newHashMap();
        Map<String, OrgLayerInfo> orgLayerInfoMap = hbOrganizationService.getOrgLayerInfoByOrgCodeList(orgCodeList);
        orgLayerInfoMap.forEach((key,value)->{
            OrgLayerInfoDTO orgLayerInfoDTO = getCopyOrgLayerInfoDTO(value);
            orgLayerInfoDTOMap.put(key, orgLayerInfoDTO);
        });
        return orgLayerInfoDTOMap;
    }

    /**
     * @description 对象复制
     * @param orgLayerInfo
     * @return com.howbuy.crm.cgi.manager.domain.crmcore.OrgLayerInfoDTO
     * @author: jianjian.yang
     * @date: 2023/11/23 15:59
     * @since JDK 1.8
     */
    private OrgLayerInfoDTO getCopyOrgLayerInfoDTO(OrgLayerInfo orgLayerInfo){
        OrgLayerInfoDTO orgLayerInfoDTO = new OrgLayerInfoDTO();
        orgLayerInfoDTO.setOrgCode(orgLayerInfo.getOrgCode());
        orgLayerInfoDTO.setOrgName(orgLayerInfo.getOrgName());
        orgLayerInfoDTO.setDistrictCode(orgLayerInfo.getDistrictCode());
        orgLayerInfoDTO.setDistrictName(orgLayerInfo.getDistrictName());
        orgLayerInfoDTO.setCenterOrgCode(orgLayerInfo.getCenterOrgCode());
        orgLayerInfoDTO.setCenterOrgName(orgLayerInfo.getCenterOrgName());
        orgLayerInfoDTO.setPartOrgName(orgLayerInfo.getPartOrgName());
        orgLayerInfoDTO.setPartOrgCode(orgLayerInfo.getPartOrgCode());
        return orgLayerInfoDTO;
    }

    /**
     * @description 查组织架构的层级信息
     * @param orgCode
     * @return com.howbuy.crm.cgi.manager.domain.crmcore.OrgLayerInfoDTO
     * @author: jianjian.yang
     * @date: 2023/11/22 17:36
     * @since JDK 1.8
     */
    public OrgLayerInfoDTO getOrgLayerInfoByOrgCode(String orgCode){
        OrgLayerInfo orgLayerInfo = hbOrganizationService.getOrgLayerInfoByOrgCode(orgCode);
        if(orgLayerInfo==null){
            return null;
        }
        OrgLayerInfoDTO orgLayerInfoDTO = getCopyOrgLayerInfoDTO(orgLayerInfo);
        return orgLayerInfoDTO;
    }

    /**
     * @description:(根据 orgCode  获取 投顾的部门层级信息，包括： 区域、中心、部门（包含删除的区域、中心、部门）)
     * @param orgCode	
     * @return com.howbuy.crm.account.service.domain.OrgLayerInfoDTO
     * @author: shijie.wang
     * @date: 2025/7/9 17:21
     * @since JDK 1.8
     */
    public OrgLayerInfoDTO getOrgLayerInfoContaionsDeleteByOrgCode(String orgCode){
        OrgLayerInfo orgLayerInfo = hbOrganizationService.getOrgLayerInfoContaionsDeleteByOrgCode(orgCode);
        if(orgLayerInfo==null){
            return null;
        }
        return getCopyOrgLayerInfoDTO(orgLayerInfo);
    }

}