/**
 * Copyright (c) 2023, <PERSON><PERSON><PERSON><PERSON> HOWBUY INVESTMENT MANAGEMENT Co., Ltd.
 * All right reserved.
 * <p>
 * THIS IS UNPUBLISHED PROPRIETARY SOURCE CODE OF HOWBUY INVESTMENT
 * MANAGEMENT CO., LTD.  THE CONTENTS OF THIS FILE MAY NOT BE DISCLOSED
 * TO THIRD PARTIES, COPIED OR DUPLICATED IN ANY FORM, IN WHOLE OR IN PART,
 * WITHOUT THE PRIOR WRITTEN PERMISSION OF HOWBUY INVESTMENT MANAGEMENT
 * CO., LTD.
 */
package com.howbuy.crm.account.service.business.custinfo;

import com.alibaba.fastjson.JSON;
import com.howbuy.common.utils.Assert;
import com.howbuy.crm.account.client.response.custinfo.HkAcctCustDetailInfoVO;
import com.howbuy.crm.account.dao.bo.custinfo.HkUpdateEbrokerIdBO;
import com.howbuy.crm.account.dao.po.custinfo.CmHkConscustPO;
import com.howbuy.crm.account.dao.req.PageReqVO;
import com.howbuy.crm.account.service.outerservice.hkacct.HkCustBasicInfoOuterService;
import com.howbuy.crm.account.service.repository.custinfo.CmHkCustInfoRepository;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.List;
import java.util.Objects;

/**
 * <AUTHOR>
 * @description: 更新[香港客户]相关字段 service
 * @date 2024/06/04 20:37
 * @since JDK 1.8
 */
@Service
@Slf4j
public class HkCustUpdateBusinessService {


    @Autowired
    private CmHkCustInfoRepository hkCustRepository;

    @Autowired
    private HkCustBasicInfoOuterService hkCustInfoOuterService;

    /**
     * 每次查询500条
     */
    private static final int PAGE_SIZE = 500;


    /**
     * 查询香港交易账户信息
     *
     * @param hkTxAcctNo
     * @return
     */
    private HkAcctCustDetailInfoVO queryHkCustInfo(String hkTxAcctNo) {
        Assert.notNull(hkTxAcctNo, "香港交易账号不能为空");
        return hkCustInfoOuterService.queryHkCustDetailInfo(hkTxAcctNo);
    }


    /**
     * @description: 根据CM_HK_CONSCUST表中[HK_TX_ACCT_NO]，查询香港账户中心的账户信息，获取到对应的[EbrokerId]，更新CM_HK_CONSCUST表中HKCUSTID字段
     * @param
     * @return void
     * @author: jin.wang03
     * @date: 2024/6/4 14:37
     * @since JDK 1.8
     */
    public void syncHkEbrokerId() {
        long count = hkCustRepository.count();
        log.info("CM_HK_CONSCUST表的记录总数为：{}", count);
        if (count <= 0L) {
            return;
        }

        // 分批处理，每次500条
        long cycNum = count / PAGE_SIZE + 1;
        // 1 先查询CM_HK_CONSCUST表里，全量的HK_ACCT_NO，分批查询，每次查询500个
        for (int i = 1; i <= cycNum; i++) {
            PageReqVO pageReqVO = new PageReqVO();
            pageReqVO.setPage(i);
            pageReqVO.setRows(PAGE_SIZE);
            List<CmHkConscustPO> hkCustVOList = hkCustRepository.selectPageByVo(pageReqVO);
            if (CollectionUtils.isEmpty(hkCustVOList)) {
                continue;
            }

            List<HkUpdateEbrokerIdBO> updateList = new ArrayList<>();
            for (CmHkConscustPO hkCustReqVO : hkCustVOList) {
                if (StringUtils.isBlank(hkCustReqVO.getHkTxAcctNo())) {
                    continue;
                }

                HkAcctCustDetailInfoVO hkCustDetailInfoVO = queryHkCustInfo(hkCustReqVO.getHkTxAcctNo());
                if (Objects.isNull(hkCustDetailInfoVO) ||
                        StringUtils.equals(hkCustReqVO.getHkcustid(), hkCustDetailInfoVO.getEbrokerId())) {
                    continue;
                }

                HkUpdateEbrokerIdBO hkUpdateEbrokerIdBO = new HkUpdateEbrokerIdBO();
                hkUpdateEbrokerIdBO.setHkCustNo(hkCustReqVO.getHkTxAcctNo());
                hkUpdateEbrokerIdBO.setEbrokerId(hkCustDetailInfoVO.getEbrokerId());
                updateList.add(hkUpdateEbrokerIdBO);
            }
            if (CollectionUtils.isNotEmpty(updateList)) {
                hkCustRepository.batchUpdateEbrokerIdByHkCustNo(updateList);
                log.info("更新HK_EBROKER_ID成功,具体更新内容为：{}", JSON.toJSONString(updateList));
            }
        }

    }


}