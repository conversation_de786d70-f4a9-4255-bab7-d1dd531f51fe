package com.howbuy.crm.account.service.config;

import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.scheduling.concurrent.ThreadPoolTaskExecutor;

import java.util.concurrent.ThreadPoolExecutor;

/**
 * @description: 线程池配置
 * <AUTHOR>
 * @date 2025-07-23 10:15
 * @since JDK 1.8
 */
@Configuration
public class ThreadPoolConfig {

    /**
     * 创建人员绩效系数初始化专用线程池
     */
    @Bean("performanceCoeffInitExecutor")
    public ThreadPoolTaskExecutor performanceCoeffInitExecutor() {
        ThreadPoolTaskExecutor executor = new ThreadPoolTaskExecutor();
        // 核心线程数：3
        executor.setCorePoolSize(3);
        // 最大线程数：5
        executor.setMaxPoolSize(5);
        // 队列容量：100，避免队列太大占用内存
        executor.setQueueCapacity(50);
        // 线程名前缀
        executor.setThreadNamePrefix("perf-coeff-init-");
        // 拒绝策略：调用者运行
        executor.setRejectedExecutionHandler(new ThreadPoolExecutor.CallerRunsPolicy());
        // 等待所有任务完成后再关闭线程池
        executor.setWaitForTasksToCompleteOnShutdown(true);
        // 等待时间（秒）
        executor.setAwaitTerminationSeconds(60);
        // 线程空闲超时时间（秒）
        executor.setKeepAliveSeconds(300);
        executor.initialize();
        return executor;
    }
} 