package com.howbuy.crm.account.service.outerservice.crmnt;

import com.alibaba.fastjson.JSON;
import com.howbuy.crm.nt.pushmsg.request.CmPushMsgRequest;
import com.howbuy.crm.nt.pushmsg.service.CmPushMsgService;
import crm.howbuy.base.dubbo.model.BaseConstantEnum;
import crm.howbuy.base.dubbo.response.BaseResponse;
import lombok.extern.slf4j.Slf4j;
import org.apache.dubbo.config.annotation.DubboReference;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.Map;

/**
 * <AUTHOR>
 * @description: 请在此添加描述
 * @date 2025/4/15 19:23
 * @since JDK 1.8
 */
@Slf4j
@Service
public class CmPushMsgOuterService {

    @DubboReference(registry = "crm-nt-server", check = false)
    private CmPushMsgService cmPushMsgService;

    /**
     * 投顾类型
     */
    public static final String PUSH_MSG_ACCOUNT_CONS = "2";
    /**
     * 客户类型
     */
    public static final String PUSH_MSG_ACCOUNT_CUST = "1";

    /**
     * @description 发消息
     * @param businessId	业务ID
     * @param paramMap	参数
     * @return void
     * @author: jianjian.yang
     * @date: 2025/4/15 19:30
     * @since JDK 1.8
     */
    public void sendPushMsg(String businessId, Map<String,String> paramMap, String accountType, String account) {
        try {
            CmPushMsgRequest msgrequest = new CmPushMsgRequest();
            msgrequest.setBusinessId(businessId);
            msgrequest.setParamJson(paramMap == null ? "" :JSON.toJSONString(paramMap));
            msgrequest.setAccountType(accountType);
            msgrequest.setAccount(account);
            log.info("调用CRM消息推送请求:"+ JSON.toJSONString(msgrequest));
            BaseResponse response = cmPushMsgService.pushMsg(msgrequest);
            log.info("调用CRM消息推送结果:"+ (response == null ? "返回结果是null" : JSON.toJSONString(response)));
        } catch (Exception e){
            log.error("调用CRM消息推送失败", e);
        }
    }
}