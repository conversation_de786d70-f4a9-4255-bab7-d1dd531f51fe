package com.howbuy.crm.account.service.facade.beisen;

import com.howbuy.crm.account.client.facade.beisen.CmBeisenOrgConfigFacade;
import com.howbuy.crm.account.client.request.beisen.BeisenConfigIdRequest;
import com.howbuy.crm.account.client.request.beisen.InsertBeisenOrgConfigRequest;
import com.howbuy.crm.account.client.request.beisen.QueryBeisenOrgConfigRequest;
import com.howbuy.crm.account.client.request.beisen.UpdateBeisenOrgConfigRequest;
import com.howbuy.crm.account.client.response.ExportToFileVO;
import com.howbuy.crm.account.client.response.PageVO;
import com.howbuy.crm.account.client.response.Response;
import com.howbuy.crm.account.client.response.beisen.CmBeisenOrgConfigDetailVO;
import com.howbuy.crm.account.client.response.beisen.CmBeisenOrgVO;
import com.howbuy.crm.account.service.service.beisen.CmBeisenOrgConfigService;
import org.apache.dubbo.config.annotation.DubboService;

import javax.annotation.Resource;


/**
 * <AUTHOR>
 * @description: 请在此添加描述
 * @date 2024/10/24 9:23
 * @since JDK 1.8
 */
@DubboService
public class CmBeisenOrgConfigFacadeImpl implements CmBeisenOrgConfigFacade {

    @Resource
    private CmBeisenOrgConfigService cmBeisenOrgConfigService;
    /**
     * @param queryBeisenOrgConfigRequest
     * @return com.howbuy.crm.account.client.response.Response
     * @description 查询列表
     * @author: jianjian.yang
     * @date: 2024/10/22 19:30
     * @since JDK 1.8
     */
    @Override
    public Response<PageVO<CmBeisenOrgConfigDetailVO>> queryBeisenOrgConfigList(QueryBeisenOrgConfigRequest queryBeisenOrgConfigRequest) {
        return Response.ok(cmBeisenOrgConfigService.query(queryBeisenOrgConfigRequest));
    }

    /**
     * @param insertBeisenOrgConfigRequest
     * @return com.howbuy.crm.account.client.response.Response
     * @description 新增配置
     * @author: jianjian.yang
     * @date: 2024/10/22 19:31
     * @since JDK 1.8
     */
    @Override
    public Response<String> insertBeisenOrgConfig(InsertBeisenOrgConfigRequest insertBeisenOrgConfigRequest) {
        return Response.ok(cmBeisenOrgConfigService.insertBeisenOrgConfig(insertBeisenOrgConfigRequest));
    }

    /**
     * @param updateBeisenOrgConfigRequest
     * @return com.howbuy.crm.account.client.response.Response
     * @description 更新配置
     * @author: jianjian.yang
     * @date: 2024/10/22 19:31
     * @since JDK 1.8
     */
    @Override
    public Response<String> updateBeisenOrgConfig(UpdateBeisenOrgConfigRequest updateBeisenOrgConfigRequest) {
        return Response.ok(cmBeisenOrgConfigService.updateBeisenOrgConfig(updateBeisenOrgConfigRequest));
    }

    /**
     * @param beisenConfigIdRequest
     * @return com.howbuy.crm.account.client.response.Response
     * @description 删除配置
     * @author: jianjian.yang
     * @date: 2024/10/22 19:31
     * @since JDK 1.8
     */
    @Override
    public Response<String> deleteBeisenOrgConfig(BeisenConfigIdRequest beisenConfigIdRequest) {
        return Response.ok(cmBeisenOrgConfigService.deleteBeisenOrgConfig(beisenConfigIdRequest.getId()));
    }

    /**
     * @param beisenConfigIdRequest
     * @return com.howbuy.crm.account.client.response.Response
     * @description 查单个明细
     * @author: jianjian.yang
     * @date: 2024/10/22 19:31
     * @since JDK 1.8
     */
    @Override
    public Response<CmBeisenOrgConfigDetailVO> getConfigDetail(BeisenConfigIdRequest beisenConfigIdRequest) {
        return Response.ok(cmBeisenOrgConfigService.getBeisenOrgConfigDetail(beisenConfigIdRequest.getId()));
    }

    /**
     * @param request
     * @return com.howbuy.crm.account.client.response.Response<com.howbuy.crm.account.client.response.ExportToFileVO>
     * @description 导出
     * @author: jianjian.yang
     * @date: 2024/11/11 13:55
     * @since JDK 1.8
     */
    @Override
    public Response<ExportToFileVO> exportBeisenOrgConfigList(QueryBeisenOrgConfigRequest request) {
        return Response.ok(cmBeisenOrgConfigService.export(request));
    }

    /**
     * @description 查北森机构列表
     * @param
     * @return com.howbuy.crm.account.client.response.Response<com.howbuy.crm.account.client.response.beisen.CmBeisenOrgVO>
     * @author: jianjian.yang
     * @date: 2024/11/19 18:04
     * @since JDK 1.8
     */
    public Response<CmBeisenOrgVO> queryBeisenOrg() {
        return Response.ok(cmBeisenOrgConfigService.queryBeisenOrg());
    }
}