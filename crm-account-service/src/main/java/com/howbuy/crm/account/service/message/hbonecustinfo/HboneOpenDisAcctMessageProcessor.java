/**
 * Copyright (c) 2023, <PERSON><PERSON><PERSON><PERSON> HOWBUY INVESTMENT MANAGEMENT Co., Ltd.
 * All right reserved.
 * <p>
 * THIS IS UNPUBLISHED PROPRIETARY SOURCE CODE OF HOWBUY INVESTMENT
 * MANAGEMENT CO., LTD.  THE CONTENTS OF THIS FILE MAY NOT BE DISCLOSED
 * TO THIRD PARTIES, COPIED OR DUPLICATED IN ANY FORM, IN WHOLE OR IN PART,
 * WITHOUT THE PRIOR WRITTEN PERMISSION OF HOWBUY INVESTMENT MANAGEMENT
 * CO., LTD.
 */
package com.howbuy.crm.account.service.message.hbonecustinfo;

import com.alibaba.fastjson.JSON;
import com.howbuy.common.utils.StringUtil;
import com.howbuy.crm.account.client.enums.custinfo.ConsCustInvestTypeEnum;
import com.howbuy.crm.account.client.enums.custinfo.FullCustSourceEnum;
import com.howbuy.crm.account.client.response.Response;
import com.howbuy.crm.account.dao.bo.custinfo.CmConscustForAnalyseBO;
import com.howbuy.crm.account.service.business.custinfo.CmCustBusinessService;
import com.howbuy.crm.account.service.business.custinfo.CmCustProcessBusinessService;
import com.howbuy.crm.account.service.commom.constant.Constants;
import com.howbuy.crm.account.service.message.dto.HboneOpenDisAcctMessageDTO;
import com.howbuy.crm.account.service.outerservice.auth.EncyptAndDecyptOuterService;
import com.howbuy.crm.account.service.repository.custinfo.AbnormalCustRepository;
import com.howbuy.crm.account.service.vo.custinfo.AbnormalAnalyseResultVO;
import com.howbuy.crm.account.service.vo.custinfo.HboneMessageCustInfoVO;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;

import java.util.Arrays;
import java.util.List;

/**
 * @description: ([一账通账户]-[开通分销交易账户] 消息处理器[topic.hbone.openAcct])
 * <AUTHOR>
 * @date 2023/12/11 17:04
 * @since JDK 1.8
 */
@Slf4j
@Service
public class HboneOpenDisAcctMessageProcessor extends AbstractHboneMessageProcessor<HboneOpenDisAcctMessageDTO> {


    /**
     * [TOPIC_OPEN_ACC]  一账通开户
     */
    @Value("${topic.hbone.openAcct}")
    private String topicHboneOpenAcct;


    /**
     * 开通分销交易账户
     * [OPEN_TX_ACC]
     */
    @Value("${tag.hbone.openDisAcct}")
    private String tagName;


    //消息体 示例：
//    {
//        "clientId": "fe369569cfb34e94b204e1f791f8bc12",
//       "content": {
//          "body": {
//                    "cityCode": null,
//                    "disCode": "UNIONPAY1",
//                    "disTxAcctNo": "1430554504UNIONPAY1",
//                    "hboneNo": "8011176826",
//                    "hkCustNo": null,
//                    "incLevel": null,
//                    "outletCode": "CO2305W01",
//                    "provCode": null,
//                    "regDate": "20240105",
//                    "vocation": null
//           },
//            "head": {
//                    "createTime": "20240105125444",
//                    "eventCode": "520005",
//                    "hboneNo": "8011176826",
//                    "msgKey": "ea1952200d3ca15ba7043e1dec31a974",
//                    "tag": "OPEN_TX_ACC"
//            }
//    },
//        "messageChannel": "TOPIC_OPEN_ACC"
//    }


    @Autowired
    private CmCustProcessBusinessService processBusinessService;


    @Autowired
    private CmCustBusinessService cmBusinessService;

    @Autowired
    private EncyptAndDecyptOuterService encyptAndDecyptOuterService;

    @Autowired
    private AbnormalCustRepository abnormalCustRepository;

    @Override
    public String getQuartMessageChannel() {
        return topicHboneOpenAcct;
    }

    @Override
    public List<String> getSupportTagList() {
        return Arrays.asList(tagName);
    }



    @Override
    HboneMessageCustInfoVO constructByMessage(HboneOpenDisAcctMessageDTO  acctMsgDto) {
        HboneMessageCustInfoVO custVo=new HboneMessageCustInfoVO();
        custVo.setHboneNo(acctMsgDto.getHboneNo());
//        custVo.setCustName(null);
        // 一账通开户消息 默认数据赋值： 客户类别=个人客户
        custVo.setInvestType(ConsCustInvestTypeEnum.PERSONAL.getCode());
        // 一账通开户消息 默认数据赋值： 手机地区码=86
        custVo.setMobileAreaCode(Constants.DEFAULT_MOBILE_AREA_CODE);
        custVo.setRegDate(acctMsgDto.getRegDate());
        custVo.setDisCode(acctMsgDto.getDisCode());

        fillHboneAcctInfo(custVo);
        return custVo;
    }

    @Override
    public FullCustSourceEnum getCustSource() {
        return FullCustSourceEnum.DISTRIBUTION_OPEN_ACCOUNT;
    }

    @Override
    Response<String> processHboneMessage(AbnormalAnalyseResultVO<HboneMessageCustInfoVO> resultVo) {
        CmConscustForAnalyseBO processedCustInfo= resultVo.getProcessedCustInfo();
        HboneMessageCustInfoVO analyseVo=resultVo.getAnalyseVo();
        String hboneNo=analyseVo.getHboneNo();

        if(processedCustInfo==null){
            //新增客户
            Response<String> createResp
                    =processBusinessService.createCustInfoByHbone(analyseVo);
            log.info("一账通开户消息处理： 一账通号[{}] 新增客户，执行结果：{}",hboneNo, JSON.toJSONString(createResp));
            String custNo=createResp.getData();
            if(StringUtil.isEmpty(custNo)){
                return Response.fail(createResp.getDescription());
            }
            //新增客户 传递 作为后续 待处理 对象
            processedCustInfo=abnormalCustRepository.queryCustBOByCustNo(custNo);
        }else{
            //更新客户
            processBusinessService.updateCustInfoByHbone(analyseVo,processedCustInfo);
        }

        //关联一账通号 vs 投顾客户号
        processBusinessService.associateHboneAcctRelation(processedCustInfo,analyseVo);

        //节点4-CRM判断是否同时绑定香港客户号
        String hkTxAcctNo = analyseVo.getHkTxAcctNo();
        if(StringUtil.isNotBlank(hkTxAcctNo)){
            return processBusinessService.processExtraHkAccount(analyseVo);
        }

        return Response.ok();
    }



}