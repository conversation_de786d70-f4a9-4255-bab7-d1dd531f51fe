/**
 * Copyright (c) 2025, <PERSON><PERSON><PERSON><PERSON> HOWBUY INVESTMENT MANAGEMENT Co., Ltd.
 * All right reserved.
 * <p>
 * THIS IS UNPUBLISHED PROPRIETARY SOURCE CODE OF HOWBUY INVESTMENT
 * MANAGEMENT CO., LTD.  THE CONTENTS OF THIS FILE MAY NOT BE DISCLOSED
 * TO THIRD PARTIES, COPIED OR DUPLICATED IN ANY FORM, IN WHOLE OR IN PART,
 * WITHOUT THE PRIOR WRITTEN PERMISSION OF HOWBUY INVESTMENT MANAGEMENT
 * CO., LTD.
 */
package com.howbuy.crm.account.service.outerservice.center;

import com.alibaba.fastjson2.JSON;
import com.howbuy.cc.center.member.tag.request.QueryUserTagsListRequest;
import com.howbuy.cc.center.member.tag.response.QueryUserTagsListResponse;
import com.howbuy.cc.center.member.tag.service.QueryUserTagsListService;
import com.howbuy.crm.account.client.enums.ExceptionCodeEnum;
import com.howbuy.crm.account.client.enums.YesOrNoEnum;
import com.howbuy.crm.account.service.commom.constant.DobboReferenceConstant;
import com.howbuy.crm.account.service.commom.exception.BusinessException;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.dubbo.config.annotation.DubboReference;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.List;

/**
 * @description: 大数据中心外部服务
 * <AUTHOR>
 * @date 2025-07-01 10:30:00
 * @since JDK 1.8
 */
@Slf4j
@Service
public class CenterDataOuterService {

    @DubboReference(registry = DobboReferenceConstant.CENTER_MEMBER_SERVICE, check = false)
    private QueryUserTagsListService queryUserTagsListService;

    /**
     * 大V标签号
     */
    private static final String BIG_V_TAG_NO = "10250";
    /**
     * 计提公募返回成功状态
     */
    private static final String IS_BIG_V_SUCCESS = "0000";

    /**
     * @description: 查询用户是否计提公募
     * @param hboneNo 一账通号
     * @return String 是否计提公募（1：是/2：否）
     * @author: shijie.wang
     * @date: 2025/1/17 10:30
     * @since JDK 1.8
     */
    public String queryIsBigV(String hboneNo) {
        try {
            if (hboneNo == null || hboneNo.trim().isEmpty()) {
                log.info("一账通号为空，无法查询是否计提公募");
                return YesOrNoEnum.YES.getCode();
            }
            log.info("开始查询一账通号：{} 的大V标签", hboneNo);
            // 注意：以下代码需要根据实际的center-client接口进行调整
            QueryUserTagsListRequest tagRequest = new QueryUserTagsListRequest();
            tagRequest.setHboneNo(hboneNo);
            List<String> tagNos = new ArrayList<>();
            tagNos.add(BIG_V_TAG_NO);
            tagRequest.setTagNos(tagNos);
            log.info("QueryUserTagsListRequest: {}", JSON.toJSONString(tagRequest));
            QueryUserTagsListResponse tagResponse = queryUserTagsListService.execute(tagRequest);
            log.info("QueryUserTagsListResponse: {}", JSON.toJSONString(tagResponse));
            // 是否计提公募 大数据有标签，取是否大V，若是，则显示"否:0"，若否，则显示"是:1"
            if (tagResponse != null && IS_BIG_V_SUCCESS.equals(tagResponse.getReturnCode())
                && CollectionUtils.isNotEmpty(tagResponse.getUserTagsSet()) && tagResponse.getUserTagsSet().size() == 1) {
                log.info("一账通号：{} 是大V客户，不计提公募", hboneNo);
                return YesOrNoEnum.NO.getCode();
            } else {
                log.info("一账通号：{} 不是大V客户，计提公募", hboneNo);
                return YesOrNoEnum.YES.getCode();
            }
        } catch (Exception e) {
            log.error("查询一账通号：{} 是否计提公募异常：{}", hboneNo, e.getMessage(), e);
            throw new BusinessException(ExceptionCodeEnum.SYSTEM_ERROR);
        }
    }
} 