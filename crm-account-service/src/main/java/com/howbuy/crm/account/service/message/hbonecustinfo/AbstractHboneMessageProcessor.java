/**
 * Copyright (c) 2023, <PERSON><PERSON><PERSON><PERSON> HOWBUY INVESTMENT MANAGEMENT Co., Ltd.
 * All right reserved.
 * <p>
 * THIS IS UNPUBLISHED PROPRIETARY SOURCE CODE OF HOWBUY INVESTMENT
 * MANAGEMENT CO., LTD.  THE CONTENTS OF THIS FILE MAY NOT BE DISCLOSED
 * TO THIRD PARTIES, COPIED OR DUPLICATED IN ANY FORM, IN WHOLE OR IN PART,
 * WITHOUT THE PRIOR WRITTEN PERMISSION OF HOWBUY INVESTMENT MANAGEMENT
 * CO., LTD.
 */
package com.howbuy.crm.account.service.message.hbonecustinfo;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.howbuy.crm.account.client.enums.custinfo.AbnormaSceneTypeEnum;
import com.howbuy.crm.account.client.enums.custinfo.FullCustSourceEnum;
import com.howbuy.crm.account.client.enums.message.AccountTypeEnum;
import com.howbuy.crm.account.client.response.PageVO;
import com.howbuy.crm.account.client.response.Response;
import com.howbuy.crm.account.client.response.custinfo.HboneAcctCustDetailInfoVO;
import com.howbuy.crm.account.client.response.custinfo.HkAcctCustInfoVO;
import com.howbuy.crm.account.dao.bo.custinfo.CmConscustForAnalyseBO;
import com.howbuy.crm.account.dao.po.message.CmAccountCenterMessagePO;
import com.howbuy.crm.account.service.business.custinfo.CmCustProcessBusinessService;
import com.howbuy.crm.account.service.commom.constant.Constants;
import com.howbuy.crm.account.service.commom.utils.AbnormalCustUtils;
import com.howbuy.crm.account.service.message.AbstractTagMessageProcessor;
import com.howbuy.crm.account.service.outerservice.acct.HboneAcctInfoOuterService;
import com.howbuy.crm.account.service.outerservice.hkacct.HkCustBasicInfoOuterService;
import com.howbuy.crm.account.service.repository.custinfo.AbnormalCustRepository;
import com.howbuy.crm.account.service.repository.message.CmAccountCenterMessageRepository;
import com.howbuy.crm.account.service.req.custinfo.HkAcctCustInfoReqVO;
import com.howbuy.crm.account.service.service.custmessage.CustMessageAnalyseFactory;
import com.howbuy.crm.account.service.vo.custinfo.AbnormalAnalyseResultVO;
import com.howbuy.crm.account.service.vo.custinfo.HboneMessageCustInfoVO;
import com.howbuy.message.MessageService;
import com.howbuy.message.SimpleMessage;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.lang.reflect.ParameterizedType;
import java.lang.reflect.Type;
import java.util.List;

/**
 * @description: ([一账通账户]-消息处理器)
 * <AUTHOR>
 * @date 2023/12/11 17:04
 * @since JDK 1.8
 */
@Service
@Slf4j
public abstract class AbstractHboneMessageProcessor<T> extends AbstractTagMessageProcessor {


    @Autowired
    private CmCustProcessBusinessService cmProcessBusinessService;

    @Autowired
    private HboneAcctInfoOuterService hboneAcctInfoOuterService;

    @Autowired
    private CustMessageAnalyseFactory messageAnalyseFactory;

    @Autowired
    private AbnormalCustRepository abnormalCustRepository;

    @Autowired
    private HkCustBasicInfoOuterService hkCustInfoOuterService;

    @Autowired
    private CmAccountCenterMessageRepository cmAccountCenterMessageRepository;


    protected Class<T> accClazz;


    protected AbstractHboneMessageProcessor() {
        // https://stackoverflow.com/questions/8474814/spring-cglib-why-cant-a-generic-class-be-proxied?noredirect=1
        // CGLIB代理的子类没有泛型，故此处强转ParameterizedType会报错
        Type type = getClass();
        do {
            type = ((Class<T>) type).getGenericSuperclass();
        } while (!(type instanceof ParameterizedType));
        this.accClazz = (Class<T>) ((ParameterizedType) type).getActualTypeArguments()[0];
//        this.clazz = String.class;
    }


    /**
     * @description:(如果 getSupportTagList 为空，则忽略tag标签，直接使用 topic)
     * @param
     * @return void
     * @author: haoran.zhang
     * @date: 2023/12/29 13:48
     * @since JDK 1.8
     */
    @Override
    public void init() {
        //如果没有指定tag，则 忽略tag 标签 直接使用 topic
        if(CollectionUtils.isEmpty(getSupportTagList())){
            log.info("添加[TAG]MessageProcessor：channel：{}, processor：{}", getQuartMessageChannel(), this);
            // 加载消息处理器
            MessageService.getInstance().addMessageProcessor(this.getQuartMessageChannel(), this);
        }else{
            log.info("添加[TOPIC]MessageProcessor：channel：{}, tag：{}, processor：{}", getQuartMessageChannel(), getTag(), this);
            MessageService.getInstance().addMessageProcessor(getQuartMessageChannel(), getTag(), this);
        }

    }

    /**
     * 获取消息来源
     * @return
     */
    abstract FullCustSourceEnum getCustSource();


    /**
     * 操作通道 1-MQ
     * @return
     */
    public String getOperateChannel(){
        return Constants.OPERATE_CHANNEL_MQ;
    }


    /**
     * 根据消息体  构建消息处理对象
     * @param acctMsgDto
     * @return
     */
    abstract HboneMessageCustInfoVO constructByMessage(T acctMsgDto);


    /**
     * 请求一账通 账户中心  填充 一账通 账户信息
     * @param custVo
     */
    protected void fillHboneAcctInfo(HboneMessageCustInfoVO custVo){
        String hboneNo=custVo.getHboneNo();
        HboneAcctCustDetailInfoVO  hboneInfo=hboneAcctInfoOuterService.queryHboneCustDetailInfo( hboneNo);
        custVo.setCustName(hboneInfo.getCustName());
        custVo.setInvestType(hboneInfo.getInvstType());
        custVo.setMobileAreaCode(hboneInfo.getMobileAreaCode());
        custVo.setMobileDigest(hboneInfo.getMobileDigest());
        custVo.setMobileMask(hboneInfo.getMobileMask());
        custVo.setIdSignAreaCode(hboneInfo.getIdSignAreaCode());
        custVo.setIdType(hboneInfo.getIdType());
        custVo.setIdNoDigest(hboneInfo.getIdNoDigest());
        custVo.setIdNoMask(hboneInfo.getIdNoMask());

        //获取  香港客户号
        HkAcctCustInfoVO hkDetailInfo=null;
        HkAcctCustInfoReqVO queryHkVo = new HkAcctCustInfoReqVO();
        queryHkVo.setHboneNo(hboneNo);
        queryHkVo.setCustStatList(AbnormalCustUtils.getValidHkCustStateList());
        PageVO<HkAcctCustInfoVO> fullMatchResp = hkCustInfoOuterService.queryHkCustInfoPage(queryHkVo);
        if (fullMatchResp != null &&
                CollectionUtils.isNotEmpty(fullMatchResp.getRows()) &&
                fullMatchResp.getRows().size() == 1) {
            hkDetailInfo= fullMatchResp.getRows().get(0);
        }
        if(hkDetailInfo!=null){
            custVo.setHkTxAcctNo(hkDetailInfo.getHkTxAcctNo());
            log.info("一账通消息处理：一账通号[{}]补充关联的香港客户号：{}！",hboneNo,hkDetailInfo.getHkTxAcctNo());
        }

//        custVo.setHboneNo(hboneInfo.geth);
    }


    /**
     * 实际业务处理-处理 一账通消息
     * @param resultVo
     */
    abstract Response<String> processHboneMessage(AbnormalAnalyseResultVO<HboneMessageCustInfoVO> resultVo);

    @Override
    protected void doProcessMessage(SimpleMessage message) {
        // 将消息保存到数据库
        saveMessage(message);
        // 处理消息
        dealMessage(message);
    }

    /**
     * @description:(请在此添加描述)
     * @param message
     * @return com.howbuy.crm.account.client.response.Response<java.lang.String>
     * @author: jin.wang03
     * @date: 2025/5/30 15:58
     * @since JDK 1.8
     */
    public Response<String> dealMessage(SimpleMessage message) {
        // 解析消息
        HboneMessageCustInfoVO messgeCustVo = getHboneMessageCustInfoVO(message);
        // 对拼装后的消息，进行分析和处理
        return analyzeAndProcess(messgeCustVo);
    }

    private HboneMessageCustInfoVO getHboneMessageCustInfoVO(SimpleMessage message) {
        String msgContent=(String) message.getContent();
        JSONObject json = JSON.parseObject(msgContent);
        // body为字符串，需要再次解析
        String body = json.getString(Constants.MQ_BODY_KEY);
        //转译 消息对象
        T acctMsgDto = JSON.parseObject(body, accClazz);

        //根据 acct消息对象 构建 crm待处理 消息对象
        HboneMessageCustInfoVO  messgeCustVo=constructByMessage(acctMsgDto);

        //设置异常来源
        FullCustSourceEnum sourceEnum = getCustSource();
        messgeCustVo.setAbnormalSource(sourceEnum.getCode());
        //设置异常 渠道
        messgeCustVo.setOperateChannel(getOperateChannel());
        //设置 消息 clientID
        messgeCustVo.setMessageClientId(message.getClientId());
        // 消息ID
        messgeCustVo.setMessageId(message.getMessageId());
        //消息处理人：sys
        messgeCustVo.setCreator(Constants.OPERATOR_SYS);
        return messgeCustVo;
    }


    private Response<String> analyzeAndProcess(HboneMessageCustInfoVO messgeCustVo) {
        //待处理数据 进行异常分析
        AbnormalAnalyseResultVO<HboneMessageCustInfoVO> resultVo=messageAnalyseFactory.analyze(messgeCustVo);
        log.info("消息处理，来源：{}，一账通交易账号：{}，香港交易账号：{}，是否解析通过：{}，是否需要处理：{}",
                FullCustSourceEnum.getDescription(messgeCustVo.getAbnormalSource()),
                messgeCustVo.getHboneNo(),
                messgeCustVo.getHkTxAcctNo(),
                resultVo.isAnalysePass(),
                resultVo.isNeedProcess());
        //异常分析结果：通过，再做后续的 业务处理
        if(!resultVo.isAnalysePass()){
            cmProcessBusinessService.insertHboneProcessAbnormal(messgeCustVo,resultVo.getSceneTypeEnum(),resultVo.getRelatedCustList());
            return Response.fail();
        }
        if(!resultVo.isNeedProcess()){
            return Response.ok();
        }
        //处理一账通  消息
        Response<String> prcessResp= processHboneMessage(resultVo);
        log.info("消息：{}，处理结果：{}",JSON.toJSONString(messgeCustVo),JSON.toJSONString(prcessResp));
        return prcessResp;
    }


    /**
     * 根据一账通号 查询 关联的 待处理 投顾客户信息
     * 注意 ：如果一账通号绑定了多个投顾客户号，则 进入异常数据表
     * @param analyseVo
     * @return
     */
    public CmConscustForAnalyseBO getAnalyseBoByHboneNo(HboneMessageCustInfoVO analyseVo){
        String hboneNo=analyseVo.getHboneNo();
        List<CmConscustForAnalyseBO> hbCustList=abnormalCustRepository.queryCustBOByHboneNo(hboneNo);
        if(CollectionUtils.isEmpty(hbCustList)){
            log.info("【{}】消息处理：该一账通号[{}]未绑定投顾客户号！",
                    FullCustSourceEnum.getDescription(analyseVo.getAbnormalSource()),
                    hboneNo);
            return  null;
        }
        //开发自定义流程： 历史数据存在hboneNo绑定多个custNo 的情况，此处不做处理
        if(hbCustList.size()>1){
            log.error("【{}】消息处理异常：该一账通号[{}]绑定了多个投顾客户号",
                    FullCustSourceEnum.getDescription(analyseVo.getAbnormalSource()),
                    hboneNo);
            cmProcessBusinessService.insertHboneProcessAbnormal(analyseVo,
                    AbnormaSceneTypeEnum.HBONE_NO_MATCH_MULTIPLE_CUST_NO,
                    hbCustList);
            return null;
        }
        CmConscustForAnalyseBO custPo=hbCustList.get(0);
        log.info("【{}】消息处理：该一账通号[{}]已绑定投顾客户号：{}！",
                FullCustSourceEnum.getDescription(analyseVo.getAbnormalSource()),
                hboneNo,
                custPo.getConscustno());
        return custPo;
    }

    /**
     * @description: 将消息保存到数据库
     * @param message
     * @return void
     * @author: jin.wang03
     * @date: 2025/5/30 15:09
     * @since JDK 1.8
     */
    private void saveMessage(SimpleMessage message) {
        CmAccountCenterMessagePO cmAccountCenterMessagePO = new CmAccountCenterMessagePO();
        cmAccountCenterMessagePO.setId(message.getMessageId());
        cmAccountCenterMessagePO.setAccountType(AccountTypeEnum.HBONE.getCode());
        cmAccountCenterMessagePO.setTopic(message.getMessageChannel());
        cmAccountCenterMessagePO.setTag(message.getTag());
        cmAccountCenterMessagePO.setMqMessageText(JSON.toJSONString(message));
        cmAccountCenterMessageRepository.insert(cmAccountCenterMessagePO);
    }

}