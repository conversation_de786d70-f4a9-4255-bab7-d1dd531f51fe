/**
 * Copyright (c) 2024, <PERSON><PERSON><PERSON><PERSON> HOWBUY INVESTMENT MANAGEMENT Co., Ltd.
 * All right reserved.
 * <p>
 * THIS IS UNPUBLISHED PROPRIETARY SOURCE CODE OF HOWBUY INVESTMENT
 * MANAGEMENT CO., LTD.  THE CONTENTS OF THIS FILE MAY NOT BE DISCLOSED
 * TO THIRD PARTIES, COPIED OR DUPLICATED IN ANY FORM, IN WHOLE OR IN PART,
 * WITHOUT THE PRIOR WRITTEN PERMISSION OF HOWBUY INVESTMENT MANAGEMENT
 * CO., LTD.
 */
package com.howbuy.crm.account.service.vo.custinfo;

import lombok.EqualsAndHashCode;

/**
 * @description: CRM资料[好买香港-开户],开户审核通过之后，账户柜台会返回 香港客户号、一账通号
 *    ；这里 我们需要和上传资料的投顾客户进行处理，所以 投顾客户号 也作为分析的字段
 * <AUTHOR>
 * @date 2024/12/6 11:20
 * @since JDK 1.8
 */

@EqualsAndHashCode(callSuper = true)
public class CrmHkOfflineOpenAcctCustInfoVO extends HkMessageCustInfoVO {


    /**
     * CRM资料管理[好买香港-开户]使用的 投顾客户号
     */
    private String dealConsCustNo;

    public String getDealConsCustNo() {
        return dealConsCustNo;
    }

    public void setDealConsCustNo(String dealConsCustNo) {
        this.dealConsCustNo = dealConsCustNo;
    }
}