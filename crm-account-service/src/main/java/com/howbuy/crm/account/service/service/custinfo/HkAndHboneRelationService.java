/**
 * Copyright (c) 2023, <PERSON><PERSON><PERSON><PERSON> HOWBUY INVESTMENT MANAGEMENT Co., Ltd.
 * All right reserved.
 * <p>
 * THIS IS UNPUBLISHED PROPRIETARY SOURCE CODE OF HOWBUY INVESTMENT
 * MANAGEMENT CO., LTD.  THE CONTENTS OF THIS FILE MAY NOT BE DISCLOSED
 * TO THIRD PARTIES, COPIED OR DUPLICATED IN ANY FORM, IN WHOLE OR IN PART,
 * WITHOUT THE PRIOR WRITTEN PERMISSION OF HOWBUY INVESTMENT MANAGEMENT
 * CO., LTD.
 */
package com.howbuy.crm.account.service.service.custinfo;

import com.howbuy.common.utils.StringUtil;
import com.howbuy.crm.account.client.response.custinfo.CustRelatedAcctInfoVO;
import com.howbuy.crm.account.client.response.custinfo.HboneTxAcctInfoVO;
import com.howbuy.crm.account.client.response.custinfo.HkAndHboneRelationVO;
import com.howbuy.crm.account.dao.bo.custinfo.CmConsCustSimpleBO;
import com.howbuy.crm.account.dao.po.custinfo.CmHkConscustPO;
import com.howbuy.crm.account.service.outerservice.acct.HboneAcctInfoOuterService;
import com.howbuy.crm.account.service.outerservice.hkacct.HkCustBasicInfoOuterService;
import com.howbuy.crm.account.service.repository.custinfo.CmHkCustInfoRepository;
import com.howbuy.crm.account.service.repository.custinfo.ConsCustInfoRepository;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

/**
 * @description: (crm客户信息service)
 * <AUTHOR>
 * @date 2023/12/13 16:41
 * @since JDK 1.8
 */
@Service
@Slf4j
public class HkAndHboneRelationService {

    @Autowired
    private HkCustBasicInfoOuterService hkCustBasicInfoOuterService;

    @Autowired
    private ConsCustInfoRepository consCustInfoRepository;

    @Autowired
    private CmHkCustInfoRepository hkCustRepository;

    @Autowired
    private HboneAcctInfoOuterService hboneCustInfoOuterService;

    /**
     * @description:(查询  [账户中心侧] 香港客户和一账通客户绑定关系)
     * @param vo
     * @return com.howbuy.crm.account.client.response.custinfo.HkAndHboneRelationVO
     * @author: haoran.zhang
     * @date: 2024/5/28 15:37
     * @since JDK 1.8
     */
    public HkAndHboneRelationVO getHkAndHboneRelation(HkAndHboneRelationVO vo){
         return hkCustBasicInfoOuterService.getHkAndHboneRelation(vo);
    }


    /**
     * @description:(根据crm客户号，获取crm客户号关联的一账通号、公募客户号、香港交易账号)
     * @param custNo
     * @return com.howbuy.crm.account.client.response.custinfo.CustRelatedAcctInfoVO
     * @author: haoran.zhang
     * @date: 2024/8/15 18:50
     * @since JDK 1.8
     */
    public CustRelatedAcctInfoVO getCustRelatedAcctInfo(String custNo){

        CmConsCustSimpleBO custBo= consCustInfoRepository.queryCustSimpleInfo(custNo);
        if(custBo==null){
            return null;
        }
        CustRelatedAcctInfoVO returnVo=new CustRelatedAcctInfoVO();
        returnVo.setCustNo(custBo.getConscustno());
        //crm关联的 一账通号
        String hboneNo=custBo.getHboneNo();
        //crm关联的 香港客户号
        String crmHkTxAcctNo=custBo.getHkTxAcctNo();
        returnVo.setHboneNo(hboneNo);
        returnVo.setCrmHkTxAcctNo(crmHkTxAcctNo);




        //一账通 账户中心 获取属性
        if(StringUtil.isNotBlank(hboneNo)){
            //一账通 关联的 香港客户号
            HkAndHboneRelationVO reqVo=new HkAndHboneRelationVO();
            reqVo.setHboneNo(hboneNo);
            HkAndHboneRelationVO relationVO=getHkAndHboneRelation(reqVo);
            returnVo.setHboneHkTxAcctNo(relationVO==null?null:relationVO.getHkTxAcctNo());
            //一账通 查询 公募客户号
            HboneTxAcctInfoVO  txAcctVo=hboneCustInfoOuterService.queryTxAcctInfo(hboneNo);
            if(txAcctVo!=null){
                returnVo.setTxAcctNo(txAcctVo.getTxAcctNo());
            }
        }
        //香港账户中心 获取属性
        if(StringUtil.isNotBlank(crmHkTxAcctNo)){
            //一账通 关联的 香港客户号
            HkAndHboneRelationVO reqVo=new HkAndHboneRelationVO();
            reqVo.setHkTxAcctNo(crmHkTxAcctNo);
            HkAndHboneRelationVO relationVO=getHkAndHboneRelation(reqVo);
            returnVo.setHkHboneNo(relationVO==null?null:relationVO.getHboneNo());
        }
        return returnVo;
    }
}