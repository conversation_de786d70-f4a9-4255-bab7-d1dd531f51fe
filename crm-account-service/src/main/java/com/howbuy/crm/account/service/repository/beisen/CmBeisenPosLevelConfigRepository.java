/**
 * Copyright (c) 2024, <PERSON><PERSON><PERSON><PERSON> HOWBUY INVESTMENT MANAGEMENT Co., Ltd.
 * All right reserved.
 * <p>
 * THIS IS UNPUBLISHED PROPRIETARY SOURCE CODE OF HOWBUY INVESTMENT
 * MANAGEMENT CO., LTD.  THE CONTENTS OF THIS FILE MAY NOT BE DISCLOSED
 * TO THIRD PARTIES, COPIED OR DUPLICATED IN ANY FORM, IN WHOLE OR IN PART,
 * WITHOUT THE PRIOR WRITTEN PERMISSION OF HOWBUY INVESTMENT MANAGEMENT
 * CO., LTD.
 */
package com.howbuy.crm.account.service.repository.beisen;


import com.howbuy.crm.account.dao.bo.beisenposlevel.CmBeisenPosLevelConfigBO;
import com.howbuy.crm.account.dao.mapper.beisenposlevel.CmBeisenPosLevelConfigMapper;
import com.howbuy.crm.account.dao.po.beisenposlevel.CmBeisenPosLevelConfigPO;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.List;


/**
 * @description: (请在此添加描述)
 * <AUTHOR>
 * @date 2024/11/14 19:08
 * @since JDK 1.8
 */
@Slf4j
@Component
public class CmBeisenPosLevelConfigRepository {
    @Autowired
    private CmBeisenPosLevelConfigMapper cmBeisenPosLevelConfigMapper;

    /**
     * @description:(查询北森职级映射列表数据)
     * @param po
     * @return java.util.List<com.howbuy.crm.account.dao.bo.beisenposlevel.CmBeisenPosLevelConfigBO>
     * @author: shijie.wang
     * @date: 2024/11/14 19:15
     * @since JDK 1.8
     */
    public List<CmBeisenPosLevelConfigBO> queryBeisenPosLevelConfigList(CmBeisenPosLevelConfigPO po) {
        return cmBeisenPosLevelConfigMapper.selectPosLevelConfig(po);
    }

    /**
     * @description:(保存好买北森职级配置表信息)
     * @param po
     * @return void
     * @author: shijie.wang
     * @date: 2024/11/14 19:15
     * @since JDK 1.8
     */
    public void savePosLevelConfig(CmBeisenPosLevelConfigPO po) {
        cmBeisenPosLevelConfigMapper.savePosLevelConfig(po);
    }

    /**
     * @description:(通过id删除好买北森职级配置表信息)
     * @param id
     * @return void
     * @author: shijie.wang
     * @date: 2024/11/14 19:16
     * @since JDK 1.8
     */
    public void deletePosLevelConfig(Long id) {
        cmBeisenPosLevelConfigMapper.deletePosLevelConfig(id);
    }

    /**
     * @description:(查询详细数据)
     * @param id
     * @return com.howbuy.crm.account.dao.bo.beisenposlevel.CmBeisenPosLevelConfigBO
     * @author: shijie.wang
     * @date: 2024/11/14 19:18
     * @since JDK 1.8
     */
    public CmBeisenPosLevelConfigBO selectPosLevelConfigById(Long id){
        return cmBeisenPosLevelConfigMapper.selectPosLevelConfigById(id);
    }

}