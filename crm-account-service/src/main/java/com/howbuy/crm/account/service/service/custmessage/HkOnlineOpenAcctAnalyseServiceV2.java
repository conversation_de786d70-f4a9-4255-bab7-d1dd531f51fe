///**
// * Copyright (c) 2025, <PERSON><PERSON><PERSON><PERSON> HOWBUY INVESTMENT MANAGEMENT Co., Ltd.
// * All right reserved.
// * <p>
// * THIS IS UNPUBLISHED PROPRIETARY SOURCE CODE OF HOWBUY INVESTMENT
// * MANAGEMENT CO., LTD.  THE CONTENTS OF THIS FILE MAY NOT BE DISCLOSED
// * TO THIRD PARTIES, COPIED OR DUPLICATED IN ANY FORM, IN WHOLE OR IN PART,
// * WITHOUT THE PRIOR WRITTEN PERMISSION OF HOWBUY INVESTMENT MANAGEMENT
// * CO., LTD.
// */
//package com.howbuy.crm.account.service.service.custmessage;
//
//import com.google.common.collect.Lists;
//import com.howbuy.common.utils.StringUtil;
//import com.howbuy.crm.account.client.enums.custinfo.AbnormaSceneTypeEnum;
//import com.howbuy.crm.account.client.enums.custinfo.FullCustSourceEnum;
//import com.howbuy.crm.account.dao.bo.custinfo.CmConscustForAnalyseBO;
//import com.howbuy.crm.account.dao.po.custinfo.CmHkConscustPO;
//import com.howbuy.crm.account.dao.req.custinfo.CmHkCustReqVO;
//import com.howbuy.crm.account.service.repository.custinfo.AbnormalCustRepository;
//import com.howbuy.crm.account.service.repository.custinfo.CmHkCustInfoRepository;
//import com.howbuy.crm.account.service.vo.custinfo.AbnormalAnalyseResultVO;
//import com.howbuy.crm.account.service.vo.custinfo.HkMessageCustInfoVO;
//import lombok.extern.slf4j.Slf4j;
//import org.apache.commons.collections.CollectionUtils;
//import org.springframework.beans.factory.annotation.Autowired;
//import org.springframework.stereotype.Service;
//import org.springframework.util.Assert;
//
//import java.util.List;
//import java.util.Objects;
//
///**
// * @description: (香港账户-香港线上开户消息处理服务V2)
// * <AUTHOR>
// * @date 2025-04-16 15:06:23
// * @since JDK 1.8
// */
//@Service
//@Slf4j
//public class HkOnlineOpenAcctAnalyseServiceV2 implements CustMessageAnalyseService<HkMessageCustInfoVO> {
//
//    @Autowired
//    private AbnormalCustRepository abnormalCustRepository;
//
//    @Autowired
//    private CmHkCustInfoRepository cmHkCustInfoRepository;
//
//    @Override
//    public List<FullCustSourceEnum> getSourceList() {
//        return Lists.newArrayList(FullCustSourceEnum.HK_ONLINE_OPEN_ACCOUNT);
//    }
//
//    @Override
//    public AbnormalAnalyseResultVO<HkMessageCustInfoVO> analyze(HkMessageCustInfoVO analyseVo) {
//        log.info("香港线上开户消息处理V2开始，香港客户号：{}", analyseVo.getHkTxAcctNo());
//        Assert.notNull(analyseVo.getHkTxAcctNo(), "香港客户号不能为空");
//
//        String hkTxAcctNo = analyseVo.getHkTxAcctNo();
//
//        // 判断①：【香港客户号A】是否已关联【投顾客户号】
//        CmHkCustReqVO hkReqVo = new CmHkCustReqVO();
//        hkReqVo.setHkTxAcctNo(hkTxAcctNo);
//        CmHkConscustPO existHkCustInfo = cmHkCustInfoRepository.selectByReqVO(hkReqVo);
//
//        // 香港客户号已关联投顾客户号
//        if (existHkCustInfo != null) {
//            return analyseExistsCustomer(existHkCustInfo, analyseVo);
//        }
//
//        String hboneNo = analyseVo.getHboneNo();
//        // 判断②：【香港客户号A】绑定的【一账通号A】是否已关联【投顾客户号】
//        if (StringUtil.isNotBlank(hboneNo)) {
//            List<CmConscustForAnalyseBO> hboneCustList = abnormalCustRepository.queryCustBOByHboneNo(hboneNo);
//
//            // 一账通号已关联【投顾客户号】
//            if (CollectionUtils.isNotEmpty(hboneCustList)) {
//                CmConscustForAnalyseBO hboneCust = hboneCustList.get(0);
//                return analyseWithHboneCustomer(hboneCust, analyseVo);
//            }
//        }
//
//        // 香港客户号未关联投顾客户号，且一账通号未关联投顾客户号
//        return analyseWithoutCustomer(analyseVo);
//    }
//
//    /**
//     * @description: 分析香港客户号已绑定投顾客户号的情况
//     * @param existHkCustInfo 已存在的香港客户信息
//     * @param analyseVo 分析对象
//     * @return 处理结果
//     * <AUTHOR>
//     * @date 2025-04-16 15:06:23
//     * @since JDK 1.8
//     */
//    private AbnormalAnalyseResultVO<HkMessageCustInfoVO> analyseExistsCustomer(
//            CmHkConscustPO existHkCustInfo,
//            HkMessageCustInfoVO analyseVo) {
//
//        log.info("香港线上开户消息处理V2 - 已存在投顾客户号分析开始，香港客户号：{}", analyseVo.getHkTxAcctNo());
//
//        String custNo = existHkCustInfo.getConscustno(); // 已绑定的投顾客户号
//        String hboneNo = analyseVo.getHboneNo(); // 香港客户号绑定的一账通号
//
//        // 判断①：【香港客户号A】绑定的【一账通号A】是否已关联【投顾客户号】
//        List<CmConscustForAnalyseBO> hboneCustList = abnormalCustRepository.queryCustBOByHboneNo(hboneNo);
//
//        // 一账通号已关联投顾客户号
//        if (CollectionUtils.isNotEmpty(hboneCustList)) {
//            CmConscustForAnalyseBO hboneCust = hboneCustList.get(0);
//
//            // 一账通号关联的投顾客户号与香港客户号关联的投顾客户号不一致（HK10）
//            if (!custNo.equals(hboneCust.getConscustno())) {
//                List<CmConscustForAnalyseBO> relatedCustList = Lists.newArrayList();
//                // 香港客户号关联的投顾客户
//                CmConscustForAnalyseBO hkCust = abnormalCustRepository.queryCustBOByCustNo(custNo);
//                relatedCustList.add(hkCust);
//                // 一账通号关联的投顾客户
//                relatedCustList.add(hboneCust);
//
//                return createAbnormalResult(
//                        analyseVo,
//                        AbnormaSceneTypeEnum.HK10,
//                        relatedCustList);
//            }
//        } else {
//            // 关联投顾客户号B
//            // 判断③：【香港客户号A】绑定的【投顾客户号A】是否已关联【一账通号】
//            CmConscustForAnalyseBO custInfo = abnormalCustRepository.queryCustBOByCustNo(custNo);
//            if (custInfo != null && StringUtil.isNotBlank(custInfo.getHboneNo())) {
//                // 投顾客户号已关联一账通号，且与香港客户号绑定的一账通号不一致（HK09）
//                if (!custInfo.getHboneNo().equals(hboneNo)) {
//                    return createAbnormalResult(
//                            analyseVo,
//                            AbnormaSceneTypeEnum.HK09,
//                            Lists.newArrayList(custInfo));
//                }
//            }
//        }
//
//        // 关联的是投顾客户号A 或 投顾客户号A未关联一账通号
//        // 判断②：香港【证件A】是否已关联【投顾客户号】
//        List<CmConscustForAnalyseBO> idMatchCustList = queryCustListByIdInfo(analyseVo);
//
//        // 香港证件已关联投顾客户号
//        if (CollectionUtils.isNotEmpty(idMatchCustList)) {
//            // 证件匹配到唯一投顾客户号，且不等于已绑定的投顾客户号（HK07）
//            if (idMatchCustList.size() == 1 && !custNo.equals(idMatchCustList.get(0).getConscustno())) {
//                List<CmConscustForAnalyseBO> relatedCustList = Lists.newArrayList();
//                // 香港客户号关联的投顾客户
//                CmConscustForAnalyseBO hkCust = abnormalCustRepository.queryCustBOByCustNo(custNo);
//                relatedCustList.add(hkCust);
//                // 证件匹配到的投顾客户
//                relatedCustList.add(idMatchCustList.get(0));
//
//                return createAbnormalResult(
//                        analyseVo,
//                        AbnormaSceneTypeEnum.HK07,
//                        relatedCustList);
//            }
//
//            // 证件匹配到多个投顾客户号（HK08）
//            if (idMatchCustList.size() > 1) {
//                List<CmConscustForAnalyseBO> relatedCustList = Lists.newArrayList();
//                // 香港客户号关联的投顾客户
//                CmConscustForAnalyseBO hkCust = abnormalCustRepository.queryCustBOByCustNo(custNo);
//                relatedCustList.add(hkCust);
//                // 添加证件匹配到的所有投顾客户
//                relatedCustList.addAll(idMatchCustList);
//
//                return createAbnormalResult(
//                        analyseVo,
//                        AbnormaSceneTypeEnum.HK08,
//                        relatedCustList);
//            }
//        }
//
//        // 所有检查通过，处理正常数据
//        AbnormalAnalyseResultVO<HkMessageCustInfoVO> resultVo = AbnormalAnalyseResultVO.normalData(analyseVo);
//        // TODO 处理正常数据
//        // resultVo.setProcessedCustInfo(custInfo);
//
//        log.info("香港线上开户消息处理V2 - 已存在投顾客户号分析结束，正常数据：{}", analyseVo.getHkTxAcctNo());
//        return resultVo;
//    }
//
//    /**
//     * @description: 分析香港客户号未绑定投顾客户号的情况
//     * @param analyseVo 分析对象
//     * @return 处理结果
//     * <AUTHOR>
//     * @date 2025-04-16 15:06:23
//     * @since JDK 1.8
//     */
//    private AbnormalAnalyseResultVO<HkMessageCustInfoVO> analyseWithoutCustomer(HkMessageCustInfoVO analyseVo) {
//        log.info("香港线上开户消息处理V2 - 未存在投顾客户号分析开始，香港客户号：{}", analyseVo.getHkTxAcctNo());
//
//        // 判断①：香港【证件A】是否已关联【投顾客户号】
//        List<CmConscustForAnalyseBO> idMatchCustList = queryCustListByIdInfo(analyseVo);
//
//        // 香港证件已关联投顾客户号
//        if (CollectionUtils.isNotEmpty(idMatchCustList)) {
//            // 证件匹配到唯一投顾客户号（HK09）
//            if (idMatchCustList.size() == 1) {
//                List<CmConscustForAnalyseBO> relatedCustList = Lists.newArrayList();
//                // 证件匹配到的投顾客户
//                relatedCustList.add(idMatchCustList.get(0));
//
//                return createAbnormalResult(
//                        analyseVo,
//                        AbnormaSceneTypeEnum.HK09,
//                        relatedCustList);
//            }
//
//            // 证件匹配到多个投顾客户号（HK11）
//            if (idMatchCustList.size() > 1) {
//                return createAbnormalResult(
//                        analyseVo,
//                        AbnormaSceneTypeEnum.HK11,
//                        idMatchCustList);
//            }
//        }
//
//        return AbnormalAnalyseResultVO.normalData(analyseVo);
//    }
//
//    /**
//     * @description: 分析一账通号已关联投顾客户号的情况
//     * @param hboneCust 一账通关联的投顾客户
//     * @param analyseVo 分析对象
//     * @return 处理结果
//     * <AUTHOR>
//     * @date 2025-04-16 15:06:23
//     * @since JDK 1.8
//     */
//    private AbnormalAnalyseResultVO<HkMessageCustInfoVO> analyseWithHboneCustomer(
//            CmConscustForAnalyseBO hboneCust,
//            HkMessageCustInfoVO analyseVo) {
//
//        log.info("香港线上开户消息处理V2 - 一账通关联投顾客户分析开始，香港客户号：{}", analyseVo.getHkTxAcctNo());
//
//        // 判断③：【投顾客户号A】绑定手机号是否等于香港【手机号A】
//        String mobileAreaCode = analyseVo.getMobileAreaCode();
//        String mobileDigest = analyseVo.getMobileDigest();
//
//        // 判断投顾客户手机号是否与香港手机号一致
//        boolean isMobileMatch = isMobileMatch(hboneCust, mobileAreaCode, mobileDigest);
//
//        if (isMobileMatch) {
//            // 手机号一致，判断证件匹配情况
//            return analyseWithMobileMatch(hboneCust, analyseVo);
//        } else {
//            // 手机号不一致，进行其他分析
//            return analyseWithMobileMismatch(hboneCust, analyseVo);
//        }
//    }
//
//    /**
//     * @description: 分析投顾客户手机号与香港手机号一致的情况
//     * @param hboneCust 一账通关联的投顾客户
//     * @param analyseVo 分析对象
//     * @return 处理结果
//     * <AUTHOR>
//     * @date 2025-04-16 15:06:23
//     * @since JDK 1.8
//     */
//    private AbnormalAnalyseResultVO<HkMessageCustInfoVO> analyseWithMobileMatch(
//            CmConscustForAnalyseBO hboneCust,
//            HkMessageCustInfoVO analyseVo) {
//
//        log.info("香港线上开户消息处理V2 - 投顾客户手机号与香港手机号一致分析开始，香港客户号：{}", analyseVo.getHkTxAcctNo());
//
//        String hkTxAcctNo = analyseVo.getHkTxAcctNo();
//        String hboneCustNo = hboneCust.getConscustno(); // 一账通关联的投顾客户号
//
//        // 判断①：香港【证件A】是否已关联【投顾客户号】
//        List<CmConscustForAnalyseBO> idMatchCustList = queryCustListByIdInfo(analyseVo);
//
//        if (CollectionUtils.isNotEmpty(idMatchCustList)) {
//            // 证件匹配到唯一的投顾客户号，且等于一账通关联的投顾客户号
//            if (idMatchCustList.size() == 1 && Objects.equals(hboneCustNo, idMatchCustList.get(0).getConscustno())) {
//                // 继续判断投顾客户是否已绑定其他香港客户号
//                return checkAndBindHkTxAcctNo(hboneCust, analyseVo);
//            }
//
//            // 证件匹配到唯一的投顾客户号，但不等于一账通关联的投顾客户号（HK20）
//            if (idMatchCustList.size() == 1 && !Objects.equals(hboneCustNo, idMatchCustList.get(0).getConscustno())) {
//                return createAbnormalResult(
//                        analyseVo,
//                        AbnormaSceneTypeEnum.HK20,
//                        Lists.newArrayList(hboneCust));
//            }
//
//            // 证件匹配到多个投顾客户号（HK21）
//            if (idMatchCustList.size() > 1) {
//                List<CmConscustForAnalyseBO> relatedCustList = Lists.newArrayList();
//                relatedCustList.add(hboneCust);
//                // 添加证件匹配到的所有投顾客户
//                relatedCustList.addAll(idMatchCustList);
//
//                return createAbnormalResult(
//                        analyseVo,
//                        AbnormaSceneTypeEnum.HK21,
//                        relatedCustList);
//            }
//        } else {
//            // 证件未匹配到投顾客户号
//
//            // 检查投顾客户是否有证件信息
//            boolean hasIdInfo = StringUtil.isNotBlank(hboneCust.getIdtype()) &&
//                              StringUtil.isNotBlank(hboneCust.getIdnoDigest());
//
//            // 投顾客户无证件信息，可以继续
//            if (!hasIdInfo) {
//                // 继续判断投顾客户是否已绑定其他香港客户号
//                return checkAndBindHkTxAcctNo(hboneCust, analyseVo);
//            }
//
//            // 投顾客户有证件信息，但与香港证件不一致（HK19）
//            return createAbnormalResult(
//                    analyseVo,
//                    AbnormaSceneTypeEnum.HK19,
//                    Lists.newArrayList(hboneCust));
//        }
//
//        // 如果以上条件都不满足，继续判断投顾客户是否已绑定其他香港客户号
//        return checkAndBindHkTxAcctNo(hboneCust, analyseVo);
//    }
//
//    /**
//     * @description: 分析投顾客户手机号与香港手机号不一致的情况
//     * @param hboneCust 一账通关联的投顾客户
//     * @param analyseVo 分析对象
//     * @return 处理结果
//     * <AUTHOR>
//     * @date 2025-04-16 15:06:23
//     * @since JDK 1.8
//     */
//    private AbnormalAnalyseResultVO<HkMessageCustInfoVO> analyseWithMobileMismatch(
//            CmConscustForAnalyseBO hboneCust,
//            HkMessageCustInfoVO analyseVo) {
//
//        log.info("香港线上开户消息处理V2 - 投顾客户手机号与香港手机号不一致分析开始，香港客户号：{}", analyseVo.getHkTxAcctNo());
//
//        String hboneCustNo = hboneCust.getConscustno(); // 一账通关联的投顾客户号
//        String mobileAreaCode = analyseVo.getMobileAreaCode();
//        String mobileDigest = analyseVo.getMobileDigest();
//
//        // 判断①：香港【手机号A】是否已关联【投顾客户号】
//        List<CmConscustForAnalyseBO> mobileMatchCustList = abnormalCustRepository.queryCustListByMobile(
//                analyseVo.getInvestType(), mobileAreaCode, mobileDigest);
//
//        // 香港手机号未匹配到投顾客户号(未关联)
//        if (CollectionUtils.isEmpty(mobileMatchCustList)) {
//            return handleNoMobileMatch(hboneCust, analyseVo);
//        }
//
//        // 香港手机号匹配到多个投顾客户号（HK26）
//        if (mobileMatchCustList.size() > 1) {
//            return createAbnormalResult(
//                    analyseVo,
//                    AbnormaSceneTypeEnum.HK26,
//                    mobileMatchCustList);
//        }
//
//        // 香港手机号匹配到唯一投顾客户号，且不是一账通关联的投顾客户号A，是投顾客户号B
//        String consCustNoB = mobileMatchCustList.get(0).getConscustno();
//        if (mobileMatchCustList.size() == 1 && !Objects.equals(hboneCustNo, consCustNoB)) {
//            return handleMobileMatchWithDifferentCust(hboneCust, mobileMatchCustList.get(0), analyseVo);
//        }
//
//        // 通常不会执行到这里，但如果没有匹配到上面的任何一个条件，则返回异常
//        log.error("香港线上开户消息处理V2 - 投顾客户手机号与香港手机号不一致分析出现未预期情况，香港客户号：{}",
//                analyseVo.getHkTxAcctNo());
//
//        return createAbnormalResult(
//                analyseVo,
//                AbnormaSceneTypeEnum.HK22, // 使用默认的异常类型
//                Lists.newArrayList(hboneCust));
//    }
//
//    /**
//     * @description: 处理手机号未匹配到投顾客户号的情况
//     * @param hboneCust 一账通关联的投顾客户
//     * @param analyseVo 分析对象
//     * @return 处理结果
//     * <AUTHOR>
//     * @date 2025-04-16 15:06:23
//     * @since JDK 1.8
//     */
//    private AbnormalAnalyseResultVO<HkMessageCustInfoVO> handleNoMobileMatch(
//            CmConscustForAnalyseBO hboneCust,
//            HkMessageCustInfoVO analyseVo) {
//
//        String hboneCustNo = hboneCust.getConscustno();
//        // 判断②：香港【证件A】是否已关联【投顾客户号】
//        List<CmConscustForAnalyseBO> idMatchCustList = queryCustListByIdInfo(analyseVo);
//
//        // 证件未匹配到投顾客户号（HK22）
//        if (CollectionUtils.isEmpty(idMatchCustList)) {
//            return createAbnormalResult(
//                    analyseVo,
//                    AbnormaSceneTypeEnum.HK22,
//                    Lists.newArrayList(hboneCust));
//        }
//
//        // 证件匹配到唯一投顾客户号，且等于一账通关联的投顾客户号（HK23）
//        if (idMatchCustList.size() == 1 && Objects.equals(hboneCustNo, idMatchCustList.get(0).getConscustno())) {
//            return createAbnormalResult(
//                    analyseVo,
//                    AbnormaSceneTypeEnum.HK23,
//                    Lists.newArrayList(hboneCust));
//        }
//
//        // 证件匹配到唯一投顾客户号（投顾客户号B），且不等于一账通关联的投顾客户号（HK24）
//        if (idMatchCustList.size() == 1 && !Objects.equals(hboneCustNo, idMatchCustList.get(0).getConscustno())) {
//            List<CmConscustForAnalyseBO> relatedCustList = Lists.newArrayList();
//            relatedCustList.add(hboneCust);
//            relatedCustList.add(idMatchCustList.get(0));
//
//            return createAbnormalResult(
//                    analyseVo,
//                    AbnormaSceneTypeEnum.HK24,
//                    relatedCustList);
//        }
//
//        // 证件匹配到多个投顾客户号（HK25）
//        if (idMatchCustList.size() > 1) {
//            List<CmConscustForAnalyseBO> relatedCustList = Lists.newArrayList();
//            relatedCustList.add(hboneCust);
//            // 添加证件匹配到的所有投顾客户
//            relatedCustList.addAll(idMatchCustList);
//
//            return createAbnormalResult(
//                    analyseVo,
//                    AbnormaSceneTypeEnum.HK25,
//                    relatedCustList);
//        }
//
//        return createAbnormalResult(
//                analyseVo,
//                AbnormaSceneTypeEnum.HK22, // 使用默认的异常类型
//                Lists.newArrayList(hboneCust));
//    }
//
//    /**
//     * @description: 处理手机号匹配到与一账通号关联投顾客户号不同的情况
//     * @param hboneCust 一账通关联的投顾客户
//     * @param mobileMatchCust 手机号匹配到的投顾客户
//     * @param analyseVo 分析对象
//     * @return 处理结果
//     * <AUTHOR>
//     * @date 2025-04-16 15:06:23
//     * @since JDK 1.8
//     */
//    private AbnormalAnalyseResultVO<HkMessageCustInfoVO> handleMobileMatchWithDifferentCust(
//            CmConscustForAnalyseBO hboneCust,
//            CmConscustForAnalyseBO mobileMatchCust,
//            HkMessageCustInfoVO analyseVo) {
//
//        String hboneCustNo = hboneCust.getConscustno();
//        String consCustNoB = mobileMatchCust.getConscustno();
//
//        // 判断③：香港【证件A】是否已关联【投顾客户号】
//        List<CmConscustForAnalyseBO> idMatchCustList = queryCustListByIdInfo(analyseVo);
//
//        // 证件未匹配到投顾客户号或匹配到的是一账通关联的投顾客户号（HK27）
//        if (CollectionUtils.isEmpty(idMatchCustList) ||
//            (idMatchCustList.size() == 1 && Objects.equals(hboneCustNo, idMatchCustList.get(0).getConscustno()))) {
//            return createAbnormalResult(
//                    analyseVo,
//                    AbnormaSceneTypeEnum.HK27,
//                    Lists.newArrayList(hboneCust));
//        }
//
//        // 证件匹配到唯一投顾客户号，且等于手机号匹配到的投顾客户号B（HK28）
//        if (idMatchCustList.size() == 1 && Objects.equals(consCustNoB, idMatchCustList.get(0).getConscustno())) {
//            List<CmConscustForAnalyseBO> relatedCustList = Lists.newArrayList();
//            relatedCustList.add(hboneCust);
//            relatedCustList.add(mobileMatchCust);
//
//            return createAbnormalResult(
//                    analyseVo,
//                    AbnormaSceneTypeEnum.HK28,
//                    relatedCustList);
//        }
//
//        // 证件匹配到唯一投顾客户号，但不等于手机号匹配到的投顾客户号（HK29）
//        if (idMatchCustList.size() == 1 &&
//            !Objects.equals(mobileMatchCust.getConscustno(), idMatchCustList.get(0).getConscustno())) {
//            List<CmConscustForAnalyseBO> relatedCustList = Lists.newArrayList();
//            relatedCustList.add(hboneCust);
//            relatedCustList.add(mobileMatchCust);
//            relatedCustList.add(idMatchCustList.get(0));
//
//            return createAbnormalResult(
//                    analyseVo,
//                    AbnormaSceneTypeEnum.HK29,
//                    relatedCustList);
//        }
//
//        // 证件匹配到多个投顾客户号（HK30）
//        if (idMatchCustList.size() > 1) {
//            List<CmConscustForAnalyseBO> relatedCustList = Lists.newArrayList();
//            relatedCustList.add(hboneCust);
//            relatedCustList.add(mobileMatchCust);
//            // 添加证件匹配到的所有投顾客户
//            relatedCustList.addAll(idMatchCustList);
//
//            return createAbnormalResult(
//                    analyseVo,
//                    AbnormaSceneTypeEnum.HK30,
//                    relatedCustList);
//        }
//
//        return createAbnormalResult(
//                analyseVo,
//                AbnormaSceneTypeEnum.HK27, // 使用默认的异常类型
//                Lists.newArrayList(hboneCust));
//    }
//
//    /**
//     * @description: 检查投顾客户号是否已绑定其他香港客户号并处理
//     * @param custBO 投顾客户号信息
//     * @param analyseVo 分析对象
//     * @return 处理结果
//     * <AUTHOR>
//     * @date 2025-04-16 15:06:23
//     * @since JDK 1.8
//     */
//    private AbnormalAnalyseResultVO<HkMessageCustInfoVO> checkAndBindHkTxAcctNo(
//            CmConscustForAnalyseBO custBO,
//            HkMessageCustInfoVO analyseVo) {
//
//        String hkTxAcctNo = analyseVo.getHkTxAcctNo();
//        String consCustNo = custBO.getConscustno();
//        // 判断【投顾客户号】是否已绑定【其他香港客户号】
//        CmHkCustReqVO hkReqVo = new CmHkCustReqVO();
//        hkReqVo.setConscustno(consCustNo);
//        CmHkConscustPO existHkCustInfo = cmHkCustInfoRepository.selectByReqVO(hkReqVo);
//
//        // 判断【投顾客户号】是否已绑定【其他香港客户号】
//        if (Objects.nonNull(existHkCustInfo) &&
//            !Objects.equals(existHkCustInfo.getHkTxAcctNo(), hkTxAcctNo)) {
//
//            // 投顾客户号已绑定其他香港客户号（HK18）
//            return createAbnormalResult(
//                    analyseVo,
//                    AbnormaSceneTypeEnum.HK18,
//                    Lists.newArrayList(custBO));
//        }
//
//        // 投顾客户号未绑定其他香港客户号，可以绑定当前香港客户号
//        AbnormalAnalyseResultVO<HkMessageCustInfoVO> resultVo = AbnormalAnalyseResultVO.normalData(analyseVo);
//        resultVo.setProcessedCustInfo(custBO);
//
//        log.info("香港线上开户消息处理V2结束，投顾客户号未被占用，可以绑定香港客户号，香港客户号：{}，投顾客户号：{}",
//                hkTxAcctNo, custBO.getConscustno());
//
//        return resultVo;
//    }
//
//    /**
//     * @description: 创建异常结果对象
//     * @param analyseVo 分析对象
//     * @param sceneType 异常场景类型
//     * @param relatedCustList 关联客户列表
//     * @return 异常结果
//     * <AUTHOR>
//     * @date 2025-04-16 15:06:23
//     * @since JDK 1.8
//     */
//    private AbnormalAnalyseResultVO<HkMessageCustInfoVO> createAbnormalResult(
//            HkMessageCustInfoVO analyseVo,
//            AbnormaSceneTypeEnum sceneType,
//            List<CmConscustForAnalyseBO> relatedCustList) {
//
//        log.info("香港线上开户消息处理V2发现异常：{}，香港客户号：{}",
//                sceneType.getDescription(), analyseVo.getHkTxAcctNo());
//
//        return AbnormalAnalyseResultVO.notNormalData(
//                analyseVo,
//                sceneType,
//                relatedCustList);
//    }
//
//    /**
//     * @description: 判断手机号是否匹配
//     * @param hboneCust 一账通关联的投顾客户
//     * @param mobileAreaCode 手机区号
//     * @param mobileDigest 手机号
//     * @return 是否匹配
//     * <AUTHOR>
//     * @date 2025-04-16 15:06:23
//     * @since JDK 1.8
//     */
//    private boolean isMobileMatch(
//            CmConscustForAnalyseBO hboneCust,
//            String mobileAreaCode,
//            String mobileDigest) {
//
//        return mobileAreaCode != null && mobileDigest != null &&
//                mobileAreaCode.equals(hboneCust.getMobileAreaCode()) &&
//                mobileDigest.equals(hboneCust.getMobileDigest());
//    }
//
//    /**
//     * @description: 根据证件信息查询客户列表
//     * @param analyseVo 分析对象
//     * @return 客户列表
//     * <AUTHOR>
//     * @date 2025-04-16 15:06:23
//     * @since JDK 1.8
//     */
//    private List<CmConscustForAnalyseBO> queryCustListByIdInfo(HkMessageCustInfoVO analyseVo) {
//        return abnormalCustRepository.queryCustListByIdNo(
//                analyseVo.getInvestType(),
//                analyseVo.getIdType(),
//                analyseVo.getIdSignAreaCode(),
//                analyseVo.getIdNoDigest());
//    }
//}