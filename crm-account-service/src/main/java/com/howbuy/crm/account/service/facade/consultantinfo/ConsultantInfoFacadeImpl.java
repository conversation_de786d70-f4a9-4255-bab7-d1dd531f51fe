/**
 * Copyright (c) 2024, <PERSON><PERSON><PERSON><PERSON> HOWBUY INVESTMENT MANAGEMENT Co., Ltd.
 * All right reserved.
 * <p>
 * THIS IS UNPUBLISHED PROPRIETARY SOURCE CODE OF HOWBUY INVESTMENT
 * MANAGEMENT CO., LTD.  THE CONTENTS OF THIS FILE MAY NOT BE DISCLOSED
 * TO THIRD PARTIES, COPIED OR DUPLICATED IN ANY FORM, IN WHOLE OR IN PART,
 * WITHOUT THE PRIOR WRITTEN PERMISSION OF HOWBUY INVESTMENT MANAGEMENT
 * CO., LTD.
 */
package com.howbuy.crm.account.service.facade.consultantinfo;

import com.howbuy.crm.account.client.facade.consultantinfo.ConsultantInfoFacade;
import com.howbuy.crm.account.client.request.consultantinfo.QueryConsultantInfoRequest;
import com.howbuy.crm.account.client.response.Response;
import com.howbuy.crm.account.client.response.consultantinfo.CmConstantInfoListVO;
import com.howbuy.crm.account.service.service.consultantinfo.ConsultantInfoService;
import org.apache.dubbo.config.annotation.DubboService;

import javax.annotation.Resource;

/**
 * <AUTHOR>
 * @description: 投顾信息服务实现
 * @date 2024/9/6 15:21
 * @since JDK 1.8
 */
@DubboService
public class ConsultantInfoFacadeImpl implements ConsultantInfoFacade {

    @Resource
    private ConsultantInfoService consultantInfoService;

    @Override
    public Response<CmConstantInfoListVO> queryConsultantInfo(QueryConsultantInfoRequest request) {
        return Response.ok(consultantInfoService.queryConsultantInfo(request));
    }
}
