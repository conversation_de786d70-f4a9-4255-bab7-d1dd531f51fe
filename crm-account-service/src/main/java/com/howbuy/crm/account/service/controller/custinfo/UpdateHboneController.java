/**
 * Copyright (c) 2023, <PERSON><PERSON><PERSON><PERSON> HOWBUY INVESTMENT MANAGEMENT Co., Ltd.
 * All right reserved.
 * <p>
 * THIS IS UNPUBLISHED PROPRIETARY SOURCE CODE OF HOWBUY INVESTMENT
 * MANAGEMENT CO., LTD.  THE CONTENTS OF THIS FILE MAY NOT BE DISCLOSED
 * TO THIRD PARTIES, COPIED OR DUPLICATED IN ANY FORM, IN WHOLE OR IN PART,
 * WITHOUT THE PRIOR WRITTEN PERMISSION OF HOWBUY INVESTMENT MANAGEMENT
 * CO., LTD.
 */
package com.howbuy.crm.account.service.controller.custinfo;

import com.howbuy.crm.account.client.request.custinfo.UpdateHkAssetIsolateRequest;
import com.howbuy.crm.account.client.response.Response;
import com.howbuy.crm.account.service.service.custinfo.HboneUpdateService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

/**
 * @description: (crm 更新一账通账户中心 接口)
 * <AUTHOR>
 * @date  2024年5月28日
 * @since JDK 1.8
 */
@RestController
@RequestMapping("/updatehbone")
public class UpdateHboneController {

    @Autowired
    private HboneUpdateService hboneUpdateService;


    /**
     * @api {POST} /updatehbone/updatehkassetisolateflag updateHkAssetIsolateFlag()
     * @apiVersion 1.0.0
     * @apiGroup UpdateHboneController
     * @apiName updateHkAssetIsolateFlag()
     * @apiDescription 更新一账通香港资产隔离标志
     * @apiParam (请求体) {String} hboneNo 香港交易账号
     * @apiParam (请求体) {String} operator 操作员
     * @apiParam (请求体) {String} hkAssetIsolateFlagUpDt 海外资产隔离标志更新时间	['yyyyMMddHHmmss']
     * @apiParam (请求体) {String} hkAssetIsolateFlag 海外资产隔离标志	['0-否', '1-是']
     * @apiParamExample 请求体示例
     * {"hkAssetIsolateFlag":"AjlOK","hkAssetIsolateFlagUpDt":"kFpmE6","operator":"OnMOg7ea","hboneNo":"drxm"}
     * @apiSuccess (响应结果) {String} code 状态码
     * @apiSuccess (响应结果) {String} description 描述信息
     * @apiSuccess (响应结果) {String} data 数据封装
     * @apiSuccessExample 响应结果示例
     * {"code":"GZl","data":"xi2","description":"3"}
     */
    @PostMapping("/updatehkassetisolateflag")
    @ResponseBody
    public Response<String> updateHkAssetIsolateFlag(@RequestBody UpdateHkAssetIsolateRequest isolateRequest) {
        return hboneUpdateService.updateHkAssetIsolateFlag(isolateRequest);
    }



}