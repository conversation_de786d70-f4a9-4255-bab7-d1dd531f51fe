/**
 * Copyright (c) 2023, <PERSON><PERSON><PERSON><PERSON> HOWBUY INVESTMENT MANAGEMENT Co., Ltd.
 * All right reserved.
 * <p>
 * THIS IS UNPUBLISHED PROPRIETARY SOURCE CODE OF HOWBUY INVESTMENT
 * MANAGEMENT CO., LTD.  THE CONTENTS OF THIS FILE MAY NOT BE DISCLOSED
 * TO THIRD PARTIES, COPIED OR DUPLICATED IN ANY FORM, IN WHOLE OR IN PART,
 * WITHOUT THE PRIOR WRITTEN PERMISSION OF HOWBUY INVESTMENT MANAGEMENT
 * CO., LTD.
 */
package com.howbuy.crm.account.service.business.custinfo;

import com.howbuy.common.utils.Assert;
import com.howbuy.common.utils.StringUtil;
import com.howbuy.crm.account.client.request.custinfo.HkAcctRelationOptRequest;
import com.howbuy.crm.account.client.response.Response;
import com.howbuy.crm.account.client.response.custinfo.HkAcctCustDetailInfoVO;
import com.howbuy.crm.account.dao.po.custinfo.CmHkConscustPO;
import com.howbuy.crm.account.dao.req.custinfo.CmHkCustReqVO;
import com.howbuy.crm.account.service.outerservice.hkacct.HkCustBasicInfoOuterService;
import com.howbuy.crm.account.service.repository.custinfo.CmHkCustInfoRepository;
import com.howbuy.crm.account.service.req.custinfo.HkCustRelationOptReqVO;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

/**
 * @description: (香港客户绑定 service )
 * <AUTHOR>
 * @date 2023/12/25 20:37
 * @since JDK 1.8
 */
@Service
@Slf4j
public class HkCustBindBusinessService {


    @Autowired
    private CmHkCustInfoRepository hkCustRepository;

    @Autowired
    private HkCustBasicInfoOuterService hkCustInfoOuterService;


    /**
     * 查询香港交易账户信息
     * @param hkTxAcctNo
     * @return
     */
    private HkAcctCustDetailInfoVO queryHkCustInfo(String hkTxAcctNo) {
        Assert.notNull(hkTxAcctNo,"香港交易账号不能为空");
        return hkCustInfoOuterService.queryHkCustDetailInfo(hkTxAcctNo);
    }





    /**
     * @description:(绑定 香港交易账号的 关联关系)
     * @param bindOptRequest
     * @return com.howbuy.crm.account.client.response.Response<java.lang.String>
     * @author: haoran.zhang
     * @date: 2023/12/15 11:25
     * @since JDK 1.8
     */
    public Response<String> bindHkTxAcct(HkAcctRelationOptRequest bindOptRequest) {
        String custNo=bindOptRequest.getCustNo();
        String hkTxAcctNo=bindOptRequest.getHkTxAcctNo();

        CmHkCustReqVO custReqVO=new CmHkCustReqVO();
        custReqVO.setConscustno(custNo);
        CmHkConscustPO existPo=hkCustRepository.selectByReqVO(custReqVO);
        if(existPo!=null){
            return Response.fail("该客户已经绑定香港交易账号!");
        }

        CmHkCustReqVO hkReqVO=new CmHkCustReqVO();
        hkReqVO.setHkTxAcctNo(hkTxAcctNo);
        CmHkConscustPO  existHkPo=hkCustRepository.selectByReqVO(hkReqVO);
        if(existHkPo!=null){
            return Response.fail("该香港交易账号已经绑定客户!");
        }
        //调用香港账户中心接口  获取 ebrokerId
        HkAcctCustDetailInfoVO hkAcctCustInfoVO=queryHkCustInfo(hkTxAcctNo);
        if(hkAcctCustInfoVO==null){
            return Response.fail("该香港交易账号在香港账户中心不存在!");
        }

        HkCustRelationOptReqVO bindOptVo=transferReqVoByRequest(bindOptRequest);
        //内部赋值  eborkeId
        bindOptVo.setEbrokerId(hkAcctCustInfoVO.getEbrokerId());
        return hkCustRepository.executeBind(bindOptVo);
    }



    /**
     * @description:(解绑 香港交易账号的 关联关系)
     * @param bindOptRequest
     * @return com.howbuy.crm.account.client.response.Response<java.lang.String>
     * @throws Exception
     * @throws
     * @since JDK 1.8
     */
    public Response<String> unBindHkTxAcct(HkAcctRelationOptRequest bindOptRequest) {
        String custNo=bindOptRequest.getCustNo();
        String hkTxAcctNo=bindOptRequest.getHkTxAcctNo();
        Assert.notNull(custNo,"客户号不能为空");
        Assert.notNull(hkTxAcctNo,"香港交易账号不能为空");

        CmHkCustReqVO custReqVO=new CmHkCustReqVO();
        custReqVO.setConscustno(custNo);
        custReqVO.setHkTxAcctNo(hkTxAcctNo);
        CmHkConscustPO  existPo=hkCustRepository.selectByReqVO(custReqVO);
        if(existPo==null){
            return Response.fail(String.format("客户号：%s ,香港交易账号：%s 不存在绑定关系,无需解绑!",custNo,hkTxAcctNo));
        }
        HkCustRelationOptReqVO unBindOptVo=transferReqVoByRequest(bindOptRequest);

        return hkCustRepository.executeUnBind(unBindOptVo);
    }


    /**
     * @description:(请在此添加描述)
     * @param bindOptRequest
     * @return com.howbuy.crm.account.service.req.custinfo.HkCustRelationOptReqVO
     * @author: haoran.zhang
     * @date: 2023/12/15 14:16
     * @since JDK 1.8
     */
    private  HkCustRelationOptReqVO transferReqVoByRequest(HkAcctRelationOptRequest bindOptRequest){
        HkCustRelationOptReqVO optReqVO=new HkCustRelationOptReqVO();
        optReqVO.setCustNo(bindOptRequest.getCustNo());
        optReqVO.setHkTxAcctNo(bindOptRequest.getHkTxAcctNo());
        optReqVO.setOperator(bindOptRequest.getOperator());
        optReqVO.setOperateChannel(bindOptRequest.getOperateChannel());
        optReqVO.setOperateSource(bindOptRequest.getOperateSource());
        optReqVO.setRemark(bindOptRequest.getRemark());
        return optReqVO;
    }




}