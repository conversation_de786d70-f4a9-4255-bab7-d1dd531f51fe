/**
 *Copyright (c) 2023, <PERSON><PERSON><PERSON><PERSON> HOWBUY INVESTMENT MANAGEMENT Co., Ltd.
 *All right reserved.
 *
 *THIS IS UNPUBLISHED PROPRIETARY SOURCE CODE OF HOWBUY INVESTMENT
 *MANAGEMENT CO., LTD.  THE CONTENTS OF THIS FILE MAY NOT BE DISCLOSED
 *TO THIRD PARTIES, COPIED OR DUPLICATED IN ANY FORM, IN WHOLE OR IN PART,
 *WITHOUT THE PRIOR WRITTEN PERMISSION OF HOWBUY INVESTMENT MANAGEMENT
 * CO., LTD.
*/
    package com.howbuy.crm.account.service.vo.custinfo;

import com.howbuy.crm.account.client.enums.YesOrNoEnum;
import com.howbuy.crm.account.client.enums.custinfo.AbnormaSceneTypeEnum;
import com.howbuy.crm.account.dao.bo.custinfo.CmConscustForAnalyseBO;
import lombok.Data;

import java.util.List;

/**
 * @description: (香港客户 )
 * <AUTHOR>
 * @date 2023/12/12 13:51
 * @since JDK 1.8
 */
@Data
public class AbnormalAnalyseResultVO<T extends MessageCustInfoVO> {


    /**
     * 待分析的--客户数据
     */
    private T analyseVo;


    /**
     * 分析结果：是否异常 , YesOrNoEnum.YES - 标记为异常客户数据
     */
    private YesOrNoEnum abnormalEnum;

    /**
     * 分析结果：异常场景类型
     */
    private AbnormaSceneTypeEnum sceneTypeEnum;

    /**
     * 分析结果： 异常客户 相关联 的客户 列表
     */
    private List<CmConscustForAnalyseBO> relatedCustList;

    /**
     * 非异常数据，关联出来 待处理的 客户信息
     */
    private CmConscustForAnalyseBO processedCustInfo;

    /**
     * analyse操作后，
     * 如果分析结果：无异常。
     * 是否需要process 处理 . 默认需要处理
     */
    private boolean  needProcess=true;

    /**
     * 是否在分析阶段，更新客户信息
     */
    private boolean updateCustInfoInAnalyseFlag = false;

    

    public AbnormalAnalyseResultVO() {
    }

    /**
     * @description:(正常数据，非异常数据)
     * @param
     * @return com.howbuy.crm.account.service.vo.custinfo.HKAbnormalAnalyseResultVO
     * @author: haoran.zhang
     * @date: 2023/12/12 16:01
     * @since JDK 1.8
     */
    public static <T extends MessageCustInfoVO> AbnormalAnalyseResultVO<T> normalData(T analyseVo){
        AbnormalAnalyseResultVO<T> resultVo=new AbnormalAnalyseResultVO();
        resultVo.setAnalyseVo(analyseVo);
        resultVo.setAbnormalEnum(YesOrNoEnum.NO);
        return resultVo;
    }

    /**
     * @description:(异常数据)
     * @param analyseVo
     * @param sceneTypeEnum
     * @param relatedCustList
     * @return com.howbuy.crm.account.service.vo.custinfo.HKAbnormalAnalyseResultVO
     * @since JDK 1.8
     */
    public static <T extends MessageCustInfoVO>  AbnormalAnalyseResultVO<T> notNormalData(T analyseVo,
                                                        AbnormaSceneTypeEnum sceneTypeEnum,
                                                        List<CmConscustForAnalyseBO> relatedCustList){
        AbnormalAnalyseResultVO<T> resultVo= new AbnormalAnalyseResultVO<>();
        resultVo.setAnalyseVo(analyseVo);
        resultVo.setAbnormalEnum(YesOrNoEnum.YES);
        resultVo.setSceneTypeEnum(sceneTypeEnum);
        resultVo.setRelatedCustList(relatedCustList);
        return resultVo;
    }

    /**
     * @description:(是否校验数据通过，非异常数据)
     * @param 	
     * @return boolean
     * @author: haoran.zhang
     * @date: 2023/12/12 16:28
     * @since JDK 1.8
     */
    public boolean isAnalysePass(){
        return  YesOrNoEnum.NO==abnormalEnum;
    }
}