package com.howbuy.crm.account.service.commom.enums;

/**
 * @description: 调用方向枚举
 * <AUTHOR>
 * @date 2025-04-02 20:40:47
 * @since JDK 1.8
 */
public enum CallDirectionEnum {
     
    /**
     * 入站调用
     */
    IN("in"),
     
    /**
     * 出站调用
     */
    OUT("out");
     
    private final String direction;
     
    CallDirectionEnum(String direction) {
        this.direction = direction;
    }
     
    public String getDirection() {
        return direction;
    }
}
