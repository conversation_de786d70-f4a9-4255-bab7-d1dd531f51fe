/**
 * Copyright (c) 2024, <PERSON><PERSON><PERSON><PERSON> HOWBUY INVESTMENT MANAGEMENT Co., Ltd.
 * All right reserved.
 * <p>
 * THIS IS UNPUBLISHED PROPRIETARY SOURCE CODE OF HOWBUY INVESTMENT
 * MANAGEMENT CO., LTD.  THE CONTENTS OF THIS FILE MAY NOT BE DISCLOSED
 * TO THIRD PARTIES, COPIED OR DUPLICATED IN ANY FORM, IN WHOLE OR IN PART,
 * WITHOUT THE PRIOR WRITTEN PERMISSION OF HOWBUY INVESTMENT MANAGEMENT
 * CO., LTD.
 */
package com.howbuy.crm.account.service.service.commvisit;

import com.google.common.base.Throwables;
import com.howbuy.crm.account.client.enums.commvisit.AccompanyingTypeEnum;
import com.howbuy.crm.account.client.enums.commvisit.ConsultantSearchTypeEnum;
import com.howbuy.crm.account.client.enums.commvisit.FeedbackTypeEnum;
import com.howbuy.crm.account.client.enums.commvisit.VisitPurposeEnum;
import com.howbuy.crm.account.client.request.commvisit.*;
import com.howbuy.crm.account.client.response.ExportToFileVO;
import com.howbuy.crm.account.client.response.commvisit.*;
import com.howbuy.crm.account.client.utils.LocalDateUtil;
import com.howbuy.crm.account.dao.bo.commvisit.CmVisitMinutesBO;
import com.howbuy.crm.account.dao.bo.commvisit.NoFeedbackMsgBO;
import com.howbuy.crm.account.dao.bo.custinfo.CmConsCustSimpleBO;
import com.howbuy.crm.account.dao.dto.commvisit.VisitMinutesQueryDTO;
import com.howbuy.crm.account.dao.mapper.CommonMapper;
import com.howbuy.crm.account.dao.po.commvisit.CmVisitMinutesAccompanyingPO;
import com.howbuy.crm.account.dao.po.commvisit.CmVisitMinutesPO;
import com.howbuy.crm.account.dao.po.consultant.SearchConsultantDTO;
import com.howbuy.crm.account.service.commom.constant.BusinessIdConstant;
import com.howbuy.crm.account.service.commom.constant.MarkConstants;
import com.howbuy.crm.account.service.commom.constant.SequenceConstants;
import com.howbuy.crm.account.service.commom.utils.ExportUtil;
import com.howbuy.crm.account.service.domain.OrgLayerInfoDTO;
import com.howbuy.crm.account.service.domain.consultantexp.ConsultantUserLevelDTO;
import com.howbuy.crm.account.service.outerservice.crmcore.OrganazitonOuterService;
import com.howbuy.crm.account.service.outerservice.crmnt.CmPushMsgOuterService;
import com.howbuy.crm.account.service.repository.commvisit.CmVisitMinutesRepository;
import com.howbuy.crm.account.service.repository.commvisit.CmVisitMinutesAccompanyRepository;
import com.github.pagehelper.PageInfo;
import com.google.common.collect.Lists;
import com.howbuy.crm.account.service.repository.custinfo.ConsCustInfoRepository;
import com.howbuy.crm.account.service.service.consultantexp.CmConsultantExpService;
import com.howbuy.crm.account.service.service.consultantinfo.ConsultantInfoService;
import com.howbuy.crm.account.service.vo.commvisit.VisitMinutesExcelVO;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.time.LocalDate;
import java.time.LocalDateTime;
import java.util.*;
import java.util.stream.Collectors;
import java.text.SimpleDateFormat;
import com.alibaba.fastjson.JSON;

/**
 * @description: 拜访纪要服务
 * <AUTHOR>
 * @date 2025-04-08 10:26:00
 * @since JDK 1.8
 */
@Service
@Slf4j
public class CmVisitMinutesService {

    @Autowired
    private CmVisitMinutesRepository cmVisitMinutesRepository;
    
    @Autowired
    private CmVisitMinutesAccompanyRepository cmVisitMinutesAccompanyRepository;

    @Autowired
    private ConsultantInfoService consultantInfoService;

    @Autowired
    private OrganazitonOuterService organazitonOuterService;

    @Autowired
    private CmPushMsgOuterService cmPushMsgOuterService;

    @Autowired
    private CommonMapper commonMapper;

    @Autowired
    private CmConsultantExpService cmConsultantExpService;

    @Autowired
    private ConsCustInfoRepository consCustInfoRepository;
    /**
     * 菜单编码
     */
    private static final String VISIT_MINUTES_MENU_CODE = "020153";

    /**
     * 反馈提醒截止天数
     */
    private static final int FEEDBACK_PLUS_DAY = 6;

    /**
     * @description 查询客户拜访纪要列表
     * @param request 查询请求参数
     * @return com.howbuy.crm.account.client.response.commvisit.VisitMinutesListVO
     * @author: jianjian.yang
     * @date: 2025/4/9 16:04
     * @since JDK 1.8
     */
    public VisitMinutesListVO queryVisitMinutesList(QueryVisitMinutesListRequest request) {
        try {
            // 构建查询DTO
            VisitMinutesQueryDTO queryDTO = new VisitMinutesQueryDTO();
            queryDTO.setOrgCode(request.getOrgCode());
            queryDTO.setConsCode(request.getConsCode());
            queryDTO.setVisitDateStart(request.getVisitDateStart());
            queryDTO.setVisitDateEnd(request.getVisitDateEnd());
            queryDTO.setCreateDateStart(request.getCreateDateStart());
            queryDTO.setCreateDateEnd(request.getCreateDateEnd());
            queryDTO.setVisitPurpose(request.getVisitPurpose());
            queryDTO.setCustName(request.getCustName());
            queryDTO.setConsCustNo(request.getConsCustNo());
            queryDTO.setAccompanyingUserIdList(consultantInfoService.getConsCodeByName(request.getAccompanyingUser()));
            queryDTO.setManagerUserIdList(consultantInfoService.getConsCodeByName(request.getManagerId()));
            queryDTO.setFeedbackStatus(request.getFeedbackStatus());
            queryDTO.setPageNo(request.getPageNo());
            queryDTO.setPageSize(request.getPageSize());
            // 转换返回结果
            VisitMinutesListVO result = new VisitMinutesListVO();
            if(noDataByCondition(request.getManagerId(), queryDTO.getManagerUserIdList(),
                    request.getAccompanyingUser(), queryDTO.getAccompanyingUserIdList())){
                result.setTotal(0L);
                return result;
            }
            // 查询数据
            PageInfo<CmVisitMinutesBO> pageInfo = cmVisitMinutesRepository.getVisitMinutesList(queryDTO);
            result.setTotal(pageInfo.getTotal());
            List<String> consCodeList = Lists.newArrayList();
            if (CollectionUtils.isNotEmpty(pageInfo.getList())) {
                // 获取所有投顾编码
                for (CmVisitMinutesBO bo : pageInfo.getList()) {
                    consCodeList.add(bo.getManagerId());
                    // 获取所有创建人编码
                    consCodeList.add(bo.getCreator());
                    // 获取所有陪访人及创建人编码
                    bo.getAccompanyingUserList().forEach(accompanying -> {
                        consCodeList.add(accompanying.getAccompanyingUserId());
                        consCodeList.add(accompanying.getCreator());
                    });
                }
                // 获取所有组织编码
                List<String> orgCodeList = pageInfo.getList().stream().map(CmVisitMinutesBO::getOrgCode)
                        .filter(Objects::nonNull)
                        .collect(Collectors.toList());
                Map<String, OrgLayerInfoDTO> orgLayerInfoMap = organazitonOuterService.getOrgLayerInfoByOrgCodeList(orgCodeList);
                Map<String, String> consultantNameMap = consultantInfoService.getConsultantNameMap(consCodeList);
                List<VisitMinutesVO> voList = new ArrayList<>();
                for (CmVisitMinutesBO bo : pageInfo.getList()) {
                    VisitMinutesVO vo = new VisitMinutesVO();
                    vo.setId(bo.getId());
                    vo.setCreateTime(LocalDateUtil.localDateTimeToString(bo.getCreateTime(), LocalDateUtil.DTF_TIME));
                    vo.setVisitDt(bo.getVisitDt());
                    vo.setVisitPurpose(getDescriptionsByCodesComma(bo.getVisitPurpose(), bo.getVisitPurposeOther()));
                    vo.setConsCustNo(bo.getConsCustNo());
                    vo.setCustName(bo.getCustName());
                    vo.setCreatorName(consultantNameMap.get(bo.getCreator()));
                    // 设置组织信息
                    setOrgLayerInfo(vo, orgLayerInfoMap.get(bo.getOrgCode()));
                    vo.setVisitType(bo.getVisitTypeName());
                    vo.setMarketVal(bo.getMarketVal());
                    vo.setHealthAvgStar(bo.getHealthAvgStar());
                    vo.setGiveInformation(bo.getGiveInformation());
                    vo.setAttendRole(bo.getAttendRole());
                    vo.setProductServiceFeedback(bo.getProductServiceFeedback());
                    vo.setIpsFeedback(bo.getIpsFeedback());
                    vo.setAddAmount(getAddAmountString(bo.getAddAmountRmb(), bo.getAddAmountForeign()));
                    vo.setFocusAsset(bo.getFocusAsset());
                    vo.setEstimateNeedBusiness(bo.getEstimateNeedBusiness());
                    vo.setNextPlan(bo.getNextPlan());
                    // 设置陪访人信息
                    setAccompanyingUserString(bo.getAccompanyingUserList(), consultantNameMap, vo);
                    // 设置主管信息
                    vo.setManagerName(consultantNameMap.get(bo.getManagerId()));
                    setManagerFeedback(vo, bo, consultantNameMap);
                    // 设置反馈权限
                    vo.setCanAccompanyFeedback(checkFeedbackPermission(bo, bo.getAccompanyingUserList(),
                            FeedbackTypeEnum.ACCOMPANYING.getCode(), request.getCurrentUserId()));
                    vo.setCanManagerFeedback(checkFeedbackPermission(bo, null,
                            FeedbackTypeEnum.MANAGE.getCode(), request.getCurrentUserId()));
                    voList.add(vo);
                }
                result.setVisitMinutesList(voList);
            }
            return result;
        } catch (Exception e) {
            log.error("查询拜访纪要列表失败", e);
            throw new RuntimeException("查询拜访纪要列表失败:" + Throwables.getStackTraceAsString(e));
        }
    }

    /**
     * @description 两个用户条件如果传了但是转换的userid为空则不用查了
     * @param manageId
     * @param managerUserIdList
     * @param accompanyingUser
     * @param accompanyingUserIdList
     * @return boolean
     * @author: jianjian.yang
     * @date: 2025/5/7 11:31
     * @since JDK 1.8
     */
    private boolean noDataByCondition(String manageId, List<String> managerUserIdList, String accompanyingUser, List<String> accompanyingUserIdList){
        if(StringUtils.isNotBlank(manageId) && CollectionUtils.isEmpty(managerUserIdList)){
            return true;
        }
        if(StringUtils.isNotBlank(accompanyingUser) && CollectionUtils.isEmpty(accompanyingUserIdList)){
            return true;
        }
        return false;
    }

    /**
     * @description 取管理反馈
     * @param vo
     * @param minutesBO
     * @param consultantNameMap
     * @return void
     * @author: jianjian.yang
     * @date: 2025/4/24 19:54
     * @since JDK 1.8
     */
    private void setManagerFeedback(VisitMinutesVO vo, CmVisitMinutesBO minutesBO, Map<String, String> consultantNameMap) {
        if (StringUtils.isNotBlank(minutesBO.getManagerSummary())) {
            vo.setManagerSummary(consultantNameMap.get(minutesBO.getManagerId()) + "：" + minutesBO.getManagerSummary());
        }
        if (StringUtils.isNotBlank(minutesBO.getManagerSuggestion())) {
            vo.setManagerSuggestion(consultantNameMap.get(minutesBO.getManagerId()) + "：" + minutesBO.getManagerSuggestion());
        }

    }

    /**
     * @description 取描述
     * @param codes
     * @return java.lang.String
     * @author: jianjian.yang
     * @date: 2025/4/24 19:53
     * @since JDK 1.8
     */
    public String getDescriptionsByCodesComma(String codes, String visitPurposeOther){
        StringBuilder description = new StringBuilder();
        if(codes != null) {
            for (String code : codes.split(MarkConstants.SEPARATOR_COMMA)) {
                VisitPurposeEnum visitPurposeEnum = VisitPurposeEnum.getByCode(code);
                if(visitPurposeEnum != null) {
                    description.append(visitPurposeEnum.getDescription());
                    if(visitPurposeOther != null && Objects.equals(code, VisitPurposeEnum.OTHER.getCode())){
                        description.append(MarkConstants.SEPARATOR_COLON_CHINESE).append(visitPurposeOther);
                    }
                    description.append(MarkConstants.SEPARATOR_DUN);
                }
            }
            if(description.toString().endsWith(MarkConstants.SEPARATOR_DUN)){
                return description.substring(0, description.length() - 1);
            }
        }
        return description.toString();
    }

    /**
     * @description 设置组织信息
     * @param vo 返回结果
     * @param orgLayerInfoDTO 组织信息
     * @author: jianjian.yang
     * @date: 2025/4/10 10:43
     * @since JDK 1.8
     */
    private void setOrgLayerInfo(VisitMinutesVO vo, OrgLayerInfoDTO orgLayerInfoDTO){
        if(orgLayerInfoDTO != null) {
            vo.setCenterName(orgLayerInfoDTO.getCenterOrgName());
            vo.setAreaName(orgLayerInfoDTO.getDistrictName());
            vo.setBranchName(orgLayerInfoDTO.getPartOrgName());
        }
    }

    /**
     * @description 拼接币种加金额，多个币种用、分隔
     * @param addAmountRmb 人民币金额
     * @param addAmountForeign 外币金额
     * @return java.lang.String
     * @author: jianjian.yang
     * @date: 2025/4/10 10:43
     * @since JDK 1.8
     */
    private String getAddAmountString(String addAmountRmb, String addAmountForeign) {
        if (StringUtils.isBlank(addAmountRmb) && StringUtils.isBlank(addAmountForeign)) {
            return "";
        }
        // 拼接币种加金额，多个币种用、分隔
        StringBuilder sb = new StringBuilder();
        if (StringUtils.isNotBlank(addAmountRmb)) {
            sb.append("人民币").append(addAmountRmb).append("万");
        }
        if (StringUtils.isNotBlank(addAmountForeign)) {
            if (sb.length() > 0) {
                sb.append("、");
            }
            sb.append("外币").append(addAmountForeign).append("万");
        }
        return sb.toString();
    }

    /**
     * @description 设置陪访人信息
     * @param accompanyingUserList 陪访人列表
     * @param vo 返回结果
     * @author: jianjian.yang
     * @date: 2025/4/10 10:43   
     * @since JDK 1.8
     */
    private void setAccompanyingUserString(List<CmVisitMinutesAccompanyingPO> accompanyingUserList,
        Map<String, String> consultantNameMap, VisitMinutesVO vo) {
        if (accompanyingUserList == null || accompanyingUserList.isEmpty()) {
            return;
        }
        // 拼接陪访人员，多个陪访人员用、分隔
        StringBuilder sbAccompanyingName = new StringBuilder();
        StringBuilder sbAccompanyingType = new StringBuilder();
        StringBuilder sbAccompanyingSummary = new StringBuilder();
        StringBuilder sbAccompanyingSuggestion = new StringBuilder();
        for (CmVisitMinutesAccompanyingPO po : accompanyingUserList) {
            if (sbAccompanyingName.length() > 0) {
                sbAccompanyingName.append(MarkConstants.SEPARATOR_DUN);
            }
            // 陪访人员
            sbAccompanyingName.append(consultantNameMap.get(po.getAccompanyingUserId()));
            String accompanyingTypeName = AccompanyingTypeEnum.getNameByCode(po.getAccompanyingType());

            // 陪访人类型
            if(sbAccompanyingType.indexOf(accompanyingTypeName) < 0) {
                if (sbAccompanyingType.length() > 0) {
                    sbAccompanyingType.append(MarkConstants.SEPARATOR_DUN);
                }
                sbAccompanyingType.append(AccompanyingTypeEnum.getNameByCode(po.getAccompanyingType()));
            }
            // 陪访总结
            if(StringUtils.isNotBlank(po.getAccompanySummary())) {
                if (sbAccompanyingSummary.length() > 0) {
                    sbAccompanyingSummary.append(MarkConstants.SEPARATOR_SEMI_COLON);
                }
                sbAccompanyingSummary.append(consultantNameMap.get(po.getAccompanyingUserId())).append(MarkConstants.SEPARATOR_COLON_CHINESE).append(po.getAccompanySummary());
            }
            // 陪访意见建议
            if(StringUtils.isNotBlank(po.getAccompanySuggestion())) {
                if (sbAccompanyingSuggestion.length() > 0) {
                    sbAccompanyingSuggestion.append(MarkConstants.SEPARATOR_SEMI_COLON);
                }
                sbAccompanyingSuggestion.append(consultantNameMap.get(po.getAccompanyingUserId())).append(MarkConstants.SEPARATOR_COLON_CHINESE).append(po.getAccompanySuggestion());
            }
        }
        vo.setAccompanyingUser(sbAccompanyingName.toString());
        vo.setAccompanyingType(sbAccompanyingType.toString());
        vo.setAccompanySummary(sbAccompanyingSummary.toString());
        vo.setAccompanySuggestion(sbAccompanyingSuggestion.toString());
    }
    
    /**
     * @description 查询拜访纪要详情
     * @param request 查询请求参数
     * @return com.howbuy.crm.account.client.response.commvisit.VisitMinutesDetailVO
     * @author: jianjian.yang
     * @date: 2025/4/9 16:04
     * @since JDK 1.8
     */
    public VisitMinutesDetailVO getVisitMinutesDetail(QueryVisitMinutesDetailRequest request) {
        try {
            // 参数校验
            if (StringUtils.isBlank(request.getVisitMinutesId())) {
                throw new IllegalArgumentException("拜访纪要ID不能为空");
            }
            
            // 查询拜访纪要基本信息
            CmVisitMinutesBO minutesBO = cmVisitMinutesRepository.getVisitMinutesBoById(request.getVisitMinutesId());
            if (minutesBO == null) {
                throw new IllegalArgumentException("拜访纪要不存在");
            }
            
            // 查询陪访人信息
            List<CmVisitMinutesAccompanyingPO> accompanyingList = minutesBO.getAccompanyingUserList();
            
            // 构建返回结果
            VisitMinutesDetailVO detailVO = new VisitMinutesDetailVO();
            setBasicInfo(detailVO, minutesBO);

            // 查询陪访人姓名
            Map<String, String> consultantNameMap = getConsultantNameMap(accompanyingList, minutesBO.getManagerId());
            List<AccompanyingVO> accompanyingVOList = Lists.newArrayList();
            // 处理陪访人列表
            if (accompanyingList != null && !accompanyingList.isEmpty()) {
                accompanyingVOList = accompanyingList.stream()
                    .map(po -> {
                        AccompanyingVO vo = new AccompanyingVO();
                        vo.setUserName(consultantNameMap.get(po.getAccompanyingUserId()));
                        vo.setSummary(po.getAccompanySummary());
                        vo.setSuggestion(po.getAccompanySuggestion());
                        return vo;
                    })
                    .collect(Collectors.toList());

                // 设置陪访人姓名(逗号分隔)
                String accompanyingNames = accompanyingList.stream()
                    .map(po -> consultantNameMap.get(po.getAccompanyingUserId()))
                    .collect(Collectors.joining(","));
                detailVO.setAccompanyingUser(accompanyingNames);
            }
            if(StringUtils.isNotBlank(minutesBO.getManagerId())) {
                // 上级主管
                AccompanyingVO vo = new AccompanyingVO();
                vo.setManagerName(consultantNameMap.get(minutesBO.getManagerId()));
                vo.setSummary(minutesBO.getManagerSummary());
                vo.setSuggestion(minutesBO.getManagerSuggestion());
                accompanyingVOList.add(vo);
            }
            detailVO.setAccompanyingList(accompanyingVOList);
            
            // 检查编辑权限
            boolean canEdit = checkEditPermission(minutesBO);
            detailVO.setCanEditData(canEdit);
            
            return detailVO;
        } catch (Exception e) {
            log.error("查询拜访纪要详情失败", e);
            throw new RuntimeException("查询拜访纪要详情失败:" + e.getMessage());
        }
    }

    /**
     * @description 保存陪访人/主管反馈
     * @param request 保存请求参数
     * @return com.howbuy.crm.account.client.response.commvisit.OperationResultVO
     * @author: jianjian.yang
     * @date: 2025/4/15 17:33
     * @since JDK 1.8
     */
    public OperationResultVO saveFeedback(SaveFeedbackRequest request) {
        try {
            OperationResultVO validResult = validateSaveFeedbackRequest(request);
            if (!validResult.isSuccess()) {
                return validResult;
            }
    
            // 查询并校验拜访纪要
            CmVisitMinutesPO minutesPO = cmVisitMinutesRepository.getVisitMinutesById(request.getVisitMinutesId());
            if (minutesPO == null) {
                return OperationResultVO.fail("拜访纪要不存在");
            }
    
            // 根据反馈类型处理
            if (FeedbackTypeEnum.ACCOMPANYING.getCode().equals(request.getFeedbackType())) {
                return handleAccompanyingFeedback(request, minutesPO);
            } else if (FeedbackTypeEnum.MANAGE.getCode().equals(request.getFeedbackType())) {
                return handleManagerFeedback(request, minutesPO);
            } else {
                return OperationResultVO.fail("反馈类型错误");
            }
        } catch (Exception e) {
            log.error("保存陪访人/主管反馈失败", e);
            return OperationResultVO.fail("保存陪访人/主管反馈失败:" + e.getMessage());
        }
    }
    
    /**
     * @description 参数校验
     * @param request 保存请求参数
     * @return com.howbuy.crm.account.client.response.commvisit.OperationResultVO
     * @author: jianjian.yang
     * @date: 2025/4/15 17:33
     * @since JDK 1.8
     */
    private OperationResultVO validateSaveFeedbackRequest(SaveFeedbackRequest request) {
        if (StringUtils.isBlank(request.getVisitMinutesId())) {
            return OperationResultVO.fail("拜访纪要ID不能为空");
        }
        if (StringUtils.isBlank(request.getFeedbackType())) {
            return OperationResultVO.fail("反馈类型不能为空");
        }
        if (FeedbackTypeEnum.ACCOMPANYING.getCode().equals(request.getFeedbackType())
                && StringUtils.isBlank(request.getAccompanyingId())) {
            return OperationResultVO.fail("陪访人ID不能为空");
        }
        if (StringUtils.isBlank(request.getSuggestion())) {
            return OperationResultVO.fail("工作建议不能为空");
        }
        if (StringUtils.isBlank(request.getCurrentUserId())) {
            return OperationResultVO.fail("当前用户ID不能为空");
        }
        return OperationResultVO.success();
    }
    
    /**
     * @description 处理陪访人反馈
     * @param request 保存请求参数
     * @param minutesPO 拜访纪要
     * @return com.howbuy.crm.account.client.response.commvisit.OperationResultVO
     * @author: jianjian.yang
     * @date: 2025/4/15 17:33
     * @since JDK 1.8
     */
    private OperationResultVO handleAccompanyingFeedback(SaveFeedbackRequest request, CmVisitMinutesPO minutesPO) {
        // 获取并校验陪访人信息
        CmVisitMinutesAccompanyingPO accompanyingPO = 
            cmVisitMinutesAccompanyRepository.getAccompanyingById(request.getAccompanyingId());
        if (accompanyingPO == null) {
            return OperationResultVO.fail("陪访人信息不存在");
        }
        if (!Objects.equals(accompanyingPO.getAccompanyingUserId(), request.getCurrentUserId())){
            return OperationResultVO.fail("无权限");
        }
    
        // 更新陪访人反馈
        updateAccompanyingFeedback(accompanyingPO, request);
    
        // 如果当前用户也是主管，同步更新主管反馈
        if (Objects.equals(minutesPO.getManagerId(), request.getCurrentUserId())) {
            updateManagerFeedback(minutesPO, request);
        }
    
        return OperationResultVO.success();
    }
    
    /**
     * @description 处理主管反馈
     * @param request 保存请求参数
     * @param minutesPO 拜访纪要
     * @return com.howbuy.crm.account.client.response.commvisit.OperationResultVO
     * @author: jianjian.yang
     * @date: 2025/4/15 17:33
     * @since JDK 1.8
     */
    private OperationResultVO handleManagerFeedback(SaveFeedbackRequest request, CmVisitMinutesPO minutesPO) {
        if (!Objects.equals(minutesPO.getManagerId(),  request.getCurrentUserId())) {
            return OperationResultVO.fail("无权限");
        }
        // 更新主管反馈
        updateManagerFeedback(minutesPO, request);
    
        // 如果当前用户也是陪访人，同步更新陪访人反馈
        List<CmVisitMinutesAccompanyingPO> accompanyingList = 
            cmVisitMinutesAccompanyRepository.getAccompanyingList(request.getVisitMinutesId());
        updateAccompanyingIfNeeded(accompanyingList, request);
    
        return OperationResultVO.success();
    }
    
    /**
     * @description 更新陪访人反馈
     * @param accompanyingPO 陪访人
     * @param request 保存请求参数
     * @return void
     * @author: jianjian.yang
     * @date: 2025/4/15 17:33
     * @since JDK 1.8
     */
    private void updateAccompanyingFeedback(CmVisitMinutesAccompanyingPO accompanyingPO, SaveFeedbackRequest request) {
        accompanyingPO.setAccompanySummary(request.getSummary());
        accompanyingPO.setAccompanySuggestion(request.getSuggestion());
        accompanyingPO.setModifyTime(new Date());
        accompanyingPO.setModifier(request.getCurrentUserId());
        accompanyingPO.setFillTime(new Date());
        cmVisitMinutesAccompanyRepository.updateAccompanyingFeedback(accompanyingPO);
    }

    /**
     * @description 更新主管反馈
     * @param minutesPO 拜访纪要
     * @param request 保存请求参数
     * @return void
     * @author: jianjian.yang
     * @date: 2025/4/15 17:33
     * @since JDK 1.8
     */
    private void updateManagerFeedback(CmVisitMinutesPO minutesPO, SaveFeedbackRequest request) {
        minutesPO.setManagerSummary(request.getSummary());
        minutesPO.setManagerSuggestion(request.getSuggestion());
        minutesPO.setModifyTime(new Date());
        minutesPO.setModifier(request.getCurrentUserId());
        minutesPO.setManagerFillTime(new Date());
        cmVisitMinutesRepository.updateManagerFeedback(minutesPO);
    }
    
    /**
     * @description 如果当前用户是陪访人则更新陪访人反馈
     * @param accompanyingList 陪访人列表
     * @param request 保存请求参数
     * @return void
     * @author: jianjian.yang
     * @date: 2025/4/15 17:33
     * @since JDK 1.8
     */
    private void updateAccompanyingIfNeeded(List<CmVisitMinutesAccompanyingPO> accompanyingList, SaveFeedbackRequest request) {
        if (accompanyingList.stream().anyMatch(po -> po.getAccompanyingUserId().equals(request.getCurrentUserId()))) {
            accompanyingList.stream()
                    .filter(po -> po.getAccompanyingUserId().equals(request.getCurrentUserId()))
                    .findFirst().ifPresent(accompanyingPO -> updateAccompanyingFeedback(accompanyingPO, request));
        }
    }
    
    /**
     * @description 修改主管
     * @param request 修改请求参数
     * @return com.howbuy.crm.account.client.response.commvisit.OperationResultVO
     * @author: jianjian.yang
     * @date: 2025/4/9 16:04
     * @since JDK 1.8
     */
    public OperationResultVO updateManager(UpdateManagerRequest request) {
        try {
            // 参数校验
            if (CollectionUtils.isEmpty(request.getVisitMinutesIds())) {
                return OperationResultVO.fail("拜访纪要ID不能为空");
            }
            if (request.getIsClear() == null) {
                return OperationResultVO.fail("是否清空不能为空");
            }
            if (!request.getIsClear() && StringUtils.isBlank(request.getNewUserId())) {
                return OperationResultVO.fail("新用户ID不能为空");
            }
            
            // 查询拜访纪要是否已反馈的数据
            int feedbackCount = cmVisitMinutesRepository.getFeedbackCountById(request.getVisitMinutesIds());
            if (feedbackCount > 0) {
                return OperationResultVO.fail("修改失败：存在上级主管已反馈的拜访纪要");
            }

            if (request.getIsClear()) {
                // 清空主管
                cmVisitMinutesRepository.updateManager(request.getVisitMinutesIds(),
                        null, null, request.getCurrentUserId());
            } else {
                String managerUserLevel = cmConsultantExpService.getAccompanyHighestLevelByConsCode(request.getCurrentUserId());
                // 更新主管
                cmVisitMinutesRepository.updateManager(request.getVisitMinutesIds(),
                    request.getNewUserId(), managerUserLevel, request.getCurrentUserId());
            }
            
            return OperationResultVO.success();
        } catch (Exception e) {
            log.error("修改主管失败", e);
            return OperationResultVO.fail("修改主管失败:" + e.getMessage());
        }
    }
    
    /**
     * @description 查询拜访纪要反馈明细
     * @param request 查询请求参数
     * @return com.howbuy.crm.account.client.response.commvisit.VisitMinutesForFeedbackVO
     * @author: jianjian.yang
     * @date: 2025/4/9 16:04
     * @since JDK 1.8
     */
    public VisitMinutesForFeedbackVO getVisitMinutesForFeedback(QueryVisitMinutesForFeedbackRequest request) {
        try {
            // 参数校验
            if (StringUtils.isBlank(request.getVisitMinutesId())) {
                throw new RuntimeException("拜访纪要ID不能为空");
            }
            if (StringUtils.isBlank(request.getFeedbackType())) {
                throw new RuntimeException("反馈类型不能为空");
            }
            if (StringUtils.isBlank(request.getCurrentUserId())) {
                throw new RuntimeException("当前用户ID不能为空");
            }

            // 查询拜访纪要
            CmVisitMinutesBO cmVisitMinutesBO = cmVisitMinutesRepository.getVisitMinutesBoById(request.getVisitMinutesId());
            if (cmVisitMinutesBO == null) {
                throw new RuntimeException("拜访纪要不存在");
            }

            // 构建返回结果
            VisitMinutesForFeedbackVO feedbackVO = new VisitMinutesForFeedbackVO();
            // 设置基本信息
            setBasicInfo(feedbackVO, cmVisitMinutesBO);

            // 陪访人列表
            List<CmVisitMinutesAccompanyingPO> accompanyingList = cmVisitMinutesBO.getAccompanyingUserList();
            // 查询陪访人姓名
            Map<String, String> consultantNameMap = getConsultantNameMap(accompanyingList, cmVisitMinutesBO.getManagerId());
            // 设置陪访人列表
            if (CollectionUtils.isNotEmpty(accompanyingList)) {
                List<String> accompanyingNames = accompanyingList.stream().map(po -> consultantNameMap.get(po.getAccompanyingUserId()))
                        .collect(Collectors.toList());
                feedbackVO.setAccompanyingList(accompanyingNames);
            }

            // 设置主管信息
            feedbackVO.setManagerName(consultantNameMap.get(cmVisitMinutesBO.getManagerId()));

            // 陪访人反馈
            CmVisitMinutesAccompanyingPO accompanyingPO = accompanyingList.stream()
                    .filter(po -> request.getCurrentUserId().equals(po.getAccompanyingUserId()))
                    .findFirst()
                    .orElse(null);
            // 设置反馈信息
            if (FeedbackTypeEnum.ACCOMPANYING.getCode().equals(request.getFeedbackType())) {
                if (accompanyingPO != null) {
                    // 陪访人姓名
                    feedbackVO.setAccompanyingUserName(consultantNameMap.get(accompanyingPO.getAccompanyingUserId()));
                    feedbackVO.setAccompanyingId(accompanyingPO.getId());
                    feedbackVO.setSummary(accompanyingPO.getAccompanySummary());
                    feedbackVO.setSuggestion(accompanyingPO.getAccompanySuggestion());
                }
            } else {
                // 主管反馈
                feedbackVO.setSummary(cmVisitMinutesBO.getManagerSummary());
                feedbackVO.setSuggestion(cmVisitMinutesBO.getManagerSuggestion());
                // 设置编辑权限 当前用户=任意陪访人，可编辑
                feedbackVO.setCanNotEditSummary(accompanyingPO == null);
            }
            return feedbackVO;
        } catch (Exception e) {
            log.error("查询拜访纪要反馈明细失败", e);
            throw new RuntimeException("查询拜访纪要反馈明细失败:" + e.getMessage());
        }
    }

    /**
     * @description 设置基本信息
     * @param feedbackVO
     * @param cmVisitMinutesBO
     * @return void
     * @author: jianjian.yang
     * @date: 2025/4/14 15:28
     * @since JDK 1.8
     */
    private void setBasicInfo(VisitMinutesForFeedbackVO feedbackVO, CmVisitMinutesBO cmVisitMinutesBO) {
        // 设置基本信息
        feedbackVO.setCustName(cmVisitMinutesBO.getCustName());
        feedbackVO.setConsCustNo(cmVisitMinutesBO.getConsCustNo());
        feedbackVO.setVisitDt(cmVisitMinutesBO.getVisitDt());
        feedbackVO.setVisitType(cmVisitMinutesBO.getVisitTypeName() != null ? cmVisitMinutesBO.getVisitTypeName() : cmVisitMinutesBO.getVisitType());
        feedbackVO.setMarketVal(cmVisitMinutesBO.getMarketVal());
        feedbackVO.setHealthAvgStar(cmVisitMinutesBO.getHealthAvgStar());

        // 设置拜访目的
        if (StringUtils.isNotBlank(cmVisitMinutesBO.getVisitPurpose())) {
            feedbackVO.setVisitPurposeList(Arrays.stream(cmVisitMinutesBO.getVisitPurpose().split(","))
                    .collect(Collectors.toList()));
        }

        // 设置其他基本信息
        feedbackVO.setVisitPurposeOther(cmVisitMinutesBO.getVisitPurposeOther());
        feedbackVO.setReportId(cmVisitMinutesBO.getAssetReportId());
        feedbackVO.setGiveInformation(cmVisitMinutesBO.getGiveInformation());
        feedbackVO.setAttendRole(cmVisitMinutesBO.getAttendRole());
        feedbackVO.setProductServiceFeedback(cmVisitMinutesBO.getProductServiceFeedback());
        feedbackVO.setIpsFeedback(cmVisitMinutesBO.getIpsFeedback());
        feedbackVO.setAddAmountRmb(cmVisitMinutesBO.getAddAmountRmb());
        feedbackVO.setAddAmountForeign(cmVisitMinutesBO.getAddAmountForeign());
        feedbackVO.setFocusAsset(cmVisitMinutesBO.getFocusAsset());
        feedbackVO.setEstimateNeedBusiness(cmVisitMinutesBO.getEstimateNeedBusiness());
        feedbackVO.setNextPlan(cmVisitMinutesBO.getNextPlan());
    }

    /**
     * @description 设置基本信息
     * @param detailVO 详情VO
     * @param cmVisitMinutesBO 拜访纪要BO
     * @return void
     * @author: jianjian.yang
     * @date: 2025/4/14 15:28
     * @since JDK 1.8
     */
    private void setBasicInfo(VisitMinutesDetailVO detailVO, CmVisitMinutesBO cmVisitMinutesBO){
        // 设置基本信息
        detailVO.setCustName(cmVisitMinutesBO.getCustName());
        detailVO.setConsCustNo(cmVisitMinutesBO.getConsCustNo());
        detailVO.setVisitDt(cmVisitMinutesBO.getVisitDt());
        detailVO.setVisitType(cmVisitMinutesBO.getVisitTypeName());
        detailVO.setMarketVal(cmVisitMinutesBO.getMarketVal());
        detailVO.setHealthAvgStar(cmVisitMinutesBO.getHealthAvgStar());

        // 设置拜访目的
        if (StringUtils.isNotBlank(cmVisitMinutesBO.getVisitPurpose())) {
            detailVO.setVisitPurposeList(Arrays.stream(cmVisitMinutesBO.getVisitPurpose().split(","))
                    .collect(Collectors.toList()));
        }

        // 设置其他基本信息
        detailVO.setVisitPurposeOther(cmVisitMinutesBO.getVisitPurposeOther());
        detailVO.setReportId(cmVisitMinutesBO.getAssetReportId());
        detailVO.setGiveInformation(cmVisitMinutesBO.getGiveInformation());
        detailVO.setAttendRole(cmVisitMinutesBO.getAttendRole());
        detailVO.setProductServiceFeedback(cmVisitMinutesBO.getProductServiceFeedback());
        detailVO.setIpsFeedback(cmVisitMinutesBO.getIpsFeedback());
        detailVO.setAddAmountRmb(cmVisitMinutesBO.getAddAmountRmb());
        detailVO.setAddAmountForeign(cmVisitMinutesBO.getAddAmountForeign());
        detailVO.setFocusAsset(cmVisitMinutesBO.getFocusAsset());
        detailVO.setEstimateNeedBusiness(cmVisitMinutesBO.getEstimateNeedBusiness());
        detailVO.setNextPlan(cmVisitMinutesBO.getNextPlan());
    }

    /**
     * @description 查投顾姓名
     * @param accompanyingList
     * @param manageId
     * @return java.util.Map<java.lang.String,java.lang.String>
     * @author: jianjian.yang
     * @date: 2025/4/14 16:32
     * @since JDK 1.8
     */
    private Map<String, String> getConsultantNameMap(List<CmVisitMinutesAccompanyingPO> accompanyingList, String manageId) {
        List<String> consCodes = Lists.newArrayList();
        if(manageId != null){
            consCodes.add(manageId);
        }
        // 陪访人id列表
        if (CollectionUtils.isNotEmpty(accompanyingList)) {
            consCodes.addAll(accompanyingList.stream()
                    .map(CmVisitMinutesAccompanyingPO::getAccompanyingUserId)
                    .collect(Collectors.toList()));
        }
        // 查询陪访人姓名
        return consultantInfoService.getConsultantNameMap(consCodes);
    }
    
    /**
     * @description 检查反馈权限
     * @param minutesPO 拜访纪要    
     * @param accompanyingList 陪访人列表
     * @param feedbackType 反馈类型
     * @param currentUserId 当前用户ID
     * @return boolean
     * @author: jianjian.yang
     * @date: 2025/4/10 14:21
     * @since JDK 1.8
     */
    private boolean checkFeedbackPermission(CmVisitMinutesPO minutesPO, List<CmVisitMinutesAccompanyingPO> accompanyingList,
                                            String feedbackType, String currentUserId) {
        LocalDateTime createTime = minutesPO.getCreateTime();
        if (createTime == null) {
            return false;
        }
        // 判断是否7天内
        String deadlineDay = createTime.plusDays(6).format(LocalDateUtil.DTF);
        if (deadlineDay.compareTo(LocalDateUtil.localDateToString(LocalDate.now(), LocalDateUtil.DTF)) < 0) {
            return false;
        }

        if (FeedbackTypeEnum.ACCOMPANYING.getCode().equals(feedbackType)) {
            // 陪访人反馈权限
            if(accompanyingList == null) {
                accompanyingList =
                        cmVisitMinutesAccompanyRepository.getAccompanyingList(minutesPO.getId());
            }
            
            return accompanyingList.stream()
                .anyMatch(po -> currentUserId.equals(po.getAccompanyingUserId()));
        } else if (FeedbackTypeEnum.MANAGE.getCode().equals(feedbackType)) {
            // 主管反馈权限
            return currentUserId.equals(minutesPO.getManagerId());
        }
        
        return false;
    }
    
    /**
     * @description 检查编辑权限
     * @param minutesBO 拜访纪要
     * @return boolean
     * @author: jianjian.yang
     * @date: 2025/4/14 16:32
     * @since JDK 1.8
     */
    private boolean checkEditPermission(CmVisitMinutesBO minutesBO) {
        // 条件2：拜访纪要创建的3个自然日内：创建日期+2天
        // 条件3：拜访纪要无任意陪访人反馈 或 上级主管反馈
        LocalDateTime createTime = minutesBO.getCreateTime();
        if (createTime == null) {
            return false;
        }
        if (createTime.plusDays(2).format(LocalDateUtil.DTF).compareTo(LocalDateTime.now().format(LocalDateUtil.DTF)) < 0) {
            return false;
        }
        return checkNoFillFeedback(minutesBO);
    }

    /**
     * @description 校验是否没有反馈
     * @param minutesBO
     * @return boolean
     * @author: jianjian.yang
     * @date: 2025/4/25 19:46
     * @since JDK 1.8
     */
    private boolean checkNoFillFeedback(CmVisitMinutesBO minutesBO){
        if (StringUtils.isNotBlank(minutesBO.getManagerSuggestion())) {
            return false;
        }
        if (CollectionUtils.isEmpty(minutesBO.getAccompanyingUserList())) {
            return true;
        }
        return minutesBO.getAccompanyingUserList().stream().noneMatch(po -> StringUtils.isNotBlank(po.getAccompanySuggestion()));
    }

    /**
     * @param request 导出请求参数
     * @return com.howbuy.crm.account.client.response.ExportToFileVO
     * @description:导出客户拜访纪要数据
     * <AUTHOR>
     * @date 2025-04-08 10:26:00
     * @since JDK 1.8
     */
    public ExportToFileVO exportVisitMinutes(ExportVisitMinutesRequest request) {
        try {
            
            // 构建查询DTO
            QueryVisitMinutesListRequest queryRequest = new QueryVisitMinutesListRequest();
            queryRequest.setOrgCode(request.getOrgCode());
            queryRequest.setConsCode(request.getConsCode());
            queryRequest.setVisitDateStart(request.getVisitDateStart());
            queryRequest.setVisitDateEnd(request.getVisitDateEnd());
            queryRequest.setCustName(request.getCustName());
            queryRequest.setConsCustNo(request.getConsCustNo());
            queryRequest.setCreateDateStart(request.getCreateDateStart());
            queryRequest.setCreateDateEnd(request.getCreateDateEnd());
            queryRequest.setVisitPurpose(request.getVisitPurpose());
            queryRequest.setManagerId(request.getManagerId());
            queryRequest.setAccompanyingUser(request.getAccompanyingUser());
            queryRequest.setFeedbackStatus(request.getFeedbackStatus());
            queryRequest.setCurrentUserId(request.getCurrentUserId());
            queryRequest.setPageNo(1);
            queryRequest.setPageSize(Integer.MAX_VALUE);
            
            // 查询数据
            VisitMinutesListVO visitMinutesListVO = queryVisitMinutesList(queryRequest);
            
            if (visitMinutesListVO == null || CollectionUtils.isEmpty(visitMinutesListVO.getVisitMinutesList())) {
                ExportToFileVO result = new ExportToFileVO();
                result.setErrorMsg("没有符合条件的数据");
                return result;
            }

            List<VisitMinutesExcelVO> excelVOList = visitMinutesListVO.getVisitMinutesList().stream()
                    .map(vo -> {
                        VisitMinutesExcelVO excelVO = new VisitMinutesExcelVO();
                        excelVO.setCreateTime(vo.getCreateTime());
                        excelVO.setVisitDt(vo.getVisitDt());
                        excelVO.setVisitPurpose(vo.getVisitPurpose());
                        excelVO.setConsCustNo(vo.getConsCustNo());
                        excelVO.setCustName(vo.getCustName());
                        excelVO.setCreatorName(vo.getCreatorName());
                        excelVO.setCenterName(vo.getCenterName());     // 添加所属中心
                        excelVO.setAreaName(vo.getAreaName());         // 添加所属区域
                        excelVO.setBranchName(vo.getBranchName());     // 添加所属分公司
                        excelVO.setVisitType(vo.getVisitType());
                        excelVO.setManagerName(vo.getManagerName());    
                        excelVO.setAccompanyingUser(vo.getAccompanyingUser());
                        excelVO.setMarketVal(vo.getMarketVal());
                        excelVO.setHealthAvgStar(vo.getHealthAvgStar());
                        excelVO.setGiveInformation(vo.getGiveInformation());
                        excelVO.setAttendRole(vo.getAttendRole());
                        excelVO.setProductServiceFeedback(vo.getProductServiceFeedback());  
                        excelVO.setIpsFeedback(vo.getIpsFeedback());
                        excelVO.setAddAmount(vo.getAddAmount());
                        excelVO.setFocusAsset(vo.getFocusAsset());
                        excelVO.setEstimateNeedBusiness(vo.getEstimateNeedBusiness());
                        excelVO.setNextPlan(vo.getNextPlan());  
                        excelVO.setAccompanyingType(vo.getAccompanyingType());
                        excelVO.setAccompanyingUser(vo.getAccompanyingUser());
                        excelVO.setAccompanySummary(vo.getAccompanySummary());
                        excelVO.setAccompanySuggestion(vo.getAccompanySuggestion());
                        excelVO.setManagerName(vo.getManagerName());    
                        excelVO.setManagerSummary(vo.getManagerSummary());
                        excelVO.setManagerSuggestion(vo.getManagerSuggestion());
                        return excelVO;
                    })
                    .collect(Collectors.toList());
            String menuName = cmVisitMinutesRepository.getMenuName(VISIT_MINUTES_MENU_CODE);
            return ExportUtil.export(excelVOList, menuName, VisitMinutesExcelVO.class);
        } catch (Exception e) {
            log.error("导出客户拜访纪要数据失败", e);
            ExportToFileVO result = new ExportToFileVO();
            result.setErrorMsg("导出客户拜访纪要数据失败:" + e.getMessage());
            return result;
        }
    }

    /**
     * @description 保存客户反馈
     * @param request 请求参数
     * @return com.howbuy.crm.account.client.response.commvisit.OperationResultVO
     * @author: jianjian.yang
     * @date: 2025/4/9 16:04
     * @since JDK 1.8
     */
    public OperationResultVO saveCustFeedback(SaveCustFeedbackRequest request) {
        try {
            // 参数校验
            if (StringUtils.isBlank(request.getVisitMinutesId())) {
                return OperationResultVO.fail("拜访纪要ID不能为空");
            }

            // 查询拜访纪要
            CmVisitMinutesBO minutesBO = cmVisitMinutesRepository.getVisitMinutesBoById(request.getVisitMinutesId());
            if (minutesBO == null) {
                return OperationResultVO.fail("拜访纪要不存在");
            }
            // 有陪访人/上级主管填写反馈，不可客户反馈
            if (!checkNoFillFeedback(minutesBO)) {
                return OperationResultVO.fail("陪访人/上级主管已填写反馈，本次修改失败");
            }

            CmVisitMinutesPO cmVisitMinutesPO = new CmVisitMinutesPO();
            // 更新拜访纪要基本信息
            cmVisitMinutesPO.setAttendRole(request.getAttendRole());
            cmVisitMinutesPO.setProductServiceFeedback(request.getProductServiceFeedback());
            cmVisitMinutesPO.setIpsFeedback(request.getIpsFeedback());
            cmVisitMinutesPO.setAddAmountRmb(request.getAddAmtRmb());
            cmVisitMinutesPO.setAddAmountForeign(request.getAddAmtForeign());
            cmVisitMinutesPO.setFocusAsset(request.getFocusAsset());
            cmVisitMinutesPO.setEstimateNeedBusiness(request.getEstimateNeedBusiness());
            cmVisitMinutesPO.setNextPlan(request.getNextPlan());
            cmVisitMinutesPO.setModifier(request.getCurrentUserId());
            cmVisitMinutesPO.setModifyTime(new Date());
            cmVisitMinutesPO.setId(request.getVisitMinutesId());

            log.info("更新拜访纪要 cmVisitMinutesPO:{}", JSON.toJSONString(cmVisitMinutesPO));
            // 更新拜访纪要
            cmVisitMinutesRepository.updateVisitMinutes(cmVisitMinutesPO);
            log.info("更新拜访纪要成功");
            // 待推送的用户id
            List<String> pushUserIds = new ArrayList<>();
            // 更新陪访人信息
            if (CollectionUtils.isEmpty(minutesBO.getAccompanyingUserList())
                    && CollectionUtils.isNotEmpty(request.getAccompanyingList())) {
                // 添加新的陪访人
                for (SaveCustFeedbackRequest.AccompanyingRequest accompanyingRequest : request.getAccompanyingList()) {
                    CmVisitMinutesAccompanyingPO accompanyingPO = new CmVisitMinutesAccompanyingPO();
                    String visitMinutesAccompanyingId = commonMapper.getSeqValue(SequenceConstants.SEQ_VISIT_MINUTES_ACCOMPANYING);
                    accompanyingPO.setId(visitMinutesAccompanyingId);
                    accompanyingPO.setVisitMinutesId(request.getVisitMinutesId());
                    accompanyingPO.setAccompanyingType(accompanyingRequest.getAccompanyingType());
                    accompanyingPO.setAccompanyingUserId(accompanyingRequest.getAccompanyingUserId());
                    // 获取陪访人最高层级
                    String accompanyingUserLevel = cmConsultantExpService.getAccompanyHighestLevelByConsCode(accompanyingRequest.getAccompanyingUserId());
                    accompanyingPO.setAccompanyingUserLevel(accompanyingUserLevel);
                    accompanyingPO.setCreator(request.getCurrentUserId());
                    accompanyingPO.setCreateTime(new Date());
                    
                    // 保存陪访人信息
                    cmVisitMinutesAccompanyRepository.insert(accompanyingPO);
                    // 不是其他类型的陪访人推送消息给陪访人
                    if (!Objects.equals(accompanyingRequest.getAccompanyingType(), AccompanyingTypeEnum.OTHER.getCode())) {
                        pushUserIds.add(accompanyingRequest.getAccompanyingUserId());
                    }

                }
                // 按拜访目的判断是否推送
                if(containsSendMsgType(minutesBO.getVisitPurpose())) {
                    // 推送消息
                    addAccompanyingPushMsg(pushUserIds, minutesBO.getCreator(), minutesBO.getCustName(),
                            minutesBO.getConsCustNo(), minutesBO.getCreateTime());
                }
            }

            return OperationResultVO.success();
        } catch (Exception e) {
            log.error("修改陪访人/主管失败" + Throwables.getStackTraceAsString(e));
        }
        return OperationResultVO.fail("修改陪访人/主管失败");
    }

    /**
     * @description 用户搜索
     * @param request 搜索参数
     * @return com.howbuy.crm.account.client.response.commvisit.SearchUserVO
     * @author: jianjian.yang
     * @date: 2025/4/9 16:04
     * @since JDK 1.8
     */
    public SearchUserVO searchUser(SearchUserRequest request) {
        log.info("CmVisitMinutesService_searchUser_request:{}", JSON.toJSONString(request));
        SearchUserVO result = new SearchUserVO();
        List<SearchConsultantDTO> consultantList;
        // 根据搜索类型调用不同的查询方法
        if (ConsultantSearchTypeEnum.PM_NORMAL.getCode().equals(request.getSearchType())) {
            // 查询项目经理
            consultantList = consultantInfoService.searchConsultant(request.getSearchParam(), true, request.getCurrentUserId());
        } else if (ConsultantSearchTypeEnum.ALL_NORMAL.getCode().equals(request.getSearchType())) {
            // 查询所有正常用户
            consultantList = consultantInfoService.searchConsultant(request.getSearchParam(), false, null);
        } else {
            throw new RuntimeException("不支持的搜索类型");
        }
        if (consultantList != null && !consultantList.isEmpty()) {
            List<SearchUserVO.UserItem> userItemList = Lists.newArrayList();
            consultantList.forEach(dto -> {
                SearchUserVO.UserItem userItem = new SearchUserVO.UserItem();
                if (StringUtils.isNotBlank(dto.getConsCode())) {
                    userItem.setUserId(dto.getConsCode());
                    userItem.setUserName(dto.getConsName());
                    userItemList.add(userItem);
                }
            });
            result.setUserList(userItemList);
        }
        return result;
    }

    /**
     * @description 添加陪访人推送消息
     * @param pushUserIds 推送用户id
     * @param creator 创建人
     * @param custName 客户姓名
     * @param consCustNo 客户编号
     * @param createTime 创建时间   
     * @author: jianjian.yang
     * @date: 2025/4/30 13:45
     */
    public void addAccompanyingPushMsg(List<String> pushUserIds, String creator, String custName, String  consCustNo, LocalDateTime createTime){
        if(CollectionUtils.isNotEmpty(pushUserIds)) {
            Map<String, String> consultantNameMap = consultantInfoService.getConsultantNameMap(Lists.newArrayList(creator));
            String consultantName = consultantNameMap.get(creator);
            if(StringUtils.isBlank(custName)){
                CmConsCustSimpleBO cmConsCustSimpleBO = consCustInfoRepository.queryCustSimpleInfo(consCustNo);
                if(cmConsCustSimpleBO != null) {
                    custName = cmConsCustSimpleBO.getCustname();
                }
            }
            String custNameSend = custName;
            pushUserIds.stream().distinct().forEach(pushUserId -> {
                // 不是其他类型的陪访人推送消息给陪访人
                pushFeedbackMsg(consultantName, custNameSend, consCustNo,
                        createTime, pushUserId);
            });
        }
    }

    /**
     * @description 推送反馈消息
     * @param consName 投顾姓名
     * @param custName 客户姓名
     * @param custNo 客户编号
     * @param createTime 创建时间
     * @param sendConsCode 发送给的投顾编号
     * @author: jianjian.yang
     * @date: 2025/4/30 13:45
     */
    public void pushFeedbackMsg(String consName, String custName, String custNo, LocalDateTime createTime, String sendConsCode) {
        if(StringUtils.isBlank(sendConsCode)){
            log.error("推送反馈消息失败，sendConsCode为空");
            return;
        }
        if(StringUtils.isBlank(custNo)){
            log.error("推送反馈消息失败，custNo为空");
            return;
        }
        if(StringUtils.isBlank(consName)){
            log.error("推送反馈消息失败，consName为空");
            return;
        }
        if(StringUtils.isBlank(custName)){
            log.error("推送反馈消息失败，custName为空");
            return;
        }
        if (createTime == null){
            log.error("推送反馈消息失败，createTime为空");
            return;
        }
        Map<String, String> paramMap = new HashMap<>();
        paramMap.put("consname", consName);
        paramMap.put("custname", custName);
        paramMap.put("custno", custNo);
        String lastday = createTime.plusDays(FEEDBACK_PLUS_DAY).format(LocalDateUtil.DTF);
        paramMap.put("lastday", lastday);
        cmPushMsgOuterService.sendPushMsg(BusinessIdConstant.BUSINESS_ID_VISIT_MINUTES_ACCOMPANYING, paramMap,
                CmPushMsgOuterService.PUSH_MSG_ACCOUNT_CONS,
                sendConsCode);
    }

    /**
     * @description 推送没有反馈的消息
     * @param curPreDayParam 手动传的日期
     * @return void
     * @author: jianjian.yang
     * @date: 2025/4/29 19:45
     * @since JDK 1.8
     */
    public void pushNoFeedback(String curPreDayParam){
        String curPreDay = curPreDayParam;
        // 如果手动传的日期为空，则取当前日期前一天 手动传的场景是当天没跑后面重跑，正常不会传
        if(StringUtils.isBlank(curPreDayParam)) {
            curPreDay = LocalDate.now().minusDays(FEEDBACK_PLUS_DAY - 1).format(LocalDateUtil.DTF);
        }
        List<NoFeedbackMsgBO> noFeedbackList = cmVisitMinutesRepository.fetchNoFeedbackList(AccompanyingTypeEnum.OTHER.getCode(), curPreDay);
        log.info("CmVisitMinutesService_pushNoFeedback_noFeedbackList:{}", noFeedbackList.size());
        if(CollectionUtils.isNotEmpty(noFeedbackList)){
            noFeedbackList.forEach(noFeedbackMsgBO -> {
                if(containsSendMsgType(noFeedbackMsgBO.getVisitPurpose())) {
                    log.info("CmVisitMinutesService_pushNoFeedback_noFeedbackMsgBO:{}", JSON.toJSONString(noFeedbackMsgBO));
                    pushFeedbackMsg(noFeedbackMsgBO.getConsName(), noFeedbackMsgBO.getCustName(),
                            noFeedbackMsgBO.getConsCustNo(), noFeedbackMsgBO.getCreateTime(),
                            noFeedbackMsgBO.getConsCode());
                }
            });
        }
    }

    /**
     * @description 是否包含需要推送消息的ips、 leads
     * @param visitPurposeCodes	拜访目的
     * @return boolean
     * @author: jianjian.yang
     * @date: 2025/5/6 10:09
     * @since JDK 1.8
     */
    public boolean containsSendMsgType(String visitPurposeCodes){
        if(StringUtils.isBlank(visitPurposeCodes)){
            return false;
        }
        String[] codeArray = visitPurposeCodes.split(MarkConstants.SEPARATOR_COMMA);
        return Arrays.stream(codeArray).anyMatch(code -> VisitPurposeEnum.IPS_VISIT.getCode().equals(code)
                || VisitPurposeEnum.LEADS_VISIT.getCode().equals(code));
    }

} 