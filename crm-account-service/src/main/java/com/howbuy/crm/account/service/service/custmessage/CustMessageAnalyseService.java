/**
 * Copyright (c) 2023, <PERSON><PERSON><PERSON><PERSON> HOWBUY INVESTMENT MANAGEMENT Co., Ltd.
 * All right reserved.
 * <p>
 * THIS IS UNPUBLISHED PROPRIETARY SOURCE CODE OF HOWBUY INVESTMENT
 * MANAGEMENT CO., LTD.  THE CONTENTS OF THIS FILE MAY NOT BE DISCLOSED
 * TO THIRD PARTIES, COPIED OR DUPLICATED IN ANY FORM, IN WHOLE OR IN PART,
 * WITHOUT THE PRIOR WRITTEN PERMISSION OF HOWBUY INVESTMENT MANAGEMENT
 * CO., LTD.
 */
package com.howbuy.crm.account.service.service.custmessage;

import com.howbuy.crm.account.client.enums.custinfo.FullCustSourceEnum;
import com.howbuy.crm.account.service.vo.custinfo.AbnormalAnalyseResultVO;
import com.howbuy.crm.account.service.vo.custinfo.MessageCustInfoVO;

import java.util.List;

/**
 * @description: (请在此添加描述)
 * <AUTHOR>
 * @date 2023/12/13 21:08
 * @since JDK 1.8
 */
public interface CustMessageAnalyseService<T extends MessageCustInfoVO> {



    /**
     * 获取消息来源
     * @return
     */
    List<FullCustSourceEnum> getSourceList();


    /**
     * 香港账户  消息  分析
     * @param messageVo
     * @return
     */
    AbnormalAnalyseResultVO<T> analyze(T messageVo);





}