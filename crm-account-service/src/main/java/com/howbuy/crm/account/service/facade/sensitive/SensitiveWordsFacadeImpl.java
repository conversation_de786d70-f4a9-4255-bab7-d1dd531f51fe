/**
 * Copyright (c) 2025, <PERSON><PERSON><PERSON><PERSON> HOWBUY INVESTMENT MANAGEMENT Co., Ltd.
 * All right reserved.
 * <p>
 * THIS IS UNPUBLISHED PROPRIETARY SOURCE CODE OF HOWBUY INVESTMENT
 * MANAGEMENT CO., LTD.  THE CONTENTS OF THIS FILE MAY NOT BE DISCLOSED
 * TO THIRD PARTIES, COPIED OR DUPLICATED IN ANY FORM, IN WHOLE OR IN PART,
 * WITHOUT THE PRIOR WRITTEN PERMISSION OF HOWBUY INVESTMENT MANAGEMENT
 * CO., LTD.
 */
package com.howbuy.crm.account.service.facade.sensitive;

import com.howbuy.crm.account.client.facade.sensitive.SensitiveWordsFacade;
import com.howbuy.crm.account.client.request.sensitive.SensitiveWordsRequest;
import com.howbuy.crm.account.client.response.Response;
import com.howbuy.crm.account.client.response.sensitive.SensitiveWordsVO;
import com.howbuy.crm.account.service.service.sensitive.SensitiveWordsService;
import lombok.extern.slf4j.Slf4j;
import org.apache.dubbo.config.annotation.DubboService;

import javax.annotation.Resource;

/**
 * @description 资配敏感词查询接口实现
 * <AUTHOR>
 * @date 2024-03-19 14:30:45
 */
@Slf4j
@DubboService
public class SensitiveWordsFacadeImpl implements SensitiveWordsFacade {

    @Resource
    private SensitiveWordsService sensitiveWordsService;
    
    /**
     * @description 查询资配敏感词
     * @param request 查询参数
     * @return 敏感词列表
     */
    @Override
    public Response<SensitiveWordsVO> querySensitiveWords(SensitiveWordsRequest request) {
        log.info("查询敏感词列表开始，请求参数：{}", request);
        SensitiveWordsVO result = sensitiveWordsService.querySensitiveWords(request);
        log.info("查询敏感词列表结束，响应结果：{}", result);
        return Response.ok(result);
    }
} 