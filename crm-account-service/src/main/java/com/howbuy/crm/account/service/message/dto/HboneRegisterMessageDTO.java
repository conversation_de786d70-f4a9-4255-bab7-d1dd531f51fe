/**
 * Copyright (c) 2023, <PERSON><PERSON><PERSON><PERSON> HOWBUY INVESTMENT MANAGEMENT Co., Ltd.
 * All right reserved.
 * <p>
 * THIS IS UNPUBLISHED PROPRIETARY SOURCE CODE OF HOWBUY INVESTMENT
 * MANAGEMENT CO., LTD.  THE CONTENTS OF THIS FILE MAY NOT BE DISCLOSED
 * TO THIRD PARTIES, COPIED OR DUPLICATED IN ANY FORM, IN WHOLE OR IN PART,
 * WITHOUT THE PRIOR WRITTEN PERMISSION OF HOWBUY INVESTMENT MANAGEMENT
 * CO., LTD.
 */
package com.howbuy.crm.account.service.message.dto;

import lombok.Data;

import java.io.Serializable;

/**
 * @description: (账户中心 一账通消息 [TOPIC_OPEN_ACC-一账通客户开户消息]-[ORDINARY_REGISTER-非实名开通一账通] 公共解析 dto)
 * <AUTHOR>
 * @date 2024年1月3日 16:04:03
 * @since JDK 1.8
 */
@Data
public class HboneRegisterMessageDTO implements Serializable {

    private static final long serialVersionUID = 1L;


    /**
     * 一账通账号(hboneNo)
     */
    private String hboneNo;


    /**
     * 分销机构号(disCode)
     */
    private String disCode;


    /**
     * 客户姓名(custName)
     */
    private String custName;


    /**
     * 用户登录名(username)
     */
    private String username;


    /*IP地址(ip)*/
    private String ip;

    /**
     * 注册网点号(regOutletCode)
     */
    private String regOutletCode;

    /**
     * 注册时间(regDate)
     */
    private String regDate;


    /**
     * 手机号摘要(mobileDigest)
     */
    private String mobileDigest;

    /**
     * 手机号掩码(mobileMask)
     */
    private String mobileMask;

    /**
     * 手机号密文(mobileCipher)
     */
    private String mobileCipher;

    /**
     手机验证状态(mobileVerifyStatus)
     */
    private String mobileVerifyStatus;



}