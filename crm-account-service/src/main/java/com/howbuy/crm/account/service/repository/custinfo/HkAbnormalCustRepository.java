/**
 * Copyright (c) 2023, <PERSON><PERSON><PERSON><PERSON> HOWBUY INVESTMENT MANAGEMENT Co., Ltd.
 * All right reserved.
 * <p>
 * THIS IS UNPUBLISHED PROPRIETARY SOURCE CODE OF HOWBUY INVESTMENT
 * MANAGEMENT CO., LTD.  THE CONTENTS OF THIS FILE MAY NOT BE DISCLOSED
 * TO THIRD PARTIES, COPIED OR DUPLICATED IN ANY FORM, IN WHOLE OR IN PART,
 * WITHOUT THE PRIOR WRITTEN PERMISSION OF HOWBUY INVESTMENT MANAGEMENT
 * CO., LTD.
 */
package com.howbuy.crm.account.service.repository.custinfo;

import com.github.pagehelper.Page;
import com.github.pagehelper.PageHelper;
import com.google.common.collect.Lists;
import com.howbuy.crm.account.client.enums.DealStatusEnum;
import com.howbuy.crm.account.client.enums.YesOrNoEnum;
import com.howbuy.crm.account.client.enums.custinfo.AbnormaSceneTypeEnum;
import com.howbuy.crm.account.client.response.Response;
import com.howbuy.crm.account.dao.bo.custinfo.CmConscustForAnalyseBO;
import com.howbuy.crm.account.dao.mapper.CommonMapper;
import com.howbuy.crm.account.dao.mapper.custinfo.CmHkAbnormalCustInfoMapper;
import com.howbuy.crm.account.dao.mapper.custinfo.CmHkAbnormalRelatedCustMapper;
import com.howbuy.crm.account.dao.mapper.customize.custinfo.HkAbnormalCustomizeMapper;
import com.howbuy.crm.account.dao.po.custinfo.CmHkAbnormalCustInfoPO;
import com.howbuy.crm.account.dao.po.custinfo.CmHkAbnormalRelatedCustPO;
import com.howbuy.crm.account.dao.req.custinfo.AbnormalCustReqVO;
import com.howbuy.crm.account.service.commom.constant.SequenceConstants;
import com.howbuy.crm.account.service.vo.custinfo.MessageCustInfoVO;
import org.apache.commons.collections.CollectionUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import org.springframework.transaction.annotation.Propagation;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.Assert;

import java.util.Date;
import java.util.List;

/**
 * @description: (香港异常客户 repository)
 * <AUTHOR>
 * @date 2023/12/8 15:31
 * @since JDK 1.8
 */
@Component
@Transactional(rollbackFor = Exception.class, propagation = Propagation.SUPPORTS)
public class HkAbnormalCustRepository {

    @Autowired
    private CmHkAbnormalCustInfoMapper hkAbnormalCustInfoMapper;

    @Autowired
    private CmHkAbnormalRelatedCustMapper hbAbnormalRelatedCustMapper;

    @Autowired
    private HkAbnormalCustomizeMapper hkAbnormalCustmozeMapper;

    @Autowired
    private CommonMapper commonMapper;


    /**
     * @description:(异常客户相关的 sequenceId  获取)
     * @param
     * @return java.lang.String
     * @author: haoran.zhang
     * @date: 2023/12/12 16:38
     * @since JDK 1.8
     */
    public String fetchSequenceId(){
        return commonMapper.getSeqValue(SequenceConstants.SEQ_CM_ABNORMAL_CUST);
    }




    /**
     * @description:(更新 异常客户处理状态)
     * @param id	
     * @param dealStatusEnum	
     * @param operator
     * @return com.howbuy.crm.account.client.response.Response<java.lang.String>
     * @author: haoran.zhang
     * @date: 2024/1/17 13:14
     * @since JDK 1.8
     */
    @Transactional(rollbackFor = Exception.class, propagation = Propagation.REQUIRED)
    public Response<String> updateDealStatus(String id,DealStatusEnum dealStatusEnum,String operator,String remark){
        Assert.notNull(id,"id 不能为空");
        Assert.notNull(dealStatusEnum,"dealStatusEnum 不能为空");
        Assert.notNull(operator,"operator 不能为空");
        CmHkAbnormalCustInfoPO updatePo=new CmHkAbnormalCustInfoPO();
        updatePo.setId(id);
        updatePo.setDealStatus(dealStatusEnum.getCode());
        updatePo.setDealTimestamp(new Date());
        updatePo.setDealOperator(operator);
        updatePo.setDealRemark(remark);
        hkAbnormalCustInfoMapper.updateByPrimaryKeySelective(updatePo);
        return Response.ok();
    }

    /**
     * @description:(批量 更新 异常客户处理状态)
     * @param idList
     * @param dealStatusEnum
     * @param operator
     * @param remark
     * @return com.howbuy.crm.account.client.response.Response<java.lang.String>
     * @author: haoran.zhang
     * @date: 2024/1/18 10:08
     * @since JDK 1.8
     */
    @Transactional(rollbackFor = Exception.class, propagation = Propagation.REQUIRED)
    public Response<String> batchUpdateDealStatus(List<String> idList,DealStatusEnum dealStatusEnum,String operator,String remark){
        Assert.notEmpty(idList,"id 不能为空");
        Assert.notNull(dealStatusEnum,"dealStatusEnum 不能为空");
        Assert.notNull(operator,"operator 不能为空");

        for(String id : idList){
            CmHkAbnormalCustInfoPO updatePo=new CmHkAbnormalCustInfoPO();
            updatePo.setId(id);
            updatePo.setDealStatus(dealStatusEnum.getCode());
            updatePo.setDealTimestamp(new Date());
            updatePo.setDealOperator(operator);
            updatePo.setDealRemark(remark);
            hkAbnormalCustInfoMapper.updateByPrimaryKeySelective(updatePo);
        }
        return Response.ok();
    }

    /**
     * @description:(根据ID 查询 异常客户信息)
     * @param id
     * @return com.howbuy.crm.account.dao.po.custinfo.CmHkAbnormalCustInfoPO
     * @author: haoran.zhang
     * @date: 2024/1/17 13:30
     * @since JDK 1.8
     */
    public CmHkAbnormalCustInfoPO selectById(String id){
        return hkAbnormalCustInfoMapper.selectByPrimaryKey(id);
    }


    /**
     * @description:(根据ID 获取 异常客户关联表信息)
     * @param id
     * @return com.howbuy.crm.account.dao.po.custinfo.CmHkAbnormalRelatedCustPO
     * @author: haoran.zhang
     * @date: 2024/1/17 13:30
     * @since JDK 1.8
     */
    public CmHkAbnormalRelatedCustPO selectSubTableBySubId(String id) {
        return hbAbnormalRelatedCustMapper.selectByPrimaryKey(id);
    }


    /**
     * @description:(根据异常分析结果， 插入 异常分析客户数据)
     * @param analyseVo
     * @param sceneTypeEnum
     * @param relatedCustList
     * @return void
     * @author: haoran.zhang
     * @date: 2024/1/8 10:08
     * @since JDK 1.8
     */
    @Transactional(rollbackFor = Exception.class, propagation = Propagation.REQUIRED)
    public  void insertAbnormalCust(MessageCustInfoVO analyseVo,
                                    AbnormaSceneTypeEnum sceneTypeEnum,
                                    List<CmConscustForAnalyseBO> relatedCustList){
        CmHkAbnormalCustInfoPO analysePo=new CmHkAbnormalCustInfoPO();
        BeanUtils.copyProperties(analyseVo,analysePo);
        //TODO: 业务逻辑是否判断 ：NOT NULL ?
        if (sceneTypeEnum != null) {
            analysePo.setAbnormalSceneType(sceneTypeEnum.getCode());
            analysePo.setAbnormalLevel(sceneTypeEnum.getLevel() == null ? null : sceneTypeEnum.getLevel().getCode());
        }
        //插入 异常客户信息数据
        String abnormalId=fetchSequenceId();
        String creator= analysePo.getCreator();
        analysePo.setId(abnormalId);
        analysePo.setCreateTimestamp(new Date());
        //处理状态
        analysePo.setDealStatus(DealStatusEnum.UN_DEAL.getCode());
        analysePo.setRecStat(YesOrNoEnum.YES.getCode());
        analysePo.setCreator(creator);
        hkAbnormalCustInfoMapper.insert(analysePo);

        //插入明细 关联异常客户数据
        if(CollectionUtils.isNotEmpty(relatedCustList)){
            List<CmHkAbnormalRelatedCustPO> detailList= Lists.newArrayList();
            relatedCustList.forEach(existCust->{
                CmHkAbnormalRelatedCustPO detailPO=new CmHkAbnormalRelatedCustPO();
                detailPO.setId(fetchSequenceId());
                detailPO.setAbnormalId(abnormalId);
                detailPO.setCustNo(existCust.getConscustno());
                detailPO.setCustName(existCust.getCustname());
                detailPO.setConsCode(existCust.getConsCode());
                detailPO.setHkTxAcctNo(existCust.getHkTxAcctNo());
                detailPO.setMobileAreaCode(existCust.getMobileAreaCode());
//                detailPO.setMobileCipher();
                detailPO.setMobileDigest(existCust.getMobileDigest());
                detailPO.setMobileMask(existCust.getMobileMask());
                detailPO.setCreateTimestamp(new Date());
                detailPO.setCreator(creator);
                detailPO.setIdType(existCust.getIdtype());
//                detailPO.setIdNoCipher();
                detailPO.setIdNoDigest(existCust.getIdnoDigest());
                detailPO.setIdNoMask(existCust.getIdnoMask());
                detailPO.setIdSignAreaCode(existCust.getIdSignAreaCode());
                detailPO.setInvestType(existCust.getInvsttype());
                detailPO.setHboneNo(existCust.getHboneNo());
                detailList.add(detailPO);
            });

            //插入明细数据表
            hbAbnormalRelatedCustMapper.batchInsert(detailList);
        }
    }

    public Page<CmHkAbnormalCustInfoPO> selectPageByVo(AbnormalCustReqVO vo){
        PageHelper.startPage(vo.getPage(),vo.getRows());
        return hkAbnormalCustmozeMapper.selectPageByVo(vo);
    }


    /**
     * @description: (查询 关联的客户信息列表)
     * @param abnormalIdList 异常主表id 列表
     * @return java.util.List<com.howbuy.crm.account.dao.po.custinfo.CmHkAbnormalCustInfoPO>
     */
    public List<CmHkAbnormalRelatedCustPO> selectRelatedListByMainIdList(List<List<String>> abnormalIdList){
        if(CollectionUtils.isEmpty(abnormalIdList)){
            return Lists.newArrayList();
        }
        return hkAbnormalCustmozeMapper.selectRelatedListByMainIdList(abnormalIdList);
    }


    /**
     * @description: (查询 关联的客户信息列表)
     * @param detailIdList 异常明细表表id 列表
     * @return java.util.List<com.howbuy.crm.account.dao.po.custinfo.CmHkAbnormalCustInfoPO>
     */
    public List<CmHkAbnormalRelatedCustPO> selectRelatedListByDetailList(List<List<String>> detailIdList){
        if(CollectionUtils.isEmpty(detailIdList)){
            return Lists.newArrayList();
        }
        return hkAbnormalCustmozeMapper.selectRelatedListByDetailList(detailIdList);
    }


}