/**
 * Copyright (c) 2023, <PERSON><PERSON><PERSON><PERSON> HOWBUY INVESTMENT MANAGEMENT Co., Ltd.
 * All right reserved.
 * <p>
 * THIS IS UNPUBLISHED PROPRIETARY SOURCE CODE OF HOWBUY INVESTMENT
 * MANAGEMENT CO., LTD.  THE CONTENTS OF THIS FILE MAY NOT BE DISCLOSED
 * TO THIRD PARTIES, COPIED OR DUPLICATED IN ANY FORM, IN WHOLE OR IN PART,
 * WITHOUT THE PRIOR WRITTEN PERMISSION OF HOWBUY INVESTMENT MANAGEMENT
 * CO., LTD.
 */
package com.howbuy.crm.account.service.service.custinfo;

import com.google.common.collect.Maps;
import com.howbuy.acc.common.utils.DigestUtil;
import com.howbuy.acc.common.utils.MaskUtil;
import com.howbuy.acccenter.facade.common.AccBaseResponse;
import com.howbuy.acccenter.facade.query.querycustdeliveryaddr.CustDeliveryAddrInfoBean;
import com.howbuy.acccenter.facade.query.querycustdeliveryaddr.QueryCustDeliveryAddrFacade;
import com.howbuy.acccenter.facade.query.querycustdeliveryaddr.QueryCustDeliveryAddrRequest;
import com.howbuy.acccenter.facade.query.querycustdeliveryaddr.QueryCustDeliveryAddrResponse;
import com.howbuy.acccenter.facade.query.sensitive.custdeliveryaddr.CustDeliveryAddrSensitiveInfo;
import com.howbuy.acccenter.facade.query.sensitive.custdeliveryaddr.QueryCustDeliveryAddrSensitiveInfoFacade;
import com.howbuy.acccenter.facade.query.sensitive.custdeliveryaddr.QueryCustDeliveryAddrSensitiveInfoRequest;
import com.howbuy.acccenter.facade.query.sensitive.custdeliveryaddr.QueryCustDeliveryAddrSensitiveInfoResponse;
import com.howbuy.crm.account.client.request.custinfo.ConsCustDeliveryAddressRequest;
import com.howbuy.crm.account.client.response.Response;
import com.howbuy.crm.account.client.response.custinfo.CmConsCustVO;
import com.howbuy.crm.account.client.response.custinfo.CmConscustDeliveryAddressSensitiveInfoVO;
import com.howbuy.crm.account.client.response.custinfo.CmConscustDeliveryAddressVO;
import com.howbuy.crm.account.client.response.custinfo.HboneDeliveryAddrInfoVO;
import com.howbuy.crm.account.client.response.dictionary.ProvCityCountyNameVO;
import com.howbuy.crm.account.dao.po.custinfo.CmConscustDeliveryAddressPO;
import com.howbuy.crm.account.service.commom.constant.Constants;
import com.howbuy.crm.account.service.commom.constant.DobboReferenceConstant;
import com.howbuy.crm.account.service.outerservice.auth.EncyptAndDecyptOuterService;
import com.howbuy.crm.account.service.repository.custinfo.ConsCustDeliveryAddressRepository;
import com.howbuy.crm.account.service.service.dictionary.DictionaryService;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.dubbo.config.annotation.DubboReference;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.Objects;

/**
 * @description: (crm客户收货地址信息service)
 * <AUTHOR>
 * @date 2023/12/13 16:41
 * @since JDK 1.8
 */
@Service
@Slf4j
public class CustDeliveryAddressService {

    @DubboReference(registry = DobboReferenceConstant.ACC_CENTER_SERVER, check = false)
    private QueryCustDeliveryAddrFacade queryCustDeliveryAddrFacade;

    @DubboReference(registry = DobboReferenceConstant.ACC_CENTER_SERVER, check = false)
    private QueryCustDeliveryAddrSensitiveInfoFacade queryCustDeliveryAddrSensitiveInfoFacade;

    @Autowired
    private ConsCustInfoService consCustInfoService;

    @Autowired
    private DictionaryService dictionaryService;

    @Autowired
    private ConsCustDeliveryAddressRepository consCustDeliveryAddressRepository;


    @Autowired
    private EncyptAndDecyptOuterService encyptAndDecyptOuterService;

    /**
     * @description: 投顾填写的收货地址信息 和 客户收货地址，比对是否一致
     * @param request 请求参数
     * @return com.howbuy.crm.account.client.response.Response<java.lang.String> 返回结果
     * @author: jin.wang03
     * @date: 2024/4/3 15:00
     * @since JDK 1.8
     */
    public Response<String> compareHboneReceiver(ConsCustDeliveryAddressRequest request) {
        // 字段是否赋值校验
        if (validateIncomplete(request)) {
            return Response.fail("待比对的收货信息不完善！");
        }

        CmConsCustVO cmConsCustVO = consCustInfoService.queryCustInfoByCustNo(request.getCustNo());
        if (Objects.isNull(cmConsCustVO)) {
            return Response.fail("查询的投顾客户信息为空！");
        }
        // 投顾客户如果未绑定一账通号，则不进行比对
        if (StringUtils.isBlank(cmConsCustVO.getHboneNo())) {
            return Response.ok();
        }
        return compareResult(request, cmConsCustVO);
    }

    /**
     * @description: 校验[收货地址]数据完整性
     * 校验规则：
     *       1、投顾客户号、姓名、手机区号、手机号、国家/地区、收货详细地址 必填
     *       2、nationCode=CN，则省市县code必填，否则省市县code不填
     * @param request
     * @return boolean
     * @author: jin.wang03
     * @date: 2024/9/11 18:32
     * @since JDK 1.8
     */
    private boolean validateIncomplete(ConsCustDeliveryAddressRequest request) {
        // 投顾客户号、姓名、手机区号、手机号、国家/地区、收货详细地址 必填
        if (StringUtils.isBlank(request.getCustNo()) || StringUtils.isBlank(request.getDeliveryName())
                || StringUtils.isBlank(request.getDeliveryMobile()) || StringUtils.isBlank(request.getDeliveryAddr())
                || StringUtils.isBlank(request.getDeliveryMobileAreaCode())
                || StringUtils.isBlank(request.getDeliveryNationCode())) {
            return true;
        }

        // nationCode=CN，则省市县code必填，否则省市县code不填
        if (StringUtils.equals(request.getDeliveryNationCode(), Constants.NATION_CODE_CN)) {
            return StringUtils.isBlank(request.getDeliveryProvCode())
                    || StringUtils.isBlank(request.getDeliveryCityCode())
                    || StringUtils.isBlank(request.getDeliveryCountyCode());
        }

        return false;
    }

    /**
     * @description: 投顾填写的收货地址信息 和 客户收货地址，比对是否一致
     * @param request 请求参数
     * @param cmConsCustVO 投顾客户信息
     * @return com.howbuy.crm.account.client.response.Response<java.lang.String> 返回结果
     * @author: jin.wang03
     * @date: 2024/4/8 13:38
     * @since JDK 1.8
     */
    private Response<String> compareResult(ConsCustDeliveryAddressRequest request, CmConsCustVO cmConsCustVO) {
        QueryCustDeliveryAddrRequest queryCustDeliveryAddrRequest = new QueryCustDeliveryAddrRequest();
        queryCustDeliveryAddrRequest.setHboneNo(cmConsCustVO.getHboneNo());
        QueryCustDeliveryAddrResponse response = queryCustDeliveryAddrFacade.execute(queryCustDeliveryAddrRequest);
        if (isAcctSuccess(response) && CollectionUtils.isNotEmpty(response.getDeliveryAddrInfoList())) {
            // ！！！注意：当前，客户只能输入一个收货地址，默认和index=0的数据比较即可 2024-04-03
            CustDeliveryAddrInfoBean custDeliveryAddrInfoBean = response.getDeliveryAddrInfoList().get(0);
            if (compareWithAcctDelivery(request, custDeliveryAddrInfoBean)) {
                return Response.ok();
            } else {
                String deliveryAddr = "";
                CustDeliveryAddrSensitiveInfo sensitiveInfo = getHboneDeliverySensitiveInfo(cmConsCustVO.getHboneNo(), custDeliveryAddrInfoBean.getAddrId());
                if (sensitiveInfo != null) {
                    deliveryAddr = sensitiveInfo.getDeliveryAddr();
                }
                // 翻译省市县名称
                ProvCityCountyNameVO cityNameVo = dictionaryService.getNamesByCodes(custDeliveryAddrInfoBean.getDeliveryProvCode(),
                        custDeliveryAddrInfoBean.getDeliveryCityCode(), custDeliveryAddrInfoBean.getDeliveryCountyCode());
                // 翻译国家/地区
                String nationName = dictionaryService.getCountryNameByCode(custDeliveryAddrInfoBean.getDeliveryCountryCode());

                return Response.fail(custDeliveryAddrInfoBean.getDeliveryName() + " "
                        + custDeliveryAddrInfoBean.getDeliveryMobileAreaCode() + "-" + custDeliveryAddrInfoBean.getDeliveryMobileMask() + " "
                        + nationName + " "
                        + cityNameVo.getProvName() + cityNameVo.getCityName() + cityNameVo.getCountyName() + deliveryAddr);
            }
        }
        return Response.ok();
    }

    /**
     * @description: 比对投顾填写的收货地址信息 和 客户APP填写的收货地址
     *  校验规则：
     *      1、姓名、手机区号、手机号、国家/地区、收货详细地址 是否一致，若不一致，则返回false
     *      2、若一致，则判断 国家/地区 是否为 中国内地，
     *          如果不是中国内地，则返回true
     *          3、如果是中国内地，则比较 省市县是否一致
     * @param request
     * @param custDeliveryAddrInfoBean
     * @return boolean
     * @author: jin.wang03
     * @date: 2024/9/12 15:29
     * @since JDK 1.8
     */
    private boolean compareWithAcctDelivery(ConsCustDeliveryAddressRequest request, CustDeliveryAddrInfoBean custDeliveryAddrInfoBean) {

        if (StringUtils.equals(custDeliveryAddrInfoBean.getDeliveryName(), request.getDeliveryName())
                && StringUtils.equals(custDeliveryAddrInfoBean.getDeliveryMobileDigest(), DigestUtil.digest(request.getDeliveryMobile()))
                && StringUtils.equals(custDeliveryAddrInfoBean.getDeliveryAddrDigest(), DigestUtil.digest(request.getDeliveryAddr()))
                && StringUtils.equals(custDeliveryAddrInfoBean.getDeliveryMobileAreaCode(), request.getDeliveryMobileAreaCode())
                && StringUtils.equals(custDeliveryAddrInfoBean.getDeliveryCountryCode(), request.getDeliveryNationCode())) {

            if (Constants.NATION_CODE_CN.equals(custDeliveryAddrInfoBean.getDeliveryCountryCode())) {
                return StringUtils.equals(custDeliveryAddrInfoBean.getDeliveryProvCode(), request.getDeliveryProvCode())
                        && StringUtils.equals(custDeliveryAddrInfoBean.getDeliveryCityCode(), request.getDeliveryCityCode())
                        && StringUtils.equals(custDeliveryAddrInfoBean.getDeliveryCountyCode(), request.getDeliveryCountyCode());
            }
            return true;
        }
        return false;
    }



    /**
     * @description:(根据一账通号、收货地址id 获取账号中心的客户收货地址敏感信息)
     * @param hboneNo	
     * @param addrId
     * @return com.howbuy.acccenter.facade.query.sensitive.custdeliveryaddr.CustDeliveryAddrSensitiveInfo
     * @author: haoran.zhang
     * @date: 2024/8/19 14:19
     * @since JDK 1.8
     */
    private CustDeliveryAddrSensitiveInfo getHboneDeliverySensitiveInfo(String hboneNo,String addrId){
        if(StringUtils.isBlank(addrId)){
            return null;
        }
        Map<String,CustDeliveryAddrSensitiveInfo> sensitiveInfoMap= getHboneDeliverySensitiveMap(hboneNo);
        return sensitiveInfoMap.get(addrId);
    }



    /**
     * @description:(根据一账通号获取账号中心的客户收货地址敏感信息Map)
     * @param hboneNo
     * @return java.util.Map<java.lang.String,com.howbuy.acccenter.facade.query.sensitive.custdeliveryaddr.CustDeliveryAddrSensitiveInfo>
     * @author: haoran.zhang
     * @date: 2024/8/19 14:07
     * @since JDK 1.8
     */
    private Map<String,CustDeliveryAddrSensitiveInfo>  getHboneDeliverySensitiveMap(String hboneNo){
        Map<String,CustDeliveryAddrSensitiveInfo> sensitiveInfoMap= Maps.newHashMap();
        QueryCustDeliveryAddrSensitiveInfoRequest sensitiveInfoRequest = new QueryCustDeliveryAddrSensitiveInfoRequest();
        sensitiveInfoRequest.setHboneNo(hboneNo);
        QueryCustDeliveryAddrSensitiveInfoResponse sensitiveInfoResponse = queryCustDeliveryAddrSensitiveInfoFacade.execute(sensitiveInfoRequest);
        if (isAcctSuccess(sensitiveInfoResponse) && CollectionUtils.isNotEmpty(sensitiveInfoResponse.getDeliveryAddrSensitiveInfoList())) {
            sensitiveInfoResponse.getDeliveryAddrSensitiveInfoList().forEach(sensitiveInfo -> {
                sensitiveInfoMap.put(sensitiveInfo.getAddrId(), sensitiveInfo);
            });
        }
        return sensitiveInfoMap;
        
    }

    /**
     * @description: 根据一账通号获取账号中心的客户收货地址列表
     * @param hboneNo 一账通号
     * @return com.howbuy.crm.account.client.response.Response<java.util.List<com.howbuy.crm.account.client.response.custinfo.HboneDeliveryAddrInfoVO>> 账号中心的客户收货地址列表
     * @author: jin.wang03
     * @date: 2024/4/7 14:13
     * @since JDK 1.8
     */
    public Response<List<HboneDeliveryAddrInfoVO>> getHboneDeliveryAddrByHboneNo(String hboneNo) {
        if (StringUtils.isBlank(hboneNo)) {
            return Response.fail("查询的一账通号不能为空");
        }

        List<HboneDeliveryAddrInfoVO> resultList = new ArrayList<>();

        QueryCustDeliveryAddrRequest queryCustDeliveryAddrRequest = new QueryCustDeliveryAddrRequest();
        queryCustDeliveryAddrRequest.setHboneNo(hboneNo);
        QueryCustDeliveryAddrResponse response = queryCustDeliveryAddrFacade.execute(queryCustDeliveryAddrRequest);
        if (isAcctSuccess(response) && CollectionUtils.isNotEmpty(response.getDeliveryAddrInfoList())) {
            for (CustDeliveryAddrInfoBean custDeliveryAddrInfoBean : response.getDeliveryAddrInfoList()) {
                HboneDeliveryAddrInfoVO hboneDeliveryAddrInfoVO = new HboneDeliveryAddrInfoVO();
                BeanUtils.copyProperties(custDeliveryAddrInfoBean, hboneDeliveryAddrInfoVO);

                // 翻译省市县名称
                ProvCityCountyNameVO cityNameVo = dictionaryService.getNamesByCodes(custDeliveryAddrInfoBean.getDeliveryProvCode(),
                        custDeliveryAddrInfoBean.getDeliveryCityCode(), custDeliveryAddrInfoBean.getDeliveryCountyCode());
                hboneDeliveryAddrInfoVO.setDeliveryProvName(cityNameVo.getProvName());
                hboneDeliveryAddrInfoVO.setDeliveryCityName(cityNameVo.getCityName());
                hboneDeliveryAddrInfoVO.setDeliveryCountyName(cityNameVo.getCountyName());
                // 翻译国家名称
                hboneDeliveryAddrInfoVO.setDeliveryCountryName(dictionaryService.getCountryNameByCode(custDeliveryAddrInfoBean.getDeliveryCountryCode()));

                resultList.add(hboneDeliveryAddrInfoVO);
            }
        }
        //为resultList 赋值 cipher信息
        Map<String,CustDeliveryAddrSensitiveInfo>  sensitiveMap=getHboneDeliverySensitiveMap(hboneNo);
        resultList.forEach(resultInfo->{
            String addrId=resultInfo.getAddrId();
            if(StringUtils.isNotEmpty(addrId) &&  sensitiveMap.containsKey(addrId) ){
                //地址 加密信息
                CustDeliveryAddrSensitiveInfo sensitiveInfo=sensitiveMap.get(addrId);
                if(StringUtils.isNotEmpty(sensitiveInfo.getDeliveryAddr())){
                    resultInfo.setDeliveryAddrCipher(getEncryptValue(sensitiveInfo.getDeliveryAddr()));
                }
                //mobile 加密信息
                if(StringUtils.isNotEmpty(sensitiveInfo.getDeliveryMobile())){
                    resultInfo.setDeliveryMobileCipher(getEncryptValue(sensitiveInfo.getDeliveryMobile()));
                }

            }
        });

        return Response.ok(resultList);
    }

    /**
     * @description: 根据投顾客户号查询客户收货地址信息
     * @param custNo 投顾客户号
     * @return com.howbuy.crm.account.client.response.Response<java.util.List<com.howbuy.crm.account.client.response.custinfo.CmConscustDeliveryAddressVO>> 客户收货地址信息列表
     * @author: jin.wang03
     * @date: 2024/4/7 16:21
     * @since JDK 1.8
     */
    public Response<List<CmConscustDeliveryAddressVO>> getCustDeliveryAddrByCustNo(String custNo) {
        List<CmConscustDeliveryAddressVO> resultList = new ArrayList<>();
        List<CmConscustDeliveryAddressPO> cmConscustDeliveryAddressPOS = consCustDeliveryAddressRepository.listCustDeliveryAddressByCustNo(custNo);

        if (CollectionUtils.isNotEmpty(cmConscustDeliveryAddressPOS)) {
            for (CmConscustDeliveryAddressPO cmConscustDeliveryAddressPO : cmConscustDeliveryAddressPOS) {
                CmConscustDeliveryAddressVO cmConscustDeliveryAddressVO = new CmConscustDeliveryAddressVO();
                BeanUtils.copyProperties(cmConscustDeliveryAddressPO, cmConscustDeliveryAddressVO);
                // 省市县转义
                ProvCityCountyNameVO cityNameVo = dictionaryService.getNamesByCodes(cmConscustDeliveryAddressPO.getProvCode(),
                        cmConscustDeliveryAddressPO.getCityCode(), cmConscustDeliveryAddressPO.getCountyCode());
                cmConscustDeliveryAddressVO.setProvName(cityNameVo.getProvName());
                cmConscustDeliveryAddressVO.setCityName(cityNameVo.getCityName());
                cmConscustDeliveryAddressVO.setCountyName(cityNameVo.getCountyName());
                // 国家/地区转义
                cmConscustDeliveryAddressVO.setNationName(dictionaryService.getCountryNameByCode(cmConscustDeliveryAddressPO.getNationCode()));

                resultList.add(cmConscustDeliveryAddressVO);
            }
        }
        return Response.ok(resultList);
    }

    /**
     * @description: 根据投顾客户号查询客户收货地址信息（敏感信息）
     * @param custNo 投顾客户号
     * @return com.howbuy.crm.account.client.response.Response<java.util.List<com.howbuy.crm.account.client.response.custinfo.CmConscustDeliveryAddressSensitiveInfoVO>> 客户收货地址信息列表（敏感信息）
     * @author: jin.wang03
     * @date: 2024/4/7 18:26
     * @since JDK 1.8
     */
    public Response<List<CmConscustDeliveryAddressSensitiveInfoVO>> getCustDeliveryAddrSensitiveInfoByCustNo(String custNo) {
        List<CmConscustDeliveryAddressSensitiveInfoVO> resultList = new ArrayList<>();
        List<CmConscustDeliveryAddressPO> cmConscustDeliveryAddressPOS = consCustDeliveryAddressRepository.listCustDeliveryAddressByCustNo(custNo);

        if (CollectionUtils.isNotEmpty(cmConscustDeliveryAddressPOS)) {
            for (CmConscustDeliveryAddressPO cmConscustDeliveryAddressPO : cmConscustDeliveryAddressPOS) {
                CmConscustDeliveryAddressSensitiveInfoVO cmConscustDeliveryAddressVO = new CmConscustDeliveryAddressSensitiveInfoVO();
                cmConscustDeliveryAddressVO.setId(cmConscustDeliveryAddressPO.getId());
                cmConscustDeliveryAddressVO.setConscustno(cmConscustDeliveryAddressPO.getConscustno());

                // 手机号、地址解密
                cmConscustDeliveryAddressVO.setMobile(getDecryptValue(cmConscustDeliveryAddressPO.getMobileCipher()));
                cmConscustDeliveryAddressVO.setAddr(getDecryptValue(cmConscustDeliveryAddressPO.getAddrCipher()));

                resultList.add(cmConscustDeliveryAddressVO);
            }
        }
        return Response.ok(resultList);
    }

    /**
     * @description: 保存投顾填写的收货地址信息
     * @param request 请求参数
     * @return com.howbuy.crm.account.client.response.Response<java.lang.String>
     * @author: jin.wang03
     * @date: 2024/4/3 17:34
     * @since JDK 1.8
     */
    public Response<String> saveDeliveryData(ConsCustDeliveryAddressRequest request) {
        if (validateIncomplete(request)) {
            return Response.fail("收货信息不完善！");
        }

        CmConscustDeliveryAddressPO addressPo = new CmConscustDeliveryAddressPO();
        addressPo.setConscustno(request.getCustNo());
        addressPo.setReceiverName(request.getDeliveryName());

        addressPo.setMobileAreaCode(request.getDeliveryMobileAreaCode());
        addressPo.setMobileDigest(DigestUtil.digest(request.getDeliveryMobile()));
        addressPo.setMobileMask(MaskUtil.maskMobile(request.getDeliveryMobile()));
        addressPo.setMobileCipher(getEncryptValue(request.getDeliveryMobile()));

        addressPo.setNationCode(request.getDeliveryNationCode());
        addressPo.setProvCode(request.getDeliveryProvCode());
        addressPo.setCityCode(request.getDeliveryCityCode());
        addressPo.setCountyCode(request.getDeliveryCountyCode());

        addressPo.setAddrDigest(DigestUtil.digest(request.getDeliveryAddr()));
        addressPo.setAddrMask(MaskUtil.maskAddr(request.getDeliveryAddr()));
        addressPo.setAddrCipher(getEncryptValue(request.getDeliveryAddr()));

        addressPo.setRemark(request.getDeliveryRemark());
        addressPo.setCreator(request.getOperator());

        consCustDeliveryAddressRepository.insertCustDeliveryAddress(addressPo);
        return Response.ok();
    }


    /**
     * @description: 更新投顾填写的收货地址信息
     * @param request 请求参数
     * @return com.howbuy.crm.account.client.response.Response<java.lang.String> 返回结果
     * @author: jin.wang03
     * @date: 2024/4/8 9:45
     * @since JDK 1.8
     */
    public Response<String> updateDeliveryData(ConsCustDeliveryAddressRequest request) {
        if (Objects.isNull(request.getId())) {
            return Response.fail("收货信息ID不能为空！");
        }

        if (validateIncomplete(request)) {
            return Response.fail("收货信息不完善！");
        }

        CmConscustDeliveryAddressPO addressPo = new CmConscustDeliveryAddressPO();
        addressPo.setId(request.getId());
        addressPo.setConscustno(request.getCustNo());
        addressPo.setReceiverName(request.getDeliveryName());

        addressPo.setMobileAreaCode(request.getDeliveryMobileAreaCode());
        addressPo.setMobileDigest(DigestUtil.digest(request.getDeliveryMobile()));
        addressPo.setMobileMask(MaskUtil.maskMobile(request.getDeliveryMobile()));
        addressPo.setMobileCipher(getEncryptValue(request.getDeliveryMobile()));

        addressPo.setNationCode(request.getDeliveryNationCode());
        addressPo.setProvCode(request.getDeliveryProvCode());
        addressPo.setCityCode(request.getDeliveryCityCode());
        addressPo.setCountyCode(request.getDeliveryCountyCode());

        addressPo.setAddrDigest(DigestUtil.digest(request.getDeliveryAddr()));
        addressPo.setAddrMask(MaskUtil.maskAddr(request.getDeliveryAddr()));
        addressPo.setAddrCipher(getEncryptValue(request.getDeliveryAddr()));

        addressPo.setRemark(request.getDeliveryRemark());

        addressPo.setModifier(request.getOperator());

        consCustDeliveryAddressRepository.updateCustDeliveryAddress(addressPo);
        return Response.ok();
    }


    /**
     * @description: 删除投顾填写的收货地址信息
     * @param request 请求参数
     * @return com.howbuy.crm.account.client.response.Response<java.lang.String> 返回结果
     * @author: jin.wang03
     * @date: 2024/4/8 11:11
     * @since JDK 1.8
     */
    public Response<String> deleteDeliveryData(ConsCustDeliveryAddressRequest request) {
        CmConscustDeliveryAddressPO addressPo = new CmConscustDeliveryAddressPO();
        addressPo.setId(request.getId());
        addressPo.setModifier(request.getOperator());

        consCustDeliveryAddressRepository.deleteDeliveryData(addressPo);
        return Response.ok();
    }


    /**
     * @description: 账户中心response返回接口 是否成功，统一判断逻辑
     * @param resp
     * @return boolean
     * @author: jin.wang03
     * @date: 2024/4/3 17:48
     * @since JDK 1.8
     */
    private static boolean isAcctSuccess(AccBaseResponse resp) {
        return resp != null && Constants.ACCT_CENTER_SUCCESS_CODE.equals(resp.getReturnCode());
    }


    /**
     * @description: 加密
     * @param value 待加密的值
     * @return java.lang.String 加密后的值
     * @author: jin.wang03
     * @date: 2024/4/3 17:48
     * @since JDK 1.8
     */
    private String getEncryptValue(String value){
        return  encyptAndDecyptOuterService.encrypt(value);
    }

    /**
     * @description: 解密
     * @param value 待解密的值
     * @return java.lang.String 解密后的值
     * @author: jin.wang03
     * @date: 2024/4/7 17:48
     * @since JDK 1.8
     */
    private String getDecryptValue(String value){
        return  encyptAndDecyptOuterService.decrypt(value);
    }

}