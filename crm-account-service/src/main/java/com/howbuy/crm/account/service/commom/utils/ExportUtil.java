/**
 * Copyright (c) 2024, <PERSON><PERSON><PERSON><PERSON> HOWBUY INVESTMENT MANAGEMENT Co., Ltd.
 * All right reserved.
 * <p>
 * THIS IS UNPUBLISHED PROPRIETARY SOURCE CODE OF HOWBUY INVESTMENT
 * MANAGEMENT CO., LTD.  THE CONTENTS OF THIS FILE MAY NOT BE DISCLOSED
 * TO THIRD PARTIES, COPIED OR DUPLICATED IN ANY FORM, IN WHOLE OR IN PART,
 * WITHOUT THE PRIOR WRITTEN PERMISSION OF HOWBUY INVESTMENT MANAGEMENT
 * CO., LTD.
 */
package com.howbuy.crm.account.service.commom.utils;

import cn.hutool.core.bean.BeanUtil;
import com.alibaba.excel.EasyExcel;
import com.alibaba.excel.ExcelWriter;
import com.alibaba.excel.write.metadata.WriteSheet;
import com.alibaba.excel.write.metadata.style.WriteCellStyle;
import com.alibaba.excel.write.metadata.style.WriteFont;
import com.alibaba.excel.write.style.HorizontalCellStyleStrategy;
import com.howbuy.crm.account.client.response.ExportToFileVO;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.codec.binary.Base64;
import org.apache.poi.ss.usermodel.FillPatternType;
import org.apache.poi.ss.usermodel.IndexedColors;
import org.springframework.util.CollectionUtils;

import java.io.ByteArrayOutputStream;
import java.io.IOException;
import java.util.ArrayList;
import java.util.Collections;
import java.util.List;
import java.util.stream.Collectors;

/**
 * @description: (导出功能)
 * <AUTHOR>
 * @date 2024/10/29 17:11
 * @since JDK 1.8
 */
@Slf4j
public class ExportUtil {
    private ExportUtil() {
        throw new IllegalStateException("Utility class");
    }
    /**
     * 导出通用方法
     * @param results 需要导出结果
     * @param menuName 导出菜单名称
     * @param className 转换成导出VO类
     * @param <T>
     * @param <V>
     * @return
     */
    public static <T,V> ExportToFileVO export(List<T> results, String menuName, Class<V> className) {
        List<V> execlVOList = new ArrayList<>();
        if(!CollectionUtils.isEmpty(results)){
            execlVOList = copyPropertiesByList(results, className);
        }
        String base64Str = ExportUtil.exportByEasyExcelByColumnWidth(execlVOList, className, menuName);
        ExportToFileVO exportToFileVO = new ExportToFileVO();
        exportToFileVO.setName(menuName);
        exportToFileVO.setFileByte(base64Str);
        return exportToFileVO;
    }

    /**
     * 集合复制
     * @param sourceList 来源集合
     * @param targetClazz 目标类类型
     * @param <T>
     * @param <V>
     * @return
     */
    public static <T,V> List<V> copyPropertiesByList(List<T> sourceList, Class<V> targetClazz) {
        if (CollectionUtils.isEmpty(sourceList)) {
            return Collections.emptyList();
        }
        return sourceList.stream().map(s -> BeanUtil.copyProperties(s, targetClazz)).collect(Collectors.toList());
    }

    /**
     * @description 导出excel
     * @param list	要导出的数据行
     * @param clazz	类
     * @param sheetName	sheet名
     * @return java.lang.String
     * @author: shijie.wang
     * @date: 2024/10/29 17:11
     * @since JDK 1.8
     */
    public static <T> String exportByEasyExcelByColumnWidth(List<T> list, Class clazz, String sheetName) {
        try (ByteArrayOutputStream outputStream = new ByteArrayOutputStream()){
            ExcelWriter excelWriter = EasyExcel.write(outputStream, clazz).build();
            //这个策略是 头是头的样式 内容是内容的样式 其他的策略可以自己实现             ;
            WriteSheet writeSheet = EasyExcel.writerSheet(sheetName).
                    registerWriteHandler(new HorizontalCellStyleStrategy(getHeadStyle(), getContentStyle())).build();
            excelWriter.write(list, writeSheet);
            excelWriter.finish();
            outputStream.flush();
            return Base64.encodeBase64String(outputStream.toByteArray());
        }catch (IOException e){
            log.error("导出excel异常", e);
        }
        return null;
    }

    /**
     * 设置头部样式
     * @return
     */
    public static WriteCellStyle getHeadStyle(){
        // 头的策略
        WriteCellStyle headWriteCellStyle = new WriteCellStyle();
        //设置自动换行
        headWriteCellStyle.setWrapped(false);
        headWriteCellStyle.setFillPatternType(FillPatternType.NO_FILL);
        headWriteCellStyle.setFillBackgroundColor(IndexedColors.WHITE.getIndex());
        WriteFont headFont = new WriteFont();
        headFont.setFontName("宋体");
        headFont.setFontHeightInPoints((short)11);
        headFont.setBold(true);
        headWriteCellStyle.setWriteFont(headFont);
        return headWriteCellStyle;
    }

    /**
     * 设置内容样式
     * @return
     */
    public static WriteCellStyle getContentStyle(){
        // 内容的策略
        WriteCellStyle contentWriteCellStyle = new WriteCellStyle();
        //设置自动换行
        contentWriteCellStyle.setWrapped(false);
        return contentWriteCellStyle;
    }
}