/**
 * Copyright (c) 2023, <PERSON><PERSON><PERSON><PERSON> HOWBUY INVESTMENT MANAGEMENT Co., Ltd.
 * All right reserved.
 * <p>
 * THIS IS UNPUBLISHED PROPRIETARY SOURCE CODE OF HOWBUY INVESTMENT
 * MANAGEMENT CO., LTD.  THE CONTENTS OF THIS FILE MAY NOT BE DISCLOSED
 * TO THIRD PARTIES, COPIED OR DUPLICATED IN ANY FORM, IN WHOLE OR IN PART,
 * WITHOUT THE PRIOR WRITTEN PERMISSION OF HOWBUY INVESTMENT MANAGEMENT
 * CO., LTD.
 */
package com.howbuy.crm.account.service.service.custinfo;

import com.howbuy.crm.account.client.request.custinfo.HkAcctOptDealRequest;
import com.howbuy.crm.account.client.request.custinfo.HkPiggyAgreementDealRequest;
import com.howbuy.crm.account.client.response.PageVO;
import com.howbuy.crm.account.client.response.Response;
import com.howbuy.crm.account.client.response.custinfo.HkAcctOptDealVO;
import com.howbuy.crm.account.client.response.custinfo.HkCustPiggyAgreementVO;
import com.howbuy.crm.account.client.response.custinfo.HkPiggyAgreementDealVO;
import com.howbuy.crm.account.dao.po.custinfo.CmHkConscustPO;
import com.howbuy.crm.account.dao.req.custinfo.CmHkCustReqVO;
import com.howbuy.crm.account.service.outerservice.hkacct.HkCustRelatedOuterService;
import com.howbuy.crm.account.service.repository.custinfo.CmHkCustInfoRepository;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * @description: (香港客户 [交易相关属性]  信息service)
 * <AUTHOR>
 * @date 2024年7月24日 09:58:23
 * @since JDK 1.8
 */
@Service
@Slf4j
public class HkCustRelatedAttributeService {


    @Autowired
    private HkCustRelatedOuterService hkCustRelatedOuterService;

    @Autowired
    private CmHkCustInfoRepository hkCustRepository;


    /**
     * @description:(根据 香港交易账号 查询 海外储蓄罐协议签署信息)
     * @param hkTxAcctNo
     * @return void
     * @author: haoran.zhang
     * @date: 2024/7/24 13:34
     * @since JDK 1.8
     */
    public HkCustPiggyAgreementVO getPiggyAgreementByHkTxAcctNo(String hkTxAcctNo) {
        return hkCustRelatedOuterService.getCustPiggyAgreement(hkTxAcctNo);
    }


    /**
     * @description:(根据 crm客户号 查询 海外储蓄罐协议签署信息)
     * @param custNo
     * @return com.howbuy.crm.account.client.response.Response<com.howbuy.crm.account.client.response.custinfo.HkCustPiggyAgreementVO>
     * @author: haoran.zhang
     * @date: 2024/7/24 14:22
     * @since JDK 1.8
     */
    public Response<HkCustPiggyAgreementVO> getPiggyAgreementByCustNo(String custNo) {
        CmHkCustReqVO reqVO=new CmHkCustReqVO();
        reqVO.setConscustno(custNo);
        CmHkConscustPO hkCustPo=hkCustRepository.selectByReqVO(reqVO);
        if(hkCustPo==null){
            return Response.fail("未绑定香港客户信息!");
        }
        HkCustPiggyAgreementVO agreementVO= getPiggyAgreementByHkTxAcctNo(hkCustPo.getHkTxAcctNo());
        return Response.ok(agreementVO);
    }


    /**
     * @description:(查询客户海外储蓄罐协议流水信息 )
     * @param queryReq
     * @return java.util.List<com.howbuy.crm.account.client.response.custinfo.HkPiggyAgreementDealVO>
     * @author: haoran.zhang
     * @date: 2024/8/12 15:41
     * @since JDK 1.8
     */
    public PageVO<HkPiggyAgreementDealVO> getPiggyAgreementDeal(HkPiggyAgreementDealRequest queryReq){
        return hkCustRelatedOuterService.getPiggyAgreementDeal(queryReq);
    }




    /**
     * @description:(香港账户中心开户订单流水查询接口)
     * @param queryReq
     * @return java.util.List<com.howbuy.crm.account.client.response.custinfo.HkPiggyAgreementDealVO>
     * @author: haoran.zhang
     * @date: 2024/8/12 15:41
     * @since JDK 1.8
     */
    public PageVO<HkAcctOptDealVO> getHkAcctOptDeal(HkAcctOptDealRequest queryReq){
        return hkCustRelatedOuterService.getHkAcctOptDeal(queryReq);
    }



}