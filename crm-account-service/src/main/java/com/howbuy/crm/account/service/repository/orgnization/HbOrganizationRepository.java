package com.howbuy.crm.account.service.repository.orgnization;

import com.google.common.collect.Lists;
import com.howbuy.crm.account.dao.mapper.organization.HbOrganizationMapper;
import com.howbuy.crm.account.dao.po.organization.OrgCodeNameDTO;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.*;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @description: 请在此添加描述
 * @date 2025/7/16 16:16
 * @since JDK 1.8
 */
@Slf4j
@Component
public class HbOrganizationRepository {

    @Resource
    private HbOrganizationMapper hbOrganizationMapper;

    public Map<String, String> getOrgByCode(List<String> orgCodes) {
        Map<String, String> map = new HashMap<>();
        log.info("HbOrganizationRepository.getOrgByCode");
        if(CollectionUtils.isNotEmpty(orgCodes)){
            // 去重后分批查询
            List<List<String>> list = Lists.partition(orgCodes, 999);
            list.forEach(l -> {
                List<OrgCodeNameDTO> hbOrganizationPOList = hbOrganizationMapper.getOrgNameByOrgCodeList(l);
                if(CollectionUtils.isNotEmpty(hbOrganizationPOList)){
                    hbOrganizationPOList.forEach(orgCodeNameDTO -> map.put(orgCodeNameDTO.getOrgCode(), orgCodeNameDTO.getOrgName()));
                }
            });
        }
        return map;
    }
}