package com.howbuy.crm.account.service.repository.commvisit;

import com.howbuy.crm.account.dao.mapper.commvisit.CmVisitMinutesAccompanyingMapper;
import com.howbuy.crm.account.dao.po.commvisit.CmVisitMinutesAccompanyingPO;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Repository;
import org.springframework.transaction.annotation.Propagation;
import org.springframework.transaction.annotation.Transactional;

import java.util.List;

/**
 * @description: 拜访纪要陪访人Repository
 * <AUTHOR>
 * @date 2025-04-08 10:26:00
 * @since JDK 1.8
 */
@Repository
public class CmVisitMinutesAccompanyRepository {

    @Autowired
    private CmVisitMinutesAccompanyingMapper cmVisitMinutesAccompanyingMapper;

    /**
     * 根据拜访纪要ID查询陪访人列表
     * @param visitMinutesId 拜访纪要ID
     * @return List<CmVisitMinutesAccompanyingPO>
     */
    public List<CmVisitMinutesAccompanyingPO> getAccompanyingList(String visitMinutesId) {
        return cmVisitMinutesAccompanyingMapper.selectByVisitMinutesId(visitMinutesId);
    }

    /**
     * 根据ID查询陪访人信息
     */
    public CmVisitMinutesAccompanyingPO getAccompanyingById(String id) {
        return cmVisitMinutesAccompanyingMapper.selectById(id);
    }

    /**
     * 更新陪访人反馈信息
     */
    public int updateAccompanyingFeedback(CmVisitMinutesAccompanyingPO po) {
        return cmVisitMinutesAccompanyingMapper.updateFeedback(po);
    }

    /**
     * 根据拜访纪要ID删除陪访人
     */
    public int deleteByVisitMinutesId(String visitMinutesId) {
        return cmVisitMinutesAccompanyingMapper.deleteByVisitMinutesId(visitMinutesId);
    }

    /**
     * 新增陪访人
     */
    @Transactional(rollbackFor = Exception.class, propagation = Propagation.REQUIRED)
    public int insert(CmVisitMinutesAccompanyingPO po) {
        return cmVisitMinutesAccompanyingMapper.insert(po);
    }
} 