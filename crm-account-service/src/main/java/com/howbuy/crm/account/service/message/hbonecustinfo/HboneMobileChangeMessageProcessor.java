/**
 * Copyright (c) 2023, <PERSON><PERSON><PERSON><PERSON> HOWBUY INVESTMENT MANAGEMENT Co., Ltd.
 * All right reserved.
 * <p>
 * THIS IS UNPUBLISHED PROPRIETARY SOURCE CODE OF HOWBUY INVESTMENT
 * MANAGEMENT CO., LTD.  THE CONTENTS OF THIS FILE MAY NOT BE DISCLOSED
 * TO THIRD PARTIES, COPIED OR DUPLICATED IN ANY FORM, IN WHOLE OR IN PART,
 * WITHOUT THE PRIOR WRITTEN PERMISSION OF HOWBUY INVESTMENT MANAGEMENT
 * CO., LTD.
 */
package com.howbuy.crm.account.service.message.hbonecustinfo;

import com.howbuy.crm.account.client.enums.custinfo.FullCustSourceEnum;
import com.howbuy.crm.account.client.response.Response;
import com.howbuy.crm.account.dao.bo.custinfo.CmConscustForAnalyseBO;
import com.howbuy.crm.account.service.business.custinfo.CmCustProcessBusinessService;
import com.howbuy.crm.account.service.commom.constant.Constants;
import com.howbuy.crm.account.service.message.dto.HboneUpdateMobileMessageDTO;
import com.howbuy.crm.account.service.vo.custinfo.AbnormalAnalyseResultVO;
import com.howbuy.crm.account.service.vo.custinfo.HboneMessageCustInfoVO;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;

import java.util.Arrays;
import java.util.List;

/**
 * @description: ([一账通账户]-[客户信息变更消息]-[手机号变更] 消息处理器[topic.hbone.updateCust])
 * <AUTHOR>
 * @date 2023/12/11 17:04
 * @since JDK 1.8
 */
@Slf4j
@Service
public class HboneMobileChangeMessageProcessor extends AbstractHboneMessageProcessor<HboneUpdateMobileMessageDTO>{


    /**
     * [TOPIC_UPDATE_CUST]
     */
    @Value("${topic.hbone.updateCust}")
    private String topicUpdateCust;


    /**
     * [MOBILE_UPDATE]
     */
    @Value("${tag.hbone.mobileUpdate}")
    private String tagName;


//    {
//        "body": {
//        "disCode": "UNIONPAY1",
//                "hboneNo": "**********",
//                "mobileCipher": "jLn7KdQOi0v6XhCwdc9fuQ==01",
//                "mobileDigest": "bfd49e6c3059df22f576dc17f14a5aa6",
//                "mobileMask": "1734326****",
//                "mobileVerifyStatus": "1"
//    },
//        "head": {
//        "createTime": "20240108000447",
//                "eventCode": "520198",
//                "hboneNo": "**********",
//                "msgKey": "9c5f2b7fe77cbf95cf29413346aa9f5e",
//                "tag": "MOBILE_UPDATE"
//    }
//    }



    @Autowired
    private CmCustProcessBusinessService processBusinessService;


    @Override
    public String getQuartMessageChannel() {
        return topicUpdateCust;
    }

    @Override
    public List<String> getSupportTagList() {
        return Arrays.asList(tagName);
    }

    @Override
    HboneMessageCustInfoVO constructByMessage(HboneUpdateMobileMessageDTO  acctMsgDto) {
        HboneMessageCustInfoVO custVo=new HboneMessageCustInfoVO();

        custVo.setHboneNo(acctMsgDto.getHboneNo());
        custVo.setDisCode(acctMsgDto.getDisCode());
        custVo.setMobileAreaCode(Constants.DEFAULT_MOBILE_AREA_CODE);
        custVo.setMobileDigest(acctMsgDto.getMobileDigest());
        custVo.setMobileMask(acctMsgDto.getMobileMask());
        custVo.setMobileVerifyStatus(acctMsgDto.getMobileVerifyStatus());

        //此处 需要 从账户中心查询 一账通账户信息
        fillHboneAcctInfo(custVo);

        return custVo;
    }
    @Override
    public FullCustSourceEnum getCustSource() {
        return FullCustSourceEnum.HBONE_CUST_MOBILE_CHANGE;
    }

    @Override
    Response<String> processHboneMessage(AbnormalAnalyseResultVO<HboneMessageCustInfoVO> resultVo) {
        HboneMessageCustInfoVO analyseVo=resultVo.getAnalyseVo();
//、判断①：根据推送的【一账通号】，查询是否存在绑定的【投顾客户号】
//（1）若不存在，则不处理
        CmConscustForAnalyseBO processedCustInfo=resultVo.getProcessedCustInfo();
        if(processedCustInfo==null){
            return Response.ok(String.format("一账通客户手机号变更同步，根据一账通号：%s ，查找crm客户不存在！",analyseVo.getHboneNo()),null);
        }
//（2）若存在，则将【香港客户号】的【手机区号、手机号】更新为账户中心推送的数据
//        若账户中心的【手机号】为空，则不更新CRM手机号
//        若账户中心的【手机号】不为空，则取账户中心【手机号】覆盖CRM的投顾客户信息
//        若更新手机号，手机区号默认写入：86
          return processBusinessService.updateMobileByHbone(analyseVo.getHboneNo(),
                  processedCustInfo.getConscustno(),
                  analyseVo.getCreator());
    }

}