package com.howbuy.crm.account.service.repository.beisen;

import com.github.pagehelper.Page;
import com.github.pagehelper.PageHelper;
import com.howbuy.crm.account.dao.mapper.beisenorg.CmBeisenOrgConfigMapper;
import com.howbuy.crm.account.dao.po.beisen.CmBeisenOrgDO;
import com.howbuy.crm.account.dao.po.beisenorg.CmBeisenOrgConfigPO;
import com.howbuy.crm.account.dao.req.beisen.QueryBeisenOrgConfigVO;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.List;

/**
 * <AUTHOR>
 * @description: 请在此添加描述
 * @date 2024/10/24 10:40
 * @since JDK 1.8
 */
@Component
public class CmBeisenOrgConfigRepository {

    @Autowired
    private CmBeisenOrgConfigMapper cmBeisenOrgConfigMapper;

    /**
     * @description 查询列表
     * @param vo	
     * @return com.github.pagehelper.Page<com.howbuy.crm.account.dao.po.beisenorg.CmBeisenOrgConfigPO>
     * @author: jianjian.yang
     * @date: 2024/11/11 14:07
     * @since JDK 1.8
     */
    public Page<CmBeisenOrgConfigPO> queryList(QueryBeisenOrgConfigVO vo){
        PageHelper.startPage(vo.getPage(),vo.getRows());
        return cmBeisenOrgConfigMapper.queryList(vo);
    }

    /**
     * @description 删除
     * @param id	
     * @return int
     * @author: jianjian.yang
     * @date: 2024/11/11 14:07
     * @since JDK 1.8
     */
    public int deleteByPrimaryKey(Long id){
        return cmBeisenOrgConfigMapper.deleteByPrimaryKey(id);
    }

    /**
     * @description 插入
     * @param record
     * @return int
     * @author: jianjian.yang
     * @date: 2024/11/11 14:07
     * @since JDK 1.8
     */
    public int insertSelective(CmBeisenOrgConfigPO record){
        return cmBeisenOrgConfigMapper.insertSelective(record);
    }

    /**
     * @description 单个查询
     * @param id	
     * @return com.howbuy.crm.account.dao.po.beisenorg.CmBeisenOrgConfigPO
     * @author: jianjian.yang
     * @date: 2024/11/11 14:08
     * @since JDK 1.8
     */
    public CmBeisenOrgConfigPO selectByPrimaryKey(Long id){
        return cmBeisenOrgConfigMapper.selectByPrimaryKey(id);
    }

    /**
     * @description 更新
     * @param record
     * @return int
     * @author: jianjian.yang
     * @date: 2024/11/11 14:08
     * @since JDK 1.8
     */
    public int updateByPrimaryKey(CmBeisenOrgConfigPO record){
        return cmBeisenOrgConfigMapper.updateByPrimaryKey(record);
    }

    /**
     * @description 根据北森ID查
     * @param orgIdBeisen
     * @return java.util.List<java.lang.Long>
     * @author: jianjian.yang
     * @date: 2024/11/19 17:25
     * @since JDK 1.8
     */
    public List<Long> selectByOrgId(String orgIdBeisen){
        return cmBeisenOrgConfigMapper.selectByOrgId(orgIdBeisen);
    }

    /**
     * @description 根据时间区间查
     * @param orgIdBeisen
     * @param startDate
     * @param endDate
     * @return int
     * @author: jianjian.yang
     * @date: 2024/11/19 16:20
     * @since JDK 1.8
     */
    public List<Long> selectByInterval(String orgIdBeisen, String startDate, String endDate){
        return cmBeisenOrgConfigMapper.selectByInterval(orgIdBeisen, startDate, endDate);
    }

    /**
     * @description 查北森机构列表
     * @param
     * @return java.util.List<com.howbuy.crm.account.dao.po.beisen.CmBeisenOrgDO>
     * @author: jianjian.yang
     * @date: 2024/11/19 17:56
     * @since JDK 1.8
     */
    public List<CmBeisenOrgDO> selectBeisenOrg(){
        return cmBeisenOrgConfigMapper.selectBeisenOrg();
    }
}