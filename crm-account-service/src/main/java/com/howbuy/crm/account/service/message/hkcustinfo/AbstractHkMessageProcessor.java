/**
 * Copyright (c) 2023, <PERSON><PERSON><PERSON><PERSON> HOWBUY INVESTMENT MANAGEMENT Co., Ltd.
 * All right reserved.
 * <p>
 * THIS IS UNPUBLISHED PROPRIETARY SOURCE CODE OF HOWBUY INVESTMENT
 * MANAGEMENT CO., LTD.  THE CONTENTS OF THIS FILE MAY NOT BE DISCLOSED
 * TO THIRD PARTIES, COPIED OR DUPLICATED IN ANY FORM, IN WHOLE OR IN PART,
 * WITHOUT THE PRIOR WRITTEN PERMISSION OF HOWBUY INVESTMENT MANAGEMENT
 * CO., LTD.
 */
package com.howbuy.crm.account.service.message.hkcustinfo;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.howbuy.common.utils.StringUtil;
import com.howbuy.crm.account.client.enums.custinfo.FullCustSourceEnum;
import com.howbuy.crm.account.client.enums.message.AccountTypeEnum;
import com.howbuy.crm.account.client.response.PageVO;
import com.howbuy.crm.account.client.response.Response;
import com.howbuy.crm.account.client.response.custinfo.HkAcctCustInfoVO;
import com.howbuy.crm.account.dao.po.message.CmAccountCenterMessagePO;
import com.howbuy.crm.account.service.business.custinfo.CmCustProcessBusinessService;
import com.howbuy.crm.account.service.commom.constant.Constants;
import com.howbuy.crm.account.service.message.AbstractTagMessageProcessor;
import com.howbuy.crm.account.service.outerservice.hkacct.HkCustBasicInfoOuterService;
import com.howbuy.crm.account.service.repository.message.CmAccountCenterMessageRepository;
import com.howbuy.crm.account.service.req.custinfo.HkAcctCustInfoReqVO;
import com.howbuy.crm.account.service.service.custmessage.CustMessageAnalyseFactory;
import com.howbuy.crm.account.service.vo.custinfo.AbnormalAnalyseResultVO;
import com.howbuy.crm.account.service.vo.custinfo.HkMessageCustInfoVO;
import com.howbuy.message.MessageService;
import com.howbuy.message.SimpleMessage;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.lang.reflect.ParameterizedType;
import java.lang.reflect.Type;

/**
 * @description: ([香港账户]-消息处理器)
 * <AUTHOR>
 * @date 2023/12/11 17:04
 * @since JDK 1.8
 */
@Service
@Slf4j
public abstract class  AbstractHkMessageProcessor<T> extends AbstractTagMessageProcessor {


    @Autowired
    private CmCustProcessBusinessService cmProcessBusinessService;

    @Autowired
    private HkCustBasicInfoOuterService hkCustInfoOuterService;

    @Autowired
    private CustMessageAnalyseFactory messageAnalyseFactory;

    @Autowired
    private CmAccountCenterMessageRepository cmAccountCenterMessageRepository;

    protected Class<T> accClazz;


    protected AbstractHkMessageProcessor() {
        // https://stackoverflow.com/questions/8474814/spring-cglib-why-cant-a-generic-class-be-proxied?noredirect=1
        // CGLIB代理的子类没有泛型，故此处强转ParameterizedType会报错
        Type type = getClass();
        do {
            type = ((Class<T>) type).getGenericSuperclass();
        } while (!(type instanceof ParameterizedType));
        this.accClazz = (Class<T>) ((ParameterizedType) type).getActualTypeArguments()[0];
//        this.clazz = String.class;
    }


    /**
     * @description:(如果 getSupportTagList 为空，则忽略tag标签，直接使用 topic)
     * @param
     * @return void
     * @author: haoran.zhang
     * @date: 2023/12/29 13:48
     * @since JDK 1.8
     */
    @Override
    public void init() {
        //如果没有指定tag，则 忽略tag 标签 直接使用 topic
        if(CollectionUtils.isEmpty(getSupportTagList())){
            log.info("添加[TAG]MessageProcessor：channel：{}, processor：{}", getQuartMessageChannel(), this);
            // 加载消息处理器
            MessageService.getInstance().addMessageProcessor(this.getQuartMessageChannel(), this);
        }else{
            log.info("添加[TOPIC]MessageProcessor：channel：{}, tag：{}, processor：{}", getQuartMessageChannel(), getTag(), this);
            MessageService.getInstance().addMessageProcessor(getQuartMessageChannel(), getTag(), this);
        }

    }

    /**
     * 获取消息来源
     * @return
     */
    abstract FullCustSourceEnum getCustSource();


    /**
     * 操作通道 1-MQ
     * @return
     */
    public String getOperateChannel(){
        return Constants.OPERATE_CHANNEL_MQ;
    }


    /**
     * 根据消息体  构建消息处理对象
     * @param acctMsgDto
     * @return
     */
    abstract HkMessageCustInfoVO constructByMessage(T acctMsgDto);


    /**
     * 获取 香港客户信息  。 此处 包括考虑状态 为 删除的客户
     * @param hkTxAcctNo
     * @return
     */
    private HkAcctCustInfoVO queryHkCustInfo(String hkTxAcctNo){
        //注销消息，  查询所有状态的客户
        HkAcctCustInfoReqVO reqVo=new HkAcctCustInfoReqVO();
        reqVo.setHkCustNo(hkTxAcctNo);
        PageVO<HkAcctCustInfoVO> pageResp=hkCustInfoOuterService.queryHkCustInfoPage(reqVo);
        if(pageResp==null|| CollectionUtils.isEmpty(pageResp.getRows())){
            log.error("香港客户信息不存在！hkTxAcctNo:{}",hkTxAcctNo);
            return null;
        }
        return pageResp.getRows().get(0);
    }


    /**
     * 请求香港账户中心  填充HK账户信息
     * @param custVo
     */
    protected void fillHkAcctInfo(HkMessageCustInfoVO  custVo){
        HkAcctCustInfoVO hkVo=queryHkCustInfo(custVo.getHkTxAcctNo());

        String usedCustName =
                StringUtil.isNotBlank(hkVo.getCustChineseName()) ? hkVo.getCustChineseName() : hkVo.getCustEnName();

        custVo.setCustName(usedCustName);
        custVo.setInvestType(hkVo.getInvstType());
        custVo.setMobileAreaCode(hkVo.getMobileAreaCode());
        custVo.setMobileDigest(hkVo.getMobileDigest());
        custVo.setMobileMask(hkVo.getMobileMask());
//        custVo.setMobileCipher();
        custVo.setIdSignAreaCode(hkVo.getIdSignAreaCode());
        custVo.setIdType(hkVo.getIdType());
        custVo.setIdNoDigest(hkVo.getIdNoDigest());
        custVo.setIdNoMask(hkVo.getIdNoMask());
//        custVo.setIdNoCipher();
        custVo.setHboneNo(hkVo.getHboneNo());
//        custVo.setCreator();
//        custVo.setAbnormalSource();
//        custVo.setOperateChannel();
//        custVo.setOpenOutletCode();
    }


    /**
     * 实际业务处理
     * @param resultVo
     */
    abstract Response<String> processHkMessage(AbnormalAnalyseResultVO<HkMessageCustInfoVO> resultVo);

    @Override
    protected void doProcessMessage(SimpleMessage message) {
        // 将消息保存到数据库
        saveMessage(message);
        // 处理消息
        dealMessage(message);
    }

    public Response<String> dealMessage(SimpleMessage message) {
        HkMessageCustInfoVO messgeCustVo = getHkMessageCustInfoVO(message);

        // 对拼装后的消息，进行分析和处理
        return analyzeAndProcess(messgeCustVo);
    }

    private HkMessageCustInfoVO getHkMessageCustInfoVO(SimpleMessage message) {
        String msgContent=(String) message.getContent();
        JSONObject json = JSON.parseObject(msgContent);
        // body为字符串，需要再次解析
        String body = json.getString(Constants.MQ_BODY_KEY);
        //转译 消息对象
        T acctMsgDto = JSON.parseObject(body, accClazz);

        //根据 acct消息对象 构建 crm待处理 消息对象
        HkMessageCustInfoVO  messgeCustVo=constructByMessage(acctMsgDto);

        //设置异常来源
        FullCustSourceEnum sourceEnum = getCustSource();
        messgeCustVo.setAbnormalSource(sourceEnum.getCode());
        //设置异常 渠道
        messgeCustVo.setOperateChannel(getOperateChannel());
        //设置 消息 clientID
        messgeCustVo.setMessageClientId(message.getClientId());
        // 消息ID
        messgeCustVo.setMessageId(message.getMessageId());
        //消息处理人：sys
        messgeCustVo.setCreator(Constants.OPERATOR_SYS);
        return messgeCustVo;
    }



    private Response<String> analyzeAndProcess(HkMessageCustInfoVO messgeCustVo) {
        //待处理数据 进行异常分析
        AbnormalAnalyseResultVO<HkMessageCustInfoVO> resultVo = messageAnalyseFactory.analyze(messgeCustVo);
        log.info("消息处理，来源：{}，香港交易账号：{}，一账通交易账号：{}，是否解析通过：{}，是否需要处理：{}",
                FullCustSourceEnum.getDescription(messgeCustVo.getAbnormalSource()),
                messgeCustVo.getHkTxAcctNo(),
                messgeCustVo.getHboneNo(),
                resultVo.isAnalysePass(),
                resultVo.isNeedProcess());
        //异常分析结果：通过，再做后续的 业务处理
        if (!resultVo.isAnalysePass()) {
            cmProcessBusinessService.insertHkProcessAbnormal(messgeCustVo, resultVo);
            return Response.fail();
        }
        if (!resultVo.isNeedProcess()) {
            return Response.ok();
        }
        //处理HK 消息
        Response<String> prcessResp = processHkMessage(resultVo);
        log.info("消息：{}，处理结果：{}", JSON.toJSONString(messgeCustVo), JSON.toJSONString(prcessResp));
        return prcessResp;
    }


    /**
     * @description: 将消息保存到数据库
     * @param message
     * @return void
     * @author: jin.wang03
     * @date: 2025/5/30 15:11
     * @since JDK 1.8
     */
    private void saveMessage(SimpleMessage message) {
        CmAccountCenterMessagePO cmAccountCenterMessagePO = new CmAccountCenterMessagePO();
        cmAccountCenterMessagePO.setId(message.getMessageId());
        cmAccountCenterMessagePO.setAccountType(AccountTypeEnum.HK.getCode());
        cmAccountCenterMessagePO.setTopic(message.getMessageChannel());
        cmAccountCenterMessagePO.setTag(message.getTag());
        cmAccountCenterMessagePO.setMqMessageText(JSON.toJSONString(message));
        cmAccountCenterMessageRepository.insert(cmAccountCenterMessagePO);
    }

}