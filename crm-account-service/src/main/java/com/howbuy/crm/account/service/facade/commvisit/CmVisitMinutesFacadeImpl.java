/**
 * Copyright (c) 2024, <PERSON><PERSON><PERSON><PERSON> HOWBUY INVESTMENT MANAGEMENT Co., Ltd.
 * All right reserved.
 * <p>
 * THIS IS UNPUBLISHED PROPRIETARY SOURCE CODE OF HOWBUY INVESTMENT
 * MANAGEMENT CO., LTD.  THE CONTENTS OF THIS FILE MAY NOT BE DISCLOSED
 * TO THIRD PARTIES, COPIED OR DUPLICATED IN ANY FORM, IN WHOLE OR IN PART,
 * WITHOUT THE PRIOR WRITTEN PERMISSION OF HOWBUY INVESTMENT MANAGEMENT
 * CO., LTD.
 */
package com.howbuy.crm.account.service.facade.commvisit;

import com.howbuy.crm.account.client.facade.commvisit.CmVisitMinutesFacade;
import com.howbuy.crm.account.client.request.commvisit.ExportVisitMinutesRequest;
import com.howbuy.crm.account.client.request.commvisit.QueryVisitMinutesDetailRequest;
import com.howbuy.crm.account.client.request.commvisit.QueryVisitMinutesForFeedbackRequest;
import com.howbuy.crm.account.client.request.commvisit.QueryVisitMinutesListRequest;
import com.howbuy.crm.account.client.request.commvisit.SaveFeedbackRequest;
import com.howbuy.crm.account.client.request.commvisit.UpdateManagerRequest;
import com.howbuy.crm.account.client.request.commvisit.SaveCustFeedbackRequest;
import com.howbuy.crm.account.client.request.commvisit.SearchUserRequest;
import com.howbuy.crm.account.client.response.ExportToFileVO;
import com.howbuy.crm.account.client.response.Response;
import com.howbuy.crm.account.client.response.commvisit.OperationResultVO;
import com.howbuy.crm.account.client.response.commvisit.VisitMinutesDetailVO;
import com.howbuy.crm.account.client.response.commvisit.VisitMinutesForFeedbackVO;
import com.howbuy.crm.account.client.response.commvisit.VisitMinutesListVO;
import com.howbuy.crm.account.client.response.commvisit.SearchUserVO;

import com.howbuy.crm.account.service.service.commvisit.CmVisitMinutesService;
import org.apache.dubbo.config.annotation.DubboService;
import org.springframework.beans.factory.annotation.Autowired;
import lombok.extern.slf4j.Slf4j;
import com.alibaba.fastjson.JSON;
import org.springframework.util.StringUtils;


/**
 * @description: 拜访纪要
 * <AUTHOR>
 * @date 2025-04-08 10:26:00
 * @since JDK 1.8
 */
@DubboService
@Slf4j
public class CmVisitMinutesFacadeImpl implements CmVisitMinutesFacade {

    @Autowired
    private CmVisitMinutesService cmVisitMinutesService;

    /**
     * @description 查询客户拜访纪要列表
     * @param request 查询请求参数
     * @return com.howbuy.crm.account.client.response.Response<com.howbuy.crm.account.client.response.commvisit.VisitMinutesListVO>
     * @author: jianjian.yang
     * @date: 2025/4/9 16:04
     * @since JDK 1.8
     */
    @Override
    public Response<VisitMinutesListVO> getVisitMinutesList(QueryVisitMinutesListRequest request) {
        return Response.ok(cmVisitMinutesService.queryVisitMinutesList(request));
    }
    
    /**
     * @description 查询客户拜访纪要明细
     * @param request 查询请求参数
     * @return com.howbuy.crm.account.client.response.Response<com.howbuy.crm.account.client.response.commvisit.VisitMinutesDetailVO>
     * @author: jianjian.yang
     * @date: 2025/4/9 16:04
     * @since JDK 1.8
     */
    @Override
    public Response<VisitMinutesDetailVO> getVisitMinutesDetail(QueryVisitMinutesDetailRequest request) {
        return Response.ok(cmVisitMinutesService.getVisitMinutesDetail(request));
    }
    
    /**
     * @description 保存陪访人/主管反馈
     * @param request 保存请求参数
     * @return com.howbuy.crm.account.client.response.Response<com.howbuy.crm.account.client.response.commvisit.OperationResultVO>
     * @author: jianjian.yang
     * @date: 2025/4/9 16:04
     * @since JDK 1.8
     */
    @Override
    public Response<OperationResultVO> saveFeedback(SaveFeedbackRequest request) {
        return Response.ok(cmVisitMinutesService.saveFeedback(request));
    }
    
    /**
     * @description 修改主管
     * @param request 修改请求参数
     * @return com.howbuy.crm.account.client.response.Response<com.howbuy.crm.account.client.response.commvisit.OperationResultVO>
     * @author: jianjian.yang
     * @date: 2025/4/9 16:04
     * @since JDK 1.8
     */
    @Override
    public Response<OperationResultVO> updateManager(UpdateManagerRequest request) {
        return Response.ok(cmVisitMinutesService.updateManager(request));
    }

    /**
     * @description 导出客户拜访纪要数据
     * @param request
     * @return com.howbuy.crm.account.client.response.Response<com.howbuy.crm.account.client.response.commvisit.ExportVisitMinutesVO>
     * @author: jianjian.yang
     * @date: 2025/4/8 10:30
     * @since JDK 1.8
     */
    @Override
    public Response<ExportToFileVO> exportVisitMinutes(ExportVisitMinutesRequest request) {
        return Response.ok(cmVisitMinutesService.exportVisitMinutes(request));
    }

    /**
     * @description 查询拜访纪要反馈明细
     * @param request 查询请求参数
     * @return com.howbuy.crm.account.client.response.Response<com.howbuy.crm.account.client.response.commvisit.VisitMinutesForFeedbackVO>
     * @author: jianjian.yang
     * @date: 2025/4/9 16:04
     * @since JDK 1.8
     */
    @Override
    public Response<VisitMinutesForFeedbackVO> getVisitMinutesForFeedback(QueryVisitMinutesForFeedbackRequest request) {
        return Response.ok(cmVisitMinutesService.getVisitMinutesForFeedback(request));
    }

    /**
     * @description 保存客户反馈
     * @param request 请求参数
     * @return com.howbuy.crm.account.client.response.Response<com.howbuy.crm.account.client.response.commvisit.OperationResultVO>
     * @author: jianjian.yang
     * @date: 2025/4/9 16:04
     * @since JDK 1.8
     */
    @Override
    public Response<OperationResultVO> saveCustFeedback(SaveCustFeedbackRequest request) {
        try {
            OperationResultVO result = cmVisitMinutesService.saveCustFeedback(request);
            return Response.ok(result);
        } catch (Exception e) {
            log.error("保存客户反馈失败", e);
            return Response.fail(e.getMessage());
        }
    }

    /**
     * @description 用户搜索
     * @param request 搜索参数
     * @return com.howbuy.crm.account.client.response.Response<com.howbuy.crm.account.client.response.commvisit.SearchUserVO>
     * @author: jianjian.yang
     * @date: 2025/4/9 16:04
     * @since JDK 1.8
     */
    @Override
    public Response<SearchUserVO> searchUser(SearchUserRequest request) {
        log.info("CmVisitMinutesFacadeImpl_searchUser_request:{}", JSON.toJSONString(request));
        try {
            // 参数校验
            if (request == null || StringUtils.isEmpty(request.getSearchParam())) {
                return Response.fail("搜索参数不能为空");
            }
            if (StringUtils.isEmpty(request.getSearchType())) {
                return Response.fail("搜索类型不能为空");
            }

            // 调用服务层搜索用户
            SearchUserVO result = cmVisitMinutesService.searchUser(request);
            return Response.ok(result);
        } catch (Exception e) {
            log.error("用户搜索异常，request:{}", JSON.toJSONString(request), e);
            return Response.fail("用户搜索异常");
        }
    }

    /**
     * @description 推送未反馈提醒
     * @param curPreDayParam 当前日期前N天的日期参数(格式YYYYMMDD)
     * @return com.howbuy.crm.account.client.response.Response<java.lang.Object>
     * @author: jianjian.yang
     * @date: 2025/4/29 19:45
     * @since JDK 1.8
     */
    @Override
    public Response<Object> pushNoFeedback(String curPreDayParam) {
        cmVisitMinutesService.pushNoFeedback(curPreDayParam);
        return Response.ok();
    }
}