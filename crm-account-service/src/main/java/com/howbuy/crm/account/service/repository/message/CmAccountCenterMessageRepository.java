/**
 * Copyright (c) 2025, <PERSON><PERSON><PERSON><PERSON> HOWBUY INVESTMENT MANAGEMENT Co., Ltd.
 * All right reserved.
 * <p>
 * THIS IS UNPUBLISHED PROPRIETARY SOURCE CODE OF HOWBUY INVESTMENT
 * MANAGEMENT CO., LTD.  THE CONTENTS OF THIS FILE MAY NOT BE DISCLOSED
 * TO THIRD PARTIES, COPIED OR DUPLICATED IN ANY FORM, IN WHOLE OR IN PART,
 * WITHOUT THE PRIOR WRITTEN PERMISSION OF HOWBUY INVESTMENT MANAGEMENT
 * CO., LTD.
 */
package com.howbuy.crm.account.service.repository.message;

import com.howbuy.crm.account.client.response.Response;
import com.howbuy.crm.account.dao.mapper.message.CmAccountCenterMessageMapper;
import com.howbuy.crm.account.dao.po.message.CmAccountCenterMessagePO;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import org.springframework.transaction.annotation.Propagation;
import org.springframework.transaction.annotation.Transactional;

/**
 * @description: (请在此添加描述)
 * <AUTHOR>
 * @date 2025/5/30 14:28
 * @since JDK 1.8
 */


@Slf4j
@Component
public class CmAccountCenterMessageRepository {

    @Autowired
    private CmAccountCenterMessageMapper cmAccountCenterMessageMapper;

    /**
     * 插入一条消息
     *
     * @param cmAccountCenterMessage
     * @return
     */
    public Response<String> insert(CmAccountCenterMessagePO cmAccountCenterMessage) {
        cmAccountCenterMessageMapper.insert(cmAccountCenterMessage);
        return Response.ok();
    }


    /**
     * 根据id查询消息
     *
     * @param id
     * @return
     */
    public CmAccountCenterMessagePO selectById(String id) {
        return cmAccountCenterMessageMapper.selectByPrimaryKey(id);
    }


}