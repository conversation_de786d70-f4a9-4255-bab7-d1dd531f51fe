/**
 * Copyright (c) 2023, <PERSON><PERSON><PERSON><PERSON> HOWBUY INVESTMENT MANAGEMENT Co., Ltd.
 * All right reserved.
 * <p>
 * THIS IS UNPUBLISHED PROPRIETARY SOURCE CODE OF HOWBUY INVESTMENT
 * MANAGEMENT CO., LTD.  THE CONTENTS OF THIS FILE MAY NOT BE DISCLOSED
 * TO THIRD PARTIES, COPIED OR DUPLICATED IN ANY FORM, IN WHOLE OR IN PART,
 * WITHOUT THE PRIOR WRITTEN PERMISSION OF HOWBUY INVESTMENT MANAGEMENT
 * CO., LTD.
 */
package com.howbuy.crm.account.service.repository.consultant;

import com.howbuy.crm.account.client.enums.YesOrNoEnum;
import com.howbuy.crm.account.dao.mapper.consultant.CmConsultantMapper;
import com.howbuy.crm.account.dao.mapper.custinfo.CmCustconstantMapper;
import com.howbuy.crm.account.dao.mapper.customize.consultant.CmConsultantCustomizeMapper;
import com.howbuy.crm.account.dao.po.consultant.CmConsultantPO;
import com.howbuy.crm.account.dao.po.consultant.SearchConsultantDTO;
import com.howbuy.crm.account.dao.po.consultant.ConsultantSimpleInfoDto;
import com.howbuy.crm.account.dao.po.custinfo.CmCustconstantPO;
import com.howbuy.crm.account.dao.req.consultant.QueryConsultantReqVO;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import org.springframework.transaction.annotation.Propagation;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.Assert;

import java.util.List;

/**
 * @description: (投顾 repository )
 * <AUTHOR>
 * @date 2023/12/26 17:05
 * @since JDK 1.8
 */
@Component
@Transactional(rollbackFor = Exception.class, propagation = Propagation.SUPPORTS)
public class CmConsultantRepository {


    @Autowired
    private CmConsultantMapper cmConsultantMapper;
    @Autowired
    private CmCustconstantMapper custconstantMapper;
    @Autowired
    private CmConsultantCustomizeMapper cmConsultantCustomizeMapper;

   
    /**
     * @description:(根据投顾consCode 查询投顾信息)
     * @param consCode
     * @return com.howbuy.crm.account.dao.po.consultant.CmConsultantPO
     * @author: haoran.zhang
     * @date: 2023/12/26 17:09
     * @since JDK 1.8
     */
    public CmConsultantPO selectConsultant(String consCode){
        return  cmConsultantMapper.selectByPrimaryKey(consCode);
    }

    /**
     * @description:(请在此添加描述)
     * @param consCodes
     * @return java.util.List<com.howbuy.crm.account.dao.po.consultant.CmConsultantPO>
     * @author: jin.wang03
     * @date: 2025/4/14 16:04
     * @since JDK 1.8
     */
    public List<CmConsultantPO> listConsultantByConsCodes(List<String> consCodes){
        return  cmConsultantMapper.listConsultantByConsCodes(consCodes);
    }



    /**
     * @description:(根据投顾consCode 查询[理财顾问状态=1有效]的 投顾信息)
     * @param consCode
     * @return com.howbuy.crm.account.dao.po.consultant.CmConsultantPO
     * @author: haoran.zhang
     * @date: 2023/12/26 17:09
     * @since JDK 1.8
     */
    public CmConsultantPO selectValidConsultant(String consCode){
        CmConsultantPO consultant=selectConsultant(consCode);
        //理财顾问状态=1有效
        if(consultant != null && YesOrNoEnum.YES.getCode().equals(consultant.getConsstatus())){
           return consultant;
        }
        return null;
    }


    /**
     * @description: (根据投顾consCode 查询客户数)
     * @param consCode
     * @return java.lang.Integer
     * @throws
     * @since JDK 1.8
     */
    public Integer countCustNumByConsCode(String consCode) {
        return custconstantMapper.countCustNumByConsCode(consCode);
    }


    /**
     * @description: (根据客户号查询该客户归属的投顾信息)
     * @param custNo
     * @return com.howbuy.crm.account.dao.po.consultant.CmConsultantPO
     * @throws
     * @since JDK 1.8
     */
    public CmConsultantPO selectConsultByCustNo(String custNo){
        Assert.notNull(custNo, "custNo is null");
        CmCustconstantPO relationPo= custconstantMapper.selectByPrimaryKey(custNo);
        return  relationPo != null ? selectValidConsultant(relationPo.getConscode()) : null;
    }

    /**
     * @description:(查询所有需要刷新客户关系的投顾企微账号)
     * @param
     * @return java.util.List<java.lang.String>
     * @author: shuai.zhang
     * @date: 2024/3/27 11:19
     * @since JDK 1.8
     */
    public List<String> getAllNeedRefreshWechatConsCode() {
        return cmConsultantMapper.getAllNeedRefreshWechatConsCode();
    }

    /**
     * @description:(根据投顾codelist查询投顾信息)
     * @param consCodeList
     * @return java.util.List<com.howbuy.crm.account.dao.po.consultant.CmConsultantPO>
     * @author: shuai.zhang
     * @date: 2024/3/28 14:39
     * @since JDK 1.8
     */
    public List<CmConsultantPO> getListByConscodes(List<String> consCodeList) {
        return cmConsultantCustomizeMapper.getListByConscodes(consCodeList);
    }

    /**
     * @description 根据投顾codelist查询包含离职投顾信息
     * @param consCodeList
     * @return java.util.List<com.howbuy.crm.account.dao.po.consultant.CmConsultantPO>
     * @author: jianjian.yang
     * @date: 2025/5/14 17:06
     * @since JDK 1.8
     */
    public List<CmConsultantPO> getContainQuitListByConsCodes(List<String> consCodeList) {
        return cmConsultantCustomizeMapper.getContainQuitListByConsCodes(consCodeList);
    }

    /**
     * @description:根据reqVO查询投顾信息
     * @param reqVO
     * @return java.util.List<com.howbuy.crm.account.dao.po.consultant.CmConsultantPO>
     * <AUTHOR>
     * @date 2024/10/11 19:20
     * @since JDK 1.8
     */
    public List<CmConsultantPO> queryListByVo(QueryConsultantReqVO reqVO) {
        return cmConsultantCustomizeMapper.queryListByVo(reqVO);
    }

    /**
     * @description: 根据 手机号摘要 查询投顾数量
     * @param mobileDigest
     * @return int
     * @author: jin.wang03
     * @date: 2024/11/4 14:17
     * @since JDK 1.8
     */
    public int countByMobileDigest(String mobileDigest) {
        return cmConsultantMapper.countByMobileDigest(mobileDigest);
    }

    /**
     * @description 搜索用户
     * @param keyword
     * @param isPm
     * @param pmRoleCodeList
     * @return java.util.List<com.howbuy.crm.account.dao.po.consultant.SearchConsultantDTO>
     * @author: jianjian.yang
     * @date: 2025/4/11 15:33
     * @since JDK 1.8
     */
    public List<SearchConsultantDTO> searchConsultant(String keyword, boolean isPm, List<String> pmRoleCodeList) {
        return cmConsultantCustomizeMapper.searchConsultant(keyword, isPm, pmRoleCodeList);
    }

    /**
     * @description 根据姓名查询投顾编码
     * @param consName
     * @return java.util.List<java.lang.String>
     * @author: jianjian.yang
     * @date: 2025/4/11 17:18
     * @since JDK 1.8
     */
    public List<String> getConsCodeByName(String consName) {
        return cmConsultantCustomizeMapper.getConsCodeByName(consName);
    }


    /**
     * @description:(查询全量投顾简单信息，缓存使用)
     * @param
     * @return java.util.List<com.howbuy.crm.account.dao.po.consultant.ConsultantSimpleInfoDto>
     * @author: haoran.zhang
     * @date: 2025/3/6 13:19
     * @since JDK 1.8
     */
    public List<ConsultantSimpleInfoDto> getSimpleConsultList(){
        return cmConsultantCustomizeMapper.getSimpleConsultList();
    }


    /**
     * @description:(根据投顾号查询投顾简单信息，缓存使用)
     * @param consCode
     * @return com.howbuy.crm.account.dao.po.consultant.ConsultantSimpleInfoDto
     * @author: haoran.zhang
     * @date: 2025/3/6 13:19
     * @since JDK 1.8
     */
    public ConsultantSimpleInfoDto getSimpleConsultantByCode( String consCode){
        return cmConsultantCustomizeMapper.getSimpleConsultantByCode(consCode);
    }

}