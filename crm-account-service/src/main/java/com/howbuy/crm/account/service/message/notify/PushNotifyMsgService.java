/**
 * Copyright (c) 2024, <PERSON><PERSON><PERSON><PERSON> HOWBUY INVESTMENT MANAGEMENT Co., Ltd.
 * All right reserved.
 * <p>
 * THIS IS UNPUBLISHED PROPRIETARY SOURCE CODE OF HOWBUY INVESTMENT
 * MANAGEMENT CO., LTD.  THE CONTENTS OF THIS FILE MAY NOT BE DISCLOSED
 * TO THIRD PARTIES, COPIED OR DUPLICATED IN ANY FORM, IN WHOLE OR IN PART,
 * WITHOUT THE PRIOR WRITTEN PERMISSION OF HOWBUY INVESTMENT MANAGEMENT
 * CO., LTD.
 */
package com.howbuy.crm.account.service.message.notify;

import com.alibaba.fastjson.JSON;
import com.howbuy.crm.account.service.message.dto.NofifyMsgDTO;
import com.howbuy.message.MessageService;
import com.howbuy.message.SimpleMessage;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;

/**
 * @description: (发送消息通知[站内信、企业微信] )
 * <AUTHOR>
 * @date 2024/1/26 15:15
 * @since JDK 1.8
 */
@Slf4j
@Service
public class PushNotifyMsgService {


    /**
     * 实现 发送 站内信的 通知
     * 去除外部 对nt-client的依赖
     */
    @Value("${crm.notify.msg.channel}")
    private String CRM_NOTIFY_MSG_CHANNEL;


    /**
     * @description:(发送 站内信通知)
     * @param nofifyMsgDTO
     * @return void
     * @author: haoran.zhang
     * @date: 2024/1/26 15:23
     * @since JDK 1.8
     */
    public void pushNofityMsg(NofifyMsgDTO nofifyMsgDTO){
        SimpleMessage simpleMessage = new SimpleMessage();
        simpleMessage.setContent(JSON.toJSONString(nofifyMsgDTO));
        log.info("发送[站内信|企微]通知消息:{}", JSON.toJSONString(nofifyMsgDTO));
        MessageService.getInstance().send(CRM_NOTIFY_MSG_CHANNEL, simpleMessage);
    }

}