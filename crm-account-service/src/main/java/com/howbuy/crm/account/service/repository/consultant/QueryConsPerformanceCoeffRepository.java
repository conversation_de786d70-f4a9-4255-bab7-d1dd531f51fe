/**
 * Copyright (c) 2024, <PERSON><PERSON><PERSON><PERSON> HOWBUY INVESTMENT MANAGEMENT Co., Ltd.
 * All right reserved.
 * <p>
 * THIS IS UNPUBLISHED PROPRIETARY SOURCE CODE OF HOWBUY INVESTMENT
 * MANAGEMENT CO., LTD.  THE CONTENTS OF THIS FILE MAY NOT BE DISCLOSED
 * TO THIRD PARTIES, COPIED OR DUPLICATED IN ANY FORM, IN WHOLE OR IN PART,
 * WITHOUT THE PRIOR WRITTEN PERMISSION OF HOWBUY INVESTMENT MANAGEMENT
 * CO., LTD.
 */
package com.howbuy.crm.account.service.repository.consultant;

import com.github.pagehelper.Page;
import com.github.pagehelper.PageHelper;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import com.howbuy.crm.account.dao.bo.consultant.CmConsPerformanceCoeffBO;
import com.howbuy.crm.account.dao.bo.consultant.SimpleConsCustDTO;
import com.howbuy.crm.account.dao.mapper.constant.HbMenuMapper;
import com.howbuy.crm.account.dao.mapper.consultant.CmConsPerformanceCoeffMapper;
import com.howbuy.crm.account.dao.mapper.consultantexp.CmConsultantExpMapper;
import com.howbuy.crm.account.dao.mapper.custinfo.CmConscustMapper;
import com.howbuy.crm.account.dao.po.consultant.CmConsPerformanceCoeffPO;
import com.howbuy.crm.account.dao.po.consultant.RegionalSubtotalDTO;
import com.howbuy.crm.account.dao.req.consultant.QueryConsPerformanceCoeffListDTO;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.ibatis.annotations.Param;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import org.springframework.transaction.annotation.Propagation;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * @description: 获取人员绩效系数表数据访问层
 * <AUTHOR>
 * @date 2025-07-02 16:15:20
 * @since JDK 1.8
 */
@Component
@Transactional(rollbackFor = Exception.class, propagation = Propagation.SUPPORTS)
@Slf4j
public class QueryConsPerformanceCoeffRepository {

    @Resource
    private CmConsPerformanceCoeffMapper cmConsPerformanceCoeffMapper;
    @Resource
    private HbMenuMapper hbMenuMapper;
    @Resource
    private CmConsultantExpMapper cmConsultantExpMapper;
    @Resource
    private CmConscustMapper cmConscustMapper;

    private static final int MAX_QUERY_SIZE = 999;

    /**
     * @param consCustNo 投顾客户号
     * @param consCode 投顾code
     * @return com.howbuy.crm.account.dao.po.consultant.CmConsPerformanceCoeffPO
     * @description: 获取最新一条记录
     * <AUTHOR>
     * @date 2025-07-02 16:15:20
     * @since JDK 1.8
     */
    public CmConsPerformanceCoeffPO getLatestRecord(String consCustNo, String consCode) {
        log.info("获取最新一条记录，投顾客户号：{}，投顾code：{}", consCustNo, consCode);
        CmConsPerformanceCoeffPO result = cmConsPerformanceCoeffMapper.selectLatestByConsCustNoAndConsCode(consCustNo, consCode);
        log.info("获取最新一条记录完成，结果：{}", result);
        return result;
    }

    /**
     * @param queryDTO 查询条件
     * @return com.github.pagehelper.Page<com.howbuy.crm.account.dao.po.consultant.CmConsPerformanceCoeffPO>
     * @description: 分页查询人员绩效系数列表
     * <AUTHOR>
     * @date 2025-07-11 18:52:00
     * @since JDK 1.8
     */
    public Page<CmConsPerformanceCoeffBO> queryConsPerformanceCoeffList(QueryConsPerformanceCoeffListDTO queryDTO) {
        log.info("分页查询人员绩效系数列表，查询条件：{}", queryDTO);
        PageHelper.startPage(queryDTO.getPage(), queryDTO.getRows());
        Page<CmConsPerformanceCoeffBO> result = cmConsPerformanceCoeffMapper.selectConsPerformanceCoeffList(queryDTO);
        log.info("分页查询人员绩效系数列表完成，总记录数：{}", result.getTotal());
        return result;
    }

    /**
     * @param queryDTO 查询条件
     * @return java.util.List<com.howbuy.crm.account.dao.po.consultant.CmConsPerformanceCoeffPO>
     * @description: 查询人员绩效系数列表（不分页，用于导出）
     * <AUTHOR>
     * @date 2025-07-11 18:52:00
     * @since JDK 1.8
     */
    public List<CmConsPerformanceCoeffBO> queryConsPerformanceCoeffListForExport(QueryConsPerformanceCoeffListDTO queryDTO) {
        log.info("查询人员绩效系数列表（不分页），查询条件：{}", queryDTO);
        List<CmConsPerformanceCoeffBO> result = cmConsPerformanceCoeffMapper.selectConsPerformanceCoeffList(queryDTO);
        log.info("查询人员绩效系数列表（不分页）完成，记录数：{}", result.size());
        return result;
    }

    /**
     * @description 请在此添加描述
     * @param boList
     * @return
     * @author: jianjian.yang
     * @date:  2025-07-31 18:52:00
     * @since JDK 1.8
     */
    public Map<String, String> getCustNameMap(List<CmConsPerformanceCoeffBO> boList){
        Map<String, String> map = Maps.newHashMap();
        if(CollectionUtils.isEmpty(boList)){
            return map;
        }
        List<String> consCustNoList = boList.stream().map(CmConsPerformanceCoeffBO::getConsCustNo).distinct().collect(Collectors.toList());
        if(CollectionUtils.isEmpty(consCustNoList)){
            return map;
        }
        List<List<String>> consCustNoListList = Lists.partition(consCustNoList, MAX_QUERY_SIZE);
        for(List<String> list : consCustNoListList){
            List<SimpleConsCustDTO> simpleConsCustList = cmConscustMapper.querySimpleConsCustList(list);
            if(CollectionUtils.isEmpty(simpleConsCustList)){
                continue;
            }
            map.putAll(simpleConsCustList.stream().collect(Collectors.toMap(SimpleConsCustDTO::getConsCustNo, SimpleConsCustDTO::getCustName)));
        }
        return map;
    }

    public int queryConsPerformanceCoeffListCount(QueryConsPerformanceCoeffListDTO queryDTO) {
        log.info("查询人员绩效系数数量，查询条件：{}", queryDTO);
        int count = cmConsPerformanceCoeffMapper.selectConsPerformanceCoeffListCount(queryDTO);
        log.info("查询人员绩效系数数量 {}", count);
        return count;
    }

    /**
     * 获取菜单名称
     * @param menuCode
     * @return
     */
    public String getMenuNameByMenuCode(String menuCode){
        return hbMenuMapper.getMenuNameByMenuCode(menuCode);
    }

    /**
     * @description 请在此添加描述
     * @@param orgCode
     * @return
     * @author: jianjian.yang
     * @date:
     * @since JDK 1.8
     */
    public Map<String, String> getRegionalSubtotalNameByOrgCodeList(@Param("orgCode") List<String> orgCodes){
        Map<String, String> map = new HashMap<>();
        if(CollectionUtils.isEmpty(orgCodes)){
            return map;
        }
        List<List<String>> orgCodeList = Lists.partition(orgCodes, MAX_QUERY_SIZE);
        for(List<String> orgCode : orgCodeList){
            List<RegionalSubtotalDTO> list = cmConsultantExpMapper.getRegionalSubtotalNameByOrgCodeList(orgCode);
            if(CollectionUtils.isEmpty(list)){
                continue;
            }
            list.forEach(regionalSubtotalDTO -> {
                map.put(regionalSubtotalDTO.getOrgCode(), regionalSubtotalDTO.getRegionalSubtotalName());
            });
        }
        return map;
    }

    /**
     * @param consCustNoList 投顾客户号列表
     * @param consCode 投顾code（可选）
     * @return java.util.List<CmConsPerformanceCoeffPO>
     * @description: 批量获取最新绩效系数记录
     * <AUTHOR>
     * @date 2025-07-17 15:50:04
     * @since JDK 1.8
     */
    public List<CmConsPerformanceCoeffPO> getLatestRecordsByConsCustNoListAndConsCode(List<String> consCustNoList, String consCode) {
        log.info("批量获取最新绩效系数记录，客户号列表：{}，投顾code：{}", consCustNoList, consCode);
        if (consCustNoList == null || consCustNoList.isEmpty()) {
            return java.util.Collections.emptyList();
        }
        List<CmConsPerformanceCoeffPO> result = cmConsPerformanceCoeffMapper.selectLatestByConsCustNoListAndConsCode(consCustNoList, consCode);
        log.info("批量获取最新绩效系数记录完成，结果数：{}", result != null ? result.size() : 0);
        return result;
    }
} 