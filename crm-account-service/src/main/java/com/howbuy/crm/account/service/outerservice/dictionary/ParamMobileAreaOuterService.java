package com.howbuy.crm.account.service.outerservice.dictionary;

import cn.hutool.http.HttpUtil;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.howbuy.crm.account.client.response.Response;
import com.howbuy.crm.account.client.response.dictionary.ParamMobileAreaDTO;
import com.howbuy.crm.account.client.response.dictionary.ParamMobileAreaListVO;
import com.howbuy.crm.account.client.response.dictionary.ParamMobileAreaVO;
import com.howbuy.crm.account.service.commom.constant.Constants;
import com.howbuy.crm.account.service.commom.utils.MainLogUtils;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;

/**
 * <AUTHOR>
 * @description: 查询手机地区码
 * @date 2023/5/19 13:33
 * @since JDK 1.8
 */
@Service
public class ParamMobileAreaOuterService {

    @Value("${query.mobileAreaCode.url}")
    private String queryMobileAreaCodeUrl;

    private final ParamMobileAreaClient paramMobileAreaClient;

    public ParamMobileAreaOuterService(ParamMobileAreaClient paramMobileAreaClient) {
        this.paramMobileAreaClient = paramMobileAreaClient;
    }
    /**
     * @description:(查询手机地区码)
     * @param
     * @return com.howbuy.crm.cgi.manager.outerservice.CrmResult<com.howbuy.crm.cgi.manager.domain.dtmsproduct.ParamMobileAreaDTO>
     * @author: shuai.zhang
     * @date: 2023/6/8 15:56
     * @since JDK 1.8
     */
    public Response<ParamMobileAreaDTO> query(){
        return paramMobileAreaClient.query();
    }


    /**
     * @description:(请在此添加描述)
     * @param
     * @return com.howbuy.crm.account.client.response.Response<com.howbuy.crm.account.client.response.dictionary.ParamMobileAreaListVO>
     * @author: jin.wang03
     * @date: 2024/1/11 14:13
     * @since JDK 1.8
     */
    public Response<ParamMobileAreaListVO> getMobileAreaCode() {
        ParamMobileAreaListVO paramMobileAreaListVO = new ParamMobileAreaListVO();

        long startTime = System.currentTimeMillis();
        String mobileAreaCodeRes = HttpUtil.post(queryMobileAreaCodeUrl, new HashMap<>());
        long endTime = System.currentTimeMillis();
        MainLogUtils.httpCallOut(queryMobileAreaCodeUrl, Constants.HTTP_SUCCESS_CODE, endTime - startTime);

        JSONObject mobileAreaCodeJsonObject = JSON.parseObject(mobileAreaCodeRes);
        JSONObject jsonObject1 = mobileAreaCodeJsonObject.getJSONObject("data");

        paramMobileAreaListVO.setIndex(JSON.parseArray(JSON.toJSONString(jsonObject1.getJSONArray("index")), String.class));

        JSONArray itemList = jsonObject1.getJSONArray("itemList");

        List<List<ParamMobileAreaVO>> lists = new ArrayList<>();
        for (int i = 0; i < itemList.size(); i++) {
            List<ParamMobileAreaVO> paramMobileAreaVOS = new ArrayList<>();
            JSONArray jsonArray = itemList.getJSONArray(i);

            for (int j = 0; j < jsonArray.size(); j++) {
                ParamMobileAreaVO paramMobileAreaVO = new ParamMobileAreaVO();
                JSONObject jsonObject = jsonArray.getJSONObject(j);
                paramMobileAreaVO.setAreacode(jsonObject.getString("areacode"));
                paramMobileAreaVO.setAreaname(jsonObject.getString("areaname"));

                paramMobileAreaVOS.add(paramMobileAreaVO);
            }
            lists.add(paramMobileAreaVOS);
        }
        paramMobileAreaListVO.setItemList(lists);

        return Response.ok(paramMobileAreaListVO);
    }

}
