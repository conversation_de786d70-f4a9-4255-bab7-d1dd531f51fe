/**
 * Copyright (c) 2023, <PERSON><PERSON><PERSON><PERSON> HOWBUY INVESTMENT MANAGEMENT Co., Ltd.
 * All right reserved.
 * <p>
 * THIS IS UNPUBLISHED PROPRIETARY SOURCE CODE OF HOWBUY INVESTMENT
 * MANAGEMENT CO., LTD.  THE CONTENTS OF THIS FILE MAY NOT BE DISCLOSED
 * TO THIRD PARTIES, COPIED OR DUPLICATED IN ANY FORM, IN WHOLE OR IN PART,
 * WITHOUT THE PRIOR WRITTEN PERMISSION OF HOWBUY INVESTMENT MANAGEMENT
 * CO., LTD.
 */
package com.howbuy.crm.account.service.business.custinfo;

import com.alibaba.fastjson.JSON;
import com.alibaba.nacos.common.utils.CollectionUtils;
import com.google.common.collect.Lists;
import com.howbuy.acc.common.utils.DigestUtil;
import com.howbuy.acc.common.utils.MaskUtil;
import com.howbuy.common.utils.Assert;
import com.howbuy.common.utils.DateUtil;
import com.howbuy.common.utils.StringUtil;
import com.howbuy.crm.account.client.cs.CsActivityTypeEnum;
import com.howbuy.crm.account.client.cs.WirelessChannelEnum;
import com.howbuy.crm.account.client.enums.DisCodeEnum;
import com.howbuy.crm.account.client.enums.YesOrNoEnum;
import com.howbuy.crm.account.client.enums.custinfo.*;
import com.howbuy.crm.account.client.request.custinfo.CreateConsCustRequest;
import com.howbuy.crm.account.client.request.custinfo.HboneAcctRelationOptRequest;
import com.howbuy.crm.account.client.request.custinfo.HkAcctRelationOptRequest;
import com.howbuy.crm.account.client.request.custinfo.UpdateConsCustRequest;
import com.howbuy.crm.account.client.response.PageVO;
import com.howbuy.crm.account.client.response.Response;
import com.howbuy.crm.account.client.response.custinfo.*;
import com.howbuy.crm.account.dao.bo.custinfo.CmConscustForAnalyseBO;
import com.howbuy.crm.account.dao.po.consultant.CmConsultantPO;
import com.howbuy.crm.account.dao.po.custinfo.CmConscustPO;
import com.howbuy.crm.account.dao.po.custinfo.CmSourceInfoPO;
import com.howbuy.crm.account.dao.po.custservice.CmBookingcustPO;
import com.howbuy.crm.account.service.commom.constant.Constants;
import com.howbuy.crm.account.service.commom.utils.AbnormalCustUtils;
import com.howbuy.crm.account.service.outerservice.acct.HboneAcctInfoOuterService;
import com.howbuy.crm.account.service.outerservice.acct.HboneDisAcctInfoOuterService;
import com.howbuy.crm.account.service.outerservice.hkacct.HkCustBasicInfoOuterService;
import com.howbuy.crm.account.service.repository.consultant.CmConsultantRepository;
import com.howbuy.crm.account.service.repository.custinfo.*;
import com.howbuy.crm.account.service.repository.custservice.BookingCustRepository;
import com.howbuy.crm.account.service.req.custinfo.HkAcctCustInfoReqVO;
import com.howbuy.crm.account.service.service.custmessage.CustMessageAnalyseFactory;
import com.howbuy.crm.account.service.vo.custinfo.*;
import lombok.extern.slf4j.Slf4j;
import org.apache.dubbo.common.utils.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.Date;
import java.util.List;

/**
 * @description: ([账户]-[公共处理服务])
 * <AUTHOR>
 * @date 2023/12/18 10:31
 * @since JDK 1.8
 */
@Service
@Slf4j
public  class CmCustProcessBusinessService{

    /**
     * 来源系统：1-CMS;2-ACC
     */
    private static final String BOOKING_SOURCE_SYS_ACCT = "2";

    /**
     * 海外 预约  serialNo  前缀
     */
    private static final String BOOKING_PREFIX ="HWKH";


    /**
     * 预约类型(1:私募预约 ;2:合作预约)
     */
    private static final String BOOKING_TYPE_SIMU="1";


    @Autowired
    private HkAbnormalCustRepository hkAbnormalCustRepository;
    @Autowired
    private HboneAbnormalCustRepository hboneAbnormalCustRepository;

    @Autowired
    private ConsCustInfoRepository consCustInfoRepository;

    @Autowired
    private CustMessageAnalyseFactory hkMessageAnalyseFactory;

    @Autowired
    private HkCustBindBusinessService hkCustBindBusinessService;

    @Autowired
    private HboneCustBindBusinessService hboneBindBusinessService;

    @Autowired
    private HkCustBasicInfoOuterService hkCustInfoOuterService;

    @Autowired
    private HboneAcctInfoOuterService hboneAcctInfoOuterService;

    @Autowired
    private HboneDisAcctInfoOuterService hboneDisAcctInfoOuterService;

    @Autowired
    private CmCustBusinessService cmBusinessService;

    @Autowired
    private AbnormalCustRepository abnormalCustRepository;
    @Autowired
    private BookingCustRepository bookingCustRepository;
    @Autowired
    private CmConsultantRepository counsultantRepository;
    @Autowired
    private ConsCustSourceRepository custSourceRepository;

    @Autowired
    private CustMessageOperateExecutor custMessageOperateExecutor;

    /**
     * 开户消息  处理的分销code
     */
    private static final List<DisCodeEnum> USED_DISCODE_ENUM_LIST
            =Lists.newArrayList(DisCodeEnum.HOWBUY,DisCodeEnum.HZ);





    /**
     * @description:(根据crm客户信息，请求香港账户中心，查询是否有匹配的香港客户信息)
     * @param processedCustInfo
     * @return com.howbuy.crm.account.client.response.Response<java.lang.String>
     * @throws Exception
     * @throws
     * @since JDK 1.8
     */
    private HkAcctCustInfoVO searchMatchedHkCustByCustNo(CmConscustForAnalyseBO processedCustInfo) {
        Assert.notNull(processedCustInfo, "客户信息不能为空！");
        String mobileAreaCode = processedCustInfo.getMobileAreaCode();
        String mobileDigest = processedCustInfo.getMobileDigest();
        String idSignAreaCode = processedCustInfo.getIdSignAreaCode();
        String idNoDigest = processedCustInfo.getIdnoDigest();
        String idType = processedCustInfo.getIdtype();


        // 查找  能匹配的香港客户 信息
        // 根据填写的【区号】+【手机号】+【证件类型】+【证件号】，调香港账户中心接口，查询是否有匹配的香港客户号
        if (!StringUtils.isAnyEmpty(mobileAreaCode, mobileDigest, idNoDigest, idType)) {
            HkAcctCustInfoReqVO queryHkVo = new HkAcctCustInfoReqVO();
            queryHkVo.setMobileAreaCode(mobileAreaCode);
            queryHkVo.setMobileDigest(mobileDigest);
            // 因CRM缺失对IdSignAreaCode字段赋值的入口，暂时不传该字段（但这可能导致：查询出 证件类型、证件号码相同，但是证件区号不同的多个客户，而无法完成信息匹配） 2024-01-30
            //queryHkVo.setIdSignAreaCode(idSignAreaCode);
            queryHkVo.setIdNoDigest(idNoDigest);
            queryHkVo.setIdType(idType);
            queryHkVo.setCustStatList(AbnormalCustUtils.getValidHkCustStateList());
            PageVO<HkAcctCustInfoVO> fullMatchResp = hkCustInfoOuterService.queryHkCustInfoPage(queryHkVo);
            if (fullMatchResp != null &&
                    CollectionUtils.isNotEmpty(fullMatchResp.getRows()) &&
                    fullMatchResp.getRows().size() == 1) {
                return fullMatchResp.getRows().get(0);
            }
        }
        // 根据填写的【区号】+【手机号】，调香港账户中心接口，查询是否有匹配的香港客户号
        if (!StringUtils.isAnyEmpty(mobileAreaCode, mobileDigest)) {
            HkAcctCustInfoReqVO mobileQueryHk = new HkAcctCustInfoReqVO();
            mobileQueryHk.setMobileAreaCode(mobileAreaCode);
            mobileQueryHk.setMobileDigest(mobileDigest);
            mobileQueryHk.setCustStatList(AbnormalCustUtils.getValidHkCustStateList());
            PageVO<HkAcctCustInfoVO> mobileMatchResp = hkCustInfoOuterService.queryHkCustInfoPage(mobileQueryHk);
            //根据填写的【区号】+【手机号】+【证件类型】+【证件号】，调香港账户中心接口，查询是否有匹配的香港客户号
            if (mobileMatchResp != null &&
                    CollectionUtils.isNotEmpty(mobileMatchResp.getRows()) &&
                    // 若有且只有一条匹配
                    mobileMatchResp.getRows().size() == 1 &&
                    // 且香港客户号关联的证件号为空
                    StringUtils.isBlank(mobileMatchResp.getRows().get(0).getIdNoDigest())) {
                return mobileMatchResp.getRows().get(0);
            }
        }

        // 根据填写的【证件类型】+【证件号】，调香港账户中心接口，查询是否有匹配的香港客户号
        if (!StringUtils.isAnyEmpty(idNoDigest, idType)) {
            HkAcctCustInfoReqVO idQueryHk = new HkAcctCustInfoReqVO();
            // 因CRM缺失对IdSignAreaCode字段赋值的入口，暂时不传该字段（但这可能导致：查询出 证件类型、证件号码相同，但是证件区号不同的多个客户，而无法完成信息匹配） 2024-01-30
            //idQueryHk.setIdSignAreaCode(idSignAreaCode);
            idQueryHk.setIdNoDigest(idNoDigest);
            idQueryHk.setIdType(idType);
            idQueryHk.setCustStatList(AbnormalCustUtils.getValidHkCustStateList());
            PageVO<HkAcctCustInfoVO> idMatchResp = hkCustInfoOuterService.queryHkCustInfoPage(idQueryHk);
            //根据填写的【区号】+【手机号】+【证件类型】+【证件号】，调香港账户中心接口，查询是否有匹配的香港客户号
            if (idMatchResp != null &&
                    CollectionUtils.isNotEmpty(idMatchResp.getRows()) &&
                    idMatchResp.getRows().size() == 1) {
                return idMatchResp.getRows().get(0);
            }
        }

        return null;
    }


        
        /**
         * @description:(根据客户号，查找香港账户是否存在 唯一匹配的香港客户。 如果存在唯一，做绑定关系。)
         * @param custNo
         * @return com.howbuy.crm.account.client.response.Response<java.lang.String>
         * @author: haoran.zhang
         * @date: 2024/1/2 13:34
         * @since JDK 1.8
         */
        public Response<String> analyseHkBindAfterUpdate(String custNo,
                                                         String operator,
                                                         CustOperateChannelEnum channelEnum,
                                                         FullCustSourceEnum sourceEnum){
        //查找待处理客户
        CmConscustForAnalyseBO processedCustInfo=abnormalCustRepository.queryCustBOByCustNo(custNo);
        if(processedCustInfo==null){
            return Response.fail(String.format("根据custNo:%s 查询不到客户信息！",custNo));
        }

        HkAcctCustInfoVO hkCustInfo= searchMatchedHkCustByCustNo(processedCustInfo);
        log.info("根据客户号:{}，查询香港账户中心，匹配到的唯一香港客户信息:{}",custNo,JSON.toJSONString(hkCustInfo));
        if(hkCustInfo!=null){
            //建立绑定关系
            HkMessageCustInfoVO  analyseVo=new HkMessageCustInfoVO();
            analyseVo.setAbnormalSource(sourceEnum.getCode());
            analyseVo.setOperateChannel(channelEnum.getCode());
            analyseVo.setHkTxAcctNo(hkCustInfo.getHkTxAcctNo());
            analyseVo.setHboneNo(hkCustInfo.getHboneNo());
            analyseVo.setCreator(operator);
            associateHkAcctRelation(processedCustInfo, analyseVo);
        }

        return Response.ok();

    }


    /**
     * @description:(处理香港客户   关联的 hboneNo  入口)
     * @param analyseVo
     * @return com.howbuy.crm.account.client.response.Response<java.lang.String>
     * @author: haoran.zhang
     * @date: 2024/1/11 13:42
     * @since JDK 1.8
     */
    public Response<String> processExtraHboneAccount(HkMessageCustInfoVO analyseVo){
        return custMessageOperateExecutor.executeMessageAfterAnalyse(
                analyseVo,
                this::analyzeHboneAccount,
                Constants.INTO_ABNORMAL_HBONE,
                //处理
                resultVo -> {
                    CmConscustForAnalyseBO processHboneCust = resultVo.getProcessedCustInfo();
                    if (processHboneCust != null) {
                        HboneAcctRelationOptRequest bindOptRequest = new HboneAcctRelationOptRequest();
                        bindOptRequest.setCustNo(processHboneCust.getConscustno());
                        bindOptRequest.setHboneNo(analyseVo.getHboneNo());
                        bindOptRequest.setOperator(analyseVo.getCreator());
                        bindOptRequest.setOperateChannel(analyseVo.getOperateChannel());
                        bindOptRequest.setOperateSource(analyseVo.getAbnormalSource());
                        bindOptRequest.setRemark("");
                        //关联 客户 vs 一账通
                        Response<String> bindResp = hboneBindBusinessService.bindHboneTxAcct(bindOptRequest);
                        log.info("绑定一账通 vs 客户号，reuqest:{} ,绑定结果：{}", JSON.toJSONString(bindOptRequest), JSON.toJSONString(bindResp));
                    }
                    return Response.ok();
                });

    }

    /**
     * @description:(处理香港客户   关联的 hboneNo  异常数据)
     * @param analyseVo
     * @return com.howbuy.crm.account.client.response.Response<java.lang.String>
     * @throws Exception
     * @throws
     * @since JDK 1.8
     */
    public AbnormalAnalyseResultVO<HkMessageCustInfoVO> analyzeHboneAccount(HkMessageCustInfoVO analyseVo) {
        //无论 香港线上开户、线下开户，如果账户中心返回了【一账通号】，则需判断是否有异常数据
        //逻辑： 香港客户信息 处理后，随带的有hboneNo  . 此处默认  hkTxAcctNo vs custNo 关系确定 。准备创建  hboneNo vs custNo 关系 .
//               准备数据： hkTxAcctNo-->custA
//               因此校验1： custA.hboneNo vs hboneNo 是否存在：  custA.hboneNo!=hboneNo
//                  校验2： hboneNO-->custB   是否存在  custA!=custB
//
        Assert.notNull(analyseVo.getHkTxAcctNo(),"香港交易账户不能为空");
        String hkTxAcctNo=analyseVo.getHkTxAcctNo();
        String hboneNo=analyseVo.getHboneNo();

//        账户中心是否返回了【一账通号B】
//        （1）若未返回，则弹窗提示“香港开户成功”，流程结束
//        （2）若已返回，则继续判断④
        if(StringUtil.isEmpty(hboneNo)){
            log.info("渠道：【{}】，hkTxAcctNo:{} ，香港账户中心未返回一账通号，无需校验！",FullCustSourceEnum.getDescription(analyseVo.getAbnormalSource()),hkTxAcctNo);
            return AbnormalAnalyseResultVO.normalData(analyseVo);
        }
        //resultVo :
        AbnormalAnalyseResultVO<HkMessageCustInfoVO> returnAnalyseVo=AbnormalAnalyseResultVO.normalData(analyseVo);

//        判断④：当前投顾客户号绑定的【一账通号A】和账户中心返回的【一账通号B】是否一致
//        若当前投顾客户号绑定一账通，且与 analyseVo中hboneNo不一致
        CmConscustForAnalyseBO existCustA=abnormalCustRepository.queryCustBOByHkTxAcctNo(hkTxAcctNo);
        if(existCustA!=null &&
                StringUtil.isNotBlank(existCustA.getHboneNo())  &&
                !hboneNo.equals(existCustA.getHboneNo())){
            //账户中心返回的【一账通号B】落“一账通异常客户主表”，写入字段：
//        a、异常来源：香港开户（CRM）
//        b、异常描述：同时绑定一账通时，投顾客户号/一账通号被占用
//        c、异常时间：取落入异常表的时间，格式yyyymmdd hh:mm:ss
//        当前【投顾客户号A】落“一账通客户待关联表”，与主表数据相对应
            return AbnormalAnalyseResultVO.notNormalData(
                    analyseVo,
                    AbnormaSceneTypeEnum.BIND_HBONE_CUST_NO_OCCUPIED,
                    Lists.newArrayList(existCustA));
        }

        //当前投顾客户号未绑定一账通，则继续判断
        if(existCustA!=null &&
                StringUtil.isBlank(existCustA.getHboneNo())){
            List<CmConscustForAnalyseBO>  hboneCustList=abnormalCustRepository.queryCustBOByHboneNo(hboneNo);
            if(!CollectionUtils.isEmpty(hboneCustList)) {
                //（3）若已绑定其他【投顾客户号B】，则（此时不会自动换绑），则异常数据进“一账通异常客户表”：
//        账户中心返回的【一账通号】落“一账通异常客户主表”，写入字段：
//        a、异常来源：香港开户（CRM）
//        b、异常描述：同时绑定一账通时，投顾客户号/一账通号被占用
//        c、异常时间：取落入异常表的时间，格式yyyymmdd hh:mm:ss
//          【投顾客户号A】和【投顾客户号B】都落“一账通客户待关联表”，与主表数据相对应。
                hboneCustList.add(existCustA);
                return AbnormalAnalyseResultVO.notNormalData(
                        analyseVo,
                        AbnormaSceneTypeEnum.BIND_HBONE_CUST_NO_OCCUPIED,
                        hboneCustList);

            }else{
                returnAnalyseVo.setProcessedCustInfo(existCustA);
            }

        }
        return returnAnalyseVo;
    }



    /**
     * @description:(处理一账通客户   关联的 香港交易账号  入口)
     * @param analyseVo
     * @return com.howbuy.crm.account.client.response.Response<java.lang.String>
     * @author: haoran.zhang
     * @date: 2024/1/11 13:42
     * @since JDK 1.8
     */
    public Response<String> processExtraHkAccount(HboneMessageCustInfoVO analyseVo){
        return custMessageOperateExecutor.executeMessageAfterAnalyse(
                analyseVo,
                this::analyzeHkAccount,
                Constants.INTO_ABNORMAL_HK,
                //处理
                resultVo -> {
                    CmConscustForAnalyseBO processCust = resultVo.getProcessedCustInfo();
                    if (processCust != null) {
                        associateHkAcctRelation(processCust,resultVo.getAnalyseVo());
                    }
                    return Response.ok();
                });

    }
    /**
     * @description:(处理一账通客户   关联的 hkTxAcctNo  异常数据)
     *
     * @param analyseVo
     * @return com.howbuy.crm.account.client.response.Response<java.lang.String>
     * @throws Exception
     * @throws
     * @since JDK 1.8
     */
    public AbnormalAnalyseResultVO<HboneMessageCustInfoVO> analyzeHkAccount(HboneMessageCustInfoVO analyseVo) {
        //一账通 分销开户、实名注册 ，如果账户中心返回了【香港交易账户号】，则需判断是否有异常数据
        //逻辑： 一账通处理后，随带的有hkTxAcctNo . 此处默认  hboneNo vs custNo 关系确定 。
        //                                    准备创建  hkTxAcctNo vs custNo 关系 .
//            因此校验：     hkTxAcctNo-->custA vs  hboneNO-->custB
//                         校验1： 是否存在  custA!=custB
//                         校验1： custA.hboneNo vs hboneNo 是否存在  custA.hboneNo!=hboneNo
        String hboneNo=analyseVo.getHboneNo();
        Assert.notNull(analyseVo.getHboneNo(),"一账通不能为空");

        String hkTxAcctNo=analyseVo.getHkTxAcctNo();
//        判断⑥： 一账通消息[分销开户]消息是否推送了【香港客户号A】
//            （1）若未推送，则流程结束
//            （2）若已推送，则进入节点4-CRM判断是否同时绑定香港客户号
        if(StringUtil.isEmpty(hkTxAcctNo)){
            log.info("渠道：【{}】，hboneNo:{} ，账户中心未返回香港交易账号，无需校验！",
                    FullCustSourceEnum.getDescription(analyseVo.getAbnormalSource()),hboneNo);
            return AbnormalAnalyseResultVO.normalData(analyseVo);
        }

        //【香港客户号A】 --> 【投顾客户号A】
        CmConscustForAnalyseBO existCustA=abnormalCustRepository.queryCustBOByHkTxAcctNo(hkTxAcctNo);

        List<CmConscustForAnalyseBO>  hboneCustList=abnormalCustRepository.queryCustBOByHboneNo(hboneNo);
        //开发自定义流程： 历史数据存在hboneNo绑定多个hkTxAcctNo的情况，此处不做处理
        if(!CollectionUtils.isEmpty(hboneCustList) && hboneCustList.size()>1){
            log.error("【香港客户号/一账通绑定】消息处理异常：一账通号[{}]绑定了多个投顾客户号",hboneNo);
            return AbnormalAnalyseResultVO.
                    notNormalData(analyseVo,AbnormaSceneTypeEnum.HBONE_NO_MATCH_MULTIPLE_CUST_NO,hboneCustList);
        }
        //【hboneNo】 --> 【投顾客户号B】
        CmConscustForAnalyseBO existCustB=CollectionUtils.isEmpty(hboneCustList)?null:hboneCustList.get(0);
        if(existCustB==null){
            log.error("【香港客户号/一账通绑定】消息处理异常：一账通号[{}]未绑定投顾客户号!",hboneNo);
            return AbnormalAnalyseResultVO.
                    notNormalData(analyseVo,AbnormaSceneTypeEnum.BIND_HK_CUST_NO_OCCUPIED,hboneCustList);

        }

//        ①：【香港客户号A】是否已绑定【投顾客户号】
//        备注：【香港客户号A】是指分销开户消息推送过来的香港客户号，说明在账户中心【一账通号A】和【香港客户号A】已经绑定了
//（1）若未绑定，则继续判断②
//（2）若已绑定当前的【投顾客户号A】，则流程结束（此时三类账号已闭环关联）
//（3）若已绑定其他【投顾客户号B】（此时不会自动换绑），则异常数据进“香港异常客户表”：（注：此处是创建香港客户号和投顾客户号的关系，异常数据落“香港异常客户表”）
//【香港客户号A】落“香港异常客户主表”，写入字段：
//        a、异常来源：好买开户
//        b、异常描述：同时绑定投顾客户时，投顾客户号/一账通号被占用
//        c、异常时间：取落入异常表的时间，格式yyyymmdd hh:mm:ss
//【投顾客户号A】和【投顾客户号B】都落“香港客户待关联表”，与主表数据相对应。
        if(existCustA!=null &&
                StringUtil.isNotBlank(existCustA.getConscustno())  &&
                !existCustA.getConscustno().equals(existCustB.getConscustno())){
            return AbnormalAnalyseResultVO.notNormalData(
                    analyseVo,
                    AbnormaSceneTypeEnum.BIND_HK_CUST_NO_OCCUPIED,
                    Lists.newArrayList(existCustA,existCustB));
        }

//        判断②：【投顾客户号A】是否已关联【一账通号】
//（1）若未绑定，则绑定【投顾客户号A】和【香港客户号A】，并更新投顾客户信息，详见节点6-CRM更新投顾客户信息（取香港客户信息）此时不更新投顾客户信息 //20240102更新：
//        备注：该场景下，投顾客户信息取一账通分销开户信息即可；一账通绑定的香港客户号，可能是很早之前开户的，客户信息未必是最新的，所以不用再次更新投顾客户信息。
//（2）若已绑定推送的【香港客户号A】，则流程结束（此时三类账号已闭环关联）
//（3）若已绑定其他【香港客户号B】（此时不会自动换绑），则异常数据进“香港异常客户表”：（注：此处是创建香港客户号号和投顾客户号的关系，异常数据落“香港异常客户表”）
//【香港客户号A】落“香港异常客户主表”，写入字段：
//        a、异常来源：好买开户
//        b、异常描述：同时绑定投顾客户时，投顾客户号/一账通号被占用
//        c、异常时间：取落入异常表的时间，格式yyyymmdd hh:mm:ss
//【投顾客户号A】落“香港客户待关联表”，与主表数据相对应。
        if(existCustA!=null &&
                StringUtil.isBlank(existCustA.getHboneNo()) &&
                !hboneNo.equals(existCustA.getHboneNo()) ){
            return AbnormalAnalyseResultVO.notNormalData(
                    analyseVo,
                    AbnormaSceneTypeEnum.BIND_HK_CUST_NO_OCCUPIED,
                    Lists.newArrayList(existCustA,existCustB));
        }

        AbnormalAnalyseResultVO<HboneMessageCustInfoVO> result=AbnormalAnalyseResultVO.normalData(analyseVo);
        result.setProcessedCustInfo(existCustB);
        return result;
    }




    /**
     * @description:(流程处理中  香港客户异常数据 )
     * @param analyseVo	
     * @param sceneTypeEnum	
     * @param relatedCustList
     * @return void
     * @author: haoran.zhang
     * @date: 2023/12/18 16:46
     * @since JDK 1.8
     */
    public void insertHkProcessAbnormal(MessageCustInfoVO analyseVo,
                                        AbnormaSceneTypeEnum sceneTypeEnum,
                                        List<CmConscustForAnalyseBO> relatedCustList){
        hkAbnormalCustRepository.insertAbnormalCust(analyseVo,sceneTypeEnum,relatedCustList);
    }
    
    /**
     * @description: (流程处理中  香港客户异常数据 )
     * @param analyseVo
     * @param resultVo
     * @return void
     * @author: hongdong.xie
     * @date: 2025/4/15 19:35
     * @since JDK 1.8
     */
    public void insertHkProcessAbnormal(MessageCustInfoVO analyseVo,AbnormalAnalyseResultVO<HkMessageCustInfoVO> resultVo){
        hkAbnormalCustRepository.insertAbnormalCust(analyseVo,resultVo.getSceneTypeEnum(),resultVo.getRelatedCustList());
    }

    /**
     * @description:(流程处理中  一账通客户异常数据 )
     * @param analyseVo
     * @param sceneTypeEnum
     * @param relatedCustList
     * @return void
     * @author: haoran.zhang
     * @date: 2023/12/18 16:46
     * @since JDK 1.8
     */
    public void insertHboneProcessAbnormal(MessageCustInfoVO analyseVo,
                                        AbnormaSceneTypeEnum sceneTypeEnum,
                                        List<CmConscustForAnalyseBO> relatedCustList){
        hboneAbnormalCustRepository.insertAbnormalCust(analyseVo,sceneTypeEnum,relatedCustList);
    }


    /**
     * @description:(process处理中 关联到的唯一客户， 与 指定的 hkTxAcctNo关联。 如果客户当前hkTxAcctNo与 指定的hkTxAcctNo不一致。处理失败)
     * @param processedCustInfo 待处理客户对象
     * @param analyseVo  原入口消息
     * @return com.howbuy.crm.account.client.response.Response<java.lang.String>
     * @author: haoran.zhang
     * @date: 2023/12/18 13:28
     * @since JDK 1.8
     */
    public  Response<String> associateHkAcctRelation(CmConscustForAnalyseBO processedCustInfo,
                                                     MessageCustInfoVO analyseVo){
        log.info("待分析处理对象:{}，匹配到唯一客户号:{}，该客户绑定的香港交易账号:{}，消息的香港账号:{}，开始关联！",
                JSON.toJSONString(analyseVo),
                processedCustInfo.getConscustno(),
                processedCustInfo.getHkTxAcctNo(),
                analyseVo.getHkTxAcctNo());
        String hkTxAcctNo=analyseVo.getHkTxAcctNo();

        //当前 客户 已绑定的 hkTxAcctNo不为空
        if(StringUtil.isNotBlank(processedCustInfo.getHkTxAcctNo())) {
            if(Boolean.TRUE.equals(StringUtil.isEqual(hkTxAcctNo,processedCustInfo.getHkTxAcctNo()))){
                log.info("待分析处理对象:{}，匹配到唯一客户号:{}，该客户绑定的香港交易账号与消息的香港账号:{}一致，无需重新绑定！",
                        JSON.toJSONString(analyseVo),
                        processedCustInfo.getConscustno(),
                        processedCustInfo.getHkTxAcctNo());
                return Response.ok("无需处理！");
            }else{
                log.error("待分析处理对象:{}，匹配到唯一客户号:{}，该客户绑定的香港交易账号：{},与消息的香港账号:{}不一致！",
                        JSON.toJSONString(analyseVo),
                        processedCustInfo.getConscustno(),
                        processedCustInfo.getHkTxAcctNo(),
                        hkTxAcctNo);
                return Response.fail("异常数据处理！");
            }
        }
        //关联香港客户号 vs 投顾客户号
        HkAcctRelationOptRequest bindOptRequest=new HkAcctRelationOptRequest();
        bindOptRequest.setCustNo(processedCustInfo.getConscustno());
        bindOptRequest.setHkTxAcctNo(hkTxAcctNo);
        bindOptRequest.setOperator(analyseVo.getCreator());
        bindOptRequest.setRemark(null);
        bindOptRequest.setOperateChannel(analyseVo.getOperateChannel());
        bindOptRequest.setOperateSource(analyseVo.getAbnormalSource());
        return hkCustBindBusinessService.bindHkTxAcct(bindOptRequest);
    }

    /**
     * @description:(process处理中 关联到的唯一客户， 与 指定的 hkTxAcctNo关联。 如果客户当前hkTxAcctNo与 指定的hkTxAcctNo不一致。处理失败)
     * @param processedCustInfo 待处理客户对象
     * @param analyseVo  原入口消息
     * @return com.howbuy.crm.account.client.response.Response<java.lang.String>
     * @author: haoran.zhang
     * @date: 2023/12/18 13:28
     * @since JDK 1.8
     */
    public  Response<String> associateHboneAcctRelation(CmConscustForAnalyseBO processedCustInfo,
                                                        HboneMessageCustInfoVO analyseVo){
        log.info("待分析处理对象:{}，匹配到唯一客户号:{}，该客户绑定的一账通账号:{}，消息的一账通账号:{}，开始关联！",
                JSON.toJSONString(analyseVo),
                processedCustInfo.getConscustno(),
                processedCustInfo.getHkTxAcctNo(),
                analyseVo.getHboneNo());
        String hboneNo=analyseVo.getHboneNo();
        String custNo=processedCustInfo.getConscustno();

        //当前 客户 已绑定的 hkTxAcctNo不为空
        if(StringUtil.isNotBlank(processedCustInfo.getHboneNo())) {
            if(Boolean.TRUE.equals(StringUtil.isEqual(hboneNo,processedCustInfo.getHboneNo()))){
                log.info("待分析处理对象:{}，匹配到唯一客户号:{}，该客户绑定的一账通账号与消息的一账通账号:{}一致，无需重新绑定！",
                        JSON.toJSONString(analyseVo),
                        processedCustInfo.getConscustno(),
                        hboneNo);
                return Response.ok("无需处理！");
            }else{
                log.error("待分析处理对象:{}，匹配到唯一客户号:{}，该客户绑定的一账通账号：{},与消息的一账通账号:{}不一致！",
                        JSON.toJSONString(analyseVo),
                        processedCustInfo.getConscustno(),
                        processedCustInfo.getHboneNo(),
                        hboneNo);
                return Response.fail("异常数据处理！");
            }
        }
        //关联 一账通账号 vs 投顾客户号
        HboneAcctRelationOptRequest bindOptRequest=new HboneAcctRelationOptRequest();
        bindOptRequest.setCustNo(custNo);
        bindOptRequest.setHboneNo(hboneNo);
        bindOptRequest.setOperator(analyseVo.getCreator());
        bindOptRequest.setRemark(null);
        bindOptRequest.setOperateChannel(analyseVo.getOperateChannel());
        bindOptRequest.setOperateSource(analyseVo.getAbnormalSource());
        return hboneBindBusinessService.bindHboneTxAcct(bindOptRequest);
    }

    /**
     * @description:(根据一账通号 查询账户中心客户信息 )
     * @param hboneNo
     * @return com.howbuy.crm.account.client.response.custinfo.HboneAcctCustDetailInfoVO
     * @author: haoran.zhang
     * @date: 2023/12/20 19:05
     * @since JDK 1.8
     */
    private HboneAcctCustDetailInfoVO queryHboneCustDetailInfo(String hboneNo){
        return hboneAcctInfoOuterService.queryHboneCustDetailInfo(hboneNo);
    }


    /**
     * 消息 中的 disCode  特殊处理，选取 使用的 disCode枚举
     * @param disCode
     * @return
     */
    private DisCodeEnum getUsedDisCodeEnum(String disCode){
        DisCodeEnum disCodeEnum=DisCodeEnum.getEnum(disCode);
        if(disCodeEnum==null||
                (!USED_DISCODE_ENUM_LIST.contains(disCodeEnum)) ){
            log.info("分销代码：{} 在消息处理中 不适用，使用默认分销代码：{}",
                    disCode,DisCodeEnum.HOWBUY.getCode());
            disCodeEnum=DisCodeEnum.HOWBUY;
        }
        return disCodeEnum;
    }


    /**
     * @description:(根据一账通号 查询账户中心客户信息 包含分销信息)
     * @param hboneNo
     * @return com.howbuy.crm.account.client.response.custinfo.HboneAcctCustDetailInfoVO
     * @author: haoran.zhang
     * @date: 2024/1/27 12:03
     * @since JDK 1.8
     */
    private HboneCustWithDisInfoVO queryHboneCustWithDisInfo(String hboneNo,DisCodeEnum disCodeEnum){
        return hboneDisAcctInfoOuterService.queryHboneCustWithDisInfo(hboneNo,disCodeEnum);
    }

    /**
     * @description:(查询 账户中心  客户信息 敏感信息)
     * @param hboneNo
     * @return com.howbuy.crm.account.client.response.custinfo.HboneAcctCustSensitiveInfoVO
     * @author: haoran.zhang
     * @date: 2023/12/21 17:11
     * @since JDK 1.8
     */
    private HboneAcctCustSensitiveInfoVO queryHboneCustSensitiveInfo(String hboneNo){
        return hboneAcctInfoOuterService.queryHboneCustSensitiveInfo(hboneNo);
    }


   /**
    * @description:(是否需要生成呼出任务)
    * @param analyseBo	
    * @param hkTxAcctNo
    * @return boolean
    * @author: haoran.zhang
    * @date: 2023/12/28 20:20
    * @since JDK 1.8
    */
    private boolean isNeedBokingTask(CmConscustForAnalyseBO   analyseBo,String hkTxAcctNo){
        // 1、判断①：【香港客户号】是否已绑定【投顾客户号】
        //（1）若未绑定，则流程结束 （不生成呼出任务，PS：这类客户在异常表里）
        if(analyseBo==null){
            log.error("根据香港交易账号：{} 未查询到客户信息，无需生成呼出任务！",hkTxAcctNo);
            return false;
        }
        String consCode=analyseBo.getConsCode();
        //若归属于虚拟投顾 或 投顾为空，则生成呼出任务

        if(StringUtil.isEmpty(consCode)){
            return  true;
        }
        // 判断条件：同时满足以下条件，则归属于虚拟投顾 . 需要生成呼出任务
//        条件1：所在部门归属【客户服务部-高端】或【客户服务部-零售】
//        条件2：是否虚拟投顾 = 是
        CmConsultantPO consultantPO=counsultantRepository.selectValidConsultant(consCode);
        if(consultantPO==null){
            log.error("根据投顾号：{} 未查询到投顾信息，需生成呼出任务！",consCode);
            return true;
        }
        //虚拟投顾 且
        if(YesOrNoEnum.YES.getCode().equals(consultantPO.getIsvirtual())){
          return true;
        }

        return false;
    }

    /**
     * @description:(插入客服回访任务 数据)
     * @param analyseVo	
     * @param hkTxAcctNo
     * @return void
     * @author: haoran.zhang
     * @date: 2023/12/28 19:36
     * @since JDK 1.8
     */
    public void insertBookingCust(HkMessageCustInfoVO analyseVo,
                                  String hkTxAcctNo){
//        前置说明：CRM先处理香港线上开户消息，再判断是否要生成呼出任务
        CmConscustForAnalyseBO   analyseBo=abnormalCustRepository.queryCustBOByHkTxAcctNo(hkTxAcctNo);
        //无需 生成 呼出任务
        if(!isNeedBokingTask(analyseBo,hkTxAcctNo)){
            log.error("根据香港交易账号：{} 未查询到客户信息，无需生成呼出任务！",hkTxAcctNo);
            return;
        }
        //客户信息
        String custNo=analyseBo.getConscustno();

        CmConsCustWithCipherVO custInfo=consCustInfoRepository.queryCustWithCipherByCustNo(custNo);
        if(custInfo==null){
            log.error("根据客户号：{} 未查询到客户信息！",custNo);
            return;
        }
        CmBookingcustPO bookingCustPo = new CmBookingcustPO();
        bookingCustPo.setConscustno(custNo);
        bookingCustPo.setHboneNo(custInfo.getHboneNo());
        bookingCustPo.setCustname(custInfo.getCustname());
        bookingCustPo.setMobileDigest(custInfo.getMobileDigest());
        bookingCustPo.setMobileMask(custInfo.getMobileMask());
        bookingCustPo.setMobileCipher(custInfo.getMobileCipher());
        //来源系统：1-CMS;2-ACC
        bookingCustPo.setSourceSys(BOOKING_SOURCE_SYS_ACCT);

        //产品已确认
        //regOutletCode ： 交易账户开户网点
        bookingCustPo.setRegOutletCode(analyseVo.getOpenOutletCode());
        //hboneRegOutletCode： 注册网点
        bookingCustPo.setHboneRegOutletCode("");
        //outletCode：客户提交高端KYC问卷的网点号
        bookingCustPo.setOutletCode("");

        // 预约类型(1:私募预约 ;2:合作预约)
        bookingCustPo.setBookingtype(BOOKING_TYPE_SIMU);

        CsActivityTypeEnum usedActivityEnum=CsActivityTypeEnum.HOWBUY_HK_OPEN;
        bookingCustPo.setActivitytype(usedActivityEnum.getCode());
        // 预约内容
        bookingCustPo.setBookingcontent(usedActivityEnum.getDescription());
        //和规状态 Y
        bookingCustPo.setQualifiedStatus(YesOrNoEnum.YES.getSignal());
        // 无线渠道（1:掌基、2:臻财、3:M站）
        bookingCustPo.setWirelessChannel(WirelessChannelEnum.M_STATION.getCode());
        String dateTime=DateUtil.date2String(new Date(),DateUtil.DEFAULT_DATESFM);
        // 预约流水号
        bookingCustPo.setBookingserialno(String.join("",BOOKING_PREFIX,dateTime));

        bookingCustRepository.createBookingCust(bookingCustPo);
    }

    /**
     * @description:(一账通侧 信息  更新  crm客户  )
     * @param analyseVo	
     * @param processedCustInfo
     * @return void
     * @author: haoran.zhang
     * @date: 2023/12/21 17:52
     * @since JDK 1.8
     */
    public  Response<String> updateCustInfoByHbone(HboneMessageCustInfoVO  analyseVo,
                                                                                CmConscustForAnalyseBO processedCustInfo ){

        // 因为需要使用 disCode 字段。所以此方法 无法支持泛型 ： <T extends MessageCustInfoVO>
        String custNo=processedCustInfo.getConscustno();
        //目标，是根据绑定的【一账通号A】，更新【投顾客户号A】的客户基本信息
        String hboneNo=analyseVo.getHboneNo();

        Assert.notNull(custNo,"客户号不能为空！");
        Assert.notNull(hboneNo,"一账通号不能为空！");
        log.info("根据一账通号：{} 更新客户：{} 客户信息！",hboneNo,custNo);


//        判断①：取【一账通号A】的【客户姓名、手机号、证件号】（好买账户中心数据），判断3个字段是否都不为空
        DisCodeEnum usedDisCodeEnum=getUsedDisCodeEnum(analyseVo.getDisCode());
        HboneCustWithDisInfoVO  custWithDisInfoVO=queryHboneCustWithDisInfo(hboneNo,usedDisCodeEnum);
        HboneDisMainCustInfoVO hboneInfo= custWithDisInfoVO.getMainCustInfo();

        if(StringUtil.isEmpty(hboneInfo.getCustName())||
//                StringUtil.isEmpty(hboneInfo.getMobileDigest())||
                StringUtil.isEmpty(hboneInfo.getIdNoDigest())){
            return Response.fail(String.format("一账通号：%s 【客户姓名、手机号、证件号】有空值！", hboneNo));
        }
//        判断②：取【一账通号A】的【手机号，证件类型，证件号】（好买账户中心数据），判断CRM是否存在重复客户
//        满足以下任一条件，则认为存在重复客户
//        条件1：【手机号】相同
        List<CmConscustForAnalyseBO> mobileExistList=Lists.newArrayList();
        //判断是否需要校验 手机号码重复： 若客户类型=机构/产品，或 一账通手机号为空，则不需要进行该条件的判断
//        boolean needValidateMobile  =
//                ConsCustInvestTypeEnum.PERSONAL.getCode().equals(hboneInfo.getInvstType()) &&
//                        StringUtil.isNotBlank(hboneInfo.getMobileDigest());
         boolean noNeedValidateMobile  =
                StringUtil.isEmpty(hboneInfo.getMobileDigest())  ||
                        ConsCustInvestTypeEnum.INSTITUTION.getCode().equals(hboneInfo.getInvstType()) ||
                        ConsCustInvestTypeEnum.PRODUCT.getCode().equals(hboneInfo.getInvstType());
        //需要校验 手机号码重复， 且 手机号码不为空
        if(!noNeedValidateMobile){
            mobileExistList=abnormalCustRepository.searchMobileExistList(hboneInfo.getInvstType(),
                    hboneInfo.getMobileAreaCode(),
                    hboneInfo.getMobileDigest(),custNo);
        }

        if(CollectionUtils.isNotEmpty(mobileExistList)){
            //当前客户 也落入异常关联表表
            mobileExistList.add(processedCustInfo);
            insertHboneProcessAbnormal(analyseVo, AbnormaSceneTypeEnum.CRM_REPEAT_CUST_WARNING,mobileExistList);
            return Response.fail(String.format("一账通号：%s,存在重复客户！手机号：%s",
                    hboneNo,hboneInfo.getMobileDigest()));
        }
//        条件2：【证件类型+证件号】相同
        List<CmConscustForAnalyseBO> idExistList=
                abnormalCustRepository.searchIdExistList(
                        hboneInfo.getInvstType(),
                        hboneInfo.getIdType(),
                        hboneInfo.getIdSignAreaCode(),
                        hboneInfo.getIdNoDigest(),
                        custNo);
        if(CollectionUtils.isNotEmpty(idExistList)){
            //当前客户 也落入异常关联表表
            idExistList.add(processedCustInfo);
            insertHboneProcessAbnormal(analyseVo, AbnormaSceneTypeEnum.CRM_REPEAT_CUST_WARNING,idExistList);
            return Response.fail(String.format("一账通号：%s,存在重复客户！证件号：%s",
                    hboneNo,hboneInfo.getIdNoDigest()));
        }
//        更新逻辑：仅更新好买账户中心有值，且与CRM投顾客户信息不一致的字段（投顾客户号为空的字段按不一致处理）。
//        涉及字段：姓名，客户类型，手机区号（默认写入86），手机号，证件类型，证件号，证件有效日期，性别，生日，国籍/地区，省市县，地址，邮箱，职业

        CmConscustPO currentCustPo=consCustInfoRepository.selectPoByCustNo(custNo);

        //查找 一账通账户中心  敏感信息 用于更新
        HboneAcctCustSensitiveInfoVO sensitiveInfo = queryHboneCustSensitiveInfo(hboneNo);


        UpdateConsCustRequest updateCustReq=new UpdateConsCustRequest();
        updateCustReq.setCustNo(custNo);
        updateCustReq.setOperator(analyseVo.getCreator());
        //更新客户类型
        if(StringUtil.isNotBlank(hboneInfo.getInvstType())
                && Boolean.FALSE.equals(StringUtil.isEqual(currentCustPo.getInvsttype(),hboneInfo.getInvstType()))){
            updateCustReq.setInvsttype(hboneInfo.getInvstType());
        }
        //更新客户姓名
        if(StringUtil.isNotBlank(hboneInfo.getCustName())
                && Boolean.FALSE.equals(StringUtil.isEqual(currentCustPo.getCustname(),hboneInfo.getCustName()))){
            updateCustReq.setCustname(hboneInfo.getCustName());
        }
        //是否更新手机号 ： 必须要求 手机号码 与  手机区号  有一个不一致
        if(StringUtil.isNotBlank(hboneInfo.getMobileDigest())){
            boolean isUpdateMobile=!(StringUtil.isEqual(currentCustPo.getMobileAreaCode(),hboneInfo.getMobileAreaCode())
                    && StringUtil.isEqual(currentCustPo.getMobileDigest(),hboneInfo.getMobileDigest()));
            if(isUpdateMobile){
                updateCustReq.setMobileAreaCode(hboneInfo.getMobileAreaCode());
                //获取明文手机号
                String mobile=hboneAcctInfoOuterService.queryHboneSensitiveMobile(hboneNo);
                //手机号码 明文信息
                updateCustReq.setMobile(mobile);
            }
        }
        //是否更新 证件信息 ： 必须要求 证件类型 、证件地区码、 证件号 有一个不一致
        boolean isUpdateId=!(StringUtil.isEqual(currentCustPo.getIdtype(),hboneInfo.getIdType())
                && StringUtil.isEqual(currentCustPo.getIdSignAreaCode(),hboneInfo.getIdSignAreaCode())
                && StringUtil.isEqual(currentCustPo.getIdnoDigest(),hboneInfo.getIdNoDigest()));
        if(isUpdateId){
            updateCustReq.setIdtype(hboneInfo.getIdType());
            updateCustReq.setIdSignAreaCode(hboneInfo.getIdSignAreaCode());
            //证件号码 明文信息
            updateCustReq.setIdNo(sensitiveInfo.getIdNo());
            //证件有效日期 截止日期
            updateCustReq.setValiditydt(hboneInfo.getIdValidityEnd());
            //证件有效日期 起始日期
            updateCustReq.setValidityst(hboneInfo.getIdValidityStart());
            //证件是否长期有效 1-长期 0-非长期
            updateCustReq.setValidity(hboneInfo.getIdAlwaysValidFlag());
        }
        // 性别 gender
        if(StringUtil.isNotBlank(hboneInfo.getGender())
                && Boolean.FALSE.equals(StringUtil.isEqual(currentCustPo.getGender(),hboneInfo.getGender()))){
            updateCustReq.setGender(hboneInfo.getGender());
        }
        // 生日
        if(StringUtil.isNotBlank(hboneInfo.getBirthday())
                && Boolean.FALSE.equals(StringUtil.isEqual(currentCustPo.getBirthday(),hboneInfo.getBirthday()))){
            updateCustReq.setBirthday(hboneInfo.getBirthday());
        }

        //职业
        if(StringUtil.isNotBlank(hboneInfo.getVocation())
                && Boolean.FALSE.equals(StringUtil.isEqual(currentCustPo.getVocation(),hboneInfo.getVocation()))){
            //邮箱 明文信息
            updateCustReq.setVocation(hboneInfo.getVocation());
        }

        //获取分销信息
        HboneDisCustInfoVO  disHboneInfo=null;
        //获取 分销层 敏感信息
        HboneDisCustSensitiveInfoVO disSensitiveInfo=null;
        if(CollectionUtils.isNotEmpty(custWithDisInfoVO.getDisCustInfoList())){
            disHboneInfo=custWithDisInfoVO.getDisCustInfoList().stream().filter(
                    disCustInfoVO -> StringUtil.isEqual(disCustInfoVO.getDisCode(),usedDisCodeEnum.getCode())
            ).findFirst().orElse(null);
        }
        if(disHboneInfo!=null){
            //获取 分销层 敏感信息
            disSensitiveInfo=hboneDisAcctInfoOuterService.queryHboneDisSensitiveInfo(hboneNo, usedDisCodeEnum);
            // 邮箱
            if(StringUtil.isNotBlank(disHboneInfo.getEmailDigest())
                    && Boolean.FALSE.equals(StringUtil.isEqual(currentCustPo.getEmailDigest(),disHboneInfo.getEmailDigest()))){
                //邮箱 明文信息
                updateCustReq.setEmail(disSensitiveInfo.getEmail());
            }
        }else{
            log.error("一账通号：{}，分销:{} ,分销层信息为空，个人客户不更新分销层信息：[省份、市、县、地址、邮箱]！",hboneNo,usedDisCodeEnum.getCode());
        }

        // 省市县  取 账户中心  【中文地址】  现住址省份、市、县 编码
        //【A0225】兼容非个人客户地址取值
        SyncHboneAttrInfoVO  syncVo= getUsedCustAddrInfo(hboneInfo,disHboneInfo,disSensitiveInfo);
        // 国籍/地区
        if(StringUtil.isNotBlank(syncVo.getNationCode())
                && Boolean.FALSE.equals(StringUtil.isEqual(currentCustPo.getNationCode(),syncVo.getNationCode()))){
            updateCustReq.setNationCode(syncVo.getNationCode());
        }
        // 省市县  取 账户中心  【中文地址】  现住址省份、市、县 编码
        if(StringUtil.isNotBlank(syncVo.getProvCode())
                && Boolean.FALSE.equals(StringUtil.isEqual(currentCustPo.getProvcode(),syncVo.getProvCode()))){
            updateCustReq.setProvcode(syncVo.getProvCode());
        }
        if(StringUtil.isNotBlank(syncVo.getCityCode())
                && Boolean.FALSE.equals(StringUtil.isEqual(currentCustPo.getCitycode(),syncVo.getCityCode()))){
            updateCustReq.setCitycode(syncVo.getCityCode());
        }
        if(StringUtil.isNotBlank(syncVo.getCountyCode())
                && Boolean.FALSE.equals(StringUtil.isEqual(currentCustPo.getCountyCode(),syncVo.getCountyCode()))){
            updateCustReq.setCountyCode(syncVo.getCountyCode());
        }
        // 地址
        if(StringUtil.isNotBlank(syncVo.getAddrDigest())
                && Boolean.FALSE.equals(StringUtil.isEqual(currentCustPo.getAddrDigest(),syncVo.getAddrDigest()))){
            //地址 明文信息
            updateCustReq.setAddr(syncVo.getAddr());
        }

        log.info("更新投顾客户信息，custNo:{},更新属性：{}",custNo, JSON.toJSONString(updateCustReq));
        Response<UpdateConsCustRespVO>  updateResp = cmBusinessService.updateCust(updateCustReq);
        log.info("更新投顾客户信息，custNo:{},更新结果：{}",custNo, JSON.toJSONString(updateResp));
        if(!updateResp.isSuccess()){
            return Response.fail(updateResp.getDescription());
        }
        return Response.ok();

    }

    /**
     * @description:(根据客户类型，获取同步到crm使用到的属性)
     * @param hboneInfo	 不允许为空 NOT NULL
     * @param disHboneInfo	允许为空
     * @param disSensitiveInfo 允许为空
     * @return com.howbuy.crm.account.service.vo.custinfo.UsedCustAddrVO
     * @author: haoran.zhang
     * @date: 2024/9/25 17:05
     * @since JDK 1.8
     */
    private SyncHboneAttrInfoVO getUsedCustAddrInfo(HboneDisMainCustInfoVO hboneInfo,
                                                    HboneDisCustInfoVO  disHboneInfo,
                                                    HboneDisCustSensitiveInfoVO disSensitiveInfo){
        SyncHboneAttrInfoVO usedVo=new SyncHboneAttrInfoVO();
        ConsCustInvestTypeEnum investTypeEnum=ConsCustInvestTypeEnum.getEnum(hboneInfo.getInvstType());
      //【A0225】兼容非个人客户地址取值逻辑
        if(investTypeEnum!=ConsCustInvestTypeEnum.PERSONAL){
            //机构客户取值逻辑  使用注册 相关地址
//           regCountry --机构注册国家
            usedVo.setNationCode(hboneInfo.getRegCountry());
            //获取 regAddr -机构注册地址-->  客户地址 .该字段账户中心明文，不会加密。 crm会和个人客户一样，加密
            String addr= hboneInfo.getRegAddr();
            usedVo.setAddr(addr);
            usedVo.setAddrDigest(DigestUtil.digest(addr));
            usedVo.setAddrMask(MaskUtil.maskAddr(addr));
            // regProvCode --机构注册省份  --> 客户省
            usedVo.setProvCode(hboneInfo.getRegProvCode());
            // regCityCode--机构注册城市  --> 客户市
            usedVo.setCityCode(hboneInfo.getRegCityCode());
            // regCountyCode–机构注册县 --> 客户县code
            usedVo.setCountyCode(hboneInfo.getRegCountyCode());
        }else{
            //个人客户取值逻辑
            usedVo.setNationCode(hboneInfo.getNationality());
            // 省市县  取 账户中心  【中文地址】  现住址省份、市、县 编码
            usedVo.setAddr(disSensitiveInfo==null?null:disSensitiveInfo.getAddr());
            if(disHboneInfo!=null){
                usedVo.setAddrDigest(disHboneInfo.getAddrDigest());
                usedVo.setAddrMask(disHboneInfo.getAddrMask());
                usedVo.setProvCode(disHboneInfo.getProvCode());
                usedVo.setCityCode(disHboneInfo.getCityCode());
                usedVo.setCountyCode(disHboneInfo.getCountyCode());
            }
        }
        log.info("根据一账通号：{}，客户类型：{}, 获取同步到crm使用到的[国家、省份、市、县、地址]属性信息：{}",
                hboneInfo.getHboneNo(), investTypeEnum, JSON.toJSONString(usedVo));
        return usedVo;
    }

    /**
     * @description:(根据 一账通 创建 客户号)
     * @param analyseVo  消息对象
     * @return com.howbuy.crm.account.client.response.Response<java.lang.String>
     * @author: haoran.zhang
     * @date: 2023/12/27 19:25
     * @since JDK 1.8
     */
    public Response<String> createCustInfoByHbone(HboneMessageCustInfoVO analyseVo){
        String hboneNo=analyseVo.getHboneNo();
        String operateSource=analyseVo.getAbnormalSource();
        String operateChannel=analyseVo.getOperateChannel();

        //获取 分销
        DisCodeEnum usedDisCodeEnum=getUsedDisCodeEnum(analyseVo.getDisCode());

        log.info("根据一账通号：{}，分销code:{} 创建客户信息！",hboneNo,usedDisCodeEnum.getCode());
        // 取【香港客户号A】的【客户姓名、手机号、证件号】（香港账户中心数据），


        HboneCustWithDisInfoVO  custWithDisInfoVO=queryHboneCustWithDisInfo(hboneNo,usedDisCodeEnum);
        HboneDisMainCustInfoVO hboneCustDetailVo= custWithDisInfoVO.getMainCustInfo();

        //查找 一账通账户中心  敏感信息 用于更新
        HboneAcctCustSensitiveInfoVO sensitiveInfo = queryHboneCustSensitiveInfo(hboneNo);

        CreateConsCustRequest createCustReq=new CreateConsCustRequest();
        createCustReq.setOperator(Constants.OPERATOR_SYS);
        //客户类型
        createCustReq.setInvsttype(hboneCustDetailVo.getInvstType());
        //更新客户姓名
        createCustReq.setCustname(hboneCustDetailVo.getCustName());

        if(StringUtil.isNotBlank(hboneCustDetailVo.getMobileDigest())){
            //手机号 ：
            createCustReq.setMobileAreaCode(hboneCustDetailVo.getMobileAreaCode());
            //获取明文手机号
            String mobile=hboneAcctInfoOuterService.queryHboneSensitiveMobile(hboneNo);
            //手机号码 明文信息
            createCustReq.setMobile(mobile);
        }
        if(StringUtil.isNotBlank(hboneCustDetailVo.getIdNoDigest())){
            createCustReq.setIdtype(hboneCustDetailVo.getIdType());
            createCustReq.setIdSignAreaCode(hboneCustDetailVo.getIdSignAreaCode());
            //证件号码 明文信息
            createCustReq.setIdNo(sensitiveInfo.getIdNo());
        }
        //证件有效日期
        createCustReq.setValiditydt(hboneCustDetailVo.getIdValidityEnd());
        //证件是否长期有效 1-长期 0-非长期
        createCustReq.setValidity(hboneCustDetailVo.getIdAlwaysValidFlag());

        // 性别 gender
        createCustReq.setGender(hboneCustDetailVo.getGender());
        // 生日
        createCustReq.setBirthday(hboneCustDetailVo.getBirthday());
        //职业
        createCustReq.setVocation(hboneCustDetailVo.getVocation());

        //获取分销信息
        HboneDisCustInfoVO  disHboneInfo=null;
        HboneDisCustSensitiveInfoVO disSensitiveInfo=null;
        if(CollectionUtils.isNotEmpty(custWithDisInfoVO.getDisCustInfoList())){
            disHboneInfo=custWithDisInfoVO.getDisCustInfoList().stream().filter(
                    disCustInfoVO -> StringUtil.isEqual(disCustInfoVO.getDisCode(),usedDisCodeEnum.getCode())
            ).findFirst().orElse(null);
        }
        if(disHboneInfo!=null) {
            //获取 分销层 敏感信息
            disSensitiveInfo=hboneDisAcctInfoOuterService.queryHboneDisSensitiveInfo(hboneNo, usedDisCodeEnum);
            // 邮箱
            if(StringUtil.isNotBlank(disHboneInfo.getEmailDigest())){
                //邮箱 明文信息
                createCustReq.setEmail(disSensitiveInfo.getEmail());
            }
        }else {
            log.error("一账通号：{}，分销:{} ,分销层信息为空，个人客户不更新分销层信息：[省份、市、县、地址、邮箱]！",hboneNo,usedDisCodeEnum.getCode());
        }

        // 省市县  取 账户中心  【中文地址】  现住址省份、市、县 编码
        //【A0225】兼容非个人客户地址取值
        SyncHboneAttrInfoVO  syncVo= getUsedCustAddrInfo(hboneCustDetailVo,disHboneInfo,disSensitiveInfo);
        // 国籍/地区
        createCustReq.setNationCode(syncVo.getNationCode());
        // 省市县  取 账户中心  【中文地址】  现住址省份、市、县 编码
        createCustReq.setProvcode(syncVo.getProvCode());
        createCustReq.setCitycode(syncVo.getCityCode());
        createCustReq.setCountyCode(syncVo.getCountyCode());
        //地址 明文信息
        if(StringUtil.isNotBlank(syncVo.getAddrDigest())){
            //地址 明文信息
            createCustReq.setAddr(syncVo.getAddr());
        }

        //TODO: 开户网点号 是否 正确使用
        String sourceNo=hboneCustDetailVo.getRegOutletCode();
        CmSourceInfoPO sourcePo=
                custSourceRepository.querySourceByCode(sourceNo,CustSourceCodeEnum.OTHER_SOURCE.getCode());
        log.info("根据一账通号：{},开户网点号：{} ，查询到的开户网点信息：{}",hboneNo,sourceNo,JSON.toJSONString(sourcePo));
        createCustReq.setCustsource(sourcePo.getSourceNo());
        SpecialConsCodeEnum consCodeEnum=getConsCodeBySourcePo(sourcePo, hboneCustDetailVo.getInvstType());
        createCustReq.setConsCode(consCodeEnum.getCode());
        createCustReq.setOperateSource(operateSource);
        createCustReq.setOperateChannel(operateChannel);
        Response<CreateConsCustRespVO> createCustResp=cmBusinessService.insertCust(createCustReq);
        CreateConsCustRespVO createRespVo=createCustResp.getData();
        if( !createCustResp.isSuccess() || StringUtil.isEmpty(createRespVo.getCustNo())){
            return  Response.fail(createCustResp.getDescription());
        }
        String custNo = createRespVo.getCustNo();
        return Response.ok("",custNo);

    }

    /**
     * @description:(根据 来源 ，获取默认投顾)
     * @param sourcePo	
     * @param invsttype
     * @return com.howbuy.crm.account.client.enums.custinfo.SpecialConsCodeEnum
     * @author: haoran.zhang
     * @date: 2024/1/5 20:44
     * @since JDK 1.8
     */
    public SpecialConsCodeEnum getConsCodeBySourcePo(CmSourceInfoPO sourcePo, String invsttype) {
        if(sourcePo==null){
            return null;
        }
        String sourceNo = sourcePo.getSourceNo();
        //sourceNo=RO1903W01， 返回 【关爱通客户】
        if(CustSourceCodeEnum.APP_H5_GUAN_AI_TONG.getCode().equals(sourceNo)){
            return SpecialConsCodeEnum.GAT_KH;
        }
        //非个人类型客户， 返回 【机构客户】
        if(!ConsCustInvestTypeEnum.PERSONAL.getCode().equals(invsttype)){
            return SpecialConsCodeEnum.JG_KH_ORG;
        }
        //以下为 个人类型客户 判断逻辑：
        CustSourceFirstLevelEnum firstLevelEnum=CustSourceFirstLevelEnum.getEnum(sourcePo.getFirstLevelCode());
        if (CustSourceFirstLevelEnum.C==firstLevelEnum) {
            return SpecialConsCodeEnum.CXGN_CSLS;
        }
        if (CustSourceFirstLevelEnum.K==firstLevelEnum) {
            return SpecialConsCodeEnum.ZSJJN_CSLS;
        }
        if(CustSourceFirstLevelEnum.L==firstLevelEnum ||
                CustSourceFirstLevelEnum.H==firstLevelEnum ||
                CustSourceFirstLevelEnum.F==firstLevelEnum){
            return SpecialConsCodeEnum.HZN_CSLS;
        }
        return SpecialConsCodeEnum.WZQTN_CSLS;
    }



   /**
    * @description:(以香港账户侧为准，更新投顾客户[手机号码]信息)
    * @param hkTxAcctNo
    * @param custNo
    * @param operator
    * @return void
    * @author: haoran.zhang
    * @date: 2023/12/22 11:08
    * @since JDK 1.8
    */
    public Response<String> updateMobileByHk(String hkTxAcctNo,
                                             String custNo,
                                             String operator){
        Assert.notNull(hkTxAcctNo,"香港交易账号不能为空！");
        //更新逻辑：
        HkAcctCustDetailInfoVO hkCustDetailVo=hkCustInfoOuterService.queryHkCustDetailInfo(hkTxAcctNo);
//        若账户中心的【手机号】为空，则不更新CRM手机号
        if(StringUtil.isEmpty(hkCustDetailVo.getMobileDigest())){
            return Response.ok();
        }
//        若账户中心的【手机号】不为空，则取账户中心【手机区号、手机号】覆盖CRM的投顾客户信息
        CmConscustPO currentCustPo=consCustInfoRepository.selectPoByCustNo(custNo);
        //是否更新手机号 ： 必须要求 手机号码 与  手机区号  有一个不一致
        boolean isUpdateMobile=!(StringUtil.isEqual(currentCustPo.getMobileAreaCode(),hkCustDetailVo.getMobileAreaCode())
                && StringUtil.isEqual(currentCustPo.getMobileDigest(),hkCustDetailVo.getMobileDigest()));
        if(isUpdateMobile){
            UpdateConsCustRequest updateCustReq=new UpdateConsCustRequest();
            updateCustReq.setOperator(operator);
            updateCustReq.setCustNo(custNo);
            updateCustReq.setMobileAreaCode(hkCustDetailVo.getMobileAreaCode());
            //获取明文手机号
            String mobile=queryHkSensitiveMobile(hkTxAcctNo);
            //手机号码 明文信息
            updateCustReq.setMobile(mobile);
            Response<UpdateConsCustRespVO> updateResp = cmBusinessService.updateCust(updateCustReq);
            log.info("根据香港交易账号：{} 更新投顾客户[mobile]信息，custNo:{},更新请求对象：{}，更新结果：{}",
                    hkTxAcctNo,custNo, JSON.toJSONString(updateCustReq),JSON.toJSONString(updateResp));

            return Response.ok();
        }else {
            log.info("根据香港交易账号：{} 更新投顾客户[mobile]信息，custNo:{},无需更新",hkTxAcctNo,custNo);
        }
        return Response.ok();

    }


    /**
     * @description:(以一账通账户侧为准，更新投顾客户[手机号码]信息)
     * @param hboneNo
     * @param custNo
     * @param operator
     * @return com.howbuy.crm.account.client.response.Response<java.lang.String>
     * @author: haoran.zhang
     * @date: 2024/1/8 17:45
     * @since JDK 1.8
     */
    public Response<String> updateMobileByHbone(String hboneNo,
                                                String custNo,
                                                String operator){
        log.info("根据一账通账号：{} 更新投顾客户[mobile]信息，custNo:{}",hboneNo,custNo);
        //更新逻辑：
        // 取【客户号A】的【客户姓名、手机号、证件号】（一账通账户中心数据），
        HboneAcctCustDetailInfoVO hboneCustDetailVo= queryHboneCustDetailInfo(hboneNo);
//        若账户中心的【手机号】为空，则不更新CRM手机号
        if(StringUtil.isEmpty(hboneCustDetailVo.getMobileDigest())){
            log.info("根据一账通账号：{} 更新投顾客户[mobile]信息，custNo:{},hbone手机号为空，无需更新!",hboneNo,custNo);
            return Response.ok();
        }

//        若账户中心的【手机号】不为空，则取账户中心【手机区号、手机号】覆盖CRM的投顾客户信息
        CmConscustPO currentCustPo=consCustInfoRepository.selectPoByCustNo(custNo);
        //是否更新手机号 ： 必须要求 手机号码 与  手机区号  有一个不一致
        boolean isUpdateMobile=!(StringUtil.isEqual(currentCustPo.getMobileAreaCode(),hboneCustDetailVo.getMobileAreaCode())
                && StringUtil.isEqual(currentCustPo.getMobileDigest(),hboneCustDetailVo.getMobileDigest()));
        if(isUpdateMobile){
            UpdateConsCustRequest updateCustReq=new UpdateConsCustRequest();
            updateCustReq.setOperator(operator);
            updateCustReq.setCustNo(custNo);
            updateCustReq.setMobileAreaCode(hboneCustDetailVo.getMobileAreaCode());
            //获取明文手机号
            String mobile=hboneAcctInfoOuterService.queryHboneSensitiveMobile(hboneNo);
            //手机号码 明文信息
            updateCustReq.setMobile(mobile);
            Response<UpdateConsCustRespVO> updateResp = cmBusinessService.updateCust(updateCustReq);
            log.info("根据一账通：{} 更新投顾客户[mobile]信息，custNo:{},更新请求对象：{}，更新结果：{}",
                    hboneNo,custNo, JSON.toJSONString(updateCustReq),JSON.toJSONString(updateResp));
            return Response.ok();
        }else {
            log.info("根据一账通：{} 更新投顾客户[mobile]信息，custNo:{},无需更新",hboneNo,custNo);
        }
        return Response.ok();

    }

    /**
     * @description:(以香港账户侧为准，更新投顾客户[证件号码]信息)
     * @param hkTxAcctNo
     * @param custNo
     * @param operator
     * @return com.howbuy.crm.account.client.response.Response<java.lang.String>
     * @author: haoran.zhang
     * @date: 2023/12/22 13:02
     * @since JDK 1.8
     */
    public Response<String> updateIdByHk(String hkTxAcctNo,String custNo,String operator){
        //更新逻辑：
//        若账户中心的【证件号】为空，则不更新CRM证件信息
//        若账户中心的【证件号】不为空，则取账户中心【客户姓名，证件类型、证件号、证件有效期】覆盖CRM的投顾客户信息
//        特别注意：更新投顾客户【客户姓名】时，优先取账户中心的【客户姓名(中文)】；若【客户姓名(中文)】为空，则取【客户姓名(英文)】
        HkAcctCustDetailInfoVO hkCustDetailVo=hkCustInfoOuterService.queryHkCustDetailInfo(hkTxAcctNo);
//        若账户中心的【证件号码】为空，则不更新CRM手机号
        if(StringUtil.isEmpty(hkCustDetailVo.getIdNoDigest())){
            return Response.ok();
        }
//        若账户中心的【手机号】不为空，则取账户中心【手机区号、手机号】覆盖CRM的投顾客户信息
        CmConscustPO currentCustPo=consCustInfoRepository.selectPoByCustNo(custNo);
        //是否更新 证件信息 ： 必须要求 证件类型 、证件地区码、 证件号 有一个不一致
        boolean isUpdateId=!(StringUtil.isEqual(currentCustPo.getIdtype(),hkCustDetailVo.getIdType())
                && StringUtil.isEqual(currentCustPo.getIdSignAreaCode(),hkCustDetailVo.getIdSignAreaCode())
                && StringUtil.isEqual(currentCustPo.getIdnoDigest(),hkCustDetailVo.getIdNoDigest()));
        if(isUpdateId){
            //查找 香港账户中心  敏感信息 用于更新
            HkAcctCustSensitiveInfoVO sensitiveInfo = queryHkCustSensitiveInfo(hkTxAcctNo);
            UpdateConsCustRequest updateCustReq=new UpdateConsCustRequest();
            updateCustReq.setOperator(operator);
            updateCustReq.setCustNo(custNo);
            updateCustReq.setIdtype(hkCustDetailVo.getIdType());
            updateCustReq.setIdSignAreaCode(hkCustDetailVo.getIdSignAreaCode());
            //证件号码 明文信息
            updateCustReq.setIdNo(sensitiveInfo.getIdNo());

            //附带证件信息的变更
            updateCustReq.setValidityst("");
            updateCustReq.setValiditydt(hkCustDetailVo.getIdValidityEnd());
            updateCustReq.setValidity(hkCustDetailVo.getIdAlwaysValidFlag());

            //更新客户名称
            updateCustReq.setCustname(hkCustDetailVo.getUsedCustName());

            Response<UpdateConsCustRespVO> updateResp = cmBusinessService.updateCust(updateCustReq);
            log.info("根据香港交易账号：{} 更新投顾客户[ID]信息，custNo:{},更新请求对象：{}，更新结果：{}",
                    hkTxAcctNo,custNo, JSON.toJSONString(updateCustReq),JSON.toJSONString(updateResp));
        }else{
            log.info("根据香港交易账号：{} 更新投顾客户[ID]信息，custNo:{},无需更新",hkTxAcctNo,custNo);
        }
        return Response.ok();
    }

    /**
     * @description:(以一账通账户侧为准，更新投顾客户[证件号码]信息)
     * @param hboneNo
     * @param custNo
     * @return com.howbuy.crm.account.client.response.Response<java.lang.String>
     * @author: haoran.zhang
     * @date: 2024/1/8 17:52
     * @since JDK 1.8
     */
    public Response<String> updateIdByHbone(String hboneNo,String custNo,String operator){
         log.info("根据一账通：{} 更新投顾客户[ID]信息，custNo:{}",hboneNo,custNo );
        //更新逻辑：
//        若账户中心的【证件号】为空，则不更新CRM证件信息
//        若账户中心的【证件号】不为空，则取账户中心【客户姓名，证件类型、证件号、证件有效期】覆盖CRM的投顾客户信息
//        特别注意：更新投顾客户【客户姓名】时，优先取账户中心的【客户姓名(中文)】；若【客户姓名(中文)】为空，则取【客户姓名(英文)】
        HboneAcctCustDetailInfoVO hboneCustDetailVo= queryHboneCustDetailInfo(hboneNo);
//        若账户中心的【手机号】为空，则不更新CRM手机号
        if(StringUtil.isEmpty(hboneCustDetailVo.getIdNoDigest())){
            log.info("根据一账通：{} 更新投顾客户[ID]信息，custNo:{},hbone证件号为空，无需更新",hboneNo,custNo);
            return Response.ok();
        }
//        若账户中心的【手机号】不为空，则取账户中心【手机区号、手机号】覆盖CRM的投顾客户信息
        CmConscustPO currentCustPo=consCustInfoRepository.selectPoByCustNo(custNo);
        //是否更新 证件信息 ： 必须要求 证件类型 、证件地区码、 证件号 有一个不一致
        boolean isUpdateId=!(StringUtil.isEqual(currentCustPo.getIdtype(),hboneCustDetailVo.getIdType())
                && StringUtil.isEqual(currentCustPo.getIdSignAreaCode(),hboneCustDetailVo.getIdSignAreaCode())
                && StringUtil.isEqual(currentCustPo.getIdnoDigest(),hboneCustDetailVo.getIdNoDigest()));
        if(isUpdateId){
            //查找 香港账户中心  敏感信息 用于更新
            //查找 一账通账户中心  敏感信息 用于更新
            HboneAcctCustSensitiveInfoVO sensitiveInfo = queryHboneCustSensitiveInfo(hboneNo);
            UpdateConsCustRequest updateCustReq=new UpdateConsCustRequest();
            updateCustReq.setOperator(operator);
            updateCustReq.setCustNo(custNo);
            updateCustReq.setIdtype(hboneCustDetailVo.getIdType());
            updateCustReq.setIdSignAreaCode(hboneCustDetailVo.getIdSignAreaCode());

            //附带证件信息的变更
            updateCustReq.setValidityst(hboneCustDetailVo.getIdValidityStart());
            updateCustReq.setValiditydt(hboneCustDetailVo.getIdValidityEnd());
            updateCustReq.setValidity(hboneCustDetailVo.getIdAlwaysValidFlag());
            //证件号码 明文信息
            updateCustReq.setIdNo(sensitiveInfo.getIdNo());
            Response<UpdateConsCustRespVO> updateResp = cmBusinessService.updateCust(updateCustReq);
            log.info("根据一账通：{} 更新投顾客户[ID]信息，custNo:{},更新请求对象：{}，更新结果：{}",
                    hboneNo,custNo, JSON.toJSONString(updateCustReq),JSON.toJSONString(updateResp));
        }else{
            log.info("根据一账通：{} 更新投顾客户[ID]信息，custNo:{},无需更新",hboneNo,custNo);
        }
        return Response.ok();
    }



    /**
     * @description:(根据 香港交易账号 查询 客户手机号 敏感信息)
     * @param hkTxAcctNo
     * @return java.lang.String
     * @author: haoran.zhang
     * @date: 2023/12/20 13:53
     * @since JDK 1.8
     */
    private String queryHkSensitiveMobile(String hkTxAcctNo){
        Assert.notNull(hkTxAcctNo,"香港交易账号不能为空");
        return hkCustInfoOuterService.queryHkSensitiveMobile(hkTxAcctNo);
    }


    /**
     * @description:(根据 香港交易账号 查询 客户信息 敏感信息)
     * @param hkTxAcctNo
     * @return com.howbuy.crm.account.client.response.custinfo.HkAcctCustSensitiveInfoVO
     * @author: haoran.zhang
     * @date: 2023/12/20 13:43
     * @since JDK 1.8
     */
    private HkAcctCustSensitiveInfoVO queryHkCustSensitiveInfo(String hkTxAcctNo) {
        Assert.notNull(hkTxAcctNo,"香港交易账号不能为空");
        return hkCustInfoOuterService.queryHkCustSensitiveInfo(hkTxAcctNo);
    }



    /**
     * @description:(根据香港客户 创建 客户号)
     * @param hkTxAcctNo 香港客户号
     * @param operateSource 操作来源 [1-MQ]时，为[异常来源]，参考枚举类[{@link com.howbuy.crm.account.client.enums.custinfo.FullCustSourceEnum}]
     *      *                [2-菜单页面]时，为菜单名称
     * @param operateChannel 操作通道 1-MQ  2-菜单页面
     * @return com.howbuy.crm.account.client.response.Response<java.lang.String>
     * @author: haoran.zhang
     * @date: 2023/12/27 19:25
     * @since JDK 1.8
     */
    public Response<String> createCustInfoByHk(String hkTxAcctNo,String operateSource,String operateChannel, String custSource){
        // 取【香港客户号A】的【客户姓名、手机号、证件号】（香港账户中心数据），
        HkAcctCustDetailInfoVO hkCustDetailVo=hkCustInfoOuterService.queryHkCustDetailInfo(hkTxAcctNo);
        //客户姓名 优先取中文姓名，若中文姓名为空，则取英文姓名
        String usedCustName = hkCustDetailVo.getUsedCustName();

        //查找 香港账户中心  敏感信息 用于更新
        HkAcctCustSensitiveInfoVO sensitiveInfo = queryHkCustSensitiveInfo(hkTxAcctNo);

        CreateConsCustRequest createCustReq=new CreateConsCustRequest();
        createCustReq.setOperator(Constants.OPERATOR_SYS);
        //客户类型
        createCustReq.setInvsttype(hkCustDetailVo.getInvstType());
        //更新客户姓名
        createCustReq.setCustname(usedCustName);

        if(StringUtil.isNotBlank(hkCustDetailVo.getMobileDigest())){
            //手机号 ：
            createCustReq.setMobileAreaCode(hkCustDetailVo.getMobileAreaCode());
            //获取明文手机号
            String mobile=queryHkSensitiveMobile(hkTxAcctNo);
            //手机号码 明文信息
            createCustReq.setMobile(mobile);
        }
        if(StringUtil.isNotBlank(hkCustDetailVo.getIdNoDigest())){
            createCustReq.setIdtype(hkCustDetailVo.getIdType());
            createCustReq.setIdSignAreaCode(hkCustDetailVo.getIdSignAreaCode());
            //证件号码 明文信息
            createCustReq.setIdNo(sensitiveInfo.getIdNo());
        }
        //证件有效日期
        createCustReq.setValiditydt(hkCustDetailVo.getIdValidityEnd());
        //证件是否长期有效 1-长期 0-非长期
        createCustReq.setValidity(hkCustDetailVo.getIdAlwaysValidFlag());

        // 性别 gender
        createCustReq.setGender(hkCustDetailVo.getGender());
        // 生日
        createCustReq.setBirthday(hkCustDetailVo.getBirthday());
        // 国籍/地区
        createCustReq.setNationCode(hkCustDetailVo.getNationality());

        // 省市县  取 账户中心  【中文地址】  现住址省份、市、县 编码
        createCustReq.setProvcode(hkCustDetailVo.getResidenceProvCode());
        createCustReq.setCitycode(hkCustDetailVo.getResidenceCityCode());
        createCustReq.setCountyCode(hkCustDetailVo.getResidenceCountyCode());
        // 地址
        if(StringUtil.isNotBlank(hkCustDetailVo.getResidenceCnAddrDigest())){
            //地址 明文信息
            createCustReq.setAddr(sensitiveInfo.getResidenceCnAddr());
        }
        // 邮箱
        if(StringUtil.isNotBlank(hkCustDetailVo.getEmailDigest())){
            //邮箱 明文信息
            createCustReq.setEmail(sensitiveInfo.getEmail());
        }

        createCustReq.setCustsource(custSource);
        createCustReq.setConsCode(SpecialConsCodeEnum.HWKH_WWH.getCode());
        createCustReq.setOperateSource(operateSource);
        createCustReq.setOperateChannel(operateChannel);
        Response<CreateConsCustRespVO> createCustResp=cmBusinessService.insertCust(createCustReq);
        CreateConsCustRespVO createRespVo=createCustResp.getData();
        if( !createCustResp.isSuccess() || StringUtil.isEmpty(createRespVo.getCustNo())){
            return  Response.fail(createCustResp.getDescription());
        }
        String custNo = createRespVo.getCustNo();
        return Response.ok("",custNo);

    }

    /**
     * @description:(根据 香港客户号 关联  更新  投顾客户信息)
     * @param analyseVo
     * @param processedCustInfo
     * @return void
     * @author: haoran.zhang
     * @date: 2023/12/18 15:57
     * @since JDK 1.8
     */
    public  Response<String> updateCustInfoByHk(HkMessageCustInfoVO analyseVo, CmConscustForAnalyseBO processedCustInfo){
        //香港客户号
        String hkTxAcctNo=analyseVo.getHkTxAcctNo();
        String custNo=processedCustInfo.getConscustno();
        log.info("根据香港交易账号：{}，更新投顾客户信息，custNo:{}",hkTxAcctNo,custNo);

//        取【香港客户号A】的【客户姓名、手机号、证件号】（香港账户中心数据），
        HkAcctCustDetailInfoVO hkCustDetailVo=hkCustInfoOuterService.queryHkCustDetailInfo(hkTxAcctNo);
        //客户姓名 优先取中文姓名，若中文姓名为空，则取英文姓名
        String usedCustName = hkCustDetailVo.getUsedCustName();

//        若存在任一为空，则不更新投顾客户信息
        if(StringUtil.isEmpty(usedCustName)||
//                StringUtil.isEmpty(hkCustDetailVo.getMobileDigest())||
                StringUtil.isEmpty(hkCustDetailVo.getIdNoDigest())){
            return Response.fail(String.format("hkTxAcctNo:%s,信息不全[客户名称、手机号码、证件信息有空值]，无需更新！",hkTxAcctNo));
        }

        //无需校验 手机号重复 的逻辑 :  若客户类型=机构/产品，或香港手机号为空，则不需要进行该条件的判断
        boolean noNeedValidateMobile  =
                StringUtil.isEmpty(hkCustDetailVo.getMobileDigest())  ||
                        ConsCustInvestTypeEnum.INSTITUTION.getCode().equals(hkCustDetailVo.getInvstType()) ||
                        ConsCustInvestTypeEnum.PRODUCT.getCode().equals(hkCustDetailVo.getInvstType());

        //取【香港客户号A】的【手机区号，手机号，证件类型，证件号】（香港账户中心数据），判断CRM是否存在重复客户
//        （2）满足以下任一条件，则认为存在重复客户
//        条件1：存在 【手机区号+手机号】相同
        List<CmConscustForAnalyseBO> mobileExistList=Lists.newArrayList();
        if(!noNeedValidateMobile){
            mobileExistList =abnormalCustRepository.searchMobileExistList(hkCustDetailVo.getInvstType(),
                    hkCustDetailVo.getMobileAreaCode(),hkCustDetailVo.getMobileDigest(),custNo);
        }
        if(CollectionUtils.isNotEmpty(mobileExistList)){
            insertHkProcessAbnormal(analyseVo, AbnormaSceneTypeEnum.CRM_REPEAT_CUST_WARNING,mobileExistList);
            return Response.fail(String.format("hkTxAcctNo:%s,存在重复客户！手机号：【%s】",hkTxAcctNo,hkCustDetailVo.getMobileDigest()));
        }
//        条件2：存在 【证件类型+证件号】相同
        List<CmConscustForAnalyseBO> idExistList=
                abnormalCustRepository.searchIdExistList(
                        hkCustDetailVo.getInvstType(),
                        hkCustDetailVo.getIdType(),
                        hkCustDetailVo.getIdSignAreaCode(),
                        hkCustDetailVo.getIdNoDigest(),
                        custNo);
        if(CollectionUtils.isNotEmpty(idExistList)){
            insertHkProcessAbnormal(analyseVo, AbnormaSceneTypeEnum.CRM_REPEAT_CUST_WARNING,idExistList);
            return Response.fail(String.format("hkTxAcctNo:%s,存在重复客户！证件号：【%s】",hkTxAcctNo,hkCustDetailVo.getIdNoDigest()));
        }
//        更新逻辑：仅更新香港账户中心有值，且与CRM投顾客户信息不一致的字段（投顾客户号为空的字段按不一致处理）。
//        涉及字段：姓名，客户类型，手机区号，手机号，证件类型，证件号，证件有效日期，性别，生日，国籍/地区，省市县，地址，邮箱
        CmConscustPO currentCustPo=consCustInfoRepository.selectPoByCustNo(custNo);

        //查找 香港账户中心  敏感信息 用于更新
        HkAcctCustSensitiveInfoVO sensitiveInfo = queryHkCustSensitiveInfo(hkTxAcctNo);


        UpdateConsCustRequest updateCustReq=new UpdateConsCustRequest();
        updateCustReq.setCustNo(custNo);
        updateCustReq.setOperator(analyseVo.getCreator());
        //更新客户类型
        if(StringUtil.isNotBlank(hkCustDetailVo.getInvstType())
                && Boolean.FALSE.equals(StringUtil.isEqual(currentCustPo.getInvsttype(),hkCustDetailVo.getInvstType()))){
            updateCustReq.setInvsttype(hkCustDetailVo.getInvstType());
        }
        //更新客户姓名
        if(StringUtil.isNotBlank(usedCustName)
                && Boolean.FALSE.equals(StringUtil.isEqual(currentCustPo.getCustname(),usedCustName))){
            updateCustReq.setCustname(usedCustName);
        }
        //是否更新手机号 ： 必须要求 手机号码 与  手机区号  有一个不一致
        if(StringUtil.isNotBlank(hkCustDetailVo.getMobileDigest())){
            boolean isUpdateMobile=!(StringUtil.isEqual(currentCustPo.getMobileAreaCode(),hkCustDetailVo.getMobileAreaCode())
                    && StringUtil.isEqual(currentCustPo.getMobileDigest(),hkCustDetailVo.getMobileDigest()));
            if(isUpdateMobile){
                updateCustReq.setMobileAreaCode(hkCustDetailVo.getMobileAreaCode());
                //获取明文手机号
                String mobile=queryHkSensitiveMobile(hkTxAcctNo);
                //手机号码 明文信息
                updateCustReq.setMobile(mobile);
            }
        }
        //是否更新 证件信息 ： 必须要求 证件类型 、证件地区码、 证件号 有一个不一致
        boolean isUpdateId=!(StringUtil.isEqual(currentCustPo.getIdtype(),hkCustDetailVo.getIdType())
                && StringUtil.isEqual(currentCustPo.getIdSignAreaCode(),hkCustDetailVo.getIdSignAreaCode())
                && StringUtil.isEqual(currentCustPo.getIdnoDigest(),hkCustDetailVo.getIdNoDigest()));
        if(isUpdateId){
            updateCustReq.setIdtype(hkCustDetailVo.getIdType());
            updateCustReq.setIdSignAreaCode(hkCustDetailVo.getIdSignAreaCode());
            //证件号码 明文信息
            updateCustReq.setIdNo(sensitiveInfo.getIdNo());
            //证件有效日期 截止日期
            updateCustReq.setValiditydt(hkCustDetailVo.getIdValidityEnd());
            //证件有效日期 起始日期  香港账户暂时没有此属性
            updateCustReq.setValidityst("");
            //证件是否长期有效 1-长期 0-非长期
            updateCustReq.setValidity(hkCustDetailVo.getIdAlwaysValidFlag());
        }
//        //证件有效日期
//        if(StringUtil.isNotBlank(hkCustDetailVo.getIdValidityEnd())
//                && Boolean.FALSE.equals(StringUtil.isEqual(currentCustPo.getValiditydt(),hkCustDetailVo.getIdValidityEnd()))){
//            updateCustReq.setValiditydt(hkCustDetailVo.getIdValidityEnd());
//        }
//        //证件是否长期有效 crm: 1-长期 2-非长期
//        //账户中心: 是否长期  0-否 1-是
//        //此处 产线数据就有问题。 后续 待重构  建议：以账户中心为准。
//        if(StringUtil.isNotBlank(hkCustDetailVo.getIdAlwaysValidFlag())
//                && Boolean.FALSE.equals(StringUtil.isEqual(currentCustPo.getValidity(),hkCustDetailVo.getIdAlwaysValidFlag()))){
//            updateCustReq.setValidity(hkCustDetailVo.getIdAlwaysValidFlag());
//        }

        // 性别 gender
        if(StringUtil.isNotBlank(hkCustDetailVo.getGender())
                && Boolean.FALSE.equals(StringUtil.isEqual(currentCustPo.getGender(),hkCustDetailVo.getGender()))){
            updateCustReq.setGender(hkCustDetailVo.getGender());
        }
        // 生日
        if(StringUtil.isNotBlank(hkCustDetailVo.getBirthday())
                && Boolean.FALSE.equals(StringUtil.isEqual(currentCustPo.getBirthday(),hkCustDetailVo.getBirthday()))){
            updateCustReq.setBirthday(hkCustDetailVo.getBirthday());
        }
        // 国籍/地区
        if(StringUtil.isNotBlank(hkCustDetailVo.getNationality())
                && Boolean.FALSE.equals(StringUtil.isEqual(currentCustPo.getNationCode(),hkCustDetailVo.getNationality()))){
            updateCustReq.setNationCode(hkCustDetailVo.getNationality());
        }
        // 省市县  取 账户中心  【中文地址】  现住址省份、市、县 编码
        if(StringUtil.isNotBlank(hkCustDetailVo.getResidenceProvCode())
                && Boolean.FALSE.equals(StringUtil.isEqual(currentCustPo.getProvcode(),hkCustDetailVo.getResidenceProvCode()))){
            updateCustReq.setProvcode(hkCustDetailVo.getResidenceProvCode());
        }
        if(StringUtil.isNotBlank(hkCustDetailVo.getResidenceCityCode())
                && Boolean.FALSE.equals(StringUtil.isEqual(currentCustPo.getCitycode(),hkCustDetailVo.getResidenceCityCode()))){
            updateCustReq.setCitycode(hkCustDetailVo.getResidenceCityCode());
        }
        if(StringUtil.isNotBlank(hkCustDetailVo.getResidenceCountyCode())
                && Boolean.FALSE.equals(StringUtil.isEqual(currentCustPo.getCountyCode(),hkCustDetailVo.getResidenceCountyCode()))){
            updateCustReq.setCountyCode(hkCustDetailVo.getResidenceCountyCode());
        }
        // 地址
        if(StringUtil.isNotBlank(hkCustDetailVo.getResidenceCnAddrDigest())
                && Boolean.FALSE.equals(StringUtil.isEqual(currentCustPo.getAddrDigest(),hkCustDetailVo.getResidenceCnAddrDigest()))){
            //地址 明文信息
            updateCustReq.setAddr(sensitiveInfo.getResidenceCnAddr());
        }
        // 邮箱
        if(StringUtil.isNotBlank(hkCustDetailVo.getEmailDigest())
                && Boolean.FALSE.equals(StringUtil.isEqual(currentCustPo.getEmailDigest(),hkCustDetailVo.getEmailDigest()))){
            //邮箱 明文信息
            updateCustReq.setEmail(sensitiveInfo.getEmail());
        }
        log.info("更新投顾客户信息，custNo:{},更新属性：{}",custNo, JSON.toJSONString(updateCustReq));
        Response<UpdateConsCustRespVO>  updateResp = cmBusinessService.updateCust(updateCustReq);
        log.info("更新投顾客户信息，custNo:{},更新结果：{}",custNo, JSON.toJSONString(updateResp));
        if(updateResp.isSuccess()){
            return Response.ok();
        }else{
            return Response.fail(updateResp.getDescription());
        }
    }

}