/**
 * Copyright (c) 2023, <PERSON><PERSON><PERSON><PERSON> HOWBUY INVESTMENT MANAGEMENT Co., Ltd.
 * All right reserved.
 * <p>
 * THIS IS UNPUBLISHED PROPRIETARY SOURCE CODE OF HOWBUY INVESTMENT
 * MANAGEMENT CO., LTD.  THE CONTENTS OF THIS FILE MAY NOT BE DISCLOSED
 * TO THIRD PARTIES, COPIED OR DUPLICATED IN ANY FORM, IN WHOLE OR IN PART,
 * WITHOUT THE PRIOR WRITTEN PERMISSION OF HOWBUY INVESTMENT MANAGEMENT
 * CO., LTD.
 */
package com.howbuy.crm.account.service.controller.custinfo;

import com.howbuy.crm.account.client.request.custinfo.CreateConsCustRequest;
import com.howbuy.crm.account.client.request.custinfo.UpdateConsCustRequest;
import com.howbuy.crm.account.client.response.Response;
import com.howbuy.crm.account.client.response.custinfo.CmConsCustSimpleVO;
import com.howbuy.crm.account.client.response.custinfo.CmConsCustWithCipherVO;
import com.howbuy.crm.account.client.response.custinfo.CreateConsCustRespVO;
import com.howbuy.crm.account.client.response.custinfo.UpdateConsCustRespVO;
import com.howbuy.crm.account.service.commom.constant.Constants;
import com.howbuy.crm.account.service.service.custinfo.ConsCustInfoService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

/**
 * @description: (crm投顾客户号)
 * <AUTHOR>
 * @date 2023/12/13 16:08
 * @since JDK 1.8
 */
@RestController
@RequestMapping("/custinfo")
public class CustInfoController {

    @Autowired
    private ConsCustInfoService consCustInfoService;


    /**
     * @api {GET} /custinfo/querycustdetailinfo queryCustInfo()
     * @apiVersion 1.0.0
     * @apiGroup CustInfoController
     * @apiName queryCustInfo()
     * @apiDescription 查询投顾客户详细信息，包含加密信息
     * @apiParam (请求参数) {String} custNo
     * @apiParamExample 请求参数示例
     * custNo=M
     * @apiSuccess (响应结果) {String} code 状态码
     * @apiSuccess (响应结果) {String} description 描述信息
     * @apiSuccess (响应结果) {Object} data 数据封装
     * @apiSuccess (响应结果) {String} data.idnoCipher 证件号密文
     * @apiSuccess (响应结果) {String} data.addrCipher 地址密文
     * @apiSuccess (响应结果) {String} data.mobileCipher 手机号密文
     * @apiSuccess (响应结果) {String} data.telnoCipher 座机号密文
     * @apiSuccess (响应结果) {String} data.emailCipher 邮箱密文
     * @apiSuccess (响应结果) {String} data.addr2Cipher 地址2密文
     * @apiSuccess (响应结果) {String} data.mobile2Cipher 手机2密文
     * @apiSuccess (响应结果) {String} data.email2Cipher 邮箱2密文
     * @apiSuccess (响应结果) {String} data.linkmanCipher 联系人姓名密文
     * @apiSuccess (响应结果) {String} data.linktelCipher 联系人座机密文
     * @apiSuccess (响应结果) {String} data.linkmobileCipher 联系人手机号密文
     * @apiSuccess (响应结果) {String} data.linkemailCipher 联系人邮箱密文
     * @apiSuccess (响应结果) {String} data.linkaddrCipher 联系人地址密文
     * @apiSuccess (响应结果) {String} data.conscustno 投顾客户编号
     * @apiSuccess (响应结果) {String} data.conscustlvl 投顾客户等级
     * @apiSuccess (响应结果) {Number} data.conscustgrade 投顾客户评分
     * @apiSuccess (响应结果) {String} data.conscuststatus 客户有效状态：0-正常 1-删除
     * @apiSuccess (响应结果) {String} data.idtype 证件类型
     * @apiSuccess (响应结果) {String} data.custname 投资者名称
     * @apiSuccess (响应结果) {String} data.provcode 省份代码
     * @apiSuccess (响应结果) {String} data.citycode 城市代码
     * @apiSuccess (响应结果) {String} data.edulevel 投资者学历
     * @apiSuccess (响应结果) {String} data.vocation 投资者职业代码
     * @apiSuccess (响应结果) {String} data.inclevel 投资者年收入
     * @apiSuccess (响应结果) {String} data.birthday 投资者生日
     * @apiSuccess (响应结果) {String} data.gender 投资者性别(0：女；1：男)
     * @apiSuccess (响应结果) {String} data.married 婚否
     * @apiSuccess (响应结果) {String} data.pincome 个人年收入
     * @apiSuccess (响应结果) {String} data.fincome 家庭年收入
     * @apiSuccess (响应结果) {String} data.decisionflag 是否投资决策人
     * @apiSuccess (响应结果) {String} data.interests 兴趣爱好
     * @apiSuccess (响应结果) {String} data.familycondition 家庭状况
     * @apiSuccess (响应结果) {String} data.contacttime 方便联系时段
     * @apiSuccess (响应结果) {String} data.sendinfoflag 是否寄送资料
     * @apiSuccess (响应结果) {String} data.recvtelflag 是否接受电话
     * @apiSuccess (响应结果) {String} data.recvemailflag 是否接受电子邮件
     * @apiSuccess (响应结果) {String} data.recvmsgflag 是否接受短信
     * @apiSuccess (响应结果) {String} data.company 公司
     * @apiSuccess (响应结果) {String} data.risklevel 客户风险承受能力等级
     * @apiSuccess (响应结果) {String} data.selfrisklevel 自定义风险承受能力等级
     * @apiSuccess (响应结果) {String} data.postcode 邮政编码
     * @apiSuccess (响应结果) {String} data.fax 传真
     * @apiSuccess (响应结果) {String} data.officetelno 投资者单位电话
     * @apiSuccess (响应结果) {String} data.source 客户来源
     * @apiSuccess (响应结果) {String} data.knowchan 了解途径
     * @apiSuccess (响应结果) {String} data.otherchan 其它途径
     * @apiSuccess (响应结果) {String} data.otherinvest 想了解的其他投资品种
     * @apiSuccess (响应结果) {String} data.salon 希望参加的沙龙
     * @apiSuccess (响应结果) {String} data.beforeinvest 之前投资品种
     * @apiSuccess (响应结果) {String} data.selfdefflag 自定义标志
     * @apiSuccess (响应结果) {String} data.visitfqcy 沟通频率
     * @apiSuccess (响应结果) {String} data.devdirection 发展方向
     * @apiSuccess (响应结果) {String} data.saledirection 销售方向
     * @apiSuccess (响应结果) {String} data.subsource 客户来源细分
     * @apiSuccess (响应结果) {String} data.subsourcetype 客户来源细分分类
     * @apiSuccess (响应结果) {String} data.postcode2 邮政编码2
     * @apiSuccess (响应结果) {String} data.knowhowbuy 知道好买
     * @apiSuccess (响应结果) {String} data.subknow 知道好买细分
     * @apiSuccess (响应结果) {String} data.subknowtype 知道好买细分分类
     * @apiSuccess (响应结果) {String} data.buyingprod 购买产品
     * @apiSuccess (响应结果) {String} data.buyedprod 曾购买产品
     * @apiSuccess (响应结果) {String} data.specialflag 是否特殊客户
     * @apiSuccess (响应结果) {String} data.dlvymode 报告寄送方式
     * @apiSuccess (响应结果) {String} data.remark 备注
     * @apiSuccess (响应结果) {String} data.regdt 登记日期
     * @apiSuccess (响应结果) {String} data.uddt 最近修改日期
     * @apiSuccess (响应结果) {String} data.pririsklevel 私募风险测试水平
     * @apiSuccess (响应结果) {String} data.linkman 联系人姓名
     * @apiSuccess (响应结果) {String} data.linkpostcode 经办人邮政编码
     * @apiSuccess (响应结果) {String} data.capacity 职位
     * @apiSuccess (响应结果) {String} data.gpsinvestlevel GPS投资级别---对应资金额度(A: 投资资产<100万,B:投资资产100-2000万 C:投资资产>2000万)
     * @apiSuccess (响应结果) {String} data.gpsrisklevel GPS风险偏好( 1: 积极型 ,2:稳健型,3:保守型)
     * @apiSuccess (响应结果) {String} data.isboss 是否企业主( 1:是企业主，0)
     * @apiSuccess (响应结果) {String} data.financeneed 金融需求
     * @apiSuccess (响应结果) {String} data.isjoinclub 是否入会（1：已经入会）
     * @apiSuccess (响应结果) {String} data.isrisktip 是否签署风险提示函
     * @apiSuccess (响应结果) {String} data.custsourceremark 客户来源备注
     * @apiSuccess (响应结果) {Number} data.pmarketamt
     * @apiSuccess (响应结果) {String} data.iswritebook 是否填写过投资者承诺书（0：否；1：是）
     * @apiSuccess (响应结果) {String} data.latesttradedt 最近一次高净值交易日期
     * @apiSuccess (响应结果) {String} data.rstohighreason 划转原因(1:RS转RT;2:RS转高端--挖掘;3:RS转高端--客户)
     * @apiSuccess (响应结果) {String} data.ispubtrade 是否公募交易过（0：否；1：是）
     * @apiSuccess (响应结果) {String} data.invsttype
     * @apiSuccess (响应结果) {String} data.source2 合并客户最早来源细分分类
     * @apiSuccess (响应结果) {String} data.subsource2 合并客户最早来源细分
     * @apiSuccess (响应结果) {String} data.subsourcetype2
     * @apiSuccess (响应结果) {String} data.vipusername 贵宾账号用户名
     * @apiSuccess (响应结果) {String} data.wechatcode
     * @apiSuccess (响应结果) {String} data.newsourceno 新来源编号
     * @apiSuccess (响应结果) {String} data.hboneNo 一账通账号
     * @apiSuccess (响应结果) {String} data.newsourceno2 新来源编号(合并)
     * @apiSuccess (响应结果) {String} data.mergedt 合并时间
     * @apiSuccess (响应结果) {String} data.hopetradetype 可以期望交易方式（1：电子交易；2：线下交易）
     * @apiSuccess (响应结果) {String} data.custstatus 投顾客户手机核查对应状态
     * @apiSuccess (响应结果) {Number} data.premiumAmount 高端资产合计
     * @apiSuccess (响应结果) {String} data.validity 证件有限期
     * @apiSuccess (响应结果) {String} data.validitydt 证件有限期日期
     * @apiSuccess (响应结果) {String} data.nature 性质
     * @apiSuccess (响应结果) {String} data.aptitude 资质
     * @apiSuccess (响应结果) {String} data.scopebusiness 经营范围
     * @apiSuccess (响应结果) {String} data.restype 资源类型0：公司资源；1：投顾资源
     * @apiSuccess (响应结果) {String} data.acregdt 客户登记日期
     * @apiSuccess (响应结果) {String} data.joinclubdt 客户首次入会日期
     * @apiSuccess (响应结果) {String} data.provcodeMobile 手机归属省份
     * @apiSuccess (响应结果) {String} data.citycodeMobile 手机归属城市
     * @apiSuccess (响应结果) {String} data.orgtype 机构类型
     * @apiSuccess (响应结果) {String} data.pinyin 姓名拼音
     * @apiSuccess (响应结果) {String} data.idnoDigest 证件号摘要
     * @apiSuccess (响应结果) {String} data.idnoMask 证件号掩码
     * @apiSuccess (响应结果) {String} data.custnameDigest 客户姓名摘要
     * @apiSuccess (响应结果) {String} data.custnameMask 客户姓名掩码
     * @apiSuccess (响应结果) {String} data.addrDigest 地址摘要
     * @apiSuccess (响应结果) {String} data.addrMask 地址掩码
     * @apiSuccess (响应结果) {String} data.mobileDigest 手机号摘要
     * @apiSuccess (响应结果) {String} data.mobileMask 手机号掩码
     * @apiSuccess (响应结果) {String} data.telnoDigest 座机号摘要
     * @apiSuccess (响应结果) {String} data.telnoMask 座机号掩码
     * @apiSuccess (响应结果) {String} data.emailDigest 邮箱摘要
     * @apiSuccess (响应结果) {String} data.emailMask 邮箱掩码
     * @apiSuccess (响应结果) {String} data.addr2Digest 地址2摘要
     * @apiSuccess (响应结果) {String} data.addr2Mask 地址2掩码
     * @apiSuccess (响应结果) {String} data.mobile2Digest 手机2摘要
     * @apiSuccess (响应结果) {String} data.mobile2Mask 手机2掩码
     * @apiSuccess (响应结果) {String} data.email2Digest 邮箱2摘要
     * @apiSuccess (响应结果) {String} data.email2Mask 邮箱2掩码
     * @apiSuccess (响应结果) {String} data.linkmanDigest 联系人姓名摘要
     * @apiSuccess (响应结果) {String} data.linkmanMask 联系人姓名掩码
     * @apiSuccess (响应结果) {String} data.linktelDigest 联系人座机号摘要
     * @apiSuccess (响应结果) {String} data.linktelMask 联系人座机号掩码
     * @apiSuccess (响应结果) {String} data.linkmobileDigest 联系人手机摘要
     * @apiSuccess (响应结果) {String} data.linkmobileMask 联系人手机掩码
     * @apiSuccess (响应结果) {String} data.linkemailDigest 联系人邮箱摘要
     * @apiSuccess (响应结果) {String} data.linkemailMask 联系人邮箱掩码
     * @apiSuccess (响应结果) {String} data.linkaddrDigest 联系人地址摘要
     * @apiSuccess (响应结果) {String} data.linkaddrMask 联系人地址掩码
     * @apiSuccess (响应结果) {String} data.isvirtualsharer 分享人投顾是否虚拟（1：是，0：否）
     * @apiSuccess (响应结果) {String} data.validityst 证件有效起始日
     * @apiSuccess (响应结果) {String} data.mobileAreaCode 手机 地区码
     * @apiSuccess (响应结果) {String} data.mobile2AreaCode 手机2 地区码
     * @apiSuccess (响应结果) {String} data.linkmobileAreaCode 联系人手机 地区码
     * @apiSuccess (响应结果) {String} data.idSignAreaCode 证件地区码
     * @apiSuccess (响应结果) {String} data.nationCode 国籍
     * @apiSuccess (响应结果) {Number} data.hboneTimestamp 一账通号关联时间
     * @apiSuccess (响应结果) {Number} data.createTimestamp 客户创建时间戳
     * @apiSuccessExample 响应结果示例
     * {"code":"G0Vv","data":{"edulevel":"aH","subsource":"xQiu","joinclubdt":"wy4mJUZ","addrCipher":"91GdyjK","otherinvest":"qjh2n3","hboneTimestamp":1556989892443,"source2":"Awh","knowhowbuy":"dbOtO3ap","telnoDigest":"n3SdTc","conscustno":"f","devdirection":"CD42I3l68U","linktelDigest":"Y1Lf","telnoCipher":"oRR44","addrDigest":"mR2FCL1","decisionflag":"KXayq2r","fax":"22EzyW6Vz","subsourcetype2":"QC9","hboneNo":"Ps2KHcHQvh","mergedt":"D3UinSupmU","dlvymode":"hpZkS","orgtype":"U","buyingprod":"YkugpcMUUM","linktelCipher":"vFAoZxgVfq","email2Digest":"9","addrMask":"n9g","email2Mask":"dElHue","idtype":"ZPqUf","isboss":"MDm7PKTGk","pincome":"EjXhiYGBY6","inclevel":"cRM","subknowtype":"DsUs4HB","citycodeMobile":"rmCr","mobileMask":"0uPMh5DtoG","linkaddrCipher":"H","gender":"Ls0mdH0a","selfdefflag":"0SKlcq","provcodeMobile":"P5HdesLW","linkemailDigest":"bwF0","capacity":"y","linkaddrMask":"3WPO","vipusername":"WEIBJx","mobileAreaCode":"RGFGUdlOw1","regdt":"qZODSA","sendinfoflag":"TLVi","saledirection":"wh0m71T7BZ","aptitude":"9RUzOC","isjoinclub":"Y3Q9SvA2","visitfqcy":"9WkKL","gpsinvestlevel":"BbR","nationCode":"5DemTBT","subknow":"QZ","email2Cipher":"h","idnoMask":"f2avd","otherchan":"hR","uddt":"byH1","scopebusiness":"Wv","linkmobileDigest":"CcCpZJ","emailDigest":"VqNP0nO","mobileCipher":"slMYDj","addr2Cipher":"Xk9MVaITx","conscuststatus":"U","knowchan":"ooxcTw","vocation":"b","isrisktip":"vVDf8dHlo","linkmobileAreaCode":"Y2","idnoDigest":"tf1dqodWmi","rstohighreason":"VLjlN","linkmanDigest":"ztPQtB","validity":"n","interests":"dUodp6De","married":"xqeT0STkR","subsourcetype":"xkA987jPpV","custname":"k61","recvtelflag":"2OEZJM","premiumAmount":6421.603793843022,"custnameMask":"FU","mobile2Cipher":"iXpoHx","buyedprod":"NVRwMTVx","newsourceno2":"d","invsttype":"RWi8","linkmanCipher":"YDdD","idnoCipher":"EGY2QJp","linkpostcode":"uOEz","custnameDigest":"JJ3H","source":"4JK0","isvirtualsharer":"ALVXqZV","conscustlvl":"avC","beforeinvest":"3XNbV","linktelMask":"hQ","createTimestamp":3346408157131,"contacttime":"x6F","citycode":"2NEGmnT","linkemailMask":"ALDk3M","newsourceno":"AoKxqNA75e","validitydt":"aU16qWwV","mobile2Digest":"1LHS","iswritebook":"Y5iRjHVxp","postcode":"PLbGViENs","fincome":"lKLytBsjhG","specialflag":"5oCX5Gx","addr2Mask":"CwmqSi","linkman":"0lDkKA79pF","restype":"E","idSignAreaCode":"b","pinyin":"HYq","salon":"k5","birthday":"XS9cBl","familycondition":"X3e464HXLS","subsource2":"qJ6NgxT","wechatcode":"ep","conscustgrade":471,"recvemailflag":"Cx3acuVAYc","telnoMask":"h1X7nSyY0f","custsourceremark":"Zhrh96","linkaddrDigest":"f2RHv","remark":"M8","validityst":"jlO9KLus","linkmanMask":"g","gpsrisklevel":"HFAAfAf5","latesttradedt":"1IrZJdHc","mobileDigest":"FM","addr2Digest":"PRIpp7QA","company":"whT20x","hopetradetype":"yjg7t06","linkemailCipher":"8dSSR","postcode2":"uFn","ispubtrade":"ivdvx5","officetelno":"HNdtUo","acregdt":"ECBKYXO4","selfrisklevel":"gS","linkmobileMask":"UBoflv","pmarketamt":9102.077919036827,"nature":"T","pririsklevel":"djsy7aBf","financeneed":"EK9","risklevel":"D","custstatus":"osincHzZ5N","recvmsgflag":"EYO5K96U","provcode":"n84OMWFta","emailMask":"6hCwt6","mobile2Mask":"TnPnEH","mobile2AreaCode":"OwQ0hN6","emailCipher":"X","linkmobileCipher":"0d"},"description":"QdrKfTbn00"}
     */
    @GetMapping("/querycustdetailinfo")
    @ResponseBody
    public Response<CmConsCustWithCipherVO> queryCustDetailInfo(@RequestParam(name="custNo") String custNo){
        return Response.ok(consCustInfoService.queryCustWithCipherByCustNo(custNo));
    }


    /**
     * @api {GET} /custinfo/querycustsimpleinfo queryCustSimpleInfo()
     * @apiVersion 1.0.0
     * @apiGroup CustInfoController
     * @apiName queryCustSimpleInfo()
     * @apiDescription 查询客户简单信息，包含 : 客户基本信息、hboneNo、香港客户信息、投顾code
     * @apiParam (请求参数) {String} custNo
     * @apiParamExample 请求参数示例
     * custNo=kRSu5HIqV0
     * @apiSuccess (响应结果) {String} code 状态码
     * @apiSuccess (响应结果) {String} description 描述信息
     * @apiSuccess (响应结果) {Object} data 数据封装
     * @apiSuccess (响应结果) {String} data.conscustno 投顾客户编号
     * @apiSuccess (响应结果) {String} data.idtype 证件类型
     * @apiSuccess (响应结果) {String} data.custname 投资者名称
     * @apiSuccess (响应结果) {String} data.invsttype 客户投资类型 0 机构客户 1 个人客户 2 产品客户
     * @apiSuccess (响应结果) {String} data.hboneNo 一账通账号
     * @apiSuccess (响应结果) {String} data.custstatus 投顾客户手机核查对应状态
     * @apiSuccess (响应结果) {String} data.idnoDigest 证件号摘要
     * @apiSuccess (响应结果) {String} data.idnoMask 证件号掩码
     * @apiSuccess (响应结果) {String} data.mobileDigest 手机号摘要
     * @apiSuccess (响应结果) {String} data.mobileMask 手机号掩码
     * @apiSuccess (响应结果) {String} data.mobileAreaCode 手机 地区码
     * @apiSuccess (响应结果) {String} data.idSignAreaCode 证件地区码
     * @apiSuccess (响应结果) {String} data.nationCode 国籍
     * @apiSuccess (响应结果) {Number} data.hboneTimestamp 一账通号关联时间
     * @apiSuccess (响应结果) {String} data.hkcustid 客户香港id (ebrokerID)
     * @apiSuccess (响应结果) {String} data.hkTxAcctNo 香港交易账号
     * @apiSuccess (响应结果) {String} data.consCode 所属投顾
     * @apiSuccessExample 响应结果示例
     * {"code":"JX","data":{"invsttype":"Otn1neu","idnoMask":"qtk0","hboneTimestamp":102359499306,"consCode":"mtlQITCFF","custstatus":"PrljmTD","mobileAreaCode":"0","hkTxAcctNo":"P1jHnrvO","idtype":"blMJR","idnoDigest":"3U","idSignAreaCode":"4dv6et20T","conscustno":"hLju27c3xv","mobileDigest":"H1nv0Y4x","hkcustid":"EQwVod","custname":"ENN","hboneNo":"Kr","mobileMask":"l4YaXJ","nationCode":"toeV4w"},"description":"ytT6llF"}
     */
    @GetMapping("/querycustsimpleinfo")
    @ResponseBody
    public Response<CmConsCustSimpleVO> queryCustSimpleInfo(@RequestParam(name="custNo") String custNo){
        return Response.ok(consCustInfoService.queryCustSimpleInfo(custNo));
    }


    /**
     * @api {POST} /custinfo/createCustInfo createCustInfo()
     * @apiVersion 1.0.0
     * @apiGroup CustInfoController
     * @apiName createCustInfo()
     * @apiDescription 新增客户信息
     * @apiParam (请求体) {String} operator 操作人
     * @apiParam (请求体) {String} invsttype 客户类型枚举 0 机构客户 1 个人客户 2 产品客户
     * @apiParam (请求体) {String} custname 客户名称
     * @apiParam (请求体) {String} mobile 手机号码-明文
     * @apiParam (请求体) {String} mobile2 手机号码2-明文
     * @apiParam (请求体) {String} email 邮箱-明文
     * @apiParam (请求体) {String} email2 邮箱2-明文
     * @apiParam (请求体) {String} telNo 座机号-明文
     * @apiParam (请求体) {String} fax 传真
     * @apiParam (请求体) {String} addr 地址-明文
     * @apiParam (请求体) {String} addr2 地址2 -明文
     * @apiParam (请求体) {String} postcode 邮政编码
     * @apiParam (请求体) {String} postcode2 邮政2 编码
     * @apiParam (请求体) {String} idtype 证件类型
     * @apiParam (请求体) {String} idNo 证件号码-明文
     * @apiParam (请求体) {String} wechatcode 微信号
     * @apiParam (请求体) {String} linkman 联系人姓名
     * @apiParam (请求体) {String} linkTel 联系人 座机号-明文
     * @apiParam (请求体) {String} linkMobile 联系人 手机号码-明文
     * @apiParam (请求体) {String} linkEmail 联系人 邮箱-明文
     * @apiParam (请求体) {String} linkAddr 联系人 地址-明文
     * @apiParam (请求体) {String} linkpostcode 经办人邮政编码
     * @apiParam (请求体) {String} provcode 省份代码
     * @apiParam (请求体) {String} citycode 城市代码
     * @apiParam (请求体) {String} countyCode 县代码
     * @apiParam (请求体) {String} knowhowbuy 知道好买
     * @apiParam (请求体) {String} subknow 知道好买细分
     * @apiParam (请求体) {String} subknowtype 知道好买细分分类
     * @apiParam (请求体) {String} custsource 客户来源： PH0806F18      [数据库字段：newsourceno]
     * @apiParam (请求体) {String} custSourceRemark 客户来源备注-投顾开发-电话营销
     * @apiParam (请求体) {String} gender 投资者性别(0：女；1：男)
     * @apiParam (请求体) {String} married 婚否
     * @apiParam (请求体) {String} conscustgrade 投顾客户评分
     * @apiParam (请求体) {String} visitfqcy 沟通频率 Eg:2
     * @apiParam (请求体) {String} edulevel 投资者学历
     * @apiParam (请求体) {String} vocation 投资者职业代码
     * @apiParam (请求体) {String} pincome 个人年收入
     * @apiParam (请求体) {String} fincome 家庭年收入
     * @apiParam (请求体) {String} decisionflag 是否投资决策人
     * @apiParam (请求体) {String} birthday 投资者生日
     * @apiParam (请求体) {String} company 公司
     * @apiParam (请求体) {String} officetelno 投资者 单位电话
     * @apiParam (请求体) {String} interests 兴趣爱好
     * @apiParam (请求体) {String} contacttime 方便联系时段
     * @apiParam (请求体) {String} remark 备注
     * @apiParam (请求体) {String} otherinvest 想了解的其他投资品种
     * @apiParam (请求体) {String} salon 希望参加的沙龙[hb_constant.PIncome]
     * @apiParam (请求体) {String} saledirection 销售方向
     * @apiParam (请求体) {String} beforeinvest 之前投资品种
     * @apiParam (请求体) {String} validity 证件有限期
     * @apiParam (请求体) {String} nature 性质
     * @apiParam (请求体) {String} aptitude 资质
     * @apiParam (请求体) {String} validitydt 证件有限期日期
     * @apiParam (请求体) {String} scopebusiness 经营范围
     * @apiParam (请求体) {String} conscustlvl 投顾客户等级
     * @apiParam (请求体) {String} mobileAreaCode 手机 地区码
     * @apiParam (请求体) {String} mobile2AreaCode 手机2 地区码
     * @apiParam (请求体) {String} linkmobileAreaCode 联系人手机 地区码
     * @apiParam (请求体) {String} nationCode 国籍
     * @apiParam (请求体) {String} consCode 投顾
     * @apiParamExample 请求体示例
     * {"edulevel":"H8oqODMh","invsttype":"TJO","linkMobile":"cT","otherinvest":"8BKnfX","linkpostcode":"ecMSv","knowhowbuy":"TNo4og0FJ","beforeinvest":"askYV","conscustlvl":"rdyQ1Psx","idNo":"IP","operator":"aJEOZ","email2":"X7YywS","contacttime":"0hbPvD","citycode":"Jb","custsource":"s9","validitydt":"8uEJ5pFet7","fax":"qd8v","decisionflag":"FKX","postcode":"KLw","fincome":"GN12H","telNo":"5Y02k","linkman":"E2rN4","linkAddr":"u2GlGRbL","consCode":"FAI","idtype":"6Fgp1lmpKv","pincome":"M5gkdh","subknowtype":"Rl6","salon":"RiqDwbPSf","birthday":"9QcuYB","wechatcode":"h","gender":"fThE8","conscustgrade":"WEoSbc","remark":"lA","linkTel":"Ff","mobileAreaCode":"FsP3ui","mobile2":"w2Rcv","saledirection":"2VVVFyk","company":"ZNGpwOVtK","aptitude":"d0csi","addr":"H","postcode2":"Tp","email":"HECr4xmyYL","visitfqcy":"XRHjYmnV","nationCode":"xgp1","officetelno":"px","subknow":"mc","addr2":"GK9","nature":"Wd","mobile":"2M5Hn","scopebusiness":"T5p279yjIJ","vocation":"VuFT7qQ80","linkmobileAreaCode":"h","countyCode":"P4","provcode":"S97W219I","custSourceRemark":"MMJx9TLgV","validity":"YzTPoB","mobile2AreaCode":"9vbSCZGe9","interests":"b","married":"3","custname":"vidFKdD7","linkEmail":"zMx"}
     * @apiSuccess (响应结果) {String} code 状态码
     * @apiSuccess (响应结果) {String} description 描述信息
     * @apiSuccess (响应结果) {Object} data 数据封装
     * @apiSuccess (响应结果) {String} data.custNo 客户号
     * @apiSuccess (响应结果) {Array} data.repeatConsCustList 重复客户列表
     * @apiSuccess (响应结果) {String} data.repeatConsCustList.custNo 客户号
     * @apiSuccess (响应结果) {String} data.repeatConsCustList.custName 客户名称
     * @apiSuccess (响应结果) {String} data.operationid 潜客操作集合 id
     * @apiSuccessExample 响应结果示例
     * {"code":"A3aCQIo1","data":{"custNo":"H90","repeatConsCustList":[{"custNo":"9Zbs02wi","custName":"aZo"}],"operationid":"mrG"},"description":"nbfxm"}
     */
    @PostMapping("/createCustInfo")
    @ResponseBody
    public Response<CreateConsCustRespVO> createCustInfo(@RequestBody CreateConsCustRequest createCustReq) {
        createCustReq.setOperateChannel(Constants.OPERATE_CHANNEL_MENU);
        return consCustInfoService.createCustInfo(createCustReq);
    }


    /**
     * @api {POST} /custinfo/updateCustInfo updateCustInfo()
     * @apiVersion 1.0.0
     * @apiGroup CustInfoController
     * @apiName updateCustInfo()
     * @apiParam (请求体) {String} custNo 投顾客户编号
     * @apiParam (请求体) {String} invsttype 客户类型枚举 0 机构客户 1 个人客户 2 产品客户
     * @apiParam (请求体) {String} custname 客户名称
     * @apiParam (请求体) {String} mobile 手机号码-明文
     * @apiParam (请求体) {String} mobile2 手机号码2-明文
     * @apiParam (请求体) {String} email 邮箱-明文
     * @apiParam (请求体) {String} email2 邮箱2-明文
     * @apiParam (请求体) {String} telNo 座机号-明文
     * @apiParam (请求体) {String} fax 传真
     * @apiParam (请求体) {String} addr 地址-明文
     * @apiParam (请求体) {String} addr2 地址2 -明文
     * @apiParam (请求体) {String} postcode 邮政编码
     * @apiParam (请求体) {String} postcode2 邮政2 编码
     * @apiParam (请求体) {String} idSignAreaCode 证件地区码-身份证签发地区编码
     * @apiParam (请求体) {String} idtype 证件类型
     * @apiParam (请求体) {String} idNo 证件号码-明文
     * @apiParam (请求体) {String} wechatcode 微信号
     * @apiParam (请求体) {String} linkman 联系人姓名
     * @apiParam (请求体) {String} linkTel 联系人 座机号-明文
     * @apiParam (请求体) {String} linkMobile 联系人 手机号码-明文
     * @apiParam (请求体) {String} linkEmail 联系人 邮箱-明文
     * @apiParam (请求体) {String} linkAddr 联系人 地址-明文
     * @apiParam (请求体) {String} linkpostcode 经办人邮政编码
     * @apiParam (请求体) {String} provcode 省份代码
     * @apiParam (请求体) {String} citycode 城市代码
     * @apiParam (请求体) {String} countyCode 县代码
     * @apiParam (请求体) {String} knowhowbuy 知道好买
     * @apiParam (请求体) {String} subknow 知道好买细分
     * @apiParam (请求体) {String} subknowtype 知道好买细分分类
     * @apiParam (请求体) {String} custsource 客户来源： PH0806F18      [数据库字段：newsourceno]
     * @apiParam (请求体) {String} custSourceRemark 客户来源备注-投顾开发-电话营销
     * @apiParam (请求体) {String} gender 投资者性别(0：女；1：男)
     * @apiParam (请求体) {String} married 婚否
     * @apiParam (请求体) {String} conscustgrade 投顾客户评分
     * @apiParam (请求体) {String} visitfqcy 沟通频率 Eg:2
     * @apiParam (请求体) {String} edulevel 投资者学历
     * @apiParam (请求体) {String} vocation 投资者职业代码
     * @apiParam (请求体) {String} pincome 个人年收入
     * @apiParam (请求体) {String} fincome 家庭年收入
     * @apiParam (请求体) {String} decisionflag 是否投资决策人
     * @apiParam (请求体) {String} birthday 投资者生日
     * @apiParam (请求体) {String} company 公司
     * @apiParam (请求体) {String} officetelno 投资者 单位电话
     * @apiParam (请求体) {String} interests 兴趣爱好
     * @apiParam (请求体) {String} contacttime 方便联系时段
     * @apiParam (请求体) {String} remark 备注
     * @apiParam (请求体) {String} otherinvest 想了解的其他投资品种
     * @apiParam (请求体) {String} salon 希望参加的沙龙[hb_constant.PIncome]
     * @apiParam (请求体) {String} saledirection 销售方向
     * @apiParam (请求体) {String} beforeinvest 之前投资品种
     * @apiParam (请求体) {String} validity 证件有限期
     * @apiParam (请求体) {String} nature 性质
     * @apiParam (请求体) {String} aptitude 资质
     * @apiParam (请求体) {String} validitydt 证件有限期日期
     * @apiParam (请求体) {String} scopebusiness 经营范围
     * @apiParam (请求体) {String} conscustlvl 投顾客户等级
     * @apiParam (请求体) {String} mobileAreaCode 手机 地区码
     * @apiParam (请求体) {String} mobile2AreaCode 手机2 地区码
     * @apiParam (请求体) {String} linkmobileAreaCode 联系人手机 地区码
     * @apiParam (请求体) {String} nationCode 国籍
     * @apiParam (请求体) {String} consCode 投顾
     * @apiParam (请求体) {Boolean} flag 省市地址以及邮编有过修改
     * @apiParam (请求体) {String} operator 变更 操作人
     * @apiParam (请求体) {String} appserialNo 变更  流水号
     * @apiParam (请求体) {String} specialReason 变更 操作人
     * @apiParam (请求体) {String} explanation 变更 说明
     * @apiParam (请求体) {String} checker 变更 复核人
     * @apiParamExample 请求体示例
     * {"edulevel":"csW","invsttype":"JpNjzIr","linkMobile":"EPEs","otherinvest":"k6","linkpostcode":"uC","checker":"tWh","knowhowbuy":"AWFiV0Si","beforeinvest":"gsaE6","conscustlvl":"3mOZv94j","explanation":"5GzE","idNo":"Ikob5myUi","operator":"Wem","email2":"7rCCuLn39","contacttime":"dRElBaWF","citycode":"aFPac","custsource":"1d2D1S","validitydt":"J5d","fax":"X4nIYgD","decisionflag":"KPvi","postcode":"tbL1j4K","fincome":"TbAxqa0O","telNo":"MwRrvbkU6","linkman":"B","linkAddr":"KCwv4J","consCode":"ylj5Od2c","idSignAreaCode":"v7oJveve1l","idtype":"4wyFPyaN","specialReason":"6uuxuWA","pincome":"MuLq","subknowtype":"tdvqG5Vjk","salon":"Ng5OCGfX","birthday":"fI3p8c","appserialNo":"d","flag":false,"wechatcode":"LaD4Ac1ivy","gender":"DY","conscustgrade":"bd82","remark":"qq","linkTel":"JUQNzBX","mobileAreaCode":"AKlpuR","mobile2":"bSWBMRg2jN","saledirection":"vOw77QE","company":"gwSC","aptitude":"TNZLG0cOv","addr":"Tf2hoxzt8Q","postcode2":"qy","email":"sHN","visitfqcy":"fxfj1ICgjk","nationCode":"D6lowa","officetelno":"CMXofYxe","subknow":"NW0sfavgDA","custNo":"8","addr2":"iqmKw","nature":"fQGH6NSW","mobile":"A","scopebusiness":"M0bflEo","vocation":"14C","linkmobileAreaCode":"CHecw8hV","countyCode":"jOmViwUbYf","provcode":"jkmZkIzLr1","custSourceRemark":"CfSgrkq","validity":"jAFs2aK","mobile2AreaCode":"YQkrFvH4x","interests":"dC8a8Vv0Wd","married":"oOgOk0hiFN","custname":"5slr","linkEmail":"n8Yz74"}
     * @apiSuccess (响应结果) {String} code 状态码
     * @apiSuccess (响应结果) {String} description 描述信息
     * @apiSuccess (响应结果) {Object} data 数据封装
     * @apiSuccess (响应结果) {String} data.custNo 客户号
     * @apiSuccess (响应结果) {Array} data.repeatConsCustList 重复客户列表
     * @apiSuccess (响应结果) {String} data.repeatConsCustList.custNo 客户号
     * @apiSuccess (响应结果) {String} data.repeatConsCustList.custName 客户名称
     * @apiSuccess (响应结果) {String} data.operationid 潜客操作集合 id
     * @apiSuccessExample 响应结果示例
     * {"code":"j4iIHo","data":{"custNo":"fggF","repeatConsCustList":[{"custNo":"kQheUR5pap","custName":"jPp27Up"}],"operationid":"CxWBOR"},"description":"7fQ"}
     */
    @PostMapping("/updateCustInfo")
    @ResponseBody
    public Response<UpdateConsCustRespVO> updateCustInfo(@RequestBody UpdateConsCustRequest updateCustReq) {
        return consCustInfoService.updateCustInfo(updateCustReq);
    }


    /**
     * @api {POST} /custinfo/updateCustInfoWithoutCheckUpdateField updateCustInfoWithoutCheckUpdateField()
     * @apiVersion 1.0.0
     * @apiGroup CustInfoController
     * @apiName updateCustInfoWithoutCheckUpdateField()
     * @apiDescription 更新投顾客户信息，不校验字段是否允许修改
     * @apiParam (请求体) {String} custNo 投顾客户编号
     * @apiParam (请求体) {Boolean} flag 省市地址以及邮编有过修改
     * @apiParam (请求体) {String} specialReason 变更 操作人
     * @apiParam (请求体) {String} explanation 变更 说明
     * @apiParam (请求体) {String} checker 变更 复核人
     * @apiParam (请求体) {String} operateSource 非必须      操作来源 [1-MQ]时，为[异常来源]，参考枚举类[{@link com.howbuy.crm.account.client.enums.custinfo.FullCustSourceEnum}]             [2-菜单页面]时，为菜单名称
     * @apiParam (请求体) {String} operateChannel 非必须      操作通道 1-MQ  2-菜单页面
     * @apiParam (请求体) {String} operator 操作人
     * @apiParam (请求体) {String} invsttype 客户类型枚举 0 机构客户 1 个人客户 2 产品客户
     * @apiParam (请求体) {String} custname 客户名称
     * @apiParam (请求体) {String} mobile 手机号码-明文
     * @apiParam (请求体) {String} mobile2 手机号码2-明文
     * @apiParam (请求体) {String} email 邮箱-明文
     * @apiParam (请求体) {String} email2 邮箱2-明文
     * @apiParam (请求体) {String} telNo 座机号-明文
     * @apiParam (请求体) {String} fax 传真
     * @apiParam (请求体) {String} addr 地址-明文
     * @apiParam (请求体) {String} addr2 地址2 -明文
     * @apiParam (请求体) {String} postcode 邮政编码
     * @apiParam (请求体) {String} postcode2 邮政2 编码
     * @apiParam (请求体) {String} idtype 证件类型
     * @apiParam (请求体) {String} idNo 证件号码-明文
     * @apiParam (请求体) {String} idSignAreaCode 证件地区码
     * @apiParam (请求体) {String} wechatcode 微信号
     * @apiParam (请求体) {String} linkman 联系人姓名
     * @apiParam (请求体) {String} linkTel 联系人 座机号-明文
     * @apiParam (请求体) {String} linkMobile 联系人 手机号码-明文
     * @apiParam (请求体) {String} linkEmail 联系人 邮箱-明文
     * @apiParam (请求体) {String} linkAddr 联系人 地址-明文
     * @apiParam (请求体) {String} linkpostcode 经办人邮政编码
     * @apiParam (请求体) {String} provcode 省份代码
     * @apiParam (请求体) {String} citycode 城市代码
     * @apiParam (请求体) {String} countyCode 县代码
     * @apiParam (请求体) {String} knowhowbuy 知道好买
     * @apiParam (请求体) {String} subknow 知道好买细分
     * @apiParam (请求体) {String} subknowtype 知道好买细分分类
     * @apiParam (请求体) {String} custsource 客户来源： PH0806F18      [数据库字段：newsourceno]
     * @apiParam (请求体) {String} custSourceRemark 客户来源备注-投顾开发-电话营销
     * @apiParam (请求体) {String} gender 投资者性别(0：女；1：男)
     * @apiParam (请求体) {String} married 婚否
     * @apiParam (请求体) {String} conscustgrade 投顾客户评分
     * @apiParam (请求体) {String} visitfqcy 沟通频率 Eg:2
     * @apiParam (请求体) {String} edulevel 投资者学历
     * @apiParam (请求体) {String} vocation 投资者职业代码
     * @apiParam (请求体) {String} pincome 个人年收入
     * @apiParam (请求体) {String} fincome 家庭年收入
     * @apiParam (请求体) {String} decisionflag 是否投资决策人
     * @apiParam (请求体) {String} birthday 投资者生日
     * @apiParam (请求体) {String} company 公司
     * @apiParam (请求体) {String} officetelno 投资者 单位电话
     * @apiParam (请求体) {String} interests 兴趣爱好
     * @apiParam (请求体) {String} contacttime 方便联系时段
     * @apiParam (请求体) {String} remark 备注
     * @apiParam (请求体) {String} otherinvest 想了解的其他投资品种
     * @apiParam (请求体) {String} salon 希望参加的沙龙[hb_constant.PIncome]
     * @apiParam (请求体) {String} saledirection 销售方向
     * @apiParam (请求体) {String} beforeinvest 之前投资品种
     * @apiParam (请求体) {String} validity 证件有限期      是否长期有效0-否,1-是
     * @apiParam (请求体) {String} nature 性质
     * @apiParam (请求体) {String} aptitude 资质
     * @apiParam (请求体) {String} validitydt 证件有限期日期
     * @apiParam (请求体) {String} validityst 证件有效起始日
     * @apiParam (请求体) {String} scopebusiness 经营范围
     * @apiParam (请求体) {String} conscustlvl 投顾客户等级
     * @apiParam (请求体) {String} mobileAreaCode 手机 地区码
     * @apiParam (请求体) {String} mobile2AreaCode 手机2 地区码
     * @apiParam (请求体) {String} linkmobileAreaCode 联系人手机 地区码
     * @apiParam (请求体) {String} nationCode 国籍
     * @apiParam (请求体) {String} acregdt 客户登记日期
     * @apiParamExample 请求体示例
     * {"edulevel":"6XgQQb","operateChannel":"d4B","invsttype":"59RB","linkMobile":"rSF05ka","operateSource":"K8t","otherinvest":"N9P","linkpostcode":"lYSIpM7e","checker":"X9jXJeC6y","explanation":"kbbfIjB","knowhowbuy":"YIJ8NWZ1cw","beforeinvest":"kbC9eHPQHC","conscustlvl":"zZmlt","idNo":"qi","operator":"4c9Bod62p","email2":"WaOxVRf6i","contacttime":"39V7cD","citycode":"u","custsource":"SZeAnPW","validitydt":"gF3mR7G","fax":"Li5g","decisionflag":"WeFDUT7w","postcode":"Mv3Dv","fincome":"m","telNo":"VQofiYNH","linkman":"32DiZ","linkAddr":"xHr","idtype":"PPw1","idSignAreaCode":"RA","specialReason":"LydwJ1E","pincome":"M","subknowtype":"l9GZBdE","salon":"M9fBNwfzN","birthday":"DRfIVWj","flag":true,"wechatcode":"HfaaiTHiy","gender":"yE","conscustgrade":"L2Fw4","remark":"NnSNWyB","validityst":"qFu","linkTel":"oSwVQo9Aot","mobileAreaCode":"ODz","mobile2":"9V42H0","saledirection":"o","company":"WfOJ","aptitude":"FeH5M3g8","addr":"60K7o","postcode2":"X","email":"xx8eG2svI","visitfqcy":"qkTb","nationCode":"Fo","officetelno":"aJYttjT","acregdt":"akY","subknow":"eXZU","custNo":"uXBL6","addr2":"M5B","nature":"vKkCp","mobile":"rLiAaMyLVS","scopebusiness":"Bk85k78p","vocation":"hfl","linkmobileAreaCode":"I3bfZY3mOr","countyCode":"N","provcode":"JhImr","custSourceRemark":"oxm","validity":"fLh","mobile2AreaCode":"Y560lyzv94","interests":"V3QBz","married":"KoOJLz","custname":"ipI","linkEmail":"0xxRenf8Ad"}
     * @apiSuccess (响应结果) {String} code 状态码
     * @apiSuccess (响应结果) {String} description 描述信息
     * @apiSuccess (响应结果) {Object} data 数据封装
     * @apiSuccess (响应结果) {String} data.custNo 客户号
     * @apiSuccess (响应结果) {Array} data.repeatConsCustList 重复客户列表
     * @apiSuccess (响应结果) {String} data.repeatConsCustList.custNo 客户号
     * @apiSuccess (响应结果) {String} data.repeatConsCustList.custName 客户名称
     * @apiSuccess (响应结果) {String} data.operationid 潜客操作集合 id
     * @apiSuccessExample 响应结果示例
     * {"code":"HUG2GqP","data":{"custNo":"g8H3IGkrsG","repeatConsCustList":[{"custNo":"5XsSIMg","custName":"C"}],"operationid":"1plg"},"description":"5oe"}
     */
    @PostMapping("/updateCustInfoWithoutCheckUpdateField")
    @ResponseBody
    public Response<UpdateConsCustRespVO> updateCustInfoWithoutCheckUpdateField(@RequestBody UpdateConsCustRequest updateCustReq) {
        return consCustInfoService.updateCustInfoWithoutCheckUpdateField(updateCustReq);
    }

    /**
     * @api {POST} /custinfo/removeCustMobileByCustNo removeCustMobileByCustNo()
     * @apiVersion 1.0.0
     * @apiGroup CustInfoController
     * @apiName removeCustMobileByCustNo()
     * @apiParam (请求体) {String} custNo 投顾客户编号
     * @apiParam (请求体) {Boolean} flag 省市地址以及邮编有过修改
     * @apiParam (请求体) {String} specialReason 变更 操作人
     * @apiParam (请求体) {String} explanation 变更 说明
     * @apiParam (请求体) {String} checker 变更 复核人
     * @apiParam (请求体) {String} operateSource 非必须      操作来源 [1-MQ]时，为[异常来源]，参考枚举类[{@link com.howbuy.crm.account.client.enums.custinfo.FullCustSourceEnum}]             [2-菜单页面]时，为菜单名称
     * @apiParam (请求体) {String} operateChannel 非必须      操作通道 1-MQ  2-菜单页面
     * @apiParam (请求体) {String} operator 操作人
     * @apiParam (请求体) {String} invsttype 客户类型枚举 0 机构客户 1 个人客户 2 产品客户
     * @apiParam (请求体) {String} custname 客户名称
     * @apiParam (请求体) {String} mobile 手机号码-明文
     * @apiParam (请求体) {String} mobile2 手机号码2-明文
     * @apiParam (请求体) {String} email 邮箱-明文
     * @apiParam (请求体) {String} email2 邮箱2-明文
     * @apiParam (请求体) {String} telNo 座机号-明文
     * @apiParam (请求体) {String} fax 传真
     * @apiParam (请求体) {String} addr 地址-明文
     * @apiParam (请求体) {String} addr2 地址2 -明文
     * @apiParam (请求体) {String} postcode 邮政编码
     * @apiParam (请求体) {String} postcode2 邮政2 编码
     * @apiParam (请求体) {String} idtype 证件类型
     * @apiParam (请求体) {String} idNo 证件号码-明文
     * @apiParam (请求体) {String} idSignAreaCode 证件地区码
     * @apiParam (请求体) {String} wechatcode 微信号
     * @apiParam (请求体) {String} linkman 联系人姓名
     * @apiParam (请求体) {String} linkTel 联系人 座机号-明文
     * @apiParam (请求体) {String} linkMobile 联系人 手机号码-明文
     * @apiParam (请求体) {String} linkEmail 联系人 邮箱-明文
     * @apiParam (请求体) {String} linkAddr 联系人 地址-明文
     * @apiParam (请求体) {String} linkpostcode 经办人邮政编码
     * @apiParam (请求体) {String} provcode 省份代码
     * @apiParam (请求体) {String} citycode 城市代码
     * @apiParam (请求体) {String} countyCode 县代码
     * @apiParam (请求体) {String} knowhowbuy 知道好买
     * @apiParam (请求体) {String} subknow 知道好买细分
     * @apiParam (请求体) {String} subknowtype 知道好买细分分类
     * @apiParam (请求体) {String} custsource 客户来源： PH0806F18      [数据库字段：newsourceno]
     * @apiParam (请求体) {String} custSourceRemark 客户来源备注-投顾开发-电话营销
     * @apiParam (请求体) {String} gender 投资者性别(0：女；1：男)
     * @apiParam (请求体) {String} married 婚否
     * @apiParam (请求体) {String} conscustgrade 投顾客户评分
     * @apiParam (请求体) {String} visitfqcy 沟通频率 Eg:2
     * @apiParam (请求体) {String} edulevel 投资者学历
     * @apiParam (请求体) {String} vocation 投资者职业代码
     * @apiParam (请求体) {String} pincome 个人年收入
     * @apiParam (请求体) {String} fincome 家庭年收入
     * @apiParam (请求体) {String} decisionflag 是否投资决策人
     * @apiParam (请求体) {String} birthday 投资者生日
     * @apiParam (请求体) {String} company 公司
     * @apiParam (请求体) {String} officetelno 投资者 单位电话
     * @apiParam (请求体) {String} interests 兴趣爱好
     * @apiParam (请求体) {String} contacttime 方便联系时段
     * @apiParam (请求体) {String} remark 备注
     * @apiParam (请求体) {String} otherinvest 想了解的其他投资品种
     * @apiParam (请求体) {String} salon 希望参加的沙龙[hb_constant.PIncome]
     * @apiParam (请求体) {String} saledirection 销售方向
     * @apiParam (请求体) {String} beforeinvest 之前投资品种
     * @apiParam (请求体) {String} validity 证件有限期
     * @apiParam (请求体) {String} nature 性质
     * @apiParam (请求体) {String} aptitude 资质
     * @apiParam (请求体) {String} validitydt 证件有限期日期
     * @apiParam (请求体) {String} scopebusiness 经营范围
     * @apiParam (请求体) {String} conscustlvl 投顾客户等级
     * @apiParam (请求体) {String} mobileAreaCode 手机 地区码
     * @apiParam (请求体) {String} mobile2AreaCode 手机2 地区码
     * @apiParam (请求体) {String} linkmobileAreaCode 联系人手机 地区码
     * @apiParam (请求体) {String} nationCode 国籍
     * @apiParam (请求体) {String} acregdt 客户登记日期
     * @apiParamExample 请求体示例
     * {"edulevel":"4XU","operateChannel":"eRd5La","invsttype":"pre","linkMobile":"IEOI0qDs","operateSource":"Z","otherinvest":"FUwo6","linkpostcode":"wo","checker":"q","explanation":"N3iqR","knowhowbuy":"ahlpm","beforeinvest":"bPYpkI","conscustlvl":"rgeUx6o","idNo":"U","operator":"PUeyp","email2":"3O3Hv","contacttime":"k","citycode":"dnZoihn","custsource":"bIzxH","validitydt":"HWOJhXA6bE","fax":"5BNusEo","decisionflag":"lL","postcode":"nINSU","fincome":"9W55","telNo":"YU","linkman":"mUak","linkAddr":"69C2KO","idtype":"FGhE7eu","idSignAreaCode":"FSamoA","specialReason":"223mty0","pincome":"5CtvsI44tP","subknowtype":"s","salon":"WWNPVlOJU","birthday":"UsZo","flag":true,"wechatcode":"q6fFjUFm","gender":"2fuBj1nJ","conscustgrade":"lf63OKAILT","remark":"UO","linkTel":"BCVb","mobileAreaCode":"hqj1IEc","mobile2":"Gh","saledirection":"gHS","company":"9kXLgqOv","aptitude":"foj4V9JWA","addr":"NNtjQ","postcode2":"aLe","email":"wmV","visitfqcy":"25zE","nationCode":"p12","officetelno":"5RXKRFa7","acregdt":"XhZ40jELFv","subknow":"GLZXaX0","custNo":"t0L8dg","addr2":"ruO2nPO3k4","nature":"Fa9f0v","mobile":"6qjYVB","scopebusiness":"Z6Dw","vocation":"ZwIJ","linkmobileAreaCode":"8haOQ7b8F","countyCode":"Z1ueGm","provcode":"9","custSourceRemark":"Jhis0p","validity":"EC","mobile2AreaCode":"fE2ZQzlsV","interests":"KmB9Ly1","married":"cn","custname":"yI","linkEmail":"7Y8mqR4f"}
     * @apiSuccess (响应结果) {String} code 状态码
     * @apiSuccess (响应结果) {String} description 描述信息
     * @apiSuccess (响应结果) {String} data 数据封装
     * @apiSuccessExample 响应结果示例
     * {"code":"51nbcI3x","data":"h1q7iCNzh4","description":"VEi"}
     */
    @PostMapping("/removeCustMobileByCustNo")
    @ResponseBody
    public Response<String> removeCustMobileByCustNo(@RequestBody UpdateConsCustRequest updateCustReq) {
        return consCustInfoService.removeCustMobileByCustNo(updateCustReq);
    }

    /**
     * @api {POST} /custinfo/removeCustIdInfoByCustNo removeCustIdInfoByCustNo()
     * @apiVersion 1.0.0
     * @apiGroup CustInfoController
     * @apiName removeCustIdInfoByCustNo()
     * @apiParam (请求体) {String} custNo 投顾客户编号
     * @apiParam (请求体) {Boolean} flag 省市地址以及邮编有过修改
     * @apiParam (请求体) {String} specialReason 变更 操作人
     * @apiParam (请求体) {String} explanation 变更 说明
     * @apiParam (请求体) {String} checker 变更 复核人
     * @apiParam (请求体) {String} operateSource 非必须      操作来源 [1-MQ]时，为[异常来源]，参考枚举类[{@link com.howbuy.crm.account.client.enums.custinfo.FullCustSourceEnum}]             [2-菜单页面]时，为菜单名称
     * @apiParam (请求体) {String} operateChannel 非必须      操作通道 1-MQ  2-菜单页面
     * @apiParam (请求体) {String} operator 操作人
     * @apiParam (请求体) {String} invsttype 客户类型枚举 0 机构客户 1 个人客户 2 产品客户
     * @apiParam (请求体) {String} custname 客户名称
     * @apiParam (请求体) {String} mobile 手机号码-明文
     * @apiParam (请求体) {String} mobile2 手机号码2-明文
     * @apiParam (请求体) {String} email 邮箱-明文
     * @apiParam (请求体) {String} email2 邮箱2-明文
     * @apiParam (请求体) {String} telNo 座机号-明文
     * @apiParam (请求体) {String} fax 传真
     * @apiParam (请求体) {String} addr 地址-明文
     * @apiParam (请求体) {String} addr2 地址2 -明文
     * @apiParam (请求体) {String} postcode 邮政编码
     * @apiParam (请求体) {String} postcode2 邮政2 编码
     * @apiParam (请求体) {String} idtype 证件类型
     * @apiParam (请求体) {String} idNo 证件号码-明文
     * @apiParam (请求体) {String} idSignAreaCode 证件地区码
     * @apiParam (请求体) {String} wechatcode 微信号
     * @apiParam (请求体) {String} linkman 联系人姓名
     * @apiParam (请求体) {String} linkTel 联系人 座机号-明文
     * @apiParam (请求体) {String} linkMobile 联系人 手机号码-明文
     * @apiParam (请求体) {String} linkEmail 联系人 邮箱-明文
     * @apiParam (请求体) {String} linkAddr 联系人 地址-明文
     * @apiParam (请求体) {String} linkpostcode 经办人邮政编码
     * @apiParam (请求体) {String} provcode 省份代码
     * @apiParam (请求体) {String} citycode 城市代码
     * @apiParam (请求体) {String} countyCode 县代码
     * @apiParam (请求体) {String} knowhowbuy 知道好买
     * @apiParam (请求体) {String} subknow 知道好买细分
     * @apiParam (请求体) {String} subknowtype 知道好买细分分类
     * @apiParam (请求体) {String} custsource 客户来源： PH0806F18      [数据库字段：newsourceno]
     * @apiParam (请求体) {String} custSourceRemark 客户来源备注-投顾开发-电话营销
     * @apiParam (请求体) {String} gender 投资者性别(0：女；1：男)
     * @apiParam (请求体) {String} married 婚否
     * @apiParam (请求体) {String} conscustgrade 投顾客户评分
     * @apiParam (请求体) {String} visitfqcy 沟通频率 Eg:2
     * @apiParam (请求体) {String} edulevel 投资者学历
     * @apiParam (请求体) {String} vocation 投资者职业代码
     * @apiParam (请求体) {String} pincome 个人年收入
     * @apiParam (请求体) {String} fincome 家庭年收入
     * @apiParam (请求体) {String} decisionflag 是否投资决策人
     * @apiParam (请求体) {String} birthday 投资者生日
     * @apiParam (请求体) {String} company 公司
     * @apiParam (请求体) {String} officetelno 投资者 单位电话
     * @apiParam (请求体) {String} interests 兴趣爱好
     * @apiParam (请求体) {String} contacttime 方便联系时段
     * @apiParam (请求体) {String} remark 备注
     * @apiParam (请求体) {String} otherinvest 想了解的其他投资品种
     * @apiParam (请求体) {String} salon 希望参加的沙龙[hb_constant.PIncome]
     * @apiParam (请求体) {String} saledirection 销售方向
     * @apiParam (请求体) {String} beforeinvest 之前投资品种
     * @apiParam (请求体) {String} validity 证件有限期
     * @apiParam (请求体) {String} nature 性质
     * @apiParam (请求体) {String} aptitude 资质
     * @apiParam (请求体) {String} validitydt 证件有限期日期
     * @apiParam (请求体) {String} scopebusiness 经营范围
     * @apiParam (请求体) {String} conscustlvl 投顾客户等级
     * @apiParam (请求体) {String} mobileAreaCode 手机 地区码
     * @apiParam (请求体) {String} mobile2AreaCode 手机2 地区码
     * @apiParam (请求体) {String} linkmobileAreaCode 联系人手机 地区码
     * @apiParam (请求体) {String} nationCode 国籍
     * @apiParam (请求体) {String} acregdt 客户登记日期
     * @apiParamExample 请求体示例
     * {"edulevel":"1xRBSQX1","operateChannel":"IJ6sA","invsttype":"Qzyje","linkMobile":"sSBZMMrs","operateSource":"hr","otherinvest":"iDQL","linkpostcode":"YJWI4hQSHB","checker":"rsYdFH","explanation":"7c","knowhowbuy":"gK7rkfcG","beforeinvest":"erY0fLm1","conscustlvl":"2koIJL","idNo":"FFTI","operator":"R","email2":"GIDcuBdQG","contacttime":"25E62o5r","citycode":"XGVGGajDU","custsource":"cip","validitydt":"xZn","fax":"EdJP","decisionflag":"T0Yh","postcode":"SayQnm1x8","fincome":"F1S3dqD","telNo":"np4Q","linkman":"hX","linkAddr":"HxU2V9dCU","idtype":"QHnl","idSignAreaCode":"395VqL8Xai","specialReason":"mQzXJz0qSx","pincome":"WXvc","subknowtype":"tZ3","salon":"z","birthday":"Tp5JE9oVxV","flag":false,"wechatcode":"v9we","gender":"HhNG9","conscustgrade":"uIJgIVC5","remark":"hHVHg","linkTel":"O13Iih07sZ","mobileAreaCode":"jl","mobile2":"hRH","saledirection":"CXgJ","company":"qi25Rm0uy","aptitude":"H0F5RPyb","addr":"8ME7pp","postcode2":"AFRqMRxFU","email":"YwCzE","visitfqcy":"Uh1UzuLrD4","nationCode":"rGalzK","officetelno":"1V6zj8KRsH","acregdt":"SylUnG","subknow":"ucvLT","custNo":"B4zLLfpL5r","addr2":"HuwHkiyde","nature":"GYn0UN0k","mobile":"qp7vBMW","scopebusiness":"nr","vocation":"J3ixQONy","linkmobileAreaCode":"j1Z","countyCode":"7T","provcode":"rwCfFo2Ae5","custSourceRemark":"1CikYBo5VB","validity":"t","mobile2AreaCode":"bTAP","interests":"y5Vu5TyNGV","married":"RMzC1GMUf","custname":"TjSz","linkEmail":"Q"}
     * @apiSuccess (响应结果) {String} code 状态码
     * @apiSuccess (响应结果) {String} description 描述信息
     * @apiSuccess (响应结果) {String} data 数据封装
     * @apiSuccessExample 响应结果示例
     * {"code":"Sb8qI","data":"1rYRpU","description":"DEgg"}
     */
    @PostMapping("/removeCustIdInfoByCustNo")
    @ResponseBody
    public Response<String> removeCustIdInfoByCustNo(@RequestBody UpdateConsCustRequest updateCustReq) {
        return consCustInfoService.removeCustIdInfoByCustNo(updateCustReq);
    }

}