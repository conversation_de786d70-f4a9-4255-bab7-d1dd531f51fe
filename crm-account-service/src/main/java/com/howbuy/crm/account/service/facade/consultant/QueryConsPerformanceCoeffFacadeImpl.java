/**
 * Copyright (c) 2024, <PERSON><PERSON><PERSON><PERSON> HOWBUY INVESTMENT MANAGEMENT Co., Ltd.
 * All right reserved.
 * <p>
 * THIS IS UNPUBLISHED PROPRIETARY SOURCE CODE OF HOWBUY INVESTMENT
 * MANAGEMENT CO., LTD.  THE CONTENTS OF THIS FILE MAY NOT BE DISCLOSED
 * TO THIRD PARTIES, COPIED OR DUPLICATED IN ANY FORM, IN WHOLE OR IN PART,
 * WITHOUT THE PRIOR WRITTEN PERMISSION OF HOWBUY INVESTMENT MANAGEMENT
 * CO., LTD.
 */
package com.howbuy.crm.account.service.facade.consultant;

import com.howbuy.crm.account.client.facade.consultant.QueryConsPerformanceCoeffFacade;
import com.howbuy.crm.account.client.request.consultant.QueryConsPerformanceCoeffRequest;
import com.howbuy.crm.account.client.response.Response;
import com.howbuy.crm.account.client.response.consultant.QueryConsPerformanceCoeffVO;
import com.howbuy.crm.account.service.service.consultant.QueryConsPerformanceCoeffService;
import lombok.extern.slf4j.Slf4j;
import org.apache.dubbo.config.annotation.DubboService;

import javax.annotation.Resource;

/**
 * @description: 获取人员绩效系数表接口实现
 * <AUTHOR>
 * @date 2025-07-02 16:15:20
 * @since JDK 1.8
 */
@Slf4j
@DubboService
public class QueryConsPerformanceCoeffFacadeImpl implements QueryConsPerformanceCoeffFacade {

    @Resource
    private QueryConsPerformanceCoeffService queryConsPerformanceCoeffService;

    /**
     * @api {DUBBO} com.howbuy.crm.account.client.facade.consultant.QueryConsPerformanceCoeffFacade.execute()
     * @apiVersion 1.0.0
     * @apiGroup QueryConsPerformanceCoeffFacade
     * @apiName execute()
     * @apiDescription 获取人员绩效系数表
     * @apiParam (请求体) {String} consCustNo 投顾客户号
     * @apiParam (请求体) {String} consCode 投顾code
     * @apiParamExample 请求体示例
     * {"consCustNo":"123456","consCode":"CONS001"}
     * @apiSuccess (响应结果) {String} code 状态码
     * @apiSuccess (响应结果) {String} description 描述信息
     * @apiSuccess (响应结果) {Object} data 数据封装
     * @apiSuccess (响应结果) {Date} data.assignTime 分配时间
     * @apiSuccess (响应结果) {String} data.consCustNo 投顾客户号
     * @apiSuccess (响应结果) {String} data.consCode 投顾code
     * @apiSuccess (响应结果) {String} data.sourceType 来源类型
     * @apiSuccess (响应结果) {String} data.custConversionCoeff 客户折算系数
     * @apiSuccess (响应结果) {BigDecimal} data.manageCoeffSubtotal 管理系数-分总
     * @apiSuccess (响应结果) {BigDecimal} data.manageCoeffRegionalsubtotal 管理系数-区副
     * @apiSuccess (响应结果) {BigDecimal} data.manageCoeffRegionaltotal 管理系数-区总
     * @apiSuccessExample 响应结果示例
     * {"code":"0000","description":"成功","data":{"assignTime":"2024-12-19","consCustNo":"123456","consCode":"CONS001","sourceType":"公司资源","custConversionCoeff":"1.0","manageCoeffSubtotal":0.8,"manageCoeffRegionalsubtotal":0.6,"manageCoeffRegionaltotal":0.4}}
     * @param request 请求参数
     * @return com.howbuy.crm.account.client.response.Response<com.howbuy.crm.account.client.response.consultant.QueryConsPerformanceCoeffVO>
     * @description: 获取人员绩效系数表
     * <AUTHOR>
     * @date 2025-07-02 16:15:20
     * @since JDK 1.8
     */
    @Override
    public Response<QueryConsPerformanceCoeffVO> execute(QueryConsPerformanceCoeffRequest request) {
        log.info("获取人员绩效系数表，请求参数：{}", request);
        try {
            QueryConsPerformanceCoeffVO result = queryConsPerformanceCoeffService.execute(request);
            log.info("获取人员绩效系数表完成，结果：{}", result);
            return Response.ok(result);
        } catch (Exception e) {
            log.error("获取人员绩效系数表异常", e);
            return Response.fail("获取人员绩效系数表失败：" + e.getMessage());
        }
    }
} 