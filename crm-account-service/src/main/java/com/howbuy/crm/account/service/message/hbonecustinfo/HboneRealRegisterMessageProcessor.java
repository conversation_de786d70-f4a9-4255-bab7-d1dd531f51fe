/**
 * Copyright (c) 2023, <PERSON><PERSON><PERSON><PERSON> HOWBUY INVESTMENT MANAGEMENT Co., Ltd.
 * All right reserved.
 * <p>
 * THIS IS UNPUBLISHED PROPRIETARY SOURCE CODE OF HOWBUY INVESTMENT
 * MANAGEMENT CO., LTD.  THE CONTENTS OF THIS FILE MAY NOT BE DISCLOSED
 * TO THIRD PARTIES, COPIED OR DUPLICATED IN ANY FORM, IN WHOLE OR IN PART,
 * WITHOUT THE PRIOR WRITTEN PERMISSION OF HOWBUY INVESTMENT MANAGEMENT
 * CO., LTD.
 */
package com.howbuy.crm.account.service.message.hbonecustinfo;

import com.howbuy.common.utils.StringUtil;
import com.howbuy.crm.account.client.enums.custinfo.ConsCustInvestTypeEnum;
import com.howbuy.crm.account.client.enums.custinfo.FullCustSourceEnum;
import com.howbuy.crm.account.client.response.Response;
import com.howbuy.crm.account.dao.bo.custinfo.CmConscustForAnalyseBO;
import com.howbuy.crm.account.service.business.custinfo.CmCustProcessBusinessService;
import com.howbuy.crm.account.service.commom.constant.Constants;
import com.howbuy.crm.account.service.message.dto.HboneRealRegisterAcctMessageDTO;
import com.howbuy.crm.account.service.vo.custinfo.AbnormalAnalyseResultVO;
import com.howbuy.crm.account.service.vo.custinfo.HboneMessageCustInfoVO;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;

import java.util.Arrays;
import java.util.List;

/**
 * @description: ([一账通账户]-[实名开通一账通] 消息处理器[topic.hbone.openAcct])
 * <AUTHOR>
 * @date 2023/12/11 17:04
 * @since JDK 1.8
 */
@Slf4j
@Service
public class HboneRealRegisterMessageProcessor extends AbstractHboneMessageProcessor<HboneRealRegisterAcctMessageDTO> {


    /**
     * [TOPIC_OPEN_ACC]  一账通开户
     */
    @Value("${topic.hbone.openAcct}")
    private String topicHboneOpenAcct;


    /**
     * 实名开通一账通
     * [REAL_REGISTER]
     */
    @Value("${tag.hbone.realRegister}")
    private String tagName;


    //消息体 示例：
//    {
//        "clientId": "95d8e8dcb4df44ef89940d864d8e7c10",
//            "content": {
//        "body": {
//            "authCardCipher": null,
//                    "authCardDigest": null,
//                    "authCardMask": null,
//                    "authDate": "20231127",
//                    "authOutletCode": "GSH000001",
//                    "authType": "1",
//                    "custName": "李齐",
//                    "disCode": "HB000A001",
//                    "hboneNo": "9200472066",
//                    "hkCustNo": "",
//                    "idNoCipher": "mk3z9mLUSk07CZrFb-4o6w==01",
//                    "idNoDigest": "00d2d7458ecdf060631f875b68bf10bd",
//                    "idNoMask": "EA337****",
//                    "idType": "1",
//                    "invstType": "1",
//                    "ip": null,
//                    "mobileCipher": "ctnPX4Lx1TTboGO7cTOcyw==01",
//                    "mobileDigest": "229ca753758b215fe3ec595aaf3a6718",
//                    "mobileMask": "1531717****",
//                    "mobileVerifyStatus": "0",
//                    "regDate": "20231127",
//                    "regOutletCode": "GSH000001",
//                    "txAcctNo": null,
//                    "username": null
//        },
//        "head": {
//            "createTime": "20231127155926",
//                    "eventCode": "520185",
//                    "hboneNo": "9200472066",
//                    "msgKey": "9397b03916e813ab2ac885f4454f9ecb",
//                    "tag": "REAL_REGISTER"
//        }
//    },
//        "messageChannel": "TOPIC_OPEN_ACC"
//    }


    @Autowired
    private CmCustProcessBusinessService processBusinessService;


    @Override
    public String getQuartMessageChannel() {
        return topicHboneOpenAcct;
    }

    @Override
    public List<String> getSupportTagList() {
        return Arrays.asList(tagName);
    }



    @Override
    HboneMessageCustInfoVO constructByMessage(HboneRealRegisterAcctMessageDTO  acctMsgDto) {
        HboneMessageCustInfoVO custVo=new HboneMessageCustInfoVO();
        custVo.setHboneNo(acctMsgDto.getHboneNo());
//        custVo.setCustName(null);
        // 一账通开户消息 默认数据赋值： 客户类别=个人客户
        custVo.setInvestType(ConsCustInvestTypeEnum.PERSONAL.getCode());
        // 一账通开户消息 默认数据赋值： 手机地区码=86
        custVo.setMobileAreaCode(Constants.DEFAULT_MOBILE_AREA_CODE);
        custVo.setRegDate(acctMsgDto.getRegDate());
        custVo.setDisCode(acctMsgDto.getDisCode());

        fillHboneAcctInfo(custVo);
        return custVo;
    }

    @Override
    public FullCustSourceEnum getCustSource() {
        return FullCustSourceEnum.HBONE_REAL_NAME_REGISTER;
    }

    @Override
    Response<String> processHboneMessage(AbnormalAnalyseResultVO<HboneMessageCustInfoVO> resultVo) {

//        实名注册一账通消息处理 不会 新增投顾客户号

        CmConscustForAnalyseBO processedCustInfo= resultVo.getProcessedCustInfo();
        HboneMessageCustInfoVO analyseVo=resultVo.getAnalyseVo();
        String hboneNo=analyseVo.getHboneNo();

        if(processedCustInfo==null){
            log.info("一账通实名开户消息处理： 一账通号[{}] 未关联投顾客户号，不新增客户！",hboneNo);
        }else{
            //更新客户
            processBusinessService.updateCustInfoByHbone(analyseVo,processedCustInfo);
            //关联一账通号 vs 投顾客户号
            processBusinessService.associateHboneAcctRelation(processedCustInfo,analyseVo);

            //节点4-CRM判断是否同时绑定香港客户号
            String hkTxAcctNo = analyseVo.getHkTxAcctNo();
            if(StringUtil.isNotBlank(hkTxAcctNo)){
                return processBusinessService.processExtraHkAccount(analyseVo);
            }
        }

        return Response.ok();
    }



}