/**
 * Copyright (c) 2022, <PERSON><PERSON><PERSON><PERSON> HOWBUY INVESTMENT MANAGEMENT Co., Ltd.
 * All right reserved.
 * <p>
 * THIS IS UNPUBLISHED PROPRIETARY SOURCE CODE OF HOWBUY INVESTMENT
 * MANAGEMENT CO., LTD.  THE CONTENTS OF THIS FILE MAY NOT BE DISCLOSED
 * TO THIRD PARTIES, COPIED OR DUPLICATED IN ANY FORM, IN WHOLE OR IN PART,
 * WITHOUT THE PRIOR WRITTEN PERMISSION OF HOWBUY INVESTMENT MANAGEMENT
 * CO., LTD.
 */
package com.howbuy.crm.account.service.config;

import com.github.pagehelper.PageInterceptor;
import com.howbuy.common.db.HowbuyBasicDataSource3;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.math.NumberUtils;
import org.apache.ibatis.plugin.Interceptor;
import org.apache.ibatis.session.SqlSessionFactory;
import org.mybatis.spring.SqlSessionFactoryBean;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.annotation.*;
import org.springframework.core.io.Resource;
import org.springframework.core.io.support.PathMatchingResourcePatternResolver;
import org.springframework.core.io.support.ResourcePatternResolver;
import org.springframework.jdbc.datasource.DataSourceTransactionManager;

/**
 * <AUTHOR>
 * @description: mybatis 数据源配置
 * @date 2023/02/20 16:01
 * @since JDK 1.8
 */
@Slf4j
@Configuration
public class MybatisConfig {

    @Value("${account.datasource.hps.url}")
    private String hpsUrl;

    @Value("${account.datasource.driverClassName}")
    private String orderDriverClassName;

    @Value("${account.datasource.url}")
    private String dsUrl;

    @Value("${account.datasource.username}")
    private String dbUsername;

    @Value("${account.datasource.password}")
    private String dbPassword;

    @Value("${account.datasource.initialSize}")
    private String initialSize;

    @Value("${account.datasource.minIdle}")
    private String minIdle;

    @Value("${account.datasource.maxActive}")
    private String maxActive;

    @Value("${account.datasource.maxWait}")
    private String maxWait;

    @Bean
    @Primary
    public HowbuyBasicDataSource3 dataSource() {
        log.info("buildDataSource|datasource start.");
        HowbuyBasicDataSource3 dataSource = new HowbuyBasicDataSource3();
        dataSource.setUrl(dsUrl);
        dataSource.setDriverClassName(orderDriverClassName);
        dataSource.setUsername(dbUsername);
        dataSource.setHpsUrl(hpsUrl);
        //setHpsUrl必须在setPassword之前, setPassword会用到hpsUrl
        dataSource.setPassword(dbPassword);
        dataSource.setInitialSize(NumberUtils.toInt(initialSize, 10));
        dataSource.setMinIdle(NumberUtils.toInt(minIdle));
        dataSource.setMaxActive(NumberUtils.toInt(maxActive));
        dataSource.setMaxWait(NumberUtils.toInt(maxWait));
        dataSource.setDefaultAutoCommit(false);
        dataSource.setTestOnBorrow(true);
        dataSource.setTestWhileIdle(true);
        //检查是否连接超时
        dataSource.setValidationQuery("select sysdate from dual");
        // 目前druid有bug:  Cause: java.sql.SQLException: 索引中丢失  IN 或 OUT 参数; 需要关闭PSCache
        dataSource.setPoolPreparedStatements(false);
        return dataSource;
    }

    /**
     * 根据数据源创建SqlSessionFactory
     *
     * @param ds           数据源
     * @param interceptors 拦截器
     * @return org.apache.ibatis.session.SqlSessionFactory
     * @author: hongdong.xie
     * @date: 2023/02/20 16:30
     * @since: JDK 1.8
     */
    @Bean
    public SqlSessionFactory sqlSessionFactory(HowbuyBasicDataSource3 ds, Interceptor[] interceptors) throws Exception {
        SqlSessionFactoryBean bean = new SqlSessionFactoryBean();
        // 指定数据源(这个必须有，否则报错)
        bean.setDataSource(ds);
        // 添加mapper文件XML目录
        ResourcePatternResolver resolver = new PathMatchingResourcePatternResolver();
        Resource[] resources = resolver.getResources("classpath*:com/howbuy/crm/account/dao/mapper/*/*.xml");
        bean.setMapperLocations(resources);
        //mybatis的拦截器设置 目前就有分页 + 日志超时拦截器
        bean.setPlugins(interceptors);
        org.apache.ibatis.session.Configuration configuration = new org.apache.ibatis.session.Configuration();
        //设置驼峰自动转换
        configuration.setMapUnderscoreToCamelCase(true);
        bean.setConfiguration(configuration);
        return bean.getObject();
    }


    /**
     * 配置事务管理器
     *
     * @param dataSource 数据源
     * @return org.springframework.jdbc.datasource.DataSourceTransactionManager
     * @author: hongdong.xie
     * @date: 2023/02/20 16:33
     * @since: JDK 1.8
     */
    @Bean
    public DataSourceTransactionManager transactionManager(HowbuyBasicDataSource3 dataSource) {
        return new DataSourceTransactionManager(dataSource);
    }


    /**
     * 5.0版本后pageHelper 用这个PageInterceptor，
     * 之前的版本是用PageHelper 这个类 分页插件 PageHelper
     * @return com.github.pagehelper.PageInterceptor
     * @author: hongdong.xie
     * @date: 2023/02/20 16:35
     * @since: JDK 1.8
     */
    @Bean
    public PageInterceptor pageInterceptor() {
        return new PageInterceptor();
    }
    
}
