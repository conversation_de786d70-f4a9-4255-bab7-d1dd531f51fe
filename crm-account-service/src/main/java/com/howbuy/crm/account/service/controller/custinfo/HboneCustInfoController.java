/**
 * Copyright (c) 2023, <PERSON><PERSON><PERSON><PERSON> HOWBUY INVESTMENT MANAGEMENT Co., Ltd.
 * All right reserved.
 * <p>
 * THIS IS UNPUBLISHED PROPRIETARY SOURCE CODE OF HOWBUY INVESTMENT
 * MANAGEMENT CO., LTD.  THE CONTENTS OF THIS FILE MAY NOT BE DISCLOSED
 * TO THIRD PARTIES, COPIED OR DUPLICATED IN ANY FORM, IN WHOLE OR IN PART,
 * WITHOUT THE PRIOR WRITTEN PERMISSION OF HOWBUY INVESTMENT MANAGEMENT
 * CO., LTD.
 */
package com.howbuy.crm.account.service.controller.custinfo;

import com.howbuy.crm.account.client.request.custinfo.CreateCustByHboneRequest;
import com.howbuy.crm.account.client.request.custinfo.HboneAcctRelationOptRequest;
import com.howbuy.crm.account.client.response.Response;
import com.howbuy.crm.account.client.response.custinfo.HboneAcctCustDetailInfoVO;
import com.howbuy.crm.account.client.response.custinfo.HboneAcctCustInfoVO;
import com.howbuy.crm.account.service.service.custinfo.HboneCustInfoService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

/**
 * @description: (一账通客户信息接口)
 * <AUTHOR>
 * @date 2023/12/13 16:08
 * @since JDK 1.8
 */
@RestController
@RequestMapping("/hbonecustinfo")
public class HboneCustInfoController {


   @Autowired
    private HboneCustInfoService hboneCustInfoService;


    /**
     * @api {GET} /hbonecustinfo/queryhbonecustinfo queryHboneCustInfo()
     * @apiVersion 1.0.0
     * @apiGroup HboneCustInfoController
     * @apiName queryHboneCustInfo()
     * @apiDescription 根据一账通查询一账通客户信息
     * @apiParam (请求参数) {String} hboneNo 一账通号
     * @apiParamExample 请求参数示例
     * hboneNo=ldbcO
     * @apiSuccess (响应结果) {String} code 状态码
     * @apiSuccess (响应结果) {String} description 描述信息
     * @apiSuccess (响应结果) {Object} data 数据封装
     * @apiSuccess (响应结果) {String} data.userType
     * @apiSuccess (响应结果) {String} data.custName 一账通 客户名称
     * @apiSuccess (响应结果) {String} data.username
     * @apiSuccess (响应结果) {String} data.idType 证件类型
     * @apiSuccess (响应结果) {String} data.idNoDigest 证件号码
     * @apiSuccess (响应结果) {String} data.idNoMask 证件号码 mask
     * @apiSuccess (响应结果) {String} data.mobileDigest 手机号码
     * @apiSuccess (响应结果) {String} data.mobileMask 手机号码 mask
     * @apiSuccess (响应结果) {String} data.mobileVerifyStatus 手机号码验证状态
     * @apiSuccess (响应结果) {String} data.regOutletCode 注册网点
     * @apiSuccess (响应结果) {String} data.regDt 注册时间
     * @apiSuccess (响应结果) {String} data.authOutletCode
     * @apiSuccess (响应结果) {String} data.authDt
     * @apiSuccess (响应结果) {Boolean} data.existPassword
     * @apiSuccess (响应结果) {String} data.gender
     * @apiSuccessExample 响应结果示例
     * {"code":"AR2fCE","data":{"idType":"TRrdU","gender":"l","regOutletCode":"j2","mobileVerifyStatus":"sMOvR","idNoDigest":"obV9XEvBV","authOutletCode":"PKNexEDZX","custName":"4knR","idNoMask":"bXBU","regDt":"fTc6","authDt":"Pjk6TEK01J","mobileDigest":"fq79cKx","existPassword":true,"userType":"6bwAR","username":"HOMlrC81","mobileMask":"iAHY8"},"description":"9ffUwh7U"}
     */
    @GetMapping("/queryhbonecustinfo")
    @ResponseBody
    public Response<HboneAcctCustInfoVO> queryHboneCustInfo(@RequestParam(name="hboneNo") String hboneNo){
        return Response.ok(hboneCustInfoService.queryHboneAcctInfo(hboneNo));
    }


    /**
     * @api {GET} /hbonecustinfo/queryhbonecustinfobycustno queryHboneCustInfoByCustNo()
     * @apiVersion 1.0.0
     * @apiGroup HboneCustInfoController
     * @apiName queryHboneCustInfoByCustNo()
     * @apiDescription 根据投顾客户号查询一账通账户信息
     * @apiParam (请求参数) {String} custNo 投顾客户号
     * @apiParamExample 请求参数示例
     * custNo=Z1
     * @apiSuccess (响应结果) {String} code 状态码
     * @apiSuccess (响应结果) {String} description 描述信息
     * @apiSuccess (响应结果) {Object} data 数据封装
     * @apiSuccess (响应结果) {String} data.userType
     * @apiSuccess (响应结果) {String} data.custName 一账通 客户名称
     * @apiSuccess (响应结果) {String} data.username
     * @apiSuccess (响应结果) {String} data.idType 证件类型
     * @apiSuccess (响应结果) {String} data.idNoDigest 证件号码
     * @apiSuccess (响应结果) {String} data.idNoMask 证件号码 mask
     * @apiSuccess (响应结果) {String} data.mobileDigest 手机号码
     * @apiSuccess (响应结果) {String} data.mobileMask 手机号码 mask
     * @apiSuccess (响应结果) {String} data.mobileVerifyStatus 手机号码验证状态
     * @apiSuccess (响应结果) {String} data.regOutletCode 注册网点
     * @apiSuccess (响应结果) {String} data.regDt 注册时间
     * @apiSuccess (响应结果) {String} data.authOutletCode
     * @apiSuccess (响应结果) {String} data.authDt
     * @apiSuccess (响应结果) {Boolean} data.existPassword
     * @apiSuccess (响应结果) {String} data.gender
     * @apiSuccessExample 响应结果示例
     * {"code":"W8PfInrY","data":{"idType":"4VXvZ","gender":"bEh","regOutletCode":"ZAk2a","mobileVerifyStatus":"9uux","idNoDigest":"Bx1k","authOutletCode":"iSChV","custName":"4WQdXWQ","idNoMask":"FAnKif8EH5","regDt":"5SbdBL","authDt":"MmgvXqj","mobileDigest":"mOYg","existPassword":true,"userType":"sYMXFympYJ","username":"u4x","mobileMask":"7WC2L"},"description":"OuYlIwMwlM"}
     */
    @GetMapping("/queryhbonecustinfobycustno")
    @ResponseBody
    public Response<HboneAcctCustInfoVO> queryHboneCustInfoByCustNo(@RequestParam(name="custNo") String custNo){
        return Response.ok(hboneCustInfoService.queryHboneAcctInfoByCustNo(custNo));
    }




    /**
     * @api {GET} /hbonecustinfo/queryhbonecustdetailinfobycustno queryHboneCustDetailInfoByCustNo()
     * @apiVersion 1.0.0
     * @apiGroup HboneCustInfoController
     * @apiName queryHboneCustDetailInfoByCustNo()
     * @apiDescription 根据投顾客户号查询一账通账户详细信息
     * @apiParam (请求参数) {String} custNo 投顾客户号
     * @apiParamExample 请求参数示例
     * custNo=Z1
     * @apiSuccess (响应结果) {String} code 状态码
     * @apiSuccess (响应结果) {String} description 描述信息
     * @apiSuccess (响应结果) {Object} data 数据封装
     * @apiSuccess (响应结果) {String} data.userType
     * @apiSuccess (响应结果) {String} data.custName 一账通 客户名称
     * @apiSuccess (响应结果) {String} data.username
     * @apiSuccess (响应结果) {String} data.idType 证件类型
     * @apiSuccess (响应结果) {String} data.idNoDigest 证件号码
     * @apiSuccess (响应结果) {String} data.idNoMask 证件号码 mask
     * @apiSuccess (响应结果) {String} data.mobileDigest 手机号码
     * @apiSuccess (响应结果) {String} data.mobileMask 手机号码 mask
     * @apiSuccess (响应结果) {String} data.mobileVerifyStatus 手机号码验证状态
     * @apiSuccess (响应结果) {String} data.regOutletCode 注册网点
     * @apiSuccess (响应结果) {String} data.regDt 注册时间
     * @apiSuccess (响应结果) {String} data.authOutletCode
     * @apiSuccess (响应结果) {String} data.authDt
     * @apiSuccess (响应结果) {Boolean} data.existPassword
     * @apiSuccess (响应结果) {String} data.gender
     * @apiSuccessExample 响应结果示例
     * {"code":"W8PfInrY","data":{"idType":"4VXvZ","gender":"bEh","regOutletCode":"ZAk2a","mobileVerifyStatus":"9uux","idNoDigest":"Bx1k","authOutletCode":"iSChV","custName":"4WQdXWQ","idNoMask":"FAnKif8EH5","regDt":"5SbdBL","authDt":"MmgvXqj","mobileDigest":"mOYg","existPassword":true,"userType":"sYMXFympYJ","username":"u4x","mobileMask":"7WC2L"},"description":"OuYlIwMwlM"}
     */
    @GetMapping("/queryhbonecustdetailinfobycustno")
    @ResponseBody
    public Response<HboneAcctCustDetailInfoVO> queryHboneCustDetailInfoByCustNo(@RequestParam(name = "custNo") String custNo) {
        return Response.ok(hboneCustInfoService.queryHboneAcctDetailInfoByCustNo(custNo));
    }


    /**
     * @api {GET} /hbonecustinfo/queryhbonecustdetailinfobyhboneno queryHboneCustDetailInfoByHboneNo()
     * @apiVersion 1.0.0
     * @apiGroup HboneCustInfoController
     * @apiName queryHboneCustDetailInfoByHboneNo()
     * @apiParam (请求参数) {String} hboneNo
     * @apiParamExample 请求参数示例
     * hboneNo=2WyJpIl
     * @apiSuccess (响应结果) {String} code 状态码
     * @apiSuccess (响应结果) {String} description 描述信息
     * @apiSuccess (响应结果) {Object} data 数据封装
     * @apiSuccess (响应结果) {String} data.idSignAreaCode 证件地区码-身份证签发地区编码
     * @apiSuccess (响应结果) {String} data.mobileAreaCode 手机号码区号
     * @apiSuccess (响应结果) {String} data.txAcctNo 交易账号
     * @apiSuccess (响应结果) {String} data.idType 证件类型
     * @apiSuccess (响应结果) {String} data.idNoDigest 证件号码摘要
     * @apiSuccess (响应结果) {String} data.idNoMask 证件号码掩码
     * @apiSuccess (响应结果) {String} data.custNo 客户号
     * @apiSuccess (响应结果) {String} data.custName 客户姓名
     * @apiSuccess (响应结果) {String} data.postCode 邮编
     * @apiSuccess (响应结果) {String} data.addrDigest 住址摘要
     * @apiSuccess (响应结果) {String} data.addrMask 住址掩码
     * @apiSuccess (响应结果) {String} data.telNoDigest 联系电话摘要
     * @apiSuccess (响应结果) {String} data.telNoMask 联系电话掩码
     * @apiSuccess (响应结果) {String} data.emailDigest 邮箱摘要
     * @apiSuccess (响应结果) {String} data.emailMask 邮箱掩码
     * @apiSuccess (响应结果) {String} data.fax
     * @apiSuccess (响应结果) {String} data.faxFlag 传真授权交易标志
     * @apiSuccess (响应结果) {String} data.homeTelNoDigest 住址电话摘要
     * @apiSuccess (响应结果) {String} data.homeTelNoMask 住址电话掩码
     * @apiSuccess (响应结果) {String} data.officeTelNo 办公室电话
     * @apiSuccess (响应结果) {String} data.mobileDigest 手机摘要
     * @apiSuccess (响应结果) {String} data.mobileMask 手机掩码
     * @apiSuccess (响应结果) {String} data.eduLevel 投资者学历01-初中及以下；02-高中/中专；03-大学/本科；04-硕士及以上
     * @apiSuccess (响应结果) {String} data.vocation 投资者职业代码      01-政府部门；02-教科文；03-金融；04-商贸；05-房地产；06-制造业；07-自由职业；08-其他
     * @apiSuccess (响应结果) {String} data.nationality 投资人国籍
     * @apiSuccess (响应结果) {String} data.incLevel 投资者年收入
     * @apiSuccess (响应结果) {String} data.birthday 投资者生日
     * @apiSuccess (响应结果) {String} data.gender 投资者性别 0-女；1-男；
     * @apiSuccess (响应结果) {String} data.minorFlag 未成年人标志0-否；1-是
     * @apiSuccess (响应结果) {String} data.minorId 未成年人ID号
     * @apiSuccess (响应结果) {String} data.provCode 省份代码
     * @apiSuccess (响应结果) {String} data.cityCode 城市代码
     * @apiSuccess (响应结果) {String} data.provName 省份名称
     * @apiSuccess (响应结果) {String} data.cityName 城市名称
     * @apiSuccess (响应结果) {String} data.company 所在单位
     * @apiSuccess (响应结果) {String} data.idValidityEnd 证件有效截至日
     * @apiSuccess (响应结果) {String} data.idAlwaysValidFlag 是否长期有效0-否,1-是
     * @apiSuccess (响应结果) {String} data.idValidityFlag 证件有效性：0-无效；1-有效（证件长期有效||截止日期大于当前日期）
     * @apiSuccess (响应结果) {String} data.fundManDlvyType 基金公司对帐单选择
     * @apiSuccess (响应结果) {String} data.agentDlvyType 销售公司对帐单选择
     * @apiSuccess (响应结果) {String} data.agentDlvyMode 销售公司对帐单寄送方式
     * @apiSuccess (响应结果) {String} data.ctxStat 电话交易
     * @apiSuccess (响应结果) {String} data.etxStatus 网上交易
     * @apiSuccess (响应结果) {String} data.idValidityStart 证件有效起始日
     * @apiSuccess (响应结果) {String} data.secuInvestExp 证券投资经历
     * @apiSuccess (响应结果) {String} data.marriageStat 婚姻状况
     * @apiSuccess (响应结果) {String} data.vipFlag 是否VIP 1-是, 0-否
     * @apiSuccess (响应结果) {String} data.activeStat 柜台激活状态0-未激活；1-已激活
     * @apiSuccess (响应结果) {String} data.regDt 开户日期
     * @apiSuccess (响应结果) {String} data.regOutletCode 开户网点号
     * @apiSuccess (响应结果) {String} data.regTradeChan 开户渠道
     * @apiSuccess (响应结果) {String} data.regDisCode 开户机构
     * @apiSuccess (响应结果) {String} data.fundtxState 客户分销交易账户状态 0-有效
     * @apiSuccess (响应结果) {String} data.txPwdMatchs 交易密码是否正确 0-正确，1-错误
     * @apiSuccess (响应结果) {String} data.mobileVrfyStat 手机是否验证:1-未验证，0-验证成功
     * @apiSuccess (响应结果) {String} data.property 机构客户性质      0-国企 1-民营 2-合资 3-其它
     * @apiSuccess (响应结果) {String} data.qualification 机构客户资质      1.金融机构      2.金融机构产品      3.社会保障基金      4.企业年金等养老基金      5.慈善基金等社会公益基金      6.合格境外机构投资者（QFII）      7.人民币合格境外机构投资者（RQFII）      0其他
     * @apiSuccess (响应结果) {String} data.businessScope 经营范围
     * @apiSuccess (响应结果) {String} data.officeAddress 办公地址
     * @apiSuccess (响应结果) {String} data.qualificationType 投资者资质类别:0普通、1专业
     * @apiSuccess (响应结果) {String} data.invstType 投资者类型：0-机构；1-个人
     * @apiSuccess (响应结果) {String} data.collectProtocolMethod 回款协议方式      1-回款至银行卡（系统默认）      2-回款至银行卡（用户选择）      3-回款至储蓄罐（系统默认）      4-回款至储蓄罐（用户选择）
     * @apiSuccess (响应结果) {String} data.fundFlag 是否签署资管合格投资者承诺书：0-未签署；1-已签署
     * @apiSuccess (响应结果) {String} data.qualifyFlag 资管合格投资者满足条件：      1-家庭金融净资产不低于300万元      2-家庭金融资产不低于500万元      3-近3年本人年均收入不低于40万元      4-最近1年末净资产不低于1000万元的法人单位（只针对机构）
     * @apiSuccess (响应结果) {String} data.fundConfirmedDate 资管合格投资者确认日期：yyyyMMdd，可为空， 为空则默认取系统日期，
     * @apiSuccess (响应结果) {String} data.actualController 实际控制人
     * @apiSuccess (响应结果) {String} data.controllerIdNoDigest 实际控制人或控股股东证件号-digest
     * @apiSuccess (响应结果) {String} data.controllerIdNoMask 实际控制人或控股股东证件号 -掩码
     * @apiSuccess (响应结果) {String} data.controllerIdType 实际控制人或控股股东证件类型
     * @apiSuccess (响应结果) {String} data.controllerIdValidityEnd 证件有效期限yyyyMMdd
     * @apiSuccess (响应结果) {String} data.controllerIdAlwaysValidFlag 证件是否长期有效:1-是；0-否
     * @apiSuccess (响应结果) {String} data.stockHolder 控股股东
     * @apiSuccess (响应结果) {String} data.stockerIdNoDigest 控股股东证件号-digest
     * @apiSuccess (响应结果) {String} data.stockerIdNoMask 控股股东证件号- 掩码
     * @apiSuccess (响应结果) {String} data.stockerIdType 证件类型
     * @apiSuccess (响应结果) {String} data.stockerIdValidityEnd 证件有效期yyyyMMdd
     * @apiSuccess (响应结果) {String} data.stockerIdAlwaysValidFlag 证件是否长期有效：1-是；0-否
     * @apiSuccess (响应结果) {String} data.countyCode 县代码
     * @apiSuccess (响应结果) {String} data.residenceCountry 现居国家或地区
     * @apiSuccess (响应结果) {String} data.corpIdType 法人代表证件类型
     * @apiSuccess (响应结果) {String} data.corporation 法人代表姓名
     * @apiSuccess (响应结果) {String} data.corpIdNoDigest 摘要-法人代表证件号码
     * @apiSuccess (响应结果) {String} data.corpIdNoMask 掩码-法人代表证件号码
     * @apiSuccessExample 响应结果示例
     * {"code":"zK6FaWg","data":{"residenceCountry":"jBX8WDVkTr","provName":"zheA2","fundManDlvyType":"HDmKP4Bi","stockerIdType":"zKJ","regTradeChan":"Kham74p9O","corpIdNoMask":"JSU","idValidityEnd":"rTIEyU","property":"2","addrDigest":"E6CTO4","fundFlag":"45Vwk","controllerIdNoMask":"2cA5qlc","telNoMask":"RR","fax":"ybM9O8D","fundConfirmedDate":"yUfl","idType":"xFEQ","eduLevel":"i","ctxStat":"s3Xg05N","regOutletCode":"Abxwl","corporation":"5","businessScope":"EtaRumAX","stockerIdValidityEnd":"6i1W6sp","addrMask":"B","activeStat":"pwdIlSmj","idSignAreaCode":"9TZx","regDt":"X3","qualification":"eJ01","nationality":"osS1qd","marriageStat":"511","corpIdNoDigest":"N7t","agentDlvyMode":"Vubga0P","stockerIdNoMask":"sn","secuInvestExp":"nnr","txPwdMatchs":"MbOsAfdBy","postCode":"RHDHf0","controllerIdAlwaysValidFlag":"0sUg","vipFlag":"1HSkT","mobileMask":"Gb6","homeTelNoDigest":"5Lb","birthday":"j28d2S2Wj","agentDlvyType":"5bLo3t","gender":"Z9qr2QPXTJ","controllerIdNoDigest":"cfD9XGZQ","minorId":"kIkAQcA","cityCode":"Cg","collectProtocolMethod":"gTqjBK0C","homeTelNoMask":"R","stockHolder":"N","controllerIdValidityEnd":"i","telNoDigest":"orOzwP40","idValidityStart":"dxzhMi","mobileAreaCode":"B6L2du","idValidityFlag":"vBj0v","incLevel":"4vxIemH","qualificationType":"bDwv","actualController":"Q0QkRL","cityName":"t","mobileVrfyStat":"OYAAb","invstType":"nmJPqv5","faxFlag":"O5l","mobileDigest":"2HLDet","txAcctNo":"2y94I1bbGr","company":"hZH","officeTelNo":"ACwc","etxStatus":"N32eEpzZvo","stockerIdNoDigest":"dTMrWQj","controllerIdType":"0ayor3B88","custNo":"hPMSq0o7Fo","provCode":"waNX","idAlwaysValidFlag":"DRVY","fundtxState":"mYOwA","idNoDigest":"7NAp6zvst","emailDigest":"d","custName":"zZyTRXnu","officeAddress":"GgCfmNS","idNoMask":"br","qualifyFlag":"DYbcG4ZFon","vocation":"dnT","regDisCode":"GmV","countyCode":"HayAg","corpIdType":"be","emailMask":"3FaI0","stockerIdAlwaysValidFlag":"hHNtZ","minorFlag":"g6Y"},"description":"Hm"}
     */
    @GetMapping("/queryhbonecustdetailinfobyhboneno")
    @ResponseBody
    public Response<HboneAcctCustDetailInfoVO> queryHboneCustDetailInfoByHboneNo(@RequestParam(name = "hboneNo") String hboneNo) {
        return Response.ok(hboneCustInfoService.queryHboneCustDetailInfo(hboneNo));
    }


 /**
  * @api {POST} /hbonecustinfo/unbindhbone unBindhHbone()
  * @apiVersion 1.0.0
  * @apiGroup HboneCustInfoController
  * @apiName unBindhHbone()
  * @apiDescription 解绑一账通
  * @apiParam (请求体) {String} custNo 投顾客户号
  * @apiParam (请求体) {String} hboneNo 香港交易账号
  * @apiParam (请求体) {String} operator 操作员
  * @apiParam (请求体) {String} operateSource 非必须      操作来源 [1-MQ]时，为[异常来源]， [2-菜单页面]时，为菜单名称
  * @apiParam (请求体) {String} operateChannel 非必须      操作通道 1-MQ  2-菜单页面
  * @apiParam (请求体) {String} remark 备注
  * @apiParamExample 请求体示例
  * {"custNo":"wZYs","operateChannel":"9R","operateSource":"AlKTGGk1","remark":"9","operator":"Bkko","hboneNo":"wB"}
  * @apiSuccess (响应结果) {String} code 状态码
  * @apiSuccess (响应结果) {String} description 描述信息
  * @apiSuccess (响应结果) {Object} data 数据封装
  * @apiSuccess (响应结果) {String} data.code 状态码
  * @apiSuccess (响应结果) {String} data.description 描述信息
  * @apiSuccess (响应结果) {String} data.data 数据封装
  * @apiSuccessExample 响应结果示例
  * {"code":"f","data":{"code":"ZsFV5","data":"jqxZo","description":"mv"},"description":"7cL6FBN"}
  */
 @PostMapping("/unbindhbone")
 @ResponseBody
 public Response<Response<String>> unBindhHbone(@RequestBody HboneAcctRelationOptRequest bindOptRequest) {
  return  Response.ok(hboneCustInfoService.unBindHkTxAcct(bindOptRequest));
 }


 /**
  * @api {POST} /hbonecustinfo/mannualDealMessage mannualDealMessage()
  * @apiVersion 1.0.0
  * @apiGroup HboneCustInfoController
  * @apiName mannualDealMessage()
  * @apiDescription 根据一账通号，创建crm客户信息
  * @apiParam (请求体) {String} hboneNo 一账通号
  * @apiParam (请求体) {String} operateSource 操作来源 [1-MQ]时，为[异常来源]，参考枚举类[{@link com.howbuy.crm.account.client.enums.custinfo.FullCustSourceEnum}]      [2-菜单页面]时，为菜单名称
  * @apiParam (请求体) {String} operateChannel operateChannel 操作通道 1-MQ  2-菜单页面
  * @apiParam (请求体) {String} disCode 分销机构号(disCode)
  * @apiParamExample 请求体示例
  * {"operateChannel":"zMg6KeEpmz","operateSource":"lpEd6ykr","disCode":"P7","hboneNo":"5JPQY4L0"}
  * @apiSuccess (响应结果) {String} code 状态码
  * @apiSuccess (响应结果) {String} description 描述信息
  * @apiSuccess (响应结果) {Object} data 数据封装
  * @apiSuccess (响应结果) {String} data.code 状态码
  * @apiSuccess (响应结果) {String} data.description 描述信息
  * @apiSuccess (响应结果) {String} data.data 数据封装
  * @apiSuccessExample 响应结果示例
  * {"code":"Z2tXNo","data":{"code":"QS","data":"B3rlU","description":"tVoJ5EHisV"},"description":"nGh8HUTL3u"}
  */
 @PostMapping("/createCustInfoByHbone")
 @ResponseBody
 public Response<Response<String>>  createCustInfoByHbone(@RequestBody CreateCustByHboneRequest request){
   return  Response.ok(hboneCustInfoService.createCustInfoByHbone(request));
 }
}