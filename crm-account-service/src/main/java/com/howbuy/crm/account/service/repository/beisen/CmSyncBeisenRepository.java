/**
 * Copyright (c) 2024, <PERSON><PERSON><PERSON><PERSON> HOWBUY INVESTMENT MANAGEMENT Co., Ltd.
 * All right reserved.
 * <p>
 * THIS IS UNPUBLISHED PROPRIETARY SOURCE CODE OF HOWBUY INVESTMENT
 * MANAGEMENT CO., LTD.  THE CONTENTS OF THIS FILE MAY NOT BE DISCLOSED
 * TO THIRD PARTIES, COPIED OR DUPLICATED IN ANY FORM, IN WHOLE OR IN PART,
 * WITHOUT THE PRIOR WRITTEN PERMISSION OF HOWBUY INVESTMENT MANAGEMENT
 * CO., LTD.
 */
package com.howbuy.crm.account.service.repository.beisen;

import com.howbuy.crm.account.dao.mapper.constant.HbConstantMapper;
import com.howbuy.crm.account.dao.mapper.consultantexp.CmConsultantExpBeisenMapper;
import com.howbuy.crm.account.dao.mapper.consultantexp.CmConsultantExpMapper;
import com.howbuy.crm.account.dao.mapper.consultantexpmodifyflag.CmConsultantExpModifyFlagMapper;
import com.howbuy.crm.account.dao.mapper.consultantexpzjsal.CmConsultantExpZjSalMapper;
import com.howbuy.crm.account.dao.po.beisen.CmBeisenOrgDO;
import com.howbuy.crm.account.dao.po.beisen.CmBeisenUserInfoPO;
import com.howbuy.crm.account.dao.po.beisenorg.CmBeisenOrgConfigPO;
import com.howbuy.crm.account.dao.po.beisenposlevel.CmBeisenPosLevelConfigPO;
import com.howbuy.crm.account.dao.po.constant.HbConstantPO;
import com.howbuy.crm.account.dao.po.consultantexp.CmConsultantExpPO;
import com.howbuy.crm.account.dao.po.consultantexp.CmConsultantExpModifyFlagPO;
import com.howbuy.crm.account.dao.po.consultantexp.CmConsultantExpZjSalPO;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import org.springframework.transaction.annotation.Propagation;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;

import java.util.List;

/**
 * @description: (请在此添加描述)
 * <AUTHOR>
 * @date 2024/11/14 19:57
 * @since JDK 1.8
 */
@Slf4j
@Component
public class CmSyncBeisenRepository {
    @Autowired
    private HbConstantMapper hbConstantMapper;
    @Autowired
    private CmConsultantExpZjSalMapper cmConsultantExpZjSalMapper;
    @Autowired
    private CmConsultantExpModifyFlagMapper cmConsultantExpModifyFlagMapper;
    @Autowired
    private CmConsultantExpBeisenMapper cmConsultantExpBeisenMapper;
    @Autowired
    private CmConsultantExpMapper cmConsultantExpMapper;
    /**
     * 职级
     */
    public static final String HRPOSITIONSLEVEL = "hrpositionslevel";

    public List<HbConstantPO> listByTypeCode(){
        return hbConstantMapper.listByTypeCode(HRPOSITIONSLEVEL);
    }

    public List<CmConsultantExpZjSalPO> selectAllData(){
        return cmConsultantExpZjSalMapper.selectAllData();
    }

    public List<CmConsultantExpModifyFlagPO> selectAll(){
        return cmConsultantExpModifyFlagMapper.selectAll();
    }

    public List<CmConsultantExpPO> listCmConsultantExpByUserNo(List<String> userNos){
        return cmConsultantExpMapper.listCmConsultantExpByUserNo(userNos);
    }

    /**
     * @description:(保存北森同步花名册数据和北森同步花名册同步标识数据)
     * @param saveExpList
     * @param saveModifyFlagList
     * @return void
     * @author: shijie.wang
     * @date: 2024/11/4 17:10
     * @since JDK 1.8
     */
    @Transactional(rollbackFor = Exception.class, propagation = Propagation.REQUIRED)
    public void save(List<CmConsultantExpPO> saveExpList, List<CmConsultantExpModifyFlagPO> saveModifyFlagList){
        if(CollectionUtils.isEmpty(saveExpList)){
            log.info("save| saveExpList is null");
            return;
        }
        cmConsultantExpMapper.mergeCmConsultantExpByBeisen(saveExpList);
        if(CollectionUtils.isEmpty(saveModifyFlagList)){
            log.info("save| saveModifyFlagList is null");
            return;
        }
        cmConsultantExpModifyFlagMapper.mergeExpModifyFlagByList(saveModifyFlagList);
    }

    public List<CmBeisenOrgDO> queryAllCmBeisenOrgData(){
        return cmConsultantExpBeisenMapper.queryAllCmBeisenOrgData();
    }

    public List<CmBeisenOrgConfigPO> queryAllCmBeisenOrgConfigData(){
        return cmConsultantExpBeisenMapper.queryAllCmBeisenOrgConfigData();
    }

    public List<CmBeisenPosLevelConfigPO> queryCmBeisenPosLevelConfigData(){
       return cmConsultantExpBeisenMapper.queryCmBeisenPosLevelConfigData();
    }

    public List<CmBeisenUserInfoPO> getBeisenUserInfo(String startDate, String endDate){
        return cmConsultantExpBeisenMapper.queryCmBeisenUserInfoData(startDate, endDate);
    }

    public List<CmBeisenUserInfoPO> getBeisenUserInfoByModdt(String startDate){
        return cmConsultantExpBeisenMapper.queryCmBeisenUserInfoDataByModdt(startDate);
    }
}