package com.howbuy.crm.account.service.vo.custinfo;

import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * @description: (香港客户信息处理 )
 * <AUTHOR>
 * @date 2023/12/11 10:57
 * @since JDK 1.8
 */

/**
    * 香港客户异常信息表
    */
@Data
@EqualsAndHashCode(callSuper = true)
public class HkMessageCustInfoVO  extends  MessageCustInfoVO{



    /**
    * 客户姓名
    */
    private String custName;

    /**
    * 投资者类型
    */
    private String investType;

    /**
    * 手机地区码
    */
    private String mobileAreaCode;

    /**
    * 手机号摘要
    */
    private String mobileDigest;

    /**
    * 手机号掩码
    */
    private String mobileMask;

    /**
    * 手机号密文
    */
    private String mobileCipher;

    /**
    * 证件地区码
    */
    private String idSignAreaCode;

    /**
    * 证件类型
    */
    private String idType;

    /**
    * 证件号码摘要
    */
    private String idNoDigest;

    /**
    * 证件号码掩码
    */
    private String idNoMask;

    /**
    * 证件号码密文
    */
    private String idNoCipher;





    /**
     * 开户网点代码
     */
    private String openOutletCode;


    /**
     * [香港客户一账通号关系消息] 消息使用
     * 0-新增；1-删除
     */
    private String operType;


}