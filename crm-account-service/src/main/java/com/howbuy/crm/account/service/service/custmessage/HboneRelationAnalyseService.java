/**
 * Copyright (c) 2023, <PERSON><PERSON><PERSON><PERSON> HOWBUY INVESTMENT MANAGEMENT Co., Ltd.
 * All right reserved.
 * <p>
 * THIS IS UNPUBLISHED PROPRIETARY SOURCE CODE OF HOWBUY INVESTMENT
 * MANAGEMENT CO., LTD.  THE CONTENTS OF THIS FILE MAY NOT BE DISCLOSED
 * TO THIRD PARTIES, COPIED OR DUPLICATED IN ANY FORM, IN WHOLE OR IN PART,
 * WITHOUT THE PRIOR WRITTEN PERMISSION OF HOWBUY INVESTMENT MANAGEMENT
 * CO., LTD.
 */
package com.howbuy.crm.account.service.service.custmessage;

import com.google.common.collect.Lists;
import com.howbuy.common.utils.StringUtil;
import com.howbuy.crm.account.client.enums.YesOrNoEnum;
import com.howbuy.crm.account.client.enums.custinfo.AbnormaSceneTypeEnum;
import com.howbuy.crm.account.client.enums.custinfo.FullCustSourceEnum;
import com.howbuy.crm.account.dao.bo.custinfo.CmConscustForAnalyseBO;
import com.howbuy.crm.account.service.repository.custinfo.AbnormalCustRepository;
import com.howbuy.crm.account.service.vo.custinfo.AbnormalAnalyseResultVO;
import com.howbuy.crm.account.service.vo.custinfo.HkMessageCustInfoVO;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * @description: ([香港账户]-[香港注册] 消息)
 * <AUTHOR>
 * @date 2023/12/14 10:12
 * @since JDK 1.8
 */
@Service
@Slf4j
public class HboneRelationAnalyseService implements CustMessageAnalyseService<HkMessageCustInfoVO> {

    @Autowired
    private AbnormalCustRepository hkAbnormalCustRepository;
    @Override
    public List<FullCustSourceEnum>  getSourceList() {
        return Lists.newArrayList(FullCustSourceEnum.HK_CUST_NO_BIND);
    }

    @Override
    public AbnormalAnalyseResultVO<HkMessageCustInfoVO> analyze(HkMessageCustInfoVO analyseVo) {

        //目前暂时只处理 绑定
        if(YesOrNoEnum.YES.getCode().equals(analyseVo.getOperType())){
            return AbnormalAnalyseResultVO.normalData(analyseVo);
        }

        String hkTxAcctNo=analyseVo.getHkTxAcctNo();
        String hboneNo=analyseVo.getHboneNo();

//        1、步骤①：根据推送的【香港客户号】，查询CRM中已绑定的【投顾客户号A】（可能为空）
        CmConscustForAnalyseBO custA=hkAbnormalCustRepository.queryCustBOByHkTxAcctNo(hkTxAcctNo);
//
//        2、步骤②：根据推送的【一账通号】，查询CRM中已绑定的【投顾客户号B】（可能为空）
        List<CmConscustForAnalyseBO> hbCustList=hkAbnormalCustRepository.queryCustBOByHboneNo(hboneNo);

        //开发自定义流程： 历史数据存在hboneNo绑定多个hkTxAcctNo的情况，此处不做处理
        if(!CollectionUtils.isEmpty(hbCustList) && hbCustList.size()>1){
            log.error("【香港客户号/一账通绑定】消息处理异常：一账通号[{}]绑定了多个投顾客户号",hboneNo);
            return AbnormalAnalyseResultVO.
                    notNormalData(analyseVo,AbnormaSceneTypeEnum.HBONE_NO_MATCH_MULTIPLE_CUST_NO,hbCustList);
        }
        CmConscustForAnalyseBO custB=CollectionUtils.isEmpty(hbCustList)?null:hbCustList.get(0);
//
//        3、步骤③：判断【投顾客户号A】和【投顾客户号B】是否都不为空，且是否一致
        //（4）若【投顾客户号A】和【投顾客户号B】都有值，且不一致，则异常数据进“香港异常客户表”：
//
//【香港客户号A】落“香港异常客户主表”，写入字段：
//        a、异常来源：香港客户号/一账通绑定//
//        b、异常描述：两边绑定的投顾客户号不一致//
//        c、异常时间：取落入异常表的时间，格式yyyymmdd hh:mm:ss
//
//【投顾客户号A】和【投顾客户号B】均落“香港异常客户待关联表”，与主表数据相对应。
        if(custA!=null && custB!=null  &&
                Boolean.FALSE.equals(StringUtil.isEqual(custA.getConscustno(),custB.getConscustno()))){
            return AbnormalAnalyseResultVO.
                    notNormalData(analyseVo,AbnormaSceneTypeEnum.BIND_CUST_NO_NOT_MATCH,Lists.newArrayList(custA,custB));
        }

        return AbnormalAnalyseResultVO.normalData(analyseVo);
    }

}