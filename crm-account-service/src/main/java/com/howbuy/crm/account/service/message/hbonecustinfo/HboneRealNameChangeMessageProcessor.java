/**
 * Copyright (c) 2023, <PERSON><PERSON><PERSON><PERSON> HOWBUY INVESTMENT MANAGEMENT Co., Ltd.
 * All right reserved.
 * <p>
 * THIS IS UNPUBLISHED PROPRIETARY SOURCE CODE OF HOWBUY INVESTMENT
 * MANAGEMENT CO., LTD.  THE CONTENTS OF THIS FILE MAY NOT BE DISCLOSED
 * TO THIRD PARTIES, COPIED OR DUPLICATED IN ANY FORM, IN WHOLE OR IN PART,
 * WITHOUT THE PRIOR WRITTEN PERMISSION OF HOWBUY INVESTMENT MANAGEMENT
 * CO., LTD.
 */
package com.howbuy.crm.account.service.message.hbonecustinfo;

import com.alibaba.fastjson.JSON;
import com.howbuy.common.utils.StringUtil;
import com.howbuy.crm.account.client.enums.custinfo.FullCustSourceEnum;
import com.howbuy.crm.account.client.response.Response;
import com.howbuy.crm.account.dao.bo.custinfo.CmConscustForAnalyseBO;
import com.howbuy.crm.account.service.business.custinfo.CmCustProcessBusinessService;
import com.howbuy.crm.account.service.message.dto.HboneUpdateMobileMessageDTO;
import com.howbuy.crm.account.service.repository.custinfo.AbnormalCustRepository;
import com.howbuy.crm.account.service.vo.custinfo.AbnormalAnalyseResultVO;
import com.howbuy.crm.account.service.vo.custinfo.HboneMessageCustInfoVO;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;

import java.util.Arrays;
import java.util.List;

/**
 * @description: ([一账通账户]-[客户信息变更消息]-[客户实名] 消息处理器[topic.hbone.updateCust])
 * <AUTHOR>
 * @date 2023/12/11 17:04
 * @since JDK 1.8
 */
@Slf4j
@Service
public class HboneRealNameChangeMessageProcessor extends AbstractHboneMessageProcessor<HboneUpdateMobileMessageDTO>{


    /**
     * [TOPIC_UPDATE_CUST]
     */
    @Value("${topic.hbone.updateCust}")
    private String topicUpdateCust;


    /**
     * [REAL_NAME]
     */
    @Value("${tag.hbone.realName}")
    private String tagName;


//    {
//        "clientId": "fe369569cfb34e94b204e1f791f8bc12",
//            "content": {
//        "body": {
//            "authCardCipher": "Wp-FtjrDe3Ogu1TIHSJfq8LCv--77Fx3SJifxv3eN9U=01",
//                    "authCardDigest": "8342f4ab37a17a9c20e4042f321ca905",
//                    "authCardMask": "******487502",
//                    "authDate": "20240111",
//                    "authOutletCode": "RO2101W03",
//                    "custName": "孙桐光",
//                    "disCode": "HB000A001",
//                    "hboneNo": "9200775404",
//                    "idNoCipher": "eJPCdtgJ29bF2U8NT3U89TsHJp6lJQCxL4-Ur5mPa-k=01",
//                    "idNoDigest": "3b3d283c4c1b5afb47dbb30ba5835d47",
//                    "idNoMask": "13082819841023****",
//                    "idType": "0",
//                    "invstType": "1"
//        },
//        "head": {
//            "createTime": "20240111001237",
//                    "eventCode": "520173",
//                    "hboneNo": "9200775404",
//                    "msgKey": "10518186864c35d7187aa34549a17621",
//                    "tag": "REAL_NAME"
//        }
//    },
//        "messageChannel": "TOPIC_UPDATE_CUST"
//    }



    @Autowired
    private AbnormalCustRepository abnormalCustRepository;

    @Autowired
    private CmCustProcessBusinessService processBusinessService;


    @Override
    public String getQuartMessageChannel() {
        return topicUpdateCust;
    }

    @Override
    public List<String> getSupportTagList() {
        return Arrays.asList(tagName);
    }

    @Override
    HboneMessageCustInfoVO constructByMessage(HboneUpdateMobileMessageDTO  acctMsgDto) {
        HboneMessageCustInfoVO custVo=new HboneMessageCustInfoVO();
        custVo.setHboneNo(acctMsgDto.getHboneNo());
        custVo.setDisCode(acctMsgDto.getDisCode());


        //此处 需要 从账户中心查询 一账通账户信息
        fillHboneAcctInfo(custVo);

        return custVo;
    }
    @Override
    public FullCustSourceEnum getCustSource() {
        return FullCustSourceEnum.HBONE_CUST_REAL_NAME_CHANGE;
    }

    @Override
    Response<String> processHboneMessage(AbnormalAnalyseResultVO<HboneMessageCustInfoVO> resultVo) {
//        实名消息处理 不会 新增投顾客户号

        CmConscustForAnalyseBO processedCustInfo= resultVo.getProcessedCustInfo();
        HboneMessageCustInfoVO analyseVo=resultVo.getAnalyseVo();
        String hboneNo=analyseVo.getHboneNo();

        if(processedCustInfo==null){
            log.info("一账通实名开户消息处理： 一账通号[{}] 未关联投顾客户号，不新增客户！",hboneNo);
        }else{
            //更新客户
            Response<String> updateHboneResp=processBusinessService.updateCustInfoByHbone(analyseVo,processedCustInfo);
            log.info("一账通实名开户消息处理： 一账通号[{}] 更新客户信息,执行结果：{}",hboneNo, JSON.toJSONString(updateHboneResp));
            //关联一账通号 vs 投顾客户号
            processBusinessService.associateHboneAcctRelation(processedCustInfo,analyseVo);

            //节点4-CRM判断是否同时绑定香港客户号
            String hkTxAcctNo = analyseVo.getHkTxAcctNo();
            if(StringUtil.isNotBlank(hkTxAcctNo)){
                return processBusinessService.processExtraHkAccount(analyseVo);
            }
        }

        return Response.ok();
    }

}