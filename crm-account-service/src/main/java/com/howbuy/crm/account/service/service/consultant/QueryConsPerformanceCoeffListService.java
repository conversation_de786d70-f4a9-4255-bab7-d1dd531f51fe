/**
 * Copyright (c) 2024, <PERSON><PERSON><PERSON><PERSON> HOWBUY INVESTMENT MANAGEMENT Co., Ltd.
 * All right reserved.
 * <p>
 * THIS IS UNPUBLISHED PROPRIETARY SOURCE CODE OF HOWBUY INVESTMENT
 * MANAGEMENT CO., LTD.  THE CONTENTS OF THIS FILE MAY NOT BE DISCLOSED
 * TO THIRD PARTIES, COPIED OR DUPLICATED IN ANY FORM, IN WHOLE OR IN PART,
 * WITHOUT THE PRIOR WRITTEN PERMISSION OF HOWBUY INVESTMENT MANAGEMENT
 * CO., LTD.
 */
package com.howbuy.crm.account.service.service.consultant;

import com.github.pagehelper.Page;
import com.google.common.collect.Lists;
import com.google.common.collect.Sets;
import com.howbuy.common.utils.DateUtil;
import com.howbuy.common.utils.StringUtil;
import com.howbuy.crm.account.client.enums.YesOrNoEnum;
import com.howbuy.crm.account.client.request.consultant.QueryConsPerformanceCoeffExportRequest;
import com.howbuy.crm.account.client.request.consultant.QueryConsPerformanceCoeffListRequest;
import com.howbuy.crm.account.client.response.ExportToFileVO;
import com.howbuy.crm.account.client.response.consultant.QueryConsPerformanceCoeffListResponse;
import com.howbuy.crm.account.client.response.consultant.QueryConsPerformanceCoeffListVO;
import com.howbuy.crm.account.dao.bo.consultant.CmConsPerformanceCoeffBO;
import com.howbuy.crm.account.dao.req.consultant.QueryConsPerformanceCoeffListDTO;
import com.howbuy.crm.account.service.commom.utils.ExportUtil;
import com.howbuy.crm.account.service.domain.OrgLayerInfoDTO;
import com.howbuy.crm.account.service.outerservice.crmcore.OrganazitonOuterService;
import com.howbuy.crm.account.service.repository.consultant.QueryConsPerformanceCoeffRepository;
import com.howbuy.crm.account.service.repository.orgnization.HbOrganizationRepository;
import com.howbuy.crm.account.service.vo.consultant.QueryConsPerformanceCoeffExcelVO;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;
import org.springframework.util.StringUtils;

import javax.annotation.Resource;
import java.text.SimpleDateFormat;
import java.util.*;
import java.util.stream.Collectors;

/**
 * @description: 人员绩效系数查询与导出服务
 * <AUTHOR>
 * @date 2025-07-16 18:05:34
 * @since JDK 1.8
 */
@Service
@Slf4j
public class QueryConsPerformanceCoeffListService {

    @Resource
    private QueryConsPerformanceCoeffRepository queryConsPerformanceCoeffRepository;
    @Resource
    private HbOrganizationRepository hbOrganizationRepository;
    @Resource
    private OrganazitonOuterService organazitonOuterService;

    private static final String DATETIME_FORMAT = "yyyyMMdd HH:mm:ss";
    private static final String MENU_CODE = "090304";
    /**
     * 最大导出数据量
     */
    private static final int MAX_COUNT = 500000;

    /**
     * @param request 请求参数
     * @return com.howbuy.crm.account.client.response.consultant.QueryConsPerformanceCoeffListResponse
     * @description: 查询人员绩效系数列表
     * <AUTHOR>
     * @date 2025-07-16 18:05:34
     * @since JDK 1.8
     */
    public QueryConsPerformanceCoeffListResponse queryList(QueryConsPerformanceCoeffListRequest request) {
        log.info("查询人员绩效系数列表，请求参数：{}", request);

        // 转换查询条件
        QueryConsPerformanceCoeffListDTO queryDTO = convertToDTO(request);

        // 查询数据
        Page<CmConsPerformanceCoeffBO> page = queryConsPerformanceCoeffRepository.queryConsPerformanceCoeffList(queryDTO);

        // 转换结果
        QueryConsPerformanceCoeffListResponse response = new QueryConsPerformanceCoeffListResponse();
        response.setTotal(page.getTotal());
        List<CmConsPerformanceCoeffBO> list = page.getResult();

        List<QueryConsPerformanceCoeffListVO> voList = new ArrayList<>();
        if (page.getResult() != null && !list.isEmpty()) {
            voList = convertToListVO(list);
        }
        response.setList(voList);

        log.info("查询人员绩效系数列表完成，总记录数：{}", response.getTotal());
        return response;
    }


    /**
     * @param request 请求参数
     * @return com.howbuy.crm.account.client.response.ExportToFileVO
     * @description: 导出人员绩效系数
     * <AUTHOR>
     * @date 2025-07-16 18:05:34
     * @since JDK 1.8
     */
    public ExportToFileVO exportData(QueryConsPerformanceCoeffExportRequest request) {
        log.info("导出人员绩效系数，请求参数：{}", request);

        ExportToFileVO result = new ExportToFileVO();
        // 转换查询条件（导出不分页）
        QueryConsPerformanceCoeffListDTO queryDTO = convertToDTO(request);

        int count = queryConsPerformanceCoeffRepository.queryConsPerformanceCoeffListCount(queryDTO);
        if(count > MAX_COUNT){
            log.info("导出人员绩效系数，数据量过大");
            result.setErrorMsg(String.format("导出人员绩效系数，数据量过大，超过%s条,请重新选择查询条件！", MAX_COUNT));
            return result;
        }
        // 查询数据（不分页）
        List<CmConsPerformanceCoeffBO> dataList = queryConsPerformanceCoeffRepository.queryConsPerformanceCoeffListForExport(queryDTO);

        if (CollectionUtils.isEmpty(dataList)) {
            log.info("导出人员绩效系数，无数据可导出");
            return null;
        }

        List<QueryConsPerformanceCoeffListVO> listVO = convertToListVO(dataList);

        // 转换为导出VO
        List<QueryConsPerformanceCoeffExcelVO> excelVOList = convertToExcelVO(listVO);
        String menuName = queryConsPerformanceCoeffRepository.getMenuNameByMenuCode(MENU_CODE);

        // 使用ExportUtil进行导出
        result = ExportUtil.export(excelVOList, menuName, QueryConsPerformanceCoeffExcelVO.class);

        log.info("导出人员绩效系数完成，导出记录数：{}", dataList.size());
        return result;
    }

    /**
     * @param request 请求参数
     * @return com.howbuy.crm.account.dao.req.consultant.QueryConsPerformanceCoeffListDTO
     * @description: 转换为查询条件DTO
     * <AUTHOR>
     * @date 2025-07-16 18:05:34
     * @since JDK 1.8
     */
    private QueryConsPerformanceCoeffListDTO convertToDTO(QueryConsPerformanceCoeffListRequest request) {
        QueryConsPerformanceCoeffListDTO queryDTO = new QueryConsPerformanceCoeffListDTO();
        queryDTO.setOrgCode(request.getOrgCode());
        queryDTO.setConsCode(request.getConsCode());
        queryDTO.setConsCustNo(request.getConsCustNo());
        queryDTO.setPage(request.getPage());
        queryDTO.setRows(request.getRows());

        queryDTO.setStartAssignDay(request.getStartAssignTime());
        queryDTO.setEndAssignDay(request.getEndAssignTime());

        // 设置排序字段
        queryDTO.setSortField(request.getSortField());
        queryDTO.setSortOrder(request.getSortOrder());

        return queryDTO;
    }

    /**
     * @param request 请求参数
     * @return com.howbuy.crm.account.dao.req.consultant.QueryConsPerformanceCoeffListDTO
     * @description: 转换为查询条件DTO（导出）
     * <AUTHOR>
     * @date 2025-07-16 18:05:34
     * @since JDK 1.8
     */
    private QueryConsPerformanceCoeffListDTO convertToDTO(QueryConsPerformanceCoeffExportRequest request) {
        QueryConsPerformanceCoeffListDTO queryDTO = new QueryConsPerformanceCoeffListDTO();
        queryDTO.setOrgCode(request.getDeptCode());
        queryDTO.setConsCode(request.getConsCode());
        queryDTO.setConsCustNo(request.getConsCustNo());

        queryDTO.setStartAssignDay(request.getStartAssignTime());
        queryDTO.setEndAssignDay(request.getEndAssignTime());

        // 设置排序字段
        queryDTO.setSortField(request.getSortField());
        queryDTO.setSortOrder(request.getSortOrder());

        return queryDTO;
    }

    /**
     * @param dataList 数据库对象列表
     * @return java.util.List<com.howbuy.crm.account.client.response.consultant.QueryConsPerformanceCoeffListVO>
     * @description: 转换为列表VO
     * <AUTHOR>
     * @date 2025-07-16 18:05:34
     * @since JDK 1.8
     */
    private List<QueryConsPerformanceCoeffListVO> convertToListVO(List<CmConsPerformanceCoeffBO> dataList) {
        if (CollectionUtils.isEmpty(dataList)) {
            return Lists.newArrayList();
        }

        // 获取组织信息
        Set<String> orgCodes = Sets.newHashSet();
        Set<String> orgCodesCurrent = Sets.newHashSet();
        dataList.forEach(bo -> {
            orgCodes.add(bo.getCenterCode());
            orgCodes.add(bo.getRegionCode());
            orgCodes.add(bo.getOutletCode());
            orgCodesCurrent.add(bo.getOrgCodeCurrent());
        });
        List<String> orgCodeList = orgCodes.stream().filter(StringUtil::isNotBlank).collect(Collectors.toList());
        List<String> orgCodeCurrentList = orgCodesCurrent.stream().filter(StringUtil::isNotBlank).collect(Collectors.toList());

        Map<String, String> orgMap = hbOrganizationRepository.getOrgByCode(orgCodeList);
        Map<String, OrgLayerInfoDTO> orgCurrentMap = organazitonOuterService.getOrgLayerInfoByOrgCodeList(orgCodeCurrentList);
        Map<String, String> regionalSubtotalNameMap = queryConsPerformanceCoeffRepository.getRegionalSubtotalNameByOrgCodeList(orgCodeCurrentList);

        // 获取客户名称
        Map<String, String> custNameMap = queryConsPerformanceCoeffRepository.getCustNameMap(dataList);

        // 转换为VO
        List<QueryConsPerformanceCoeffListVO> voList = Lists.newArrayList();
        for (CmConsPerformanceCoeffBO bo : dataList) {
            OrgLayerInfoDTO orgLayerInfoDTO = orgCurrentMap.get(bo.getOrgCodeCurrent());
            QueryConsPerformanceCoeffListVO vo = convertToSingleListVO(bo, orgMap, orgLayerInfoDTO, regionalSubtotalNameMap, custNameMap);
            voList.add(vo);
        }

        return voList;
    }

    /**
     * @param listVO 数据库对象列表
     * @return java.util.List<com.howbuy.crm.account.service.vo.consultant.QueryConsPerformanceCoeffExcelVO>
     * @description: 转换为Excel导出VO
     * <AUTHOR>
     * @date 2025-07-16 18:05:34
     * @since JDK 1.8
     */
    private List<QueryConsPerformanceCoeffExcelVO> convertToExcelVO(List<QueryConsPerformanceCoeffListVO> listVO) {
        if (CollectionUtils.isEmpty(listVO)) {
            return Lists.newArrayList();
        }

        // 转换为Excel VO
        List<QueryConsPerformanceCoeffExcelVO> excelVOList = Lists.newArrayList();
        for (QueryConsPerformanceCoeffListVO listVOItem : listVO) {
            QueryConsPerformanceCoeffExcelVO excelVO = convertToSingleExcelVO(listVOItem);
            excelVOList.add(excelVO);
        }

        return excelVOList;
    }

    /**
     * @param bo 数据库对象
     * @param orgMap 组织信息
     * @param orgLayerInfoDTO 组织层级信息
     * @param regionalSubtotalNameMap 区副名称映射
     * @return com.howbuy.crm.account.client.response.consultant.QueryConsPerformanceCoeffListVO
     * @description: 转换为单个列表VO
     * <AUTHOR>
     * @date 2025-07-16 18:05:34
     * @since JDK 1.8
     */
    private QueryConsPerformanceCoeffListVO convertToSingleListVO(CmConsPerformanceCoeffBO bo,
                                                                  Map<String, String> orgMap,
                                                                  OrgLayerInfoDTO orgLayerInfoDTO,
                                                                  Map<String, String> regionalSubtotalNameMap,
                                                                  Map<String, String> custNameMap) {
        QueryConsPerformanceCoeffListVO vo = new QueryConsPerformanceCoeffListVO();
        vo.setCustName(custNameMap.get(bo.getConsCustNo()));
        vo.setConsName(bo.getConsName());
        vo.setAssignTime(DateUtil.date2String(bo.getAssignTime(), DATETIME_FORMAT));
        vo.setConsCustNo(bo.getConsCustNo());
        vo.setConsCode(bo.getConscode());
        vo.setSourceType(bo.getSourceType());
        vo.setCommissionCoeffStart(bo.getCommissionCoeffStart());
        vo.setCustConversionCoeff(bo.getCustConversionCoeff());
        vo.setManageCoeffSubtotal(bo.getManageCoeffSubtotal());
        vo.setManageCoeffRegionalsubtotal(bo.getManageCoeffRegionalsubtotal());
        vo.setManageCoeffRegionaltotal(bo.getManageCoeffRegionaltotal());
        vo.setCxb(bo.getCxb());

        // 转换是否计提公募字段
        vo.setIsBigV(YesOrNoEnum.getDescription(bo.getIsBigV()));

        // 设置组织信息（分配时）
        vo.setCenterOrgAssignTime(orgMap.get(bo.getCenterCode()));
        vo.setRegionAssignTime(orgMap.get(bo.getRegionCode()));
        vo.setRegionViceAssignTime(bo.getRegionalsubtotal());
        vo.setPartOrgAssignTime(orgMap.get(bo.getOutletCode()));

        // 设置组织信息（当前）
        if (orgLayerInfoDTO != null) {
            vo.setCenterOrgCurrent(orgLayerInfoDTO.getCenterOrgName());
            vo.setRegionCurrent(orgLayerInfoDTO.getDistrictName());
            vo.setRegionViceCurrent(regionalSubtotalNameMap.get(bo.getOrgCodeCurrent()));
            vo.setPartOrgCurrent(orgLayerInfoDTO.getPartOrgName());
        }

        // 设置修改人
        vo.setUpdateUser(bo.getModifier());

        return vo;
    }

    /**
     * @param listVO 列表VO
     * @return com.howbuy.crm.account.service.vo.consultant.QueryConsPerformanceCoeffExcelVO
     * @description: 转换为单个Excel导出VO
     * <AUTHOR>
     * @date 2025-07-16 18:05:34
     * @since JDK 1.8
     */
    private QueryConsPerformanceCoeffExcelVO convertToSingleExcelVO(QueryConsPerformanceCoeffListVO listVO) {
        QueryConsPerformanceCoeffExcelVO excelVO = new QueryConsPerformanceCoeffExcelVO();
        excelVO.setCustName(listVO.getCustName());
        excelVO.setConsName(listVO.getConsName());
        excelVO.setAssignTime(listVO.getAssignTime());
        excelVO.setConsCustNo(listVO.getConsCustNo());
        excelVO.setConsCode(listVO.getConsCode());
        excelVO.setCenterOrgCurrent(listVO.getCenterOrgCurrent());
        excelVO.setRegionCurrent(listVO.getRegionCurrent());
        excelVO.setRegionViceCurrent(listVO.getRegionViceCurrent());
        excelVO.setPartOrgCurrent(listVO.getPartOrgCurrent());
        excelVO.setSourceType(listVO.getSourceType());
        excelVO.setCommissionCoeffStart(listVO.getCommissionCoeffStart());
        excelVO.setCustConversionCoeff(listVO.getCustConversionCoeff());
        excelVO.setManageCoeffSubtotal(listVO.getManageCoeffSubtotal());
        excelVO.setManageCoeffRegionalsubtotal(listVO.getManageCoeffRegionalsubtotal());
        excelVO.setManageCoeffRegionaltotal(listVO.getManageCoeffRegionaltotal());
        excelVO.setCxb(listVO.getCxb());
        excelVO.setIsBigV(listVO.getIsBigV());
        excelVO.setCenterOrgAssignTime(listVO.getCenterOrgAssignTime());
        excelVO.setRegionAssignTime(listVO.getRegionAssignTime());
        excelVO.setRegionViceAssignTime(listVO.getRegionViceAssignTime());
        excelVO.setPartOrgAssignTime(listVO.getPartOrgAssignTime());
        excelVO.setUpdateUser(listVO.getUpdateUser());

        return excelVO;
    }
} 