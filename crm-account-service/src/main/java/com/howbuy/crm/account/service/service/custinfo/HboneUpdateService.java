/**
 * Copyright (c) 2023, <PERSON><PERSON><PERSON><PERSON> HOWBUY INVESTMENT MANAGEMENT Co., Ltd.
 * All right reserved.
 * <p>
 * THIS IS UNPUBLISHED PROPRIETARY SOURCE CODE OF HOWBUY INVESTMENT
 * MANAGEMENT CO., LTD.  THE CONTENTS OF THIS FILE MAY NOT BE DISCLOSED
 * TO THIRD PARTIES, COPIED OR DUPLICATED IN ANY FORM, IN WHOLE OR IN PART,
 * WITHOUT THE PRIOR WRITTEN PERMISSION OF HOWBUY INVESTMENT MANAGEMENT
 * CO., LTD.
 */
package com.howbuy.crm.account.service.service.custinfo;

import com.howbuy.crm.account.client.request.custinfo.UpdateHkAssetIsolateRequest;
import com.howbuy.crm.account.client.response.Response;
import com.howbuy.crm.account.service.outerservice.acct.HboneUpdateOuterService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

/**
 * @description: (一账通  更新一账通相关 信息service)
 * <AUTHOR>
 * @date 2023/12/13 16:41
 * @since JDK 1.8
 */
@Service
@Slf4j
public class HboneUpdateService {


    @Autowired
    private HboneUpdateOuterService hboneUpdateOuterService;




    /**
     * @description:(更新一账通资产隔离标志)
     * @param isolateRequest
     * @return com.howbuy.crm.account.client.response.Response<java.lang.String>
     * @author: haoran.zhang
     * @date: 2024/5/29 9:58
     * @since JDK 1.8
     */
    public Response<String> updateHkAssetIsolateFlag(UpdateHkAssetIsolateRequest isolateRequest) {
        return hboneUpdateOuterService.updateHkAssetIsolateFlag(isolateRequest);
    }

}