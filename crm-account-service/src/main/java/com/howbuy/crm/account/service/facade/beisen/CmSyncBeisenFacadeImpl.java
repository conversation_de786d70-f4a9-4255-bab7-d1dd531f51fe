/**
 * Copyright (c) 2024, <PERSON><PERSON><PERSON><PERSON> HOWBUY INVESTMENT MANAGEMENT Co., Ltd.
 * All right reserved.
 * <p>
 * THIS IS UNPUBLISHED PROPRIETARY SOURCE CODE OF HOWBUY INVESTMENT
 * MANAGEMENT CO., LTD.  THE CONTENTS OF THIS FILE MAY NOT BE DISCLOSED
 * TO THIRD PARTIES, COPIED OR DUPLICATED IN ANY FORM, IN WHOLE OR IN PART,
 * WITHOUT THE PRIOR WRITTEN PERMISSION OF HOWBUY INVESTMENT MANAGEMENT
 * CO., LTD.
 */
package com.howbuy.crm.account.service.facade.beisen;

import com.howbuy.crm.account.client.facade.beisen.CmSyncBeisenFacade;
import com.howbuy.crm.account.client.request.beisen.CmSyncBeisenRequest;
import com.howbuy.crm.account.client.response.Response;
import com.howbuy.crm.account.service.service.beisen.CmSyncBeisenService;
import org.apache.dubbo.config.annotation.DubboService;

import javax.annotation.Resource;
import java.util.List;

/**
 * @description: (北森同步crm数据facade实现)
 * <AUTHOR>
 * @date 2024/11/15 09:36
 * @since JDK 1.8
 */
@DubboService
public class CmSyncBeisenFacadeImpl implements CmSyncBeisenFacade {
    @Resource
    private CmSyncBeisenService cmSyncBeisenService;

    /**
     * @description:(通过userno同步北森数据)
     * @param request
     * @return java.util.List<java.lang.String> 返回异常userno集合
     * @author: shijie.wang
     * @date: 2024/11/15 9:57
     * @since JDK 1.8
     */
    @Override
    public Response<List<String>> syncConsultantExpByBeisenUserNoFacade(CmSyncBeisenRequest request) {
        return  Response.ok(cmSyncBeisenService.syncCmConsultantExpToCmBeisenByUserNo(request));
    }

    /**
     * @description:(通过userno集合同步北森数据)
     * @param requestList
     * @return java.util.List<java.lang.String>
     * @author: shijie.wang
     * @date: 2024/11/15 10:10
     * @since JDK 1.8
     */
    @Override
    public Response<List<String>> syncConsultantExpByBeisenUserNosFacade(List<CmSyncBeisenRequest> requestList) {
        return  Response.ok(cmSyncBeisenService.syncCmConsultantExpToCmBeisenByUserNos(requestList));
    }

    /**
     * @description:(通过时间段同步北森数据)
     * @param startDate
     * @param endDate
     * @return void
     * @author: shijie.wang
     * @date: 2024/11/6 17:33
     * @since JDK 1.8
     */
    @Override
    public Response<List<String>> syncConsultantExpByCmBeisenDateTimeFacade(String startDate, String endDate) {
        return  Response.ok(cmSyncBeisenService.syncCmConsultantExpToCmBeisenByDateTime(startDate, endDate));
    }

    /**
     * @description:(执行调度任务的北森同步花名册数据)
     * @param
     * @return void
     * @author: shijie.wang
     * @date: 2024/11/14 17:20
     * @since JDK 1.8
     */
    @Override
    public Response<List<String>> execSyncBeisenToCrmByJobFacade() {
        return  Response.ok(cmSyncBeisenService.execSyncBeisenToCrmByJob());
    }
}