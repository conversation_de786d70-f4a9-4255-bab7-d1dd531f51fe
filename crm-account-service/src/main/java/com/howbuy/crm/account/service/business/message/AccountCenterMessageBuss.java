/**
 * Copyright (c) 2025, <PERSON><PERSON><PERSON><PERSON> HOWBUY INVESTMENT MANAGEMENT Co., Ltd.
 * All right reserved.
 * <p>
 * THIS IS UNPUBLISHED PROPRIETARY SOURCE CODE OF HOWBUY INVESTMENT
 * MANAGEMENT CO., LTD.  THE CONTENTS OF THIS FILE MAY NOT BE DISCLOSED
 * TO THIRD PARTIES, COPIED OR DUPLICATED IN ANY FORM, IN WHOLE OR IN PART,
 * WITHOUT THE PRIOR WRITTEN PERMISSION OF HOWBUY INVESTMENT MANAGEMENT
 * CO., LTD.
 */
package com.howbuy.crm.account.service.business.message;

import com.alibaba.fastjson.JSON;
import com.howbuy.crm.account.client.enums.message.AccountTypeEnum;
import com.howbuy.crm.account.client.request.custinfo.HkAcctCreateOptVO;
import com.howbuy.crm.account.client.response.Response;
import com.howbuy.crm.account.dao.po.message.CmAccountCenterMessagePO;
import com.howbuy.crm.account.service.commom.constant.Constants;
import com.howbuy.crm.account.service.message.hbonecustinfo.AbstractHboneMessageProcessor;
import com.howbuy.crm.account.service.message.hkcustinfo.AbstractHkMessageProcessor;
import com.howbuy.crm.account.service.repository.message.CmAccountCenterMessageRepository;
import com.howbuy.crm.account.service.service.custinfo.HkCustInfoService;
import com.howbuy.message.SimpleMessage;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.ApplicationContext;
import org.springframework.stereotype.Service;

import java.util.Objects;

/**
 * <AUTHOR>
 * @description: (请在此添加描述)
 * @date 2025/5/30 15:25
 * @since JDK 1.8
 */

@Slf4j
@Service
public class AccountCenterMessageBuss {

    @Autowired
    private CmAccountCenterMessageRepository cmAccountCenterMessageRepository;

    @Autowired
    private ApplicationContext applicationContext;

    @Autowired
    private HkCustInfoService hkCustInfoService;


    /**
     * @description: 重新消费 账号中心的消息
     * @param messageId
     * @return com.howbuy.crm.account.client.response.Response<java.lang.String>
     * @author: jin.wang03
     * @date: 2025/5/30 15:40
     * @since JDK 1.8
     */
    public Response<String> consumeAgain(String messageId) {
        CmAccountCenterMessagePO cmAccountCenterMessagePO = cmAccountCenterMessageRepository.selectById(messageId);
        if (Objects.isNull(cmAccountCenterMessagePO)) {
            return Response.fail("消息不存在");
        }

        // 是 香港账号消息，还是 一账通账户消息
        String accountType = cmAccountCenterMessagePO.getAccountType();
        // 消息topic
        String topic = cmAccountCenterMessagePO.getTopic();
        // 消息tag
        String tag = cmAccountCenterMessagePO.getTag();

        // 消息内容
        String mqMessageText = cmAccountCenterMessagePO.getMqMessageText();


        if (AccountTypeEnum.HK.getCode().equals(accountType)) {
            // CRM资料-香港开户，不是通过监听消息来触发，而是回调接口，因此需要手动处理
            if (Constants.TOPIC_HK_OPEN_ACCT.equals(topic) && Constants.CRM_OPEN_ACCT.equals(tag)) {
                HkAcctCreateOptVO hkAcctCreateOptVO = JSON.parseObject(mqMessageText, HkAcctCreateOptVO.class);
                return hkCustInfoService.analyzeAndProcess(hkAcctCreateOptVO, cmAccountCenterMessagePO.getId());
            }

            String[] beanNamesForType = applicationContext.getBeanNamesForType(AbstractHkMessageProcessor.class);
            for (String beanName : beanNamesForType) {
                AbstractHkMessageProcessor processor = (AbstractHkMessageProcessor) applicationContext.getBean(beanName);
                if (processor.getQuartMessageChannel().equals(topic)
                        && processor.getSupportTagList().contains(tag)) {

                    SimpleMessage simpleMessage = JSON.parseObject(mqMessageText, SimpleMessage.class);
                    return processor.dealMessage(simpleMessage);
                }
            }

            return Response.fail("不支持的消息类型");
        } else if (AccountTypeEnum.HBONE.getCode().equals(accountType)) {
            String[] beanNamesForType = applicationContext.getBeanNamesForType(AbstractHboneMessageProcessor.class);
            for (String beanName : beanNamesForType) {
                AbstractHboneMessageProcessor processor = (AbstractHboneMessageProcessor) applicationContext.getBean(beanName);
                if (processor.getQuartMessageChannel().equals(topic)
                        && processor.getSupportTagList().contains(tag)) {

                    SimpleMessage simpleMessage = JSON.parseObject(mqMessageText, SimpleMessage.class);
                    return processor.dealMessage(simpleMessage);
                }
            }
            return Response.fail("不支持的消息类型");
        } else {
            return Response.fail("不支持的消息类型");
        }

    }

}