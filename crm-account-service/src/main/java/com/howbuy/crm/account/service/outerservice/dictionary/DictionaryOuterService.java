/**
 * Copyright (c) 2023, <PERSON><PERSON><PERSON><PERSON> HOWBUY INVESTMENT MANAGEMENT Co., Ltd.
 * All right reserved.
 * <p>
 * THIS IS UNPUBLISHED PROPRIETARY SOURCE CODE OF HOWBUY INVESTMENT
 * MANAGEMENT CO., LTD.  THE CONTENTS OF THIS FILE MAY NOT BE DISCLOSED
 * TO THIRD PARTIES, COPIED OR DUPLICATED IN ANY FORM, IN WHOLE OR IN PART,
 * WITHOUT THE PRIOR WRITTEN PERMISSION OF HOWBUY INVESTMENT MANAGEMENT
 * CO., LTD.
 */
package com.howbuy.crm.account.service.outerservice.dictionary;

import cn.hutool.http.HttpUtil;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.howbuy.crm.account.client.response.Response;
import com.howbuy.crm.account.client.response.dictionary.*;
import com.howbuy.crm.account.service.commom.constant.Constants;
import com.howbuy.crm.account.service.commom.utils.MainLogUtils;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;

import javax.annotation.PostConstruct;
import java.util.*;

/**
 * <AUTHOR>
 * @description: 字典值outerservice
 * @date 2023/12/26 9:51
 * @since JDK 1.8
 */

@Slf4j
@Service
public class DictionaryOuterService {

    @Value("${query.countrylist.url}")
    private String queryCountryListUrl;

    @Value("${query.citylist.url}")
    private String queryCityListUrl;

    @Value("${common.area}")
    private String commonAreaStr;

    @Autowired
    private ParamMobileAreaOuterService paramMobileAreaOuterService;

    private final Map<Integer, String> commonAreaMap = new HashMap<>();

    @PostConstruct
    private void init() {
        if (StringUtils.isEmpty(commonAreaStr)) {
            return;
        }
        JSONObject jsonObject = JSON.parseObject(commonAreaStr);
        for (Map.Entry<String, Object> entry : jsonObject.entrySet()) {
            String sortId = entry.getKey();
            Object countryShortCode = entry.getValue();
            commonAreaMap.put(Integer.valueOf(sortId), String.valueOf(countryShortCode));
        }
    }

    /**
     * @return com.howbuy.crm.account.client.response.dictionary.CountryListVO 国家列表
     * @description: 获取国家列表
     * @author: jin.wang03
     * @date: 2023/12/26 10:18
     * @since JDK 1.8
     */
    public List<CountryInfoVO> getCountryList() {
        List<CountryInfoVO> resultList = new ArrayList<>();


        Map<String, CountryInfoVO> commonAreaCountryInfoVOMap = new HashMap<>();
        try {
            long startTime = System.currentTimeMillis();
            String s = HttpUtil.get(queryCountryListUrl);
            long endTime = System.currentTimeMillis();
            MainLogUtils.httpCallOut(queryCountryListUrl, Constants.HTTP_SUCCESS_CODE, endTime - startTime);
            JSONObject jsonObject = JSON.parseObject(s);
            JSONArray countryList = jsonObject.getJSONObject("body").getJSONArray("dataList");
            for (Object country : countryList) {
                JSONObject countryObject = (JSONObject) country;
                CountryInfoVO countryInfoVO = new CountryInfoVO();
                countryInfoVO.setChineseName(countryObject.getString("ChineseName"));
                countryInfoVO.setEnglishName(countryObject.getString("EnglishName"));
                countryInfoVO.setFirstPinyin(countryObject.getString("firstPinyin"));
                countryInfoVO.setIso31662(countryObject.getString("iso31662"));
                countryInfoVO.setShortCode(countryObject.getString("shortCode"));
                if (commonAreaMap.containsValue(countryInfoVO.getShortCode())) {
                    commonAreaCountryInfoVOMap.put(countryInfoVO.getShortCode(), countryInfoVO);
                } else {
                    resultList.add(countryInfoVO);
                }
            }
            // 常用的国家 排在队列的前面
            for (Map.Entry<Integer, String> entry : commonAreaMap.entrySet()) {
                Integer index = entry.getKey();
                String shortCode = entry.getValue();
                CountryInfoVO countryInfoVO = commonAreaCountryInfoVOMap.get(shortCode);
                if (countryInfoVO == null) {
                    continue;
                }
                resultList.add(index, countryInfoVO);
            }

        } catch (Exception e) {
            log.error("error in getCountryList", e);
        }
        return resultList;
    }

    /**
     * @description: 获取省市区列表接口
     * @return java.util.List<com.howbuy.crm.account.client.response.dictionary.CityListVO> 省市区列表
     * @author: jin.wang03
     * @date: 2023/12/26 11:18
     * @since JDK 1.8
     */
    public List<CityListVO> getCityListVO() {
        long startTime = System.currentTimeMillis();
        String cityList = HttpUtil.get(queryCityListUrl);
        long endTime = System.currentTimeMillis();
        MainLogUtils.httpCallOut(queryCityListUrl, Constants.HTTP_SUCCESS_CODE, endTime - startTime);

        JSONObject JSONObject = JSON.parseObject(cityList).getJSONObject("body");
        JSONArray jsonArray = JSONObject.getJSONArray("dataList");

        return JSON.parseArray(jsonArray.toJSONString(), CityListVO.class);

    }

    public ParamMobileAreaListVO getMobileAreaCodeList() {
        ParamMobileAreaListVO result = new ParamMobileAreaListVO();

        Response<ParamMobileAreaDTO> dtoResponse = paramMobileAreaOuterService.query();
        if (Objects.nonNull(dtoResponse) && Objects.nonNull(dtoResponse.getData())) {
            ParamMobileAreaDTO paramMobileAreaDTO = dtoResponse.getData();

            result.setIndex(paramMobileAreaDTO.getIndex());
            result.setItemList(paramMobileAreaDTO.getItemList());
        }


        return result;
    }


    /**
     * @description:(请在此添加描述)
     * @param
     * @return com.howbuy.crm.account.client.response.Response<com.howbuy.crm.account.client.response.dictionary.ParamMobileAreaListVO>
     * @author: jin.wang03
     * @date: 2024/1/11 14:13
     * @since JDK 1.8
     */
    public Response<ParamMobileAreaListVO> getMobileAreaCodeList2() {
        return paramMobileAreaOuterService.getMobileAreaCode();
    }
}