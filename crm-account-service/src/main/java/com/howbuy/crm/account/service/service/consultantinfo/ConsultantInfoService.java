/**
 * Copyright (c) 2023, <PERSON><PERSON><PERSON><PERSON> HOWBUY INVESTMENT MANAGEMENT Co., Ltd.
 * All right reserved.
 * <p>
 * THIS IS UNPUBLISHED PROPRIETARY SOURCE CODE OF HOWBUY INVESTMENT
 * MANAGEMENT CO., LTD.  THE CONTENTS OF THIS FILE MAY NOT BE DISCLOSED
 * TO THIRD PARTIES, COPIED OR DUPLICATED IN ANY FORM, IN WHOLE OR IN PART,
 * WITHOUT THE PRIOR WRITTEN PERMISSION OF HOWBUY INVESTMENT MANAGEMENT
 * CO., LTD.
 */
package com.howbuy.crm.account.service.service.consultantinfo;

import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import com.howbuy.crm.account.client.enums.ExceptionCodeEnum;
import com.howbuy.crm.account.client.enums.YesOrNoEnum;
import com.howbuy.crm.account.client.request.consultantinfo.ConsultantWechatInfoRequest;
import com.howbuy.crm.account.client.request.consultantinfo.QueryConsultantInfoRequest;
import com.howbuy.crm.account.client.response.consultantinfo.CmConstantInfoListVO;
import com.howbuy.crm.account.client.response.consultantinfo.CmConsultantInfo;
import com.howbuy.crm.account.client.response.consultantinfo.ConsultantWechatInfoRespVO;
import com.howbuy.crm.account.client.response.custinfo.CmConsCustVO;
import com.howbuy.crm.account.dao.po.consultant.CmConsultantPO;
import com.howbuy.crm.account.dao.po.consultant.SearchConsultantDTO;
import com.howbuy.crm.account.dao.req.consultant.QueryConsultantReqVO;
import com.howbuy.crm.account.service.commom.exception.ParamsException;
import com.howbuy.crm.account.service.domain.OrgLayerInfoDTO;
import com.howbuy.crm.account.service.outerservice.crmcore.OrganazitonOuterService;
import com.howbuy.crm.account.service.repository.consultant.CmConsultantRepository;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;

import java.util.*;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @description: (crm投顾信息service)
 * @date ********
 * @since JDK 1.8
 */
@Service
@Slf4j
public class ConsultantInfoService {


    @Autowired
    private OrganazitonOuterService organazitonOuterService;

    @Autowired
    private CmConsultantRepository cmConsultantRepository;
    @Value("${pm.role.code}")
    private String pmRoleCode;

    /**
     * @param
     * @return java.util.List<java.lang.String>
     * @description:(查询所有需要刷新客户关系的投顾企微账号)
     * @author: shuai.zhang
     * @date: 2024/3/27 11:18
     * @since JDK 1.8
     */
    public List<String> getAllNeedRefreshWechatConsCode() {
        return cmConsultantRepository.getAllNeedRefreshWechatConsCode();
    }

    /**
     * @param req
     * @return com.howbuy.crm.account.client.response.consultantinfo.ConsultantWechatInfoRespVO
     * @description:(根据投顾codelist查询投顾企微账号)
     * @author: shuai.zhang
     * @date: 2024/3/28 14:37
     * @since JDK 1.8
     */
    public ConsultantWechatInfoRespVO getWechatConscodesByConscodes(ConsultantWechatInfoRequest req) {
        ConsultantWechatInfoRespVO respVO = new ConsultantWechatInfoRespVO();
        List<CmConsultantPO> list = cmConsultantRepository.getListByConscodes(req.getConsCodeList());
        if (CollectionUtils.isNotEmpty(list)) {
            ArrayList<CmConsultantInfo> objects = Lists.newArrayList();
            list.forEach(cmConsultantPO -> {
                CmConsultantInfo cmConsultantInfo = new CmConsultantInfo();
                cmConsultantInfo.setConscode(cmConsultantPO.getConscode());
                cmConsultantInfo.setConsname(cmConsultantInfo.getConsname());
                cmConsultantInfo.setWechatconscode(cmConsultantPO.getWechatconscode());
                objects.add(cmConsultantInfo);
            });
            respVO.setConsultantWechatInfoList(objects);
        }
        return respVO;
    }

    /**
     * @param request
     * @return com.howbuy.crm.account.client.response.consultantinfo.CmConsultantInfo
     * @description:查询投顾信息
     * <AUTHOR>
     * @date 2024/9/6 15:41
     * @since JDK 1.8
     */
    public CmConstantInfoListVO queryConsultantInfo(QueryConsultantInfoRequest request) {
        if (StringUtils.isEmpty(request.getUserId()) && CollectionUtils.isEmpty(request.getConsCodeList())) {
            throw new ParamsException(ExceptionCodeEnum.PARAMS_ERROR);
        }

        CmConstantInfoListVO listVO = new CmConstantInfoListVO();
        listVO.setCmConsultantInfoList(new ArrayList<>());

        QueryConsultantReqVO reqVO = new QueryConsultantReqVO();
        reqVO.setConsCodeList(request.getConsCodeList());
        reqVO.setUserId(request.getUserId());
        reqVO.setConsStatus(StringUtils.isEmpty(request.getConsStatus()) ? YesOrNoEnum.YES.getCode() : request.getConsStatus());
        List<CmConsultantPO> poList = cmConsultantRepository.queryListByVo(reqVO);
        if (CollectionUtils.isEmpty(poList)) {
            return listVO;
        }
        for (CmConsultantPO cmConsultantPO : poList) {
            CmConsultantInfo cmConsultantInfo = new CmConsultantInfo();
            cmConsultantInfo.setConscode(cmConsultantPO.getConscode());
            cmConsultantInfo.setConsname(cmConsultantPO.getConsname());
            cmConsultantInfo.setWechatconscode(cmConsultantPO.getWechatconscode());
            listVO.getCmConsultantInfoList().add(cmConsultantInfo);
        }
        return listVO;
    }

    /**
     * @description: 查询 客户的手机号 是否是公司投顾
     * @param mobileDigest
     * @return int
     * @author: jin.wang03
     * @date: 2024/11/4 14:23
     * @since JDK 1.8
     */
    public Boolean queryMobileIsConsultant(String mobileDigest) {
        // 查询CRM<用户管理>页，是否有 员工状态 = “正常” 且 员工手机号 = 客户手机号的记录；
        int count = cmConsultantRepository.countByMobileDigest(mobileDigest);
        return count > 0;
    }

    /**
     * @description 获取用户名称
     * @param consCodeList
     * @return java.util.Map<java.lang.String,java.lang.String>
     * @author: jianjian.yang
     * @date: 2025/4/10 11:12
     * @since JDK 1.8
     */
    public Map<String, String> getConsultantNameMap(List<String> consCodeList) {
        if (CollectionUtils.isEmpty(consCodeList)) {
            return Maps.newHashMap();
        }
        consCodeList = consCodeList.stream().distinct().filter(StringUtils::isNotEmpty).collect(Collectors.toList());
        List<CmConsultantPO> list = cmConsultantRepository.getContainQuitListByConsCodes(consCodeList);
        Map<String,String> map = Maps.newHashMap();
        if (CollectionUtils.isNotEmpty(list)) {
            list.forEach(cmConsultantPO -> map.put(cmConsultantPO.getConscode(),cmConsultantPO.getConsname()));
        }
        return map;
    }

    /**
     * @description 搜索用户
     * @param keyword 搜索关键字
     * @param isPm 是否是PM
     * @param currentUserId 当前登录用户id 查项目经理时需要传入
     * @return java.util.List<com.howbuy.crm.account.dao.po.consultant.SearchConsultantDTO>
     * @author: jianjian.yang
     * @date: 2025/4/11 15:39
     * @since JDK 1.8
     */
    public List<SearchConsultantDTO> searchConsultant(String keyword, boolean isPm, String currentUserId) {
        List<SearchConsultantDTO> list;
        if(isPm){
            List<String> pmRoleCodes = Arrays.stream(pmRoleCode.split(",")).collect(Collectors.toList());
            List<SearchConsultantDTO> searchConsultantDTOS = cmConsultantRepository.searchConsultant(keyword, true, pmRoleCodes);
            // 过滤投顾
            list = filterConsultant(searchConsultantDTOS, currentUserId);
        }else {
            list = cmConsultantRepository.searchConsultant(keyword, false, null);
        }
        return list;
    }
    
    /**
     * @description 过滤投顾
     * @param list 所有投顾
     * @param currentUserId 当前登录用户id
     * @return java.util.List<com.howbuy.crm.account.dao.po.consultant.SearchConsultantDTO>
     * @author: jianjian.yang
     * @date: 2025/5/6 15:40
     * @since JDK 1.8
     */
    private List<SearchConsultantDTO> filterConsultant(List<SearchConsultantDTO> list, String currentUserId) {
        if(CollectionUtils.isEmpty(list)){
            return list;
        }
        // 获取所有投顾的机构编码
        List<String> orgCodes = list.stream().map(SearchConsultantDTO::getOrgCode).collect(Collectors.toList());
        // 获取所有投顾的机构层级信息
        Map<String, OrgLayerInfoDTO> orgLayerInfoDTOMap = organazitonOuterService.getOrgLayerInfoByOrgCodeList(orgCodes);
        // 获取当前登录用户信息
        CmConsultantPO cmConsultantPO = cmConsultantRepository.selectConsultant(currentUserId);
        OrgLayerInfoDTO currentUserOrgLayerInfoDTO = organazitonOuterService.getOrgLayerInfoByOrgCode(cmConsultantPO.getOutletcode());
        List<SearchConsultantDTO> resultList = new ArrayList<>();
        // 遍历所有投顾，判断是否属于当前登录用户机构层级
        list.forEach(searchConsultantDTO -> {
            OrgLayerInfoDTO orgLayerInfoDTO = orgLayerInfoDTOMap.get(searchConsultantDTO.getOrgCode());
            if(orgLayerInfoDTO != null && Objects.equals(orgLayerInfoDTO.getCenterOrgCode(), currentUserOrgLayerInfoDTO.getCenterOrgCode())) {
                resultList.add(searchConsultantDTO);
            }
        });
        return resultList;

    }

    /**
     * @description 根据姓名查询投顾编码
     * @param consName
     * @return java.util.List<java.lang.String>
     * @author: jianjian.yang
     * @date: 2025/4/11 17:18
     * @since JDK 1.8
     */
    public List<String> getConsCodeByName(String consName) {
        if(StringUtils.isEmpty(consName)){
            return null;
        }
        return cmConsultantRepository.getConsCodeByName(consName);
    }
}