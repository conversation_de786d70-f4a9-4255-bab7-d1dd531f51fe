package com.howbuy.crm.account.service.business.custinfo;

import com.alibaba.fastjson.JSON;
import com.howbuy.crm.account.client.enums.custinfo.AbnormaSceneTypeEnum;
import com.howbuy.crm.account.client.enums.custinfo.FullCustSourceEnum;
import com.howbuy.crm.account.client.response.Response;
import com.howbuy.crm.account.dao.bo.custinfo.CmConscustForAnalyseBO;
import com.howbuy.crm.account.service.commom.constant.Constants;
import com.howbuy.crm.account.service.repository.custinfo.HboneAbnormalCustRepository;
import com.howbuy.crm.account.service.repository.custinfo.HkAbnormalCustRepository;
import com.howbuy.crm.account.service.service.custmessage.CustMessageAnalyseService;
import com.howbuy.crm.account.service.vo.custinfo.AbnormalAnalyseResultVO;
import com.howbuy.crm.account.service.vo.custinfo.MessageCustInfoVO;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;
import java.util.function.Function;



@Service
@Slf4j
public class CustMessageOperateExecutor {

    @Autowired
    private HkAbnormalCustRepository hkAbnormalCustRepository;

    @Autowired
    private HboneAbnormalCustRepository hboneAbnormalCustRepository;



    /**
     * @description:(流程处理中  香港客户异常数据 )
     * @param analyseVo
     * @param sceneTypeEnum
     * @param relatedCustList
     * @return void
     * @author: haoran.zhang
     * @date: 2023/12/18 16:46
     * @since JDK 1.8
     */
    public void insertHkProcessAbnormal(MessageCustInfoVO analyseVo,
                                        AbnormaSceneTypeEnum sceneTypeEnum,
                                        List<CmConscustForAnalyseBO> relatedCustList){
        hkAbnormalCustRepository.insertAbnormalCust(analyseVo,sceneTypeEnum,relatedCustList);
    }

    /**
     * @description:(流程处理中  一账通客户异常数据 )
     * @param analyseVo
     * @param sceneTypeEnum
     * @param relatedCustList
     * @return void
     * @author: haoran.zhang
     * @date: 2023/12/18 16:46
     * @since JDK 1.8
     */
    public void insertHboneProcessAbnormal(MessageCustInfoVO analyseVo,
                                           AbnormaSceneTypeEnum sceneTypeEnum,
                                           List<CmConscustForAnalyseBO> relatedCustList){
        hboneAbnormalCustRepository.insertAbnormalCust(analyseVo,sceneTypeEnum,relatedCustList);
    }






    /**
     * @description:(请在此添加描述)
     * @param analyseVo 待分析的数据
     * @param analyseService 异常分析
     * @param abnormalChoose 进入异常表选择 1-香港 2-一账通
     * @param processFunction 处理函数
     * @return com.howbuy.crm.account.client.response.Response<java.lang.String>
     * @author: haoran.zhang
     * @date: 2024/1/11 13:00
     * @since JDK 1.8
     */
    public  <Vo extends MessageCustInfoVO>  Response<String> executeMessageAfterAnalyse(Vo analyseVo,
                                                       CustMessageAnalyseService<Vo> analyseService,
                                                       String abnormalChoose,
                                                       Function<AbnormalAnalyseResultVO<Vo>,Response<String>> processFunction
                                                       ){
        AbnormalAnalyseResultVO<Vo> resultVo=analyseService.analyze(analyseVo);
        //是否解析通过
        if(!resultVo.isAnalysePass()){
            if(Constants.INTO_ABNORMAL_HBONE.equals(abnormalChoose)){
                insertHboneProcessAbnormal(analyseVo,resultVo.getSceneTypeEnum(),resultVo.getRelatedCustList());
            }else if(Constants.INTO_ABNORMAL_HK.equals(abnormalChoose)){
                insertHkProcessAbnormal(analyseVo,resultVo.getSceneTypeEnum(),resultVo.getRelatedCustList());
            }
            return Response.ok();
        }
        //是否标记： 无需处理
        if(!resultVo.isNeedProcess()){
            return Response.ok();
        }
        //处理解析结果
        return processFunction.apply(resultVo);
    }


   private String  getAbnormlaTranslation(String abnormalChoose){
        if (Constants.INTO_ABNORMAL_HBONE.equals(abnormalChoose)) {
            return "一账通异常信息";
        }
        if (Constants.INTO_ABNORMAL_HK.equals(abnormalChoose)) {
            return "香港异常信息";
        }
        return "";
   }


    /**
     * @description:(请在此添加描述)
     * @param analyseVo	
     * @param analyseFunction	
     * @param abnormalChoose	
     * @param processFunction	
     * @return com.howbuy.crm.account.client.response.Response<java.lang.String>
     * @author: haoran.zhang
     * @date: 2024/1/11 13:05
     * @since JDK 1.8
     */
    public  <Vo extends MessageCustInfoVO>  Response<String>
                          executeMessageAfterAnalyse(Vo analyseVo,
                                                     Function<Vo,AbnormalAnalyseResultVO<Vo>> analyseFunction,
                                                     String abnormalChoose,
                                                     Function<AbnormalAnalyseResultVO<Vo>,Response<String>> processFunction){
        AbnormalAnalyseResultVO<Vo> resultVo=analyseFunction.apply(analyseVo);
        log.info("消息处理，来源：{}，香港交易账号：{}，一账通交易账号：{}，是否解析通过：{}，是否需要处理：{}，如有异常，指定进入异常信息表：{}",
                FullCustSourceEnum.getDescription(analyseVo.getAbnormalSource()),
                analyseVo.getHkTxAcctNo(),
                analyseVo.getHboneNo(),
                resultVo.isAnalysePass(),
                resultVo.isNeedProcess(),
                getAbnormlaTranslation(abnormalChoose));
        log.info("消息分析，待分析对象：{}，分析结果：{}",JSON.toJSONString(analyseVo),JSON.toJSONString(resultVo));
        //是否解析通过
        if(!resultVo.isAnalysePass()){
            if(Constants.INTO_ABNORMAL_HBONE.equals(abnormalChoose)){
                insertHboneProcessAbnormal(analyseVo,resultVo.getSceneTypeEnum(),resultVo.getRelatedCustList());
            }else if(Constants.INTO_ABNORMAL_HK.equals(abnormalChoose)){
                insertHkProcessAbnormal(analyseVo,resultVo.getSceneTypeEnum(),resultVo.getRelatedCustList());
            }
            return Response.ok();
        }
        //是否标记： 无需处理
        if(!resultVo.isNeedProcess()){
            return Response.ok();
        }
        //处理解析结果
        Response<String> processResp=processFunction.apply(resultVo);
        log.info("消息处理，来源：{}，香港交易账号：{}，一账通交易账号：{}，处理结果：{}",
                FullCustSourceEnum.getDescription(analyseVo.getAbnormalSource()),
                analyseVo.getHkTxAcctNo(),
                analyseVo.getHboneNo(),
                JSON.toJSONString(processResp));
        return processResp;
    }

}
