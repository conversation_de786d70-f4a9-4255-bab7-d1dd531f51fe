/**
 * Copyright (c) 2023, <PERSON><PERSON><PERSON><PERSON> HOWBUY INVESTMENT MANAGEMENT Co., Ltd.
 * All right reserved.
 * <p>
 * THIS IS UNPUBLISHED PROPRIETARY SOURCE CODE OF HOWBUY INVESTMENT
 * MANAGEMENT CO., LTD.  THE CONTENTS OF THIS FILE MAY NOT BE DISCLOSED
 * TO THIRD PARTIES, COPIED OR DUPLICATED IN ANY FORM, IN WHOLE OR IN PART,
 * WITHOUT THE PRIOR WRITTEN PERMISSION OF HOWBUY INVESTMENT MANAGEMENT
 * CO., LTD.
 */
package com.howbuy.crm.account.service.business.custinfo;

import com.alibaba.fastjson.JSON;
import com.google.common.collect.Lists;
import com.howbuy.acc.common.utils.DigestUtil;
import com.howbuy.acc.common.utils.MaskUtil;
import com.howbuy.acccenter.facade.query.queryhboneinfo.QueryAccHboneInfoRequest;
import com.howbuy.common.utils.DateUtil;
import com.howbuy.common.utils.StringUtil;
import com.howbuy.crm.account.client.enums.YesOrNoEnum;
import com.howbuy.crm.account.client.enums.custinfo.ConsCustInvestTypeEnum;
import com.howbuy.crm.account.client.enums.custinfo.CustSourceFirstLevelEnum;
import com.howbuy.crm.account.client.enums.custinfo.CustSourceSecondLevelEnum;
import com.howbuy.crm.account.client.enums.custinfo.IdTypeEnum;
import com.howbuy.crm.account.client.request.custinfo.BaseModifyConsCustRequest;
import com.howbuy.crm.account.client.request.custinfo.CreateConsCustRequest;
import com.howbuy.crm.account.client.request.custinfo.UpdateConsCustRequest;
import com.howbuy.crm.account.client.response.Response;
import com.howbuy.crm.account.client.response.custinfo.*;
import com.howbuy.crm.account.client.utils.IdTypeUtil;
import com.howbuy.crm.account.dao.bo.custinfo.CmConsCustSimpleBO;
import com.howbuy.crm.account.dao.bo.custinfo.CmConscustForAnalyseBO;
import com.howbuy.crm.account.dao.po.consultant.CmConsultantPO;
import com.howbuy.crm.account.dao.po.custinfo.CmConscustCipherPO;
import com.howbuy.crm.account.dao.po.custinfo.CmConscustPO;
import com.howbuy.crm.account.dao.po.custinfo.CmHkConscustPO;
import com.howbuy.crm.account.dao.po.custinfo.CmSourceInfoPO;
import com.howbuy.crm.account.dao.req.custinfo.CmHkCustReqVO;
import com.howbuy.crm.account.dao.req.custinfo.ConsCustICReqVO;
import com.howbuy.crm.account.dao.req.custinfo.CreateConsCustReqVO;
import com.howbuy.crm.account.dao.req.custinfo.UpdateConsCustReqVO;
import com.howbuy.crm.account.service.commom.constant.Constants;
import com.howbuy.crm.account.service.outerservice.acct.HboneAcctInfoOuterService;
import com.howbuy.crm.account.service.outerservice.auth.EncyptAndDecyptOuterService;
import com.howbuy.crm.account.service.outerservice.hkacct.HkCustBasicInfoOuterService;
import com.howbuy.crm.account.service.repository.consultant.CmConsultantRepository;
import com.howbuy.crm.account.service.repository.custinfo.*;
import com.howbuy.crm.account.service.req.custinfo.HboneCustRelationOptReqVO;
import com.howbuy.crm.account.service.service.consultant.MergeConsPerformanceCoeffService;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;
import org.springframework.util.Assert;
import org.springframework.util.CollectionUtils;

import java.util.Date;
import java.util.List;
import java.util.Locale;
import java.util.Map;
import java.util.concurrent.ConcurrentHashMap;
import java.util.function.Function;
import java.util.function.Predicate;
import java.util.stream.Collectors;

import com.howbuy.crm.account.client.request.consultant.MergeConsPerformanceCoeffRequest;
import com.howbuy.crm.account.dao.po.custinfo.CmCustconstantPO;

/**
 * @description: (crm 投顾客户 业务处理 service)
 * <AUTHOR>
 * @date 2023/12/18 10:31
 * @since JDK 1.8
 */
@Slf4j
@Service
public class CmCustBusinessService {

    @Value("${max.cust.limit}")
    private String maxCustLimit;

    /**
     * 默认投顾可分配的最大客户数
     */
    private static final int DEFAULT_MAX_CUST_LIMIT = 3000;

    @Autowired
    private ConsCustInfoRepository consCustInfoRepository;
    @Autowired
    private ConsCustICRepository icRepository;

    @Autowired
    private CmConsultantRepository consultantRepository;

    @Autowired
    private ConsCustSourceRepository custSourceRepository;
    @Autowired
    private CmHkCustInfoRepository hkCustRepository;

    @Autowired
    private AbnormalCustRepository abnormalCustRepository;
    @Autowired
    private CmHboneCustRelationRepository cmHboneCustRelationRepository;

    @Autowired
    private EncyptAndDecyptOuterService encyptAndDecyptOuterService;
    @Autowired
    private HboneAcctInfoOuterService hboneAcctInfoOuterService;
    @Autowired
    private HkCustBasicInfoOuterService hkCustInfoOuterService;
    @Autowired
    private MergeConsPerformanceCoeffService mergeConsPerformanceCoeffService;
    /**
     *  空字符串
     */
    private static final String  EMPTY_STRING = "";


    /**
     * 计算类型：新增
     */
    private static final String CALC_TYPE_ADD = "1";


    /**
     *0007-存在重复客户
     *原  0009-存在重复机构客户  共用此 code
     * @return
     */
    public static final String REPEAT_CUST="0007";
    /**
     *0008-存在一账通重复客户
     * @return
     */
    public static final String REPEAT_HBONE="0008";

    /**
     * 客户类型-创建客户
     */
    private static final String CUST_TYPE_CREATE = "1";

    /**
     * 投顾可分配的最大客户数. 替换原[HB_CONSTANT][typeCode=maxCustLimit]
     * @return
     */
    private int getMaxCustLimit() {
        return StringUtil.isEmpty(maxCustLimit)? DEFAULT_MAX_CUST_LIMIT :Integer.parseInt(maxCustLimit);
    }


    /**
     * @description:(更新的校验逻辑)
     * @param modifyReq
     * @return com.howbuy.crm.account.client.response.Response<java.lang.String>
     * @author: haoran.zhang
     * @date: 2024/1/9 9:49
     * @since JDK 1.8
     */
    private Response<String> validateUpdate(UpdateConsCustRequest modifyReq){

        //补充 数据原有对象
        CmConsCustVO  dbExistVo=consCustInfoRepository.queryCustInfoByCustNo(modifyReq.getCustNo());

        if("".equals(modifyReq.getInvsttype())){
            return Response.fail("客户类型不能为空！");
        }
        String usedInvstType=StringUtil.isNotBlank(modifyReq.getInvsttype())?modifyReq.getInvsttype():dbExistVo.getInvsttype();
        ConsCustInvestTypeEnum investTypeEnum=ConsCustInvestTypeEnum.getEnum(usedInvstType);
        if(investTypeEnum==null){
            return Response.fail("非法客户类型！");
        }

        /**
         * CRM日常迭代需求******** 新增客户、修改：当 客户类型 = 0-机构/2-产品 时，手机1调整为非必填
         * （即 客户类型 = 1-个人 时，手机1为必填；其他客户类型都是非必填）
         */
        if(investTypeEnum==ConsCustInvestTypeEnum.PERSONAL &&  "".equals(modifyReq.getMobile())){
            return Response.fail("手机号不能为空！");
        }

        if("".equals(modifyReq.getCustsource())){
            return Response.fail("客户来源不能为空！");
        }

        //手机1
        if(StringUtils.isNotBlank(modifyReq.getMobile()) &&  StringUtils.isEmpty(modifyReq.getMobileAreaCode())){
            //要求  手机号码归属地 不能为空
            return Response.fail("手机号码地区码不能为空！");
        }
        //手机2
        if(StringUtils.isNotBlank(modifyReq.getMobile2())  &&  StringUtils.isEmpty(modifyReq.getMobile2AreaCode())){
            //要求  手机号码归属地 不能为空
            return Response.fail("手机号码2 地区码不能为空！");
        }

        if(StringUtils.isNotBlank(modifyReq.getIdNo())){
            //要求 证件类型、证件地区码不能为空
            if(StringUtils.isEmpty(modifyReq.getIdtype()) || StringUtils.isEmpty(modifyReq.getIdSignAreaCode()) ){
                return Response.fail("证件类型、证件地区码不能为空！");
            }
        }
        return Response.ok();
    }


    /**
     *  新增 校验
     * @param modifyReq
     * @return
     */
    private Response<String> validateInsert(BaseModifyConsCustRequest modifyReq){
        ConsCustInvestTypeEnum investTypeEnum=ConsCustInvestTypeEnum.getEnum(modifyReq.getInvsttype());
        if(investTypeEnum==null){
            return Response.fail("客户类型不能为空！");
        }
        /**
         * CRM日常迭代需求******** 新增客户、修改：当 客户类型 = 0-机构/2-产品 时，手机1调整为非必填
         * （即 客户类型 = 1-个人 时，手机1为必填；其他客户类型都是非必填）
         */
        if(investTypeEnum==ConsCustInvestTypeEnum.PERSONAL &&  StringUtil.isEmpty(modifyReq.getMobile())){
            return Response.fail("手机号不能为空！");
        }

        if(StringUtils.isEmpty(modifyReq.getCustsource())){
            return Response.fail("客户来源不能为空！");
        }

        //手机1
        if(StringUtils.isNotBlank(modifyReq.getMobile()) &&  StringUtils.isEmpty(modifyReq.getMobileAreaCode())){
            //要求  手机号码归属地 不能为空
            return Response.fail("手机号码地区码不能为空！");
        }
        //手机2
        if(StringUtils.isNotBlank(modifyReq.getMobile2())  &&  StringUtils.isEmpty(modifyReq.getMobile2AreaCode())){
            //要求  手机号码归属地 不能为空
            return Response.fail("手机号码2 地区码不能为空！");
        }

        if(StringUtils.isNotBlank(modifyReq.getIdNo())){
            //要求 证件类型、证件地区码不能为空
            if(StringUtils.isEmpty(modifyReq.getIdtype()) || StringUtils.isEmpty(modifyReq.getIdSignAreaCode()) ){
                return Response.fail("证件类型、证件地区码不能为空！");
            }
        }

        return Response.ok();
    }




    /**
     * @description:(校验投顾信息， 校验通过，返回 投顾对象)
     * @param consCode
     * @return com.howbuy.crm.account.client.response.Response<com.howbuy.crm.account.dao.po.consultant.CmConsultantPO>
     * @author: haoran.zhang
     * @date: 2023/12/26 17:26
     * @since JDK 1.8
     */
    private Response<CmConsultantPO> validateConsCode(String consCode){
        CmConsultantPO consultantPo=consultantRepository.selectValidConsultant(consCode);
        //校验 投顾  是否存在
        if(consultantPo==null){
            log.error("新增客户，投顾不存在或已失效！consCode:{}",consCode);
            return Response.fail("投顾不存在或已失效！");
        }
        //非虚拟投顾，校验 投顾最大可配置客户量
        if(YesOrNoEnum.NO.getCode().equals(consultantPo.getIsvirtual())){
            //查询投顾最大可配置客户量
            //查询投顾已配置客户量
            Integer hadCustNum=consultantRepository.countCustNumByConsCode(consCode);
            if(hadCustNum>=getMaxCustLimit()){
                log.error("新增客户，投顾已配置客户量已达上限！consCode:{},hadCustNum:{},maxCustLimit:{}",consCode,hadCustNum,maxCustLimit);
                return Response.fail("投顾已配置客户量已达上限！");
            }
        }

        return Response.ok("",consultantPo);
    }



    /**
     * @description: 创建投顾客户
     * @param createCustReq	新增客户信息请求类
     * @return com.howbuy.crm.account.client.response.Response<com.howbuy.crm.account.client.response.custinfo.CreateConsCustRespVO> 新增客户信息响应类
     * @author: jin.wang03
     * @date: 2023/12/27 10:54
     * @since JDK 1.8
     */
    public Response<CreateConsCustRespVO> insertCust(CreateConsCustRequest createCustReq){

        //校验-常规属性
        Response<String> validateResp= validateInsert(createCustReq);
        if(!validateResp.isSuccess()){
            return Response.fail(validateResp.getDescription());
        }
        //conseCode逻辑：默认 当前操作人
        String consCode=StringUtil.isEmpty(createCustReq.getConsCode())?createCustReq.getOperator():createCustReq.getConsCode();
        //校验-投顾信息
        Response<CmConsultantPO> validateConsultResp= validateConsCode(consCode);
        if(!validateConsultResp.isSuccess()){
            return Response.fail(validateConsultResp.getDescription());
        }
        //返回  后续使用的 投顾对象
        CmConsultantPO useConsultantPo=validateConsultResp.getData();
        //判断依赖的 属性
        ConsCustInvestTypeEnum investTypeEnum=ConsCustInvestTypeEnum.getEnum(createCustReq.getInvsttype());

        //处理逻辑
        CmConscustPO record=new CmConscustPO();
        CmConscustCipherPO cipherPo=new CmConscustCipherPO();
        //request 对象属性 --> 客户po
        fillAttribute(createCustReq,record,cipherPo);
        record.setConscustgrade(Short.parseShort("0"));

        //历史逻辑。如果 idNo不为空， 且为个人客户类型 且 证件类型=身份证(18位)。   生日、性别 使用证件解析 覆写
        fillAgeByIdInfo(record,record.getIdtype(),investTypeEnum,createCustReq.getIdNo());
        //设置分享人投顾是否虚拟
        //若一级来源选择MGM-，二级来源选择成交客户介绍、非成交客户介绍时，"分享人投顾是否虚拟"取值为 来源备注对应客户的所属投顾对应"是否虚拟投顾"
        fillIsVirtualShare(record);

        //构建 repository 保存参数
        CreateConsCustReqVO createVo=new CreateConsCustReqVO();
        createVo.setOperator(createCustReq.getOperator());
        createVo.setConsCode(useConsultantPo.getConscode());
        createVo.setCipherPO(cipherPo);
        createVo.setConscustPO(record);


        CreateConsCustRespVO resp = new CreateConsCustRespVO();
        //历史逻辑： 新增修改  操作记录记录一个 id  .与划转相关。
        String operationId = consCustInfoRepository.getCustOpreationId();
        resp.setOperationid(operationId);

        //重复客户的校验
        List<RepeatCustInfo> repeatList=getRepeatCustList(record,null);
        if(!CollectionUtils.isEmpty(repeatList)){
            //进入IC 表
            ConsCustICReqVO icReqVo =new ConsCustICReqVO();
            icReqVo.setOperationId(operationId);
            icReqVo.setStatus(Constants.CUST_IC_OPERATION_CREATE);
            icReqVo.setIcCheckFlag(Constants.IC_CHECK_FLAG_NO);
            icRepository.createCustIc(createVo,icReqVo,null);
            resp.setRepeatConsCustList(repeatList);
            //重复客户 进数据库 IC 表
            return  Response.fail(REPEAT_CUST,
                    String.format("存在重复%s客户", ConsCustInvestTypeEnum.PERSONAL==investTypeEnum?"":"机构或产品"),
                    resp);
        }


        //无重复客户
        //根据规则  从账户中心 获取 hboneNo.
        String  hboneNoFromAcct=getHboneNoFromAcct(record);
        if(StringUtils.isNotEmpty(hboneNoFromAcct)){
            //获取到 hboneNo . 校验hboneNo 是否被占用
            CmConsCustVO existHboneCust = consCustInfoRepository.queryCustInfoByHboneNo(hboneNoFromAcct)  ;
            if(existHboneCust!=null){
                //"手机号码"+conscustinfo.getMobileMask()+"对应的一账通在CRM中已存在绑定关系"
                //TODO:机构处理 此处为更新存在的客户信息。待产品确认
                return Response.fail(REPEAT_HBONE,
                        String.format("手机号码%s对应的一账通在CRM中已存在绑定关系",record.getMobileMask()),
                        resp);

            }else{
                //hboneNo 未被占用
                record.setHboneNo(hboneNoFromAcct);
            }

        }


        String custNo=consCustInfoRepository.createConsCust(createVo);
        resp.setCustNo(custNo);
        //进入ic表
        //进入IC 表
        ConsCustICReqVO icReqVo =new ConsCustICReqVO();
        icReqVo.setOperationId(operationId);
        icReqVo.setStatus(Constants.CUST_IC_OPERATION_CREATE);
        icReqVo.setIcCheckFlag(Constants.IC_CHECK_FLAG_YES);
        icRepository.createCustIc(createVo,icReqVo,custNo);
        resp.setRepeatConsCustList(repeatList);

        //绑定一账通
        if(StringUtil.isNotBlank(record.getHboneNo())){
            HboneCustRelationOptReqVO bindOpt=new HboneCustRelationOptReqVO();
            bindOpt.setCustNo(custNo);
            bindOpt.setHboneNo(record.getHboneNo());
            bindOpt.setOperator(createCustReq.getOperator());
            bindOpt.setOperateSource(createCustReq.getOperateSource());
            bindOpt.setOperateChannel(createCustReq.getOperateChannel());
            cmHboneCustRelationRepository.executeBind(bindOpt);
        }

        //新创建客户写入到人员绩效系数表中
        mergeConsPerformanceCoeffByCreateCustNo(custNo, createVo.getConsCode(), createCustReq.getOperator());

        return Response.ok(resp);

    }



    /**
     * @description:(根据客户的属性，查找一账通账户中心，获取唯一的 hboneNo )
     * @param consutPo
     * @return java.lang.String
     * @author: haoran.zhang
     * @date: 2024/1/9 10:30
     * @since JDK 1.8
     */ 
    private String getHboneNoFromAcct(CmConscustPO consutPo){
        ConsCustInvestTypeEnum investTypeEnum=ConsCustInvestTypeEnum.getEnum(consutPo.getInvsttype());
        if(investTypeEnum==ConsCustInvestTypeEnum.INSTITUTION || investTypeEnum==ConsCustInvestTypeEnum.PRODUCT){
            QueryAccHboneInfoRequest request=new QueryAccHboneInfoRequest();
            request.setIdNoDigest(consutPo.getIdnoDigest());
            request.setIdType(IdTypeUtil.getAcctUsedIdType(consutPo.getInvsttype(),consutPo.getIdtype()));
            request.setUsername(consutPo.getCustname());
            request.setUserType(consutPo.getInvsttype());
            HboneAcctCustInfoVO hboneCustVo=hboneAcctInfoOuterService.queryAcctInfoByRequest(request);
            return hboneCustVo==null?null:hboneCustVo.getHboneNo();
        }else{
            QueryAccHboneInfoRequest queryHboneInfo = new QueryAccHboneInfoRequest();
            queryHboneInfo.setMobileDigest(consutPo.getMobileDigest());
            HboneAcctCustInfoVO hboneCustVo = hboneAcctInfoOuterService.queryAcctInfoByRequest(queryHboneInfo);
            if(hboneCustVo != null &&
                    StringUtils.isBlank(hboneCustVo.getIdNoDigest()) &&
                    "1".equals(hboneCustVo.getMobileVerifyStatus())){
             return hboneCustVo.getHboneNo();
            }

        }
        return null;
    }


    /**
     * @description:(新增或者修改 客户。 根据request  赋值 )
     * @param modifyCustReq
     * @param conscustPO
     * @param cipherPo
     * @return void
     * @author: haoran.zhang
     * @date: 2023/12/26 19:45
     * @since JDK 1.8
     */
    private void fillAttribute(BaseModifyConsCustRequest modifyCustReq,
                               CmConscustPO conscustPO,
                               CmConscustCipherPO cipherPo){
        conscustPO.setCustname(modifyCustReq.getCustname());
        String mobile=StringUtil.trim(modifyCustReq.getMobile());
        if(StringUtils.isNotBlank(mobile)){
            conscustPO.setMobileDigest(DigestUtil.digest(mobile));
            conscustPO.setMobileMask(MaskUtil.maskMobile(mobile));
            cipherPo.setMobileCipher(getEncryptValue(mobile));
        }else if(EMPTY_STRING.equals(mobile)){
            conscustPO.setMobileDigest(EMPTY_STRING);
            conscustPO.setMobileMask(EMPTY_STRING);
            cipherPo.setMobileCipher(EMPTY_STRING);
        }

        Locale locale = Locale.TRADITIONAL_CHINESE;
        String email = StringUtil.trim(modifyCustReq.getEmail()) ;
        if (StringUtils.isNotBlank(email)) {
            email = email.toLowerCase(locale);
            conscustPO.setEmailDigest(DigestUtil.digest(email));
            conscustPO.setEmailMask(MaskUtil.maskEmail(email));
            cipherPo.setEmailCipher(getEncryptValue(email));
        }else if (EMPTY_STRING.equals(email)) {
            conscustPO.setEmailDigest(EMPTY_STRING);
            conscustPO.setEmailMask(EMPTY_STRING);
            cipherPo.setEmailCipher(EMPTY_STRING);
        }
        String telNo = StringUtil.trim(modifyCustReq.getTelNo());
        if(StringUtils.isNotBlank(telNo)){
            conscustPO.setTelnoDigest(DigestUtil.digest(telNo));
            conscustPO.setTelnoMask(MaskUtil.maskMobile(telNo));
            cipherPo.setTelnoCipher(getEncryptValue(telNo));
        }else if (EMPTY_STRING.equals(telNo)) {
            conscustPO.setTelnoDigest(EMPTY_STRING);
            conscustPO.setTelnoMask(EMPTY_STRING);
            cipherPo.setTelnoCipher(EMPTY_STRING);
        }
        conscustPO.setFax(modifyCustReq.getFax());
        String addr=StringUtil.trim(modifyCustReq.getAddr());
        if(StringUtils.isNotBlank(addr)){
            conscustPO.setAddrDigest(DigestUtil.digest(addr));
            conscustPO.setAddrMask(MaskUtil.maskAddr(addr));
            cipherPo.setAddrCipher(getEncryptValue(addr));
        }else if (EMPTY_STRING.equals(addr)) {
            conscustPO.setAddrDigest(EMPTY_STRING);
            conscustPO.setAddrMask(EMPTY_STRING);
            cipherPo.setAddrCipher(EMPTY_STRING);
        }
        conscustPO.setPostcode(modifyCustReq.getPostcode());
        conscustPO.setIdtype(modifyCustReq.getIdtype());
        String idNo=StringUtil.trim(modifyCustReq.getIdNo());
        if(StringUtils.isNotBlank(idNo)){
            conscustPO.setIdnoDigest(DigestUtil.digest(idNo));
            conscustPO.setIdnoMask(MaskUtil.maskIdNo(idNo));
            cipherPo.setIdnoCipher(getEncryptValue(idNo));
        }else if (EMPTY_STRING.equals(idNo)) {
            conscustPO.setIdnoDigest(EMPTY_STRING);
            conscustPO.setIdnoMask(EMPTY_STRING);
            cipherPo.setIdnoCipher(EMPTY_STRING);
        }
        conscustPO.setIdSignAreaCode(modifyCustReq.getIdSignAreaCode());
        conscustPO.setLinkman(modifyCustReq.getLinkman());
        String linkEmail=StringUtil.trim(modifyCustReq.getLinkEmail());
        if (StringUtils.isNotBlank(linkEmail)) {
            linkEmail = linkEmail.toLowerCase(locale);
            conscustPO.setLinkemailDigest(DigestUtil.digest(linkEmail));
            conscustPO.setLinkemailMask(MaskUtil.maskEmail(linkEmail));
            cipherPo.setLinkemailCipher(getEncryptValue(linkEmail));
        }else if (EMPTY_STRING.equals(linkEmail)) {
            conscustPO.setLinkemailDigest(EMPTY_STRING);
            conscustPO.setLinkemailMask(EMPTY_STRING);
            cipherPo.setLinkemailCipher(EMPTY_STRING);
        }
        String linkMobile=StringUtil.trim(modifyCustReq.getLinkMobile());
        if(StringUtils.isNotBlank(linkMobile)){
            conscustPO.setLinkmobileDigest(DigestUtil.digest(linkMobile));
            conscustPO.setLinkmobileMask(MaskUtil.maskMobile(linkMobile));
            cipherPo.setLinkmobileCipher(getEncryptValue(linkMobile));
        }else if (EMPTY_STRING.equals(linkMobile)) {
            conscustPO.setLinkmobileDigest(EMPTY_STRING);
            conscustPO.setLinkmobileMask(EMPTY_STRING);
            cipherPo.setLinkmobileCipher(EMPTY_STRING);
        }
        conscustPO.setLinkpostcode(modifyCustReq.getLinkpostcode());
        String linkTel=StringUtil.trim(modifyCustReq.getLinkTel());
        if(StringUtils.isNotBlank(linkTel)){
            conscustPO.setLinktelDigest(DigestUtil.digest(linkTel));
            conscustPO.setLinktelMask(MaskUtil.maskMobile(linkTel));
            cipherPo.setLinktelCipher(getEncryptValue(linkTel));
        }else if (EMPTY_STRING.equals(linkTel)) {
            conscustPO.setLinktelDigest(EMPTY_STRING);
            conscustPO.setLinktelMask(EMPTY_STRING);
            cipherPo.setLinktelCipher(EMPTY_STRING);
        }
        String linkAddr=StringUtil.trim(modifyCustReq.getLinkAddr());
        if(StringUtils.isNotBlank(linkAddr)){
            conscustPO.setLinkaddrDigest(DigestUtil.digest(linkAddr));
            conscustPO.setLinkaddrMask(MaskUtil.maskAddr(linkAddr));
            cipherPo.setLinkaddrCipher(getEncryptValue(linkAddr));
        }else if (EMPTY_STRING.equals(linkAddr)) {
            conscustPO.setLinkaddrDigest(EMPTY_STRING);
            conscustPO.setLinkaddrMask(EMPTY_STRING);
            cipherPo.setLinkaddrCipher(EMPTY_STRING);
        }
        conscustPO.setProvcode(modifyCustReq.getProvcode());
        conscustPO.setCitycode(modifyCustReq.getCitycode());
        conscustPO.setCountyCode(modifyCustReq.getCountyCode());
        conscustPO.setConscustlvl(modifyCustReq.getConscustlvl());
        conscustPO.setKnowhowbuy(modifyCustReq.getKnowhowbuy());
        conscustPO.setSubknow(modifyCustReq.getSubknow());
        conscustPO.setSubknowtype(modifyCustReq.getSubknowtype());
        conscustPO.setInvsttype(modifyCustReq.getInvsttype());
        if (StringUtils.isNotBlank(modifyCustReq.getCustsource())) {
            conscustPO.setNewsourceno(modifyCustReq.getCustsource());
        }
        conscustPO.setCustsourceremark(modifyCustReq.getCustSourceRemark());
        conscustPO.setGender(modifyCustReq.getGender());
        conscustPO.setBirthday(modifyCustReq.getBirthday());
        conscustPO.setMarried(modifyCustReq.getMarried());
        if(StringUtils.isNotEmpty(modifyCustReq.getConscustgrade())){
            conscustPO.setConscustgrade(Short.parseShort(modifyCustReq.getConscustgrade()));
        }
        conscustPO.setVisitfqcy(modifyCustReq.getVisitfqcy());
        conscustPO.setEdulevel(modifyCustReq.getEdulevel());
        conscustPO.setVocation(modifyCustReq.getVocation());
        conscustPO.setPincome(modifyCustReq.getPincome());
        conscustPO.setWechatcode(modifyCustReq.getWechatcode());
        conscustPO.setFincome(modifyCustReq.getFincome());
        conscustPO.setDecisionflag(modifyCustReq.getDecisionflag());
        conscustPO.setAcregdt(modifyCustReq.getAcregdt());

        String email2=StringUtil.trim(modifyCustReq.getEmail2());
        if (StringUtils.isNotBlank(email2)) {
            email2=email2.toLowerCase(locale);
            conscustPO.setEmail2Digest(DigestUtil.digest(email2));
            conscustPO.setEmail2Mask(MaskUtil.maskEmail(email2));
            cipherPo.setEmail2Cipher(getEncryptValue(email2));
        }else if (EMPTY_STRING.equals(email2)) {
            conscustPO.setEmail2Digest(EMPTY_STRING);
            conscustPO.setEmail2Mask(EMPTY_STRING);
            cipherPo.setEmail2Cipher(EMPTY_STRING);
        }
        String mobile2=StringUtil.trim(modifyCustReq.getMobile2());
        if(StringUtils.isNotBlank(mobile2)){
            conscustPO.setMobile2Digest(DigestUtil.digest(mobile2));
            conscustPO.setMobile2Mask(MaskUtil.maskMobile(mobile2));
            cipherPo.setMobile2Cipher(getEncryptValue(mobile2));
        }else if (EMPTY_STRING.equals(mobile2)) {
            conscustPO.setMobile2Digest(EMPTY_STRING);
            conscustPO.setMobile2Mask(EMPTY_STRING);
            cipherPo.setMobile2Cipher(EMPTY_STRING);
        }
        String addr2=StringUtil.trim(modifyCustReq.getAddr2());
        if(StringUtils.isNotBlank(addr2)){
            conscustPO.setAddr2Digest(DigestUtil.digest(addr2));
            conscustPO.setAddr2Mask(MaskUtil.maskAddr(addr2));
            cipherPo.setAddr2Cipher(getEncryptValue(addr2));
        }else if (EMPTY_STRING.equals(addr2)) {
            conscustPO.setAddr2Digest(EMPTY_STRING);
            conscustPO.setAddr2Mask(EMPTY_STRING);
            cipherPo.setAddr2Cipher(EMPTY_STRING);
        }
        conscustPO.setPostcode2(modifyCustReq.getPostcode2());
        conscustPO.setCompany(modifyCustReq.getCompany());
        conscustPO.setOfficetelno(modifyCustReq.getOfficetelno());
        conscustPO.setInterests(modifyCustReq.getInterests());
        conscustPO.setContacttime(modifyCustReq.getContacttime());
        conscustPO.setRemark(modifyCustReq.getRemark());


        conscustPO.setSalon(modifyCustReq.getSalon());
        conscustPO.setSaledirection(modifyCustReq.getSaledirection());
        conscustPO.setBeforeinvest(modifyCustReq.getBeforeinvest());
        conscustPO.setValidity(modifyCustReq.getValidity());
        conscustPO.setValiditydt(modifyCustReq.getValiditydt());
        conscustPO.setNature(modifyCustReq.getNature());
        conscustPO.setAptitude(modifyCustReq.getAptitude());
        conscustPO.setScopebusiness(modifyCustReq.getScopebusiness());

        conscustPO.setMobileAreaCode(modifyCustReq.getMobileAreaCode());
        conscustPO.setMobile2AreaCode(modifyCustReq.getMobile2AreaCode());
        conscustPO.setLinkmobileAreaCode(modifyCustReq.getLinkmobileAreaCode());
        conscustPO.setNationCode(modifyCustReq.getNationCode());
    }


    /**
     *对于个人客户，根据身份证号码，自动填充性别和生日
     * @param custPo  客户对象
     * @param usedIdType 证件类型
     * @param usedInvestTypeEnum 客户类型
     * @param idNo 明文idNo
     */
    private void fillAgeByIdInfo(CmConscustPO custPo,
                             String usedIdType,
                             ConsCustInvestTypeEnum usedInvestTypeEnum,
                             String idNo){
        if (IdTypeEnum.ID.getCode().equals(usedIdType)
                && ConsCustInvestTypeEnum.PERSONAL==usedInvestTypeEnum
                && StringUtil.isNotBlank(idNo)
                &&  idNo.length() == 18 ) {
            // 从身份证中截取生日
            String birthDayFromId=idNo.substring(6, 14);
            //验证是否合法Date 对象
            Date  idDate=DateUtil.string2Date(birthDayFromId,DateUtil.SHORT_DATE_PATTERN);
            //日期解析 正确日期格式
            if(idDate!=null){
                custPo.setBirthday(birthDayFromId);
                String tempGender = idNo.substring(idNo.length() - 2, idNo.length() - 1);
                //0-女 1-男
                String genderFromIdNo = Integer.parseInt(tempGender) % 2 == 0 ? YesOrNoEnum.NO.getCode(): YesOrNoEnum.YES.getCode();
                custPo.setGender(genderFromIdNo);
            }
        }
    }


    /**
     *  若一级来源选择MGM-，二级来源选择成交客户介绍、非成交客户介绍时，"分享人投顾是否虚拟"取值为 来源备注对应客户的所属投顾对应"是否虚拟投顾"
     * @param custPo
     */
    private void fillIsVirtualShare(CmConscustPO custPo){
        String sourceCode=custPo.getNewsourceno();
        CmSourceInfoPO  sourcePo=custSourceRepository.querySourceByCode(sourceCode);
        log.info("根据客户来源编号：{} 获取来源信息：{}",sourceCode, JSON.toJSONString(sourcePo));

        // 若一级来源选择MGM-，二级来源选择成交客户介绍、非成交客户介绍时，"分享人投顾是否虚拟"取值为 来源备注对应客户的所属投顾对应"是否虚拟投顾"
        if (CustSourceFirstLevelEnum.B.getCode().equals(sourcePo.getFirstLevelCode()) &&
                (CustSourceSecondLevelEnum.DEAL_CUSTOMER_INTRODUCE.getCode().equals(sourcePo.getSecondLevelCode()) ||
                        CustSourceSecondLevelEnum.UNDEAL_CUSTOMER_INTRODUCE.getCode().equals(sourcePo.getSecondLevelCode()) )
           ) {
            // 备注: 实际为 客户号
            String remarkCoustNo = custPo.getCustsourceremark();
            if (StringUtils.isNotBlank(remarkCoustNo) && remarkCoustNo.length() == 10) {
                CmConsultantPO consultantPo=consultantRepository.selectConsultByCustNo(remarkCoustNo);
                if(consultantPo!=null){
                    //是否虚拟投顾（1：是，0：否）
                    custPo.setIsvirtualsharer(consultantPo.getIsvirtual());
                }
            }
        }
    }


    /**
     * 根据 客户对象 筛选 重复客户列表
     * @param custPo
     * @param excludeCustNo 排除 当前客户号
     * @return
     */
    private  List<RepeatCustInfo> getRepeatCustList(CmConscustPO custPo,String excludeCustNo) {
        ConsCustInvestTypeEnum investTypeEnum=ConsCustInvestTypeEnum.getEnum(custPo.getInvsttype());
        Assert.notNull(investTypeEnum,"客户类型不能为空！");

        String idType=custPo.getIdtype();
        String idSignAreaCode=custPo.getIdSignAreaCode();
        String idNoDigest=custPo.getIdnoDigest();
        String mobileAreaCode=custPo.getMobileAreaCode();
        String mobileDigest=custPo.getMobileDigest();
        String investType=custPo.getInvsttype();

        List<CmConscustForAnalyseBO> repeatList= Lists.newArrayList();
        //机构或产品产品
        if(investTypeEnum==ConsCustInvestTypeEnum.INSTITUTION || investTypeEnum==ConsCustInvestTypeEnum.PRODUCT){
            if(StringUtils.isNoneBlank(custPo.getIdnoDigest(),custPo.getIdtype(),custPo.getCustname())){
                //机构客户： 按照 证件类型、证件号码、姓名
                repeatList=
                        abnormalCustRepository.searchIdExistList(investType,idType,idSignAreaCode,idNoDigest,excludeCustNo)
                                        .stream().filter(po->custPo.getCustname().equals(po.getCustname()))
                                        .collect(Collectors.toList());
            }
        }else{
            //证件号与证件类型匹配联合查询，必须同时存在
            if(StringUtils.isNoneBlank(custPo.getIdnoDigest(),custPo.getIdtype())){
                repeatList.addAll(abnormalCustRepository.searchIdExistList(investType,idType,idSignAreaCode,idNoDigest,excludeCustNo));
            }
            //手机号查询重复数据，有效客户，并且个人类型，因为机构客户手机号大部分都相同
            if(StringUtils.isNoneBlank(mobileDigest,mobileAreaCode)){
                repeatList.addAll(abnormalCustRepository.searchMobileExistList(investType,mobileAreaCode,mobileDigest,excludeCustNo));
            }
        }
        //repeatList 去重， 且排除 参数conscustno
        repeatList= repeatList.stream()
                .filter(distinctByKey(CmConscustForAnalyseBO::getConscustno))
                .collect(Collectors.toList());

        List<RepeatCustInfo> returnList = Lists.newArrayList();
        repeatList.forEach(repeat->{
            RepeatCustInfo info=new RepeatCustInfo();
            info.setCustName(repeat.getCustname());
            info.setCustNo(repeat.getConscustno());
            returnList.add(info);
        });
        return returnList;
    }

    /**
     * 去重使用
     * @param keyExtractor
     * @param <T>
     * @return
     */
    public static <T> Predicate<T> distinctByKey(Function<? super T, Object> keyExtractor) {
        Map<Object, Boolean> map = new ConcurrentHashMap<>();
        return t -> map.putIfAbsent(keyExtractor.apply(t), Boolean.TRUE) == null;
    }


    /**
     * 加密
     * @param value
     * @return
     */
    private String getEncryptValue(String value){
        return  encyptAndDecyptOuterService.encrypt(value);
    }


    /**
     * @description:(更新客户 )
     * @param updateCustReq
     * @return com.howbuy.crm.account.client.response.Response<java.lang.String>
     * @author: haoran.zhang
     * @date: 2023/12/19 15:07
     * @since JDK 1.8
     */
    public Response<UpdateConsCustRespVO> updateCust(UpdateConsCustRequest updateCustReq){
        Assert.notNull(updateCustReq,"更新投顾客户信息参数不能为空");
        Assert.notNull(updateCustReq.getCustNo(),"更新投顾客户信息参数custNo不能为空");
        String custNo=updateCustReq.getCustNo();


        //校验-常规属性
        Response<String> validateResp= validateUpdate(updateCustReq);
        if(!validateResp.isSuccess()){
            return Response.fail(validateResp.getDescription());
        }

        UpdateConsCustReqVO updateVo=new UpdateConsCustReqVO();
        //待更新 客户号
        updateVo.setCustNo(custNo);

        //变更信息
        updateVo.setOperator(updateCustReq.getOperator());
        updateVo.setExplanation(updateCustReq.getExplanation());
        updateVo.setSpecialReason(updateCustReq.getSpecialReason());
        updateVo.setChecker(updateCustReq.getChecker());

        //客户常规信息
        CmConscustPO custPo=new CmConscustPO();
        custPo.setConscustno(custNo);
        //处理密文
        CmConscustCipherPO cipherPo=new CmConscustCipherPO();
        cipherPo.setConscustno(custNo);

        //request 对象属性 --> 客户po
        fillAttribute(updateCustReq,custPo,cipherPo);

        UpdateConsCustRespVO resp = new UpdateConsCustRespVO();
        //历史逻辑： 新增修改  操作记录记录一个 id  .与划转相关。
        String operationId = consCustInfoRepository.getCustOpreationId();
        resp.setOperationid(operationId);


        //查询 数据库原对象。 用于错误提示信息， 根据证件类型，覆写生日、性别
        CmConsCustSimpleBO existCustBo=consCustInfoRepository.queryCustSimpleInfo(custNo);
        //后续使用的 客户类型：
        String usedInvestType= StringUtil.isNotBlank(updateCustReq.getInvsttype())?updateCustReq.getInvsttype():existCustBo.getInvsttype();
        ConsCustInvestTypeEnum usedInvestTypeEnum=ConsCustInvestTypeEnum.getEnum(usedInvestType);
        if(usedInvestTypeEnum==null){
            return Response.fail("非法客户类型！");
        }
        custPo.setInvsttype(usedInvestTypeEnum.getCode());
        //校验重复
        //重复客户的校验
        List<RepeatCustInfo> repeatList=getRepeatCustList(custPo,custNo);
        if(!CollectionUtils.isEmpty(repeatList)){
            //构建 repository 保存参数
            CreateConsCustReqVO updateIcVo=new CreateConsCustReqVO();
            updateIcVo.setOperator(updateCustReq.getOperator());
            updateIcVo.setConsCode(custNo);
            updateIcVo.setCipherPO(cipherPo);
            updateIcVo.setConscustPO(custPo);

            //进入IC  划转 表
            ConsCustICReqVO icReqVo =new ConsCustICReqVO();
            icReqVo.setOperationId(operationId);
            icReqVo.setStatus(Constants.CUST_IC_OPERATION_UPDATE);
            icReqVo.setIcCheckFlag(Constants.IC_CHECK_FLAG_NO);
            icRepository.createOperation(updateIcVo,icReqVo);
            resp.setRepeatConsCustList(repeatList);
            //重复客户 进数据库 IC 表
            return  Response.fail(REPEAT_CUST,
                    String.format("存在重复%s客户", ConsCustInvestTypeEnum.PERSONAL==usedInvestTypeEnum?"":"机构或产品"),
                    resp);
        }


        String usedIdType=StringUtil.isNotBlank(updateCustReq.getIdtype())?updateCustReq.getIdtype():existCustBo.getIdtype();
         //历史逻辑。如果 idNo不为空， 且为个人客户类型 且 证件类型=身份证(18位)。   生日、性别 使用证件解析 覆写
        fillAgeByIdInfo(custPo,usedIdType,usedInvestTypeEnum,updateCustReq.getIdNo());


        //客户常规信息
        updateVo.setConscustPO(custPo);
        //处理明文
        updateVo.setCipherPO(cipherPo);
        consCustInfoRepository.updateConsCust(updateVo);
        return Response.ok();
    }


    /**
     * @description: 清除投顾客户的手机号
     *  ### 此方法为 兜底方法， 没有任何校验，相当于研发刷数据库开的一个口子
     *  ### 极端异常场景才会用这个方法，直接刷数据库
     * @param updateCustReq
     * @return java.lang.String
     * @author: jin.wang03
     * @date: 2024/1/9 19:04
     * @since JDK 1.8
     */
    public Response<String> removeCustMobileByCustNo(UpdateConsCustRequest updateCustReq) {
        String custNo = updateCustReq.getCustNo();

        UpdateConsCustReqVO updateVo = new UpdateConsCustReqVO();
        // 待更新 客户号
        updateVo.setCustNo(custNo);

        // 变更信息
        updateVo.setOperator(updateCustReq.getOperator());
        updateVo.setExplanation(updateCustReq.getExplanation());
        updateVo.setSpecialReason(updateCustReq.getSpecialReason());
        updateVo.setChecker(updateCustReq.getChecker());

        // 客户常规信息
        CmConscustPO custPo = new CmConscustPO();
        custPo.setConscustno(custNo);

        custPo.setMobileAreaCode("");
        custPo.setMobileMask("");
        custPo.setMobileDigest("");

        // 处理密文
        CmConscustCipherPO cipherPo = new CmConscustCipherPO();
        cipherPo.setConscustno(custNo);
        cipherPo.setMobileCipher("");


        // 客户常规信息
        updateVo.setConscustPO(custPo);
        // 处理明文
        updateVo.setCipherPO(cipherPo);
        try {
            consCustInfoRepository.updateConsCust(updateVo);
        } catch (Exception e) {
            log.error("清除投顾客户的手机号失败，custNo:{}", custNo, e);
            return Response.fail(e.getMessage());
        }

        return Response.ok();
    }

    /**
     * @description: 清除投顾客户的证件信息
     *  ### 此方法为 兜底方法， 没有任何校验，相当于研发刷数据库开的一个口子
     *  ### 极端异常场景才会用这个方法，直接刷数据库
     * @param updateCustReq 更新客户信息请求类
     * @return com.howbuy.crm.account.client.response.Response<java.lang.String> 清除指定投顾客户的证件信息响应类
     * @author: jin.wang03
     * @date: 2024/1/10 13:36
     * @since JDK 1.8
     */
    public Response<String> removeCustIdInfoByCustNo(UpdateConsCustRequest updateCustReq) {
        String custNo = updateCustReq.getCustNo();

        UpdateConsCustReqVO updateVo = new UpdateConsCustReqVO();
        // 待更新 客户号
        updateVo.setCustNo(custNo);

        // 变更信息
        updateVo.setOperator(updateCustReq.getOperator());
        updateVo.setExplanation(updateCustReq.getExplanation());
        updateVo.setSpecialReason(updateCustReq.getSpecialReason());
        updateVo.setChecker(updateCustReq.getChecker());

        // 客户常规信息
        CmConscustPO custPo = new CmConscustPO();
        custPo.setConscustno(custNo);

        custPo.setIdtype("");
        custPo.setIdnoDigest("");
        custPo.setIdnoMask("");
        custPo.setIdSignAreaCode("");
        custPo.setValidity("");
        custPo.setValiditydt("");

        // 处理密文
        CmConscustCipherPO cipherPo = new CmConscustCipherPO();
        cipherPo.setConscustno(custNo);
        cipherPo.setIdnoCipher("");


        // 客户常规信息
        updateVo.setConscustPO(custPo);
        // 处理明文
        updateVo.setCipherPO(cipherPo);
        try {
            consCustInfoRepository.updateConsCust(updateVo);
        } catch (Exception e) {
            log.error("清除投顾客户的证件信息失败，custNo:{}", custNo, e);
            return Response.fail(e.getMessage());
        }

        return Response.ok();
    }


    /**
     * @description:(请在此添加描述)
     * @param str	
     * @return java.lang.String
     * @author: haoran.zhang
     * @date: 2024/1/31 16:57
     * @since JDK 1.8
     */
    private static String trimToEmpty(String str){
        return str==null?"":str.trim();
    }

    /**
     * @description: 字段是否允许修改逻辑优化
     * 手机1&证件号码
     * a）校验当前投顾客户有无关联的【香港客户号】
     * 若未关联，则允许修改【手机1】和【证件号码】；若已关联，继续下面的步骤
     * b）      //20240103 更新：区号和手机号都不能修改
     * c）校验【香港客户号】绑定的【证件号】是否为空
     * 若不空，则不允许修改【用户名】、【客户类别】、【证件类型】、【证件号码】、【证件有限期】、【证件有效日期】字段
     * 否则，允许修改【用户名】、【客户类别】、【证件类型】、【证件号码】、【证件有限期】、【证件有效日期】字段
     * @param updateCustReq
     * @return com.howbuy.crm.account.client.response.Response<com.howbuy.crm.account.client.response.custinfo.UpdateConsCustRespVO>
     * @author: jin.wang03
     * @date: 2024/1/18 14:45
     * @since JDK 1.8
     */
    public Response<UpdateConsCustRespVO> checkCustUpdateField(UpdateConsCustRequest updateCustReq) {
        Assert.notNull(updateCustReq, "更新投顾客户信息参数不能为空");
        Assert.notNull(updateCustReq.getCustNo(), "更新投顾客户信息参数custNo不能为空");
        String custNo = updateCustReq.getCustNo();

        // 数据库原有对象
        CmConsCustVO dbExistVo = consCustInfoRepository.queryCustInfoByCustNo(custNo);

        // 若已关联【香港客户号】，区号和手机号都不能修改
        // 校验【香港客户号】绑定的【证件号】是否为空，则不允许修改【用户名】、【客户类别】、【证件类型】、【证件号码】、【证件有限期】、【证件有效日期】字段
        CmHkCustReqVO custReqVO = new CmHkCustReqVO();
        custReqVO.setConscustno(custNo);
        CmHkConscustPO cmHkConscustPO = hkCustRepository.selectByReqVO(custReqVO);
        if (cmHkConscustPO != null && StringUtils.isNotBlank(cmHkConscustPO.getHkTxAcctNo())) {
            if (updateCustReq.getMobile() != null && !DigestUtil.digest(updateCustReq.getMobile()).equals(dbExistVo.getMobileDigest())) {
                // 若已关联【香港客户号】，不允许修改【手机1】字段
                return Response.fail("已关联香港客户号，不允许修改手机1");
            }
            // 若已关联【香港客户号】，不允许修改【手机1区号】字段
            if (updateCustReq.getMobileAreaCode() != null && !updateCustReq.getMobileAreaCode().equals(dbExistVo.getMobileAreaCode())) {
                return Response.fail("已关联香港客户号，不允许修改手机1区号");
            }
            HkAcctCustDetailInfoVO hkAcctCustDetailInfoVO = hkCustInfoOuterService.queryHkCustDetailInfo(cmHkConscustPO.getHkTxAcctNo());
            if (hkAcctCustDetailInfoVO != null) {
                if (StringUtils.isNotBlank(hkAcctCustDetailInfoVO.getIdNoDigest())) {
                    // 则不允许修改【用户名】、【客户类别】、【证件类型】、【证件号码】、【证件有限期】、【证件有效日期】字段
                    if (updateCustReq.getCustname() != null && !updateCustReq.getCustname().equals(trimToEmpty(dbExistVo.getCustname()))) {
                        return Response.fail("已关联香港客户号，不允许修改用户名");
                    }
                    if (updateCustReq.getInvsttype() != null && !updateCustReq.getInvsttype().equals(trimToEmpty(dbExistVo.getInvsttype()))) {
                        return Response.fail("已关联香港客户号，不允许修改客户类别");
                    }
                    if (updateCustReq.getIdtype() != null && !updateCustReq.getIdtype().equals(trimToEmpty(dbExistVo.getIdtype()))) {
                        return Response.fail("已关联香港客户号，不允许修改证件类型");
                    }
                    if (updateCustReq.getIdNo() != null && !DigestUtil.digest(updateCustReq.getIdNo()).equals(trimToEmpty(dbExistVo.getIdnoDigest()))) {
                        return Response.fail("已关联香港客户号，不允许修改证件号码");
                    }
                    if (updateCustReq.getValidity() != null && !updateCustReq.getValidity().equals(trimToEmpty(dbExistVo.getValidity()))) {
                        return Response.fail("已关联香港客户号，不允许修改证件有限期");
                    }
                    if (updateCustReq.getValiditydt() != null && !updateCustReq.getValiditydt().equals(trimToEmpty(dbExistVo.getValiditydt()))) {
                        return Response.fail("已关联香港客户号，不允许修改证件有效日期");
                    }
                }
            }
        }

        // 若关联了一账通，且一账通绑定的【证件号】不为空，则不允许修改【用户名】、【客户类别】、【证件类型】、【证件号码】、【证件有限期】、【证件有效日期】字段
        if (StringUtils.isNotBlank(dbExistVo.getHboneNo())) {
            HboneAcctCustInfoVO hboneAcctCustInfoVO = hboneAcctInfoOuterService.queryHboneAcctInfo(dbExistVo.getHboneNo());
            if (hboneAcctCustInfoVO != null && StringUtils.isNotBlank(hboneAcctCustInfoVO.getIdNoDigest())) {

                if (updateCustReq.getCustname() != null && !updateCustReq.getCustname().equals(trimToEmpty(dbExistVo.getCustname()))) {
                    return Response.fail("已关联一账通，不允许修改用户名");
                }
                if (updateCustReq.getInvsttype() != null && !updateCustReq.getInvsttype().equals(trimToEmpty(dbExistVo.getInvsttype()))) {
                    return Response.fail("已关联一账通，不允许修改客户类别");
                }
                if (updateCustReq.getIdtype() != null && !updateCustReq.getIdtype().equals(trimToEmpty(dbExistVo.getIdtype()))) {
                    return Response.fail("已关联一账通，不允许修改证件类型");
                }
                if (updateCustReq.getIdNo() != null && !DigestUtil.digest(updateCustReq.getIdNo()).equals(trimToEmpty(dbExistVo.getIdnoDigest()))) {
                    return Response.fail("已关联一账通，不允许修改证件号码");
                }
                if (updateCustReq.getValidity() != null && !updateCustReq.getValidity().equals(trimToEmpty(dbExistVo.getValidity()))) {
                    return Response.fail("已关联一账通，不允许修改证件有限期");
                }
                if (updateCustReq.getValiditydt() != null && !updateCustReq.getValiditydt().equals(trimToEmpty(dbExistVo.getValiditydt()))) {
                    return Response.fail("已关联一账通，不允许修改证件有效日期");
                }

            }

        }

        return Response.ok();
    }

    /**
     * @description: 新创建客户写入到人员绩效系数表中
     * @param custNo 客户号
     * @param consCode 投顾编码
     * @param operator 操作人
     * <AUTHOR>
     * @date 2025-07-02 18:10:23
     * @since JDK 1.8
     */
    private void mergeConsPerformanceCoeffByCreateCustNo(String custNo, String consCode, String operator) {
        CmCustconstantPO custConstant = consCustInfoRepository.selectCustConsRelationByCustNo(custNo);
        if (custConstant != null) {
            MergeConsPerformanceCoeffRequest mergeRequest = new MergeConsPerformanceCoeffRequest();
            mergeRequest.setCustType(CUST_TYPE_CREATE); // 1:创建客户
            mergeRequest.setAssignTime(custConstant.getBinddate()); // 分配时间
            mergeRequest.setConsCustNo(custNo); // 投顾客户号
            mergeRequest.setConsCode(consCode); // 投顾code
            mergeRequest.setModifier(operator); // 修改人
            mergeRequest.setCalcType(CALC_TYPE_ADD);
            mergeConsPerformanceCoeffService.execute(mergeRequest);
        }
    }
}

