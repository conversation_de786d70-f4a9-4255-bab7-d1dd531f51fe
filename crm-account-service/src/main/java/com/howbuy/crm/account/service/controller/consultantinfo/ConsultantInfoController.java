/**
 * Copyright (c) 2023, <PERSON><PERSON><PERSON><PERSON> HOWBUY INVESTMENT MANAGEMENT Co., Ltd.
 * All right reserved.
 * <p>
 * THIS IS UNPUBLISHED PROPRIETARY SOURCE CODE OF HOWBUY INVESTMENT
 * MANAGEMENT CO., LTD.  THE CONTENTS OF THIS FILE MAY NOT BE DISCLOSED
 * TO THIRD PARTIES, COPIED OR DUPLICATED IN ANY FORM, IN WHOLE OR IN PART,
 * WITHOUT THE PRIOR WRITTEN PERMISSION OF HOWBUY INVESTMENT MANAGEMENT
 * CO., LTD.
 */
package com.howbuy.crm.account.service.controller.consultantinfo;

import com.howbuy.crm.account.client.request.consultantinfo.ConsultantWechatInfoRequest;
import com.howbuy.crm.account.client.request.custinfo.UpdateConsCustRequest;
import com.howbuy.crm.account.client.response.Response;
import com.howbuy.crm.account.client.response.beisen.CmBeisenOrgVO;
import com.howbuy.crm.account.client.response.consultantinfo.ConsultantWechatInfoRespVO;
import com.howbuy.crm.account.client.response.custinfo.UpdateConsCustRespVO;
import com.howbuy.crm.account.client.response.dictionary.CityListVO;
import com.howbuy.crm.account.client.response.dictionary.CountryInfoVO;
import com.howbuy.crm.account.client.response.dictionary.ParamMobileAreaListVO;
import com.howbuy.crm.account.service.service.consultantinfo.ConsultantInfoService;
import com.howbuy.crm.account.service.service.dictionary.DictionaryService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import java.util.List;

/**
 * @description: 投顾相关
 * <AUTHOR>
 * @date ********
 * @since JDK 1.8
 */

@RestController
@RequestMapping("/consultantinfo")
public class ConsultantInfoController {

    @Autowired
    private ConsultantInfoService consultantInfoService;


    /**
     * @api {GET} /consultantinfo/getallneedrefreshwechatconscode getAllNeedRefreshWechatConsCode()
     * @apiVersion 1.0.0
     * @apiGroup ConsultantInfoController
     * @apiName getAllNeedRefreshWechatConsCode()
     * @apiDescription 查询所有需要刷新客户关系的投顾企微账号
     * @apiSuccess (响应结果) {String} code 状态码
     * @apiSuccess (响应结果) {String} description 描述信息
     * @apiSuccess (响应结果) {Array} data 数据封装
     * @apiSuccessExample 响应结果示例
     * {"code":"0G","data":["725ntPr"],"description":"Td"}
     */
    @GetMapping("/getallneedrefreshwechatconscode")
    @ResponseBody
    public Response<List<String>> getAllNeedRefreshWechatConsCode() {
        return Response.ok(consultantInfoService.getAllNeedRefreshWechatConsCode());
    }


    /**
     * @api {POST} /consultantinfo/getwechatconscodesbyconscodes getWechatConscodesByConscodes()
     * @apiVersion 1.0.0
     * @apiGroup ConsultantInfoController
     * @apiName getWechatConscodesByConscodes()
     * @apiDescription 根据投顾codelist查询投顾企微账号
     * @apiParam (请求体) {Array} consCodeList 投顾consCodeList
     * @apiParamExample 请求体示例
     * {"consCodeList":["fz"]}
     * @apiSuccess (响应结果) {String} code 状态码
     * @apiSuccess (响应结果) {String} description 描述信息
     * @apiSuccess (响应结果) {Object} data 数据封装
     * @apiSuccess (响应结果) {Array} data.consultantWechatInfoList 投顾信息
     * @apiSuccess (响应结果) {String} data.consultantWechatInfoList.conscode 理财顾问代码
     * @apiSuccess (响应结果) {String} data.consultantWechatInfoList.wechatconscode 企业微信账号
     * @apiSuccessExample 响应结果示例
     * {"code":"JG","data":{"consultantWechatInfoList":[{"wechatconscode":"f7","conscode":"XJqfSN7"}]},"description":"NdOFTfU"}
     */
    @PostMapping("/getwechatconscodesbyconscodes")
    @ResponseBody
    public Response<ConsultantWechatInfoRespVO> getWechatConscodesByConscodes(@RequestBody ConsultantWechatInfoRequest req) {
        return Response.ok(consultantInfoService.getWechatConscodesByConscodes(req));
    }

}