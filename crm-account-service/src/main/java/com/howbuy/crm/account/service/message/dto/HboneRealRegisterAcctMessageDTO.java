/**
 * Copyright (c) 2023, <PERSON><PERSON><PERSON><PERSON> HOWBUY INVESTMENT MANAGEMENT Co., Ltd.
 * All right reserved.
 * <p>
 * THIS IS UNPUBLISHED PROPRIETARY SOURCE CODE OF HOWBUY INVESTMENT
 * MANAGEMENT CO., LTD.  THE CONTENTS OF THIS FILE MAY NOT BE DISCLOSED
 * TO THIRD PARTIES, COPIED OR DUPLICATED IN ANY FORM, IN WHOLE OR IN PART,
 * WITHOUT THE PRIOR WRITTEN PERMISSION OF HOWBUY INVESTMENT MANAGEMENT
 * CO., LTD.
 */
package com.howbuy.crm.account.service.message.dto;

import lombok.Data;

import java.io.Serializable;

/**
 * @description: (账户中心 一账通消息 [TOPIC_OPEN_ACC-一账通客户开户消息]-[REAL_REGISTER-实名开通一账通]   公共解析 dto)
 * <AUTHOR>
 * @date 2024年1月3日 16:04:03
 * @since JDK 1.8
 */
@Data
public class HboneRealRegisterAcctMessageDTO implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 一账通账号(hboneNo)
     */
    private String hboneNo;

    /**
     * 分销机构号(disCode)
     */
    private String disCode;

    /**
     * 客户姓名(custName)
     */
    private String custName;


    /**
     * 用户登录名(username)
      */
    private String username;

    /**
     *IP地址(ip)
     */
    private String ip;

    /**
     * 注册网点号(regOutletCode)
     */
    private String regOutletCode;

    /**
     * 注册时间(regDate)
     */
    private String regDate;

    /**
     *  用户类型(invstType)：个人-1，机构-0
     */
    private String invstType;


    /**
     * 证件类型(idType)
     */
    private String idType;

    /**
     * 证件号码摘要(idNoDigest)
     */
    private String idNoDigest;

    /**
     * 证件号码掩码(idNoMask)
     */
    private String idNoMask;


    /**
     * 证件号码密文(idNoCipher)
     */
    private String idNoCipher;

//    用户类型为机构时，手机号、摘要、掩码及验证状态为空

    /**
     * 手机号摘要(mobileDigest)
     */
    private String mobileDigest;

    /**
     * 手机号掩码(mobileMask)
     */
    private String mobileMask;



    /**
     * 手机号密文(mobileCipher)
     */
    private String mobileCipher;

    /**
     * 手机验证状态(mobileVerifyStatus)
     */
    private String mobileVerifyStatus;

    /**
     * 实名认证网点号(authOutletCode)
     */
    private String authOutletCode;

    /**
     * 实名认证时间(authDate)
     */
    private String authDate;

    /**
     * 实名认证卡号摘要(authCardDigest)
     */
    private String authCardDigest;

    /**
     * 实名认证卡号掩码(authCardMask)
     */
    private String authCardMask;

    /**
     * 实名认证卡号密文(authCardCipher)
     */
    private String authCardCipher;

}