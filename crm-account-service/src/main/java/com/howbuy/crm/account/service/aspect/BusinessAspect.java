/**
 * Copyright (c) 2023, <PERSON><PERSON><PERSON><PERSON> HOWBUY INVESTMENT MANAGEMENT Co., Ltd.
 * All right reserved.
 * <p>
 * THIS IS UNPUBLISHED PROPRIETARY SOURCE CODE OF HOWBUY INVESTMENT
 * MANAGEMENT CO., LTD.  THE CONTENTS OF THIS FILE MAY NOT BE DISCLOSED
 * TO THIRD PARTIES, COPIED OR DUPLICATED IN ANY FORM, IN WHOLE OR IN PART,
 * WITHOUT THE PRIOR WRITTEN PERMISSION OF HOWBUY INVESTMENT MANAGEMENT
 * CO., LTD.
 */
package com.howbuy.crm.account.service.aspect;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.howbuy.crm.account.client.request.Request;
import com.howbuy.crm.account.client.response.Response;
import com.howbuy.crm.account.service.commom.utils.LoggerUtils;
import com.howbuy.crm.account.service.commom.utils.MainLogUtils;
import net.sf.oval.Validator;
import org.apache.commons.lang3.StringUtils;
import org.apache.dubbo.rpc.RpcContext;
import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;
import org.aspectj.lang.ProceedingJoinPoint;
import org.aspectj.lang.Signature;
import org.aspectj.lang.annotation.Around;
import org.aspectj.lang.annotation.Aspect;
import org.aspectj.lang.annotation.Pointcut;
import org.springframework.stereotype.Component;
import org.springframework.web.context.request.RequestAttributes;
import org.springframework.web.context.request.RequestContextHolder;

import javax.servlet.http.HttpServletRequest;
import java.text.SimpleDateFormat;
import java.util.Date;
import java.util.Objects;
import java.util.UUID;

/**
 * @description: 日志页面业务处理
 * <AUTHOR>
 * @date 2023/11/13 17:59
 * @since JDK 1.8
 */
@Component
@Aspect
public class BusinessAspect {
    private static final Logger log = LogManager.getLogger(BusinessAspect.class);
    private static final Logger mainLogger = LogManager.getLogger("mainlog");
    private static final int  MAX_LOG_SIZE = 20000;

    @Pointcut("execution(* com.howbuy.crm.account.service.controller..*.*(..)) || execution(* com.howbuy.crm.account.service.facade..*.*(..))")
    public void entryPoint() {
    }

    @Around("entryPoint()")
    public Object around(ProceedingJoinPoint pjp) throws Throwable {
        long start = System.currentTimeMillis();
        LoggerUtils.setReqId(getReqId());
        LoggerUtils.setRanNo(LoggerUtils.createRanNo());

        // 获取方法参数
        Object[] args = pjp.getArgs();
        Signature signature = pjp.getSignature();
        Request request = null;
        Response response = null;
        String signatureStr = signature.toShortString();
        try {
            //  打印请求日志
            printRequestLog(signatureStr, args);
            // 获取请求参数
            if (args != null && args.length > 0 && args[0] instanceof Request) {
                request = (Request) args[0];
            }

            // 参数校验
            if (request != null) {
                Validator validator = new Validator();
                validator.assertValid(request);
            }

            response = (Response) pjp.proceed();
        } finally {
            // 打印响应日志
            printResponseLong(signatureStr, response);
            // 打印耗时日志
            long time = System.currentTimeMillis() - start;
            log.info("BusinessAspect|cost:{}", time);

            // 记录main.log
            printMainLog(response,signatureStr,time);
        }


        return response;
    }

    /**
     * @description: 打印main.log
     * @param
     * @return java.lang.String
     * @throws
     * @since JDK 1.8
     */
    private void printMainLog(Response response, String signatureStr, long time) {
        if (mainLogger.isInfoEnabled()) {
            try {
                String returnCode = response.getCode();
                String reqId = LoggerUtils.getReqId();
                String ranNo = LoggerUtils.getRanNo();
                if (MainLogUtils.isHttpRequest()) {
                    MainLogUtils.httpCallIn(reqId, ranNo, signatureStr, returnCode, time);
                } else {
                    MainLogUtils.dubboCallIn(reqId, ranNo, signatureStr, returnCode, time);
                }
            } catch (Exception e) {
                log.error("BusinessAspect printMianLog error.|methodName:{}", signatureStr);
                log.error("", e);
            }
        }
    }

    /**
     * @description: 获取当前请求的reqId
     * @param
     * @return java.lang.String
     * @author: hongdong.xie
     * @date: 2024/9/24 10:06
     * @since JDK 1.8
     */
    private String getHttpReqId(){
        RequestAttributes requestAttributes = RequestContextHolder.getRequestAttributes();
        String reqId = UUID.randomUUID().toString();
        if(Objects.isNull(requestAttributes)){
            return reqId;
        }
        //从获取RequestAttributes中获取HttpServletRequest的信息
        try {
            HttpServletRequest request = (HttpServletRequest) requestAttributes.resolveReference(RequestAttributes.REFERENCE_REQUEST);
            if(Objects.isNull(request)){
                return reqId;
            }
            String tradeId = request.getHeader(LoggerUtils.CTX_UUID);
            if (StringUtils.isNotEmpty(tradeId)) {
                reqId = tradeId;
            }
        } catch (Exception e) {
            log.warn("获取HttpServletRequest失败: {}", e.getMessage());
        }

        return reqId;
    }

    /**
     * @description: 获取reqId
     * @param
     * @return java.lang.String
     * @throws
     * @since JDK 1.8
     */
    private String getReqId() {
        // dubbo接口调用
        String reqId = (RpcContext.getServerAttachment()==null) ? null : RpcContext.getServerAttachment().getAttachment(LoggerUtils.CTX_UUID);
        if (StringUtils.isEmpty(reqId)) {
            // http接口调用
            reqId = getHttpReqId();
        }
        return reqId;
    }

    /**
     * @description: 打印响应日志
     * @param signatureStr 类名方法名
     * @param response 响应
     * @return void
     * @author: hongdong.xie
     * @date: 2023/7/23 17:18
     * @since JDK 1.8
     */
    private void printResponseLong(String signatureStr, Response response) {
        if (log.isInfoEnabled()) {
            try{
                String responseStr = JSON.toJSONString(response);
                if(responseStr.length() > MAX_LOG_SIZE){
                    responseStr = responseStr.substring(0, MAX_LOG_SIZE/2) + "..." + responseStr.substring(responseStr.length() - MAX_LOG_SIZE/2);
                }
                log.info("BusinessAspect|methodName:{},response:{}",signatureStr, responseStr);

            }catch (Exception e){
                log.error("BusinessAspect printResponseLong error.|methodName:{}" , signatureStr);
                log.error("",e);
            }
        }
    }

    /**
     * @description: 打印请求日志
     * @param signatureStr 类名方法名
     * @param args 参数
     * @return void
     * @author: hongdong.xie
     * @date: 2023/7/23 17:12
     * @since JDK 1.8
     */
    private void printRequestLog(String signatureStr, Object[] args) {
        if (log.isInfoEnabled()) {
            try{
                log.info("BusinessAspect|methodName:{},args:{}", signatureStr, JSON.toJSONString(args));
            }catch (Exception e){
                log.error("BusinessAspect printRequestLog error.|methodName:{}" ,signatureStr);
                log.error("",e);
            }
        }
    }

    /**
     * @param dateStr     时间
     * @param interfaName 接口名
     * @param returnCode  返回码
     * @param costTime    花费时间
     * @return java.lang.String
     * @description: main.log日志组装
     * @author: hongdong.xie
     * @date: 2023/3/15 13:15
     * @since JDK 1.8
     */
    private String convertMainLog(String dateStr, String interfaName, String returnCode, long costTime) {
        JSONObject json = new JSONObject();
        json.put("time", dateStr);
        json.put("tx_code", interfaName);
        json.put("return_code", returnCode);
        json.put("costs", costTime);

        return json.toJSONString();
    }

    /**
     * @return java.lang.String
     * @description:获取当前日期
     * @author: hongdong.xie
     * @date: 2023/3/15 13:32
     * @since JDK 1.8
     */
    private String getTimeStr() {
        return fmtDate(new Date(), "yyyy-MM-dd HH:mm:ss.SSS");
    }

    /**
     * @description: 日期格式化
     * @param date	 日期
     * @param style	 格式
     * @return java.lang.String
     * @author: hongdong.xie
     * @date: 2023/11/13 18:33
     * @since JDK 1.8
     */
    public String fmtDate(Date date, String style) {
        SimpleDateFormat dateFormat = new SimpleDateFormat(style);
        return dateFormat.format(date);
    }
}