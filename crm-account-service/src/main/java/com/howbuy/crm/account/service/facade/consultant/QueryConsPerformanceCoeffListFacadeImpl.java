/**
 * Copyright (c) 2024, <PERSON><PERSON><PERSON><PERSON> HOWBUY INVESTMENT MANAGEMENT Co., Ltd.
 * All right reserved.
 * <p>
 * THIS IS UNPUBLISHED PROPRIETARY SOURCE CODE OF HOWBUY INVESTMENT
 * MANAGEMENT CO., LTD.  THE CONTENTS OF THIS FILE MAY NOT BE DISCLOSED
 * TO THIRD PARTIES, COPIED OR DUPLICATED IN ANY FORM, IN WHOLE OR IN PART,
 * WITHOUT THE PRIOR WRITTEN PERMISSION OF HOWBUY INVESTMENT MANAGEMENT
 * CO., LTD.
 */
package com.howbuy.crm.account.service.facade.consultant;

import com.howbuy.crm.account.client.facade.consultant.QueryConsPerformanceCoeffListFacade;
import com.howbuy.crm.account.client.request.consultant.QueryConsPerformanceCoeffListRequest;
import com.howbuy.crm.account.client.response.Response;
import com.howbuy.crm.account.client.response.consultant.QueryConsPerformanceCoeffListResponse;
import com.howbuy.crm.account.service.service.consultant.QueryConsPerformanceCoeffListService;
import lombok.extern.slf4j.Slf4j;
import org.apache.dubbo.config.annotation.DubboService;

import javax.annotation.Resource;

/**
 * @description: 查询人员绩效系数列表接口实现
 * <AUTHOR>
 * @date 2025-07-11 18:52:00
 * @since JDK 1.8
 */
@Slf4j
@DubboService
public class QueryConsPerformanceCoeffListFacadeImpl implements QueryConsPerformanceCoeffListFacade {

    @Resource
    private QueryConsPerformanceCoeffListService queryConsPerformanceCoeffListService;

    /**
     * @api {DUBBO} com.howbuy.crm.account.client.facade.consultant.QueryConsPerformanceCoeffListFacade.execute()
     * @apiVersion 1.0.0
     * @apiGroup QueryConsPerformanceCoeffListFacade
     * @apiName execute()
     * @apiDescription 查询人员绩效系数列表
     * @apiParam (请求体) {String} deptCode 部门代码（必填，带数据广度权限）
     * @apiParam (请求体) {String} consCode 投顾code（必填，带数据广度权限）
     * @apiParam (请求体) {String} startAssignTime 分配开始时间（YYYYMMDD，可空）
     * @apiParam (请求体) {String} endAssignTime 分配结束时间（YYYYMMDD，可空）
     * @apiParam (请求体) {String} consCustNo 投顾客户号（可空）
     * @apiParam (请求体) {Integer} page 页号
     * @apiParam (请求体) {Integer} rows 每页大小
     * @apiParamExample 请求体示例
     * {"deptCode":"D001","consCode":"C001","startAssignTime":"********","endAssignTime":"********","consCustNo":"10001","page":1,"rows":20}
     * @apiSuccess (响应结果) {String} code 状态码
     * @apiSuccess (响应结果) {String} description 描述信息
     * @apiSuccess (响应结果) {Object} data 数据封装
     * @apiSuccess (响应结果) {Long} data.total 总记录数
     * @apiSuccess (响应结果) {Array} data.list 列表数据
     * @apiSuccessExample 响应结果示例
     * {"code":"0000","description":"成功","data":{"total":1,"list":[{"assignTime":"2024-12-19 10:30:00","consCustNo":"123456","custName":"张三","consCode":"CONS001","consName":"李四","centerCurrent":"北京中心","regionCurrent":"华北区域","regionViceCurrent":"王五","branchCurrent":"北京分公司","sourceType":"投顾资源（成交-划转）","commissionCoeffStart":"1.0","custConversionCoeff":1.0,"manageCoeffSubtotal":0.8,"manageCoeffRegionalsubtotal":0.6,"manageCoeffRegionaltotal":0.4,"cxb":"投顾资源","isBigV":"是","centerAssignTime":"北京中心","regionAssignTime":"华北区域","regionViceAssignTime":"王五","branchAssignTime":"北京分公司","updateUser":"chun.lin"}]}}
     * @param request 请求参数
     * @return com.howbuy.crm.account.client.response.Response<com.howbuy.crm.account.client.response.consultant.QueryConsPerformanceCoeffListResponse>
     * @description: 查询人员绩效系数列表
     * <AUTHOR>
     * @date 2025-07-11 18:52:00
     * @since JDK 1.8
     */
    @Override
    public Response<QueryConsPerformanceCoeffListResponse> execute(QueryConsPerformanceCoeffListRequest request) {
        log.info("查询人员绩效系数列表，请求参数：{}", request);
        try {
            QueryConsPerformanceCoeffListResponse result = queryConsPerformanceCoeffListService.queryList(request);
            log.info("查询人员绩效系数列表完成，总记录数：{}", result != null ? result.getTotal() : 0);
            return Response.ok(result);
        } catch (Exception e) {
            log.error("查询人员绩效系数列表异常", e);
            return Response.fail("查询人员绩效系数列表失败：" + e.getMessage());
        }
    }
} 