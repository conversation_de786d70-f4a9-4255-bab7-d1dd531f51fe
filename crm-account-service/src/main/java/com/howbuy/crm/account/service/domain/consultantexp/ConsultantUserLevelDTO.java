/**
 * Copyright (c) 2025, <PERSON><PERSON><PERSON><PERSON> HOWBUY INVESTMENT MANAGEMENT Co., Ltd.
 * All right reserved.
 * <p>
 * THIS IS UNPUBLISHED PROPRIETARY SOURCE CODE OF HOWBUY INVESTMENT
 * MANAGEMENT CO., LTD.  THE CONTENTS OF THIS FILE MAY NOT BE DISCLOSED
 * TO THIRD PARTIES, COPIED OR DUPLICATED IN ANY FORM, IN WHOLE OR IN PART,
 * WITHOUT THE PRIOR WRITTEN PERMISSION OF HOWBUY INVESTMENT MANAGEMENT
 * CO., LTD.
 */
package com.howbuy.crm.account.service.domain.consultantexp;

/**
 * @description: 投顾编码 和 花名册层级
 * <AUTHOR>
 * @date 2025/4/16 10:38
 * @since JDK 1.8
 */

public class ConsultantUserLevelDTO {

    /**
     * 投顾编码
     */
    private String constCode;

    /**
     * 投顾层级
     */
    private String userLevel;

    public String getConstCode() {
        return constCode;
    }

    public void setConstCode(String constCode) {
        this.constCode = constCode;
    }

    public String getUserLevel() {
        return userLevel;
    }

    public void setUserLevel(String userLevel) {
        this.userLevel = userLevel;
    }

    public ConsultantUserLevelDTO(String constCode, String userLevel) {
        this.constCode = constCode;
        this.userLevel = userLevel;
    }

    public ConsultantUserLevelDTO() {
    }
}