/**
 * Copyright (c) 2023, <PERSON><PERSON><PERSON><PERSON> HOWBUY INVESTMENT MANAGEMENT Co., Ltd.
 * All right reserved.
 * <p>
 * THIS IS UNPUBLISHED PROPRIETARY SOURCE CODE OF HOWBUY INVESTMENT
 * MANAGEMENT CO., LTD.  THE CONTENTS OF THIS FILE MAY NOT BE DISCLOSED
 * TO THIRD PARTIES, COPIED OR DUPLICATED IN ANY FORM, IN WHOLE OR IN PART,
 * WITHOUT THE PRIOR WRITTEN PERMISSION OF HOWBUY INVESTMENT MANAGEMENT
 * CO., LTD.
 */
package com.howbuy.crm.account.service.repository.custinfo;

import com.howbuy.crm.account.client.enums.YesOrNoEnum;
import com.howbuy.crm.account.dao.mapper.custinfo.CmConscustDeliveryAddressMapper;
import com.howbuy.crm.account.dao.po.custinfo.CmConscustDeliveryAddressPO;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.Date;
import java.util.List;

/**
 * @description: (投顾客户收货地址 repository)
 * <AUTHOR>
 * @date 2023/12/8 15:31
 * @since JDK 1.8
 */

@Slf4j
@Component
public class ConsCustDeliveryAddressRepository {

    @Autowired
    private CmConscustDeliveryAddressMapper cmConscustDeliveryAddressMapper;

    /**
     * @description: 保存投顾客户收货地址信息
     * @param addressPo 投顾客户收货地址信息
     * @author: jin.wang03
     * @date: 2024/4/3 17:46
     * @since JDK 1.8
     */
    public void insertCustDeliveryAddress(CmConscustDeliveryAddressPO addressPo) {
        addressPo.setRecStat(YesOrNoEnum.YES.getCode());
        addressPo.setCreateTimestamp(new Date());

        int insert = cmConscustDeliveryAddressMapper.insert(addressPo);
        log.info("insertCustDeliveryAddress insert result:{}", insert);
    }

    /**
     * @description: 更新投顾客户收货地址信息
     * @param addressPo 投顾客户收货地址信息
     * @author: jin.wang03
     * @date: 2024/4/8 9:45
     * @since JDK 1.8
     */
    public void updateCustDeliveryAddress(CmConscustDeliveryAddressPO addressPo) {
        addressPo.setModifyTimestamp(new Date());

        int update = cmConscustDeliveryAddressMapper.updateByPrimaryKeySelective(addressPo);
        log.info("insertCustDeliveryAddress insert result:{}", update);
    }

    /**
     * @description: 删除投顾客户收货地址信息
     * @param addressPo 投顾客户收货地址信息
     * @author: jin.wang03
     * @date: 2024/4/8 13:09
     * @since JDK 1.8
     */
    public void deleteDeliveryData(CmConscustDeliveryAddressPO addressPo) {
        addressPo.setRecStat(YesOrNoEnum.NO.getCode());
        addressPo.setModifyTimestamp(new Date());

        int update = cmConscustDeliveryAddressMapper.updateByPrimaryKeySelective(addressPo);
        log.info("deleteDeliveryData update result:{}", update);
    }

    /**
     * @description: 查询投顾客户收货地址信息
     * @param custNo 投顾客户号
     * @return java.util.List<com.howbuy.crm.account.dao.po.custinfo.CmConscustDeliveryAddressPO> 投顾客户收货地址信息列表
     * @author: jin.wang03
     * @date: 2024/4/7 16:12
     * @since JDK 1.8
     */
    public List<CmConscustDeliveryAddressPO> listCustDeliveryAddressByCustNo(String custNo) {
        return cmConscustDeliveryAddressMapper.listCustDeliveryAddressByCustNo(custNo);
    }

}