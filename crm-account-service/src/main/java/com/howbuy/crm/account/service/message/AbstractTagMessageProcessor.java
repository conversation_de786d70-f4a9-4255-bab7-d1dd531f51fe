/**
 * Copyright (c) 2023, <PERSON>gH<PERSON> HOWBUY INVESTMENT MANAGEMENT Co., Ltd.
 * All right reserved.
 * <p>
 * THIS IS UNPUBLISHED PROPRIETARY SOURCE CODE OF HOWBUY INVESTMENT
 * MANAGEMENT CO., LTD.  THE CONTENTS OF THIS FILE MAY NOT BE DISCLOSED
 * TO THIRD PARTIES, COPIED OR DUPLICATED IN ANY FORM, IN WHOLE OR IN PART,
 * WITHOUT THE PRIOR WRITTEN PERMISSION OF HOWBUY INVESTMENT MANAGEMENT
 * CO., LTD.
 */
package com.howbuy.crm.account.service.message;

import com.howbuy.message.MessageService;
import lombok.extern.slf4j.Slf4j;

import java.util.List;

/**
 * <pre>
 * howbuy-message：
 * RocketMQ消费者组：
 *      - 不指定Tag的情况下，consumerGroupName为 Topic对应配置中groupName的值
 *      - 指定Tag
 *          - 多个Tag，用双竖线 || 分隔
 *          - consumerGroupName为：groupName_toLowerCase(tag||tag)_G
 * </pre>
 *
 * <AUTHOR>
 */
@Slf4j
public abstract class AbstractTagMessageProcessor extends AbstractBusinessMessageProcessor {

    /**
     * 调用  addMessageProcessor  形式：
     * 多个 TAG，用双竖杠隔开<p>
     * 例：tag1||tag2
     */
    protected   String getTag(){
        return String.join("||",getSupportTagList());
    }

    /**
     * 支持多个 多个 TAG
     */
    public abstract List<String> getSupportTagList();




    @Override
    public void init() {
        log.info("添加MessageProcessor：channel：{}, tag：{}, processor：{}", getQuartMessageChannel(), getTag(), this);
        MessageService.getInstance().addMessageProcessor(getQuartMessageChannel(), getTag(), this);
    }

}