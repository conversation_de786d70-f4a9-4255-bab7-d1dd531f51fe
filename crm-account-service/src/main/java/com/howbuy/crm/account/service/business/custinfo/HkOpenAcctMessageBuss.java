/**
 * Copyright (c) 2025, <PERSON><PERSON><PERSON><PERSON> HOWBUY INVESTMENT MANAGEMENT Co., Ltd.
 * All right reserved.
 * <p>
 * THIS IS UNPUBLISHED PROPRIETARY SOURCE CODE OF HOWBUY INVESTMENT
 * MANAGEMENT CO., LTD.  THE CONTENTS OF THIS FILE MAY NOT BE DISCLOSED
 * TO THIRD PARTIES, COPIED OR DUPLICATED IN ANY FORM, IN WHOLE OR IN PART,
 * WITHOUT THE PRIOR WRITTEN PERMISSION OF HOWBUY INVESTMENT MANAGEMENT
 * CO., LTD.
 */
package com.howbuy.crm.account.service.business.custinfo;

import com.howbuy.common.utils.StringUtil;
import com.howbuy.crm.account.client.enums.NotifyMsgTypeEnum;
import com.howbuy.crm.account.client.enums.custinfo.CustSourceCodeEnum;
import com.howbuy.crm.account.client.enums.custinfo.FullCustSourceEnum;
import com.howbuy.crm.account.client.response.Response;
import com.howbuy.crm.account.dao.bo.custinfo.CmConscustForAnalyseBO;
import com.howbuy.crm.account.service.commom.constant.NofityMsgConstans;
import com.howbuy.crm.account.service.message.dto.NofifyMsgDTO;
import com.howbuy.crm.account.service.message.notify.PushNotifyMsgService;
import com.howbuy.crm.account.service.repository.custinfo.AbnormalCustRepository;
import com.howbuy.crm.account.service.vo.custinfo.AbnormalAnalyseResultVO;
import com.howbuy.crm.account.service.vo.custinfo.HkMessageCustInfoVO;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.HashMap;
import java.util.Map;
import java.util.Objects;

/**
 * <AUTHOR>
 * @description: (请在此添加描述)
 * @date 2025/5/16 17:56
 * @since JDK 1.8
 */

@Slf4j
@Component
public class HkOpenAcctMessageBuss {


    @Autowired
    private CmCustProcessBusinessService processBusinessService;


    @Autowired
    private AbnormalCustRepository abnormalCustRepository;

    @Autowired
    private PushNotifyMsgService pushNotifyMsgService;


    public Response<String> processHkMessage(AbnormalAnalyseResultVO<HkMessageCustInfoVO> resultVo, CustSourceCodeEnum custSourceCodeEnum) {
        CmConscustForAnalyseBO processedCustInfo = resultVo.getProcessedCustInfo();
        HkMessageCustInfoVO analyseVo = resultVo.getAnalyseVo();
        String hkTxAcctNo = analyseVo.getHkTxAcctNo();
        String hboneNo = analyseVo.getHboneNo(); // 获取一账通号

        log.info("【{} V3】开始执行业务处理逻辑，香港客户号：{}, 涉及投顾客户信息：{}",
                FullCustSourceEnum.getDescription(analyseVo.getAbnormalSource()), hkTxAcctNo, processedCustInfo);

        // 场景 D1111: CRM创建客户，并绑定香港客户号A，并绑定一账通号A
        if (processedCustInfo == null) {
            log.info("【{} V3】处理场景：需要创建新客户。香港客户号：{}", FullCustSourceEnum.getDescription(analyseVo.getAbnormalSource()), hkTxAcctNo);
            // 新增客户
            Response<String> createResp = processBusinessService.createCustInfoByHk(
                    hkTxAcctNo,
                    analyseVo.getAbnormalSource(), // 使用分析VO中的来源 Code
                    analyseVo.getOperateChannel(), // 使用分析VO中的渠道
                    custSourceCodeEnum.getCode() // 香港线上开户来源编码
            );

            String custNo = createResp.getData();
            if (StringUtil.isEmpty(custNo)) {
                log.error("【{} V3】创建客户失败，香港客户号：{}, 失败原因：{}",
                        FullCustSourceEnum.getDescription(analyseVo.getAbnormalSource()),
                        hkTxAcctNo,
                        createResp.getDescription());
                return Response.fail(createResp.getDescription());
            }
            log.info("【{} V3】创建客户成功，投顾客户号：{}, 香港客户号：{}",
                    FullCustSourceEnum.getDescription(analyseVo.getAbnormalSource()), custNo, hkTxAcctNo);
            // 新增客户后需要重新获取客户信息用于后续处理
            processedCustInfo = abnormalCustRepository.queryCustBOByCustNo(custNo);
            if (processedCustInfo == null) {
                log.error("【{} V3】创建客户后未能查询到客户信息，投顾客户号：{}", FullCustSourceEnum.getDescription(analyseVo.getAbnormalSource()), custNo);
                return Response.fail("创建客户后未能查询到客户信息");
            }

            // 绑定香港客户号A (因为是新客户，必定需要绑定)
            processBusinessService.associateHkAcctRelation(processedCustInfo, analyseVo);
            log.info("【{} V3】新客户绑定香港客户号完成，投顾客户号：{}, 香港客户号：{}",
                    FullCustSourceEnum.getDescription(analyseVo.getAbnormalSource()), custNo, hkTxAcctNo);

            // 绑定一账通号A (如果消息中存在)
            if (StringUtil.isNotBlank(hboneNo)) {
                // 注意: V3分析流程中 D1111 场景意味着一账通也未关联，所以这里执行绑定是合理的
                processBusinessService.processExtraHboneAccount(analyseVo);
                log.info("【{} V3】新客户绑定一账通号完成，投顾客户号：{}, 一账通号：{}",
                        FullCustSourceEnum.getDescription(analyseVo.getAbnormalSource()), custNo, hboneNo);
            }

        } else {
            // 处理分析服务识别出的已有客户 (对应场景 A111, E111, H1111)
            String custNo = processedCustInfo.getConscustno();
            log.info("【{} V3】处理场景：更新已有客户。投顾客户号：{}, 香港客户号：{}",
                    FullCustSourceEnum.getDescription(analyseVo.getAbnormalSource()), custNo, hkTxAcctNo);

            // 香港开户信息更新CRM信息 (公共逻辑)
            processBusinessService.updateCustInfoByHk(analyseVo, processedCustInfo);
            log.info("【{} V3】更新CRM信息完成，投顾客户号：{}", FullCustSourceEnum.getDescription(analyseVo.getAbnormalSource()), custNo);

            // 处理绑定关系 (根据不同场景的TODO)
            // 场景 A111: 香港开户信息更新CRM信息 (已处理)，需要绑定一账通 (如果原客户没有且消息中有)
            // 场景 E111: 投顾客户号A与香港客户号A绑定, 投顾客户号A与一账通A绑定, 香港开户信息更新CRM信息 (已处理)
            // 场景 H1111: 投顾客户号A与香港客户号A绑定, 香港开户信息更新CRM信息 (已处理)

            // 统一处理香港客户号绑定：如果该客户当前未绑定香港号 (V3分析逻辑已保证在 E111 和 H1111 路径下未绑定其他号)
            if (StringUtil.isBlank(processedCustInfo.getHkTxAcctNo())) {
                processBusinessService.associateHkAcctRelation(processedCustInfo, analyseVo);
                log.info("【{} V3】绑定香港客户号完成，投顾客户号：{}, 香港客户号：{}",
                        FullCustSourceEnum.getDescription(analyseVo.getAbnormalSource()), custNo, hkTxAcctNo);
            } else if (!Objects.equals(processedCustInfo.getHkTxAcctNo(), hkTxAcctNo)) {
                // 按理说 V3 流程不会走到这里，但加个日志以防万一
                log.warn("【{} V3】警告：处理已有客户时发现其已绑定不同香港号 {}，但分析流程允许通过？投顾客户号：{}, 消息香港号：{}",
                        FullCustSourceEnum.getDescription(analyseVo.getAbnormalSource()),
                        processedCustInfo.getHkTxAcctNo(), custNo, hkTxAcctNo);
            }

            // 统一处理一账通绑定：如果该客户当前未绑定一账通，且消息中存在一账通号 (对应场景 A111 和 E111)
            if (StringUtil.isBlank(processedCustInfo.getHboneNo()) && StringUtil.isNotBlank(hboneNo)) {
                processBusinessService.processExtraHboneAccount(analyseVo);
                log.info("【{} V3】绑定一账通号完成，投顾客户号：{}, 一账通号：{}",
                        FullCustSourceEnum.getDescription(analyseVo.getAbnormalSource()), custNo, hboneNo);
            }
        }

        // --- 公共后续处理逻辑 ---
        if (processedCustInfo == null) {
            log.error("【{} V3】处理结束时 processedCustInfo 仍为 null，流程异常中止。香港客户号：{}", FullCustSourceEnum.getDescription(analyseVo.getAbnormalSource()), hkTxAcctNo);
            return Response.fail("内部处理错误，未能确定最终客户信息");
        }

        // 生成呼出任务
        processBusinessService.insertBookingCust(analyseVo, hkTxAcctNo);
        log.info("【{} V3】生成呼出任务完成，香港客户号：{}", FullCustSourceEnum.getDescription(analyseVo.getAbnormalSource()), hkTxAcctNo);

        // 发送开户成功通知信息
        NofifyMsgDTO nofifyMsgDto = new NofifyMsgDTO();
        nofifyMsgDto.setBusinessId(NofityMsgConstans.HK_ONLINE_OPEN_ACCT_SUCCESS);
        nofifyMsgDto.setNotifyType(NotifyMsgTypeEnum.HK_TX_ACCT_NO.getCode());
        nofifyMsgDto.setHkTxAcctNo(hkTxAcctNo);

        // 模板ID：201483 .  【香港开户成功】客户：${custName}（${custNo}）开户申请成功，请及时关注
        Map<String, String> paramMap = new HashMap<>();
        paramMap.put("custName", processedCustInfo.getCustname());
        paramMap.put("custNo", processedCustInfo.getConscustno());
        nofifyMsgDto.setParamMap(paramMap);
        pushNotifyMsgService.pushNofityMsg(nofifyMsgDto);
        log.info("【{} V3】发送开户成功通知完成，香港客户号：{}", FullCustSourceEnum.getDescription(analyseVo.getAbnormalSource()), hkTxAcctNo);

        log.info("【{} V3】消息处理成功完成，香港客户号：{}", FullCustSourceEnum.getDescription(analyseVo.getAbnormalSource()), hkTxAcctNo);
        return Response.ok();
    }

}