/**
 * Copyright (c) 2023, <PERSON><PERSON><PERSON><PERSON> HOWBUY INVESTMENT MANAGEMENT Co., Ltd.
 * All right reserved.
 * <p>
 * THIS IS UNPUBLISHED PROPRIETARY SOURCE CODE OF HOWBUY INVESTMENT
 * MANAGEMENT CO., LTD.  THE CONTENTS OF THIS FILE MAY NOT BE DISCLOSED
 * TO THIRD PARTIES, COPIED OR DUPLICATED IN ANY FORM, IN WHOLE OR IN PART,
 * WITHOUT THE PRIOR WRITTEN PERMISSION OF HOWBUY INVESTMENT MANAGEMENT
 * CO., LTD.
 */
package com.howbuy.crm.account.service.message.hkcustinfo;

import com.howbuy.crm.account.client.enums.custinfo.CustSourceCodeEnum;
import com.howbuy.crm.account.client.enums.custinfo.FullCustSourceEnum;
import com.howbuy.crm.account.client.response.Response;
import com.howbuy.crm.account.service.business.custinfo.HkOpenAcctMessageBuss;
import com.howbuy.crm.account.service.message.dto.HkOpenAcctMessageDTO;
import com.howbuy.crm.account.service.vo.custinfo.AbnormalAnalyseResultVO;
import com.howbuy.crm.account.service.vo.custinfo.HkMessageCustInfoVO;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;

import java.util.Collections;
import java.util.List;

/**
 * <AUTHOR>
 * @description: ([香港账户] - [香港线下开户] 消息处理器[topic.hk.openAcct])
 * @date 2023/12/11 17:04
 * @since JDK 1.8
 */
@Slf4j
@Service
public class HkOfflineOpenAcctMessageProcessor extends AbstractHkMessageProcessor<HkOpenAcctMessageDTO> {

    /**
     * [TOPIC_HK_OPEN_ACCT]  香港线上开户
     */
    @Value("${topic.hk.openAcct}")
    private String topicHkOpenAcct;


    /**
     * [OFFLINE_OPEN_ACCT]
     */
    @Value("${tag.hk.offlineOpenAcct}")
    private String tagName;


    @Autowired
    private HkOpenAcctMessageBuss hkOpenAcctMessageBuss;


    @Override
    public String getQuartMessageChannel() {
        return topicHkOpenAcct;
    }

    @Override
    public List<String> getSupportTagList() {
        return Collections.singletonList(tagName);
    }

    @Override
    HkMessageCustInfoVO constructByMessage(HkOpenAcctMessageDTO acctMsgDto) {
        HkMessageCustInfoVO custVo = new HkMessageCustInfoVO();
        custVo.setHkTxAcctNo(acctMsgDto.getHkCustNo());
        custVo.setOpenOutletCode(acctMsgDto.getOpenOutletCode());

        fillHkAcctInfo(custVo);
        return custVo;
    }

    @Override
    public FullCustSourceEnum getCustSource() {
        return FullCustSourceEnum.HK_OFFLINE_OPEN_ACCOUNT;
    }

    @Override
    public Response<String> processHkMessage(AbnormalAnalyseResultVO<HkMessageCustInfoVO> resultVo) {
        return hkOpenAcctMessageBuss.processHkMessage(resultVo, CustSourceCodeEnum.HOWBUY_HK_COUNTER_OPEN);
    }


}