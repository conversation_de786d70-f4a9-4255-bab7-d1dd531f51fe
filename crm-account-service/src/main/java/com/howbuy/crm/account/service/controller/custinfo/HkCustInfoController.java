/**
 * Copyright (c) 2023, <PERSON><PERSON><PERSON><PERSON> HOWBUY INVESTMENT MANAGEMENT Co., Ltd.
 * All right reserved.
 * <p>
 * THIS IS UNPUBLISHED PROPRIETARY SOURCE CODE OF HOWBUY INVESTMENT
 * MANAGEMENT CO., LTD.  THE CONTENTS OF THIS FILE MAY NOT BE DISCLOSED
 * TO THIRD PARTIES, COPIED OR DUPLICATED IN ANY FORM, IN WHOLE OR IN PART,
 * WITHOUT THE PRIOR WRITTEN PERMISSION OF HOWBUY INVESTMENT MANAGEMENT
 * CO., LTD.
 */
package com.howbuy.crm.account.service.controller.custinfo;

import com.howbuy.crm.account.client.enums.custinfo.HkAcctCustStatusEnum;
import com.howbuy.crm.account.client.request.custinfo.*;
import com.howbuy.crm.account.client.request.deposit.HkDepositRequest;
import com.howbuy.crm.account.client.response.PageVO;
import com.howbuy.crm.account.client.response.Response;
import com.howbuy.crm.account.client.response.bankcardinfo.HkBankCardInfoVO;
import com.howbuy.crm.account.client.response.custinfo.*;
import com.howbuy.crm.account.client.response.deposit.HkDepositResultVO;
import com.howbuy.crm.account.service.service.custinfo.HkCustInfoService;
import net.sf.oval.guard.Post;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import java.util.List;

/**
 * @description: (香港客户信息接口)
 * <AUTHOR>
 * @date 2023/12/13 16:08
 * @since JDK 1.8
 */
@RestController
@RequestMapping("/hkcustinfo")
public class HkCustInfoController {

    @Autowired
    private HkCustInfoService hkCustInfoService;


    /**
     * @api {POST} /hkcustinfo/queryhkcustinfopage queryHkCustInfoPage()
     * @apiVersion 1.0.0
     * @apiGroup HkCustInfoController
     * @apiName queryHkCustInfoPage()
     * @apiDescription 分页查询香港账户中心香港客户信息列表
     * @apiParam (请求体) {String} hkCustNo 香港客户号
     * @apiParam (请求体) {String} mobileAreaCode 手机 区号
     * @apiParam (请求体) {String} mobileDigest 手机号
     * @apiParam (请求体) {String} emailDigest 邮箱
     * @apiParam (请求体) {String} idNoDigest 证件号
     * @apiParam (请求体) {String} hboneNo 一账通号
     * @apiParam (请求体) {String} custCnOrEnName 客户中文或英文名称
     * @apiParam (请求体) {String} invstType 投资者类型
     * @apiParam (请求体) {String} ebrokerId ebrokerId
     * @apiParam (请求体) {Array} custStatList 客户状态 列表      {@link com.howbuy.crm.account.client.enums.custinfo.HkAcctCustStatusEnum}
     * @apiParam (请求体) {Number} page 页号
     * @apiParam (请求体) {Number} size 每页大小
     * @apiParamExample 请求体示例
     * {"mobileAreaCode":"ZIY4","size":1045,"invstType":"AfqbdPB","hkCustNo":"DM","mobileDigest":"4xIpvwp2V1","emailDigest":"4ObMVo2","idNoDigest":"4r7g","page":5466,"hboneNo":"0ZdXah9m3","custCnOrEnName":"nbRpjqFe0X","ebrokerId":"R5l9","custStatList":["ws3"]}
     * @apiSuccess (响应结果) {String} code 状态码
     * @apiSuccess (响应结果) {String} description 描述信息
     * @apiSuccess (响应结果) {Object} data 数据封装
     * @apiSuccess (响应结果) {Number} data.page 当前第几页
     * @apiSuccess (响应结果) {Number} data.size 单页条数
     * @apiSuccess (响应结果) {Number} data.total 总条数
     * @apiSuccess (响应结果) {Array} data.rows 数据对象列表
     * @apiSuccess (响应结果) {String} data.rows.custNo CRM 客户号
     * @apiSuccess (响应结果) {String} data.rows.hkTxAcctNo 香港交易账号
     * @apiSuccess (响应结果) {String} data.rows.ebrokerId ebrokerId
     * @apiSuccess (响应结果) {String} data.rows.custChineseName 香港客户中文名
     * @apiSuccess (响应结果) {String} data.rows.custEnName 香港客户英文名
     * @apiSuccess (响应结果) {String} data.rows.hkCustStatus {@link com.howbuy.crm.account.client.enums.custinfo.HkAcctCustStatusEnum}      香港客户状态-   客户状态 0-正常 1-注销 2-休眠 3-注册4-开户申请成功
     * @apiSuccess (响应结果) {String} data.rows.birthDt 出生日期
     * @apiSuccess (响应结果) {String} data.rows.idNoDigest 证件号码
     * @apiSuccess (响应结果) {String} data.rows.idNoMask 证件号码
     * @apiSuccess (响应结果) {String} data.rows.idType 证件类型
     * @apiSuccess (响应结果) {String} data.rows.invstType 投资者类型 0-机构,1-个人,2-产品户
     * @apiSuccess (响应结果) {String} data.rows.mobileAreaCode 手机号码区号
     * @apiSuccess (响应结果) {String} data.rows.mobileDigest 手机号码
     * @apiSuccess (响应结果) {String} data.rows.mobileMask 手机号码
     * @apiSuccess (响应结果) {String} data.rows.mobileVerifyStatus 手机号码 验证状态      0:未验证 1:已验证
     * @apiSuccess (响应结果) {String} data.rows.emailDigest 邮件
     * @apiSuccess (响应结果) {String} data.rows.emailMask 邮件
     * @apiSuccess (响应结果) {String} data.rows.emailVerifyStatus 邮件 验证状态      0:未验证 1:已验证
     * @apiSuccessExample 响应结果示例
     * {"code":"8u5JMnNt","data":{"total":3017,"size":9942,"page":7569,"rows":[{"custNo":"cBO2","birthDt":"7Q","idType":"FgJxkQb","hkCustStatus":"2kXGgbhtG","mobileVerifyStatus":"n","idNoDigest":"I","emailDigest":"U","emailVerifyStatus":"eVt","idNoMask":"09jY4Gxfj","ebrokerId":"zwgTHN","hkTxAcctNo":"NdAF4","mobileAreaCode":"GOknx5gVOj","invstType":"q","mobileDigest":"wX","emailMask":"9","custChineseName":"O","custEnName":"JDeI3856v","mobileMask":"VI5SktwP"}]},"description":"LvkL8vGZ5"}
     */
    @PostMapping("/queryhkcustinfopage")
    @ResponseBody
    public Response<PageVO<HkAcctCustInfoVO>> queryHkCustInfoPage(@RequestBody HkAcctCustInfoRequest  hkCustInfoRequest){
        return Response.ok(hkCustInfoService.queryHkCustInfoPage(hkCustInfoRequest));
    }


    /**
     * @api {GET} /hkcustinfo/queryhkcustinfo queryHkCustInfo()
     * @apiVersion 1.0.0
     * @apiGroup HkCustInfoController
     * @apiName queryHkCustInfo()
     * @apiDescription 根据香港交易账号查询香港账户中心香港客户信息
     * @apiParam (请求参数) {String} hkTxAcctNo
     * @apiParamExample 请求参数示例
     * hkTxAcctNo=d5VlVRI
     * @apiSuccess (响应结果) {String} code 状态码
     * @apiSuccess (响应结果) {String} description 描述信息
     * @apiSuccess (响应结果) {Object} data 数据封装
     * @apiSuccess (响应结果) {String} data.custNo CRM 客户号
     * @apiSuccess (响应结果) {String} data.hkTxAcctNo 香港交易账号
     * @apiSuccess (响应结果) {String} data.ebrokerId ebrokerId
     * @apiSuccess (响应结果) {String} data.custChineseName 香港客户中文名
     * @apiSuccess (响应结果) {String} data.custEnName 香港客户英文名
     * @apiSuccess (响应结果) {String} data.hkCustStatus {@link HkAcctCustStatusEnum}      香港客户状态-   客户状态 0-正常 1-注销 2-休眠 3-注册4-开户申请成功
     * @apiSuccess (响应结果) {String} data.birthDt 出生日期
     * @apiSuccess (响应结果) {String} data.idNoDigest 证件号码
     * @apiSuccess (响应结果) {String} data.idNoMask 证件号码
     * @apiSuccess (响应结果) {String} data.idSignAreaCode 证件地区码-身份证签发地区编码
     * @apiSuccess (响应结果) {String} data.idType 证件类型
     * @apiSuccess (响应结果) {String} data.invstType 投资者类型 0-机构,1-个人,2-产品户
     * @apiSuccess (响应结果) {String} data.mobileAreaCode 手机号码区号
     * @apiSuccess (响应结果) {String} data.mobileDigest 手机号码
     * @apiSuccess (响应结果) {String} data.mobileMask 手机号码
     * @apiSuccess (响应结果) {String} data.mobileVerifyStatus 手机号码 验证状态      0:未验证 1:已验证
     * @apiSuccess (响应结果) {String} data.emailDigest 邮件
     * @apiSuccess (响应结果) {String} data.emailMask 邮件
     * @apiSuccess (响应结果) {String} data.emailVerifyStatus 邮件 验证状态      0:未验证 1:已验证
     * @apiSuccess (响应结果) {String} data.hboneNo 关联的一账通号
     * @apiSuccess (响应结果) {String} data.idValidityEnd 证件有效期
     * @apiSuccess (响应结果) {String} data.idAlwaysValidFlag 证件是否长期有效 0-否 1-是
     * @apiSuccess (响应结果) {String} data.idImageUploadStatus 证件图片上传状态0-未上传 1-已上传
     * @apiSuccess (响应结果) {String} data.nationality 国籍
     * @apiSuccess (响应结果) {String} data.birthday 出生日期
     * @apiSuccess (响应结果) {String} data.gender 性别 0-女，1-男，2-非自然人
     * @apiSuccess (响应结果) {String} data.vocation 职业
     * @apiSuccess (响应结果) {String} data.custLoginPasswdType 客户登录密码状态0正常状态 1重置状态 2-未设置
     * @apiSuccess (响应结果) {String} data.custTxPasswdType 客户交易密码状态0正常状态 1重置状态 2-未设置
     * @apiSuccess (响应结果) {String} data.riskToleranceLevel 风险承受能力评估级别 0,1,2,3,4,5
     * @apiSuccess (响应结果) {String} data.derivativeKnowledge 衍生金融工具知识0-无 1-有
     * @apiSuccess (响应结果) {String} data.riskToleranceDate 风险评测日期	['yyyyMMdd']
     * @apiSuccess (响应结果) {String} data.riskToleranceTerm 风险评测有效期	['yyyyMMdd']
     * @apiSuccess (响应结果) {String} data.riskToleranceScore 风险评测分数
     * @apiSuccess (响应结果) {String} data.investorQualification 投资者资质PRO-投资者资质专业/NORMAL-投资者资质普通
     * @apiSuccess (响应结果) {String} data.investorQualificationDate 投资者资质认证日期
     * @apiSuccess (响应结果) {String} data.residenceCountryCode 现住址国家编码
     * @apiSuccess (响应结果) {String} data.residenceProvCode 现住址省份编码
     * @apiSuccess (响应结果) {String} data.residenceCityCode 现住址城市编码
     * @apiSuccess (响应结果) {String} data.residenceCountyCode 现住址地区编码
     * @apiSuccess (响应结果) {String} data.residenceCnAddrDigest 现住址的中文地址-摘要
     * @apiSuccess (响应结果) {String} data.residenceCnAddrMask 现住址的中文地址-掩码
     * @apiSuccess (响应结果) {String} data.residenceEnCountry 现住址英文国家名称
     * @apiSuccess (响应结果) {String} data.residenceEnProv 现住址英文省份名称
     * @apiSuccess (响应结果) {String} data.residenceEnCity 现住址英文城市名称
     * @apiSuccess (响应结果) {String} data.residenceEnCounty 现住址英文地区名称
     * @apiSuccess (响应结果) {String} data.residenceTown 现住址城镇名称
     * @apiSuccess (响应结果) {String} data.residenceState 现住址州县名称
     * @apiSuccess (响应结果) {String} data.residenceEnAddrDigest 现住址的英文地址-摘要
     * @apiSuccess (响应结果) {String} data.residenceEnAddrMask 现住址的英文地址-掩码
     * @apiSuccess (响应结果) {String} data.mailingCountryCode 通讯地址国家编码
     * @apiSuccess (响应结果) {String} data.mailingProvCode 通讯地址省份编码
     * @apiSuccess (响应结果) {String} data.mailingCityCode 通讯地址城市编码
     * @apiSuccess (响应结果) {String} data.mailingCountyCode 通讯地址地区编码
     * @apiSuccess (响应结果) {String} data.mailingCnAddrDigest 通讯地址的中文地址-摘要
     * @apiSuccess (响应结果) {String} data.mailingCnAddrMask 通讯地址的中文地址-掩码
     * @apiSuccess (响应结果) {String} data.mailingEnCountry 通讯地址英文国家名称
     * @apiSuccess (响应结果) {String} data.mailingEnProv 通讯地址英文省份名称
     * @apiSuccess (响应结果) {String} data.mailingEnCity 通讯地址英文城市名称
     * @apiSuccess (响应结果) {String} data.mailingEnCounty 通讯地址英文地区名称
     * @apiSuccess (响应结果) {String} data.mailingTown 通讯地址城镇名称
     * @apiSuccess (响应结果) {String} data.mailingState 通讯地址州县名称
     * @apiSuccess (响应结果) {String} data.mailingEnAddrDigest 通讯地址的英文地址-摘要
     * @apiSuccess (响应结果) {String} data.mailingEnAddrMask 通讯地址的英文地址-掩码
     * @apiSuccess (响应结果) {String} data.emplStatus 就业状态	['01-雇主', '02-全职', '03-兼职', '04-主妇', '05-学生', '06-退休', '07-非在职']
     * @apiSuccess (响应结果) {String} data.emplIncLevel 就业年收入	['01', '02', '03', '04', '05']
     * @apiSuccessExample 响应结果示例
     * {"code":"Xld8nzYwbM","data":{"residenceState":"hIQpxLAy","derivativeKnowledge":"54lcGG","mailingEnAddrDigest":"l","residenceEnCounty":"8O820r","residenceCountyCode":"p","residenceProvCode":"yfu","mailingEnCountry":"ASaaY","ebrokerId":"ureC","riskToleranceScore":"aB1F3x5","mailingEnProv":"jvVB7a","idValidityEnd":"DpWTjRWo14","mailingProvCode":"I67s8br2","custChineseName":"eKSfvF","residenceCnAddrDigest":"LclCDp","mailingState":"o","riskToleranceTerm":"LKgT6Uc","mailingEnCity":"Q","investorQualification":"Eo1","custEnName":"so","hboneNo":"WbT","idType":"DtS","custLoginPasswdType":"ml","mailingCountryCode":"q2Kz3","residenceEnCity":"uBPhsyn","hkTxAcctNo":"TY","idSignAreaCode":"Saf","residenceEnAddrMask":"Rq","nationality":"LmJnLZ5","mailingCountyCode":"8","mailingCnAddrMask":"SSDWqO7N","mobileMask":"Re","birthday":"ETmhqo","mailingCityCode":"lU4pPa","emplStatus":"U8z4","riskToleranceDate":"GqSyErKd","gender":"v3","residenceEnAddrDigest":"JaMp","residenceCountryCode":"2YnwtbkL","mailingTown":"4Q","residenceTown":"9","mobileVerifyStatus":"FtFj","mobileAreaCode":"8fy","residenceEnCountry":"hD6reY","custTxPasswdType":"7","idImageUploadStatus":"Y8iF","investorQualificationDate":"qFMqitW","invstType":"GvaxtJbwyp","mobileDigest":"LHwp4xQBB","residenceEnProv":"nc","residenceCityCode":"ks1oGgb","custNo":"oiBdGTP","residenceCnAddrMask":"b","emplIncLevel":"5mqsyVlO4","birthDt":"cOZoa","riskToleranceLevel":"1hk","hkCustStatus":"ErL0LZGS","idAlwaysValidFlag":"1L7rjLd","mailingCnAddrDigest":"x","idNoDigest":"h5EhDrYcTK","emailDigest":"01g6mUQOLg","emailVerifyStatus":"euNP1p","mailingEnAddrMask":"bOpv","idNoMask":"P","vocation":"TXFu4","mailingEnCounty":"abJsU20","emailMask":"Ryc73N1"},"description":"3Ffi32"}
     */
    @GetMapping("/queryhkcustinfo")
    @ResponseBody
    public Response<HkAcctCustDetailInfoVO> queryHkCustInfo(@RequestParam(name="hkTxAcctNo") String hkTxAcctNo){
        return Response.ok(hkCustInfoService.queryHkCustInfo(hkTxAcctNo));
    }


    /**
     * @api {POST} /hkcustinfo/queryhkacctcustrelation queryHkAcctCustRelation()
     * @apiVersion 1.0.0
     * @apiGroup HkCustInfoController
     * @apiName queryHkAcctCustRelation()
     * @apiDescription 查询crm系统内，香港客户vs投顾客户绑定关系
     * @apiParam (请求体) {Number} id 主键
     * @apiParam (请求体) {String} custNo 投顾客户号
     * @apiParam (请求体) {String} ebrokerId 客户香港id (ebrokerID)
     * @apiParam (请求体) {String} hkTxAcctNo 香港交易账号
     * @apiParamExample 请求体示例
     * {"hkTxAcctNo":"W7R6FLohK2","custNo":"KRhqQ61Ma0","id":8891.231740178444,"ebrokerId":"4l"}
     * @apiSuccess (响应结果) {String} code 状态码
     * @apiSuccess (响应结果) {String} description 描述信息
     * @apiSuccess (响应结果) {Object} data 数据封装
     * @apiSuccess (响应结果) {Number} data.id 主键
     * @apiSuccess (响应结果) {String} data.custNo 投顾客户号
     * @apiSuccess (响应结果) {String} data.ebrokerId 客户香港id (ebrokerID)
     * @apiSuccess (响应结果) {String} data.hkTxAcctNo 香港交易账号
     * @apiSuccessExample 响应结果示例
     * {"code":"5a03","data":{"hkTxAcctNo":"B5G","custNo":"gtYOuV","id":9593.669845908096,"ebrokerId":"beLfw0w"},"description":"mYu"}
     */
    @PostMapping("/queryhkacctcustrelation")
    @ResponseBody
    public Response<HkAcctCustRelationInfoVO> queryHkAcctCustRelation(@RequestBody HkAcctRelationRequest relationRequest){
        return Response.ok(hkCustInfoService.queryHkAcctCustRelation(relationRequest));
    }


    /**
     * @api {POST} /hkcustinfo/bindhktxacct bindHkTxAcct()
     * @apiVersion 1.0.0
     * @apiGroup HkCustInfoController
     * @apiName bindHkTxAcct()
     * @apiDescription 绑定 香港客户号、投顾客户号关系
     * @apiParam (请求体) {String} custNo 投顾客户号
     * @apiParam (请求体) {String} hkTxAcctNo 香港交易账号
     * @apiParam (请求体) {String} operator 操作员
     * @apiParam (请求体) {String} operateSource 非必须      操作来源 [1-MQ]时，为[异常来源]， [2-菜单页面]时，为菜单名称
     * @apiParam (请求体) {String} operateChannel 非必须      操作通道 1-MQ  2-菜单页面
     * @apiParam (请求体) {String} remark 备注
     * @apiParamExample 请求体示例
     * {"hkTxAcctNo":"W1fq8v","custNo":"OVzwg","operateChannel":"XPTtOafV2a","operateSource":"e","operator":"bg0Py0F"}
     * @apiSuccess (响应结果) {String} code 状态码
     * @apiSuccess (响应结果) {String} description 描述信息
     * @apiSuccess (响应结果) {Object} data 数据封装
     * @apiSuccess (响应结果) {String} data.code 状态码
     * @apiSuccess (响应结果) {String} data.description 描述信息
     * @apiSuccess (响应结果) {String} data.data 数据封装
     * @apiSuccessExample 响应结果示例
     * {"code":"axFwr4cRc","data":"E","description":"EG9fTSR8p"}
     */
    @PostMapping("/bindhktxacct")
    @ResponseBody
    public Response<Response<String>> bindHkTxAcct(@RequestBody HkAcctRelationOptRequest bindOptRequest) {
         return  Response.ok(hkCustInfoService.bindHkTxAcct(bindOptRequest));
    }


    /**
     * @api {POST} /hkcustinfo/unbindhktxacct unBindHkTxAcct()
     * @apiVersion 1.0.0
     * @apiGroup HkCustInfoController
     * @apiName unBindHkTxAcct()
     * @apiDescription 解除绑定 香港客户号、投顾客户号关系
     * @apiParam (请求体) {String} custNo 投顾客户号
     * @apiParam (请求体) {String} hkTxAcctNo 香港交易账号
     * @apiParam (请求体) {String} operator 操作员
     * @apiParam (请求体) {String} operateSource 非必须      操作来源 [1-MQ]时，为[异常来源]， [2-菜单页面]时，为菜单名称
     * @apiParam (请求体) {String} operateChannel 非必须      操作通道 1-MQ  2-菜单页面
     * @apiParam (请求体) {String} remark 备注
     * @apiParamExample 请求体示例
     * {"hkTxAcctNo":"AX0v5G1B","custNo":"JTATOW","operateChannel":"Y7nvp","operateSource":"XFUBt","operator":"mhh7lHRI"}
     * @apiSuccess (响应结果) {String} code 状态码
     * @apiSuccess (响应结果) {String} description 描述信息
     * @apiSuccess (响应结果) {Object} data 数据封装
     * @apiSuccess (响应结果) {String} data.code 状态码
     * @apiSuccess (响应结果) {String} data.description 描述信息
     * @apiSuccess (响应结果) {String} data.data 数据封装
     * @apiSuccessExample 响应结果示例
     * {"code":"SjkbF","data":"HAYLI","description":"3mSbjX"}
     */
    @PostMapping("/unbindhktxacct")
    @ResponseBody
    public Response<Response<String>> unBindHkTxAcct(@RequestBody HkAcctRelationOptRequest bindOptRequest) {
        return  Response.ok(hkCustInfoService.unBindHkTxAcct(bindOptRequest));
    }

    /**
     * @api {POST} /hkcustinfo/dealAfterCrmCreateHkTxAcct dealAfterCrmCreateHkTxAcct()
     * @apiVersion 1.0.0
     * @apiGroup HkCustInfoController
     * @apiName dealAfterCrmCreateHkTxAcct()
     * @apiDescription CRM资料管理，香港线下开户 审核通过之后，回调该接口，来完成投顾客户和香港客户的绑定及更新
     * @apiParam (请求体) {String} hkCustNo 香港客户号
     * @apiParam (请求体) {String} operator 操作人员
     * @apiParam (请求体) {String} custNo 客户号
     * @apiParam (请求体) {String} hboneNo 关联的一账通号
     * @apiParamExample 请求体示例
     * {"custNo":"zYrGdlN3Ml","hkCustNo":"GFnHx7Etn","operator":"bP","hboneNo":"FDep"}
     * @apiSuccess (响应结果) {String} code 状态码
     * @apiSuccess (响应结果) {String} description 描述信息
     * @apiSuccess (响应结果) {String} data 数据封装
     * @apiSuccessExample 响应结果示例
     * {"code":"2ZjBYlE","data":"z6wh","description":"1Xrf"}
     */
    @ResponseBody
    @PostMapping("/dealAfterCrmCreateHkTxAcct")
    public Response<String> dealAfterCrmCreateHkTxAcct(@RequestBody HkAcctCreateOptVO hkAcctCreateOptVO) {
        return hkCustInfoService.dealAfterCrmCreateHkTxAcct(hkAcctCreateOptVO);
    }


    /**
     * @api {POST} /hkcustinfo/updateCustInfoByHk updateCustInfoByHk()
     * @apiVersion 1.0.0
     * @apiGroup HkCustInfoController
     * @apiName updateCustInfoByHk()
     * @apiDescription 根据 香港客户信息 更新投顾客户信息
     * @apiParam (请求体) {String} hkCustNo 香港客户号
     * @apiParam (请求体) {String} operator 操作人员
     * @apiParam (请求体) {String} custNo 客户号
     * @apiParamExample 请求体示例
     * {"custNo":"ZmAXXQvz","hkCustNo":"xDS0CV2","operator":"2p"}
     * @apiSuccess (响应结果) {String} code 状态码
     * @apiSuccess (响应结果) {String} description 描述信息
     * @apiSuccess (响应结果) {String} data 数据封装
     * @apiSuccessExample 响应结果示例
     * {"code":"sTYhvyHfv","data":"HB5sBvcZvg","description":"YrMZT"}
     */
    @ResponseBody
    @PostMapping("/updateCustInfoByHk")
    public Response<String> updateCustInfoByHk(@RequestBody UpdateCustInfoByHkRequest updateCustInfoByHkRequest) {
        return hkCustInfoService.updateCustInfoByHk(updateCustInfoByHkRequest);
    }

    /**
     * @api {POST} /hkcustinfo/mergehktxacct mergeHkTxAcct()
     * @apiVersion 1.0.0
     * @apiGroup HkCustInfoController
     * @apiName mergeHkTxAcct()
     * @apiDescription 处理合并客户相关的香港交易账号变更
     * @apiParam (请求体) {Array} deleteCustNoList 待删除客户号列表
     * @apiParam (请求体) {String} preserveCustNo 保留的客户号
     * @apiParam (请求体) {String} definedHkTxAcctNo 指定 保留的客户号 关联的香港交易账号
     * @apiParam (请求体) {String} operator 操作员
     * @apiParam (请求体) {String} operateSource 非必须      操作来源 [1-MQ]时，为[异常来源]， [2-菜单页面]时，为菜单名称
     * @apiParam (请求体) {String} operateChannel 非必须      操作通道 1-MQ  2-菜单页面
     * @apiParam (请求体) {String} remark 备注
     * @apiParamExample 请求体示例
     * {"preserveCustNo":"t","operateChannel":"m","definedHkTxAcctNo":"rHl","deleteCustNoList":["vr"],"operateSource":"H5xnFvr","operator":"9F4VZi"}
     * @apiSuccess (响应结果) {String} code 状态码
     * @apiSuccess (响应结果) {String} description 描述信息
     * @apiSuccess (响应结果) {Object} data 数据封装
     * @apiSuccess (响应结果) {String} data.code 状态码
     * @apiSuccess (响应结果) {String} data.description 描述信息
     * @apiSuccess (响应结果) {String} data.data 数据封装
     * @apiSuccessExample 响应结果示例
     * {"code":"3Ac9","data":{"code":"kzQXku3","data":"vOR","description":"axKYhIAfof"},"description":"WM"}
     */
    @PostMapping("/mergehktxacct")
    @ResponseBody
    public Response<Response<String>> mergeHkTxAcct(@RequestBody HkAcctMergeOptRequest mergetOptRequest) {
        return  Response.ok(hkCustInfoService.mergeHkTxAcct(mergetOptRequest));
    }


    /**
     * @api {GET} /hkcustinfo/queryHkCustInfoByCustNo queryHkCustInfoByCustNo()
     * @apiVersion 1.0.0
     * @apiGroup HkCustInfoController
     * @apiName queryHkCustInfoByCustNo()
     * @apiDescription 根据 投顾客户号 查询香港账户中心香港客户信息
     * @apiParam (请求参数) {String} custNo 投顾客户号
     * @apiParamExample 请求参数示例
     * custNo=d5VlVRI
     * @apiSuccess (响应结果) {String} code 状态码
     * @apiSuccess (响应结果) {String} description 描述信息
     * @apiSuccess (响应结果) {Object} data 数据封装
     * @apiSuccess (响应结果) {String} data.custNo CRM 客户号
     * @apiSuccess (响应结果) {String} data.hkTxAcctNo 香港交易账号
     * @apiSuccess (响应结果) {String} data.ebrokerId ebrokerId
     * @apiSuccess (响应结果) {String} data.custChineseName 香港客户中文名
     * @apiSuccess (响应结果) {String} data.custEnName 香港客户英文名
     * @apiSuccess (响应结果) {String} data.hkCustStatus {@link HkAcctCustStatusEnum}      香港客户状态-   客户状态 0-正常 1-注销 2-休眠 3-注册4-开户申请成功
     * @apiSuccess (响应结果) {String} data.birthDt 出生日期
     * @apiSuccess (响应结果) {String} data.idNoDigest 证件号码
     * @apiSuccess (响应结果) {String} data.idNoMask 证件号码
     * @apiSuccess (响应结果) {String} data.idSignAreaCode 证件地区码-身份证签发地区编码
     * @apiSuccess (响应结果) {String} data.idType 证件类型
     * @apiSuccess (响应结果) {String} data.invstType 投资者类型 0-机构,1-个人,2-产品户
     * @apiSuccess (响应结果) {String} data.mobileAreaCode 手机号码区号
     * @apiSuccess (响应结果) {String} data.mobileDigest 手机号码
     * @apiSuccess (响应结果) {String} data.mobileMask 手机号码
     * @apiSuccess (响应结果) {String} data.mobileVerifyStatus 手机号码 验证状态      0:未验证 1:已验证
     * @apiSuccess (响应结果) {String} data.emailDigest 邮件
     * @apiSuccess (响应结果) {String} data.emailMask 邮件
     * @apiSuccess (响应结果) {String} data.emailVerifyStatus 邮件 验证状态      0:未验证 1:已验证
     * @apiSuccess (响应结果) {String} data.hboneNo 关联的一账通号
     * @apiSuccess (响应结果) {String} data.idValidityEnd 证件有效期
     * @apiSuccess (响应结果) {String} data.idAlwaysValidFlag 证件是否长期有效 0-否 1-是
     * @apiSuccess (响应结果) {String} data.idImageUploadStatus 证件图片上传状态0-未上传 1-已上传
     * @apiSuccess (响应结果) {String} data.nationality 国籍
     * @apiSuccess (响应结果) {String} data.birthday 出生日期
     * @apiSuccess (响应结果) {String} data.gender 性别 0-女，1-男，2-非自然人
     * @apiSuccess (响应结果) {String} data.vocation 职业
     * @apiSuccess (响应结果) {String} data.custLoginPasswdType 客户登录密码状态0正常状态 1重置状态 2-未设置
     * @apiSuccess (响应结果) {String} data.custTxPasswdType 客户交易密码状态0正常状态 1重置状态 2-未设置
     * @apiSuccess (响应结果) {String} data.riskToleranceLevel 风险承受能力评估级别 0,1,2,3,4,5
     * @apiSuccess (响应结果) {String} data.derivativeKnowledge 衍生金融工具知识0-无 1-有
     * @apiSuccess (响应结果) {String} data.riskToleranceDate 风险评测日期	['yyyyMMdd']
     * @apiSuccess (响应结果) {String} data.riskToleranceTerm 风险评测有效期	['yyyyMMdd']
     * @apiSuccess (响应结果) {String} data.riskToleranceScore 风险评测分数
     * @apiSuccess (响应结果) {String} data.investorQualification 投资者资质PRO-投资者资质专业/NORMAL-投资者资质普通
     * @apiSuccess (响应结果) {String} data.investorQualificationDate 投资者资质认证日期
     * @apiSuccess (响应结果) {String} data.residenceCountryCode 现住址国家编码
     * @apiSuccess (响应结果) {String} data.residenceProvCode 现住址省份编码
     * @apiSuccess (响应结果) {String} data.residenceCityCode 现住址城市编码
     * @apiSuccess (响应结果) {String} data.residenceCountyCode 现住址地区编码
     * @apiSuccess (响应结果) {String} data.residenceCnAddrDigest 现住址的中文地址-摘要
     * @apiSuccess (响应结果) {String} data.residenceCnAddrMask 现住址的中文地址-掩码
     * @apiSuccess (响应结果) {String} data.residenceEnCountry 现住址英文国家名称
     * @apiSuccess (响应结果) {String} data.residenceEnProv 现住址英文省份名称
     * @apiSuccess (响应结果) {String} data.residenceEnCity 现住址英文城市名称
     * @apiSuccess (响应结果) {String} data.residenceEnCounty 现住址英文地区名称
     * @apiSuccess (响应结果) {String} data.residenceTown 现住址城镇名称
     * @apiSuccess (响应结果) {String} data.residenceState 现住址州县名称
     * @apiSuccess (响应结果) {String} data.residenceEnAddrDigest 现住址的英文地址-摘要
     * @apiSuccess (响应结果) {String} data.residenceEnAddrMask 现住址的英文地址-掩码
     * @apiSuccess (响应结果) {String} data.mailingCountryCode 通讯地址国家编码
     * @apiSuccess (响应结果) {String} data.mailingProvCode 通讯地址省份编码
     * @apiSuccess (响应结果) {String} data.mailingCityCode 通讯地址城市编码
     * @apiSuccess (响应结果) {String} data.mailingCountyCode 通讯地址地区编码
     * @apiSuccess (响应结果) {String} data.mailingCnAddrDigest 通讯地址的中文地址-摘要
     * @apiSuccess (响应结果) {String} data.mailingCnAddrMask 通讯地址的中文地址-掩码
     * @apiSuccess (响应结果) {String} data.mailingEnCountry 通讯地址英文国家名称
     * @apiSuccess (响应结果) {String} data.mailingEnProv 通讯地址英文省份名称
     * @apiSuccess (响应结果) {String} data.mailingEnCity 通讯地址英文城市名称
     * @apiSuccess (响应结果) {String} data.mailingEnCounty 通讯地址英文地区名称
     * @apiSuccess (响应结果) {String} data.mailingTown 通讯地址城镇名称
     * @apiSuccess (响应结果) {String} data.mailingState 通讯地址州县名称
     * @apiSuccess (响应结果) {String} data.mailingEnAddrDigest 通讯地址的英文地址-摘要
     * @apiSuccess (响应结果) {String} data.mailingEnAddrMask 通讯地址的英文地址-掩码
     * @apiSuccess (响应结果) {String} data.emplStatus 就业状态	['01-雇主', '02-全职', '03-兼职', '04-主妇', '05-学生', '06-退休', '07-非在职']
     * @apiSuccess (响应结果) {String} data.emplIncLevel 就业年收入	['01', '02', '03', '04', '05']
     * @apiSuccessExample 响应结果示例
     * {"code":"Xld8nzYwbM","data":{"residenceState":"hIQpxLAy","derivativeKnowledge":"54lcGG","mailingEnAddrDigest":"l","residenceEnCounty":"8O820r","residenceCountyCode":"p","residenceProvCode":"yfu","mailingEnCountry":"ASaaY","ebrokerId":"ureC","riskToleranceScore":"aB1F3x5","mailingEnProv":"jvVB7a","idValidityEnd":"DpWTjRWo14","mailingProvCode":"I67s8br2","custChineseName":"eKSfvF","residenceCnAddrDigest":"LclCDp","mailingState":"o","riskToleranceTerm":"LKgT6Uc","mailingEnCity":"Q","investorQualification":"Eo1","custEnName":"so","hboneNo":"WbT","idType":"DtS","custLoginPasswdType":"ml","mailingCountryCode":"q2Kz3","residenceEnCity":"uBPhsyn","hkTxAcctNo":"TY","idSignAreaCode":"Saf","residenceEnAddrMask":"Rq","nationality":"LmJnLZ5","mailingCountyCode":"8","mailingCnAddrMask":"SSDWqO7N","mobileMask":"Re","birthday":"ETmhqo","mailingCityCode":"lU4pPa","emplStatus":"U8z4","riskToleranceDate":"GqSyErKd","gender":"v3","residenceEnAddrDigest":"JaMp","residenceCountryCode":"2YnwtbkL","mailingTown":"4Q","residenceTown":"9","mobileVerifyStatus":"FtFj","mobileAreaCode":"8fy","residenceEnCountry":"hD6reY","custTxPasswdType":"7","idImageUploadStatus":"Y8iF","investorQualificationDate":"qFMqitW","invstType":"GvaxtJbwyp","mobileDigest":"LHwp4xQBB","residenceEnProv":"nc","residenceCityCode":"ks1oGgb","custNo":"oiBdGTP","residenceCnAddrMask":"b","emplIncLevel":"5mqsyVlO4","birthDt":"cOZoa","riskToleranceLevel":"1hk","hkCustStatus":"ErL0LZGS","idAlwaysValidFlag":"1L7rjLd","mailingCnAddrDigest":"x","idNoDigest":"h5EhDrYcTK","emailDigest":"01g6mUQOLg","emailVerifyStatus":"euNP1p","mailingEnAddrMask":"bOpv","idNoMask":"P","vocation":"TXFu4","mailingEnCounty":"abJsU20","emailMask":"Ryc73N1"},"description":"3Ffi32"}
     */
    @GetMapping("/queryHkCustInfoByCustNo")
    @ResponseBody
    public Response<HkAcctCustDetailInfoVO> queryHkCustInfoByCustNo(@RequestParam(name = "custNo") String custNo) {
        return Response.ok(hkCustInfoService.queryHkCustInfoByCustNo(custNo));
    }

    /**
     * @api {GET} /hkcustinfo/queryHkCustSensitiveInfoByHkTxAcctNo queryHkCustSensitiveInfo()
     * @apiVersion 1.0.0
     * @apiGroup HkCustInfoController
     * @apiName queryHkCustSensitiveInfo()
     * @apiParam (请求参数) {String} hkTxAcctNo
     * @apiParamExample 请求参数示例
     * hkTxAcctNo=lO5FDV23
     * @apiSuccess (响应结果) {String} code 状态码
     * @apiSuccess (响应结果) {String} description 描述信息
     * @apiSuccess (响应结果) {Object} data 数据封装
     * @apiSuccess (响应结果) {String} data.idNo 证件号码
     * @apiSuccess (响应结果) {String} data.email 邮箱
     * @apiSuccess (响应结果) {String} data.statementEmail 结单邮箱
     * @apiSuccess (响应结果) {String} data.residenceCnAddr 现住址的中文地址
     * @apiSuccess (响应结果) {String} data.residenceEnAddr 现住址的英文地址
     * @apiSuccess (响应结果) {String} data.mailingCnAddr 通讯地址的中文地址
     * @apiSuccess (响应结果) {String} data.mailingEnAddr 通讯地址的英文地址
     * @apiSuccess (响应结果) {String} data.birthAddr 出生地地址
     * @apiSuccess (响应结果) {String} data.emplAddr 就业地址
     * @apiSuccessExample 响应结果示例
     * {"code":"Nsbz6wP","data":{"residenceCnAddr":"UuLX6eBy0","mailingEnAddr":"e","emplAddr":"XnUGZY","residenceEnAddr":"LTQM","idNo":"EXl","email":"m84vse","statementEmail":"uPPkEn7C","mailingCnAddr":"3SHiD6l20","birthAddr":"gG0"},"description":"zhVxTFioM"}
     */
    @GetMapping("/queryHkCustSensitiveInfoByHkTxAcctNo")
    @ResponseBody
    public Response<HkAcctCustSensitiveInfoVO> queryHkCustSensitiveInfo(@RequestParam(name = "hkTxAcctNo") String hkTxAcctNo) {
        return Response.ok(hkCustInfoService.queryHkCustSensitiveInfo(hkTxAcctNo));
    }


    /**
     * @api {GET} /hkcustinfo/queryHkSensitiveMobile queryHkSensitiveMobile()
     * @apiVersion 1.0.0
     * @apiGroup HkCustInfoController
     * @apiName queryHkSensitiveMobile()
     * @apiParam (请求参数) {String} hkTxAcctNo
     * @apiParamExample 请求参数示例
     * hkTxAcctNo=gqj9N61fT3
     * @apiSuccess (响应结果) {String} code 状态码
     * @apiSuccess (响应结果) {String} description 描述信息
     * @apiSuccess (响应结果) {String} data 数据封装
     * @apiSuccessExample 响应结果示例
     * {"code":"FwR","data":"ysx","description":"Q52jLYSA"}
     */
    @GetMapping("/queryHkSensitiveMobile")
    @ResponseBody
    public Response<String> queryHkSensitiveMobile(@RequestParam(name = "hkTxAcctNo") String hkTxAcctNo) {
        return Response.ok(hkCustInfoService.queryHkSensitiveMobile(hkTxAcctNo));
    }



    /**
     * @api {GET} /hkcustinfo/queryHkKycAnswerListByHkTxAcctNo queryHkKycAnswerListByHkTxAcctNo()
     * @apiVersion 1.0.0
     * @apiGroup HkCustInfoController
     * @apiName queryHkKycAnswerListByHkTxAcctNo()
     * @apiParam (请求参数) {String} hkTxAcctNo
     * @apiParam (请求参数) {String} startDate
     * @apiParam (请求参数) {String} endDate
     * @apiParamExample 请求参数示例
     * hkTxAcctNo=n3LAbor7s&endDate=s6&startDate=DOTFl
     * @apiSuccess (响应结果) {String} code 状态码
     * @apiSuccess (响应结果) {String} description 描述信息
     * @apiSuccess (响应结果) {Array} data 数据封装
     * @apiSuccess (响应结果) {String} data.levelValue 风险等级 1-低风险等级 2-中低风险等级 3-中风险等级 4-中高风险等级 5-高风险等级
     * @apiSuccess (响应结果) {Number} data.score 计算得到的分数
     * @apiSuccess (响应结果) {String} data.derivativeKnowledge 衍生金融工具知识 0-无 1-有
     * @apiSuccess (响应结果) {String} data.riskToleranceDate 风险测评日期 YYYYMMDD
     * @apiSuccess (响应结果) {String} data.riskToleranceTerm 风险评测过期日期 YYYYMMDD
     * @apiSuccess (响应结果) {Number} data.stimestamp 创建时间戳
     * @apiSuccessExample 响应结果示例
     * {"code":"0x4Rlhjt","data":[{"score":6068.926148165533,"derivativeKnowledge":"rJ","riskToleranceDate":"1OyGudyN","stimestamp":2823092261109,"riskToleranceTerm":"82WOB7TD","levelValue":"xC"}],"description":"D"}
     */
    @GetMapping("/queryHkKycAnswerListByHkTxAcctNo")
    @ResponseBody
    public Response<List<HkKycAnswerVO>> queryHkKycAnswerListByHkTxAcctNo(@RequestParam(name = "hkTxAcctNo") String hkTxAcctNo,
                                                                          @RequestParam(name = "startDate") String startDate,
                                                                          @RequestParam(name = "endDate") String endDate) {
        return Response.ok(hkCustInfoService.queryHkKycAnswerListByHkTxAcctNo(hkTxAcctNo, startDate, endDate));
    }


    /**
     * @api {GET} /hkcustinfo/queryHkBankCardInfoListByHkTxAcctNo queryHkBankCardInfoListByHkTxAcctNo()
     * @apiVersion 1.0.0
     * @apiGroup HkCustInfoController
     * @apiName queryHkBankCardInfoListByHkTxAcctNo()
     * @apiParam (请求参数) {String} hkTxAcctNo
     * @apiParamExample 请求参数示例
     * hkTxAcctNo=2EO5NU
     * @apiSuccess (响应结果) {String} code 状态码
     * @apiSuccess (响应结果) {String} description 描述信息
     * @apiSuccess (响应结果) {Array} data 数据封装
     * @apiSuccess (响应结果) {String} data.hkCpAcctNo 香港资金账号
     * @apiSuccess (响应结果) {String} data.bankCode 银行代码
     * @apiSuccess (响应结果) {String} data.mainlandBankCode 大陆银行编码
     * @apiSuccess (响应结果) {String} data.bankName 银行名称
     * @apiSuccess (响应结果) {String} data.bankChineseName 银行中文名称
     * @apiSuccess (响应结果) {String} data.bankRegionCode 联行号
     * @apiSuccess (响应结果) {String} data.bankAcctDigest 摘要-银行账号
     * @apiSuccess (响应结果) {String} data.bankAcctMask 掩码-银行账号
     * @apiSuccess (响应结果) {String} data.bankAcctName 银行账户名称
     * @apiSuccess (响应结果) {String} data.bankAcctStatus 银行账户状态0-正常 1-注销
     * @apiSuccess (响应结果) {String} data.defaultAccount 默认账号0-否 1-是
     * @apiSuccess (响应结果) {String} data.curCode 币种
     * @apiSuccess (响应结果) {Array} data.currencyCodes 币种代码列表
     * @apiSuccess (响应结果) {String} data.swiftCode SWIFT代码
     * @apiSuccess (响应结果) {String} data.jointAccount 是否为联名账户 0-否 1-是
     * @apiSuccess (响应结果) {String} data.correspondentSwiftCode 代理银行swift编码
     * @apiSuccess (响应结果) {String} data.correspondentBankCode 代理银行代码
     * @apiSuccess (响应结果) {String} data.correspondentMainlandBankCode 代理银行大陆银行编码
     * @apiSuccess (响应结果) {String} data.correspondentBankName 代理银行银行名称
     * @apiSuccess (响应结果) {String} data.correspondentBankChineseName 代理银行中文名称
     * @apiSuccess (响应结果) {String} data.correspondentBankAcctDigest 代理银行账户-摘要
     * @apiSuccess (响应结果) {String} data.correspondentBankAcctMask 代理银行账户-掩码
     * @apiSuccess (响应结果) {String} data.depositsVefyStat 入金验证状态 0-未验证 1-已验证
     * @apiSuccess (响应结果) {Number} data.stimestamp 创建时间戳
     * @apiSuccessExample 响应结果示例
     * {"code":"epoC","data":[{"bankRegionCode":"TByef9","correspondentSwiftCode":"SBN","hkCpAcctNo":"pYX","bankCode":"kDln5tR","correspondentBankName":"hX1xDf","bankChineseName":"Ny","correspondentBankAcctMask":"y4C7F5C","mainlandBankCode":"6wz","bankAcctName":"rysp2q","bankAcctMask":"zSL9NrO","currencyCodes":["qGtxU29G"],"stimestamp":*************,"swiftCode":"j","bankName":"RJjPG7ClF","defaultAccount":"ZMOLoc0","correspondentBankChineseName":"kn","bankAcctDigest":"RbTmgQ","bankAcctStatus":"eoUonazF","jointAccount":"sbj","correspondentMainlandBankCode":"FMXd","correspondentBankAcctDigest":"YK9BsAJ","depositsVefyStat":"VQ099WXv","correspondentBankCode":"TkIBsjSgMX","curCode":"r8"}],"description":"Byn43"}
     */
    @ResponseBody
    @GetMapping("/queryHkBankCardInfoListByHkTxAcctNo")
    public Response<List<HkBankCardInfoVO>> queryHkBankCardInfoListByHkTxAcctNo(@RequestParam(name = "hkTxAcctNo") String hkTxAcctNo) {
        return Response.ok(hkCustInfoService.queryHkBankCardInfoListByHkTxAcctNo(hkTxAcctNo));
    }


    /**
     * @api {GET} /hkcustinfo/queryHkCustAllInfoByHkTxAcctNo queryHkCustAllInfoByHkTxAcctNo()
     * @apiVersion 1.0.0
     * @apiGroup HkCustInfoController
     * @apiName queryHkCustAllInfoByHkTxAcctNo()
     * @apiParam (请求参数) {String} hkTxAcctNo
     * @apiParamExample 请求参数示例
     * hkTxAcctNo=KQAp
     * @apiSuccess (响应结果) {String} code 状态码
     * @apiSuccess (响应结果) {String} description 描述信息
     * @apiSuccess (响应结果) {Object} data 数据封装
     * @apiSuccess (响应结果) {Object} data.custInfoVO 客户信息
     * @apiSuccess (响应结果) {String} data.custInfoVO.hkCustNo 香港客户号
     * @apiSuccess (响应结果) {String} data.custInfoVO.ebrokerId EBROKER_ID
     * @apiSuccess (响应结果) {String} data.custInfoVO.custName 客户姓名
     * @apiSuccess (响应结果) {String} data.custInfoVO.idNoDigest 证件号码摘要
     * @apiSuccess (响应结果) {String} data.custInfoVO.idNoMask 证件号码掩码
     * @apiSuccess (响应结果) {String} data.custInfoVO.idType 证件类型
     * @apiSuccess (响应结果) {String} data.custInfoVO.idValidityEnd 证件有效期
     * @apiSuccess (响应结果) {String} data.custInfoVO.idAlwaysValidFlag 证件是否长期有效0-否 1-是
     * @apiSuccess (响应结果) {String} data.custInfoVO.idImageUploadStatus 证件图片上传状态0-未上传 1-已上传
     * @apiSuccess (响应结果) {String} data.custInfoVO.nationality 国籍
     * @apiSuccess (响应结果) {String} data.custInfoVO.birthday 出生日期
     * @apiSuccess (响应结果) {String} data.custInfoVO.gender 性别 0-女，1-男，2-非自然人
     * @apiSuccess (响应结果) {String} data.custInfoVO.vocation 职业
     * @apiSuccess (响应结果) {String} data.custInfoVO.invstType 投资者类型0-机构,1-个人,2-产品户
     * @apiSuccess (响应结果) {String} data.custInfoVO.mobileAreaCode 手机号码区号
     * @apiSuccess (响应结果) {String} data.custInfoVO.mobileDigest 手机号码摘要
     * @apiSuccess (响应结果) {String} data.custInfoVO.mobileMask 手机号码掩码
     * @apiSuccess (响应结果) {String} data.custInfoVO.mobileVerifyStatus 手机验证状态0:未验证 1:已验证
     * @apiSuccess (响应结果) {String} data.custInfoVO.emailDigest 电子邮箱摘要
     * @apiSuccess (响应结果) {String} data.custInfoVO.emailMask 电子邮箱掩码
     * @apiSuccess (响应结果) {String} data.custInfoVO.emailVerifyStatus 邮箱验证状态0:未验证 1:已验证
     * @apiSuccess (响应结果) {String} data.custInfoVO.custState 客户状态0-正常 1-注销 2-休眠 3-注册 4-开户申请成功
     * @apiSuccess (响应结果) {String} data.custInfoVO.custLoginPasswdType 客户登录密码状态0正常状态 1重置状态 2-未设置
     * @apiSuccess (响应结果) {String} data.custInfoVO.custTxPasswdType 客户交易密码状态0正常状态 1重置状态 2-未设置
     * @apiSuccess (响应结果) {String} data.custInfoVO.openDate 开户日期
     * @apiSuccess (响应结果) {String} data.custInfoVO.closeDate 销户日期
     * @apiSuccess (响应结果) {String} data.custInfoVO.custChineseName 客户中文名
     * @apiSuccess (响应结果) {String} data.custInfoVO.companyRegCountry 公司注册国家
     * @apiSuccess (响应结果) {String} data.custInfoVO.companyRegDate 公司注册日期
     * @apiSuccess (响应结果) {String} data.custInfoVO.natureOfBusiness 业务性质
     * @apiSuccess (响应结果) {String} data.custInfoVO.businessRegistrationNumber 营业登记编号
     * @apiSuccess (响应结果) {String} data.custInfoVO.investorQualification 投资者资质PRO-投资者资质专业/NORMAL-投资者资质普通
     * @apiSuccess (响应结果) {String} data.custInfoVO.investorQualificationDate 投资者资质认证日期
     * @apiSuccess (响应结果) {String} data.custInfoVO.idSignAreaCode 证件所属国家/地区代码
     * @apiSuccess (响应结果) {String} data.custInfoVO.registerDt 注册日期
     * @apiSuccess (响应结果) {String} data.custInfoVO.openType 开户方式 0-线下 1-线上
     * @apiSuccess (响应结果) {String} data.custInfoVO.cnSurname 中文姓
     * @apiSuccess (响应结果) {String} data.custInfoVO.cnGivenName 中文名
     * @apiSuccess (响应结果) {String} data.custInfoVO.custEnName 英文名称
     * @apiSuccess (响应结果) {String} data.custInfoVO.enSurname 英文姓
     * @apiSuccess (响应结果) {String} data.custInfoVO.enGivenName 英文名
     * @apiSuccess (响应结果) {String} data.custInfoVO.cnFormerName 曾用中文名
     * @apiSuccess (响应结果) {String} data.custInfoVO.enFormerName 曾用英文名
     * @apiSuccess (响应结果) {String} data.custInfoVO.marriageStat 婚姻状况
     * @apiSuccess (响应结果) {String} data.custInfoVO.marriageStatDesc 婚姻状况说明
     * @apiSuccess (响应结果) {String} data.custInfoVO.eduLevel 教育程度
     * @apiSuccess (响应结果) {String} data.custInfoVO.statementEmailDigest 结单接收电子邮箱摘要
     * @apiSuccess (响应结果) {String} data.custInfoVO.statementEmailMask 结单接收电子邮箱掩码
     * @apiSuccess (响应结果) {String} data.custInfoVO.residenceCountryCode 现居地址-国家/地区代码
     * @apiSuccess (响应结果) {String} data.custInfoVO.residenceProvCode 现居地址-省代码
     * @apiSuccess (响应结果) {String} data.custInfoVO.residenceCityCode 现居地址-市代码
     * @apiSuccess (响应结果) {String} data.custInfoVO.residenceCountyCode 现居地址-县代码
     * @apiSuccess (响应结果) {String} data.custInfoVO.residenceCnAddrDigest 现居地址-中文详细地址摘要
     * @apiSuccess (响应结果) {String} data.custInfoVO.residenceCnAddrMask 现居地址-中文详细地址掩码
     * @apiSuccess (响应结果) {String} data.custInfoVO.residenceEnCountry 现居地址（英文）-国家/地区
     * @apiSuccess (响应结果) {String} data.custInfoVO.residenceEnProv 现居地址（英文）-省
     * @apiSuccess (响应结果) {String} data.custInfoVO.residenceEnCity 现居地址（英文）-市
     * @apiSuccess (响应结果) {String} data.custInfoVO.residenceEnCounty 现居地址（英文）-县
     * @apiSuccess (响应结果) {String} data.custInfoVO.residenceTown 现居地址（英文）-城/镇
     * @apiSuccess (响应结果) {String} data.custInfoVO.residenceState 现居地址（英文）-省/州
     * @apiSuccess (响应结果) {String} data.custInfoVO.residenceEnAddrDigest 现居地址-英文详细地址摘要
     * @apiSuccess (响应结果) {String} data.custInfoVO.residenceEnAddrMask 现居地址-英文详细地址掩码
     * @apiSuccess (响应结果) {String} data.custInfoVO.mailingCountryCode 通讯地址-国家/地区代码
     * @apiSuccess (响应结果) {String} data.custInfoVO.mailingProvCode 通讯地址-省代码
     * @apiSuccess (响应结果) {String} data.custInfoVO.mailingCityCode 通讯地址-市代码
     * @apiSuccess (响应结果) {String} data.custInfoVO.mailingCountyCode 通讯地址-县代码
     * @apiSuccess (响应结果) {String} data.custInfoVO.mailingCnAddrDigest 通讯地址-中文详细地址摘要
     * @apiSuccess (响应结果) {String} data.custInfoVO.mailingCnAddrMask 通讯地址-中文详细地址掩码
     * @apiSuccess (响应结果) {String} data.custInfoVO.mailingEnCountry 通讯地址（英文）-国家/地区
     * @apiSuccess (响应结果) {String} data.custInfoVO.mailingEnProv 通讯地址（英文）-省
     * @apiSuccess (响应结果) {String} data.custInfoVO.mailingEnCity 通讯地址（英文）-市
     * @apiSuccess (响应结果) {String} data.custInfoVO.mailingEnCounty 通讯地址（英文）-县
     * @apiSuccess (响应结果) {String} data.custInfoVO.mailingTown 通讯地址（英文）-城/镇
     * @apiSuccess (响应结果) {String} data.custInfoVO.mailingState 通讯地址（英文）-省/州
     * @apiSuccess (响应结果) {String} data.custInfoVO.mailingEnAddrDigest 通讯地址-英文详细地址摘要
     * @apiSuccess (响应结果) {String} data.custInfoVO.mailingEnAddrMask 通讯地址-英文详细地址掩码
     * @apiSuccess (响应结果) {String} data.custInfoVO.birthCountryCode 出生地-国家/地区代码
     * @apiSuccess (响应结果) {String} data.custInfoVO.birthProvCode 出生地-省代码
     * @apiSuccess (响应结果) {String} data.custInfoVO.birthCityCode 出生地-市代码
     * @apiSuccess (响应结果) {String} data.custInfoVO.birthCountyCode 出生地-县代码
     * @apiSuccess (响应结果) {String} data.custInfoVO.birthTown 出生地（英文）-城/镇
     * @apiSuccess (响应结果) {String} data.custInfoVO.birthState 出生地（英文）-省/州
     * @apiSuccess (响应结果) {String} data.custInfoVO.birthAddrDigest 出生地-详细地址摘要
     * @apiSuccess (响应结果) {String} data.custInfoVO.birthAddrMask 出生地-详细地址掩码
     * @apiSuccess (响应结果) {String} data.custInfoVO.assetCertDate 资产证明日期
     * @apiSuccess (响应结果) {String} data.custInfoVO.assetCertExpiredDate 资产证明有效期
     * @apiSuccess (响应结果) {String} data.custInfoVO.wealthSource 财富来源 01-薪金及/或花红 02-业务收入 03-退休金 04-礼物 05-储蓄 06-投资回报 07-遗赠 -08其他
     * @apiSuccess (响应结果) {String} data.custInfoVO.wealthSourceDesc 财富来源补充说明
     * @apiSuccess (响应结果) {String} data.custInfoVO.fundSourceCountry 资金来源地
     * @apiSuccess (响应结果) {String} data.custInfoVO.fundSourceCountryDesc 资金来源地补充说明
     * @apiSuccess (响应结果) {Number} data.custInfoVO.stimestamp 创建时间戳
     * @apiSuccess (响应结果) {Number} data.custInfoVO.updatedStimestamp 更新时间戳
     * @apiSuccess (响应结果) {Object} data.queryIndiTaxInfoVO 税务信息
     * @apiSuccess (响应结果) {String} data.queryIndiTaxInfoVO.emplStatus 就业状况 01-雇主 02-全职 03-兼职 04-主妇 05-学生 06-退休 07-非在职
     * @apiSuccess (响应结果) {String} data.queryIndiTaxInfoVO.emplCompanyName 就业公司名称
     * @apiSuccess (响应结果) {String} data.queryIndiTaxInfoVO.emplCountryCode 就业公司地址-国家/地区代码
     * @apiSuccess (响应结果) {String} data.queryIndiTaxInfoVO.emplProvCode 就业公司地址-省代码
     * @apiSuccess (响应结果) {String} data.queryIndiTaxInfoVO.emplCityCode 就业公司地址-市代码
     * @apiSuccess (响应结果) {String} data.queryIndiTaxInfoVO.emplCountyCode 就业公司地址-县代码
     * @apiSuccess (响应结果) {String} data.queryIndiTaxInfoVO.emplTown 就业公司地址（英文）-城/镇
     * @apiSuccess (响应结果) {String} data.queryIndiTaxInfoVO.emplState 就业公司地址（英文）-省/州
     * @apiSuccess (响应结果) {String} data.queryIndiTaxInfoVO.emplAddrDigest 就业公司地址-详细地址摘要
     * @apiSuccess (响应结果) {String} data.queryIndiTaxInfoVO.emplAddrMask 就业公司地址-详细地址掩码
     * @apiSuccess (响应结果) {String} data.queryIndiTaxInfoVO.emplNatureOfBusiness 就业公司业务性质
     * @apiSuccess (响应结果) {String} data.queryIndiTaxInfoVO.emplNatureOfBusinessDesc 就业公司业务性质说明
     * @apiSuccess (响应结果) {String} data.queryIndiTaxInfoVO.emplDesignation 就业职位/称衔
     * @apiSuccess (响应结果) {String} data.queryIndiTaxInfoVO.emplIncLevel 就业每年收入 01-≦HK$500000 02-HK$500001-HK$1000000 03-HK$1000001-HK$2000000 04-HK$2000001-HK$5000000 05->HK$5000000
     * @apiSuccess (响应结果) {String} data.queryIndiTaxInfoVO.taxJurisdiction 税务管辖区国家代码
     * @apiSuccess (响应结果) {String} data.queryIndiTaxInfoVO.hasTin 是否有税务编号 0-否 1-是
     * @apiSuccess (响应结果) {String} data.queryIndiTaxInfoVO.noTinReason 无税务编号理由 01-账户持有人的居留司法税务管辖区并没有向其居民发出税务编号 02-账户持有人不能取得税务编号 03-账户持有人无须提供税务编号
     * @apiSuccess (响应结果) {String} data.queryIndiTaxInfoVO.noObtainTinReason 不能取的税务编号的原因
     * @apiSuccess (响应结果) {String} data.queryIndiTaxInfoVO.tinType 税务编号类型 01-身份证 02-其他
     * @apiSuccess (响应结果) {String} data.queryIndiTaxInfoVO.tinTypeDesc 税务编号类型说明
     * @apiSuccess (响应结果) {String} data.queryIndiTaxInfoVO.tin 税务编号
     * @apiSuccess (响应结果) {Array} data.queryDeclarationInfoVOList 声明信息列表
     * @apiSuccess (响应结果) {Number} data.queryDeclarationInfoVOList.sortNum 声明序号
     * @apiSuccess (响应结果) {String} data.queryDeclarationInfoVOList.result 声明结果 0-否 1-是
     * @apiSuccess (响应结果) {String} data.queryDeclarationInfoVOList.fileId 声明文件ID
     * @apiSuccess (响应结果) {String} data.queryDeclarationInfoVOList.content 声明内容
     * @apiSuccess (响应结果) {Array} data.bankCardInfoVOList 银行卡信息
     * @apiSuccess (响应结果) {String} data.bankCardInfoVOList.hkCpAcctNo 香港资金账号
     * @apiSuccess (响应结果) {String} data.bankCardInfoVOList.bankCode 银行代码
     * @apiSuccess (响应结果) {String} data.bankCardInfoVOList.mainlandBankCode 大陆银行编码
     * @apiSuccess (响应结果) {String} data.bankCardInfoVOList.bankName 银行名称
     * @apiSuccess (响应结果) {String} data.bankCardInfoVOList.bankChineseName 银行中文名称
     * @apiSuccess (响应结果) {String} data.bankCardInfoVOList.bankRegionCode 联行号
     * @apiSuccess (响应结果) {String} data.bankCardInfoVOList.bankAcctDigest 摘要-银行账号
     * @apiSuccess (响应结果) {String} data.bankCardInfoVOList.bankAcctMask 掩码-银行账号
     * @apiSuccess (响应结果) {String} data.bankCardInfoVOList.bankAcctName 银行账户名称
     * @apiSuccess (响应结果) {String} data.bankCardInfoVOList.bankAcctStatus 银行账户状态0-正常 1-注销
     * @apiSuccess (响应结果) {String} data.bankCardInfoVOList.defaultAccount 默认账号0-否 1-是
     * @apiSuccess (响应结果) {String} data.bankCardInfoVOList.curCode 币种
     * @apiSuccess (响应结果) {Array} data.bankCardInfoVOList.currencyCodes 币种代码列表
     * @apiSuccess (响应结果) {String} data.bankCardInfoVOList.swiftCode SWIFT代码
     * @apiSuccess (响应结果) {String} data.bankCardInfoVOList.jointAccount 是否为联名账户 0-否 1-是
     * @apiSuccess (响应结果) {String} data.bankCardInfoVOList.correspondentSwiftCode 代理银行swift编码
     * @apiSuccess (响应结果) {String} data.bankCardInfoVOList.correspondentBankCode 代理银行代码
     * @apiSuccess (响应结果) {String} data.bankCardInfoVOList.correspondentMainlandBankCode 代理银行大陆银行编码
     * @apiSuccess (响应结果) {String} data.bankCardInfoVOList.correspondentBankName 代理银行银行名称
     * @apiSuccess (响应结果) {String} data.bankCardInfoVOList.correspondentBankChineseName 代理银行中文名称
     * @apiSuccess (响应结果) {String} data.bankCardInfoVOList.correspondentBankAcctDigest 代理银行账户-摘要
     * @apiSuccess (响应结果) {String} data.bankCardInfoVOList.correspondentBankAcctMask 代理银行账户-掩码
     * @apiSuccess (响应结果) {String} data.bankCardInfoVOList.depositsVefyStat 入金验证状态 0-未验证 1-已验证
     * @apiSuccess (响应结果) {Number} data.bankCardInfoVOList.stimestamp 创建时间戳
     * @apiSuccess (响应结果) {Object} data.kycInfoVO Kyc信息
     * @apiSuccess (响应结果) {String} data.kycInfoVO.riskToleranceLevel 风险承受能力 1-低风险等级 2-中低风险等级 3-中风险等级 4-中高风险等级 5-高风险等级
     * @apiSuccess (响应结果) {String} data.kycInfoVO.derivativeKnowledge 衍生金融工具知识0-无 1-有
     * @apiSuccess (响应结果) {String} data.kycInfoVO.riskToleranceDate 风测日期
     * @apiSuccess (响应结果) {String} data.kycInfoVO.riskToleranceTerm 风险评测过期日期 YYYYMMDD
     * @apiSuccess (响应结果) {Number} data.kycInfoVO.riskToleranceScore 分数
     * @apiSuccess (响应结果) {Object} data.hkCustHboneRelVO 香港客户一账通关系
     * @apiSuccess (响应结果) {String} data.hkCustHboneRelVO.hkCustNo 香港客户号
     * @apiSuccess (响应结果) {String} data.hkCustHboneRelVO.hboneNo 一账通号
     * @apiSuccessExample 响应结果示例
     * {"code":"JtYdICW","data":{"queryDeclarationInfoVOList":[{"result":"M","sortNum":1883,"content":"dp","fileId":"In3ovu"}],"queryIndiTaxInfoVO":{"emplTown":"kh8X7","emplState":"x6fcY5Wy3","emplAddrMask":"qfrMJy6WRv","emplStatus":"PkZn","emplIncLevel":"ZBXNa0ni","emplAddrDigest":"djoBMH","emplNatureOfBusinessDesc":"xCu9NO7D","tinType":"B4zdZsA","emplDesignation":"5J","emplCountyCode":"h7","tinTypeDesc":"bHU3sxQR7","emplCityCode":"oC88f25","noTinReason":"yT5RETYLX","tin":"Sx1qrdTPy","emplCompanyName":"lniFpLgISN","emplProvCode":"q","emplNatureOfBusiness":"BpceEAz","hasTin":"xZJS","emplCountryCode":"J","noObtainTinReason":"xhSbM4aN","taxJurisdiction":"ToXy"},"hkCustHboneRelVO":{"hkCustNo":"yaw49","hboneNo":"OohkrSWl9a"},"bankCardInfoVOList":[{"bankRegionCode":"jc25PT","correspondentSwiftCode":"Rfp","hkCpAcctNo":"uGax","bankCode":"WmB1N7IGHA","correspondentBankName":"KmCWJdBWsj","bankChineseName":"O4i","correspondentBankAcctMask":"yb","mainlandBankCode":"9yBz9","bankAcctName":"DS35","bankAcctMask":"3f3","currencyCodes":["AZxQZPJEBZ"],"stimestamp":*************,"swiftCode":"tAT20Le88q","bankName":"OCxmE","defaultAccount":"TqsRE","correspondentBankChineseName":"wvN","bankAcctDigest":"6T8w","bankAcctStatus":"KdSEf","jointAccount":"r","correspondentMainlandBankCode":"9v7","correspondentBankAcctDigest":"qeBccqc","depositsVefyStat":"Wa0hg3L9","correspondentBankCode":"xLx61q","curCode":"AghLfK54N"}],"custInfoVO":{"residenceState":"R2Ar7W","mailingEnAddrDigest":"5h","updatedStimestamp":*************,"statementEmailMask":"hn3","residenceEnCounty":"DqYPV","residenceCountyCode":"p6","enFormerName":"hU0gwZ","residenceProvCode":"lRmBzo","birthProvCode":"9","mailingEnCountry":"SizMGKMZh","cnSurname":"bevLWDNos","ebrokerId":"35yTLJ80","mailingEnProv":"n5kaafrx","idValidityEnd":"q","birthState":"ZjdUdTZ1","birthTown":"nIOpOpt7b6","mailingProvCode":"3MY4","registerDt":"4H86MmA4B","custChineseName":"9WkhbjQ","residenceCnAddrDigest":"PNLF","mailingState":"5obW","mailingEnCity":"boSSSxf","investorQualification":"g","custEnName":"aeY0T","birthAddrDigest":"mXkrNDO","idType":"ndUtF9iD3","custLoginPasswdType":"FG4ugl","mailingCountryCode":"ngin1WWqo","eduLevel":"5tk","marriageStatDesc":"n8e4I","residenceEnCity":"a99","fundSourceCountry":"0MkW","cnFormerName":"mVkQ0kcO","wealthSource":"teHT3ka3jY","idSignAreaCode":"7Blen","residenceEnAddrMask":"rgR0nvSK","nationality":"g32t","enGivenName":"o2JEoHFp","marriageStat":"qHPtCc","mailingCountyCode":"a","mailingCnAddrMask":"dK7fqfDOzv","assetCertExpiredDate":"m","mobileMask":"LWhO","birthday":"loIeQGf","birthAddrMask":"MwWKRbfmc","mailingCityCode":"2FwoA","gender":"RYmUfLX","residenceEnAddrDigest":"U","residenceCountryCode":"ttNucZQd","stimestamp":1644121713583,"mailingTown":"V","residenceTown":"2l55yXwLT0","mobileVerifyStatus":"w14S2i5oRs","natureOfBusiness":"oaxGrETwD","openType":"oc","mobileAreaCode":"uuY5Hvou","statementEmailDigest":"6NjOO","residenceEnCountry":"rP","custTxPasswdType":"PE5d","idImageUploadStatus":"7","investorQualificationDate":"y","invstType":"V5HlMua7","mobileDigest":"T7yXcviVdX","companyRegCountry":"N5","residenceEnProv":"CQMW","birthCountyCode":"gNAjas","residenceCityCode":"8zGYf","birthCityCode":"mTERa9k","residenceCnAddrMask":"j","idAlwaysValidFlag":"2s","birthCountryCode":"V9","hkCustNo":"QMaYA","mailingCnAddrDigest":"1HBBZAK7C","idNoDigest":"7tLblNeZNV","emailDigest":"huXlUb7","emailVerifyStatus":"vcKzLcCF","custName":"l4h","mailingEnAddrMask":"A6NWHM","idNoMask":"WXKhKqBWI","vocation":"O","closeDate":"Q","companyRegDate":"5KT","mailingEnCounty":"6N","enSurname":"k4sqiS","businessRegistrationNumber":"pMi3lX","emailMask":"n4q1U1Kx2c","assetCertDate":"vSllyIvY15","wealthSourceDesc":"0W7Ndt","custState":"FCMt","openDate":"IcCGW6","cnGivenName":"Z0b","fundSourceCountryDesc":"6QT"},"kycInfoVO":{"riskToleranceLevel":"2QeozQW","derivativeKnowledge":"B","riskToleranceDate":"X","riskToleranceScore":6014.009167354872,"riskToleranceTerm":"YAH2f70pNa"}},"description":"BU1qcR3"}
     */
    @ResponseBody
    @GetMapping("/queryHkCustAllInfoByHkTxAcctNo")
    public Response<HkAcctCustInfoHolderVO> queryHkCustAllInfoByHkTxAcctNo(@RequestParam(name = "hkTxAcctNo") String hkTxAcctNo) {
        return Response.ok(hkCustInfoService.queryHkCustAllInfoByHkTxAcctNo(hkTxAcctNo));
    }

    /**
     * @api {GET} /hkcustinfo/queryHkOpenAcctResultByHkTxAcctNo queryHkOpenAcctResultByHkTxAcctNo()
     * @apiVersion 1.0.0
     * @apiGroup HkCustInfoController
     * @apiName queryHkOpenAcctResultByHkTxAcctNo()
     * @apiParam (请求参数) {String} hkTxAcctNo
     * @apiParamExample 请求参数示例
     * hkTxAcctNo=wqA
     * @apiSuccess (响应结果) {String} code 状态码
     * @apiSuccess (响应结果) {String} description 描述信息
     * @apiSuccess (响应结果) {Object} data 数据封装
     * @apiSuccess (响应结果) {Object} data.openAcctRejectVO 开户拒绝信息 字段值为对应字段的拒绝原因
     * @apiSuccess (响应结果) {String} data.openAcctRejectVO.custChineseName 客户中文姓名
     * @apiSuccess (响应结果) {String} data.openAcctRejectVO.cnSurname 中文姓氏
     * @apiSuccess (响应结果) {String} data.openAcctRejectVO.cnGivenName 中文名
     * @apiSuccess (响应结果) {String} data.openAcctRejectVO.custEnName 英文姓名
     * @apiSuccess (响应结果) {String} data.openAcctRejectVO.enSurname 英文姓氏
     * @apiSuccess (响应结果) {String} data.openAcctRejectVO.enGivenName 英文名
     * @apiSuccess (响应结果) {String} data.openAcctRejectVO.gender 性别 0-女 1-男 2-非自然人
     * @apiSuccess (响应结果) {String} data.openAcctRejectVO.cnFormerName 曾用中文名
     * @apiSuccess (响应结果) {String} data.openAcctRejectVO.enFormerName 曾用英文名
     * @apiSuccess (响应结果) {String} data.openAcctRejectVO.birthday 出生日期
     * @apiSuccess (响应结果) {String} data.openAcctRejectVO.nationality 国籍
     * @apiSuccess (响应结果) {String} data.openAcctRejectVO.marriageStat 婚姻状况 0-未婚 1-已婚 2-其他
     * @apiSuccess (响应结果) {String} data.openAcctRejectVO.marriageStatDesc 婚姻状况描述
     * @apiSuccess (响应结果) {String} data.openAcctRejectVO.eduLevel 教育水平 01-小学或以下 02-中学 03-大专或预科 04-大学或本科 05-硕士或以上
     * @apiSuccess (响应结果) {String} data.openAcctRejectVO.mobileAreaCode 手机号码的地区编码
     * @apiSuccess (响应结果) {String} data.openAcctRejectVO.mobile 手机号码
     * @apiSuccess (响应结果) {String} data.openAcctRejectVO.email 电子邮件地址
     * @apiSuccess (响应结果) {String} data.openAcctRejectVO.statementEmail 对账电子邮件地址
     * @apiSuccess (响应结果) {String} data.openAcctRejectVO.idSignAreaCode 证件签发地区编码
     * @apiSuccess (响应结果) {String} data.openAcctRejectVO.idType 身份证类型
     * @apiSuccess (响应结果) {String} data.openAcctRejectVO.idNo 身份证号码
     * @apiSuccess (响应结果) {String} data.openAcctRejectVO.idAlwaysValidFlag 证件是否长期有效
     * @apiSuccess (响应结果) {String} data.openAcctRejectVO.idValidityEnd 身份证有效期结束时间
     * @apiSuccess (响应结果) {String} data.openAcctRejectVO.residenceCountryCode 现住址国家编码
     * @apiSuccess (响应结果) {String} data.openAcctRejectVO.residenceEnCountry 现住址英文国家名称
     * @apiSuccess (响应结果) {String} data.openAcctRejectVO.mailingCountryCode 通讯地址国家编码
     * @apiSuccess (响应结果) {String} data.openAcctRejectVO.mailingEnCountry 通讯地址英文国家名称
     * @apiSuccess (响应结果) {String} data.openAcctRejectVO.birthCountryCode 出生地国家编码
     * @apiSuccess (响应结果) {String} data.openAcctRejectVO.bankName 银行名称
     * @apiSuccess (响应结果) {String} data.openAcctRejectVO.jointAccount 是否为联名账户 0-否 1-是
     * @apiSuccess (响应结果) {String} data.openAcctRejectVO.bankAcct 银行账户
     * @apiSuccess (响应结果) {String} data.openAcctRejectVO.bankAcctName 银行账户名称
     * @apiSuccess (响应结果) {String} data.openAcctRejectVO.swiftCode SWIFT代码
     * @apiSuccess (响应结果) {String} data.openAcctRejectVO.correspondentSwiftCode 代理银行SWIFT编码
     * @apiSuccess (响应结果) {String} data.openAcctRejectVO.correspondentBankName 代理银行名称
     * @apiSuccess (响应结果) {String} data.openAcctRejectVO.correspondentBankAcct 代理银行账户
     * @apiSuccess (响应结果) {String} data.openAcctRejectVO.currencyCodes 币种代码列表
     * @apiSuccess (响应结果) {Object} data.openAcctRejectVO.declarationInfo 声明信息列表   key对应 DeclarationInfoDTO.declarationSort  value对应 拒绝信息
     * @apiSuccess (响应结果) {String} data.openAcctRejectVO.emplStatus 就业状态 01-雇主 02-全职 03-兼职 04-主妇 05-学生 06-退休 07-非在职
     * @apiSuccess (响应结果) {String} data.openAcctRejectVO.emplCompanyName 就业公司名称
     * @apiSuccess (响应结果) {String} data.openAcctRejectVO.emplCountryCode 就业国家代码
     * @apiSuccess (响应结果) {String} data.openAcctRejectVO.emplNatureOfBusiness 就业业务性质
     * @apiSuccess (响应结果) {String} data.openAcctRejectVO.emplNatureOfBusinessDesc 就业业务性质描述
     * @apiSuccess (响应结果) {String} data.openAcctRejectVO.emplDesignation 就业职位
     * @apiSuccess (响应结果) {String} data.openAcctRejectVO.emplIncLevel 就业年收入      01-≦HK$500,000      02-HK$500,001 - HK$1,000,000      03-HK$1,000,001 - HK$2,000,000      04-HK$2,000,001 - HK$5,000,000      05->HK$5,000,000
     * @apiSuccess (响应结果) {String} data.openAcctRejectVO.taxJurisdiction 税务管辖权
     * @apiSuccess (响应结果) {String} data.openAcctRejectVO.hasTin 是否有税务编号
     * @apiSuccess (响应结果) {String} data.openAcctRejectVO.noTinReason 未获取TIN的原因      01-账户持有人的居留司法税务管辖区并没有向其居民发出税务编号。      02-账户持有人不能取得税务编号。      03-账户持有人无须提供税务编号。
     * @apiSuccess (响应结果) {String} data.openAcctRejectVO.noObtainTinReason 未取得TIN的原因
     * @apiSuccess (响应结果) {String} data.openAcctRejectVO.tinType TIN类型 01-身份证 02-其他
     * @apiSuccess (响应结果) {String} data.openAcctRejectVO.tinTypeDesc TIN类型说明
     * @apiSuccess (响应结果) {String} data.openAcctRejectVO.tin TIN号码
     * @apiSuccess (响应结果) {String} data.openAcctRejectVO.investorQualification 投资者类型 PRO-投资者资质专业 NORMAL-投资者资质普通
     * @apiSuccess (响应结果) {String} data.openAcctRejectVO.esignPicturePaths 电子签名文件路径
     * @apiSuccess (响应结果) {String} data.openAcctRejectVO.assetCertDate 资产证明日期（审核人用）
     * @apiSuccess (响应结果) {String} data.txChkFlag 审核状态 0-不需审核 1-等待审核 2-等待复核 3-审核通过 4-审核不通过/作废 5-驳回至初审 6-驳回至客户
     * @apiSuccess (响应结果) {String} data.extInfo 扩展信息
     * @apiSuccessExample 响应结果示例
     * {"code":"Nx","data":{"openAcctRejectVO":{"correspondentSwiftCode":"eiJgnx0Ol","currencyCodes":"CJL5","emplNatureOfBusinessDesc":"6","enFormerName":"d","mailingEnCountry":"Be","idNo":"CCJEA4","cnSurname":"0EQTu0","tinTypeDesc":"VwgSodP1x","jointAccount":"NnN","idValidityEnd":"W2tT","correspondentBankAcct":"1k0t2CCY","tin":"NVEBFHzETp","custChineseName":"y5YPM","emplNatureOfBusiness":"intozG3","hasTin":"viSB7bcbWJ","investorQualification":"XJxp","custEnName":"7FnECXZW","correspondentBankName":"YEXyMPl","idType":"5pxxoav","mailingCountryCode":"NrirllWZP","eduLevel":"M4gVOINV5","marriageStatDesc":"jjpZ9W","cnFormerName":"jfBquIh","statementEmail":"M","idSignAreaCode":"7zOr6T","enGivenName":"FXe","nationality":"hzUK","marriageStat":"NgVg31M8x0","emplCompanyName":"vBrA8cB","taxJurisdiction":"f4sJwJ","birthday":"n3b","emplStatus":"262","gender":"8syZAR","residenceCountryCode":"TE7tiot","tinType":"snw09eKId","bankAcct":"YK7GDNGrI","swiftCode":"U","emplDesignation":"DOS12TEOP","bankName":"T","mobileAreaCode":"dN","residenceEnCountry":"cT","noObtainTinReason":"XRFtKg","email":"hVKtg7M","emplIncLevel":"hT1YIUS","bankAcctName":"E","idAlwaysValidFlag":"7PkiFT32DJ","birthCountryCode":"R","declarationInfo":{},"mobile":"AdS","enSurname":"ZT2S8","noTinReason":"K","assetCertDate":"VF","esignPicturePaths":"bRqJYCjwF","cnGivenName":"OL","emplCountryCode":"RE19w6aP"},"txChkFlag":"R1LWGCT","extInfo":"DgKhm5"},"description":"sxRD0p"}
     */
    @ResponseBody
    @GetMapping("/queryHkOpenAcctResultByHkTxAcctNo")
    public Response<HkOpenAcctResultVO> queryHkOpenAcctResultByHkTxAcctNo(String hkTxAcctNo) {
        return Response.ok(hkCustInfoService.queryHkOpenAcctResultByHkTxAcctNo(hkTxAcctNo));
    }


    /**
     * @api {POST} /hkcustinfo/queryHkDepositResult queryHkDepositResult()
     * @apiVersion 1.0.0
     * @apiGroup HkCustInfoController
     * @apiName queryHkDepositResult()
     * @apiDescription 香港入金记录查询
     * @apiParam (请求体) {String} hkCustNo
     * @apiParam (请求体) {String} depositType
     * @apiParam (请求体) {Number} page 页号
     * @apiParam (请求体) {Number} rows 每页大小
     * @apiParamExample 请求体示例
     * {"hkCustNo":"j","page":5871,"rows":8420,"depositType":"OuVKewGgvN"}
     * @apiSuccess (响应结果) {String} code 状态码
     * @apiSuccess (响应结果) {String} description 描述信息
     * @apiSuccessExample 响应结果示例
     * {"code":"pljGWka","description":"be"}
     */
    @ResponseBody
    @PostMapping("/queryHkDepositResult")
    public Response<PageVO<HkDepositResultVO>> queryHkDepositResult(@RequestBody HkDepositRequest hkDepositRequest) {
        return Response.ok(hkCustInfoService.queryHkDepositResult(hkDepositRequest));
    }


    /**
     * @api {POST} /hkcustinfo/createCustInfoByHk createCustInfoByHk()
     * @apiVersion 1.0.0
     * @apiGroup HkCustInfoController
     * @apiName createCustInfoByHk()
     * @apiDescription 根据香港客户号，创建crm客户信息
     * @apiParam (请求体) {String} hkTxAcctNo 香港客户号
     * @apiParam (请求体) {String} operateSource 操作来源 [1-MQ]时，为[异常来源]，参考枚举类[{@link com.howbuy.crm.account.client.enums.custinfo.FullCustSourceEnum}]      [2-菜单页面]时，为菜单名称
     * @apiParam (请求体) {String} operateChannel operateChannel 操作通道 1-MQ  2-菜单页面
     * @apiParam (请求体) {String} custSource 客户来源-来源编号      {@link com.howbuy.crm.account.client.enums.custinfo.CustSourceCodeEnum}
     * @apiParamExample 请求体示例
     * {"hkTxAcctNo":"S9UOkKM","custSource":"lE1rUFpa","operateChannel":"rd","operateSource":"tsp"}
     * @apiSuccess (响应结果) {String} code 状态码
     * @apiSuccess (响应结果) {String} description 描述信息
     * @apiSuccess (响应结果) {String} data 数据封装
     * @apiSuccessExample 响应结果示例
     * {"code":"kAs","data":"Ome2","description":"5Ia4Gj"}
     */
    @ResponseBody
    @PostMapping("/createCustInfoByHk")
    public Response<String> createCustInfoByHk(@RequestBody CreateCustByHkRequest createCustByHkRequest) {
        return hkCustInfoService.createCustInfoByHk(createCustByHkRequest);
    }
}