/**
 * Copyright (c) 2023, <PERSON><PERSON><PERSON><PERSON> HOWBUY INVESTMENT MANAGEMENT Co., Ltd.
 * All right reserved.
 * <p>
 * THIS IS UNPUBLISHED PROPRIETARY SOURCE CODE OF HOWBUY INVESTMENT
 * MANAGEMENT CO., LTD.  THE CONTENTS OF THIS FILE MAY NOT BE DISCLOSED
 * TO THIRD PARTIES, COPIED OR DUPLICATED IN ANY FORM, IN WHOLE OR IN PART,
 * WITHOUT THE PRIOR WRITTEN PERMISSION OF HOWBUY INVESTMENT MANAGEMENT
 * CO., LTD.
 */
package com.howbuy.crm.account.service.commom.filter.trace;

import com.howbuy.common.utils.StringUtil;
import org.apache.dubbo.rpc.RpcContext;

/**
 * @description: dubbo调用链参数设置
 * <AUTHOR>
 * @date 2023/6/7 08:50
 * @since JDK 1.8
 */
public class RequestChainTrace {

    private RequestChainTrace() {
    }

    private static final ThreadLocal<TraceParam> THREAD_LOCAL = new InheritableThreadLocal<TraceParam>() {
        @Override
        protected TraceParam initialValue() {
            return new TraceParam();
        }
    };

    public static TraceParam get() {
        return THREAD_LOCAL.get();
    }

    public static void set(TraceParam traceParam) {
        THREAD_LOCAL.set(traceParam);
    }

    public static void remove() {
        THREAD_LOCAL.remove();
    }

    /**
     * @description:提取追踪信息
     * @return com.howbuy.crm.cgi.manager.filter.trace.TraceParam
     * @author: hongdong.xie
     * @date: 2023/6/7 09:04
     * @since JDK 1.8
     */
//    public static TraceParam extractTraces() {
//        TraceParam traceParam = new TraceParam();
//        String uuid = RpcContext.getContext().getAttachment(Constants.UUID);
//        if(!StringUtil.isEmpty(uuid)) {
//            String tokenId = RpcContext.getContext().getAttachment(Constants.TOKEN_ID);
//            String awakeId = RpcContext.getContext().getAttachment(Constants.AWAKEID);
//            String funcId = RpcContext.getContext().getAttachment(Constants.FUNCID);
//            String pageId = RpcContext.getContext().getAttachment(Constants.PAGEID);
//            String reqId = RpcContext.getContext().getAttachment(Constants.REQID);
//            traceParam.setTokenId(tokenId);
//            traceParam.setAwakeId(awakeId);
//            traceParam.setFuncId(funcId);
//            traceParam.setPageId(pageId);
//            traceParam.setReqId(reqId);
//        } else {
//            traceParam.build();
//        }
//
//        set(traceParam);
//
//        return traceParam;
//    }

    /**
     * @description: 统一设置dubbo链路参数
     * @param traceId
     * @return com.howbuy.crm.cgi.manager.filter.trace.TraceParam
     * @author: hongdong.xie
     * @date: 2023/6/7 11:28
     * @since JDK 1.8
     */
    public static TraceParam extractTraces(String traceId) {
        TraceParam traceParam = new TraceParam();
        if(!StringUtil.isEmpty(traceId)) {
            traceParam.setTokenId(traceId);
            traceParam.setAwakeId(traceId);
            traceParam.setFuncId(traceId);
            traceParam.setPageId(traceId);
            traceParam.setReqId(traceId);
        } else {
            traceParam.build();
        }

        set(traceParam);

        return traceParam;
    }

    /**
     * @description: 投递追踪信息
     * @return com.howbuy.crm.cgi.manager.filter.trace.TraceParam
     * @author: hongdong.xie
     * @date: 2023/6/7 09:04
     * @since JDK 1.8
     */
//    public static TraceParam deliverTraces() {
//        TraceParam traceParam = get();
//        if(traceParam == null) {
//            traceParam = new TraceParam().build();
//        }
//        RpcContext.getContext().setAttachment(Constants.TOKEN_ID, traceParam.getTokenId());
//        RpcContext.getContext().setAttachment(Constants.AWAKEID, traceParam.getAwakeId());
//        RpcContext.getContext().setAttachment(Constants.FUNCID, traceParam.getFuncId());
//        RpcContext.getContext().setAttachment(Constants.REQID, traceParam.getReqId());
//        // simple
//        RpcContext.getContext().setAttachment(Constants.UUID, traceParam.getReqId());
//
//        return traceParam;
//    }
}