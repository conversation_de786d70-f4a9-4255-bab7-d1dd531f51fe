/**
 * Copyright (c) 2023, Shang<PERSON><PERSON> HOWBUY INVESTMENT MANAGEMENT Co., Ltd.
 * All right reserved.
 * <p>
 * THIS IS UNPUBLISHED PROPRIETARY SOURCE CODE OF HOWBUY INVESTMENT
 * MANAGEMENT CO., LTD.  THE CONTENTS OF THIS FILE MAY NOT BE DISCLOSED
 * TO THIRD PARTIES, COPIED OR DUPLICATED IN ANY FORM, IN WHOLE OR IN PART,
 * WITHOUT THE PRIOR WRITTEN PERMISSION OF HOWBUY INVESTMENT MANAGEMENT
 * CO., LTD.
 */
package com.howbuy.crm.account.service.commom.filter.trace;

/**
 * <AUTHOR>
 * @description: (请在此添加描述)
 * @date 2023/6/7 08:53
 * @since JDK 1.8
 */
public class TradeParamLocalUtils {
    private static final ThreadLocal<TradeParamLocalUtils> LOCAL = new InheritableThreadLocal<TradeParamLocalUtils>() {

        @Override
        protected TradeParamLocalUtils initialValue() {
            return new TradeParamLocalUtils();

        }

    };

    /**
     * getTradeParam:(获取TradeParam)
     *
     * @return TradeParamLocal
     * <AUTHOR>
     * @date 2016年9月29日 下午4:55:36
     */
    public static TradeParamLocalUtils getTradeParam() {
        return LOCAL.get();
    }

    /**
     * removeTradeParam:(移除TradeParam)
     *
     * <AUTHOR>
     * @date 2016年9月29日 下午5:02:04
     */
    public static void removeTradeParam() {
        LOCAL.remove();
    }

    /**
     * 交易渠道号
     */
    private String txChannel;
    /**
     * 分销渠道号
     */
    private String disCode;
    /**
     * 网点号: app应用市场
     */
    private String outletCode;
    /**
     * 接口申请日期
     */
    private String appDt;
    /**
     * 接口申请时间
     */
    private String appTm;
    /**
     * 客户IP地址
     */
    private String custIp;

    public static String getTxChannel() {
        return TradeParamLocalUtils.getTradeParam().txChannel;
    }

    public static void setTxChannel(String txChannel) {
        TradeParamLocalUtils.getTradeParam().txChannel = txChannel;
    }

    public static String getDisCode() {
        return TradeParamLocalUtils.getTradeParam().disCode;
    }

    public static void setDisCode(String disCode) {
        TradeParamLocalUtils.getTradeParam().disCode = disCode;
    }

    public static String getOutletCode() {
        return TradeParamLocalUtils.getTradeParam().outletCode;
    }

    public static void setOutletCode(String outletCode) {
        TradeParamLocalUtils.getTradeParam().outletCode = outletCode;
    }

    public static String getAppDt() {
        return TradeParamLocalUtils.getTradeParam().appDt;
    }

    public static void setAppDt(String appDt) {
        TradeParamLocalUtils.getTradeParam().appDt = appDt;
    }

    public static String getAppTm() {
        return TradeParamLocalUtils.getTradeParam().appTm;
    }

    public static void setAppTm(String appTm) {
        TradeParamLocalUtils.getTradeParam().appTm = appTm;
    }

    public static String getCustIp() {
        return TradeParamLocalUtils.getTradeParam().custIp;
    }

    public static void setCustIp(String custIp) {
        TradeParamLocalUtils.getTradeParam().custIp = custIp;
    }
}