/**
 * Copyright (c) 2024, <PERSON><PERSON><PERSON><PERSON> HOWBUY INVESTMENT MANAGEMENT Co., Ltd.
 * All right reserved.
 * <p>
 * THIS IS UNPUBLISHED PROPRIETARY SOURCE CODE OF HOWBUY INVESTMENT
 * MANAGEMENT CO., LTD.  THE CONTENTS OF THIS FILE MAY NOT BE DISCLOSED
 * TO THIRD PARTIES, COPIED OR DUPLICATED IN ANY FORM, IN WHOLE OR IN PART,
 * WITHOUT THE PRIOR WRITTEN PERMISSION OF HOWBUY INVESTMENT MANAGEMENT
 * CO., LTD.
 */
package com.howbuy.crm.account.service.facade.beisen;

import com.howbuy.crm.account.client.facade.beisen.CmBeisenPosLevelConfigFacade;
import com.howbuy.crm.account.client.request.beisen.BeisenPosLevelConfigRequest;
import com.howbuy.crm.account.client.response.ExportToFileVO;
import com.howbuy.crm.account.client.response.Response;
import com.howbuy.crm.account.client.response.beisen.CmBeisenPosLevelConfigListVO;
import com.howbuy.crm.account.client.response.beisen.CmBeisenPosLevelConfigVO;
import com.howbuy.crm.account.service.service.beisen.CmBeisenPosLevelConfigService;
import org.apache.dubbo.config.annotation.DubboService;

import javax.annotation.Resource;

/**
 * @description: (好买北森职级映射facade实现)
 * <AUTHOR>
 * @date 2024/10/24 13:37
 * @since JDK 1.8
 */
@DubboService
public class CmBeisenPosLevelConfigFacadeImpl implements CmBeisenPosLevelConfigFacade {
    @Resource
    private CmBeisenPosLevelConfigService cmBeisenPosLevelConfigService;

    /**
     * @description:(查询北森职级映射集合)
     * @param request
     * @return com.howbuy.crm.account.client.response.Response<com.howbuy.crm.account.client.response.beisen.CmBeisenPosLevelConfigListVO>
     * @author: shijie.wang
     * @date: 2024/11/14 17:00
     * @since JDK 1.8
     */
    @Override
    public Response<CmBeisenPosLevelConfigListVO> queryBeisenPosLevelConfigList(BeisenPosLevelConfigRequest request) {
        return Response.ok(cmBeisenPosLevelConfigService.queryBeisenPosLevelConfigList(request));
    }

    /**
     * @description:(保存北森职级配置数据)
     * @param request
     * @return com.howbuy.crm.account.client.response.Response<java.lang.String>
     * @author: shijie.wang
     * @date: 2024/11/14 17:00
     * @since JDK 1.8
     */
    @Override
    public Response<String> saveBeisenPosLevelConfig(BeisenPosLevelConfigRequest request) {
        return Response.ok(cmBeisenPosLevelConfigService.saveBeisenPosLevelConfig(request));
    }

    /**
     * @description:(删除北森职级配置数据)
     * @param request
     * @return com.howbuy.crm.account.client.response.Response<java.lang.String>
     * @author: shijie.wang
     * @date: 2024/11/14 17:00
     * @since JDK 1.8
     */
    @Override
    public Response<String> deleteBeisenPosLevelConfig(BeisenPosLevelConfigRequest request) {
        return Response.ok(cmBeisenPosLevelConfigService.deleteBeisenPosLevelConfig(request));
    }

    /**
     * @description:(查询北森职级配置详细数据)
     * @param request
     * @return com.howbuy.crm.account.client.response.Response<com.howbuy.crm.account.client.response.beisen.CmBeisenPosLevelConfigVO>
     * @author: shijie.wang
     * @date: 2024/11/14 17:00
     * @since JDK 1.8
     */
    @Override
    public Response<CmBeisenPosLevelConfigVO> queryBeisenPosLevelConfigDetail(BeisenPosLevelConfigRequest request) {
        return Response.ok(cmBeisenPosLevelConfigService.queryBeisenPosLevelConfigDetail(request));
    }

    /**
     * @description:(导出北森职级配置数据)
     * @param request
     * @return com.howbuy.crm.account.client.response.Response<com.howbuy.crm.account.client.response.ExportToFileVO>
     * @author: shijie.wang
     * @date: 2024/11/14 17:00
     * @since JDK 1.8
     */
    @Override
    public Response<ExportToFileVO> exportBeisenPosLevelConfigList(BeisenPosLevelConfigRequest request) {
        return Response.ok(cmBeisenPosLevelConfigService.export(request));
    }
}