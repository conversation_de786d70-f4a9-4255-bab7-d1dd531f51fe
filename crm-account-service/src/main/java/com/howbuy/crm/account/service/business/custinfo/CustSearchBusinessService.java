/**
 * Copyright (c) 2023, <PERSON><PERSON><PERSON><PERSON> HOWBUY INVESTMENT MANAGEMENT Co., Ltd.
 * All right reserved.
 * <p>
 * THIS IS UNPUBLISHED PROPRIETARY SOURCE CODE OF HOWBUY INVESTMENT
 * MANAGEMENT CO., LTD.  THE CONTENTS OF THIS FILE MAY NOT BE DISCLOSED
 * TO THIRD PARTIES, COPIED OR DUPLICATED IN ANY FORM, IN WHOLE OR IN PART,
 * WITHOUT THE PRIOR WRITTEN PERMISSION OF HOWBUY INVESTMENT MANAGEMENT
 * CO., LTD.
 */
package com.howbuy.crm.account.service.business.custinfo;

import com.google.common.collect.Lists;
import com.howbuy.common.utils.Assert;
import com.howbuy.common.utils.StringUtil;
import com.howbuy.crm.account.client.enums.custinfo.AbnormaSceneTypeEnum;
import com.howbuy.crm.account.client.enums.custinfo.ConsCustInvestTypeEnum;
import com.howbuy.crm.account.dao.bo.custinfo.CmConscustForAnalyseBO;
import com.howbuy.crm.account.service.commom.utils.AbnormalCustUtils;
import com.howbuy.crm.account.service.repository.custinfo.AbnormalCustRepository;
import com.howbuy.crm.account.service.vo.custinfo.AbnormalAnalyseResultVO;
import com.howbuy.crm.account.service.vo.custinfo.MessageCustInfoVO;
import com.howbuy.crm.account.service.vo.custinfo.SearchCustKeyAttrVO;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;

import static com.howbuy.crm.account.service.commom.utils.AbnormalCustUtils.distinctList;

/**
 * @description: ([账户]-[公共 复杂搜索 处理服务])
 * <AUTHOR>
 * @date 2023/12/18 10:31
 * @since JDK 1.8
 */
@Service
@Slf4j
public  class CustSearchBusinessService {

    @Autowired
    private AbnormalCustRepository abnormalCustRepository;





    /**
     * @description:(根据消息 模糊查询 是否捕获到 唯一的一条待处理 客户信息)
     * @param analyseVo	
     * @param searchAttrInfo	
     * @return com.howbuy.crm.account.service.vo.custinfo.AbnormalAnalyseResultVO<T>
     * @author: haoran.zhang
     * @date: 2024/1/12 18:13
     * @since JDK 1.8
     */ 
    public <T extends MessageCustInfoVO>   AbnormalAnalyseResultVO<T> searchMatchedCust(T analyseVo,
                                                                                        SearchCustKeyAttrVO searchAttrInfo){
        ConsCustInvestTypeEnum investTypeEnum=ConsCustInvestTypeEnum.getEnum(searchAttrInfo.getInvstType());
        Assert.notNull(investTypeEnum);

        if(ConsCustInvestTypeEnum.INSTITUTION==investTypeEnum || ConsCustInvestTypeEnum.PRODUCT==investTypeEnum){
            //机构客户处理
            return searchMatchedInstitutionCust(analyseVo,searchAttrInfo);
        }else{
            //个人客户处理
            return searchMatchedPersonCust(analyseVo,searchAttrInfo);
        }

    }


    /**
     * @description:(根据消息 模糊查询 是否捕获到 唯一的一条待处理 客户信息 .适用于 个人客户)
     * @param analyseVo
     * @param searchAttrInfo
     * @return com.howbuy.crm.account.service.vo.custinfo.AbnormalAnalyseResultVO<T>
     * @author: haoran.zhang
     * @date: 2024/1/12 18:15
     * @since JDK 1.8
     */
    public <T extends MessageCustInfoVO>   AbnormalAnalyseResultVO<T> searchMatchedPersonCust(T analyseVo,
                                                                                        SearchCustKeyAttrVO searchAttrInfo){

        AbnormalAnalyseResultVO<T> resultVo=AbnormalAnalyseResultVO.normalData(analyseVo);

        //TODO: 一定会有 手机信息 、证件信息吗？ 有可能为空吗？
        //手机 查询
        List<CmConscustForAnalyseBO> mobileMatchList=abnormalCustRepository.queryCustListByMobile(searchAttrInfo.getInvstType(),
                searchAttrInfo.getMobileAreaCode(),searchAttrInfo.getMobileDigest());
        //证件查询
        List<CmConscustForAnalyseBO> idNoMatchList=abnormalCustRepository.queryCustListByIdNo(searchAttrInfo.getInvstType(),
                searchAttrInfo.getIdType(),searchAttrInfo.getIdSignAreaCode(),searchAttrInfo.getIdNoDigest());
//        1）匹配逻辑，满足以下任一条件，取并集后按投顾客户号去重：
//        ①条件1：匹配到的【投顾客户号】的【证件类型+证件号】相同
//        ②条件2：匹配到的【投顾客户号】的【手机区号+手机号】相同
        List<CmConscustForAnalyseBO> repeatList= AbnormalCustUtils.dealDistinct(Lists.newArrayList(mobileMatchList,idNoMatchList));

//        场景4：匹配不到投顾客户号
//        判断条件：去重后的投顾客户号=0，后续处理： 则  新增客户，
        if(repeatList.isEmpty()){
            return resultVo;
        }

        //        场景1：存在重复客户
//        判断条件：去重后的投顾客户号＞1，则存在重复客户（此时不自动绑定【香港客户号】和【投顾客户号】，流程中止），异常数据进“香港异常客户表”：
//        匹配异常的【香港客户号】落“香港异常客户主表”，写入字段
//        a、异常来源：香港开户
//        b、异常描述：匹配到多个投顾客户号
//        c、异常时间：取落入异常表的时间，格式yyyymmdd hh:mm:ss
//        匹配到的全部【投顾客户号】落“香港异常客户待关联表”，与主表数据相对应。
        if(repeatList.size()>1){
            return AbnormalAnalyseResultVO.notNormalData(analyseVo,
                    AbnormaSceneTypeEnum.MATCH_MULTIPLE_CUST_NO,
                    repeatList);
        }
//        场景2：匹配异常
//        判断条件：去重后的投顾客户号=1，且满足以下任一条件，则认为匹配异常（此时不自动绑定【香港客户号】和【投顾客户号】），异常数据进“香港异常客户表”：
//①条件1：匹配到的【投顾客户号】的【手机区号+手机号】相同，但【证件类型+证件号】证件不同/或为空
//②条件2：匹配到的【投顾客户号】的【证件类型+证件号】相同，但【手机区号+手机号】不同
//        匹配异常的【香港客户号】落“香港异常客户主表”，写入字段
//        a、异常来源：香港开户
//        b、异常描述：根据不同的场景，写入不同的值
//
//①若仅匹配到1个投顾客户号，且满足上述条件1，则写入：手机号相同，证件类型/证件号不匹配
//②若仅匹配到1个投顾客户号，且满足上述条件2，则写入：证件相同，但手机号不匹配
//        c、异常时间：取落入异常表的时间，格式yyyymmdd hh:mm:ss
//        匹配到的全部【投顾客户号】落“香港异常客户待关联表”，与主表数据相对应。
        if(repeatList.size()==1){
            //分析：repeatList是 根据 手机、或 证件 查询的。所以以下的不同，一定会有能进入的。
            CmConscustForAnalyseBO cust=repeatList.get(0);
            if(     StringUtil.isNotBlank(cust.getMobileDigest())  &&
                    !isMobileInfoSame(searchAttrInfo,cust)){
                return AbnormalAnalyseResultVO.notNormalData(analyseVo,
                        AbnormaSceneTypeEnum.ID_TYPE_ID_NO_SAME_PHONE_NOT_MATCH,
                        repeatList);
            }
            if(!isIdNoInfoSame(searchAttrInfo,cust)){
                return AbnormalAnalyseResultVO.notNormalData(analyseVo,
                        AbnormaSceneTypeEnum.PHONE_SAME_ID_TYPE_ID_NO_NOT_MATCH,
                        repeatList);
            }
        }

        CmConscustForAnalyseBO  matchedOneCust=null;
//        场景3：匹配正常
//        判断条件：去重后的投顾客户号=1，且满足以下任一条件，则认为匹配正常，并继续判断③
//①条件1：【手机区号+手机号、证件类型+证件号】四要素完全相同
//②条件2：匹配到的【投顾客户号】的【证件类型+证件号】相同，但手机号为空
        if(repeatList.size()==1){
            //是否能匹配到唯一的【投顾客户】
            matchedOneCust=getMatchedSingleCust(searchAttrInfo,repeatList);
        }
        resultVo.setProcessedCustInfo(matchedOneCust);
        return resultVo;
    }


    /**
     * @description:(根据消息 模糊查询 是否捕获到 唯一的一条待处理 客户信息 .适用于 机构客户)
     * @param analyseVo
     * @param searchAttrInfo
     * @return com.howbuy.crm.account.service.vo.custinfo.AbnormalAnalyseResultVO<T>
     * @author: haoran.zhang
     * @date: 2024/1/12 18:15
     * @since JDK 1.8
     */
    public <T extends MessageCustInfoVO>   AbnormalAnalyseResultVO<T> searchMatchedInstitutionCust(T analyseVo,
                                                                                              SearchCustKeyAttrVO searchAttrInfo){

        AbnormalAnalyseResultVO<T> resultVo=AbnormalAnalyseResultVO.normalData(analyseVo);

        //则在CRM【客户类型=机构】的客户中，根据【一账通号A】的【客户姓名、证件类型+证件号】查询，是否存在匹配的【投顾客户号】
        //  （1）匹配逻辑，满足以下任一条件，取并集后按投顾客户号去重：
//            ①条件1：匹配到的【投顾客户号】的【证件类型+证件号】相同
//            ②条件2：匹配到的【投顾客户号】的【客户姓名】相同
        //证件信息查询
        List<CmConscustForAnalyseBO> idNoMatchList=
                abnormalCustRepository.queryCustListByIdNo(searchAttrInfo.getInvstType(),
                searchAttrInfo.getIdType(),searchAttrInfo.getIdSignAreaCode(),searchAttrInfo.getIdNoDigest());

        //客户姓名查询
        List<CmConscustForAnalyseBO>  custNameMatchList =
                abnormalCustRepository.queryCustListByCustName(searchAttrInfo.getInvstType(),searchAttrInfo.getCustName());


        List<CmConscustForAnalyseBO> repeatList= AbnormalCustUtils.dealDistinct(Lists.newArrayList(custNameMatchList,idNoMatchList));
//        场景4：匹配不到投顾客户号
//        判断条件：去重后的投顾客户号=0，后续处理： 则  新增客户，
        if(repeatList.isEmpty()){
            return resultVo;
        }

        //        场景1：存在重复客户
//        判断条件：去重后的投顾客户号＞1，则存在重复客户（此时不自动绑定【香港客户号】和【投顾客户号】，流程中止），异常数据进“香港异常客户表”：
//        匹配异常的【香港客户号】落“香港异常客户主表”，写入字段
//        a、异常来源：香港开户
//        b、异常描述：匹配到多个投顾客户号
//        c、异常时间：取落入异常表的时间，格式yyyymmdd hh:mm:ss
//        匹配到的全部【投顾客户号】落“香港异常客户待关联表”，与主表数据相对应。
        if(repeatList.size()>1){
            return AbnormalAnalyseResultVO.notNormalData(analyseVo,
                    AbnormaSceneTypeEnum.MATCH_MULTIPLE_CUST_NO,
                    repeatList);
        }
//        场景2：匹配异常
//        判断条件：去重后的投顾客户号=1，且满足以下任一条件，则认为匹配异常（此时不自动绑定【香港客户号】和【投顾客户号】），异常数据进“香港异常客户表”：
//            ①条件1：匹配到的【投顾客户号】的【客户姓名】相同，但【证件类型+证件号】证件不同/或为空
//            ②条件2：匹配到的【投顾客户号】的【证件类型+证件号】相同，但【客户姓名】不同/为空
//        匹配异常的【香港客户号】落“香港异常客户主表”，写入字段
//        a、异常来源：香港开户
//        b、异常描述：根据不同的场景，写入不同的值//
        //①若仅匹配到1个投顾客户号，且满足上述条件1，则写入：客户姓名相同，证件类型/证件号不匹配//
        //②若仅匹配到1个投顾客户号，且满足上述条件2，则写入：证件相同，但客户姓名不匹配
//        c、异常时间：取落入异常表的时间，格式yyyymmdd hh:mm:ss
//        匹配到的全部【投顾客户号】落“香港异常客户待关联表”，与主表数据相对应。
        if(repeatList.size()==1){
            //分析：repeatList是 根据 手机、或 证件 查询的。所以以下的不同，一定会有能进入的。
            CmConscustForAnalyseBO cust=repeatList.get(0);
//            ①条件1：匹配到的【投顾客户号】的【客户姓名】相同，但【证件类型+证件号】证件不同/或为空
            if(Boolean.TRUE.equals(StringUtil.isEqual(searchAttrInfo.getCustName(),cust.getCustname())) &&
                    (StringUtil.isEmpty(cust.getIdnoDigest()) || !isIdNoInfoSame(searchAttrInfo,cust)  )
             ){
                return AbnormalAnalyseResultVO.notNormalData(analyseVo,
                        AbnormaSceneTypeEnum.CUST_NAME_SAME_ID_TYPE_ID_NO_NOT_MATCH,
                        repeatList);
            }
//            ②条件2：匹配到的【投顾客户号】的【证件类型+证件号】相同，但【客户姓名】不同/为空
            if(isIdNoInfoSame(searchAttrInfo,cust) &&
                    (StringUtil.isEmpty(cust.getCustname()) || !StringUtil.isEqual(searchAttrInfo.getCustName(),cust.getCustname()) )
             ){
                return AbnormalAnalyseResultVO.notNormalData(analyseVo,
                        AbnormaSceneTypeEnum.ID_TYPE_ID_NO_SAME_CUST_NAME_NOT_MATCH,
                        repeatList);
            }
        }

        CmConscustForAnalyseBO  matchedOneCust=null;
//        场景3：匹配正常/
//        去重后的投顾客户号=1，且满足以下任一条件，则认为匹配正常，并继续判断⑤
//①条件1：【客户姓名、证件类型+证件号】三要素完全相同
        if(repeatList.size()==1){
            CmConscustForAnalyseBO cust=repeatList.get(0);
            //是否能匹配到唯一的【投顾客户】
            if(Boolean.TRUE.equals(StringUtil.isEqual(searchAttrInfo.getCustName(),cust.getCustname())) &&
                    isIdNoInfoSame(searchAttrInfo,cust) ){
                matchedOneCust=cust;
            }
        }

        resultVo.setProcessedCustInfo(matchedOneCust);
        return resultVo;
    }




    /**
     * @description:(判断 手机号码 是否相同)
     * @param searchAttrInfo
     * @param cust2
     * @return boolean
     * @author: haoran.zhang
     * @date: 2023/12/14 19:05
     * @since JDK 1.8
     */
    public  static boolean isMobileInfoSame(SearchCustKeyAttrVO searchAttrInfo, CmConscustForAnalyseBO cust2){
        return StringUtil.isEqual(searchAttrInfo.getMobileAreaCode(),cust2.getMobileAreaCode()) &&
                StringUtil.isEqual(searchAttrInfo.getMobileDigest(),cust2.getMobileDigest());
    }

    /**
     * @description:(判断 证件信息 是否相同)
     * @param searchAttrInfo
     * @param cust2
     * @return boolean
     * @throws
     * @since JDK 1.8
     */
    public static  boolean isIdNoInfoSame(SearchCustKeyAttrVO searchAttrInfo, CmConscustForAnalyseBO cust2){
        return StringUtil.isEqual(searchAttrInfo.getIdType(),cust2.getIdtype()) &&
                StringUtil.isEqual(searchAttrInfo.getIdNoDigest(),cust2.getIdnoDigest()) ;
//                StringUtil.isEqual(searchAttrInfo.getIdSignAreaCode(),cust2.getIdSignAreaCode());
    }

    /**
     * 根据匹配 规则， 获取唯一匹配 客户
     * @param searchAttrInfo
     * @param repeateList
     * @return
     */
    public static CmConscustForAnalyseBO getMatchedSingleCust(SearchCustKeyAttrVO searchAttrInfo,
                                                              List<CmConscustForAnalyseBO> repeateList){
        //先去重
        List<CmConscustForAnalyseBO> distinctList=distinctList(repeateList);
        //多条 返回
        if(distinctList.size()!=1){
            return null;
        }
        //比较唯一的1条是否满足  匹配正常 逻辑
        CmConscustForAnalyseBO compareBo=distinctList.get(0);
//      满足以下任一条件，取并集后有且仅有一条数据，则认为匹配到唯一的【投顾客户号A】
//①条件1：【手机区号+手机号、证件类型+证件号】四要素完全相同
        if(isMobileInfoSame(searchAttrInfo,compareBo) && isIdNoInfoSame(searchAttrInfo,compareBo) ){
            return compareBo;
        }

//②条件2：匹配到的【投顾客户号】的【证件类型+证件号】相同，但手机号为空
        //过滤只保留 手机号为空的客户
        if(isIdNoInfoSame(searchAttrInfo,compareBo) &&
                StringUtil.isEmpty(compareBo.getMobileDigest())
        ){
            return compareBo;
        }
        return null;
    }


}