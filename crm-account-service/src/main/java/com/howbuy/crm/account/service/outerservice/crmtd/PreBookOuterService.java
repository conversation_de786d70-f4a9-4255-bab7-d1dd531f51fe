/**
 * Copyright (c) 2024, <PERSON><PERSON><PERSON><PERSON> HOWBUY INVESTMENT MANAGEMENT Co., Ltd.
 * All right reserved.
 * <p>
 * THIS IS UNPUBLISHED PROPRIETARY SOURCE CODE OF HOWBUY INVESTMENT
 * MANAGEMENT CO., LTD.  THE CONTENTS OF THIS FILE MAY NOT BE DISCLOSED
 * TO THIRD PARTIES, COPIED OR DUPLICATED IN ANY FORM, IN WHOLE OR IN PART,
 * WITHOUT THE PRIOR WRITTEN PERMISSION OF HOWBUY INVESTMENT MANAGEMENT
 * CO., LTD.
 */
package com.howbuy.crm.account.service.outerservice.crmtd;

import com.howbuy.crm.account.client.enums.ExceptionCodeEnum;
import com.howbuy.crm.account.service.commom.constant.DobboReferenceConstant;
import com.howbuy.crm.account.service.commom.exception.BusinessException;
import com.howbuy.crm.prosale.service.PreBookService;
import lombok.extern.slf4j.Slf4j;
import org.apache.dubbo.config.annotation.DubboReference;
import org.springframework.stereotype.Service;


/**
 * @description: crm-td-client预约服务外部调用服务
 * @author: shijie.wang
 * @date: 2025/6/30 13:25
 * @since JDK 1.8
 */
@Slf4j
@Service
public class PreBookOuterService {

    @DubboReference(registry = DobboReferenceConstant.CRM_TD_SERVER, group = "prebook", check = false)
    private PreBookService preBookService;

    /**
     * @param consCustNo 投顾客户号
     * @param currentTime 当前时间
     * @return java.lang.String 客户来源类型
     * @description: 调用crm-td-client获取客户来源类型
     * <AUTHOR>
     * @date 2024-12-19 17:05:00
     * @since JDK 1.8
     */
    public String getCustSourceType(String consCustNo, String currentTime) {
        try {
            log.info("开始调用crm-td-client获取客户来源类型，投顾客户号：{}，时间：{}", consCustNo, currentTime);
            String sourceType = preBookService.getCustSourceType(consCustNo, currentTime);
            log.info("调用crm-td-client获取客户来源类型完成，投顾客户号：{}，来源类型：{}", consCustNo, sourceType);
            return sourceType;
        } catch (Exception e) {
            log.error("调用crm-td-client获取客户来源类型异常", e);
            throw new BusinessException(ExceptionCodeEnum.SYSTEM_ERROR);
        }
    }
} 