package com.howbuy.crm.account.service.cacheservice.consultant;

import com.google.common.collect.Maps;
import com.howbuy.crm.account.dao.po.consultant.ConsultantSimpleInfoDto;
import com.howbuy.crm.account.service.cacheservice.AbstractCacheService;
import com.howbuy.crm.account.service.repository.consultant.CmConsultantRepository;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.InitializingBean;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import java.util.List;
import java.util.Map;
import java.util.function.Function;
import java.util.stream.Collectors;

import static com.howbuy.crm.account.service.cacheservice.CacheKey.CRM_CONSULTANT_INFO;

/**
 * @description: 投顾信息service
 * <AUTHOR>
 * @time  2022年6月16日 17点38分
 */
@Service("cacheConsultantInfoService")
@Slf4j
public class CacheConsultantInfoService extends AbstractCacheService implements InitializingBean {


    @Autowired
    private CmConsultantRepository cmConsultantRepository;


    @Override
    public void afterPropertiesSet(){
        reloadCache();
    }


    /**
     * @description: 根据consCode刷新单个投顾缓存
     * @param consCode
     * @return void
     * @author: jin.wang03
     * @date: 2025/2/7 13:42
     * @since JDK 1.8
     */
    public void reloadCacheByConsCode(String consCode) {
        ConsultantSimpleInfoDto simpleInfoDto = cmConsultantRepository.getSimpleConsultantByCode(consCode);
        if (simpleInfoDto != null) {
            putConsultToCache(simpleInfoDto);
        } else {
            log.info("根据consCode:{},无法获取投顾数据！", consCode);
        }
    }


    /**
     * 对象-->缓存
     * @param simpleInfoDto
     */
    private void putConsultToCache(ConsultantSimpleInfoDto simpleInfoDto){
        CACHE_SERVICE.putObjToMap(CRM_CONSULTANT_INFO,simpleInfoDto.getConsCode(),simpleInfoDto);

    }


    /**
     * 刷新单个投顾缓存
     */
    public void reloadCache(){
        List<ConsultantSimpleInfoDto>  list=cmConsultantRepository.getSimpleConsultList();
        log.info("刷新缓存,key:{},数据库查询条数：{}",CRM_CONSULTANT_INFO,list.size());
        Map<String,ConsultantSimpleInfoDto> consultMap=list.stream().collect(Collectors.toMap(ConsultantSimpleInfoDto::getConsCode, Function.identity()));
        if(!CollectionUtils.isEmpty(consultMap)){
            consultMap.forEach((consCode,simpleInfoDto)->{
                putConsultToCache(simpleInfoDto);
            });
        }else{
            log.error("无法获取投顾数据！");
        }
    }

    /**
     * 根据consCode获取 投顾信息
     * @param consCode NOT NULL
     * @return ConsultantSimpleInfoDto
     */
    public ConsultantSimpleInfoDto getInfoByconsCode(String consCode){
        return (ConsultantSimpleInfoDto)CACHE_SERVICE.getObjFromMap(CRM_CONSULTANT_INFO,consCode);
    }


    public Map<String,ConsultantSimpleInfoDto> getConsultantMap(){
        Map<String,ConsultantSimpleInfoDto> returnMap= Maps.newHashMap();
        Map<String, Object> cacheMap=CACHE_SERVICE.getFromObjMap(CRM_CONSULTANT_INFO);
        cacheMap.forEach((consCode,value)->{
            returnMap.put(consCode,(ConsultantSimpleInfoDto)value);
        });
//        cacheMap.values().stream().collect(Collectors.toMap())
        return returnMap;
    }


}
