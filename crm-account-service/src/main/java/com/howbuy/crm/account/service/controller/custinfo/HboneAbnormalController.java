/**
 * Copyright (c) 2023, <PERSON><PERSON><PERSON><PERSON> HOWBUY INVESTMENT MANAGEMENT Co., Ltd.
 * All right reserved.
 * <p>
 * THIS IS UNPUBLISHED PROPRIETARY SOURCE CODE OF HOWBUY INVESTMENT
 * MANAGEMENT CO., LTD.  THE CONTENTS OF THIS FILE MAY NOT BE DISCLOSED
 * TO THIRD PARTIES, COPIED OR DUPLICATED IN ANY FORM, IN WHOLE OR IN PART,
 * WITHOUT THE PRIOR WRITTEN PERMISSION OF HOWBUY INVESTMENT MANAGEMENT
 * CO., LTD.
 */
package com.howbuy.crm.account.service.controller.custinfo;

import com.howbuy.crm.account.client.request.custinfo.*;
import com.howbuy.crm.account.client.response.PageVO;
import com.howbuy.crm.account.client.response.Response;
import com.howbuy.crm.account.client.response.custinfo.AbnormalCustInfoVO;
import com.howbuy.crm.account.client.response.custinfo.AbnormalRelatedDetailVO;
import com.howbuy.crm.account.service.service.custinfo.HboneAbnormalCustInfoService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import java.util.List;

/**
 * @description: (一账通异常客户处理 controller)
 * <AUTHOR>
 * @date 2023/12/13 16:08
 * @since JDK 1.8
 */
@RestController
@RequestMapping("/hboneabnormal")
public class HboneAbnormalController {


   @Autowired
    private HboneAbnormalCustInfoService abnormalCustInfoService;


    /**
     * @api {POST} /hboneabnormal/queryhboneabmoamlpage queryHboneAbnormalPage()
     * @apiVersion 1.0.0
     * @apiGroup HboneAbnormalController
     * @apiName queryHboneAbnormalPage()
     * @apiDescription 分页查询一账通客户异常信息列表
     * @apiParam (请求体) {String} custName 客户姓名
     * @apiParam (请求体) {String} hkTxAcctNo 香港客户号
     * @apiParam (请求体) {String} hboneNo 一账通号
     * @apiParam (请求体) {String} custNo 客户号
     * @apiParam (请求体) {String} mobileDigest 手机号
     * @apiParam (请求体) {String} idNoDigest 证件号
     * @apiParam (请求体) {String} abnormalSource 异常来源：1-香港注册2-香港开户 7-好买开户 [暂时同于  7-分销开户]3-香港客户信息同步4-香港客户号/一账通绑定5-一账通实名
     * @apiParam (请求体) {String} dealStatus 处理状态：0-未处理 1-已处理 2-无需处理
     * @apiParam (请求体) {String} createBginDdate 创建时间-开始      yyyyMMdd
     * @apiParam (请求体) {String} createEndDate 创建时间-结束      yyyyMMdd
     * @apiParam (请求体) {Number} page 页号
     * @apiParam (请求体) {Number} size 每页大小
     * @apiParamExample 请求体示例
     * {"hkTxAcctNo":"H4im3F59R","custNo":"Idtv","abnormalSource":"mmhKZmCWzg","size":8595,"mobileDigest":"xZK","idNoDigest":"8Ngg1WCh","dealStatus":"aBZNjG","createBginDdate":"7jSnStp9","page":723,"custName":"VEoc5UA","createEndDate":"9LVDgB648J","hboneNo":"y11OnDvR"}
     * @apiSuccess (响应结果) {String} code 状态码
     * @apiSuccess (响应结果) {String} description 描述信息
     * @apiSuccess (响应结果) {Object} data 数据封装
     * @apiSuccess (响应结果) {Number} data.page 当前第几页
     * @apiSuccess (响应结果) {Number} data.size 单页条数
     * @apiSuccess (响应结果) {Number} data.total 总条数
     * @apiSuccess (响应结果) {Array} data.rows 数据对象列表
     * @apiSuccess (响应结果) {String} data.rows.id 异常客户数据ID
     * @apiSuccess (响应结果) {String} data.rows.messageClientId 消息通知的clientId
     * @apiSuccess (响应结果) {String} data.rows.hkTxAcctNo 香港客户号
     * @apiSuccess (响应结果) {String} data.rows.custName 客户姓名
     * @apiSuccess (响应结果) {String} data.rows.investType 投资者类型
     * @apiSuccess (响应结果) {String} data.rows.mobileAreaCode 手机地区码
     * @apiSuccess (响应结果) {String} data.rows.mobileDigest 手机号摘要
     * @apiSuccess (响应结果) {String} data.rows.mobileMask 手机号掩码
     * @apiSuccess (响应结果) {String} data.rows.mobileCipher 手机号密文
     * @apiSuccess (响应结果) {String} data.rows.idSignAreaCode 证件地区码
     * @apiSuccess (响应结果) {String} data.rows.idType 证件类型
     * @apiSuccess (响应结果) {String} data.rows.idNoDigest 证件号码摘要
     * @apiSuccess (响应结果) {String} data.rows.idNoMask 证件号码掩码
     * @apiSuccess (响应结果) {String} data.rows.idNoCipher 证件号码密文
     * @apiSuccess (响应结果) {String} data.rows.hboneNo 一账通号
     * @apiSuccess (响应结果) {String} data.rows.abnormalSource 异常来源：1-香港注册2-香港开户7-好买开户 [暂时同于  7-分销开户]3-香港客户信息同步4-香港客户号/一账通绑定5-一账通实名
     * @apiSuccess (响应结果) {String} data.rows.abnormalSceneType 异常描述，按异常类别汇总：1-匹配到多个投顾客户号【香港注册、香港开户】2-匹配到的投顾客户号已被占用 【香港注册】3-手机号相同，证件类型/证件号不匹配 【香港开户】4-证件相同，但手机号不匹配【香港开户】5-CRM重复客户预警【香港客户信息同步】6-同时绑定香港客户号时，投顾客户号/香港客户号被占用 【好买开户、一账通实名】7-香港开户证件与一账通证件不一致 【香港开户】8-两边绑定的投顾客户号不一致【香港客户号/一账通绑定】
     * @apiSuccess (响应结果) {String} data.rows.operateChannel 操作通道 1-MQ  2-菜单页面
     * @apiSuccess (响应结果) {String} data.rows.creator 创建人
     * @apiSuccess (响应结果) {Number} data.rows.createTimestamp 创建时间
     * @apiSuccess (响应结果) {String} data.rows.modifier 修改人
     * @apiSuccess (响应结果) {Number} data.rows.modifyTimestamp 修改时间
     * @apiSuccess (响应结果) {String} data.rows.recStat 记录有效状态（1-正常  0-删除）
     * @apiSuccess (响应结果) {String} data.rows.dealStatus 处理状态：0-未处理 1-已处理 2-无需处理
     * @apiSuccess (响应结果) {String} data.rows.dealOperator 处理人
     * @apiSuccess (响应结果) {String} data.rows.dealRemark 处理意见
     * @apiSuccess (响应结果) {Object} data.rows.hkSideInfo 香港账户侧 客户信息
     * @apiSuccess (响应结果) {Object} data.rows.hboneSideInfo 一账通账户侧 客户信息
     * @apiSuccess (响应结果) {String} data.rows.relatedCustNo crm已关联的 客户号
     * @apiSuccess (响应结果) {Number} data.rows.dealTimestamp 处理时间
     * @apiSuccess (响应结果) {Array} data.rows.relatedList 关联客户列表
     * @apiSuccess (响应结果) {String} data.rows.idTypeDesc 证件类型描述
     * @apiSuccess (响应结果) {String} data.rows.abnormalSceneDesc 异常详细描述
     * @apiSuccessExample 响应结果示例
     * {"code":"D3iBIkkToT","data":{"total":8210,"size":4497,"page":6118,"rows":[{"operateChannel":"0g5bWVsM","modifier":"OxQIUowo","createTimestamp":1556629805080,"modifyTimestamp":981039491722,"mobileAreaCode":"o76Fad","abnormalSceneDesc":"grG","mobileDigest":"k","id":"DcFW4","dealRemark":"SPccsU","hboneNo":"OG1mJL","investType":"yIBi","creator":"zVF","idType":"s","messageClientId":"az","dealOperator":"Z6","dealTimestamp":2624408544603,"idNoDigest":"C","dealStatus":"sx8C","custName":"Hi","mobileCipher":"M","idNoMask":"pRmeDO","hkTxAcctNo":"V2j","idSignAreaCode":"3TXl8arHoH","abnormalSource":"3T92Q","abnormalSceneType":"Ihj","relatedList":[],"idTypeDesc":"Tn2dD","idNoCipher":"sU2","relatedCustNo":"RVE48G4RT","recStat":"lknWBu","mobileMask":"LH"}]},"description":"NysG05o"}
     */
    @PostMapping("/queryhboneabmoamlpage")
    @ResponseBody
    public Response<PageVO<AbnormalCustInfoVO>> queryHboneAbnormalPage(@RequestBody AbnormalCustInfoRequest custInfoRequest){
     return Response.ok(abnormalCustInfoService.queryHboneAbnormalPage(custInfoRequest));
   }


    /**
     * @api {POST} /hboneabnormal/queryhboneabmoamldetaillist queryHboneAbnormalDetailList()
     * @apiVersion 1.0.0
     * @apiGroup HboneAbnormalController
     * @apiName queryHboneAbnormalDetailList()
     * @apiDescription 根据一账通异常客户关联子表明细id列表查询明细信息
     * @apiParam (请求体) {Array} requestBody
     * @apiParamExample 请求体示例
     * ["vBj6l"]
     * @apiSuccess (响应结果) {String} code 状态码
     * @apiSuccess (响应结果) {String} description 描述信息
     * @apiSuccess (响应结果) {Array} data 数据封装
     * @apiSuccess (响应结果) {String} data.id 异常客户待关联id
     * @apiSuccess (响应结果) {String} data.abnormalId 异常客户数据ID
     * @apiSuccess (响应结果) {String} data.custNo 客户号
     * @apiSuccess (响应结果) {String} data.custName 客户名称
     * @apiSuccess (响应结果) {String} data.investType 投资者类型
     * @apiSuccess (响应结果) {String} data.mobileAreaCode 手机地区码
     * @apiSuccess (响应结果) {String} data.mobileDigest 手机号摘要
     * @apiSuccess (响应结果) {String} data.mobileMask 手机号掩码
     * @apiSuccess (响应结果) {String} data.mobileCipher 手机号密文
     * @apiSuccess (响应结果) {String} data.idSignAreaCode 证件地区码
     * @apiSuccess (响应结果) {String} data.idType 证件类型
     * @apiSuccess (响应结果) {String} data.idNoDigest 证件号码摘要
     * @apiSuccess (响应结果) {String} data.idNoMask 证件号码掩码
     * @apiSuccess (响应结果) {String} data.idNoCipher 证件号码密文
     * @apiSuccess (响应结果) {String} data.hboneNo 一账通号
     * @apiSuccess (响应结果) {String} data.hkTxAcctNo 香港客户号
     * @apiSuccess (响应结果) {String} data.consCode 客户所属投顾
     * @apiSuccess (响应结果) {String} data.creator 创建人
     * @apiSuccess (响应结果) {Number} data.createTimestamp 创建时间
     * @apiSuccess (响应结果) {String} data.modifier 修改人
     * @apiSuccess (响应结果) {Number} data.modifyTimestamp 修改时间
     * @apiSuccess (响应结果) {String} data.idTypeDesc 证件类型描述
     * @apiSuccessExample 响应结果示例
     * {"code":"nhr0","data":[{"investType":"9od","custNo":"DQ8","creator":"M8whBY","idType":"tMmZHzFIa","modifier":"prOnXwFMF","idNoDigest":"zWBQi","custName":"RNYYvC","mobileCipher":"u","idNoMask":"t","consCode":"uONG7YtxV","createTimestamp":2324564284208,"modifyTimestamp":1204128957796,"mobileAreaCode":"QdI","hkTxAcctNo":"OSZJyksS","idSignAreaCode":"dR","abnormalId":"2LB","mobileDigest":"PY","id":"acPe","idTypeDesc":"o","idNoCipher":"fmcVeL4xU","mobileMask":"5dMCes1o","hboneNo":"Q2MJiBFpdR"}],"description":"EO"}
     */
    @PostMapping("/queryhboneabmoamldetaillist")
    @ResponseBody
    public Response<List<AbnormalRelatedDetailVO>> queryHboneAbnormalDetailList(@RequestBody List<String> detailList){
        return Response.ok(abnormalCustInfoService.queryHboneAbnormalDetailList(detailList));
    }


    /**
     * @api {POST} /hboneabnormal/dealabnormal dealAbnormal()
     * @apiVersion 1.0.0
     * @apiGroup HboneAbnormalController
     * @apiName dealAbnormal()
     * @apiDescription 处理异常信息
     * @apiParam (请求体) {String} id 异常客户信息id
     * @apiParam (请求体) {String} operator 操作人
     * @apiParam (请求体) {String} remark remark备注说明
     * @apiParam (请求体) {String} dealStatus 处理状态：0-未处理 1-已处理 2-无需处理
     * @apiParamExample 请求体示例
     * {"remark":"aViXNg4","id":"ANT","dealStatus":"bnjhsQaJ4","operator":"RxKdAz"}
     * @apiSuccess (响应结果) {String} code 状态码
     * @apiSuccess (响应结果) {String} description 描述信息
     * @apiSuccess (响应结果) {String} data 数据封装
     * @apiSuccessExample 响应结果示例
     * {"code":"4","data":"oqF","description":"Zh1fnQ"}
     */
    @PostMapping("/dealabnormal")
    @ResponseBody
    public Response<String> dealAbnormal(@RequestBody DealAbnormalRequest custInfoRequest) {
     return abnormalCustInfoService.dealAbnormal(custInfoRequest) ;
   }

    /**
     * @api {POST} /hboneabnormal/batchdealabnormal batchDealAbnormal()
     * @apiVersion 1.0.0
     * @apiGroup HboneAbnormalController
     * @apiName batchDealAbnormal()
     * @apiDescription 批量处理异常信息
     * @apiParam (请求体) {Array} idList 异常客户信息id
     * @apiParam (请求体) {String} operator 操作人
     * @apiParam (请求体) {String} remark remark备注说明
     * @apiParam (请求体) {String} dealStatus 处理状态：0-未处理 1-已处理 2-无需处理
     * @apiParamExample 请求体示例
     * {"remark":"yfW1XQBjYo","idList":["Zq6ZHv"],"dealStatus":"bUZaUHDT","operator":"3bhLLXeTFx"}
     * @apiSuccess (响应结果) {String} code 状态码
     * @apiSuccess (响应结果) {String} description 描述信息
     * @apiSuccess (响应结果) {String} data 数据封装
     * @apiSuccessExample 响应结果示例
     * {"code":"XeL6L","data":"d7RzRqjs3U","description":"9m"}
     */
    @PostMapping("/batchdealabnormal")
   @ResponseBody
   public Response<String> batchDealAbnormal(@RequestBody BatchDealAbnormalRequest batchRequest) {
    return abnormalCustInfoService.batchDealAbnormal(batchRequest) ;
   }

    /**
     * @api {POST} /hboneabnormal/associateabnormalhbone associateAbnormalHbone()
     * @apiVersion 1.0.0
     * @apiGroup HboneAbnormalController
     * @apiName associateAbnormalHbone()
     * @apiDescription 根据 异常明细Id，关联异常客户与异常主表一账通，并更新信息
     * @apiParam (请求参数) {String} detailId 异常明细Id
     * @apiParam (请求参数) {String} operator 操作人
     * @apiParamExample 请求参数示例
     * detailId=kkvde&operator=EyQEX
     * @apiSuccess (响应结果) {String} code 状态码
     * @apiSuccess (响应结果) {String} description 描述信息
     * @apiSuccess (响应结果) {String} data 数据封装
     * @apiSuccessExample 响应结果示例
     * {"code":"qvTn6v","data":"P","description":"ChAosYQ"}
     */
    @PostMapping("/associateabnormalhbone")
    @ResponseBody
    public Response<String> associateAbnormalHbone(String detailId,String operator) {
        return abnormalCustInfoService.associateAbnormalHbone(detailId,operator) ;
    }


    /**
     * @api {POST} /hboneabnormal/createcustinfobyhbone createCustInfoByHbone()
     * @apiVersion 1.0.0
     * @apiGroup HboneAbnormalController
     * @apiName createCustInfoByHbone()
     * @apiDescription 一账通异常客户页，按钮：新增客户
     * @apiParam (请求体) {String} id 异常客户信息id
     * @apiParam (请求体) {String} operator 操作人
     * @apiParamExample 请求体示例
     * {"id":"S3","operator":"4xvb1DU9U"}
     * @apiSuccess (响应结果) {String} code 状态码
     * @apiSuccess (响应结果) {String} description 描述信息
     * @apiSuccess (响应结果) {String} data 数据封装
     * @apiSuccessExample 响应结果示例
     * {"code":"vT","data":"j","description":"ZnNhsr"}
     */
    @ResponseBody
    @PostMapping("/createcustinfobyhbone")
    public Response<String> createCustInfoByHbone(@RequestBody HboneAbnormalCreateConsCustRequest custInfoRequest) {
        return abnormalCustInfoService.createCustInfoByHbone(custInfoRequest);
    }



}