/**
 * Copyright (c) 2023, <PERSON><PERSON><PERSON><PERSON> HOWBUY INVESTMENT MANAGEMENT Co., Ltd.
 * All right reserved.
 * <p>
 * THIS IS UNPUBLISHED PROPRIETARY SOURCE CODE OF HOWBUY INVESTMENT
 * MANAGEMENT CO., LTD.  THE CONTENTS OF THIS FILE MAY NOT BE DISCLOSED
 * TO THIRD PARTIES, COPIED OR DUPLICATED IN ANY FORM, IN WHOLE OR IN PART,
 * WITHOUT THE PRIOR WRITTEN PERMISSION OF HOWBUY INVESTMENT MANAGEMENT
 * CO., LTD.
 */
package com.howbuy.crm.account.service.commom.constant;

/**
 * @description: fegin服务端key常量类
 * <AUTHOR>
 * @date 2023/3/16 11:15
 * @since JDK 1.8
 */
public class FeginServerKeyConstant {
    private FeginServerKeyConstant(){}

    /**
     * 海外直销 产品中心
     */
    public static final String DTMS_PRODUCT_REMOTE = "dtms-product-remote";
}