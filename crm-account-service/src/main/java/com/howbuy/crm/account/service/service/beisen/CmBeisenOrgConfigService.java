package com.howbuy.crm.account.service.service.beisen;

import com.github.pagehelper.Page;
import com.google.common.collect.Lists;
import com.howbuy.crm.account.client.request.beisen.InsertBeisenOrgConfigRequest;
import com.howbuy.crm.account.client.request.beisen.QueryBeisenOrgConfigRequest;
import com.howbuy.crm.account.client.request.beisen.UpdateBeisenOrgConfigRequest;
import com.howbuy.crm.account.client.response.ExportToFileVO;
import com.howbuy.crm.account.client.response.PageVO;
import com.howbuy.crm.account.client.response.beisen.CmBeisenOrgConfigDetailVO;
import com.howbuy.crm.account.client.response.beisen.CmBeisenOrgDetailVO;
import com.howbuy.crm.account.client.response.beisen.CmBeisenOrgVO;
import com.howbuy.crm.account.dao.po.beisen.CmBeisenOrgDO;
import com.howbuy.crm.account.dao.po.beisenorg.CmBeisenOrgConfigPO;
import com.howbuy.crm.account.dao.req.beisen.QueryBeisenOrgConfigVO;
import com.howbuy.crm.account.service.commom.utils.ExportUtil;
import com.howbuy.crm.account.service.domain.OrgLayerInfoDTO;
import com.howbuy.crm.account.service.outerservice.crmcore.OrganazitonOuterService;
import com.howbuy.crm.account.service.repository.beisen.CmBeisenOrgConfigRepository;
import com.howbuy.crm.account.service.vo.beisen.CmBeisenOrgConfigExcelVO;
import crm.howbuy.base.utils.StringUtil;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.stream.Collectors;

@Service
@Slf4j
public class CmBeisenOrgConfigService {

    @Resource
    private CmBeisenOrgConfigRepository cmBeisenOrgConfigRepository;

    @Resource
    private OrganazitonOuterService organazitonOuterService;

    private static int MAX_PAGE_SIZE = 100000;

    /**
     * @description 列表查询
     * @param queryBeisenOrgConfigRequest
     * @return com.howbuy.crm.account.client.response.PageVO<com.howbuy.crm.account.client.response.beisen.CmBeisenOrgConfigDetailVO>
     * @author: jianjian.yang
     * @date: 2024/10/30 20:00
     * @since JDK 1.8
     */
    public PageVO<CmBeisenOrgConfigDetailVO> query(QueryBeisenOrgConfigRequest queryBeisenOrgConfigRequest){
        PageVO<CmBeisenOrgConfigDetailVO> pageVO = new PageVO();
        QueryBeisenOrgConfigVO queryBeisenOrgConfigVO = new QueryBeisenOrgConfigVO();
        queryBeisenOrgConfigVO.setBeisenOrgName(queryBeisenOrgConfigRequest.getOrgNameBeisen());
        queryBeisenOrgConfigVO.setOrgCode(queryBeisenOrgConfigRequest.getOrgCode());
        queryBeisenOrgConfigVO.setStartDate(queryBeisenOrgConfigRequest.getStartDate());
        queryBeisenOrgConfigVO.setEndDate(queryBeisenOrgConfigRequest.getEndDate());
        queryBeisenOrgConfigVO.setPage(queryBeisenOrgConfigRequest.getPageNo());
        queryBeisenOrgConfigVO.setRows(queryBeisenOrgConfigRequest.getPageSize());
        Page<CmBeisenOrgConfigPO> page = cmBeisenOrgConfigRepository.queryList(queryBeisenOrgConfigVO);
        List<CmBeisenOrgConfigPO> poList = page.getResult();
        List<CmBeisenOrgConfigDetailVO> voList = new ArrayList<>();
        if(!CollectionUtils.isEmpty(poList)){
            poList.forEach(cmBeisenOrgConfigPO -> {
                CmBeisenOrgConfigDetailVO cmBeisenOrgConfigDetailVO = copyByPO(cmBeisenOrgConfigPO);
                voList.add(cmBeisenOrgConfigDetailVO);
            });
        }
        pageVO.setTotal(page.getTotal());
        pageVO.setPage(page.getPageNum());
        pageVO.setSize(page.getPageSize());
        pageVO.setRows(voList);
        return pageVO;
    }

    /**
     * @description 插入配置
     * @param insertBeisenOrgConfigRequest
     * @return void
     * @author: jianjian.yang
     * @date: 2024/10/30 20:00
     * @since JDK 1.8
     */
    public String insertBeisenOrgConfig(InsertBeisenOrgConfigRequest insertBeisenOrgConfigRequest) {
        String verifyResult = verify(null,
                insertBeisenOrgConfigRequest.getOrgIdBeisen(),
                insertBeisenOrgConfigRequest.getStartDate(),
                insertBeisenOrgConfigRequest.getEndDate());
        if(verifyResult != null){
            return verifyResult;
        }
        CmBeisenOrgConfigPO cmBeisenOrgConfigPO = new CmBeisenOrgConfigPO();
        cmBeisenOrgConfigPO.setOrgIdBeisen(insertBeisenOrgConfigRequest.getOrgIdBeisen());
        cmBeisenOrgConfigPO.setOrgNameBeisen(insertBeisenOrgConfigRequest.getOrgNameBeisen());
        cmBeisenOrgConfigPO.setOrgCode(insertBeisenOrgConfigRequest.getOrgCode());
        cmBeisenOrgConfigPO.setStartDate(insertBeisenOrgConfigRequest.getStartDate());
        cmBeisenOrgConfigPO.setEndDate(insertBeisenOrgConfigRequest.getEndDate());
        cmBeisenOrgConfigPO.setCenterOrg(insertBeisenOrgConfigRequest.getCenterOrg());
        cmBeisenOrgConfigRepository.insertSelective(cmBeisenOrgConfigPO);
        return null;
    }

    /**
     * @description 修改配置
     * @param updateBeisenOrgConfigRequest
     * @return void
     * @author: jianjian.yang
     * @date: 2024/10/30 20:00
     * @since JDK 1.8
     */
    public String updateBeisenOrgConfig(UpdateBeisenOrgConfigRequest updateBeisenOrgConfigRequest) {
        String verifyResult = verify(updateBeisenOrgConfigRequest.getId(),
                updateBeisenOrgConfigRequest.getOrgIdBeisen(),
                updateBeisenOrgConfigRequest.getStartDate(),
                updateBeisenOrgConfigRequest.getEndDate());
        if(verifyResult != null){
            return verifyResult;
        }
        CmBeisenOrgConfigPO cmBeisenOrgConfigPO = new CmBeisenOrgConfigPO();
        cmBeisenOrgConfigPO.setId(Long.valueOf(updateBeisenOrgConfigRequest.getId()));
        cmBeisenOrgConfigPO.setOrgIdBeisen(updateBeisenOrgConfigRequest.getOrgIdBeisen());
        cmBeisenOrgConfigPO.setOrgNameBeisen(updateBeisenOrgConfigRequest.getOrgNameBeisen());
        cmBeisenOrgConfigPO.setOrgCode(updateBeisenOrgConfigRequest.getOrgCode());
        cmBeisenOrgConfigPO.setStartDate(updateBeisenOrgConfigRequest.getStartDate());
        cmBeisenOrgConfigPO.setEndDate(updateBeisenOrgConfigRequest.getEndDate());
        cmBeisenOrgConfigPO.setCenterOrg(updateBeisenOrgConfigRequest.getCenterOrg());
        cmBeisenOrgConfigRepository.updateByPrimaryKey(cmBeisenOrgConfigPO);
        return null;
    }

    /**
     * @description 校验
     * @param updateId
     * @param orgIdBeisen
     * @param startDate
     * @param endDate
     * @return java.lang.String
     * @author: jianjian.yang
     * @date: 2024/11/19 17:03
     * @since JDK 1.8
     */
    private String verify(String updateId, String orgIdBeisen, String startDate, String endDate){
        List<Long> ids = cmBeisenOrgConfigRepository.selectByOrgId(orgIdBeisen);
        // 校验机构是否已配置
        if(ids == null){
            return null;
        }
        List<Long> existIds = cmBeisenOrgConfigRepository.selectByInterval(orgIdBeisen, startDate, endDate);
        // 校验区间是否已配置且不是当前更新的数据
        if(existIds != null){
            for(Long id : existIds){
                if(!Objects.equals(id.toString(), updateId)){
                    return "当前区间已有数据，请勿重复维护！";
                }
            }
        }
        return null;
    }

    /**
     * @description 删除配置
     * @param id
     * @return void
     * @author: jianjian.yang
     * @date: 2024/10/30 20:00
     * @since JDK 1.8
     */
    public String deleteBeisenOrgConfig(String id){
        if(StringUtil.isEmpty(id)){
            return "参数错误";
        }
        cmBeisenOrgConfigRepository.deleteByPrimaryKey(Long.valueOf(id));
        return null;
    }

    /**
     * @description 查单个详情
     * @param id
     * @return com.howbuy.crm.account.client.response.beisen.CmBeisenOrgConfigDetailVO
     * @author: jianjian.yang
     * @date: 2024/10/30 20:01
     * @since JDK 1.8
     */
    public CmBeisenOrgConfigDetailVO getBeisenOrgConfigDetail(String id){
        CmBeisenOrgConfigPO cmBeisenOrgConfigPO = cmBeisenOrgConfigRepository.selectByPrimaryKey(Long.valueOf(id));
        return copyByPO(cmBeisenOrgConfigPO);
    }

    /**
     * @description 导出
     * @param queryBeisenOrgConfigRequest
     * @return com.howbuy.crm.account.client.response.ExportToFileVO
     * @author: jianjian.yang
     * @date: 2024/11/13 14:37
     * @since JDK 1.8
     */
    public ExportToFileVO export(QueryBeisenOrgConfigRequest queryBeisenOrgConfigRequest){
        queryBeisenOrgConfigRequest.setPageNo(1);
        queryBeisenOrgConfigRequest.setPageSize(MAX_PAGE_SIZE);
        PageVO<CmBeisenOrgConfigDetailVO> pageVO = query(queryBeisenOrgConfigRequest);
        if(CollectionUtils.isEmpty(pageVO.getRows())){
            return null;
        }
        List<String> orgCodeList = pageVO.getRows().stream().map(CmBeisenOrgConfigDetailVO::getOrgCode)
                .filter(Objects::nonNull)
                .collect(Collectors.toList());
        Map<String, OrgLayerInfoDTO> orgLayerInfoMap = organazitonOuterService.getOrgLayerInfoByOrgCodeList(orgCodeList);
        List<CmBeisenOrgConfigExcelVO> excelVOList = Lists.newArrayList();
        pageVO.getRows().forEach(cmBeisenOrgConfigDetailVO -> {
            CmBeisenOrgConfigExcelVO cmBeisenOrgConfigExcelVO = new CmBeisenOrgConfigExcelVO();
            cmBeisenOrgConfigExcelVO.setOrgIdBeisen(cmBeisenOrgConfigDetailVO.getOrgIdBeisen());
            cmBeisenOrgConfigExcelVO.setOrgNameBeisen(cmBeisenOrgConfigDetailVO.getOrgNameBeisen());
            cmBeisenOrgConfigExcelVO.setCenterOrg(cmBeisenOrgConfigDetailVO.getCenterOrg());
            if(orgLayerInfoMap.containsKey(cmBeisenOrgConfigDetailVO.getOrgCode())){
                OrgLayerInfoDTO orgLayerInfoDTO = orgLayerInfoMap.get(cmBeisenOrgConfigDetailVO.getOrgCode());
                cmBeisenOrgConfigExcelVO.setU1NameCrm(orgLayerInfoDTO.getCenterOrgName());
                cmBeisenOrgConfigExcelVO.setU2NameCrm(orgLayerInfoDTO.getDistrictName());
                cmBeisenOrgConfigExcelVO.setU3NameCrm(orgLayerInfoDTO.getPartOrgName());
            }
            cmBeisenOrgConfigExcelVO.setStartDate(cmBeisenOrgConfigDetailVO.getStartDate());
            cmBeisenOrgConfigExcelVO.setEndDate(cmBeisenOrgConfigDetailVO.getEndDate());
            excelVOList.add(cmBeisenOrgConfigExcelVO);
        });
        return ExportUtil.export(excelVOList, "架构映射表", CmBeisenOrgConfigExcelVO.class);
    }

    /**
     * @description 查北森机构
     * @param
     * @return com.howbuy.crm.account.client.response.beisen.CmBeisenOrgVO
     * @author: jianjian.yang
     * @date: 2024/11/19 18:01
     * @since JDK 1.8
     */
    public CmBeisenOrgVO queryBeisenOrg(){
        CmBeisenOrgVO cmBeisenOrgVO = new CmBeisenOrgVO();
        List<CmBeisenOrgDO> cmBeisenOrgDOList = cmBeisenOrgConfigRepository.selectBeisenOrg();
        List<CmBeisenOrgDetailVO> cmBeisenOrgDetailVOList = Lists.newArrayList();
        cmBeisenOrgDOList.forEach(cmBeisenOrgDO -> {
            CmBeisenOrgDetailVO cmBeisenOrgDetailVO = new CmBeisenOrgDetailVO();
            cmBeisenOrgDetailVO.setOrgIdBeisen(cmBeisenOrgDO.getOrgIdBeisen());
            cmBeisenOrgDetailVO.setOrgNameBeisen(cmBeisenOrgDO.getOrgNameBeisen());
            cmBeisenOrgDetailVOList.add(cmBeisenOrgDetailVO);
        });
        cmBeisenOrgVO.setList(cmBeisenOrgDetailVOList);
        return cmBeisenOrgVO;
    }

    /**
     * @description 对象拷贝
     * @param cmBeisenOrgConfigPO
     * @return com.howbuy.crm.account.client.response.beisen.CmBeisenOrgConfigDetailVO
     * @author: jianjian.yang
     * @date: 2024/10/30 20:01
     * @since JDK 1.8
     */
    private CmBeisenOrgConfigDetailVO copyByPO(CmBeisenOrgConfigPO cmBeisenOrgConfigPO){
        CmBeisenOrgConfigDetailVO cmBeisenOrgConfigDetailVO = new CmBeisenOrgConfigDetailVO();
        cmBeisenOrgConfigDetailVO.setId(cmBeisenOrgConfigPO.getId().toString());
        cmBeisenOrgConfigDetailVO.setOrgIdBeisen(cmBeisenOrgConfigPO.getOrgIdBeisen());
        cmBeisenOrgConfigDetailVO.setOrgNameBeisen(cmBeisenOrgConfigPO.getOrgNameBeisen());
        cmBeisenOrgConfigDetailVO.setCenterOrg(cmBeisenOrgConfigPO.getCenterOrg());
        cmBeisenOrgConfigDetailVO.setStartDate(cmBeisenOrgConfigPO.getStartDate());
        cmBeisenOrgConfigDetailVO.setEndDate(cmBeisenOrgConfigPO.getEndDate());
        cmBeisenOrgConfigDetailVO.setOrgCode(cmBeisenOrgConfigPO.getOrgCode());
        return cmBeisenOrgConfigDetailVO;
    }
}
