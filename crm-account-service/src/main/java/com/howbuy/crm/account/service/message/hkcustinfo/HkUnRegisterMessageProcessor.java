/**
 * Copyright (c) 2023, <PERSON><PERSON><PERSON><PERSON> HOWBUY INVESTMENT MANAGEMENT Co., Ltd.
 * All right reserved.
 * <p>
 * THIS IS UNPUBLISHED PROPRIETARY SOURCE CODE OF HOWBUY INVESTMENT
 * MANAGEMENT CO., LTD.  THE CONTENTS OF THIS FILE MAY NOT BE DISCLOSED
 * TO THIRD PARTIES, COPIED OR DUPLICATED IN ANY FORM, IN WHOLE OR IN PART,
 * WITHOUT THE PRIOR WRITTEN PERMISSION OF HOWBUY INVESTMENT MANAGEMENT
 * CO., LTD.
 */
package com.howbuy.crm.account.service.message.hkcustinfo;

import com.howbuy.crm.account.client.enums.custinfo.FullCustSourceEnum;
import com.howbuy.crm.account.client.request.custinfo.HkAcctRelationOptRequest;
import com.howbuy.crm.account.client.response.Response;
import com.howbuy.crm.account.dao.po.custinfo.CmHkConscustPO;
import com.howbuy.crm.account.dao.req.custinfo.CmHkCustReqVO;
import com.howbuy.crm.account.service.commom.constant.Constants;
import com.howbuy.crm.account.service.message.dto.HkUpdateCustMessageDTO;
import com.howbuy.crm.account.service.repository.custinfo.CmHkCustInfoRepository;
import com.howbuy.crm.account.service.service.custinfo.HkCustInfoService;
import com.howbuy.crm.account.service.vo.custinfo.AbnormalAnalyseResultVO;
import com.howbuy.crm.account.service.vo.custinfo.HkMessageCustInfoVO;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;

import java.util.Arrays;
import java.util.List;

/**
 * @description: ([香港账户]-[香港注销] 消息处理器[topic.hk.unRegister])
 * <AUTHOR>
 * @date 2023/12/11 17:04
 * @since JDK 1.8
 */
@Slf4j
@Service
public class HkUnRegisterMessageProcessor extends AbstractHkMessageProcessor<HkUpdateCustMessageDTO> {



    /**
     * [TOPIC_HK_UPDATE_CUST]
     */
    @Value("${topic.hk.updateCust}")
    private String topicUpdateCust;


    /**
     * [CLOSE_ACCT]
     */
    @Value("${tag.hk.closeAcct}")
    private String tagName;

    @Autowired
    private CmHkCustInfoRepository cmHkCustInfoRepository;

    @Autowired
    private HkCustInfoService hkCustInfoService;

    @Override
    public String getQuartMessageChannel() {
        return topicUpdateCust;
    }

    @Override
    public List<String> getSupportTagList() {
        return Arrays.asList(tagName);
    }

    @Override
    HkMessageCustInfoVO constructByMessage(HkUpdateCustMessageDTO acctMsgDto) {
        HkMessageCustInfoVO custVo=new HkMessageCustInfoVO();
        custVo.setHkTxAcctNo(acctMsgDto.getHkCustNo());
        fillHkAcctInfo(custVo);
        return custVo;
    }

    @Override
    public FullCustSourceEnum getCustSource() {
        return FullCustSourceEnum.HK_UN_REGISTER;
    }

    @Override
    Response<String> processHkMessage(AbnormalAnalyseResultVO<HkMessageCustInfoVO> resultVo) {
        String hkTxAcctNo=resultVo.getAnalyseVo().getHkTxAcctNo();
//        判断①：根据推送的【香港客户号】，查询是否存在绑定的【投顾客户号】//
//        （1）若不存在，则不处理
//        （2）若存在，则将【香港客户号】和【投顾客户号】解绑，此时不更新投顾客户信息
        CmHkCustReqVO reqVo=new CmHkCustReqVO();
        reqVo.setHkTxAcctNo(hkTxAcctNo);
        CmHkConscustPO  custPo=cmHkCustInfoRepository.selectByReqVO(reqVo);
        if(custPo==null){
            return Response.ok();
        }
        HkAcctRelationOptRequest unbindRequest=new HkAcctRelationOptRequest();
        unbindRequest.setCustNo(custPo.getConscustno());
        unbindRequest.setHkTxAcctNo(hkTxAcctNo);
        unbindRequest.setOperator(Constants.OPERATOR_SYS);
        unbindRequest.setOperateChannel(getOperateChannel());
        //AbnormalCustSourceEnum 中 增加  是否需要分析 已经 是否有异常
        unbindRequest.setOperateSource(getCustSource().getCode());
        return hkCustInfoService.unBindHkTxAcct(unbindRequest);

    }

}