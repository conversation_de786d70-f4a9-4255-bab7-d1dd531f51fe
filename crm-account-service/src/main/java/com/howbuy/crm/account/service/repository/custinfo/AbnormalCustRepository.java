/**
 * Copyright (c) 2023, <PERSON><PERSON><PERSON><PERSON> HOWBUY INVESTMENT MANAGEMENT Co., Ltd.
 * All right reserved.
 * <p>
 * THIS IS UNPUBLISHED PROPRIETARY SOURCE CODE OF HOWBUY INVESTMENT
 * MANAGEMENT CO., LTD.  THE CONTENTS OF THIS FILE MAY NOT BE DISCLOSED
 * TO THIRD PARTIES, COPIED OR DUPLICATED IN ANY FORM, IN WHOLE OR IN PART,
 * WITHOUT THE PRIOR WRITTEN PERMISSION OF HOWBUY INVESTMENT MANAGEMENT
 * CO., LTD.
 */
package com.howbuy.crm.account.service.repository.custinfo;

import com.alibaba.nacos.common.utils.CollectionUtils;
import com.google.common.collect.Lists;
import com.howbuy.crm.account.dao.bo.custinfo.CmConscustForAnalyseBO;
import com.howbuy.crm.account.dao.mapper.custinfo.CmHkConscustMapper;
import com.howbuy.crm.account.dao.mapper.customize.custinfo.ConscustCustomizeMapper;
import com.howbuy.crm.account.dao.po.custinfo.CmHkConscustPO;
import com.howbuy.crm.account.dao.req.custinfo.MatchedCustReqVO;
import com.howbuy.crm.account.service.commom.utils.AbnormalCustUtils;
import com.howbuy.crm.account.service.vo.custinfo.HkMessageCustInfoVO;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import org.springframework.transaction.annotation.Propagation;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.Assert;

import java.util.List;
import java.util.stream.Collectors;

/**
 * @description: (异常客户 公共 repository)
 * <AUTHOR>
 * @date 2023/12/8 15:31
 * @since JDK 1.8
 */
@Component
@Transactional(rollbackFor = Exception.class, propagation = Propagation.SUPPORTS)
public class AbnormalCustRepository {

    @Autowired
    private ConscustCustomizeMapper conscustCustomizeMapper;
    @Autowired
    private CmHkConscustMapper cmHkConscustMapper;


    /**
     * @description: (根据手机区号+手机号 查询客户信息)
     * @param investType 客户类型
     * @param mobileAreaCode 手机号区号
     * @param mobileDigest 手机号
     * @return com.howbuy.crm.account.dao.po.custinfo.CmConscustPO
     * @throws
     * @since JDK 1.8
     */
    public List<CmConscustForAnalyseBO> queryCustListByMobile(
            String investType,
            String mobileAreaCode,
            String mobileDigest){
        return conscustCustomizeMapper.queryCustListByMobile(AbnormalCustUtils.getSearchInvestTypeList(investType),
                mobileAreaCode,mobileDigest);
    }
    /**
     * @description: (根据证件类型+证件签发地区+证件号码 查询客户信息)
     * @param investType 客户类型
     * @param idType 证件类型
     * @param idSignAreaCode 证件签发地区
     * @param idNoDigest 证件号码
     * @return com.howbuy.crm.account.dao.po.custinfo.CmConscustPO
     * @throws
     * @since JDK 1.8
     */
    public List<CmConscustForAnalyseBO> queryCustListByIdNo(
                                                     String investType,
                                                     String idType,
                                                     String idSignAreaCode,
                                                     String idNoDigest){
       //证件类型 转译 特殊处理的。
       List<String> searchedIdTypeList=AbnormalCustUtils.getSearchIdTypeList(investType,idType);
       //投资者类型 转译
       List<String> searchedInvestTypeList=AbnormalCustUtils.getSearchInvestTypeList(investType);
       //2024年1月30日 14:52:06  根据证件信息查询， 忽略 证件签发地区[idSignAreaCode]
       return conscustCustomizeMapper.queryCustListByIdNo(
               searchedInvestTypeList,
               searchedIdTypeList,
               null,
               idNoDigest);
    }

    /**
     * @description: (根据 客户姓名 查询客户信息)
     * @param investType 客户类型
     * @param custName 客户姓名
     * @return com.howbuy.crm.account.dao.po.custinfo.CmConscustPO
     * @throws
     * @since JDK 1.8
     */
    public List<CmConscustForAnalyseBO> queryCustListByCustName(String investType,String custName){
        return conscustCustomizeMapper.queryCustListByCustName(AbnormalCustUtils.getSearchInvestTypeList(investType),custName);
    }

    /**
     * 根据手机信息 查询重复数据
     * <p> 1.investType 为  个人客户 时，只查询 个人客户； 否则 查询  机构客户+产品客户 </p>
     * @param investType 客户类型
     * @param mobileAreaCode 手机区号
     * @param mobileDigest 手机号
     * @param excludeCustNo 排除的客户号
     * @return
     */
    public  List<CmConscustForAnalyseBO> searchMobileExistList(String investType,
                                                               String mobileAreaCode,
                                                               String mobileDigest,
                                                               String excludeCustNo){
        List<CmConscustForAnalyseBO> mobileExistList=
                queryCustListByMobile(investType,mobileAreaCode,mobileDigest);
        if(excludeCustNo != null){
            mobileExistList=mobileExistList.stream().filter(s->!s.getConscustno().equals(excludeCustNo)).collect(Collectors.toList());
        }
        return mobileExistList;
    }

    /**
     * 根据 证件信息 查询重复数据
     * <p> 1.investType 为  个人客户 时，只查询 个人客户； 否则 查询  机构客户+产品客户 </p>
     * @param investType 客户类型
     * @param idType 证件类型
     * @param idSignAreaCode 证件签发地区
     * @param idNoDigest 证件号码
     * @param excludeCustNo 排除的客户号
     * @return
     */
    public  List<CmConscustForAnalyseBO> searchIdExistList(String investType,
                                                           String idType,
                                                           String idSignAreaCode,
                                                           String idNoDigest,
                                                           String excludeCustNo){
        List<CmConscustForAnalyseBO> mobileExistList=
                queryCustListByIdNo(investType,idType,idSignAreaCode,idNoDigest);
        if(excludeCustNo != null){
            mobileExistList=mobileExistList.stream().filter(s->!s.getConscustno().equals(excludeCustNo)).collect(Collectors.toList());
        }
        return mobileExistList;
    }

    /**
     * @description: (四要素 全部命中查询 ： 手机号码+证件号码)
     * @param analyseVo 客户信息
     * @return com.howbuy.crm.account.dao.po.custinfo.CmConscustPO
     * @throws
     * @since JDK 1.8
     */
    public List<CmConscustForAnalyseBO> queryCustListByMatchedVo(HkMessageCustInfoVO analyseVo){
        MatchedCustReqVO matchedVo = new MatchedCustReqVO();
        matchedVo.setMobileAreaCode(analyseVo.getMobileAreaCode());
        matchedVo.setMobileDigest(analyseVo.getMobileDigest());
        matchedVo.setIdType(analyseVo.getIdType());
        matchedVo.setIdNoDigest(analyseVo.getIdNoDigest());
        matchedVo.setIdSignAreaCode(analyseVo.getIdSignAreaCode());
        matchedVo.setInvestTypeList(AbnormalCustUtils.getSearchInvestTypeList(analyseVo.getInvestType()));
        return conscustCustomizeMapper.queryCustListByMatchedVo(matchedVo);
    }




    /**
     * @description: (根据客户号列表 查询客户信息列表)
     * @param custNoList 客户号列表
     * @return com.howbuy.crm.account.dao.po.custinfo.CmConscustPO
     * @throws
     * @since JDK 1.8
     */
    public List<CmConscustForAnalyseBO> queryCustListByCustNoList(List<String> custNoList){
        return conscustCustomizeMapper.queryCustListByCustNoList(custNoList);
    }

    /**
     * @description:(根据客户号查询客户信息)
     * @param custNo
     * @return com.howbuy.crm.account.dao.bo.custinfo.CmConscustForAnalyseBO
     * @author: haoran.zhang
     * @date: 2023/12/18 14:14
     * @since JDK 1.8
     */
    public CmConscustForAnalyseBO queryCustBOByCustNo(String custNo){
        Assert.notNull(custNo);
        List<CmConscustForAnalyseBO>  list=queryCustListByCustNoList(Lists.newArrayList(custNo));
        return CollectionUtils.isEmpty(list)?null:list.get(0);
    }


    /**
     * @description:(根据香港交易账号查询客户信息)
     * @param hkTxAcctNo
     * @return com.howbuy.crm.account.dao.bo.custinfo.CmConscustForAnalyseBO
     * @throws
     * @since JDK 1.8
     */
    public CmConscustForAnalyseBO queryCustBOByHkTxAcctNo(String hkTxAcctNo){
        CmHkConscustPO custPO=cmHkConscustMapper.selectByHkTxAcctNo(hkTxAcctNo);
        if(custPO!=null){
           return queryCustBOByCustNo(custPO.getConscustno());
        }
        return null;
    }

    /**
     * @description:(根据 一账通号 查询客户信息)
     * @param hboneNo
     * @return java.util.List<com.howbuy.crm.account.dao.bo.custinfo.CmConscustForAnalyseBO>
     * @author: haoran.zhang
     * @date: 2023/12/25 16:34
     * @since JDK 1.8
     */
    public List<CmConscustForAnalyseBO> queryCustBOByHboneNo(String hboneNo){
        return conscustCustomizeMapper.queryCustListByHboneNo(hboneNo);
    }


}