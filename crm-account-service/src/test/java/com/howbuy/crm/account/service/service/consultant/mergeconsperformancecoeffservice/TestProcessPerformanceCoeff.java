package com.howbuy.crm.account.service.service.consultant.mergeconsperformancecoeffservice;

import com.howbuy.crm.account.dao.bo.consperformancecoeff.ConsPerformanceCoeffBO;
import com.howbuy.crm.account.dao.bo.consultant.CmCustSourceCoeffBO;
import com.howbuy.crm.account.service.repository.consultant.MergeConsPerformanceCoeffRepository;
import com.howbuy.crm.account.service.service.consultant.MergeConsPerformanceCoeffService;
import org.junit.Assert;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.MockitoAnnotations;
import org.powermock.api.mockito.PowerMockito;
import org.powermock.core.classloader.annotations.PowerMockIgnore;
import org.powermock.core.classloader.annotations.PrepareForTest;
import org.powermock.modules.testng.PowerMockTestCase;
import org.testng.annotations.Test;

import java.lang.reflect.Field;
import java.math.BigDecimal;

import static org.powermock.reflect.Whitebox.getField;
import static org.powermock.reflect.Whitebox.invokeMethod;

/**
 * @description: 处理佣金系数相关字段方法单元测试
 * <AUTHOR>
 * @date 2025-07-18 17:05:00
 * @since JDK 1.8
 *
 * 场景1：当来源类型为空时，不做任何处理
 * 场景2：当查询不到来源类型对应的系数配置时，不做任何处理
 * 场景3：当查询到系数配置时，正确设置佣金系数起点
 * 场景4：当查询到系数配置时，正确设置客户折算系数（入参有值时使用入参值）
 * 场景5：当查询到系数配置时，正确设置客户折算系数（入参无值时使用配置值）
 * 场景6：当查询到系数配置时，正确设置存续B值
 */
@PowerMockIgnore("javax.management.*")
@Test
@PrepareForTest({MergeConsPerformanceCoeffService.class})
public class TestProcessPerformanceCoeff extends PowerMockTestCase {
    
    @InjectMocks
    private MergeConsPerformanceCoeffService service;
    
    @Mock
    private MergeConsPerformanceCoeffRepository mergeConsPerformanceCoeffRepository;

    public void initData() throws IllegalAccessException {
        MockitoAnnotations.initMocks(this);
        service = PowerMockito.spy(new MergeConsPerformanceCoeffService());
        Field fieldRepository = getField(MergeConsPerformanceCoeffService.class, "mergeConsPerformanceCoeffRepository");
        fieldRepository.set(service, mergeConsPerformanceCoeffRepository);
    }

    /**
     * 场景1：当来源类型为空时，不做任何处理
     */
    @Test
    public void test_01() throws Exception {
        initData();
        ConsPerformanceCoeffBO bo = new ConsPerformanceCoeffBO();
        bo.setConsCustNo("TEST001");
        CmCustSourceCoeffBO custSourceCoeff = new CmCustSourceCoeffBO();
        
        invokeMethod(service, "processPerformanceCoeff", bo, custSourceCoeff);
        
        Assert.assertNull(bo.getCommissionCoeffStart());
        Assert.assertNull(bo.getCustConversionCoeff());
        Assert.assertNull(bo.getCxb());
    }

    /**
     * 场景2：当查询不到来源类型对应的系数配置时，不做任何处理
     */
    @Test
    public void test_02() throws Exception {
        initData();
        ConsPerformanceCoeffBO bo = new ConsPerformanceCoeffBO();
        bo.setConsCustNo("TEST001");
        bo.setSourceType("TEST_SOURCE");
        
        invokeMethod(service, "processPerformanceCoeff", bo, null);
        
        Assert.assertNull(bo.getCommissionCoeffStart());
        Assert.assertNull(bo.getCustConversionCoeff());
        Assert.assertNull(bo.getCxb());
    }

    /**
     * 场景3：当查询到系数配置时，正确设置佣金系数起点
     */
    @Test
    public void test_03() throws Exception {
        initData();
        ConsPerformanceCoeffBO bo = new ConsPerformanceCoeffBO();
        bo.setConsCustNo("TEST001");
        bo.setSourceType("TEST_SOURCE");
        
        CmCustSourceCoeffBO custSourceCoeff = new CmCustSourceCoeffBO();
        custSourceCoeff.setStartPoint("0.5");
        
        invokeMethod(service, "processPerformanceCoeff", bo, custSourceCoeff);
        
        Assert.assertEquals("0.5", bo.getCommissionCoeffStart());
    }

    /**
     * 场景4：当查询到系数配置时，正确设置客户折算系数（入参有值时使用入参值）
     */
    @Test
    public void test_04() throws Exception {
        initData();
        ConsPerformanceCoeffBO bo = new ConsPerformanceCoeffBO();
        bo.setConsCustNo("TEST001");
        bo.setSourceType("TEST_SOURCE");
        bo.setCustConversionCoeff(new BigDecimal("0.8"));
        
        CmCustSourceCoeffBO custSourceCoeff = new CmCustSourceCoeffBO();
        custSourceCoeff.setZbCoeff(new BigDecimal("0.5"));
        
        invokeMethod(service, "processPerformanceCoeff", bo, custSourceCoeff);
        
        Assert.assertEquals(0, new BigDecimal("0.8").compareTo(bo.getCustConversionCoeff()));
    }

    /**
     * 场景5：当查询到系数配置时，正确设置客户折算系数（入参无值时使用配置值）
     */
    @Test
    public void test_05() throws Exception {
        initData();
        ConsPerformanceCoeffBO bo = new ConsPerformanceCoeffBO();
        bo.setConsCustNo("TEST001");
        bo.setSourceType("TEST_SOURCE");
        
        CmCustSourceCoeffBO custSourceCoeff = new CmCustSourceCoeffBO();
        custSourceCoeff.setZbCoeff(new BigDecimal("0.5"));
        
        invokeMethod(service, "processPerformanceCoeff", bo, custSourceCoeff);
        
        Assert.assertEquals(0, new BigDecimal("0.5").compareTo(bo.getCustConversionCoeff()));
    }

    /**
     * 场景6：当查询到系数配置时，正确设置存续B值
     */
    @Test
    public void test_06() throws Exception {
        initData();
        ConsPerformanceCoeffBO bo = new ConsPerformanceCoeffBO();
        bo.setConsCustNo("TEST001");
        bo.setSourceType("TEST_SOURCE");
        
        CmCustSourceCoeffBO custSourceCoeff = new CmCustSourceCoeffBO();
        custSourceCoeff.setCxb("0.3");
        
        invokeMethod(service, "processPerformanceCoeff", bo, custSourceCoeff);
        
        Assert.assertEquals("0.3", bo.getCxb());
    }
} 