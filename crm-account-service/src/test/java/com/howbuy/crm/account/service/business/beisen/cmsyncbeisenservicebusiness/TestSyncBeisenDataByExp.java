/**
 * Copyright (c) 2024, <PERSON><PERSON><PERSON><PERSON> HOWBUY INVESTMENT MANAGEMENT Co., Ltd.
 * All right reserved.
 * <p>
 * THIS IS UNPUBLISHED PROPRIETARY SOURCE CODE OF HOWBUY INVESTMENT
 * MANAGEMENT CO., LTD.  THE CONTENTS OF THIS FILE MAY NOT BE DISCLOSED
 * TO THIRD PARTIES, COPIED OR DUPLICATED IN ANY FORM, IN WHOLE OR IN PART,
 * WITHOUT THE PRIOR WRITTEN PERMISSION OF HOWBUY INVESTMENT MANAGEMENT
 * CO., LTD.
 */
package com.howbuy.crm.account.service.business.beisen.cmsyncbeisenservicebusiness;

import com.howbuy.crm.account.dao.po.beisen.CmBeisenUserInfoPO;
import com.howbuy.crm.account.dao.po.consultantexp.CmConsultantExpModifyFlagPO;
import com.howbuy.crm.account.dao.po.consultantexp.CmConsultantExpPO;
import com.howbuy.crm.account.service.bo.beisen.CmConcultantExpBeisenBO;
import com.howbuy.crm.account.service.business.beisen.CmSyncBeisenServiceBusiness;
import com.howbuy.crm.account.service.business.beisen.calcenum.CrmBeisenMappingEnum;
import com.howbuy.crm.account.service.repository.beisen.CmSyncBeisenRepository;
import org.junit.Assert;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.Mockito;
import org.mockito.MockitoAnnotations;
import org.powermock.api.mockito.PowerMockito;
import org.powermock.core.classloader.annotations.PowerMockIgnore;
import org.powermock.core.classloader.annotations.PrepareForTest;
import org.powermock.modules.testng.PowerMockTestCase;
import org.testng.annotations.Test;

import java.lang.reflect.Field;
import java.util.*;

import static org.powermock.api.mockito.PowerMockito.doReturn;
import static org.powermock.api.mockito.PowerMockito.mockStatic;
import static org.powermock.reflect.Whitebox.getField;
import static org.powermock.reflect.Whitebox.invokeMethod;

/**
 * @description: (花名册同步北森数据)
 * <AUTHOR>
 * @date 2024/12/27 10:19
 * @since JDK 1.8
 *
 * 场景1：没有需要更新的花名册数据不更新
 * 场景2：有需要更新的花名册数据，通过北森ID和用户编号未找到北森用户数据，不更新
 * 场景3：有需要更新的花名册数据，通过北森ID和用户编号找到北森用户数据，花名册员工状态为离职，北森离职状态为生效，不更新
 * 场景4：有需要更新的花名册数据，通过北森ID和用户编号找到北森用户数据，花名册员工状态不为离职或北森离职状态不为生效，执行同步规则
 */

@PowerMockIgnore("javax.management.*")
@Test
@PrepareForTest({CmSyncBeisenServiceBusiness.class, CrmBeisenMappingEnum.class})
public class TestSyncBeisenDataByExp extends PowerMockTestCase {
    @InjectMocks
    private CmSyncBeisenServiceBusiness service;
    @Mock
    private CmSyncBeisenRepository cmSyncBeisenRepository;




    public void init() throws IllegalAccessException {
        MockitoAnnotations.initMocks(this);
        service = PowerMockito.spy(new CmSyncBeisenServiceBusiness());
        Field fieldMapper1 = getField(CmSyncBeisenServiceBusiness.class, "cmSyncBeisenRepository");
        fieldMapper1.set(service, cmSyncBeisenRepository);
    }

    @Test
    public void test_01() throws Exception {
        init();
        List<CmBeisenUserInfoPO> beisenUserInfoList = new ArrayList<>();
        CmBeisenUserInfoPO po = new CmBeisenUserInfoPO();
        po.setExt3("C0000001");
        beisenUserInfoList.add(po);
        doReturn(Collections.emptyMap()).when(service, "getCmConsultantExpModifyFlagMap");
        doReturn(Collections.emptyMap()).when(service, "getBeisenUserInfoByBeisenId", Mockito.anyList());
        doReturn(Collections.emptyMap()).when(service, "getBeisenUserInfoByUserNo", Mockito.anyList());
        doReturn(Collections.emptyMap()).when(service, "getBeisenUserInfoByEmail", Mockito.anyList());
        PowerMockito.doNothing().when(service, "transCityCodeMap", Mockito.anyMap(), Mockito.anyMap());
        List<String> resultList = invokeMethod(service, "syncBeisenDataByExp", new ArrayList<>(), new CmConcultantExpBeisenBO(), beisenUserInfoList);
        Boolean resultBool = resultList.size() == 0;
        Assert.assertTrue(resultBool);
    }

    @Test
    public void test_02() throws Exception {
        init();
        List<CmBeisenUserInfoPO> beisenUserInfoList = new ArrayList<>();
        CmBeisenUserInfoPO po = new CmBeisenUserInfoPO();
        po.setExt3("C0000001");
        beisenUserInfoList.add(po);
        List<CmConsultantExpPO> expList = new ArrayList<>();
        CmConsultantExpPO cmConsultantExpPO = new CmConsultantExpPO();
        cmConsultantExpPO.setUserid("san.zhang");
        expList.add(cmConsultantExpPO);
        doReturn(Collections.emptyMap()).when(service, "getCmConsultantExpModifyFlagMap");
        doReturn(Collections.emptyMap()).when(service, "getBeisenUserInfoByBeisenId", Mockito.anyList());
        doReturn(Collections.emptyMap()).when(service, "getBeisenUserInfoByUserNo", Mockito.anyList());
        doReturn(Collections.emptyMap()).when(service, "getBeisenUserInfoByEmail", Mockito.anyList());
        PowerMockito.doNothing().when(service, "transCityCodeMap", Mockito.anyMap(), Mockito.anyMap());
        doReturn(new CmBeisenUserInfoPO()).when(service, "getBeisenUserInfo", Mockito.anyString(), Mockito.anyString(), Mockito.anyMap(), Mockito.anyMap());
        List<String> resultList = invokeMethod(service, "syncBeisenDataByExp", expList, new CmConcultantExpBeisenBO(), beisenUserInfoList);
        Boolean resultBool = resultList.size() == 0;
        Assert.assertTrue(resultBool);
    }

    @Test
    public void test_03() throws Exception {
        init();
        List<CmBeisenUserInfoPO> beisenUserInfoList = new ArrayList<>();
        CmBeisenUserInfoPO po = new CmBeisenUserInfoPO();
        po.setExt3("C0000001");
        po.setExt25("生效");
        beisenUserInfoList.add(po);
        List<CmConsultantExpPO> expList = new ArrayList<>();
        CmConsultantExpPO cmConsultantExpPO = new CmConsultantExpPO();
        cmConsultantExpPO.setUserid("san.zhang");
        cmConsultantExpPO.setWorktype("2");
        cmConsultantExpPO.setBeisenid("C0000001");
        cmConsultantExpPO.setUserno("1111111");
        expList.add(cmConsultantExpPO);
        doReturn(Collections.emptyMap()).when(service, "getCmConsultantExpModifyFlagMap");
        doReturn(Collections.emptyMap()).when(service, "getBeisenUserInfoByBeisenId", Mockito.anyList());
        doReturn(Collections.emptyMap()).when(service, "getBeisenUserInfoByUserNo", Mockito.anyList());
        doReturn(Collections.emptyMap()).when(service, "getBeisenUserInfoByEmail", Mockito.anyList());
        PowerMockito.doNothing().when(service, "transCityCodeMap", Mockito.anyMap(), Mockito.anyMap());
        doReturn(po).when(service, "getBeisenUserInfo", Mockito.anyString(), Mockito.anyString(), Mockito.anyMap(), Mockito.anyMap());
        List<String> resultList = invokeMethod(service, "syncBeisenDataByExp", expList, new CmConcultantExpBeisenBO(), beisenUserInfoList);
        Boolean resultBool = resultList.size() == 0;
        Assert.assertTrue(resultBool);
    }

    @Test
    public void test_04() throws Exception {
        init();
        List<CmBeisenUserInfoPO> beisenUserInfoList = new ArrayList<>();
        CmBeisenUserInfoPO po = new CmBeisenUserInfoPO();
        po.setExt3("C0000001");
        beisenUserInfoList.add(po);
        List<CmConsultantExpPO> expList = new ArrayList<>();
        CmConsultantExpPO cmConsultantExpPO = new CmConsultantExpPO();
        cmConsultantExpPO.setUserid("san.zhang");
        cmConsultantExpPO.setWorktype("2");
        cmConsultantExpPO.setBeisenid("C0000001");
        cmConsultantExpPO.setUserno("1111111");
        expList.add(cmConsultantExpPO);
        Map<String, CmConsultantExpModifyFlagPO> modifyFlagMap = new HashMap<>();
        CmConsultantExpModifyFlagPO cmConsultantExpModifyFlagPO = new CmConsultantExpModifyFlagPO();
        cmConsultantExpModifyFlagPO.setUserid(cmConsultantExpPO.getUserid());
        modifyFlagMap.put(cmConsultantExpPO.getUserid(), cmConsultantExpModifyFlagPO);
        doReturn(modifyFlagMap).when(service, "getCmConsultantExpModifyFlagMap");
        doReturn(Collections.emptyMap()).when(service, "getBeisenUserInfoByBeisenId", Mockito.anyList());
        doReturn(Collections.emptyMap()).when(service, "getBeisenUserInfoByUserNo", Mockito.anyList());
        doReturn(Collections.emptyMap()).when(service, "getBeisenUserInfoByEmail", Mockito.anyList());
        PowerMockito.doNothing().when(service, "transCityCodeMap", Mockito.anyMap(), Mockito.anyMap());
        doReturn(po).when(service, "getBeisenUserInfo", Mockito.anyString(), Mockito.anyString(), Mockito.anyMap(), Mockito.anyMap());
        mockStatic(CrmBeisenMappingEnum.class);
        PowerMockito.doNothing().when(CrmBeisenMappingEnum.class, "syncBeisenData", Mockito.any());
        PowerMockito.doNothing().when(service, "save", Mockito.anyList(), Mockito.anyList());
        List<String> resultList = invokeMethod(service, "syncBeisenDataByExp", expList, new CmConcultantExpBeisenBO(), beisenUserInfoList);
        Boolean resultBool = resultList.size() == 0;
        Assert.assertTrue(resultBool);
    }

}