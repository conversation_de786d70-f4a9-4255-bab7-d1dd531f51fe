/**
 * Copyright (c) 2024, <PERSON><PERSON><PERSON><PERSON> HOWBUY INVESTMENT MANAGEMENT Co., Ltd.
 * All right reserved.
 * <p>
 * THIS IS UNPUBLISHED PROPRIETARY SOURCE CODE OF HOWBUY INVESTMENT
 * MANAGEMENT CO., LTD.  THE CONTENTS OF THIS FILE MAY NOT BE DISCLOSED
 * TO THIRD PARTIES, COPIED OR DUPLICATED IN ANY FORM, IN WHOLE OR IN PART,
 * WITHOUT THE PRIOR WRITTEN PERMISSION OF HOWBUY INVESTMENT MANAGEMENT
 * CO., LTD.
 */
package com.howbuy.crm.account.service.business.beisen.cmsyncbeisenservicebusiness;

import com.howbuy.crm.account.client.response.dictionary.CityListVO;
import com.howbuy.crm.account.service.bo.beisen.AreaBO;
import com.howbuy.crm.account.service.business.beisen.CmSyncBeisenServiceBusiness;
import com.howbuy.crm.account.service.commom.constant.Constants;
import com.howbuy.crm.account.service.outerservice.dictionary.DictionaryOuterService;
import org.junit.Assert;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.MockitoAnnotations;
import org.powermock.api.mockito.PowerMockito;
import org.powermock.core.classloader.annotations.PowerMockIgnore;
import org.powermock.core.classloader.annotations.PrepareForTest;
import org.powermock.modules.testng.PowerMockTestCase;
import org.testng.annotations.Test;

import java.lang.reflect.Field;
import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

import static org.powermock.reflect.Whitebox.getField;
import static org.powermock.reflect.Whitebox.invokeMethod;

/**
 * @description: (北森同步花名册获取省市区数据转换)
 * <AUTHOR>
 * @date 2024/11/25 11:16
 * @since JDK 1.8
 *
 * 场景1：获取所有省市区集合，循环省集合，获得对应市集合，把市集合写入市map，循环市集合获得对应区集合，把区集合写入区map
 * 场景2：获取所有省市区集合，循环省集合，获得对应市集合，把市集合写入市map，循环市集合获得对应区集合，没有区集合退出
 *
 */

@PowerMockIgnore("javax.management.*")
@Test
@PrepareForTest({CmSyncBeisenServiceBusiness.class})
public class TestTransCityCodeMap extends PowerMockTestCase {
    @InjectMocks
    private CmSyncBeisenServiceBusiness service;
    @Mock
    private DictionaryOuterService dictionaryOuterService;

    private Map<String, AreaBO> cityMapByProv;
    private Map<String, AreaBO > countryMapByCity;

    /**
     * 浙江省
     */
    private static final String ZJS = "浙江省";
    /**
     * 湖州市
     */
    private static final String HZS = "湖州市";
    /**
     * 安吉县
     */
    private static final String AJX = "安吉县";




    public void init() throws IllegalAccessException {
        MockitoAnnotations.initMocks(this);
        service = PowerMockito.spy(new CmSyncBeisenServiceBusiness());
        Field fieldMapper1 = getField(CmSyncBeisenServiceBusiness.class, "dictionaryOuterService");
        fieldMapper1.set(service, dictionaryOuterService);
    }

    @Test
    public void test_01() throws Exception {
        init();
        cityMapByProv = new HashMap<>();
        countryMapByCity = new HashMap<>();
        List<CityListVO> getProvCityList = new ArrayList<>();
        CityListVO voProv = new CityListVO();
        voProv.setDm("1");
        voProv.setMc(ZJS);
        List<CityListVO> dataListCity = new ArrayList<>();
        CityListVO voCity = new CityListVO();
        voCity.setDm("11");
        voCity.setMc(HZS);
        List<CityListVO> dataListCountry = new ArrayList<>();
        CityListVO voCountry = new CityListVO();
        voCountry.setDm("111");
        voCountry.setMc(AJX);
        dataListCountry.add(voCountry);
        voCity.setDataList(dataListCountry);
        dataListCity.add(voCity);
        voProv.setDataList(dataListCity);
        getProvCityList.add(voProv);
        PowerMockito.doReturn(getProvCityList).when(dictionaryOuterService, "getCityListVO");
        invokeMethod(service, "transCityCodeMap" , cityMapByProv, countryMapByCity);
        int count = BigDecimal.ONE.intValue();
        Boolean sizeBool = count == cityMapByProv.size() && count == countryMapByCity.size();
        Assert.assertTrue(sizeBool);
    }

    @Test
    public void test_02() throws Exception {
        init();
        cityMapByProv = new HashMap<>();
        countryMapByCity = new HashMap<>();
        List<CityListVO> getProvCityList = new ArrayList<>();
        CityListVO voProv = new CityListVO();
        voProv.setDm("1");
        voProv.setMc(ZJS);
        List<CityListVO> dataListCity = new ArrayList<>();
        CityListVO voCity = new CityListVO();
        voCity.setDm("11");
        voCity.setMc(HZS);
        dataListCity.add(voCity);
        voProv.setDataList(dataListCity);
        getProvCityList.add(voProv);
        PowerMockito.doReturn(getProvCityList).when(dictionaryOuterService, "getCityListVO");
        invokeMethod(service, "transCityCodeMap" , cityMapByProv, countryMapByCity);
        Boolean sizeBool = BigDecimal.ONE.intValue() == cityMapByProv.size() && BigDecimal.ZERO.intValue() == countryMapByCity.size();
        Assert.assertTrue(sizeBool);
    }

}