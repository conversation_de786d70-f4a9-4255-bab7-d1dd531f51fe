# TestGetManageUserList 单元测试说明文档

## 1. 测试概述

### 1.1 测试目的

本测试用例用于测试 `CsCommVisitService` 类中的 `getManageUserList` 方法，该方法用于获取投顾的主管列表，包括分总、区域执行副总和区域总。

### 1.2 测试环境

- 测试框架：TestNG
- Mock框架：PowerMock、Mockito

### 1.3 被测试类

```java
com.howbuy.crm.account.service.service.commvisit.CsCommVisitService
```

### 1.4 被测试方法

```java
private List<VisitInitDataResponse.UserInfo> getManageUserList(VisitInitDataRequest request, CmConsultantExpPO cmConsultantExp)
```

## 2. 测试方法说明

### 2.1 方法功能

`getManageUserList` 方法用于获取投顾的主管列表，包括：
- 分总（Division Manager）
- 区域执行副总（Vice President）
- 区域总（Area Manager）

### 2.2 方法参数

- `VisitInitDataRequest request`：请求参数，包含操作人的投顾代码（consCode）
- `CmConsultantExpPO cmConsultantExp`：投顾扩展信息，包含网点代码（outletcode）和区域代码（areacode）

### 2.3 方法返回值

- `List<VisitInitDataResponse.UserInfo>`：主管信息列表

### 2.4 方法逻辑

1. 检查投顾扩展信息是否为空，如果为空则返回空列表
2. 根据网点代码获取分总列表
3. 根据网点代码获取区域执行副总列表
4. 如果区域代码不为空，则根据区域代码获取区域总列表
5. 合并所有主管列表，并排除操作人自己
6. 根据主管代码获取主管详细信息
7. 将主管信息转换为返回格式并返回

## 3. 测试场景

### 3.1 投顾信息为空（testGetManageUserList_NullConsultantExp）

**测试目的**：验证当投顾扩展信息为空时，方法是否正确返回空列表。

**测试步骤**：
1. 准备请求参数，设置投顾代码
2. 调用 `getManageUserList` 方法，传入请求参数和 null 作为投顾扩展信息
3. 验证返回结果是否为空列表

**预期结果**：返回非空但内容为空的列表。

### 3.2 正常获取主管列表（testGetManageUserList_Normal）

**测试目的**：验证在正常情况下，方法是否能正确获取并返回所有类型的主管列表。

**测试步骤**：
1. 准备请求参数和投顾扩展信息
2. 模拟分总层级常量和分总列表
3. 模拟区域执行副总列表
4. 模拟区域总层级常量和区域总列表
5. 模拟主管详细信息
6. 调用 `getManageUserList` 方法
7. 验证返回结果是否包含所有主管信息

**预期结果**：返回包含所有主管信息的列表，列表大小为6，包含所有模拟的主管代码。

### 3.3 排除操作人（testGetManageUserList_ExcludeOperator）

**测试目的**：验证当操作人是主管之一时，返回的主管列表中是否不包含操作人自己。

**测试步骤**：
1. 准备请求参数，设置操作人为分总之一（DM001）
2. 准备投顾扩展信息
3. 模拟分总层级常量和分总列表（包含操作人）
4. 模拟区域执行副总列表
5. 模拟区域总层级常量和区域总列表
6. 模拟主管详细信息（不包含操作人）
7. 调用 `getManageUserList` 方法
8. 验证返回结果是否不包含操作人

**预期结果**：返回的主管列表大小为5，不包含操作人（DM001）。

### 3.4 主管列表为空（testGetManageUserList_EmptyManagerList）

**测试目的**：验证当所有主管列表都为空时，方法是否正确返回空列表。

**测试步骤**：
1. 准备请求参数和投顾扩展信息
2. 模拟分总层级常量，但分总列表为空
3. 模拟区域执行副总列表为空
4. 模拟区域总层级常量，但区域总列表为空
5. 调用 `getManageUserList` 方法
6. 验证返回结果是否为空列表

**预期结果**：返回非空但内容为空的列表。

### 3.5 区域代码为空（testGetManageUserList_NullAreaCode）

**测试目的**：验证当区域代码为空时，方法是否只返回分总和区域执行副总列表，而不包含区域总。

**测试步骤**：
1. 准备请求参数和投顾扩展信息，设置区域代码为null
2. 模拟分总层级常量和分总列表
3. 模拟区域执行副总列表
4. 模拟主管详细信息
5. 调用 `getManageUserList` 方法
6. 验证返回结果是否只包含分总和区域执行副总

**预期结果**：返回的主管列表大小为4，只包含分总和区域执行副总的代码。

### 3.6 主管信息查询结果为空（testGetManageUserList_EmptyManagerInfo）

**测试目的**：验证当主管代码存在但查询主管详细信息为空时，方法是否正确返回空列表。

**测试步骤**：
1. 准备请求参数和投顾扩展信息
2. 模拟分总层级常量和分总列表
3. 模拟区域执行副总列表
4. 模拟区域总层级常量和区域总列表
5. 模拟查询主管详细信息返回空列表
6. 调用 `getManageUserList` 方法
7. 验证返回结果是否为空列表

**预期结果**：返回非空但内容为空的列表。

## 4. 测试覆盖情况

本测试用例覆盖了 `getManageUserList` 方法的以下分支：

1. 投顾扩展信息为空的情况
2. 正常获取所有类型主管的情况
3. 操作人是主管之一的情况
4. 所有主管列表为空的情况
5. 区域代码为空的情况
6. 主管详细信息查询结果为空的情况

通过这些测试场景，基本覆盖了 `getManageUserList` 方法的所有主要分支和边界条件。

## 5. 测试依赖

本测试依赖以下模拟对象：

- `CmConsultantExpService`：用于获取主管层级常量和主管列表
- `CmConsultantRepository`：用于获取主管详细信息
- `CmConsultantExpRepository`：投顾扩展信息相关操作

## 6. 注意事项

1. 本测试使用了PowerMock来测试私有方法，通过反射获取方法引用并设置可访问性
2. 测试中使用了大量的模拟数据，确保了测试的独立性和可重复性
3. 测试覆盖了多种边界条件，提高了测试的全面性和有效性