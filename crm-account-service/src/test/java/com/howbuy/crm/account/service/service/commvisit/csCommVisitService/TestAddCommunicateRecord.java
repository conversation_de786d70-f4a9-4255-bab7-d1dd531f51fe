/**
 * Copyright (c) 2024, <PERSON><PERSON><PERSON><PERSON> HOWBUY INVESTMENT MANAGEMENT Co., Ltd.
 * All right reserved.
 * <p>
 * THIS IS UNPUBLISHED PROPRIETARY SOURCE CODE OF HOWBUY INVESTMENT
 * MANAGEMENT CO., LTD.  THE CONTENTS OF THIS FILE MAY NOT BE DISCLOSED
 * TO THIRD PARTIES, COPIED OR DUPLICATED IN ANY FORM, IN WHOLE OR IN PART,
 * WITHOUT THE PRIOR WRITTEN PERMISSION OF HOWBUY INVESTMENT MANAGEMENT
 * CO., LTD.
 */
package com.howbuy.crm.account.service.service.commvisit.csCommVisitService;

import com.howbuy.crm.account.client.enums.commvisit.AccompanyingTypeEnum;
import com.howbuy.crm.account.client.enums.commvisit.VisitPurposeEnum;
import com.howbuy.crm.account.client.request.commvisit.AddCommunicateRecordRequest;
import com.howbuy.crm.account.client.response.Response;
import com.howbuy.crm.account.client.response.commvisit.AddCommunicateRecordResponse;
import com.howbuy.crm.account.dao.mapper.CommonMapper;
import com.howbuy.crm.account.dao.po.commvisit.CmVisitMinutesAccompanyingPO;
import com.howbuy.crm.account.dao.po.commvisit.CmVisitMinutesPO;
import com.howbuy.crm.account.dao.po.commvisit.CsCommunicateVisitPO;
import com.howbuy.crm.account.service.commom.constant.SequenceConstants;
import com.howbuy.crm.account.service.domain.consultantexp.ConsultantUserLevelDTO;
import com.howbuy.crm.account.service.repository.commvisit.CmVisitMinutesAccompanyRepository;
import com.howbuy.crm.account.service.repository.commvisit.CmVisitMinutesRepository;
import com.howbuy.crm.account.service.repository.commvisit.CsCommunicateVisitRepository;
import com.howbuy.crm.account.service.service.commvisit.CmVisitMinutesService;
import com.howbuy.crm.account.service.service.commvisit.CsCommVisitService;
import com.howbuy.crm.account.service.service.consultantexp.CmConsultantExpService;
import org.junit.Assert;
import org.mockito.ArgumentCaptor;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.powermock.core.classloader.annotations.PowerMockIgnore;
import org.powermock.modules.testng.PowerMockTestCase;
import org.testng.annotations.BeforeMethod;
import org.testng.annotations.Test;

import java.util.ArrayList;
import java.util.List;

import static org.mockito.ArgumentMatchers.any;
import static org.mockito.ArgumentMatchers.anyString;
import static org.mockito.Mockito.times;
import static org.mockito.Mockito.verify;
import static org.mockito.Mockito.when;

/**
 * <AUTHOR>
 * @description: CsCommVisitService单元测试
 * @date 2024/05/20 10:00
 * @since JDK 1.8
 */
@PowerMockIgnore("javax.management.*")
@Test
public class TestAddCommunicateRecord extends PowerMockTestCase {

    @InjectMocks
    private CsCommVisitService csCommVisitService;

    @Mock
    private CsCommunicateVisitRepository csCommunicateVisitRepository;

    @Mock
    private CmVisitMinutesRepository cmVisitMinutesRepository;

    @Mock
    private CmVisitMinutesAccompanyRepository cmVisitMinutesAccompanyRepository;

    @Mock
    private CommonMapper commonMapper;

    @Mock
    private CmConsultantExpService cmConsultantExpService;

    @Mock
    private CmVisitMinutesService cmVisitMinutesService;

    private static final String TEST_CONS_CUST_NO = "TEST_CONS_CUST_NO";
    private static final String TEST_OPERATOR = "TEST_OPERATOR";
    private static final String TEST_COMMUNICATE_ID = "TEST_COMMUNICATE_ID";
    private static final String TEST_VISIT_MINUTES_ID = "TEST_VISIT_MINUTES_ID";
    private static final String TEST_ACCOMPANYING_ID = "TEST_ACCOMPANYING_ID";

    @BeforeMethod
    public void setUp() {
        // 设置序列值返回
        when(commonMapper.getSeqValue("SEQ_CS_COMMUNICATE_VISIT_ID")).thenReturn(TEST_COMMUNICATE_ID);
        when(commonMapper.getSeqValue("SEQ_PCUSTREC")).thenReturn("TEST_SEQ_PCUSTREC");
        when(commonMapper.getSeqValue(SequenceConstants.SEQ_CM_VISIT_MINUTES)).thenReturn(TEST_VISIT_MINUTES_ID);
        when(commonMapper.getSeqValue(SequenceConstants.SEQ_VISIT_MINUTES_ACCOMPANYING)).thenReturn(TEST_ACCOMPANYING_ID);
    }

    /**
     * 测试添加沟通记录 - 无拜访纪要
     */
    @Test
    public void testAddCommunicateRecord_WithoutVisitMinutes() {
        // 准备测试数据
        AddCommunicateRecordRequest request = createBasicRequest();
        
        // 执行测试
        Response<AddCommunicateRecordResponse> response = csCommVisitService.addCommunicateRecord(request);
        
        // 验证结果
        Assert.assertNotNull(response);
        Assert.assertTrue(response.isSuccess());
        Assert.assertEquals(TEST_COMMUNICATE_ID, response.getData().getCommunicateId());
        Assert.assertNull(response.getData().getVisitMinutesId());
        
        // 验证调用
        verify(csCommunicateVisitRepository, times(1)).insertCommunicateVisit(any(CsCommunicateVisitPO.class));
        verify(cmVisitMinutesRepository, times(0)).insert(any(CmVisitMinutesPO.class));
    }

    /**
     * 测试添加沟通记录 - 有拜访纪要但无陪访人
     */
    @Test
    public void testAddCommunicateRecord_WithVisitMinutesNoAccompanying() {
        // 准备测试数据
        AddCommunicateRecordRequest request = createRequestWithVisitMinutes(false);
        
        // 模拟主管信息
        ConsultantUserLevelDTO managerUserLevelDto = new ConsultantUserLevelDTO();
        managerUserLevelDto.setConstCode("MANAGER_CODE");
        managerUserLevelDto.setUserLevel("MANAGER_LEVEL");
        when(cmConsultantExpService.getManagerInfoByConsCode(TEST_OPERATOR)).thenReturn(managerUserLevelDto);
        
        // 执行测试
        Response<AddCommunicateRecordResponse> response = csCommVisitService.addCommunicateRecord(request);
        
        // 验证结果
        Assert.assertNotNull(response);
        Assert.assertTrue(response.isSuccess());
        Assert.assertEquals(TEST_COMMUNICATE_ID, response.getData().getCommunicateId());
        Assert.assertEquals(TEST_VISIT_MINUTES_ID, response.getData().getVisitMinutesId());
        
        // 验证调用
        verify(csCommunicateVisitRepository, times(1)).insertCommunicateVisit(any(CsCommunicateVisitPO.class));
        verify(cmVisitMinutesRepository, times(1)).insert(any(CmVisitMinutesPO.class));
        verify(cmVisitMinutesAccompanyRepository, times(0)).insert(any(CmVisitMinutesAccompanyingPO.class));
    }

    /**
     * 测试添加沟通记录 - 有拜访纪要和陪访人
     */
    @Test
    public void testAddCommunicateRecord_WithVisitMinutesAndAccompanying() {
        // 准备测试数据
        AddCommunicateRecordRequest request = createRequestWithVisitMinutes(true);
        
        // 模拟主管信息
        ConsultantUserLevelDTO managerUserLevelDto = new ConsultantUserLevelDTO();
        managerUserLevelDto.setConstCode("MANAGER_CODE");
        managerUserLevelDto.setUserLevel("MANAGER_LEVEL");
        when(cmConsultantExpService.getManagerInfoByConsCode(TEST_OPERATOR)).thenReturn(managerUserLevelDto);
        
        // 模拟陪访人层级
        when(cmConsultantExpService.getAccompanyHighestLevelByConsCode(anyString())).thenReturn("ACCOMPANYING_LEVEL");
        
        // 执行测试
        Response<AddCommunicateRecordResponse> response = csCommVisitService.addCommunicateRecord(request);
        
        // 验证结果
        Assert.assertNotNull(response);
        Assert.assertTrue(response.isSuccess());
        Assert.assertEquals(TEST_COMMUNICATE_ID, response.getData().getCommunicateId());
        Assert.assertEquals(TEST_VISIT_MINUTES_ID, response.getData().getVisitMinutesId());
        
        // 验证调用
        verify(csCommunicateVisitRepository, times(1)).insertCommunicateVisit(any(CsCommunicateVisitPO.class));
        verify(cmVisitMinutesRepository, times(1)).insert(any(CmVisitMinutesPO.class));
        
        // 验证陪访人保存
        ArgumentCaptor<CmVisitMinutesAccompanyingPO> accompanyingCaptor = ArgumentCaptor.forClass(CmVisitMinutesAccompanyingPO.class);
        verify(cmVisitMinutesAccompanyRepository, times(2)).insert(accompanyingCaptor.capture());
        
        List<CmVisitMinutesAccompanyingPO> capturedAccompanyings = accompanyingCaptor.getAllValues();
        Assert.assertEquals(2, capturedAccompanyings.size());
        Assert.assertEquals(TEST_VISIT_MINUTES_ID, capturedAccompanyings.get(0).getVisitMinutesId());
    }

    /**
     * 测试添加沟通记录 - IPS陪访场景，需要发送通知
     */
    @Test
    public void testAddCommunicateRecord_WithIPSVisitPurpose() {
        // 准备测试数据
        AddCommunicateRecordRequest request = createRequestWithVisitMinutes(true);
        request.getVisitMinutesReq().setVisitPurpose(VisitPurposeEnum.IPS_VISIT.getCode());
        
        // 模拟主管信息
        ConsultantUserLevelDTO managerUserLevelDto = new ConsultantUserLevelDTO();
        managerUserLevelDto.setConstCode("MANAGER_CODE");
        managerUserLevelDto.setUserLevel("MANAGER_LEVEL");
        when(cmConsultantExpService.getManagerInfoByConsCode(TEST_OPERATOR)).thenReturn(managerUserLevelDto);
        
        // 模拟陪访人层级
        when(cmConsultantExpService.getAccompanyHighestLevelByConsCode(anyString())).thenReturn("ACCOMPANYING_LEVEL");
        
        // 执行测试
        Response<AddCommunicateRecordResponse> response = csCommVisitService.addCommunicateRecord(request);
        
        // 验证结果
        Assert.assertNotNull(response);
        Assert.assertTrue(response.isSuccess());
        
        // 验证发送通知
        verify(cmVisitMinutesService, times(1)).addAccompanyingPushMsg(any(), anyString(), any(), anyString(), any());
    }

    /**
     * 创建基本请求对象
     */
    private AddCommunicateRecordRequest createBasicRequest() {
        AddCommunicateRecordRequest request = new AddCommunicateRecordRequest();
        
        // 设置操作人
        request.setOperator(TEST_OPERATOR);
        
        // 设置沟通记录信息
        AddCommunicateRecordRequest.CommunicateReq communicateReq = new AddCommunicateRecordRequest.CommunicateReq();
        communicateReq.setConsCustNo(TEST_CONS_CUST_NO);
        communicateReq.setVisitDate("20240520");
        communicateReq.setVisittype("1"); // 假设1表示电话沟通
        communicateReq.setCommcontent("测试沟通内容");
        communicateReq.setConsulttype("1");
        communicateReq.setVisitclassify("0"); // 正常客户
        request.setCommunicateReq(communicateReq);
        
        return request;
    }

    /**
     * 创建带拜访纪要的请求对象
     * @param withAccompanying 是否包含陪访人
     */
    private AddCommunicateRecordRequest createRequestWithVisitMinutes(boolean withAccompanying) {
        AddCommunicateRecordRequest request = createBasicRequest();
        
        // 设置拜访纪要信息
        AddCommunicateRecordRequest.VisitMinutesReq visitMinutesReq = new AddCommunicateRecordRequest.VisitMinutesReq();
        visitMinutesReq.setVisitPurpose(VisitPurposeEnum.FIRST_MEETING.getCode()); // 常规拜访
        visitMinutesReq.setAssetReportId("ASSET_REPORT_ID");
        visitMinutesReq.setMarketVal("1000000");
        visitMinutesReq.setHealthAvgStar("4.5");
        visitMinutesReq.setGiveInformation("给客户的资料");
        visitMinutesReq.setAttendRole("客户本人");
        visitMinutesReq.setProductServiceFeedback("产品服务反馈");
        visitMinutesReq.setIpsFeedback("IPS反馈");
        visitMinutesReq.setAddAmountRmb("50000");
        visitMinutesReq.setAddAmountForeign("10000");
        visitMinutesReq.setFocusAsset("关注资产");
        visitMinutesReq.setEstimateNeedBusiness("预计业务需求");
        visitMinutesReq.setNextPlan("下一步计划");
        
        // 设置陪访人
        if (withAccompanying) {
            List<AddCommunicateRecordRequest.AccompanyingPerson> accompanyingList = new ArrayList<>();
            
            // 添加主管陪访人
            AddCommunicateRecordRequest.AccompanyingPerson manager = new AddCommunicateRecordRequest.AccompanyingPerson();
            manager.setAccompanyingType(AccompanyingTypeEnum.MANAGER.getCode());
            manager.setAccompanyingUserId("MANAGER_USER_ID");
            accompanyingList.add(manager);
            
            // 添加其他陪访人
            AddCommunicateRecordRequest.AccompanyingPerson other = new AddCommunicateRecordRequest.AccompanyingPerson();
            other.setAccompanyingType(AccompanyingTypeEnum.OTHER.getCode());
            other.setAccompanyingUserId("OTHER_USER_ID");
            accompanyingList.add(other);
            
            visitMinutesReq.setAccompanyingList(accompanyingList);
        }
        
        request.setVisitMinutesReq(visitMinutesReq);
        
        return request;
    }
}