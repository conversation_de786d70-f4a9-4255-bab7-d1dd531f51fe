/**
 * Copyright (c) 2024, <PERSON><PERSON><PERSON><PERSON> HOWBUY INVESTMENT MANAGEMENT Co., Ltd.
 * All right reserved.
 * <p>
 * THIS IS UNPUBLISHED PROPRIETARY SOURCE CODE OF HOWBUY INVESTMENT
 * MANAGEMENT CO., LTD.  THE CONTENTS OF THIS FILE MAY NOT BE DISCLOSED
 * TO THIRD PARTIES, COPIED OR DUPLICATED IN ANY FORM, IN WHOLE OR IN PART,
 * WITHOUT THE PRIOR WRITTEN PERMISSION OF HOWBUY INVESTMENT MANAGEMENT
 * CO., LTD.
 */
package com.howbuy.crm.account.service.business.beisen.cmsyncbeisenservicebusiness;

import com.howbuy.crm.account.dao.po.constant.HbConstantPO;
import com.howbuy.crm.account.service.business.beisen.CmSyncBeisenServiceBusiness;
import com.howbuy.crm.account.service.repository.beisen.CmSyncBeisenRepository;
import org.junit.Assert;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.Mockito;
import org.mockito.MockitoAnnotations;
import org.powermock.api.mockito.PowerMockito;
import org.powermock.core.classloader.annotations.PowerMockIgnore;
import org.powermock.core.classloader.annotations.PrepareForTest;
import org.powermock.modules.testng.PowerMockTestCase;
import org.testng.annotations.Test;

import java.lang.reflect.Field;
import java.util.ArrayList;
import java.util.Collections;
import java.util.List;

import static org.powermock.api.mockito.PowerMockito.doReturn;
import static org.powermock.reflect.Whitebox.getField;
import static org.powermock.reflect.Whitebox.invokeMethod;

/**
 * @description: (花名册同步北森数据)
 * <AUTHOR>
 * @date 2024/12/27 09:59
 * @since JDK 1.8
 *
 * 场景1：获得北森同步花名册需要初始化的数据
 */

@PowerMockIgnore("javax.management.*")
@Test
@PrepareForTest({CmSyncBeisenServiceBusiness.class})
public class TestSyncCmConsultantExpToCmBeisen extends PowerMockTestCase {
    @InjectMocks
    private CmSyncBeisenServiceBusiness service;
    @Mock
    private CmSyncBeisenRepository cmSyncBeisenRepository;




    public void init() throws IllegalAccessException {
        MockitoAnnotations.initMocks(this);
        service = PowerMockito.spy(new CmSyncBeisenServiceBusiness());
        Field fieldMapper1 = getField(CmSyncBeisenServiceBusiness.class, "cmSyncBeisenRepository");
        fieldMapper1.set(service, cmSyncBeisenRepository);
    }

    @Test
    public void test_01() throws Exception {
        init();
        List<HbConstantPO> hrPositionsLevelList = new ArrayList<>();
        HbConstantPO po = new HbConstantPO();
        //增加职级代码
        po.setConstcode("2");
        po.setCodedesc("理财师1级");
        hrPositionsLevelList.add(po);
        List<String> errorUserIdList = new ArrayList<>();
        errorUserIdList.add("san.zhang");
        PowerMockito.doReturn(hrPositionsLevelList).when(cmSyncBeisenRepository, "listByTypeCode");
        doReturn(Collections.emptyMap()).when(service, "getMapByZjSal");
        doReturn(Collections.emptyMap()).when(service, "getBeisenOrgDOMap");
        doReturn(Collections.emptyMap()).when(service, "getCmBeisenOrgConfigMap");
        doReturn(Collections.emptyMap()).when(service, "getCmBeisenPosLevelConfigPOMap");
        doReturn(errorUserIdList).when(service, "syncBeisenDataByExp", Mockito.anyList(), Mockito.any(), Mockito.anyList());
        List<String> resultList = invokeMethod(service, "syncCmConsultantExpToCmBeisen", Mockito.anyList(), Mockito.anyList());
        Boolean resultBool = resultList.size() == 1;
        Assert.assertTrue(resultBool);
    }

}