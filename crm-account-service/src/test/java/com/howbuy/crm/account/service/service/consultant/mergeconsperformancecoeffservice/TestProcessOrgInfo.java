package com.howbuy.crm.account.service.service.consultant.mergeconsperformancecoeffservice;

import com.howbuy.crm.account.dao.bo.consperformancecoeff.ConsPerformanceCoeffBO;
import com.howbuy.crm.account.service.domain.OrgLayerInfoDTO;
import com.howbuy.crm.account.service.outerservice.crmcore.OrganazitonOuterService;
import com.howbuy.crm.account.service.repository.consultant.MergeConsPerformanceCoeffRepository;
import com.howbuy.crm.account.service.service.consultant.MergeConsPerformanceCoeffService;
import org.junit.Assert;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.Mockito;
import org.mockito.MockitoAnnotations;
import org.powermock.api.mockito.PowerMockito;
import org.powermock.core.classloader.annotations.PowerMockIgnore;
import org.powermock.core.classloader.annotations.PrepareForTest;
import org.powermock.modules.testng.PowerMockTestCase;
import org.testng.annotations.Test;

import java.lang.reflect.Field;

import static org.powermock.reflect.Whitebox.getField;
import static org.powermock.reflect.Whitebox.invokeMethod;

/**
 * @description: 处理组织信息方法单元测试
 * <AUTHOR>
 * @date 2025-07-18 17:05:00
 * @since JDK 1.8
 *
 * 场景1：当投顾code为空时，不做任何处理
 * 场景2：当查询不到outletcode时，不做任何处理
 * 场景3：当查询不到组织层级信息时，不做任何处理
 * 场景4：当查询到组织层级信息时，正确设置所有组织信息
 * 场景5：当查询不到区副信息时，只设置其他组织信息
 * 场景6：当查询到区副信息时，正确设置所有组织信息（包括区副）
 */
@PowerMockIgnore("javax.management.*")
@Test
@PrepareForTest({MergeConsPerformanceCoeffService.class})
public class TestProcessOrgInfo extends PowerMockTestCase {
    
    @InjectMocks
    private MergeConsPerformanceCoeffService service;
    
    @Mock
    private MergeConsPerformanceCoeffRepository mergeConsPerformanceCoeffRepository;
    
    @Mock
    private OrganazitonOuterService organazitonOuterService;

    public void initData() throws IllegalAccessException {
        MockitoAnnotations.initMocks(this);
        service = PowerMockito.spy(new MergeConsPerformanceCoeffService());
        Field fieldRepository = getField(MergeConsPerformanceCoeffService.class, "mergeConsPerformanceCoeffRepository");
        fieldRepository.set(service, mergeConsPerformanceCoeffRepository);
        Field fieldOrgService = getField(MergeConsPerformanceCoeffService.class, "organazitonOuterService");
        fieldOrgService.set(service, organazitonOuterService);
    }

    /**
     * 场景1：当投顾code为空时，不做任何处理
     */
    @Test
    public void test_01() throws Exception {
        initData();
        ConsPerformanceCoeffBO bo = new ConsPerformanceCoeffBO();
        bo.setConsCustNo("TEST001");
        
        invokeMethod(service, "processOrgInfo", bo);
        
        Assert.assertNull(bo.getOutletCode());
        Assert.assertNull(bo.getCenterCode());
        Assert.assertNull(bo.getRegionCode());
        Assert.assertNull(bo.getRegionalSubtotal());
        PowerMockito.verifyZeroInteractions(mergeConsPerformanceCoeffRepository);
        PowerMockito.verifyZeroInteractions(organazitonOuterService);
    }

    /**
     * 场景2：当查询不到outletcode时，不做任何处理
     */
    @Test
    public void test_02() throws Exception {
        initData();
        ConsPerformanceCoeffBO bo = new ConsPerformanceCoeffBO();
        bo.setConsCustNo("TEST001");
        bo.setConsCode("TEST_CONS_CODE");
        
        PowerMockito.doReturn(null).when(mergeConsPerformanceCoeffRepository)
                .selectOutletCodeByConsCode(bo.getConsCode());
        
        invokeMethod(service, "processOrgInfo", bo);
        
        Assert.assertNull(bo.getOutletCode());
        Assert.assertNull(bo.getCenterCode());
        Assert.assertNull(bo.getRegionCode());
        Assert.assertNull(bo.getRegionalSubtotal());
        Mockito.verify(mergeConsPerformanceCoeffRepository).selectOutletCodeByConsCode(bo.getConsCode());
        PowerMockito.verifyZeroInteractions(organazitonOuterService);
    }

    /**
     * 场景3：当查询不到组织层级信息时，不做任何处理
     */
    @Test
    public void test_03() throws Exception {
        initData();
        ConsPerformanceCoeffBO bo = new ConsPerformanceCoeffBO();
        bo.setConsCustNo("TEST001");
        bo.setConsCode("TEST_CONS_CODE");
        String outletCode = "TEST_OUTLET_CODE";
        
        PowerMockito.doReturn(outletCode).when(mergeConsPerformanceCoeffRepository)
                .selectOutletCodeByConsCode(bo.getConsCode());
        PowerMockito.doReturn(null).when(organazitonOuterService)
                .getOrgLayerInfoContaionsDeleteByOrgCode(outletCode);
        
        invokeMethod(service, "processOrgInfo", bo);
        
        Assert.assertNull(bo.getOutletCode());
        Assert.assertNull(bo.getCenterCode());
        Assert.assertNull(bo.getRegionCode());
        Assert.assertNull(bo.getRegionalSubtotal());
        Mockito.verify(mergeConsPerformanceCoeffRepository).selectOutletCodeByConsCode(bo.getConsCode());
        Mockito.verify(organazitonOuterService).getOrgLayerInfoContaionsDeleteByOrgCode(outletCode);
    }

    /**
     * 场景4：当查询到组织层级信息时，正确设置所有组织信息
     */
    @Test
    public void test_04() throws Exception {
        initData();
        ConsPerformanceCoeffBO bo = new ConsPerformanceCoeffBO();
        bo.setConsCustNo("TEST001");
        bo.setConsCode("TEST_CONS_CODE");
        String outletCode = "TEST_OUTLET_CODE";
        
        PowerMockito.doReturn(outletCode).when(mergeConsPerformanceCoeffRepository)
                .selectOutletCodeByConsCode(bo.getConsCode());
        
        OrgLayerInfoDTO orgLayerInfo = new OrgLayerInfoDTO();
        orgLayerInfo.setPartOrgCode("ORG_CODE");
        orgLayerInfo.setCenterOrgCode("CENTER_CODE");
        orgLayerInfo.setDistrictCode("DISTRICT_CODE");
        
        PowerMockito.doReturn(orgLayerInfo).when(organazitonOuterService)
                .getOrgLayerInfoContaionsDeleteByOrgCode(outletCode);
        
        invokeMethod(service, "processOrgInfo", bo);
        
        Assert.assertEquals("ORG_CODE", bo.getOutletCode());
        Assert.assertEquals("CENTER_CODE", bo.getCenterCode());
        Assert.assertEquals("DISTRICT_CODE", bo.getRegionCode());
        Mockito.verify(mergeConsPerformanceCoeffRepository).selectOutletCodeByConsCode(bo.getConsCode());
        Mockito.verify(organazitonOuterService).getOrgLayerInfoContaionsDeleteByOrgCode(outletCode);
    }

    /**
     * 场景5：当查询不到区副信息时，只设置其他组织信息
     */
    @Test
    public void test_05() throws Exception {
        initData();
        ConsPerformanceCoeffBO bo = new ConsPerformanceCoeffBO();
        bo.setConsCustNo("TEST001");
        bo.setConsCode("TEST_CONS_CODE");
        String outletCode = "TEST_OUTLET_CODE";
        
        PowerMockito.doReturn(outletCode).when(mergeConsPerformanceCoeffRepository)
                .selectOutletCodeByConsCode(bo.getConsCode());
        
        OrgLayerInfoDTO orgLayerInfo = new OrgLayerInfoDTO();
        orgLayerInfo.setPartOrgCode("ORG_CODE");
        orgLayerInfo.setCenterOrgCode("CENTER_CODE");
        orgLayerInfo.setDistrictCode("DISTRICT_CODE");
        
        PowerMockito.doReturn(orgLayerInfo).when(organazitonOuterService)
                .getOrgLayerInfoContaionsDeleteByOrgCode(outletCode);
        PowerMockito.doReturn(null).when(mergeConsPerformanceCoeffRepository)
                .getRegionalSubtotalByOrgCode(outletCode);
        
        invokeMethod(service, "processOrgInfo", bo);
        
        Assert.assertEquals("ORG_CODE", bo.getOutletCode());
        Assert.assertEquals("CENTER_CODE", bo.getCenterCode());
        Assert.assertEquals("DISTRICT_CODE", bo.getRegionCode());
        Assert.assertNull(bo.getRegionalSubtotal());
        Mockito.verify(mergeConsPerformanceCoeffRepository).selectOutletCodeByConsCode(bo.getConsCode());
        Mockito.verify(organazitonOuterService).getOrgLayerInfoContaionsDeleteByOrgCode(outletCode);
        Mockito.verify(mergeConsPerformanceCoeffRepository).getRegionalSubtotalByOrgCode(outletCode);
    }

    /**
     * 场景6：当查询到区副信息时，正确设置所有组织信息（包括区副）
     */
    @Test
    public void test_06() throws Exception {
        initData();
        ConsPerformanceCoeffBO bo = new ConsPerformanceCoeffBO();
        bo.setConsCustNo("TEST001");
        bo.setConsCode("TEST_CONS_CODE");
        String outletCode = "TEST_OUTLET_CODE";
        
        PowerMockito.doReturn(outletCode).when(mergeConsPerformanceCoeffRepository)
                .selectOutletCodeByConsCode(bo.getConsCode());
        
        OrgLayerInfoDTO orgLayerInfo = new OrgLayerInfoDTO();
        orgLayerInfo.setPartOrgCode("ORG_CODE");
        orgLayerInfo.setCenterOrgCode("CENTER_CODE");
        orgLayerInfo.setDistrictCode("DISTRICT_CODE");
        
        PowerMockito.doReturn(orgLayerInfo).when(organazitonOuterService)
                .getOrgLayerInfoContaionsDeleteByOrgCode(outletCode);
        PowerMockito.doReturn("REGIONAL_SUBTOTAL").when(mergeConsPerformanceCoeffRepository)
                .getRegionalSubtotalByOrgCode(outletCode);
        
        invokeMethod(service, "processOrgInfo", bo);
        
        Assert.assertEquals("ORG_CODE", bo.getOutletCode());
        Assert.assertEquals("CENTER_CODE", bo.getCenterCode());
        Assert.assertEquals("DISTRICT_CODE", bo.getRegionCode());
        Assert.assertEquals("REGIONAL_SUBTOTAL", bo.getRegionalSubtotal());
        Mockito.verify(mergeConsPerformanceCoeffRepository).selectOutletCodeByConsCode(bo.getConsCode());
        Mockito.verify(organazitonOuterService).getOrgLayerInfoContaionsDeleteByOrgCode(outletCode);
        Mockito.verify(mergeConsPerformanceCoeffRepository).getRegionalSubtotalByOrgCode(outletCode);
    }
} 