/**
 * Copyright (c) 2024, <PERSON><PERSON><PERSON><PERSON> HOWBUY INVESTMENT MANAGEMENT Co., Ltd.
 * All right reserved.
 * <p>
 * THIS IS UNPUBLISHED PROPRIETARY SOURCE CODE OF HOWBUY INVESTMENT
 * MANAGEMENT CO., LTD.  THE CONTENTS OF THIS FILE MAY NOT BE DISCLOSED
 * TO THIRD PARTIES, COPIED OR DUPLICATED IN ANY FORM, IN WHOLE OR IN PART,
 * WITHOUT THE PRIOR WRITTEN PERMISSION OF HOWBUY INVESTMENT MANAGEMENT
 * CO., LTD.
 */
package com.howbuy.crm.account.service.service.custmessage.hbonecustinfochangeanalyseservice;

import com.howbuy.crm.account.client.enums.custinfo.AbnormaSceneTypeEnum;
import com.howbuy.crm.account.client.enums.custinfo.FullCustSourceEnum;
import com.howbuy.crm.account.dao.bo.custinfo.CmConscustForAnalyseBO;
import com.howbuy.crm.account.service.repository.custinfo.AbnormalCustRepository;
import com.howbuy.crm.account.service.service.custmessage.HboneCustInfoChangeAnalyseService;
import com.howbuy.crm.account.service.vo.custinfo.AbnormalAnalyseResultVO;
import com.howbuy.crm.account.service.vo.custinfo.HboneMessageCustInfoVO;
import org.assertj.core.util.Lists;
import org.junit.Assert;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.Mockito;
import org.mockito.internal.verification.Times;
import org.powermock.api.mockito.PowerMockito;
import org.powermock.core.classloader.annotations.PowerMockIgnore;
import org.powermock.modules.testng.PowerMockTestCase;
import org.springframework.util.CollectionUtils;
import org.testng.annotations.Test;

import java.lang.reflect.Method;
import java.util.List;

import static org.mockito.ArgumentMatchers.any;
import static org.mockito.Mockito.verify;
import static org.mockito.Mockito.when;

/**
 * @description: (请在此添加描述)
 * <AUTHOR>
 * @date 2024/1/16 13:11
 * @since JDK 1.8
 */
//@PrepareForTest({HboneCustInfoChangeAnalyseService.class})
@PowerMockIgnore("javax.management.*")
@Test
public class AnalyzeTest   extends PowerMockTestCase {


    @InjectMocks
    private HboneCustInfoChangeAnalyseService analyseService;
    @Mock
    private AbnormalCustRepository abnormalCustRepository;


    private static final String HBONE_NO = "123456789";


    private static final String TEST_CUST_NO="TEST_CUST_NO";

    /**
     * 重复客户
     */
    private static final String TEST_REPEAT_CUST_NO="TEST_REPEAT_CUST_NO";



    private HboneMessageCustInfoVO getAnalyseVo(FullCustSourceEnum sourceEnum){
        HboneMessageCustInfoVO analyseVo=new HboneMessageCustInfoVO();
        analyseVo.setHboneNo(HBONE_NO);
        analyseVo.setAbnormalSource(sourceEnum.getCode());
        return analyseVo;
    }

    /**
     * 构建 重复列表
     * @return
     */
    private  List<CmConscustForAnalyseBO>  getRepeateList(){
        List<CmConscustForAnalyseBO> repeatList=Lists.newArrayList();
        CmConscustForAnalyseBO repeatCust=new CmConscustForAnalyseBO();
        repeatCust.setConscustno(TEST_REPEAT_CUST_NO);
        repeatList.add(repeatCust);
        return repeatList;
    }

    /**
     * mock 私有方法 analyseMobile
     * @param analyseVo
     * @param excludeCustNo
     */
    private List<CmConscustForAnalyseBO> mockAnalyseMobile(HboneMessageCustInfoVO  analyseVo,String excludeCustNo){
        Method privateMethod = PowerMockito.method(HboneCustInfoChangeAnalyseService.class,
                "analyseMobile");
        PowerMockito.stub(privateMethod);

        List<CmConscustForAnalyseBO> repeatList=Lists.newArrayList();
        CmConscustForAnalyseBO repeatCust=new CmConscustForAnalyseBO();
        repeatCust.setConscustno(TEST_REPEAT_CUST_NO);
        repeatList.add(repeatCust);
        return repeatList;
    }

    /**
     * mock 私有方法 analyseIdNo
     * @param analyseVo
     * @param excludeCustNo
     * @return
     */
    private List<CmConscustForAnalyseBO> mockAnalyseIdNo(HboneMessageCustInfoVO  analyseVo,String excludeCustNo){
        Method privateMethod = PowerMockito.method(HboneCustInfoChangeAnalyseService.class,
                "analyseIdNo");
        PowerMockito.stub(privateMethod);

        List<CmConscustForAnalyseBO> repeatList=Lists.newArrayList();
        CmConscustForAnalyseBO repeatCust=new CmConscustForAnalyseBO();
        repeatCust.setConscustno(TEST_REPEAT_CUST_NO);
        repeatList.add(repeatCust);
        return repeatList;
    }

    /**
     * mock  多个客户 对应一个 hboneNo
     */
    @Test
    public void testMultipleCust() {

        HboneMessageCustInfoVO  analyseVo=getAnalyseVo(FullCustSourceEnum.HBONE_CUST_MOBILE_CHANGE);


        List<CmConscustForAnalyseBO> hbCustList= Lists.newArrayList(
                new CmConscustForAnalyseBO(),new CmConscustForAnalyseBO()
        );

        when(abnormalCustRepository.queryCustBOByHboneNo(HBONE_NO)).thenReturn(hbCustList);

        AbnormalAnalyseResultVO<HboneMessageCustInfoVO> resultVo=analyseService.analyze(analyseVo);

        // Assert
        verify(abnormalCustRepository).queryCustBOByHboneNo(HBONE_NO);
//        verify(cmConferenceLogMapper).insert(any(CmConferenceLog.class));
//        verifyNoMoreInteractions(conscustMapper, cmConferenceLogMapper);

        Assert.assertEquals(resultVo.isAnalysePass(), false);

        Assert.assertEquals(resultVo.getSceneTypeEnum(), AbnormaSceneTypeEnum.HBONE_NO_MATCH_MULTIPLE_CUST_NO);

        Assert.assertEquals(resultVo.getRelatedCustList().size(), hbCustList.size());
    }


    /**
     * mock  hone 没有客户
     */
    @Test
    public void testNoneCust() {

        HboneMessageCustInfoVO  analyseVo=getAnalyseVo(FullCustSourceEnum.HBONE_CUST_MOBILE_CHANGE);
        List<CmConscustForAnalyseBO> hbCustList= Lists.newArrayList();
        when(abnormalCustRepository.queryCustBOByHboneNo(HBONE_NO)).thenReturn(hbCustList);
        AbnormalAnalyseResultVO<HboneMessageCustInfoVO> resultVo=analyseService.analyze(analyseVo);

        // Assert
        verify(abnormalCustRepository).queryCustBOByHboneNo(HBONE_NO);
//        verify(cmConferenceLogMapper).insert(any(CmConferenceLog.class));
//        verifyNoMoreInteractions(conscustMapper, cmConferenceLogMapper);

        //分析 通过
        Assert.assertEquals(resultVo.isAnalysePass(), true);
        //是否处理  验证
        Assert.assertEquals(resultVo.isNeedProcess(), false);
        Assert.assertEquals(CollectionUtils.isEmpty(resultVo.getRelatedCustList()), true);
    }


    /**
     * mock  手机 重复客户
     */
    @Test
    public void testRepeatMobileCust() {

        HboneMessageCustInfoVO  analyseVo=getAnalyseVo(FullCustSourceEnum.HBONE_CUST_MOBILE_CHANGE);

        CmConscustForAnalyseBO processedCustBo=new CmConscustForAnalyseBO();
        processedCustBo.setConscustno(TEST_CUST_NO);

        //返回只有1条 客户数据
        List<CmConscustForAnalyseBO> hbCustList= Lists.newArrayList(processedCustBo);
        when(abnormalCustRepository.queryCustBOByHboneNo(HBONE_NO)).thenReturn(hbCustList);

        //根据手机号码查询  异常 列表 大于1
        List<CmConscustForAnalyseBO>  mobileRepeatLits=getRepeateList();
        int repeatSize=mobileRepeatLits.size();
        when(abnormalCustRepository.searchMobileExistList(any(),any(),any(),any())).thenReturn(mobileRepeatLits);

        AbnormalAnalyseResultVO<HboneMessageCustInfoVO> resultVo=analyseService.analyze(analyseVo);
        // Assert
        Mockito.verify(abnormalCustRepository,new Times(1)).queryCustBOByHboneNo(HBONE_NO);
        //不在查询  id
        Mockito.verify(abnormalCustRepository,new Times(0)).searchIdExistList(Mockito.any(), Mockito.any(),Mockito.any(),Mockito.any(),Mockito.any());
        //分析 通过
        Assert.assertEquals(resultVo.isAnalysePass(), false);

        Assert.assertEquals(resultVo.getSceneTypeEnum(), AbnormaSceneTypeEnum.CRM_REPEAT_CUST_WARNING);

        Assert.assertEquals(resultVo.getRelatedCustList().size(), repeatSize+1);
    }


    /**
     * mock id  重复客户
     */
//    @Test
//    public void testRepeatIdCust() {
//
//        HboneMessageCustInfoVO  analyseVo=getAnalyseVo(FullCustSourceEnum.HBONE_CUST_REAL_NAME_CHANGE);
//
//        CmConscustForAnalyseBO processedCustBo=new CmConscustForAnalyseBO();
//        processedCustBo.setConscustno(TEST_CUST_NO);
//
//        //返回只有1条 客户数据
//        List<CmConscustForAnalyseBO> hbCustList= Lists.newArrayList(processedCustBo);
//        when(abnormalCustRepository.queryCustBOByHboneNo(HBONE_NO)).thenReturn(hbCustList);
//
//        //根据手机号码查询  异常 列表 大于1
//        List<CmConscustForAnalyseBO>  mobileRepeatLits=getRepeateList();
//        int repeatSize=mobileRepeatLits.size();
//        when(abnormalCustRepository.searchIdExistList(any(),any(),any(),any(),any())).thenReturn(mobileRepeatLits);
//
//        AbnormalAnalyseResultVO<HboneMessageCustInfoVO> resultVo=analyseService.analyze(analyseVo);
//        // Assert
//        Mockito.verify(abnormalCustRepository,new Times(1)).queryCustBOByHboneNo(HBONE_NO);
//
//        Mockito.verify(abnormalCustRepository,new Times(0)).searchMobileExistList(Mockito.any(), Mockito.any(),Mockito.any(),Mockito.any());
//
//        //不在查询  id
//        Mockito.verify(abnormalCustRepository,new Times(1)).searchIdExistList(Mockito.any(), Mockito.any(),Mockito.any(),Mockito.any(),Mockito.any());
//        //分析 通过
//        Assert.assertEquals(resultVo.isAnalysePass(), false);
//
//        Assert.assertEquals(resultVo.getSceneTypeEnum(), AbnormaSceneTypeEnum.CRM_REPEAT_CUST_WARNING);
//
//        Assert.assertEquals(resultVo.getRelatedCustList().size(), repeatSize+1);
//    }
//

    /**
     * mock 通过
     */
    @Test
    public void testNormalCust() {

        HboneMessageCustInfoVO  analyseVo=getAnalyseVo(FullCustSourceEnum.HBONE_CUST_MOBILE_CHANGE);

        CmConscustForAnalyseBO processedCustBo=new CmConscustForAnalyseBO();
        processedCustBo.setConscustno(TEST_CUST_NO);

        //返回只有1条 客户数据
        List<CmConscustForAnalyseBO> hbCustList= Lists.newArrayList(processedCustBo);
        when(abnormalCustRepository.queryCustBOByHboneNo(HBONE_NO)).thenReturn(hbCustList);

        //根据手机号码查询  异常 列表 ==0
        List<CmConscustForAnalyseBO>  emptyList=Lists.newArrayList();
        when(abnormalCustRepository.searchMobileExistList(any(),any(),any(),any())).thenReturn(emptyList);
        when(abnormalCustRepository.searchIdExistList(any(),any(),any(),any(),any())).thenReturn(emptyList);

        AbnormalAnalyseResultVO<HboneMessageCustInfoVO> resultVo=analyseService.analyze(analyseVo);
        // Assert
        Mockito.verify(abnormalCustRepository,new Times(1)).searchMobileExistList(any(),any(),any(),any());
        //不在查询  id
        Mockito.verify(abnormalCustRepository,new Times(0)).searchIdExistList(Mockito.any(), Mockito.any(),Mockito.any(),Mockito.any(),Mockito.any());
        //分析 通过
        Assert.assertEquals(resultVo.isAnalysePass(), true);

        Assert.assertEquals(resultVo.getSceneTypeEnum(), null);

        Assert.assertEquals(CollectionUtils.isEmpty(resultVo.getRelatedCustList()), true);

        Assert.assertEquals(resultVo.getProcessedCustInfo().getConscustno(), TEST_CUST_NO);

    }

//

}