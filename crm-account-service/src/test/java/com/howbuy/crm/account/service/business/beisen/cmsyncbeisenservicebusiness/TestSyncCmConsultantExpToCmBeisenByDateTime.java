/**
 * Copyright (c) 2024, <PERSON><PERSON><PERSON><PERSON> HOWBUY INVESTMENT MANAGEMENT Co., Ltd.
 * All right reserved.
 * <p>
 * THIS IS UNPUBLISHED PROPRIETARY SOURCE CODE OF HOWBUY INVESTMENT
 * MANAGEMENT CO., LTD.  THE CONTENTS OF THIS FILE MAY NOT BE DISCLOSED
 * TO THIRD PARTIES, COPIED OR DUPLICATED IN ANY FORM, IN WHOLE OR IN PART,
 * WITHOUT THE PRIOR WRITTEN PERMISSION OF HOWBUY INVESTMENT MANAGEMENT
 * CO., LTD.
 */
package com.howbuy.crm.account.service.business.beisen.cmsyncbeisenservicebusiness;

import com.howbuy.crm.account.service.business.beisen.CmSyncBeisenServiceBusiness;
import com.howbuy.crm.account.service.repository.beisen.CmSyncBeisenRepository;
import org.junit.Assert;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.Mockito;
import org.mockito.MockitoAnnotations;
import org.powermock.api.mockito.PowerMockito;
import org.powermock.core.classloader.annotations.PowerMockIgnore;
import org.powermock.core.classloader.annotations.PrepareForTest;
import org.powermock.modules.testng.PowerMockTestCase;
import org.springframework.util.CollectionUtils;
import org.testng.annotations.Test;

import java.lang.reflect.Field;
import java.util.*;

import static org.powermock.api.mockito.PowerMockito.doReturn;
import static org.powermock.api.mockito.PowerMockito.mockStatic;
import static org.powermock.reflect.Whitebox.getField;
import static org.powermock.reflect.Whitebox.invokeMethod;

/**
 * @description: (北森同步花名册获取花名册和北森数据)
 * <AUTHOR>
 * @date 2024/11/25 11:16
 * @since JDK 1.8
 *
 * 场景1：获得全量花名册数据，数据为空返回空集合
 * 场景2：获得全量花名册数据，数据不为空，再根据开始日期和结束日期获取北森用户数据，北森用户数据为空返回空集合
 * 场景3：获得全量花名册数据，数据不为空，再根据开始日期和结束日期获取北森用户数据，数据不为空调用北森同步逻辑，返回结果用户
 */

@PowerMockIgnore("javax.management.*")
@Test
@PrepareForTest({CmSyncBeisenServiceBusiness.class, CollectionUtils.class})
public class TestSyncCmConsultantExpToCmBeisenByDateTime extends PowerMockTestCase {
    @InjectMocks
    private CmSyncBeisenServiceBusiness service;
    @Mock
    private CmSyncBeisenRepository cmSyncBeisenRepository;




    public void init() throws IllegalAccessException {
        MockitoAnnotations.initMocks(this);
        service = PowerMockito.spy(new CmSyncBeisenServiceBusiness());
        Field fieldMapper1 = getField(CmSyncBeisenServiceBusiness.class, "cmSyncBeisenRepository");
        fieldMapper1.set(service, cmSyncBeisenRepository);
    }

    @Test
    public void test_01() throws Exception {
        init();
        PowerMockito.doReturn(new ArrayList<>()).when(cmSyncBeisenRepository, "listCmConsultantExpByUserNo", new ArrayList<>());
        mockStatic(CollectionUtils.class);
        PowerMockito.doReturn(Boolean.TRUE).when(CollectionUtils.class, "isEmpty", Mockito.anyList());
        List<String> resultList = invokeMethod(service, "syncCmConsultantExpToCmBeisenByDateTime", Mockito.anyString(), Mockito.anyString());
        Boolean resultBool = resultList.size() == 0;
        Assert.assertTrue(resultBool);
    }

    @Test
    public void test_02() throws Exception {
        init();
        PowerMockito.doReturn(new ArrayList<>()).when(cmSyncBeisenRepository, "listCmConsultantExpByUserNo", new ArrayList<>());
        doReturn(Collections.emptyList()).when(service, "getBeisenUserInfo", Mockito.anyString(), Mockito.anyString());
        mockStatic(CollectionUtils.class);
        PowerMockito.doReturn(Boolean.FALSE, Boolean.TRUE).when(CollectionUtils.class, "isEmpty", Mockito.anyList());
        List<String> resultList = invokeMethod(service, "syncCmConsultantExpToCmBeisenByDateTime", Mockito.anyString(), Mockito.anyString());
        Boolean resultBool = resultList.size() == 0;
        Assert.assertTrue(resultBool);
    }

    @Test
    public void test_03() throws Exception {
        init();
        PowerMockito.doReturn(new ArrayList<>()).when(cmSyncBeisenRepository, "listCmConsultantExpByUserNo", new ArrayList<>());
        doReturn(Collections.emptyList()).when(service, "getBeisenUserInfo", Mockito.anyString(), Mockito.anyString());
        mockStatic(CollectionUtils.class);
        PowerMockito.doReturn(Boolean.FALSE, Boolean.FALSE).when(CollectionUtils.class, "isEmpty", Mockito.anyList());
        List<String> list = new ArrayList<>();
        list.add("test1");
        doReturn(list).when(service, "syncCmConsultantExpToCmBeisen", Mockito.anyList(), Mockito.anyList());
        List<String> resultList = invokeMethod(service, "syncCmConsultantExpToCmBeisenByDateTime", Mockito.anyString(), Mockito.anyString());
        Boolean resultBool = list.get(0).equals(resultList.get(0));
        Assert.assertTrue(resultBool);
    }

}