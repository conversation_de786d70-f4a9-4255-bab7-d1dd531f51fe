## 测试的类
> com.howbuy.crm.account.service.service.commvisit.CsCommVisitService
## 测试的方法 
> addCommunicateRecord(AddCommunicateRecordRequest request)

## 分支伪代码
``` java
添加沟通记录过程 {

    // 1. 保存沟通记录基本信息
    获取沟通记录ID
    构建沟通记录PO对象
    保存沟通记录
    
    // 2. 判断是否需要保存拜访纪要
    if (拜访纪要信息为空) {
        返回 成功，只包含沟通记录ID
    }
    
    // 3. 保存拜访纪要信息
    获取拜访纪要ID
    获取当前投顾的主管信息
    构建拜访纪要PO对象
    保存拜访纪要
    
    // 4. 判断是否有陪访人信息
    if (陪访人列表为空) {
        返回 成功，包含沟通记录ID和拜访纪要ID
    }
    
    // 5. 保存陪访人信息
    for (每个陪访人) {
        获取陪访人ID
        获取陪访人层级
        构建陪访人PO对象
        保存陪访人信息
    }
    
    // 6. 判断是否需要发送通知
    if (拜访目的为IPS拜访) {
        发送陪访通知消息
    }
    
    返回 成功，包含沟通记录ID和拜访纪要ID
}
```

## 测试案例
1、添加沟通记录 - 无拜访纪要：只保存沟通记录基本信息，不保存拜访纪要和陪访人信息
### testAddCommunicateRecord_WithoutVisitMinutes

2、添加沟通记录 - 有拜访纪要但无陪访人：保存沟通记录和拜访纪要信息，不保存陪访人信息
### testAddCommunicateRecord_WithVisitMinutesNoAccompanying

3、添加沟通记录 - 有拜访纪要和陪访人：保存沟通记录、拜访纪要和陪访人信息
### testAddCommunicateRecord_WithVisitMinutesAndAccompanying

4、添加沟通记录 - IPS陪访场景：保存沟通记录、拜访纪要和陪访人信息，并发送通知
### testAddCommunicateRecord_WithIPSVisitPurpose