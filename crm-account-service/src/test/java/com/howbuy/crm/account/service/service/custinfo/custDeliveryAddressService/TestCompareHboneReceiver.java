/**
 * Copyright (c) 2024, <PERSON><PERSON><PERSON><PERSON> HOWBUY INVESTMENT MANAGEMENT Co., Ltd.
 * All right reserved.
 * <p>
 * THIS IS UNPUBLISHED PROPRIETARY SOURCE CODE OF HOWBUY INVESTMENT
 * MANAGEMENT CO., LTD.  THE CONTENTS OF THIS FILE MAY NOT BE DISCLOSED
 * TO THIRD PARTIES, COPIED OR DUPLICATED IN ANY FORM, IN WHOLE OR IN PART,
 * WITHOUT THE PRIOR WRITTEN PERMISSION OF HOWBUY INVESTMENT MANAGEMENT
 * CO., LTD.
 */
package com.howbuy.crm.account.service.service.custinfo.custDeliveryAddressService;

import com.howbuy.acccenter.facade.query.querycustdeliveryaddr.CustDeliveryAddrInfoBean;
import com.howbuy.acccenter.facade.query.querycustdeliveryaddr.QueryCustDeliveryAddrFacade;
import com.howbuy.acccenter.facade.query.querycustdeliveryaddr.QueryCustDeliveryAddrResponse;
import com.howbuy.acccenter.facade.query.sensitive.custdeliveryaddr.CustDeliveryAddrSensitiveInfo;
import com.howbuy.acccenter.facade.query.sensitive.custdeliveryaddr.QueryCustDeliveryAddrSensitiveInfoFacade;
import com.howbuy.acccenter.facade.query.sensitive.custdeliveryaddr.QueryCustDeliveryAddrSensitiveInfoResponse;
import com.howbuy.crm.account.client.request.custinfo.ConsCustDeliveryAddressRequest;
import com.howbuy.crm.account.client.response.Response;
import com.howbuy.crm.account.client.response.custinfo.CmConsCustVO;
import com.howbuy.crm.account.client.response.dictionary.ProvCityCountyNameVO;
import com.howbuy.crm.account.service.service.custinfo.ConsCustInfoService;
import com.howbuy.crm.account.service.service.custinfo.CustDeliveryAddressService;
import com.howbuy.crm.account.service.service.dictionary.DictionaryService;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.Mockito;
import org.powermock.api.mockito.PowerMockito;
import org.powermock.core.classloader.annotations.PowerMockIgnore;
import org.powermock.core.classloader.annotations.PrepareForTest;
import org.powermock.modules.testng.PowerMockTestCase;
import org.testng.Assert;
import org.testng.annotations.Test;

import java.util.ArrayList;
import java.util.List;

/**
 * @description: 单元测试： 客户版收货地址 和 投顾版收货地址 比较
 * <AUTHOR>
 * @date 2024/4/11 11:02
 * @since JDK 1.8
 */


@Test
@PowerMockIgnore("javax.management.*")
@PrepareForTest({CustDeliveryAddressService.class})
public class TestCompareHboneReceiver extends PowerMockTestCase {

    @InjectMocks
    private CustDeliveryAddressService serviceMock;

    @Mock
    private ConsCustInfoService consCustInfoServiceMock;

    @Mock
    private QueryCustDeliveryAddrFacade queryCustDeliveryAddrFacadeMock;

    @Mock
    private DictionaryService dictionaryServiceMock;

    @Mock
    private QueryCustDeliveryAddrSensitiveInfoFacade queryCustDeliveryAddrSensitiveInfoFacadeMock;

    private static final String HBONE_NO = "123456789";

    private static final String TEST_CUST_NO="TEST_CUST_NO";

    /**
     * 收货地址 addrId
     */
    private static final String TEST_ADDR_ID="TEST_ADDR_ID";


    /**
     * 测试：投顾版收货地址信息不全
     */
    @Test
    public void test01() throws Exception {
        ConsCustDeliveryAddressRequest consCustDeliveryAddressRequest = new ConsCustDeliveryAddressRequest();
        consCustDeliveryAddressRequest.setOperator("");


        Response<String> resp = serviceMock.compareHboneReceiver(consCustDeliveryAddressRequest);

        Assert.assertFalse(resp.isSuccess());
        Assert.assertEquals(resp.getDescription(), "待比对的收货信息不完善！");
    }


    /**
     * 测试：查询的投顾客户信息为空
     */
    @Test
    public void test02() throws Exception {
        ConsCustDeliveryAddressRequest consCustDeliveryAddressRequest = getConsCustDeliveryAddressRequest();


        PowerMockito.when(consCustInfoServiceMock.queryCustInfoByCustNo(TEST_CUST_NO))
                .thenReturn(null);

        Response<String> resp = serviceMock.compareHboneReceiver(consCustDeliveryAddressRequest);

        Assert.assertFalse(resp.isSuccess());
        Assert.assertEquals(resp.getDescription(), "查询的投顾客户信息为空！");
    }

    /**
     * 测试：查询的投顾客户的一账通号为空
     */
    @Test
    public void test03() throws Exception {
        ConsCustDeliveryAddressRequest consCustDeliveryAddressRequest = getConsCustDeliveryAddressRequest();

        PowerMockito.when(consCustInfoServiceMock.queryCustInfoByCustNo(TEST_CUST_NO))
                .thenReturn(new CmConsCustVO());

        Response<String> resp = serviceMock.compareHboneReceiver(consCustDeliveryAddressRequest);

        Assert.assertTrue(resp.isSuccess());
    }

    /**
     * 测试：查询的投顾客户的一账通号不为空， 但客户版的收货地址为空
     *
     */
    @Test
    public void test04() throws Exception {
        ConsCustDeliveryAddressRequest consCustDeliveryAddressRequest = getConsCustDeliveryAddressRequest();

        CmConsCustVO cmConsCustVO = new CmConsCustVO();
        cmConsCustVO.setHboneNo(HBONE_NO);

        PowerMockito.when(consCustInfoServiceMock.queryCustInfoByCustNo(TEST_CUST_NO))
                .thenReturn(cmConsCustVO);


        PowerMockito.when(queryCustDeliveryAddrFacadeMock.execute(Mockito.any()))
                .thenReturn(null);

        Response<String> resp = serviceMock.compareHboneReceiver(consCustDeliveryAddressRequest);

        Assert.assertTrue(resp.isSuccess());
    }


    /**
     * 测试：查询的投顾客户的一账通号不为空， 但客户版的收货地址 返回状态码为失败
     */
    @Test
    public void test05() throws Exception {
        ConsCustDeliveryAddressRequest consCustDeliveryAddressRequest = getConsCustDeliveryAddressRequest();

        CmConsCustVO cmConsCustVO = new CmConsCustVO();
        cmConsCustVO.setHboneNo(HBONE_NO);

        PowerMockito.when(consCustInfoServiceMock.queryCustInfoByCustNo(TEST_CUST_NO))
                .thenReturn(cmConsCustVO);


        QueryCustDeliveryAddrResponse queryCustDeliveryAddrResponse = new QueryCustDeliveryAddrResponse();
        queryCustDeliveryAddrResponse.setReturnCode("9999");
        PowerMockito.when(queryCustDeliveryAddrFacadeMock.execute(Mockito.any()))
                .thenReturn(queryCustDeliveryAddrResponse);

        Response<String> resp = serviceMock.compareHboneReceiver(consCustDeliveryAddressRequest);

        Assert.assertTrue(resp.isSuccess());
    }

    /**
     * 测试：查询的投顾客户的一账通号不为空， 但客户版的收货地址list为空
     */
    @Test
    public void test06() throws Exception {
        ConsCustDeliveryAddressRequest consCustDeliveryAddressRequest = getConsCustDeliveryAddressRequest();

        CmConsCustVO cmConsCustVO = new CmConsCustVO();
        cmConsCustVO.setHboneNo(HBONE_NO);

        PowerMockito.when(consCustInfoServiceMock.queryCustInfoByCustNo(TEST_CUST_NO))
                .thenReturn(cmConsCustVO);


        QueryCustDeliveryAddrResponse queryCustDeliveryAddrResponse = new QueryCustDeliveryAddrResponse();
        queryCustDeliveryAddrResponse.setReturnCode("0000");
        queryCustDeliveryAddrResponse.setDeliveryAddrInfoList(null);
        PowerMockito.when(queryCustDeliveryAddrFacadeMock.execute(Mockito.any()))
                .thenReturn(queryCustDeliveryAddrResponse);

        Response<String> resp = serviceMock.compareHboneReceiver(consCustDeliveryAddressRequest);

        Assert.assertTrue(resp.isSuccess());
    }


    /**
     * 测试：查询的投顾客户的一账通号不为空， 但客户版的收货地址list有一个地址，且地址和投顾版的地址一致
     */
    @Test
    public void test07() throws Exception {
        ConsCustDeliveryAddressRequest consCustDeliveryAddressRequest = getConsCustDeliveryAddressRequest();

        CmConsCustVO cmConsCustVO = new CmConsCustVO();
        cmConsCustVO.setHboneNo(HBONE_NO);

        PowerMockito.when(consCustInfoServiceMock.queryCustInfoByCustNo(TEST_CUST_NO))
                .thenReturn(cmConsCustVO);


        QueryCustDeliveryAddrResponse queryCustDeliveryAddrResponse = new QueryCustDeliveryAddrResponse();
        queryCustDeliveryAddrResponse.setReturnCode("0000000");
        List<CustDeliveryAddrInfoBean> deliveryAddrInfoList = new ArrayList<>();
        CustDeliveryAddrInfoBean custDeliveryAddrInfoBean = new CustDeliveryAddrInfoBean();
        custDeliveryAddrInfoBean.setDeliveryName("张三");
        custDeliveryAddrInfoBean.setDeliveryMobileDigest("bf1e378221078ed8faae0989af79126d");
        custDeliveryAddrInfoBean.setDeliveryProvCode("12345678901");
        custDeliveryAddrInfoBean.setDeliveryCityCode("12345678901");
        custDeliveryAddrInfoBean.setDeliveryCountyCode("12345678901");
        custDeliveryAddrInfoBean.setDeliveryAddrDigest("fcdfda0a9fdcf27e4dff61b6df9936f9");

        custDeliveryAddrInfoBean.setDeliveryCountryCode("CN");
        custDeliveryAddrInfoBean.setDeliveryMobileAreaCode("+86");

        deliveryAddrInfoList.add(custDeliveryAddrInfoBean);
        queryCustDeliveryAddrResponse.setDeliveryAddrInfoList(deliveryAddrInfoList);

        PowerMockito.when(queryCustDeliveryAddrFacadeMock.execute(Mockito.any()))
                .thenReturn(queryCustDeliveryAddrResponse);

        Response<String> resp = serviceMock.compareHboneReceiver(consCustDeliveryAddressRequest);

        Assert.assertTrue(resp.isSuccess());
    }


    /**
     * 测试：查询的投顾客户的一账通号不为空， 但客户版的收货地址list有一个地址，且地址和投顾版的地址不一致
     */
    @Test
    public void test08() throws Exception {
        ConsCustDeliveryAddressRequest consCustDeliveryAddressRequest = getConsCustDeliveryAddressRequest();

        CmConsCustVO cmConsCustVO = new CmConsCustVO();
        cmConsCustVO.setHboneNo(HBONE_NO);

        PowerMockito.when(consCustInfoServiceMock.queryCustInfoByCustNo(TEST_CUST_NO))
                .thenReturn(cmConsCustVO);


        QueryCustDeliveryAddrResponse queryCustDeliveryAddrResponse = new QueryCustDeliveryAddrResponse();
        queryCustDeliveryAddrResponse.setReturnCode("0000000");
        List<CustDeliveryAddrInfoBean> deliveryAddrInfoList = new ArrayList<>();
        CustDeliveryAddrInfoBean custDeliveryAddrInfoBean = new CustDeliveryAddrInfoBean();
        custDeliveryAddrInfoBean.setAddrId(TEST_ADDR_ID);
        custDeliveryAddrInfoBean.setDeliveryName("张三");
        custDeliveryAddrInfoBean.setDeliveryMobileMask("12345678901");
        custDeliveryAddrInfoBean.setDeliveryMobileDigest("bf1e378221078ed8faae0989af79126d");
        custDeliveryAddrInfoBean.setDeliveryProvCode("12345678901");
        custDeliveryAddrInfoBean.setDeliveryCityCode("12345678901");
        custDeliveryAddrInfoBean.setDeliveryCountyCode("12345678901");
        custDeliveryAddrInfoBean.setDeliveryAddrDigest("1");

        deliveryAddrInfoList.add(custDeliveryAddrInfoBean);
        queryCustDeliveryAddrResponse.setDeliveryAddrInfoList(deliveryAddrInfoList);

        PowerMockito.when(queryCustDeliveryAddrFacadeMock.execute(Mockito.any()))
                .thenReturn(queryCustDeliveryAddrResponse);

        QueryCustDeliveryAddrSensitiveInfoResponse queryCustDeliveryAddrSensitiveInfoResponse = new QueryCustDeliveryAddrSensitiveInfoResponse();
        queryCustDeliveryAddrSensitiveInfoResponse.setReturnCode("0000000");
        List<CustDeliveryAddrSensitiveInfo> deliveryAddrSensitiveInfoList = new ArrayList<>();
        CustDeliveryAddrSensitiveInfo custDeliveryAddrSensitiveInfo = new CustDeliveryAddrSensitiveInfo();
        custDeliveryAddrSensitiveInfo.setDeliveryAddr("TEST_ADDRESS");
        custDeliveryAddrSensitiveInfo.setAddrId(TEST_ADDR_ID);
        deliveryAddrSensitiveInfoList.add(custDeliveryAddrSensitiveInfo);

        queryCustDeliveryAddrSensitiveInfoResponse.setDeliveryAddrSensitiveInfoList(deliveryAddrSensitiveInfoList);
        PowerMockito.when(queryCustDeliveryAddrSensitiveInfoFacadeMock.execute(Mockito.any()))
                .thenReturn(queryCustDeliveryAddrSensitiveInfoResponse);



        ProvCityCountyNameVO provCityCountyNameVO = new ProvCityCountyNameVO();
        provCityCountyNameVO.setProvName("北京市");
        provCityCountyNameVO.setCityName("北京市");
        provCityCountyNameVO.setCountyName("东城区");
        PowerMockito.when(dictionaryServiceMock.getNamesByCodes("12345678901", "12345678901", "12345678901"))
                .thenReturn(provCityCountyNameVO);


        Response<String> resp = serviceMock.compareHboneReceiver(consCustDeliveryAddressRequest);

        Assert.assertFalse(resp.isSuccess());
        Assert.assertEquals(resp.getDescription(), "张三 null-12345678901 null 北京市北京市东城区TEST_ADDRESS");

    }

    private static ConsCustDeliveryAddressRequest getConsCustDeliveryAddressRequest() {
        ConsCustDeliveryAddressRequest consCustDeliveryAddressRequest = new ConsCustDeliveryAddressRequest();
        consCustDeliveryAddressRequest.setCustNo(TEST_CUST_NO);
        consCustDeliveryAddressRequest.setDeliveryName("张三");
        consCustDeliveryAddressRequest.setDeliveryMobile("12345678901");
        consCustDeliveryAddressRequest.setDeliveryCountyCode("12345678901");
        consCustDeliveryAddressRequest.setDeliveryCityCode("12345678901");
        consCustDeliveryAddressRequest.setDeliveryProvCode("12345678901");
        consCustDeliveryAddressRequest.setDeliveryAddr("TEST_ADDRESS");
        consCustDeliveryAddressRequest.setDeliveryNationCode("CN");
        consCustDeliveryAddressRequest.setDeliveryMobileAreaCode("+86");
        return consCustDeliveryAddressRequest;
    }
}