package com.howbuy.crm.account.service.service.consultant.mergeconsperformancecoeffservice;

import com.howbuy.crm.account.dao.bo.consperformancecoeff.ConsPerformanceCoeffBO;
import com.howbuy.crm.account.service.repository.consultant.MergeConsPerformanceCoeffRepository;
import com.howbuy.crm.account.service.service.consultant.MergeConsPerformanceCoeffService;
import org.junit.Assert;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.Mockito;
import org.mockito.MockitoAnnotations;
import org.powermock.api.mockito.PowerMockito;
import org.powermock.core.classloader.annotations.PowerMockIgnore;
import org.powermock.core.classloader.annotations.PrepareForTest;
import org.powermock.modules.testng.PowerMockTestCase;
import org.testng.annotations.Test;

import java.lang.reflect.Field;
import java.util.Date;

import static org.powermock.reflect.Whitebox.getField;
import static org.powermock.reflect.Whitebox.invokeMethod;

/**
 * @description: 处理分配时间方法单元测试
 * <AUTHOR>
 * @date 2025-07-18 17:05:00
 * @since JDK 1.8
 *
 * 场景1：当assignTime已有值时，直接返回，不做任何处理
 * 场景2：当custNo为空时，不做任何处理
 * 场景3：当通过custNo查询不到bindDate时，不做任何处理
 * 场景4：当通过custNo查询到bindDate时，设置到BO对象中
 */
@PowerMockIgnore("javax.management.*")
@Test
@PrepareForTest({MergeConsPerformanceCoeffService.class})
public class TestProcessAssignTime extends PowerMockTestCase {
    
    @InjectMocks
    private MergeConsPerformanceCoeffService service;
    
    @Mock
    private MergeConsPerformanceCoeffRepository mergeConsPerformanceCoeffRepository;

    public void initData() throws IllegalAccessException {
        MockitoAnnotations.initMocks(this);
        service = PowerMockito.spy(new MergeConsPerformanceCoeffService());
        Field fieldRepository = getField(MergeConsPerformanceCoeffService.class, "mergeConsPerformanceCoeffRepository");
        fieldRepository.set(service, mergeConsPerformanceCoeffRepository);
    }

    /**
     * 场景1：当assignTime已有值时，直接返回，不做任何处理
     */
    @Test
    public void test_01() throws Exception {
        initData();
        ConsPerformanceCoeffBO bo = new ConsPerformanceCoeffBO();
        Date now = new Date();
        bo.setAssignTime(now);
        bo.setConsCustNo("TEST001");
        
        invokeMethod(service, "processAssignTime", bo);
        
        Assert.assertEquals(now, bo.getAssignTime());
        PowerMockito.verifyZeroInteractions(mergeConsPerformanceCoeffRepository);
    }

    /**
     * 场景2：当custNo为空时，不做任何处理
     */
    @Test
    public void test_02() throws Exception {
        initData();
        ConsPerformanceCoeffBO bo = new ConsPerformanceCoeffBO();
        
        invokeMethod(service, "processAssignTime", bo);
        
        Assert.assertNull(bo.getAssignTime());
        PowerMockito.verifyZeroInteractions(mergeConsPerformanceCoeffRepository);
    }

    /**
     * 场景3：当通过custNo查询不到bindDate时，不做任何处理
     */
    @Test
    public void test_03() throws Exception {
        initData();
        ConsPerformanceCoeffBO bo = new ConsPerformanceCoeffBO();
        bo.setConsCustNo("TEST001");
        
        PowerMockito.doReturn(null).when(mergeConsPerformanceCoeffRepository)
                .selectBindDateByCustNo(bo.getConsCustNo());
        
        invokeMethod(service, "processAssignTime", bo);
        
        Assert.assertNull(bo.getAssignTime());
        Mockito.verify(mergeConsPerformanceCoeffRepository).selectBindDateByCustNo(bo.getConsCustNo());
    }

    /**
     * 场景4：当通过custNo查询到bindDate时，设置到BO对象中
     */
    @Test
    public void test_04() throws Exception {
        initData();
        ConsPerformanceCoeffBO bo = new ConsPerformanceCoeffBO();
        bo.setConsCustNo("TEST001");
        Date bindDate = new Date();
        
        PowerMockito.doReturn(bindDate).when(mergeConsPerformanceCoeffRepository)
                .selectBindDateByCustNo(bo.getConsCustNo());
        
        invokeMethod(service, "processAssignTime", bo);
        
        Assert.assertEquals(bindDate, bo.getAssignTime());
        Mockito.verify(mergeConsPerformanceCoeffRepository).selectBindDateByCustNo(bo.getConsCustNo());
    }

} 