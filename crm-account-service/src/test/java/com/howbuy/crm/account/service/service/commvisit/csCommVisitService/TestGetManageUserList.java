package com.howbuy.crm.account.service.service.commvisit.csCommVisitService;

import com.howbuy.crm.account.client.request.commvisit.VisitInitDataRequest;
import com.howbuy.crm.account.client.response.commvisit.VisitInitDataResponse;
import com.howbuy.crm.account.dao.po.consultant.CmConsultantPO;
import com.howbuy.crm.account.dao.po.consultantexp.CmConsultantExpPO;
import com.howbuy.crm.account.service.commom.constant.Constants;
import com.howbuy.crm.account.service.domain.OrgLayerInfoDTO;
import com.howbuy.crm.account.service.outerservice.crmcore.OrganazitonOuterService;
import com.howbuy.crm.account.service.repository.consultant.CmConsultantRepository;
import com.howbuy.crm.account.service.repository.consultantexp.CmConsultantExpRepository;
import com.howbuy.crm.account.service.service.commvisit.CsCommVisitService;
import com.howbuy.crm.account.service.service.consultantexp.CmConsultantExpService;
import org.mockito.ArgumentMatchers;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.powermock.core.classloader.annotations.PowerMockIgnore;
import org.powermock.core.classloader.annotations.PrepareForTest;
import org.powermock.modules.testng.PowerMockTestCase;
import org.testng.Assert;
import org.testng.annotations.BeforeMethod;
import org.testng.annotations.Test;

import java.lang.reflect.Method;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.HashSet;
import java.util.List;

import static org.mockito.Mockito.when;

/**
 * CsCommVisitService.getManageUserList方法的单元测试
 * 该方法用于获取投顾的主管列表，包括分总、区域执行副总和区域总
 */
@PrepareForTest({CsCommVisitService.class})
@PowerMockIgnore("javax.management.*")
public class TestGetManageUserList extends PowerMockTestCase {

    @InjectMocks
    private CsCommVisitService csCommVisitService;

    @Mock
    private CmConsultantExpService cmConsultantExpService;

    @Mock
    private CmConsultantRepository crmConsultantRepository;

    @Mock
    private CmConsultantExpRepository cmConsultantExpRepository;
    
    @Mock
    private OrganazitonOuterService organizerOuterService;

    private static final String TEST_CONS_CODE = "TEST_CONS_CODE";
    private static final String TEST_OUTLET_CODE = "TEST_OUTLET_CODE";
    private static final String TEST_AREA_CODE = "TEST_AREA_CODE";
    private static final String TEST_DISTRICT_CODE = "TEST_DISTRICT_CODE";

    private Method getManageUserListMethod;

    @BeforeMethod
    public void setUp() throws Exception {
        // 获取私有方法的引用，以便在测试中调用
        getManageUserListMethod = CsCommVisitService.class.getDeclaredMethod(
                "getManageUserList", VisitInitDataRequest.class, CmConsultantExpPO.class);
        getManageUserListMethod.setAccessible(true);
    }

    /**
     * 测试场景：投顾信息为空
     * 预期结果：返回空列表
     */
    @Test
    public void testGetManageUserList_NullConsultantExp() throws Exception {
        // 准备测试数据
        VisitInitDataRequest request = new VisitInitDataRequest();
        request.setConsCode(TEST_CONS_CODE);

        // 执行测试
        @SuppressWarnings("unchecked")
        List<VisitInitDataResponse.UserInfo> result = (List<VisitInitDataResponse.UserInfo>) getManageUserListMethod.invoke(
                csCommVisitService, request, null);

        // 验证结果
        Assert.assertNotNull(result);
        Assert.assertTrue(result.isEmpty());
    }

    /**
     * 测试场景：正常获取主管列表
     * 预期结果：返回合并后的主管列表
     */
    @Test
    public void testGetManageUserList_Normal() throws Exception {
        // 准备测试数据
        VisitInitDataRequest request = new VisitInitDataRequest();
        request.setConsCode(TEST_CONS_CODE);

        CmConsultantExpPO cmConsultantExp = new CmConsultantExpPO();
        cmConsultantExp.setOutletcode(TEST_OUTLET_CODE);
        cmConsultantExp.setConscode(TEST_CONS_CODE);

        // 模拟分总层级常量
        List<String> divisionManagerLevelCodes = Arrays.asList("DM1", "DM2");
        when(cmConsultantExpService.listConstantCodeByUserLevel(Constants.DIVISION_MANAGER_LEVEL))
                .thenReturn(divisionManagerLevelCodes);

        // 模拟分总列表
        List<String> divisionManagers = Arrays.asList("DM001", "DM002");
        when(cmConsultantExpService.listLeadersByOrgCodeAndLevel(TEST_OUTLET_CODE, "DM1|DM2"))
                .thenReturn(divisionManagers);

        // 模拟区域执行副总列表
        List<String> vicePresidents = Arrays.asList("VP001", "VP002");
        when(cmConsultantExpService.listFLeadersByOrgCode(TEST_OUTLET_CODE))
                .thenReturn(vicePresidents);

        // 模拟OrgLayerInfoDTO对象
        OrgLayerInfoDTO orgLayerInfo = new OrgLayerInfoDTO();
        orgLayerInfo.setDistrictCode(TEST_DISTRICT_CODE);
        when(organizerOuterService.getOrgLayerInfoByOrgCode(TEST_OUTLET_CODE))
                .thenReturn(orgLayerInfo);

        // 模拟区域总层级常量
        List<String> areaManagerLevelCodes = Arrays.asList("AM1", "AM2");
        when(cmConsultantExpService.listConstantCodeByUserLevel(Constants.AREA_MANAGER_LEVEL))
                .thenReturn(areaManagerLevelCodes);

        // 模拟区域总列表 - 注意这里使用TEST_DISTRICT_CODE而不是TEST_AREA_CODE
        List<String> areaManagers = Arrays.asList("AM001", "AM002");
        when(cmConsultantExpService.listLeadersByOrgCodeAndLevel(TEST_DISTRICT_CODE, "AM1|AM2"))
                .thenReturn(areaManagers);

        // 模拟主管信息
        List<CmConsultantPO> managerInfoList = new ArrayList<>();
        // 添加分总信息
        CmConsultantPO dm1 = new CmConsultantPO();
        dm1.setConscode("DM001");
        dm1.setConsname("分总1");
        managerInfoList.add(dm1);

        CmConsultantPO dm2 = new CmConsultantPO();
        dm2.setConscode("DM002");
        dm2.setConsname("分总2");
        managerInfoList.add(dm2);

        // 添加区域执行副总信息
        CmConsultantPO vp1 = new CmConsultantPO();
        vp1.setConscode("VP001");
        vp1.setConsname("区域执行副总1");
        managerInfoList.add(vp1);

        CmConsultantPO vp2 = new CmConsultantPO();
        vp2.setConscode("VP002");
        vp2.setConsname("区域执行副总2");
        managerInfoList.add(vp2);

        // 添加区域总信息
        CmConsultantPO am1 = new CmConsultantPO();
        am1.setConscode("AM001");
        am1.setConsname("区域总1");
        managerInfoList.add(am1);

        CmConsultantPO am2 = new CmConsultantPO();
        am2.setConscode("AM002");
        am2.setConsname("区域总2");
        managerInfoList.add(am2);

        // 模拟查询主管信息
        when(crmConsultantRepository.listConsultantByConsCodes(ArgumentMatchers.anyList()))
                .thenReturn(managerInfoList);

        // 执行测试
        @SuppressWarnings("unchecked")
        List<VisitInitDataResponse.UserInfo> result = (List<VisitInitDataResponse.UserInfo>) getManageUserListMethod.invoke(
                csCommVisitService, request, cmConsultantExp);

        // 验证结果
        Assert.assertNotNull(result);
        Assert.assertEquals(6, result.size());

        // 验证返回的主管列表包含所有主管信息
        HashSet<String> expectedCodes = new HashSet<>(Arrays.asList("DM001", "DM002", "VP001", "VP002", "AM001", "AM002"));
        HashSet<String> actualCodes = new HashSet<>();
        for (VisitInitDataResponse.UserInfo userInfo : result) {
            actualCodes.add(userInfo.getCode());
        }
        Assert.assertEquals(expectedCodes, actualCodes);
    }

    /**
     * 测试场景：操作人是主管之一
     * 预期结果：返回的主管列表中不包含操作人
     */
    @Test
    public void testGetManageUserList_ExcludeOperator() throws Exception {
        // 准备测试数据
        VisitInitDataRequest request = new VisitInitDataRequest();
        request.setConsCode("DM001"); // 操作人是分总之一

        CmConsultantExpPO cmConsultantExp = new CmConsultantExpPO();
        cmConsultantExp.setOutletcode(TEST_OUTLET_CODE);
        cmConsultantExp.setConscode("DM001");

        // 模拟分总层级常量
        List<String> divisionManagerLevelCodes = Arrays.asList("DM1", "DM2");
        when(cmConsultantExpService.listConstantCodeByUserLevel(Constants.DIVISION_MANAGER_LEVEL))
                .thenReturn(divisionManagerLevelCodes);

        // 模拟分总列表，包含操作人
        List<String> divisionManagers = Arrays.asList("DM001", "DM002");
        when(cmConsultantExpService.listLeadersByOrgCodeAndLevel(TEST_OUTLET_CODE, "DM1|DM2"))
                .thenReturn(divisionManagers);

        // 模拟区域执行副总列表
        List<String> vicePresidents = Arrays.asList("VP001", "VP002");
        when(cmConsultantExpService.listFLeadersByOrgCode(TEST_OUTLET_CODE))
                .thenReturn(vicePresidents);

        // 模拟OrgLayerInfoDTO对象
        OrgLayerInfoDTO orgLayerInfo = new OrgLayerInfoDTO();
        orgLayerInfo.setDistrictCode(TEST_DISTRICT_CODE);
        when(organizerOuterService.getOrgLayerInfoByOrgCode(TEST_OUTLET_CODE))
                .thenReturn(orgLayerInfo);

        // 模拟区域总层级常量
        List<String> areaManagerLevelCodes = Arrays.asList("AM1", "AM2");
        when(cmConsultantExpService.listConstantCodeByUserLevel(Constants.AREA_MANAGER_LEVEL))
                .thenReturn(areaManagerLevelCodes);

        // 模拟区域总列表 - 使用TEST_DISTRICT_CODE
        List<String> areaManagers = Arrays.asList("AM001", "AM002");
        when(cmConsultantExpService.listLeadersByOrgCodeAndLevel(TEST_DISTRICT_CODE, "AM1|AM2"))
                .thenReturn(areaManagers);

        // 模拟主管信息，不包含操作人
        List<CmConsultantPO> managerInfoList = new ArrayList<>();
        // 添加分总信息，不包含操作人DM001
        CmConsultantPO dm2 = new CmConsultantPO();
        dm2.setConscode("DM002");
        dm2.setConsname("分总2");
        managerInfoList.add(dm2);

        // 添加区域执行副总信息
        CmConsultantPO vp1 = new CmConsultantPO();
        vp1.setConscode("VP001");
        vp1.setConsname("区域执行副总1");
        managerInfoList.add(vp1);

        CmConsultantPO vp2 = new CmConsultantPO();
        vp2.setConscode("VP002");
        vp2.setConsname("区域执行副总2");
        managerInfoList.add(vp2);

        // 添加区域总信息
        CmConsultantPO am1 = new CmConsultantPO();
        am1.setConscode("AM001");
        am1.setConsname("区域总1");
        managerInfoList.add(am1);

        CmConsultantPO am2 = new CmConsultantPO();
        am2.setConscode("AM002");
        am2.setConsname("区域总2");
        managerInfoList.add(am2);

        // 模拟查询主管信息
        when(crmConsultantRepository.listConsultantByConsCodes(ArgumentMatchers.anyList()))
                .thenReturn(managerInfoList);

        // 执行测试
        @SuppressWarnings("unchecked")
        List<VisitInitDataResponse.UserInfo> result = (List<VisitInitDataResponse.UserInfo>) getManageUserListMethod.invoke(
                csCommVisitService, request, cmConsultantExp);

        // 验证结果
        Assert.assertNotNull(result);
        Assert.assertEquals(5, result.size());

        // 验证返回的主管列表不包含操作人
        for (VisitInitDataResponse.UserInfo userInfo : result) {
            Assert.assertNotEquals("DM001", userInfo.getCode());
        }
    }

    /**
     * 测试场景：主管列表为空
     * 预期结果：返回空列表
     */
    @Test
    public void testGetManageUserList_EmptyManagerList() throws Exception {
        // 准备测试数据
        VisitInitDataRequest request = new VisitInitDataRequest();
        request.setConsCode(TEST_CONS_CODE);

        CmConsultantExpPO cmConsultantExp = new CmConsultantExpPO();
        cmConsultantExp.setOutletcode(TEST_OUTLET_CODE);
        cmConsultantExp.setConscode(TEST_CONS_CODE);

        // 模拟分总层级常量
        List<String> divisionManagerLevelCodes = Arrays.asList("DM1", "DM2");
        when(cmConsultantExpService.listConstantCodeByUserLevel(Constants.DIVISION_MANAGER_LEVEL))
                .thenReturn(divisionManagerLevelCodes);

        // 模拟分总列表为空
        when(cmConsultantExpService.listLeadersByOrgCodeAndLevel(TEST_OUTLET_CODE, "DM1|DM2"))
                .thenReturn(new ArrayList<>());

        // 模拟区域执行副总列表为空
        when(cmConsultantExpService.listFLeadersByOrgCode(TEST_OUTLET_CODE))
                .thenReturn(new ArrayList<>());

        // 模拟OrgLayerInfoDTO对象
        OrgLayerInfoDTO orgLayerInfo = new OrgLayerInfoDTO();
        orgLayerInfo.setDistrictCode(TEST_DISTRICT_CODE);
        when(organizerOuterService.getOrgLayerInfoByOrgCode(TEST_OUTLET_CODE))
                .thenReturn(orgLayerInfo);

        // 模拟区域总层级常量
        List<String> areaManagerLevelCodes = Arrays.asList("AM1", "AM2");
        when(cmConsultantExpService.listConstantCodeByUserLevel(Constants.AREA_MANAGER_LEVEL))
                .thenReturn(areaManagerLevelCodes);

        // 模拟区域总列表为空 - 使用TEST_DISTRICT_CODE
        when(cmConsultantExpService.listLeadersByOrgCodeAndLevel(TEST_DISTRICT_CODE, "AM1|AM2"))
                .thenReturn(new ArrayList<>());

        // 执行测试
        @SuppressWarnings("unchecked")
        List<VisitInitDataResponse.UserInfo> result = (List<VisitInitDataResponse.UserInfo>) getManageUserListMethod.invoke(
                csCommVisitService, request, cmConsultantExp);

        // 验证结果
        Assert.assertNotNull(result);
        Assert.assertTrue(result.isEmpty());
    }

    /**
     * 测试场景：OrgLayerInfoDTO为空或者districtCode为空
     * 预期结果：只返回分总和区域执行副总列表
     */
    @Test
    public void testGetManageUserList_NullOrgLayerInfo() throws Exception {
        // 准备测试数据
        VisitInitDataRequest request = new VisitInitDataRequest();
        request.setConsCode(TEST_CONS_CODE);

        CmConsultantExpPO cmConsultantExp = new CmConsultantExpPO();
        cmConsultantExp.setOutletcode(TEST_OUTLET_CODE);
        cmConsultantExp.setConscode(TEST_CONS_CODE);

        // 模拟分总层级常量
        List<String> divisionManagerLevelCodes = Arrays.asList("DM1", "DM2");
        when(cmConsultantExpService.listConstantCodeByUserLevel(Constants.DIVISION_MANAGER_LEVEL))
                .thenReturn(divisionManagerLevelCodes);

        // 模拟分总列表
        List<String> divisionManagers = Arrays.asList("DM001", "DM002");
        when(cmConsultantExpService.listLeadersByOrgCodeAndLevel(TEST_OUTLET_CODE, "DM1|DM2"))
                .thenReturn(divisionManagers);

        // 模拟区域执行副总列表
        List<String> vicePresidents = Arrays.asList("VP001", "VP002");
        when(cmConsultantExpService.listFLeadersByOrgCode(TEST_OUTLET_CODE))
                .thenReturn(vicePresidents);

        // 模拟OrgLayerInfoDTO对象为空
        when(organizerOuterService.getOrgLayerInfoByOrgCode(TEST_OUTLET_CODE))
                .thenReturn(null);

        // 模拟主管信息
        List<CmConsultantPO> managerInfoList = new ArrayList<>();
        // 添加分总信息
        CmConsultantPO dm1 = new CmConsultantPO();
        dm1.setConscode("DM001");
        dm1.setConsname("分总1");
        managerInfoList.add(dm1);

        CmConsultantPO dm2 = new CmConsultantPO();
        dm2.setConscode("DM002");
        dm2.setConsname("分总2");
        managerInfoList.add(dm2);

        // 添加区域执行副总信息
        CmConsultantPO vp1 = new CmConsultantPO();
        vp1.setConscode("VP001");
        vp1.setConsname("区域执行副总1");
        managerInfoList.add(vp1);

        CmConsultantPO vp2 = new CmConsultantPO();
        vp2.setConscode("VP002");
        vp2.setConsname("区域执行副总2");
        managerInfoList.add(vp2);

        // 模拟查询主管信息
        when(crmConsultantRepository.listConsultantByConsCodes(ArgumentMatchers.anyList()))
                .thenReturn(managerInfoList);

        // 执行测试
        @SuppressWarnings("unchecked")
        List<VisitInitDataResponse.UserInfo> result = (List<VisitInitDataResponse.UserInfo>) getManageUserListMethod.invoke(
                csCommVisitService, request, cmConsultantExp);

        // 验证结果
        Assert.assertNotNull(result);
        Assert.assertEquals(4, result.size());

        // 验证返回的主管列表只包含分总和区域执行副总
        HashSet<String> expectedCodes = new HashSet<>(Arrays.asList("DM001", "DM002", "VP001", "VP002"));
        HashSet<String> actualCodes = new HashSet<>();
        for (VisitInitDataResponse.UserInfo userInfo : result) {
            actualCodes.add(userInfo.getCode());
        }
        Assert.assertEquals(expectedCodes, actualCodes);
    }

    /**
     * 测试场景：主管信息查询结果为空
     * 预期结果：返回空列表
     */
    @Test
    public void testGetManageUserList_EmptyManagerInfo() throws Exception {
        // 准备测试数据
        VisitInitDataRequest request = new VisitInitDataRequest();
        request.setConsCode(TEST_CONS_CODE);

        CmConsultantExpPO cmConsultantExp = new CmConsultantExpPO();
        cmConsultantExp.setOutletcode(TEST_OUTLET_CODE);
        cmConsultantExp.setConscode(TEST_CONS_CODE);

        // 模拟分总层级常量
        List<String> divisionManagerLevelCodes = Arrays.asList("DM1", "DM2");
        when(cmConsultantExpService.listConstantCodeByUserLevel(Constants.DIVISION_MANAGER_LEVEL))
                .thenReturn(divisionManagerLevelCodes);

        // 模拟分总列表
        List<String> divisionManagers = Arrays.asList("DM001", "DM002");
        when(cmConsultantExpService.listLeadersByOrgCodeAndLevel(TEST_OUTLET_CODE, "DM1|DM2"))
                .thenReturn(divisionManagers);

        // 模拟区域执行副总列表
        List<String> vicePresidents = Arrays.asList("VP001", "VP002");
        when(cmConsultantExpService.listFLeadersByOrgCode(TEST_OUTLET_CODE))
                .thenReturn(vicePresidents);

        // 模拟OrgLayerInfoDTO对象
        OrgLayerInfoDTO orgLayerInfo = new OrgLayerInfoDTO();
        orgLayerInfo.setDistrictCode(TEST_DISTRICT_CODE);
        when(organizerOuterService.getOrgLayerInfoByOrgCode(TEST_OUTLET_CODE))
                .thenReturn(orgLayerInfo);

        // 模拟区域总层级常量
        List<String> areaManagerLevelCodes = Arrays.asList("AM1", "AM2");
        when(cmConsultantExpService.listConstantCodeByUserLevel(Constants.AREA_MANAGER_LEVEL))
                .thenReturn(areaManagerLevelCodes);

        // 模拟区域总列表 - 使用TEST_DISTRICT_CODE
        List<String> areaManagers = Arrays.asList("AM001", "AM002");
        when(cmConsultantExpService.listLeadersByOrgCodeAndLevel(TEST_DISTRICT_CODE, "AM1|AM2"))
                .thenReturn(areaManagers);

        // 模拟查询主管信息为空
        when(crmConsultantRepository.listConsultantByConsCodes(ArgumentMatchers.anyList()))
                .thenReturn(new ArrayList<>());

        // 执行测试
        @SuppressWarnings("unchecked")
        List<VisitInitDataResponse.UserInfo> result = (List<VisitInitDataResponse.UserInfo>) getManageUserListMethod.invoke(
                csCommVisitService, request, cmConsultantExp);

        // 验证结果
        Assert.assertNotNull(result);
        Assert.assertTrue(result.isEmpty());
    }
}