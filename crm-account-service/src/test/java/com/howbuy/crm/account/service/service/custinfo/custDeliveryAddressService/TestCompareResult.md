## 测试的类
> com.howbuy.crm.account.service.service.custinfo.custDeliveryAddressService
## 测试的方法 
> compareHboneReceiver(ConsCustDeliveryAddressRequest request)

## 分支伪代码
``` java
验证过程 {

    if (投顾版收货地址信息不全) {
        返回 待比对的收货信息不完善!
    }
    if (查询的投顾客户信息为空) {
       返回 查询的投顾客户信息为空
    }
    if (查询的投顾客户的一账通号为空) {
        返回 ok
    }
    if (查询的投顾客户的一账通号不为空&&客户版的收货地址返回值为空) {
        返回 ok
    }
    if (查询的投顾客户的一账通号不为空&&客户版的收货地址返回值为失败) {
        返回 ok
    }
    if (查询的投顾客户的一账通号不为空&&客户版的收货地址返回值为成功&&客户版的收货地址list为空 ) {
        返回 ok
    }
    if (查询的投顾客户的一账通号不为空&&客户版的收货地址返回值为成功&&客户版的收货地址list不为空&&投顾版的收货地址和客户版收货地址不一致) {
        返回 fail
    }
    
    返回 ok
}


## 测试案例
1、投顾版收货地址信息不全：则返回待比对的收货信息不完善!
### test01
2、查询的投顾客户信息为空：则返回查询的投顾客户信息为空
### test02
3、查询的投顾客户的一账通号为空：则返回ok
### test03
4、(查询的投顾客户的一账通号不为空&&客户版的收货地址返回值为空)：则返回ok
### test04
5、查询的投顾客户的一账通号不为空&&客户版的收货地址返回值为失败：则返回ok
### test05
6、查询的投顾客户的一账通号不为空&&客户版的收货地址返回值为成功&&客户版的收货地址list为空 ：则返回ok
### test06
7、查询的投顾客户的一账通号不为空&&客户版的收货地址返回值为成功&&客户版的收货地址list不为空&&投顾版的收货地址和客户版收货地址不一致：则返回fail
### test07
8、查询的投顾客户的一账通号不为空&&客户版的收货地址返回值为成功&&客户版的收货地址list不为空&&投顾版的收货地址和客户版收货地址一致：则返回ok
### test08
```