/**
 * Copyright (c) 2023, <PERSON><PERSON><PERSON><PERSON> HOWBUY INVESTMENT MANAGEMENT Co., Ltd.
 * All right reserved.
 * <p>
 * THIS IS UNPUBLISHED PROPRIETARY SOURCE CODE OF HOWBUY INVESTMENT
 * MANAGEMENT CO., LTD.  THE CONTENTS OF THIS FILE MAY NOT BE DISCLOSED
 * TO THIRD PARTIES, COPIED OR DUPLICATED IN ANY FORM, IN WHOLE OR IN PART,
 * WITHOUT THE PRIOR WRITTEN PERMISSION OF HOWBUY INVESTMENT MANAGEMENT
 * CO., LTD.
 */
package com.howbuy.crm.account.remote.log;

/**
 * @description: 日志打点
 * <AUTHOR>
 * @date 2023/3/16 10:23
 * @since JDK 1.8
 */
public class FeignMarkConstant {
    private FeignMarkConstant(){

    }
    /**
     * 访问外部超时 或者错误
     */
    public static final String CALL_OUT_ERROR = "crm.account.callOuterServiceError";

    /**
     * 通过feign 调用http接口状态码非200错误
     */
    public static final String FEIGN_HTTP_NO_OK_ERROR = "crm.account.feign.no.okError";
}