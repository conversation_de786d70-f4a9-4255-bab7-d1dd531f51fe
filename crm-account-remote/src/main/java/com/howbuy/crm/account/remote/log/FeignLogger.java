/**
 * Copyright (c) 2023, <PERSON><PERSON><PERSON><PERSON> HOWBUY INVESTMENT MANAGEMENT Co., Ltd.
 * All right reserved.
 * <p>
 * THIS IS UNPUBLISHED PROPRIETARY SOURCE CODE OF HOWBUY INVESTMENT
 * MANAGEMENT CO., LTD.  THE CONTENTS OF THIS FILE MAY NOT BE DISCLOSED
 * TO THIRD PARTIES, COPIED OR DUPLICATED IN ANY FORM, IN WHOLE OR IN PART,
 * WITHOUT THE PRIOR WRITTEN PERMISSION OF HOWBUY INVESTMENT MANAGEMENT
 * CO., LTD.
 */
package com.howbuy.crm.account.remote.log;

import feign.Logger;
import feign.Request;
import feign.Response;
import feign.Util;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.MarkerFactory;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.http.HttpStatus;

import java.io.IOException;
import java.io.UnsupportedEncodingException;
import java.net.URLDecoder;
import java.util.Objects;

import static feign.Util.UTF_8;
import static feign.Util.decodeOrDefault;

/**
 * @description: feign 日志
 * <AUTHOR>
 * @date 2023/3/16 10:20
 * @since JDK 1.8
 */
@Slf4j
public class FeignLogger extends Logger{
    @Value("${feign.log.cost.time:5000}")
    private long costTime;

    /**
     * http 成功请求状态码 200
     */
    private static final int HTTP_OK = 200;

    private static final int  MAX_LOG_SIZE = 20000;

    /**
     *
     *  打印request
     * @param configKey key
     * @param logLevel 日志级别
     * @param request  request
     * @return void
     * @author: tao.zhang01
     * @date: 2022/9/30 15:24
     * @since: JDK 1.8
     */
    @Override
    protected void logRequest(String configKey, Level logLevel, Request request) {
        // 不打印日志
    }


    /**
     * 打印response
     * @param configKey key
     * @param logLevel 日志级别
     * @param response response
     * @param elapsedTime  耗时
     * @return feign.Response
     * @author: tao.zhang01
     * @date: 2022/9/30 15:25
     * @since: JDK 1.8
     */
    @Override
    protected Response logAndRebufferResponse(String configKey, Level logLevel, Response response,
                                              long elapsedTime) throws IOException {
        int status = response.status();
        Request request = response.request();
        String url = this.urlDecode(request.url());
        if (HTTP_OK != status) {
            log.error(MarkerFactory.getMarker(FeignMarkConstant.FEIGN_HTTP_NO_OK_ERROR), "status:{}, url:{}", status, url);
        }
        String body = "";
        if (request.body() != null) {
            body = new String(request.body());
        }
        if (!this.responseBodyHaveValue(response)) {
            log.info("Sending RequestUrl: {}, requestBody: {},  Response " +
                    "httpCode: {}, {}ms, responseBody: {}", url, body, status, elapsedTime, "");
            return response;
        }
        byte[] bodyData = Util.toByteArray(response.body().asInputStream());
        String content = "";
        if (bodyData.length > 0) {
            content = decodeOrDefault(bodyData, UTF_8, "Binary data");
        }
        if (elapsedTime > costTime) {
            log.error(MarkerFactory.getMarker(FeignMarkConstant.CALL_OUT_ERROR + ":" + url), "url: {}, cost:{}", url, elapsedTime);
        }

        if(StringUtils.isNotEmpty(content) && content.length() > MAX_LOG_SIZE){
            content = content.substring(0, MAX_LOG_SIZE/2) + "..." + content.substring(content.length() - MAX_LOG_SIZE/2);
        }
        log.info("Sending RequestUrl {}, requestBody: {},  Response " +
                "httpCode: {}, {}ms, responseBody: {}", url, body, status, elapsedTime, content);
        return response.toBuilder().body(bodyData).build();
    }

    /**
     *  response 是否有值
     * @param response  response
     * @return boolean
     * @author: tao.zhang01
     * @date: 2022/9/30 15:22
     * @since: JDK 1.8
     */
    private boolean responseBodyHaveValue(Response response) {
        int status = response.status();
        return Objects.nonNull(response.body()) &&
                !(status == HttpStatus.NO_CONTENT.value() || status == HttpStatus.RESET_CONTENT.value());
    }

    @Override
    protected void log(String configKey, String format, Object... args) {
        // 不打印日志
    }

    /**
     * 解码url
     * @param url url
     * @return java.lang.String
     * @author: tao.zhang01
     * @date: 2022/10/18 15:51
     * @since: JDK 1.8
     */
    private String urlDecode(String url) {
        try {
            return URLDecoder.decode(url, "utf-8");
        } catch (UnsupportedEncodingException e) {
            return url;
        }
    }
}