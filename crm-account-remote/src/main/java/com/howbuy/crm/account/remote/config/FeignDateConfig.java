/**
 * Copyright (c) 2023, <PERSON><PERSON><PERSON><PERSON> HOWBUY INVESTMENT MANAGEMENT Co., Ltd.
 * All right reserved.
 * <p>
 * THIS IS UNPUBLISHED PROPRIETARY SOURCE CODE OF HOWBUY INVESTMENT
 * MANAGEMENT CO., LTD.  THE CONTENTS OF THIS FILE MAY NOT BE DISCLOSED
 * TO THIRD PARTIES, COPIED OR DUPLICATED IN ANY FORM, IN WHOLE OR IN PART,
 * WITHOUT THE PRIOR WRITTEN PERMISSION OF HOWBUY INVESTMENT MANAGEMENT
 * CO., LTD.
 */
package com.howbuy.crm.account.remote.config;

import org.springframework.cloud.openfeign.FeignFormatterRegistrar;
import org.springframework.context.annotation.Configuration;
import org.springframework.format.FormatterRegistry;
import org.springframework.format.datetime.DateFormatter;

/**
 * @description: feign 时间格式配置 否则 认为是美国时区传入
 *  中国时区接收 ,然后自动加入14小时
 * <AUTHOR>
 * @date 2023/3/16 10:17
 * @since JDK 1.8
 */
@Configuration
public class FeignDateConfig implements FeignFormatterRegistrar {

    private static final String DATA_PATTERN = "yyyy-MM-dd HH:mm:ss.SSS";

    @Override
    public void registerFormatters(FormatterRegistry registry) {
        registry.addFormatter(dateFormatter());
    }

    /**
     * @description: 将date 类型转成String
     * @return org.springframework.format.datetime.DateFormatter
     * @author: hongdong.xie
     * @date: 2023/3/16 10:18
     * @since JDK 1.8
     */
    public DateFormatter dateFormatter(){
        return new DateFormatter(DATA_PATTERN);
    }
}