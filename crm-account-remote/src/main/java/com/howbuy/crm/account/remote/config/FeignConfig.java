/**
 * Copyright (c) 2023, <PERSON><PERSON><PERSON><PERSON> HOWBUY INVESTMENT MANAGEMENT Co., Ltd.
 * All right reserved.
 * <p>
 * THIS IS UNPUBLISHED PROPRIETARY SOURCE CODE OF HOWBUY INVESTMENT
 * MANAGEMENT CO., LTD.  THE CONTENTS OF THIS FILE MAY NOT BE DISCLOSED
 * TO THIRD PARTIES, COPIED OR DUPLICATED IN ANY FORM, IN WHOLE OR IN PART,
 * WITHOUT THE PRIOR WRITTEN PERMISSION OF HOWBUY INVESTMENT MANAGEMENT
 * CO., LTD.
 */
package com.howbuy.crm.account.remote.config;

import com.howbuy.crm.account.remote.interceptor.FeignInterceptor;
import com.howbuy.crm.account.remote.log.FeignLogger;
import feign.Logger;
import feign.Request;
import feign.RequestInterceptor;
import feign.Retryer;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.web.client.RestTemplate;

import java.util.concurrent.TimeUnit;

/**
 * @description: feignConfig
 * <AUTHOR>
 * @date 2023/3/16 10:14
 * @since JDK 1.8
 */
@Configuration
public class FeignConfig {

    /**
     * @description:日志级别
     * @return feign.Logger.Level
     * @author: hongdong.xie
     * @date: 2023/3/16 10:15
     * @since JDK 1.8
     */
    @Bean
    public Logger.Level feignLogLevel() {
        // 日志级别为BASIC 记录请求 响应 超时时间
        return Logger.Level.HEADERS;
    }

    /**
     * 过滤器
     * @return feign.RequestInterceptor
     * @author: hongdong.xie
     * @date: 2023/3/16 10:15
     * @since: JDK 1.8
     */
    @Bean
    public RequestInterceptor requestInterceptor() {
        return new FeignInterceptor();
    }

    /**
     * 将重写的日志放到容器里面
     * @return feign.Logger
     * @author: hongdong.xie
     * @date: 2023/3/16 10:15
     * @since: JDK 1.8
     */
    @Bean
    public Logger feignLogger() {
        return new FeignLogger();
    }

    /**
     * 构建feign的连接时间 和 处理响应时间 20s/30s,
     * 且关心重定向
     * @return feign.Request.Options
     * @author: hongdong.xie
     * @date: 2023/3/16 10:15
     * @since: JDK 1.8
     */
    @Bean
    public Request.Options options() {
        return new Request.Options(20, TimeUnit.SECONDS, 60,TimeUnit.SECONDS,true);
    }


    /**
     * 不进行重试
     * @return feign.Retryer
     * @author: hongdong.xie
     * @date: 2023/3/16 10:15
     * @since: JDK 1.8
     */
    @Bean
    public Retryer feignRetry() {
        return Retryer.NEVER_RETRY;
    }

    /**
     * 构建restTemplate
     * @return org.springframework.web.client.RestTemplate
     * @author: hongdong.xie
     * @date: 2023/3/16 10:15
     * @since: JDK 1.8
     */
    @Bean
    public RestTemplate restTemplate() {
        return new RestTemplate();
    }
}