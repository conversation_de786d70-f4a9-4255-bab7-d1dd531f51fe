/**
 * Copyright (c) 2023, <PERSON><PERSON><PERSON><PERSON> HOWBUY INVESTMENT MANAGEMENT Co., Ltd.
 * All right reserved.
 * <p>
 * THIS IS UNPUBLISHED PROPRIETARY SOURCE CODE OF HOWBUY INVESTMENT
 * MANAGEMENT CO., LTD.  THE CONTENTS OF THIS FILE MAY NOT BE DISCLOSED
 * TO THIRD PARTIES, COPIED OR DUPLICATED IN ANY FORM, IN WHOLE OR IN PART,
 * WITHOUT THE PRIOR WRITTEN PERMISSION OF HOWBUY INVESTMENT MANAGEMENT
 * CO., LTD.
 */
package com.howbuy.crm.account.remote.interceptor;

import com.howbuy.crm.account.service.commom.constant.Constants;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.apache.logging.log4j.ThreadContext;
import org.slf4j.MDC;
import org.springframework.stereotype.Component;
import org.springframework.web.servlet.handler.HandlerInterceptorAdapter;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.util.UUID;

/**
 * @description: 日志拦截器
 * <AUTHOR>
 * @date 2023/5/22 10:54
 * @since JDK 1.8
 */
@Slf4j
@Component
public class LogInterceptor extends HandlerInterceptorAdapter {
    @Override
    public boolean preHandle(HttpServletRequest request, HttpServletResponse response, Object handler) throws Exception {
        long reqcost = System.currentTimeMillis();
        request.setAttribute(Constants.SERVICE_REQUEST_COST, reqcost);
        String traceId = getTraceId(request);
        MDC.clear();
        MDC.put(Constants.TRACE_ID,traceId);
        log.info("#### HTTP REQUEST ####,URL:{},",request.getRequestURI());
        return true;
    }

    @Override
    public void afterCompletion(HttpServletRequest request, HttpServletResponse response, Object handler, Exception ex) throws Exception {
        long cost = System.currentTimeMillis() - (long)request.getAttribute(Constants.SERVICE_REQUEST_COST);
        log.info("#### HTTP RESPONSE ####,URI:{},cost:{}",request.getRequestURI(), cost);

        MDC.clear();
    }

    /**
     * @description: 获取traceId
     * @param request
     * @return java.lang.String
     * @author: hongdong.xie
     * @date: 2023/5/22 11:11
     * @since JDK 1.8
     */
    private String getTraceId(HttpServletRequest request){
        String tradeId = request.getHeader(Constants.XTRACES);
        if(StringUtils.isNotEmpty(tradeId)){
            return tradeId;
        }
        return UUID.randomUUID().toString();
    }


}