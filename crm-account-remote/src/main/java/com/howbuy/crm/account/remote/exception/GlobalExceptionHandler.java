/**
 * Copyright (c) 2023, <PERSON><PERSON><PERSON><PERSON> HOWBUY INVESTMENT MANAGEMENT Co., Ltd.
 * All right reserved.
 * <p>
 * THIS IS UNPUBLISHED PROPRIETARY SOURCE CODE OF HOWBUY INVESTMENT
 * MANAGEMENT CO., LTD.  THE CONTENTS OF THIS FILE MAY NOT BE DISCLOSED
 * TO THIRD PARTIES, COPIED OR DUPLICATED IN ANY FORM, IN WHOLE OR IN PART,
 * WITHOUT THE PRIOR WRITTEN PERMISSION OF HOWBUY INVESTMENT MANAGEMENT
 * CO., LTD.
 */
package com.howbuy.crm.account.remote.exception;

import com.alibaba.fastjson.JSON;
import com.howbuy.crm.account.client.enums.ExceptionCodeEnum;
import com.howbuy.crm.account.client.response.Response;
import com.howbuy.crm.account.service.commom.exception.BusinessException;
import com.howbuy.crm.account.service.commom.exception.ParamsException;
import lombok.extern.slf4j.Slf4j;
import net.sf.oval.exception.ConstraintsViolatedException;
import org.springframework.web.bind.annotation.ControllerAdvice;
import org.springframework.web.bind.annotation.ExceptionHandler;
import org.springframework.web.bind.annotation.ResponseBody;

/**
 * @description: 全局异常处理器，用于处理所有的异常，包括业务异常和系统异常，统一返回json格式的错误信息，方便前端处理
 * @create: 2023/12/6
 * <AUTHOR>
 * @date 2023/12/6 13:24
 * @since JDK 1.8
 */
@Slf4j
@ControllerAdvice
public class GlobalExceptionHandler {
    /**
     * @description: 参数错误异常处理
     * @param e	异常信息
     * @return com.howbuy.crm.account.client.response.Response
     * @author: hongdong.xie
     * @date: 2023/12/6 13:38
     * @since JDK 1.8
     */
    @ExceptionHandler(value = ConstraintsViolatedException.class)
    @ResponseBody
    public Response paramsExceptionHandler(ConstraintsViolatedException e) {
        log.error("参数异常1：", e);
        Response response = Response.fail(ExceptionCodeEnum.PARAMS_ERROR.getCode(), e.getMessage());
        printLog(response);
        return response;
    }

    /**
     * @description:参数错误异常处理
     * @param e	
     * @return com.howbuy.crm.account.client.response.Response
     * @author: hongdong.xie
     * @date: 2023/12/6 13:39
     * @since JDK 1.8
     */
    @ExceptionHandler(value = IllegalArgumentException.class)
    @ResponseBody
    public Response illegalArgumentExceptionHandler(IllegalArgumentException e) {
        log.error("参数异常2：", e);
        Response response = Response.fail(ExceptionCodeEnum.PARAMS_ERROR.getCode(), e.getMessage());
        printLog(response);
        return response;
    }
    
    
    /**
     * @description: 参数异常处理
     * @param e	
     * @return com.howbuy.crm.account.client.response.Response<java.lang.Object>
     * @author: hongdong.xie
     * @date: 2023/12/6 13:39
     * @since JDK 1.8
     */
    @ExceptionHandler(value = ParamsException.class)
    @ResponseBody
    public Response<Object> paramsExceptionHandler(ParamsException e) {
        log.error("参数异常3：", e);
        Response response = Response.fail(e.getCode(), e.getDesc());
        printLog(response);
        return response;
    }

    /**
     * @description: 自定义业务处理异常，业务异常处理
     * @param e
     * @return com.howbuy.crm.account.client.response.Response
     * @author: hongdong.xie
     * @date: 2023/12/6 13:39
     * @since JDK 1.8
     */
    @ExceptionHandler(value = BusinessException.class)
    @ResponseBody
    public Response businessExceptionHandler(BusinessException e) {
        log.error("业务异常：", e);
        Response response = Response.fail(e.getCode(), e.getDesc());
        printLog(response);
        return response;
    }

    /**
     * @description: 系统异常处理
     * @param e
     * @return com.howbuy.crm.account.client.response.Response
     * @author: hongdong.xie
     * @date: 2023/12/6 13:40
     * @since JDK 1.8
     */
    @ExceptionHandler(value = Exception.class)
    @ResponseBody
    public Response defaultExceptionHandler(Exception e) {
        log.error(ExceptionMarker.getMarker(ExceptionMarker.SYSTEM_ERROR_KEY), e.getMessage());
        log.error("系统异常：", e);
        Response response = Response.fail(ExceptionCodeEnum.SYSTEM_ERROR.getCode(), ExceptionCodeEnum.SYSTEM_ERROR.getDesc());
        printLog(response);
        return response;
    }

    /**
     * @description: 日志打印
     * @param response	返回信息
     * @return void
     * @author: hongdong.xie
     * @date: 2023/12/6 13:42
     * @since JDK 1.8
     */
    private void printLog(Response response) {
        log.info("response:{}",JSON.toJSONString(response));
    }
}