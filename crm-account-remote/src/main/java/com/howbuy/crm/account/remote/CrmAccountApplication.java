/**
 * Copyright (c) 2022, <PERSON><PERSON><PERSON><PERSON> HOWBUY INVESTMENT MANAGEMENT Co., Ltd.
 * All right reserved.
 * <p>
 * THIS IS UNPUBLISHED PROPRIETARY SOURCE CODE OF HOWBUY INVESTMENT
 * MANAGEMENT CO., LTD.  THE CONTENTS OF THIS FILE MAY NOT BE DISCLOSED
 * TO THIRD PARTIES, COPIED OR DUPLICATED IN ANY FORM, IN WHOLE OR IN PART,
 * WITHOUT THE PRIOR WRITTEN PERMISSION OF HOWBUY INVESTMENT MANAGEMENT
 * CO., LTD.
 */
package com.howbuy.crm.account.remote;

import org.apache.dubbo.config.spring.context.annotation.EnableDubbo;
import org.mybatis.spring.annotation.MapperScan;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.boot.SpringApplication;
import org.springframework.boot.autoconfigure.SpringBootApplication;
import org.springframework.boot.autoconfigure.jdbc.DataSourceAutoConfiguration;
import org.springframework.boot.web.servlet.ServletComponentScan;
import org.springframework.cloud.client.discovery.EnableDiscoveryClient;
import org.springframework.cloud.openfeign.EnableFeignClients;
import org.springframework.web.bind.annotation.*;

/**
 * <AUTHOR>
 * @description: 订单系统启动类
 * @date 2022/9/14 8:46
 * @since JDK 1.8
 */
@RestController
@EnableDiscoveryClient
@MapperScan("com.howbuy.crm.account.dao.mapper")
@EnableFeignClients(basePackages = "com.howbuy.crm.account.service.outerservice")
@ServletComponentScan(basePackages = "com.howbuy.crm.account.service")
@EnableDubbo(scanBasePackages = "com.howbuy.crm.account.service.facade")
@SpringBootApplication(scanBasePackages = {"com.howbuy.crm.account"}, exclude = {DataSourceAutoConfiguration.class})
public class CrmAccountApplication {

    @Value("${spring.application.name}")
    private String applicationName;

    public static void main(String[] args) {
        //设置dubbo 日志为slf4j
        System.setProperty("dubbo.application.log", "slf4j");
        SpringApplication.run(CrmAccountApplication.class, args);
    }

    @RequestMapping( value = "/index", method = {RequestMethod.GET, RequestMethod.POST})
    public String hello() {
        return "hello, ".concat(this.applicationName);
    }

}
