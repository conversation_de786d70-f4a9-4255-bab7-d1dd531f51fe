<?xml version="1.0" encoding="UTF-8"?>
<Configuration status="info" monitorInterval="30">

	<properties>
		<property name="logPath">/data/logs/crm-account-remote</property>
		<property name="rollingLogName">crm-account-remote</property>
		<property name="mainLogName">crm-account-main</property>
		<property name="sqlLevel">info</property>
		<property name="cacheLogName">cache-state-collect</property>
		<property name="slowSqlLog">slow-sql</property>
		<property name="paLog">pa</property>
		<property name="rocketLog">rocketmq-client</property>
	</properties>

	<Appenders>
		<Console name="Console" target="SYSTEM_OUT">
			<CustomPatternLayout pattern='{"time":"%d{yyyy-MM-dd HH:mm:ss.SSS}","custNo":"%X{custNo}","tid":"%X{traceId}","status":"%X{status}","labelName":"%markerSimpleName","thread":"%t","level":"%-5p","class_line":"%c:%L","msg":"%enc{%msg}{JSON}"}%n'>
				<replaces>
					<replace regex='(\\"idNo\\":\\")(\d{6})\d{1,8}(\d{4})' replacement="$1$2********$3" />
					<replace regex='(\\"bankAcct\\":\\")(\d{6})\d{1,6}(\d{4})' replacement="$1$2********$3" />
					<replace regex='(\\"fileBytes\\":\\")[^\\"]*' replacement="$1***" />
				</replaces>
			</CustomPatternLayout>
		</Console>

		<RollingFile name="RollingFile" filename="${logPath}/${rollingLogName}.log" filepattern="${logPath}/%d{yyyyMMdd}/${rollingLogName}-%i.log">
			<CustomPatternLayout pattern='{"time":"%d{yyyy-MM-dd HH:mm:ss.SSS}","custNo":"%X{custNo}","tid":"%X{traceId}","status":"%X{status}","labelName":"%markerSimpleName","thread":"%t","level":"%-5p","class_line":"%c:%L","msg":"%enc{%msg}{JSON}"}%n'>
				<replaces>
					<replace regex='(\\"idNo\\":\\")(\d{6})\d{1,8}(\d{4})' replacement="$1$2********$3" />
					<replace regex='(\\"bankAcct\\":\\")(\d{6})\d{1,6}(\d{4})' replacement="$1$2********$3" />
					<replace regex='(\\"fileBytes\\":\\")[^\\"]*' replacement="$1***" />
				</replaces>
			</CustomPatternLayout>
			<Policies>
				<TimeBasedTriggeringPolicy interval="1" modulate="true" />
				<SizeBasedTriggeringPolicy size="1024 MB" />
			</Policies>
			<DefaultRolloverStrategy max="100" />
		</RollingFile>
		
		<RollingFile name="MainLogger" filename="${logPath}/${mainLogName}.log"
			filepattern="${logPath}/%d{yyyyMMdd}/${mainLogName}.log">
			<PatternLayout pattern="%msg%n" />
			<Policies>
				<TimeBasedTriggeringPolicy interval="1" modulate="true" />
			</Policies>
			<DefaultRolloverStrategy max="30" />
		</RollingFile>
		
		<RollingFile name="CacheStatCollectLogger" filename="${logPath}/${cacheLogName}.log"
			filepattern="${logPath}/%d{yyyyMMdd}/${cacheLogName}.log">
			<PatternLayout pattern="%msg%n" />
			<Policies>
				<TimeBasedTriggeringPolicy interval="1" modulate="true" />
			</Policies>
			<DefaultRolloverStrategy max="30" />
		</RollingFile>


		<RollingFile name="paRelatedLog" filename="${logPath}/${paLog}.log"
					 filepattern="${logPath}/%d{yyyyMMdd}/${paLog}.log">
			<PatternLayout pattern="%msg%n" />
			<Policies>
				<TimeBasedTriggeringPolicy interval="1" modulate="true" />
			</Policies>
			<DefaultRolloverStrategy max="30" />
		</RollingFile>

		<RollingFile name="RocketmqClientLog" filename="${logPath}/${rocketLog}.log"
					 filepattern="${logPath}/%d{yyyyMMdd}/${rocketLog}.log">
			<PatternLayout pattern="%msg%n" />
			<Policies>
				<TimeBasedTriggeringPolicy interval="1" modulate="true" />
			</Policies>
			<DefaultRolloverStrategy max="30" />
		</RollingFile>

		<RollingFile name="SlowSqlLog" filename="${logPath}/${slowSqlLog}.log"
					 filepattern="${logPath}/%d{yyyyMMdd}/${slowSqlLog}.log">
			<PatternLayout pattern='{"time":"%d{yyyy-MM-dd HH:mm:ss.SSS}","custNo":"%X{custNo}","tid":"%X{traceId}","status":"%X{status}","labelName":"%markerSimpleName","thread":"%t","level":"%-5p","class_line":"%c:%L","msg":"%enc{%msg}{JSON}"}%n'/>
			<Policies>
				<TimeBasedTriggeringPolicy interval="1" modulate="true" />
			</Policies>
			<DefaultRolloverStrategy max="30" />
		</RollingFile>

	</Appenders>

	<Loggers>
		<AsyncLogger name="com.howbuy" level="info" additivity="false" includeLocation="true">
			<appender-ref ref="RollingFile" />
		</AsyncLogger>
		<AsyncLogger name="mainlog" level="info" additivity="false">
             <appender-ref ref="MainLogger"/>
        </AsyncLogger>
		<AsyncLogger name="com.howbuy.crm.account.dao.mapper" level="${sqlLevel}" additivity="false" includeLocation="true">
			<AppenderRef ref="RollingFile" />
		</AsyncLogger>
		<AsyncLogger name="com.howbuy.pa" level="info" additivity="false">
			<appender-ref ref="paRelatedLog" />
		</AsyncLogger>
		<AsyncLogger name="RocketmqClient" level="warn"  additivity="false">
			<appender-ref ref="RocketmqClientLog"/>
		</AsyncLogger>
		<AsyncLogger name="COST_TIME_COLLECT_LOGGER" level="error" additivity="false">
			<AppenderRef ref="CacheStatCollectLogger" />
		</AsyncLogger>
		<AsyncLogger name="com.alibaba.nacos" level="error" additivity="false">
			<appender-ref ref="RollingFile" />
		</AsyncLogger>
		<AsyncLogger name="slowSqlLog" level="info" additivity="false">
			<AppenderRef ref="SlowSqlLog"/>
		</AsyncLogger>
		<root level="info" includeLocation="true">
			<appender-ref ref="Console" />
			<appender-ref ref="RollingFile" />
		</root>
	</Loggers>
</Configuration>