crm-account
===========================

###########环境依赖
JDK1.8

###########部署步骤
start.sh

###########目录结构描述
├── crm-account            // 项目名称
├── crm-acount-dao      // 数据dao层
├── crm-account-remote   // 启动
├── crm-account-service  // 业务代码
├── pom.xml                 // 父pom
└── README.md               // help

###########事务处理
1.springCloud 项目采取注解 @Transactional 来开启事务
2.在类上加上@Transactional(rollbackFor = Exception.class, propagation = Propagation.SUPPORTS)
保证有事务 就在事务中运行,没有就无事务运行
3.增删改 在方法上加上@Transactional(rollbackFor = Exception.class, propagation = Propagation.REQUIRED)
默认的事务传播机制,没有事务就开启一个,有就在当前事务中运行.
4.项目命名规范见：http://dms.intelnal.howbuy.com/pages/viewpage.action?pageId=********

