<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.howbuy.crm.account.dao.mapper.customize.custinfo.ConscustCustomizeMapper">
    <resultMap id="BaseCustForAnalyseResultMap" type="com.howbuy.crm.account.dao.bo.custinfo.CmConscustForAnalyseBO">
        <!--@mbg.generated-->
        <!--@Table CM_CONSCUST-->
        <id column="CONSCUSTNO" jdbcType="VARCHAR" property="conscustno"/>
        <result column="CONSCUSTSTATUS" jdbcType="VARCHAR" property="conscuststatus"/>
        <result column="IDTYPE" jdbcType="VARCHAR" property="idtype"/>
        <result column="CUSTNAME" jdbcType="VARCHAR" property="custname"/>
        <result column="INVSTTYPE" jdbcType="VARCHAR" property="invsttype"/>
        <result column="HBONE_NO" jdbcType="VARCHAR" property="hboneNo"/>
        <result column="IDNO_DIGEST" jdbcType="VARCHAR" property="idnoDigest"/>
        <result column="IDNO_MASK" jdbcType="VARCHAR" property="idnoMask"/>
        <result column="MOBILE_DIGEST" jdbcType="VARCHAR" property="mobileDigest"/>
        <result column="MOBILE_MASK" jdbcType="VARCHAR" property="mobileMask"/>
        <result column="EMAIL_DIGEST" jdbcType="VARCHAR" property="emailDigest"/>
        <result column="EMAIL_MASK" jdbcType="VARCHAR" property="emailMask"/>
        <result column="MOBILE_AREA_CODE" jdbcType="VARCHAR" property="mobileAreaCode"/>
        <result column="ID_SIGN_AREA_CODE" jdbcType="VARCHAR" property="idSignAreaCode"/>
        <result column="NATION_CODE" jdbcType="VARCHAR" property="nationCode"/>

        <!--以下为 香港客户信息-->
        <result column="HKCUSTID" jdbcType="VARCHAR" property="hkcustid"/>
        <result column="HK_TX_ACCT_NO" jdbcType="VARCHAR" property="hkTxAcctNo"/>
        <!--以下为  所属投顾信息-->
        <result column="CONSCODE" jdbcType="VARCHAR" property="consCode"/>
    </resultMap>


    <select id="selectHboneNoByCustNo" parameterType="java.lang.String" resultType="string">
        <!--@mbg.generated-->
        SELECT C.HBONE_NO
        FROM CM_CONSCUST C
        WHERE C.CONSCUSTNO = #{custNo,jdbcType=VARCHAR}
    </select>


    <select id="selectConsCodeByCustNo" parameterType="java.lang.String" resultType="string">
        <!--@mbg.generated-->
        SELECT C.CONSCODE
        FROM CM_CUSTCONSTANT C
        WHERE C.CUSTNO = #{custNo,jdbcType=VARCHAR}
    </select>

    <select id="queryCustListByHboneNo" parameterType="string" resultMap="BaseCustForAnalyseResultMap">
        SELECT C.CONSCUSTNO,
               C.CONSCUSTSTATUS,
               C.IDTYPE,
               C.CUSTNAME,
               C.INVSTTYPE,
               C.HBONE_NO,
               C.IDNO_DIGEST,
               C.IDNO_MASK,
               C.MOBILE_DIGEST,
               C.MOBILE_MASK,
               C.EMAIL_DIGEST,
               C.EMAIL_MASK,
               C.MOBILE_AREA_CODE,
               C.ID_SIGN_AREA_CODE,
               C.NATION_CODE,
               H.HKCUSTID,
               H.HK_TX_ACCT_NO,
               S.CONSCODE
        FROM CM_CONSCUST C
                 LEFT JOIN CM_HK_CONSCUST H ON H.CONSCUSTNO = C.CONSCUSTNO
                 LEFT JOIN CM_CUSTCONSTANT S ON S.CUSTNO = C.CONSCUSTNO
        WHERE C.CONSCUSTSTATUS = '0'
          AND C.HBONE_NO = #{hboneNo,jdbcType=VARCHAR}
    </select>

    <select id="queryCustListByMobile" parameterType="string" resultMap="BaseCustForAnalyseResultMap">
        SELECT C.CONSCUSTNO,
               C.CONSCUSTSTATUS,
               C.IDTYPE,
               C.CUSTNAME,
               C.INVSTTYPE,
               C.HBONE_NO,
               C.IDNO_DIGEST,
               C.IDNO_MASK,
               C.MOBILE_DIGEST,
               C.MOBILE_MASK,
               C.EMAIL_DIGEST,
               C.EMAIL_MASK,
               C.MOBILE_AREA_CODE,
               C.ID_SIGN_AREA_CODE,
               C.NATION_CODE,
               H.HKCUSTID,
               H.HK_TX_ACCT_NO,
               S.CONSCODE
        FROM CM_CONSCUST C
                 LEFT JOIN CM_HK_CONSCUST H ON H.CONSCUSTNO = C.CONSCUSTNO
                 LEFT JOIN CM_CUSTCONSTANT S ON S.CUSTNO = C.CONSCUSTNO
        WHERE C.CONSCUSTSTATUS = '0'
          AND C.INVSTTYPE IN
        <foreach collection="investTypeList" index="index" item="item" open="(" close=")" separator=",">
            #{item}
        </foreach>
        AND C.MOBILE_DIGEST = #{mobileDigest,jdbcType=VARCHAR}
        AND C.MOBILE_AREA_CODE = #{mobileAreaCode,jdbcType=VARCHAR}
    </select>

    <select id="queryCustListByCustNoList" parameterType="list" resultMap="BaseCustForAnalyseResultMap">
        SELECT C.CONSCUSTNO,
               C.CONSCUSTSTATUS,
               C.IDTYPE,
               C.CUSTNAME,
               C.INVSTTYPE,
               C.HBONE_NO,
               C.IDNO_DIGEST,
               C.IDNO_MASK,
               C.MOBILE_DIGEST,
               C.MOBILE_MASK,
               C.EMAIL_DIGEST,
               C.EMAIL_MASK,
               C.MOBILE_AREA_CODE,
               C.ID_SIGN_AREA_CODE,
               C.NATION_CODE,
               H.HKCUSTID,
               H.HK_TX_ACCT_NO,
               S.CONSCODE
        FROM CM_CONSCUST C
                 LEFT JOIN CM_HK_CONSCUST H ON H.CONSCUSTNO = C.CONSCUSTNO
                 LEFT JOIN CM_CUSTCONSTANT S ON S.CUSTNO = C.CONSCUSTNO
        WHERE C.CONSCUSTSTATUS = '0'
          AND C.CONSCUSTNO IN
        <foreach collection="custNoList" index="index" item="item" open="(" close=")" separator=",">
            #{item}
        </foreach>
    </select>


    <select id="queryCustListByIdNo" parameterType="string" resultMap="BaseCustForAnalyseResultMap">
        SELECT C.CONSCUSTNO,
               C.CONSCUSTSTATUS,
               C.IDTYPE,
               C.CUSTNAME,
               C.INVSTTYPE,
               C.HBONE_NO,
               C.IDNO_DIGEST,
               C.IDNO_MASK,
               C.MOBILE_DIGEST,
               C.MOBILE_MASK,
               C.EMAIL_DIGEST,
               C.EMAIL_MASK,
               C.MOBILE_AREA_CODE,
               C.ID_SIGN_AREA_CODE,
               C.NATION_CODE,
               H.HKCUSTID,
               H.HK_TX_ACCT_NO,
               S.CONSCODE
        FROM CM_CONSCUST C
                 LEFT JOIN CM_HK_CONSCUST H ON H.CONSCUSTNO = C.CONSCUSTNO
                 LEFT JOIN CM_CUSTCONSTANT S ON S.CUSTNO = C.CONSCUSTNO
        WHERE C.CONSCUSTSTATUS = '0'
          AND C.INVSTTYPE IN
        <foreach collection="investTypeList" index="index" item="item" open="(" close=")" separator=",">
            #{item}
        </foreach>
        AND C.IDTYPE IN
        <foreach collection="idTypeList" index="index" item="item" open="(" close=")" separator=",">
            #{item}
        </foreach>
        <!--允许 idSignAreaCode 为空，且 不影响重复判断 -->
        <if test="idSignAreaCode != null and idSignAreaCode != ''">
            AND C.ID_SIGN_AREA_CODE = #{idSignAreaCode,jdbcType=VARCHAR}
        </if>
        AND C.IDNO_DIGEST = #{idNoDigest,jdbcType=VARCHAR}
    </select>

    <select id="queryCustListByCustName" parameterType="string" resultMap="BaseCustForAnalyseResultMap">
        SELECT C.CONSCUSTNO,
               C.CONSCUSTSTATUS,
               C.IDTYPE,
               C.CUSTNAME,
               C.INVSTTYPE,
               C.HBONE_NO,
               C.IDNO_DIGEST,
               C.IDNO_MASK,
               C.MOBILE_DIGEST,
               C.MOBILE_MASK,
               C.EMAIL_DIGEST,
               C.EMAIL_MASK,
               C.MOBILE_AREA_CODE,
               C.ID_SIGN_AREA_CODE,
               C.NATION_CODE,
               H.HKCUSTID,
               H.HK_TX_ACCT_NO,
               S.CONSCODE
        FROM CM_CONSCUST C
                 LEFT JOIN CM_HK_CONSCUST H ON H.CONSCUSTNO = C.CONSCUSTNO
                 LEFT JOIN CM_CUSTCONSTANT S ON S.CUSTNO = C.CONSCUSTNO
        WHERE C.CONSCUSTSTATUS = '0'
          AND C.INVSTTYPE IN
        <foreach collection="investTypeList" index="index" item="item" open="(" close=")" separator=",">
            #{item}
        </foreach>
        AND C.CUSTNAME = #{custName,jdbcType=VARCHAR}
    </select>

    <select id="queryCustListByMatchedVo" parameterType="com.howbuy.crm.account.dao.req.custinfo.MatchedCustReqVO"
            resultMap="BaseCustForAnalyseResultMap">
        SELECT C.CONSCUSTNO,
               C.CONSCUSTSTATUS,
               C.IDTYPE,
               C.CUSTNAME,
               C.INVSTTYPE,
               C.HBONE_NO,
               C.IDNO_DIGEST,
               C.IDNO_MASK,
               C.MOBILE_DIGEST,
               C.MOBILE_MASK,
               C.EMAIL_DIGEST,
               C.EMAIL_MASK,
               C.MOBILE_AREA_CODE,
               C.ID_SIGN_AREA_CODE,
               C.NATION_CODE,
               H.HKCUSTID,
               H.HK_TX_ACCT_NO,
               S.CONSCODE
        FROM CM_CONSCUST C
                 LEFT JOIN CM_HK_CONSCUST H ON H.CONSCUSTNO = C.CONSCUSTNO
                 LEFT JOIN CM_CUSTCONSTANT S ON S.CUSTNO = C.CONSCUSTNO
        WHERE C.CONSCUSTSTATUS = '0'
          AND C.INVSTTYPE IN
        <foreach collection="investTypeList" index="index" item="item" open="(" close=")" separator=",">
            #{item}
        </foreach>
        AND C.MOBILE_DIGEST = #{mobileDigest,jdbcType=VARCHAR}
        AND C.MOBILE_AREA_CODE = #{mobileAreaCode,jdbcType=VARCHAR}
        AND C.IDNO_DIGEST = #{idNoDigest,jdbcType=VARCHAR}
        AND C.ID_SIGN_AREA_CODE = #{idSignAreaCode,jdbcType=VARCHAR}
        AND C.IDTYPE = #{idType,jdbcType=VARCHAR}
    </select>

    <select id="queryCustBOByCustNoAndHbone" parameterType="map" resultMap="BaseCustForAnalyseResultMap">
        SELECT C.CONSCUSTNO,
               C.CONSCUSTSTATUS,
               C.IDTYPE,
               C.CUSTNAME,
               C.INVSTTYPE,
               C.HBONE_NO,
               C.IDNO_DIGEST,
               C.IDNO_MASK,
               C.MOBILE_DIGEST,
               C.MOBILE_MASK,
               C.EMAIL_DIGEST,
               C.EMAIL_MASK,
               C.MOBILE_AREA_CODE,
               C.ID_SIGN_AREA_CODE,
               C.NATION_CODE,
               H.HKCUSTID,
               H.HK_TX_ACCT_NO,
               S.CONSCODE
        FROM CM_CONSCUST C
                 LEFT JOIN CM_HK_CONSCUST H ON H.CONSCUSTNO = C.CONSCUSTNO
                 LEFT JOIN CM_CUSTCONSTANT S ON S.CUSTNO = C.CONSCUSTNO
        WHERE C.CONSCUSTSTATUS = '0'
          AND C.CONSCUSTNO = #{custNo,jdbcType=VARCHAR}
          AND C.HBONE_NO = #{hboneNo,jdbcType=VARCHAR}
    </select>

    <select id="queryCustSimpleInfo" parameterType="string"
            resultType="com.howbuy.crm.account.dao.bo.custinfo.CmConsCustSimpleBO">
        SELECT C.CONSCUSTNO        as conscustno,
               C.CONSCUSTSTATUS    as conscuststatus,
               C.IDTYPE            as idtype,
               C.CUSTNAME          as custname,
               C.INVSTTYPE         as invsttype,
               C.HBONE_NO          as hboneNo,
               C.IDNO_DIGEST       as idnoDigest,
               C.IDNO_MASK         as idnoMask,
               C.MOBILE_DIGEST     as mobileDigest,
               C.MOBILE_MASK       as mobileMask,
               C.MOBILE_AREA_CODE  as mobileAreaCode,
               C.ID_SIGN_AREA_CODE as idSignAreaCode,
               C.NATION_CODE       as nationCode,
               C.HBONE_TIMESTAMP   as hboneTimestamp,
               H.HKCUSTID          as hkcustid,
               H.HK_TX_ACCT_NO     as hkTxAcctNo,
               C.REGDT             as regDt,
               S.CONSCODE          as consCode
        FROM CM_CONSCUST C
                 LEFT JOIN CM_HK_CONSCUST H ON H.CONSCUSTNO = C.CONSCUSTNO
                 LEFT JOIN CM_CUSTCONSTANT S ON S.CUSTNO = C.CONSCUSTNO
        WHERE C.CONSCUSTNO = #{custNo,jdbcType=VARCHAR}
    </select>

    <select id="pageQueryCustSimpleByVo" parameterType="com.howbuy.crm.account.dao.req.custinfo.PageCustSimpleReqVO"
            resultType="com.howbuy.crm.account.dao.bo.custinfo.CmConsCustSimpleBO">
        SELECT C.CONSCUSTNO        as conscustno,
               C.CONSCUSTSTATUS    as conscuststatus,
               C.IDTYPE            as idtype,
               C.CUSTNAME          as custname,
               C.INVSTTYPE         as invsttype,
               C.HBONE_NO          as hboneNo,
               C.IDNO_DIGEST       as idnoDigest,
               C.IDNO_MASK         as idnoMask,
               C.MOBILE_DIGEST     as mobileDigest,
               C.MOBILE_MASK       as mobileMask,
               C.MOBILE_AREA_CODE  as mobileAreaCode,
               C.ID_SIGN_AREA_CODE as idSignAreaCode,
               C.NATION_CODE       as nationCode,
               C.HBONE_TIMESTAMP   as hboneTimestamp,
               H.HKCUSTID          as hkcustid,
               H.HK_TX_ACCT_NO     as hkTxAcctNo,
               C.REGDT             as regDt,
               S.CONSCODE          as consCode
        FROM CM_CONSCUST C
                 LEFT JOIN CM_HK_CONSCUST H ON H.CONSCUSTNO = C.CONSCUSTNO
                 LEFT JOIN CM_CUSTCONSTANT S ON S.CUSTNO = C.CONSCUSTNO
        WHERE S.CONSCODE = #{conscode,jdbcType=VARCHAR}
          AND C.CONSCUSTSTATUS = '0'
        <if test="searchContent != null and searchContent != ''">
            AND (C.CUSTNAME LIKE '%' || #{searchContent,jdbcType=VARCHAR} || '%'
                     OR C.CONSCUSTNO LIKE '%' || #{searchContent,jdbcType=VARCHAR} || '%'
                     OR C.MOBILE_MASK LIKE '%' || #{searchContent,jdbcType=VARCHAR} || '%')
        </if>
        order by conscustno
    </select>

    <select id="pageQueryCustSimpleByVoAndOutletCodes"
            parameterType="com.howbuy.crm.account.dao.req.custinfo.PageCustSimpleReqVO"
            resultType="com.howbuy.crm.account.dao.bo.custinfo.CmConsCustSimpleBO">
        SELECT C.CONSCUSTNO        as conscustno,
               C.CONSCUSTSTATUS    as conscuststatus,
               C.IDTYPE            as idtype,
               C.CUSTNAME          as custname,
               C.INVSTTYPE         as invsttype,
               C.HBONE_NO          as hboneNo,
               C.IDNO_DIGEST       as idnoDigest,
               C.IDNO_MASK         as idnoMask,
               C.MOBILE_DIGEST     as mobileDigest,
               C.MOBILE_MASK       as mobileMask,
               C.MOBILE_AREA_CODE  as mobileAreaCode,
               C.ID_SIGN_AREA_CODE as idSignAreaCode,
               C.NATION_CODE       as nationCode,
               C.HBONE_TIMESTAMP   as hboneTimestamp,
               H.HKCUSTID          as hkcustid,
               H.HK_TX_ACCT_NO     as hkTxAcctNo,
               C.REGDT             as regDt,
               S.CONSCODE          as consCode
        FROM CM_CONSCUST C
                 LEFT JOIN CM_HK_CONSCUST H ON H.CONSCUSTNO = C.CONSCUSTNO
                 LEFT JOIN CM_CUSTCONSTANT S ON S.CUSTNO = C.CONSCUSTNO
                 LEFT JOIN CM_CONSULTANT T3 ON T3.CONSCODE = S.CONSCODE
                 LEFT JOIN HB_ORGANIZATION T4 ON T3.OUTLETCODE = T4.ORGCODE
        WHERE C.CONSCUSTSTATUS = '0'
        <if test="searchContent != null and searchContent != ''">
            AND (C.CUSTNAME = #{searchContent,jdbcType=VARCHAR}
                 OR C.CONSCUSTNO = #{searchContent,jdbcType=VARCHAR})
        </if>
        <if test="searchContent == null or searchContent == ''">
            AND S.CONSCODE = #{conscode,jdbcType=VARCHAR}
        </if>
        order by conscustno
    </select>

    <!-- 客户来源 的 baseMap -->
    <resultMap id="BaseSourceResultMap" type="com.howbuy.crm.account.dao.po.custinfo.CmSourceInfoPO">
        <result column="SOURCENO" property="sourceId"/>
        <result column="FIRST_LEVEL_CODE" property="firstLevelCode"/>
        <result column="SECOND_LEVEL_CODE" property="secondLevelCode"/>
        <result column="THIRD_LEVEL_CODE" property="thirdLevelCode"/>
        <result column="FOURTH_LEVEL_CODE" property="fourthLevelCode"/>
        <result column="SOURCENO" property="sourceNo"/>
        <result column="SOURCENAME" property="sourceName"/>
    </resultMap>
    <!--根据来源编号 查找来源信息-->
    <select id="querySourceByCode" parameterType="string" resultMap="BaseSourceResultMap">
        SELECT T.SOURCENO AS SOURCEID,
               T.FIRST_LEVEL_CODE,
               T.SECOND_LEVEL_CODE,
               T.THIRD_LEVEL_CODE,
               T.FOURTH_LEVEL_CODE,
               T.SOURCENO,
               T.SOURCENAME
        FROM CM_SOURCEINFO_NEW T
        WHERE T.REC_STAT = '1'
          AND T.IS_MERGE = '1'
          AND T.SOURCENO = #{sourceCode,jdbcType=VARCHAR}
        UNION
        SELECT T2.SOURCENO AS SOURCEID,
               T.FIRST_LEVEL_CODE,
               T.SECOND_LEVEL_CODE,
               T.THIRD_LEVEL_CODE,
               T.FOURTH_LEVEL_CODE,
               T.SOURCENO,
               T.SOURCENAME
        FROM CM_SOURCEINFO_NEW T
                 JOIN CM_SOURCEINFO T2
                      ON T2.NEWSOURCENO = T.SOURCENO
        WHERE T.REC_STAT = '1'
          AND T.IS_MERGE = '1'
          AND T2.SOURCESTATE = '1'
          AND T2.SOURCENO = #{sourceCode,jdbcType=VARCHAR}
    </select>

    <select id="queryCustSimpleByHboneNo" parameterType="string"
            resultType="com.howbuy.crm.account.dao.bo.custinfo.CmConsCustSimpleBO">
        SELECT C.CONSCUSTNO        as conscustno,
               C.CONSCUSTSTATUS    as conscuststatus,
               C.IDTYPE            as idtype,
               C.CUSTNAME          as custname,
               C.INVSTTYPE         as invsttype,
               C.HBONE_NO          as hboneNo,
               C.IDNO_DIGEST       as idnoDigest,
               C.IDNO_MASK         as idnoMask,
               C.MOBILE_DIGEST     as mobileDigest,
               C.MOBILE_MASK       as mobileMask,
               C.MOBILE_AREA_CODE  as mobileAreaCode,
               C.ID_SIGN_AREA_CODE as idSignAreaCode,
               C.NATION_CODE       as nationCode,
               C.HBONE_TIMESTAMP   as hboneTimestamp,
               H.HKCUSTID          as hkcustid,
               H.HK_TX_ACCT_NO     as hkTxAcctNo,
               C.REGDT             as regDt,
               S.CONSCODE          as consCode
        FROM CM_CONSCUST C
                 LEFT JOIN CM_HK_CONSCUST H ON H.CONSCUSTNO = C.CONSCUSTNO
                 LEFT JOIN CM_CUSTCONSTANT S ON S.CUSTNO = C.CONSCUSTNO
        WHERE C.HBONE_NO = #{hboneNo,jdbcType=VARCHAR}
          AND C.CONSCUSTSTATUS = '0'
    </select>


    <select id="queryCustSimpleBySearchVo" parameterType="com.howbuy.crm.account.dao.req.custinfo.CustSimpleSearchVO"
            resultType="com.howbuy.crm.account.dao.bo.custinfo.CmConsCustSimpleBO">
        SELECT C.CONSCUSTNO        as conscustno,
        C.CONSCUSTSTATUS    as conscuststatus,
        C.IDTYPE            as idtype,
        C.CUSTNAME          as custname,
        C.INVSTTYPE         as invsttype,
        C.HBONE_NO          as hboneNo,
        C.IDNO_DIGEST       as idnoDigest,
        C.IDNO_MASK         as idnoMask,
        C.MOBILE_DIGEST     as mobileDigest,
        C.MOBILE_MASK       as mobileMask,
        C.MOBILE_AREA_CODE  as mobileAreaCode,
        C.ID_SIGN_AREA_CODE as idSignAreaCode,
        C.NATION_CODE       as nationCode,
        C.HBONE_TIMESTAMP   as hboneTimestamp,
        H.HKCUSTID          as hkcustid,
        H.HK_TX_ACCT_NO     as hkTxAcctNo,
        C.REGDT             as regDt,
        S.CONSCODE          as consCode
        FROM CM_CONSCUST C
        LEFT JOIN CM_HK_CONSCUST H ON H.CONSCUSTNO = C.CONSCUSTNO
        LEFT JOIN CM_CUSTCONSTANT S ON S.CUSTNO = C.CONSCUSTNO
        <where>
            AND C.CONSCUSTSTATUS = '0'
            <if test="consCodeList != null and consCodeList.size() != 0">
                AND S.CONSCODE IN
                <foreach collection="consCodeList" item="consCode" open="(" close=")" separator=",">
                    #{consCode}
                </foreach>
            </if>
            <if test="hboneNoList != null and hboneNoList.size() != 0">
                AND ( C.HBONE_NO IN
                <foreach item="item" index="index" collection="hboneNoList" separator=" OR C.HBONE_NO  IN ">
                    <foreach collection="item" item="mId" open="(" separator="," close=")">
                        #{mId}
                    </foreach>
                </foreach>
                )
            </if>
            <if test="hkTxAcctNoList != null and hkTxAcctNoList.size() != 0">
                AND ( H.HK_TX_ACCT_NO IN
                <foreach item="item" index="index" collection="hkTxAcctNoList" separator=" OR H.HK_TX_ACCT_NO  IN ">
                    <foreach collection="item" item="mId" open="(" separator="," close=")">
                        #{mId}
                    </foreach>
                </foreach>
                )
            </if>
            <if test="mobileDigest != null and mobileDigest != ''">
                AND C.MOBILE_DIGEST = #{mobileDigest,jdbcType=VARCHAR}
            </if>
        </where>
    </select>

    <select id="queryCustHisConsultantList" parameterType="java.lang.String"
            resultType="com.howbuy.crm.account.dao.bo.custinfo.CmCustHisConsultantBO">
        SELECT M1.CUSTNO           AS conscustno,
               M1.CONSCODE         AS conscode,
               nvl(M1.STARTDT, '') AS startDt,
               nvl(M1.ENDDT, '')   AS endDt,
               M2.CONSNAME         AS consName,
               M3.CUSTNAME         AS custName
        FROM ((SELECT T1.CUSTNO, T1.CONSCODE, T1.CREATOR, T1.STARTDT, T1.ENDDT
               FROM CM_CUSTCONSTANT T1
               WHERE CUSTNO = #{conscustno,jdbcType=VARCHAR})
              UNION ALL
              (SELECT T2.CUSTNO, T2.CONSCODE, T2.CREATOR, T2.STARTDT, T2.ENDDT
               FROM CM_CUSTCONSTANTHIS T2
               WHERE CUSTNO = #{conscustno,jdbcType=VARCHAR})) M1,
             CM_CONSULTANT M2,
             CM_CONSCUST M3
        WHERE M1.CONSCODE = M2.CONSCODE
          AND M1.CUSTNO = M3.CONSCUSTNO
        ORDER BY M1.STARTDT DESC
    </select>
    
    <select id="queryHisCountByConscustno" parameterType="java.lang.String" resultType="java.lang.String">
        SELECT COUNT(1) rn
        FROM CM_CUSTCONSTANTHIS t
        WHERE CUSTNO = #{conscustno,jdbcType=VARCHAR} and t.conscode != #{consCode,jdbcType=VARCHAR}
    </select>


    <!-- 分页查询初始化数据 -->
    <select id="queryInitData" resultType="com.howbuy.crm.account.dao.bo.consperformancecoeff.ConsPerformanceCoeffBO">
        SELECT * FROM (
            SELECT ROWNUM AS rn, t.* FROM (
                SELECT 
                    cons_cust_no as consCustNo,
                    conscode as consCode,
                    to_timestamp(to_char(binddate, 'yyyy-mm-dd hh24:mi:ss'), 'yyyy-mm-dd hh24:mi:ss')  as assignTime,
                    sourceType as sourceType,
                    custType as custType
                FROM cm_cons_performance_coeff_init
                ORDER BY cons_cust_no
            ) t WHERE ROWNUM &lt;= #{endRow}
        ) WHERE rn > #{startRow}
    </select>

    <!-- 获取初始化数据总数 -->
    <select id="countInitData" resultType="int">
        SELECT COUNT(1) FROM cm_cons_performance_coeff_init
    </select>

</mapper>