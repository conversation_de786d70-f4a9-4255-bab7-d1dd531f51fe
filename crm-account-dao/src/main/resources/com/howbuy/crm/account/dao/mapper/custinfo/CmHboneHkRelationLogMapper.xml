<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.howbuy.crm.account.dao.mapper.custinfo.CmHboneHkRelationLogMapper">
  <resultMap id="BaseResultMap" type="com.howbuy.crm.account.dao.po.custinfo.CmHboneHkRelationLogPO">
    <!--@mbg.generated-->
    <!--@Table CM_HBONE_HK_RELATION_LOG-->
    <id column="ID" jdbcType="VARCHAR" property="id" />
    <result column="CUST_NO" jdbcType="VARCHAR" property="custNo" />
    <result column="RELATION_TYPE" jdbcType="VARCHAR" property="relationType" />
    <result column="OPERATE_TYPE" jdbcType="VARCHAR" property="operateType" />
    <result column="CREATOR" jdbcType="VARCHAR" property="creator" />
    <result column="CREATE_TIMESTAMP" jdbcType="TIMESTAMP" property="createTimestamp" />
    <result column="REMARK" jdbcType="VARCHAR" property="remark" />
    <result column="OPERATE_SOURCE" jdbcType="VARCHAR" property="operateSource" />
    <result column="OPERATE_CHANNEL" jdbcType="VARCHAR" property="operateChannel" />
    <result column="MODIFIER" jdbcType="VARCHAR" property="modifier" />
    <result column="MODIFY_TIMESTAMP" jdbcType="TIMESTAMP" property="modifyTimestamp" />
    <result column="ACCT_NO" jdbcType="VARCHAR" property="acctNo" />
  </resultMap>
  <sql id="Base_Column_List">
    <!--@mbg.generated-->
    ID, CUST_NO, RELATION_TYPE, OPERATE_TYPE, CREATOR, CREATE_TIMESTAMP, REMARK, OPERATE_SOURCE, 
    OPERATE_CHANNEL, MODIFIER, MODIFY_TIMESTAMP, ACCT_NO
  </sql>
  <select id="selectByPrimaryKey" parameterType="java.lang.String" resultMap="BaseResultMap">
    <!--@mbg.generated-->
    select 
    <include refid="Base_Column_List" />
    from CM_HBONE_HK_RELATION_LOG
    where ID = #{id,jdbcType=VARCHAR}
  </select>
  <delete id="deleteByPrimaryKey" parameterType="java.lang.String">
    <!--@mbg.generated-->
    delete from CM_HBONE_HK_RELATION_LOG
    where ID = #{id,jdbcType=VARCHAR}
  </delete>
  <insert id="insert" parameterType="com.howbuy.crm.account.dao.po.custinfo.CmHboneHkRelationLogPO">
    <!--@mbg.generated-->
    insert into CM_HBONE_HK_RELATION_LOG (ID, CUST_NO, RELATION_TYPE, 
      OPERATE_TYPE, CREATOR, CREATE_TIMESTAMP, 
      REMARK, OPERATE_SOURCE, OPERATE_CHANNEL, 
      MODIFIER, MODIFY_TIMESTAMP, ACCT_NO
      )
    values (#{id,jdbcType=VARCHAR}, #{custNo,jdbcType=VARCHAR}, #{relationType,jdbcType=VARCHAR}, 
      #{operateType,jdbcType=VARCHAR}, #{creator,jdbcType=VARCHAR}, #{createTimestamp,jdbcType=TIMESTAMP}, 
      #{remark,jdbcType=VARCHAR}, #{operateSource,jdbcType=VARCHAR}, #{operateChannel,jdbcType=VARCHAR}, 
      #{modifier,jdbcType=VARCHAR}, #{modifyTimestamp,jdbcType=TIMESTAMP}, #{acctNo,jdbcType=VARCHAR}
      )
  </insert>
  <insert id="insertSelective" parameterType="com.howbuy.crm.account.dao.po.custinfo.CmHboneHkRelationLogPO">
    <!--@mbg.generated-->
    insert into CM_HBONE_HK_RELATION_LOG
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="id != null">
        ID,
      </if>
      <if test="custNo != null">
        CUST_NO,
      </if>
      <if test="relationType != null">
        RELATION_TYPE,
      </if>
      <if test="operateType != null">
        OPERATE_TYPE,
      </if>
      <if test="creator != null">
        CREATOR,
      </if>
      <if test="createTimestamp != null">
        CREATE_TIMESTAMP,
      </if>
      <if test="remark != null">
        REMARK,
      </if>
      <if test="operateSource != null">
        OPERATE_SOURCE,
      </if>
      <if test="operateChannel != null">
        OPERATE_CHANNEL,
      </if>
      <if test="modifier != null">
        MODIFIER,
      </if>
      <if test="modifyTimestamp != null">
        MODIFY_TIMESTAMP,
      </if>
      <if test="acctNo != null">
        ACCT_NO,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="id != null">
        #{id,jdbcType=VARCHAR},
      </if>
      <if test="custNo != null">
        #{custNo,jdbcType=VARCHAR},
      </if>
      <if test="relationType != null">
        #{relationType,jdbcType=VARCHAR},
      </if>
      <if test="operateType != null">
        #{operateType,jdbcType=VARCHAR},
      </if>
      <if test="creator != null">
        #{creator,jdbcType=VARCHAR},
      </if>
      <if test="createTimestamp != null">
        #{createTimestamp,jdbcType=TIMESTAMP},
      </if>
      <if test="remark != null">
        #{remark,jdbcType=VARCHAR},
      </if>
      <if test="operateSource != null">
        #{operateSource,jdbcType=VARCHAR},
      </if>
      <if test="operateChannel != null">
        #{operateChannel,jdbcType=VARCHAR},
      </if>
      <if test="modifier != null">
        #{modifier,jdbcType=VARCHAR},
      </if>
      <if test="modifyTimestamp != null">
        #{modifyTimestamp,jdbcType=TIMESTAMP},
      </if>
      <if test="acctNo != null">
        #{acctNo,jdbcType=VARCHAR},
      </if>
    </trim>
  </insert>
  <update id="updateByPrimaryKeySelective" parameterType="com.howbuy.crm.account.dao.po.custinfo.CmHboneHkRelationLogPO">
    <!--@mbg.generated-->
    update CM_HBONE_HK_RELATION_LOG
    <set>
      <if test="custNo != null">
        CUST_NO = #{custNo,jdbcType=VARCHAR},
      </if>
      <if test="relationType != null">
        RELATION_TYPE = #{relationType,jdbcType=VARCHAR},
      </if>
      <if test="operateType != null">
        OPERATE_TYPE = #{operateType,jdbcType=VARCHAR},
      </if>
      <if test="creator != null">
        CREATOR = #{creator,jdbcType=VARCHAR},
      </if>
      <if test="createTimestamp != null">
        CREATE_TIMESTAMP = #{createTimestamp,jdbcType=TIMESTAMP},
      </if>
      <if test="remark != null">
        REMARK = #{remark,jdbcType=VARCHAR},
      </if>
      <if test="operateSource != null">
        OPERATE_SOURCE = #{operateSource,jdbcType=VARCHAR},
      </if>
      <if test="operateChannel != null">
        OPERATE_CHANNEL = #{operateChannel,jdbcType=VARCHAR},
      </if>
      <if test="modifier != null">
        MODIFIER = #{modifier,jdbcType=VARCHAR},
      </if>
      <if test="modifyTimestamp != null">
        MODIFY_TIMESTAMP = #{modifyTimestamp,jdbcType=TIMESTAMP},
      </if>
      <if test="acctNo != null">
        ACCT_NO = #{acctNo,jdbcType=VARCHAR},
      </if>
    </set>
    where ID = #{id,jdbcType=VARCHAR}
  </update>
  <update id="updateByPrimaryKey" parameterType="com.howbuy.crm.account.dao.po.custinfo.CmHboneHkRelationLogPO">
    <!--@mbg.generated-->
    update CM_HBONE_HK_RELATION_LOG
    set CUST_NO = #{custNo,jdbcType=VARCHAR},
      RELATION_TYPE = #{relationType,jdbcType=VARCHAR},
      OPERATE_TYPE = #{operateType,jdbcType=VARCHAR},
      CREATOR = #{creator,jdbcType=VARCHAR},
      CREATE_TIMESTAMP = #{createTimestamp,jdbcType=TIMESTAMP},
      REMARK = #{remark,jdbcType=VARCHAR},
      OPERATE_SOURCE = #{operateSource,jdbcType=VARCHAR},
      OPERATE_CHANNEL = #{operateChannel,jdbcType=VARCHAR},
      MODIFIER = #{modifier,jdbcType=VARCHAR},
      MODIFY_TIMESTAMP = #{modifyTimestamp,jdbcType=TIMESTAMP},
      ACCT_NO = #{acctNo,jdbcType=VARCHAR}
    where ID = #{id,jdbcType=VARCHAR}
  </update>
</mapper>