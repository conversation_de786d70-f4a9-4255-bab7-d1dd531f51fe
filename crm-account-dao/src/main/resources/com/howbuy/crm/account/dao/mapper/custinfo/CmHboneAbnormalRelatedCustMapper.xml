<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.howbuy.crm.account.dao.mapper.custinfo.CmHboneAbnormalRelatedCustMapper">
  <resultMap id="BaseResultMap" type="com.howbuy.crm.account.dao.po.custinfo.CmHboneAbnormalRelatedCustPO">
    <!--@mbg.generated-->
    <!--@Table CM_HBONE_ABNORMAL_RELATED_CUST-->
    <id column="ID" jdbcType="VARCHAR" property="id" />
    <result column="ABNORMAL_ID" jdbcType="VARCHAR" property="abnormalId" />
    <result column="CUST_NO" jdbcType="VARCHAR" property="custNo" />
    <result column="CUST_NAME" jdbcType="VARCHAR" property="custName" />
    <result column="INVEST_TYPE" jdbcType="VARCHAR" property="investType" />
    <result column="MOBILE_AREA_CODE" jdbcType="VARCHAR" property="mobileAreaCode" />
    <result column="MOBILE_DIGEST" jdbcType="VARCHAR" property="mobileDigest" />
    <result column="MOBILE_MASK" jdbcType="VARCHAR" property="mobileMask" />
    <result column="MOBILE_CIPHER" jdbcType="VARCHAR" property="mobileCipher" />
    <result column="ID_SIGN_AREA_CODE" jdbcType="VARCHAR" property="idSignAreaCode" />
    <result column="ID_TYPE" jdbcType="VARCHAR" property="idType" />
    <result column="ID_NO_DIGEST" jdbcType="VARCHAR" property="idNoDigest" />
    <result column="ID_NO_MASK" jdbcType="VARCHAR" property="idNoMask" />
    <result column="ID_NO_CIPHER" jdbcType="VARCHAR" property="idNoCipher" />
    <result column="HBONE_NO" jdbcType="VARCHAR" property="hboneNo" />
    <result column="HK_TX_ACCT_NO" jdbcType="VARCHAR" property="hkTxAcctNo" />
    <result column="CONS_CODE" jdbcType="VARCHAR" property="consCode" />
    <result column="CREATOR" jdbcType="VARCHAR" property="creator" />
    <result column="CREATE_TIMESTAMP" jdbcType="TIMESTAMP" property="createTimestamp" />
    <result column="MODIFIER" jdbcType="VARCHAR" property="modifier" />
    <result column="MODIFY_TIMESTAMP" jdbcType="TIMESTAMP" property="modifyTimestamp" />
  </resultMap>
  <sql id="Base_Column_List">
    <!--@mbg.generated-->
    ID, ABNORMAL_ID, CUST_NO, CUST_NAME, INVEST_TYPE, MOBILE_AREA_CODE, MOBILE_DIGEST, 
    MOBILE_MASK, MOBILE_CIPHER, ID_SIGN_AREA_CODE, ID_TYPE, ID_NO_DIGEST, ID_NO_MASK, 
    ID_NO_CIPHER, HBONE_NO, HK_TX_ACCT_NO, CONS_CODE, CREATOR, CREATE_TIMESTAMP, MODIFIER, 
    MODIFY_TIMESTAMP
  </sql>
  <select id="selectByPrimaryKey" parameterType="java.lang.String" resultMap="BaseResultMap">
    <!--@mbg.generated-->
    select 
    <include refid="Base_Column_List" />
    from CM_HBONE_ABNORMAL_RELATED_CUST
    where ID = #{id,jdbcType=VARCHAR}
  </select>
  <delete id="deleteByPrimaryKey" parameterType="java.lang.String">
    <!--@mbg.generated-->
    delete from CM_HBONE_ABNORMAL_RELATED_CUST
    where ID = #{id,jdbcType=VARCHAR}
  </delete>
  <insert id="insert" parameterType="com.howbuy.crm.account.dao.po.custinfo.CmHboneAbnormalRelatedCustPO">
    <!--@mbg.generated-->
    insert into CM_HBONE_ABNORMAL_RELATED_CUST (ID, ABNORMAL_ID, CUST_NO, 
      CUST_NAME, INVEST_TYPE, MOBILE_AREA_CODE, 
      MOBILE_DIGEST, MOBILE_MASK, MOBILE_CIPHER, 
      ID_SIGN_AREA_CODE, ID_TYPE, ID_NO_DIGEST, 
      ID_NO_MASK, ID_NO_CIPHER, HBONE_NO, 
      HK_TX_ACCT_NO, CONS_CODE, CREATOR, 
      CREATE_TIMESTAMP, MODIFIER, MODIFY_TIMESTAMP
      )
    values (#{id,jdbcType=VARCHAR}, #{abnormalId,jdbcType=VARCHAR}, #{custNo,jdbcType=VARCHAR}, 
      #{custName,jdbcType=VARCHAR}, #{investType,jdbcType=VARCHAR}, #{mobileAreaCode,jdbcType=VARCHAR}, 
      #{mobileDigest,jdbcType=VARCHAR}, #{mobileMask,jdbcType=VARCHAR}, #{mobileCipher,jdbcType=VARCHAR}, 
      #{idSignAreaCode,jdbcType=VARCHAR}, #{idType,jdbcType=VARCHAR}, #{idNoDigest,jdbcType=VARCHAR}, 
      #{idNoMask,jdbcType=VARCHAR}, #{idNoCipher,jdbcType=VARCHAR}, #{hboneNo,jdbcType=VARCHAR}, 
      #{hkTxAcctNo,jdbcType=VARCHAR}, #{consCode,jdbcType=VARCHAR}, #{creator,jdbcType=VARCHAR}, 
      #{createTimestamp,jdbcType=TIMESTAMP}, #{modifier,jdbcType=VARCHAR}, #{modifyTimestamp,jdbcType=TIMESTAMP}
      )
  </insert>

  <insert id="batchInsert"  parameterType="java.util.List">
    <!--@mbg.generated-->
    begin
    <foreach collection="recordList" item="row" index="index" separator=";">
      insert into CM_HBONE_ABNORMAL_RELATED_CUST (ID, ABNORMAL_ID, CUST_NO,
      CUST_NAME, INVEST_TYPE, MOBILE_AREA_CODE,
      MOBILE_DIGEST, MOBILE_MASK, MOBILE_CIPHER,
      ID_SIGN_AREA_CODE, ID_TYPE, ID_NO_DIGEST,
      ID_NO_MASK, ID_NO_CIPHER, HBONE_NO,
      HK_TX_ACCT_NO, CONS_CODE, CREATOR,
      CREATE_TIMESTAMP, MODIFIER, MODIFY_TIMESTAMP
      )
      values (#{row.id,jdbcType=VARCHAR}, #{row.abnormalId,jdbcType=VARCHAR}, #{row.custNo,jdbcType=VARCHAR},
      #{row.custName,jdbcType=VARCHAR}, #{row.investType,jdbcType=VARCHAR}, #{row.mobileAreaCode,jdbcType=VARCHAR},
      #{row.mobileDigest,jdbcType=VARCHAR}, #{row.mobileMask,jdbcType=VARCHAR}, #{row.mobileCipher,jdbcType=VARCHAR},
      #{row.idSignAreaCode,jdbcType=VARCHAR}, #{row.idType,jdbcType=VARCHAR}, #{row.idNoDigest,jdbcType=VARCHAR},
      #{row.idNoMask,jdbcType=VARCHAR}, #{row.idNoCipher,jdbcType=VARCHAR}, #{row.hboneNo,jdbcType=VARCHAR},
      #{row.hkTxAcctNo,jdbcType=VARCHAR}, #{row.consCode,jdbcType=VARCHAR}, #{row.creator,jdbcType=VARCHAR},
      #{row.createTimestamp,jdbcType=TIMESTAMP}, #{row.modifier,jdbcType=VARCHAR}, #{row.modifyTimestamp,jdbcType=TIMESTAMP}
      )
    </foreach>
    ;end;
  </insert>
  <insert id="insertSelective" parameterType="com.howbuy.crm.account.dao.po.custinfo.CmHboneAbnormalRelatedCustPO">
    <!--@mbg.generated-->
    insert into CM_HBONE_ABNORMAL_RELATED_CUST
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="id != null">
        ID,
      </if>
      <if test="abnormalId != null">
        ABNORMAL_ID,
      </if>
      <if test="custNo != null">
        CUST_NO,
      </if>
      <if test="custName != null">
        CUST_NAME,
      </if>
      <if test="investType != null">
        INVEST_TYPE,
      </if>
      <if test="mobileAreaCode != null">
        MOBILE_AREA_CODE,
      </if>
      <if test="mobileDigest != null">
        MOBILE_DIGEST,
      </if>
      <if test="mobileMask != null">
        MOBILE_MASK,
      </if>
      <if test="mobileCipher != null">
        MOBILE_CIPHER,
      </if>
      <if test="idSignAreaCode != null">
        ID_SIGN_AREA_CODE,
      </if>
      <if test="idType != null">
        ID_TYPE,
      </if>
      <if test="idNoDigest != null">
        ID_NO_DIGEST,
      </if>
      <if test="idNoMask != null">
        ID_NO_MASK,
      </if>
      <if test="idNoCipher != null">
        ID_NO_CIPHER,
      </if>
      <if test="hboneNo != null">
        HBONE_NO,
      </if>
      <if test="hkTxAcctNo != null">
        HK_TX_ACCT_NO,
      </if>
      <if test="consCode != null">
        CONS_CODE,
      </if>
      <if test="creator != null">
        CREATOR,
      </if>
      <if test="createTimestamp != null">
        CREATE_TIMESTAMP,
      </if>
      <if test="modifier != null">
        MODIFIER,
      </if>
      <if test="modifyTimestamp != null">
        MODIFY_TIMESTAMP,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="id != null">
        #{id,jdbcType=VARCHAR},
      </if>
      <if test="abnormalId != null">
        #{abnormalId,jdbcType=VARCHAR},
      </if>
      <if test="custNo != null">
        #{custNo,jdbcType=VARCHAR},
      </if>
      <if test="custName != null">
        #{custName,jdbcType=VARCHAR},
      </if>
      <if test="investType != null">
        #{investType,jdbcType=VARCHAR},
      </if>
      <if test="mobileAreaCode != null">
        #{mobileAreaCode,jdbcType=VARCHAR},
      </if>
      <if test="mobileDigest != null">
        #{mobileDigest,jdbcType=VARCHAR},
      </if>
      <if test="mobileMask != null">
        #{mobileMask,jdbcType=VARCHAR},
      </if>
      <if test="mobileCipher != null">
        #{mobileCipher,jdbcType=VARCHAR},
      </if>
      <if test="idSignAreaCode != null">
        #{idSignAreaCode,jdbcType=VARCHAR},
      </if>
      <if test="idType != null">
        #{idType,jdbcType=VARCHAR},
      </if>
      <if test="idNoDigest != null">
        #{idNoDigest,jdbcType=VARCHAR},
      </if>
      <if test="idNoMask != null">
        #{idNoMask,jdbcType=VARCHAR},
      </if>
      <if test="idNoCipher != null">
        #{idNoCipher,jdbcType=VARCHAR},
      </if>
      <if test="hboneNo != null">
        #{hboneNo,jdbcType=VARCHAR},
      </if>
      <if test="hkTxAcctNo != null">
        #{hkTxAcctNo,jdbcType=VARCHAR},
      </if>
      <if test="consCode != null">
        #{consCode,jdbcType=VARCHAR},
      </if>
      <if test="creator != null">
        #{creator,jdbcType=VARCHAR},
      </if>
      <if test="createTimestamp != null">
        #{createTimestamp,jdbcType=TIMESTAMP},
      </if>
      <if test="modifier != null">
        #{modifier,jdbcType=VARCHAR},
      </if>
      <if test="modifyTimestamp != null">
        #{modifyTimestamp,jdbcType=TIMESTAMP},
      </if>
    </trim>
  </insert>
  <update id="updateByPrimaryKeySelective" parameterType="com.howbuy.crm.account.dao.po.custinfo.CmHboneAbnormalRelatedCustPO">
    <!--@mbg.generated-->
    update CM_HBONE_ABNORMAL_RELATED_CUST
    <set>
      <if test="abnormalId != null">
        ABNORMAL_ID = #{abnormalId,jdbcType=VARCHAR},
      </if>
      <if test="custNo != null">
        CUST_NO = #{custNo,jdbcType=VARCHAR},
      </if>
      <if test="custName != null">
        CUST_NAME = #{custName,jdbcType=VARCHAR},
      </if>
      <if test="investType != null">
        INVEST_TYPE = #{investType,jdbcType=VARCHAR},
      </if>
      <if test="mobileAreaCode != null">
        MOBILE_AREA_CODE = #{mobileAreaCode,jdbcType=VARCHAR},
      </if>
      <if test="mobileDigest != null">
        MOBILE_DIGEST = #{mobileDigest,jdbcType=VARCHAR},
      </if>
      <if test="mobileMask != null">
        MOBILE_MASK = #{mobileMask,jdbcType=VARCHAR},
      </if>
      <if test="mobileCipher != null">
        MOBILE_CIPHER = #{mobileCipher,jdbcType=VARCHAR},
      </if>
      <if test="idSignAreaCode != null">
        ID_SIGN_AREA_CODE = #{idSignAreaCode,jdbcType=VARCHAR},
      </if>
      <if test="idType != null">
        ID_TYPE = #{idType,jdbcType=VARCHAR},
      </if>
      <if test="idNoDigest != null">
        ID_NO_DIGEST = #{idNoDigest,jdbcType=VARCHAR},
      </if>
      <if test="idNoMask != null">
        ID_NO_MASK = #{idNoMask,jdbcType=VARCHAR},
      </if>
      <if test="idNoCipher != null">
        ID_NO_CIPHER = #{idNoCipher,jdbcType=VARCHAR},
      </if>
      <if test="hboneNo != null">
        HBONE_NO = #{hboneNo,jdbcType=VARCHAR},
      </if>
      <if test="hkTxAcctNo != null">
        HK_TX_ACCT_NO = #{hkTxAcctNo,jdbcType=VARCHAR},
      </if>
      <if test="consCode != null">
        CONS_CODE = #{consCode,jdbcType=VARCHAR},
      </if>
      <if test="creator != null">
        CREATOR = #{creator,jdbcType=VARCHAR},
      </if>
      <if test="createTimestamp != null">
        CREATE_TIMESTAMP = #{createTimestamp,jdbcType=TIMESTAMP},
      </if>
      <if test="modifier != null">
        MODIFIER = #{modifier,jdbcType=VARCHAR},
      </if>
      <if test="modifyTimestamp != null">
        MODIFY_TIMESTAMP = #{modifyTimestamp,jdbcType=TIMESTAMP},
      </if>
    </set>
    where ID = #{id,jdbcType=VARCHAR}
  </update>
  <update id="updateByPrimaryKey" parameterType="com.howbuy.crm.account.dao.po.custinfo.CmHboneAbnormalRelatedCustPO">
    <!--@mbg.generated-->
    update CM_HBONE_ABNORMAL_RELATED_CUST
    set ABNORMAL_ID = #{abnormalId,jdbcType=VARCHAR},
      CUST_NO = #{custNo,jdbcType=VARCHAR},
      CUST_NAME = #{custName,jdbcType=VARCHAR},
      INVEST_TYPE = #{investType,jdbcType=VARCHAR},
      MOBILE_AREA_CODE = #{mobileAreaCode,jdbcType=VARCHAR},
      MOBILE_DIGEST = #{mobileDigest,jdbcType=VARCHAR},
      MOBILE_MASK = #{mobileMask,jdbcType=VARCHAR},
      MOBILE_CIPHER = #{mobileCipher,jdbcType=VARCHAR},
      ID_SIGN_AREA_CODE = #{idSignAreaCode,jdbcType=VARCHAR},
      ID_TYPE = #{idType,jdbcType=VARCHAR},
      ID_NO_DIGEST = #{idNoDigest,jdbcType=VARCHAR},
      ID_NO_MASK = #{idNoMask,jdbcType=VARCHAR},
      ID_NO_CIPHER = #{idNoCipher,jdbcType=VARCHAR},
      HBONE_NO = #{hboneNo,jdbcType=VARCHAR},
      HK_TX_ACCT_NO = #{hkTxAcctNo,jdbcType=VARCHAR},
      CONS_CODE = #{consCode,jdbcType=VARCHAR},
      CREATOR = #{creator,jdbcType=VARCHAR},
      CREATE_TIMESTAMP = #{createTimestamp,jdbcType=TIMESTAMP},
      MODIFIER = #{modifier,jdbcType=VARCHAR},
      MODIFY_TIMESTAMP = #{modifyTimestamp,jdbcType=TIMESTAMP}
    where ID = #{id,jdbcType=VARCHAR}
  </update>
</mapper>