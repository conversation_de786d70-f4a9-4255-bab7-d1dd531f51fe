<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.howbuy.crm.account.dao.mapper.consultant.CmConsultantMapper">
  <resultMap id="BaseResultMap" type="com.howbuy.crm.account.dao.po.consultant.CmConsultantPO">
    <!--@mbg.generated-->
    <!--@Table CM_CONSULTANT-->
    <id column="CONSCODE" jdbcType="VARCHAR" property="conscode" />
    <result column="CONSNAME" jdbcType="VARCHAR" property="consname" />
    <result column="CONSLEVEL" jdbcType="VARCHAR" property="conslevel" />
    <result column="TEAMCODE" jdbcType="VARCHAR" property="teamcode" />
    <result column="CHARACTER" jdbcType="VARCHAR" property="character" />
    <result column="CONSSTATUS" jdbcType="VARCHAR" property="consstatus" />
    <result column="RECSTAT" jdbcType="VARCHAR" property="recstat" />
    <result column="CHECKFLAG" jdbcType="VARCHAR" property="checkflag" />
    <result column="CREATOR" jdbcType="VARCHAR" property="creator" />
    <result column="MODIFIER" jdbcType="VARCHAR" property="modifier" />
    <result column="CHECKER" jdbcType="VARCHAR" property="checker" />
    <result column="CREDT" jdbcType="VARCHAR" property="credt" />
    <result column="MODDT" jdbcType="VARCHAR" property="moddt" />
    <result column="TELNO" jdbcType="VARCHAR" property="telno" />
    <result column="MOBILE" jdbcType="VARCHAR" property="mobile" />
    <result column="DEPTCODE" jdbcType="VARCHAR" property="deptcode" />
    <result column="OUTLETCODE" jdbcType="VARCHAR" property="outletcode" />
    <result column="PICTUREURL" jdbcType="VARCHAR" property="pictureurl" />
    <result column="EMAIL" jdbcType="VARCHAR" property="email" />
    <result column="RESUME" jdbcType="CLOB" property="resume" />
    <result column="STARTDT" jdbcType="VARCHAR" property="startdt" />
    <result column="ENDDT" jdbcType="VARCHAR" property="enddt" />
    <result column="EMPCARDNO" jdbcType="VARCHAR" property="empcardno" />
    <result column="LOGINFLAG" jdbcType="VARCHAR" property="loginflag" />
    <result column="ISSENIORMGR" jdbcType="VARCHAR" property="isseniormgr" />
    <result column="WORKPLACE" jdbcType="VARCHAR" property="workplace" />
    <result column="DIALCHANNEL" jdbcType="VARCHAR" property="dialchannel" />
    <result column="IP" jdbcType="VARCHAR" property="ip" />
    <result column="SALARYAMTBEFORE" jdbcType="DECIMAL" property="salaryamtbefore" />
    <result column="ISVIRTUAL" jdbcType="VARCHAR" property="isvirtual" />
    <result column="ISINSIDE" jdbcType="VARCHAR" property="isinside" />
    <result column="PICADDR" jdbcType="VARCHAR" property="picaddr" />
    <result column="PICADDR1" jdbcType="VARCHAR" property="picaddr1" />
    <result column="CHARACTER1" jdbcType="VARCHAR" property="character1" />
    <result column="POSITION" jdbcType="VARCHAR" property="position" />
    <result column="OWNERTYPE" jdbcType="VARCHAR" property="ownertype" />
    <result column="CODEPICADDR" jdbcType="VARCHAR" property="codepicaddr" />
    <result column="MOBILE_DIGEST" jdbcType="VARCHAR" property="mobileDigest" />
    <result column="AGENTNO" jdbcType="VARCHAR" property="agentno" />
    <result column="WECHATCONSCODE" jdbcType="VARCHAR" property="wechatconscode" />
  </resultMap>
  <sql id="Base_Column_List">
    <!--@mbg.generated-->
    CONSCODE, CONSNAME, CONSLEVEL, TEAMCODE, "CHARACTER", CONSSTATUS, RECSTAT, CHECKFLAG, 
    CREATOR, MODIFIER, CHECKER, CREDT, MODDT, TELNO, MOBILE, DEPTCODE, OUTLETCODE, PICTUREURL, 
    EMAIL, RESUME, STARTDT, ENDDT, EMPCARDNO, LOGINFLAG, ISSENIORMGR, WORKPLACE, DIALCHANNEL, 
    IP, SALARYAMTBEFORE, ISVIRTUAL, ISINSIDE, PICADDR, PICADDR1, CHARACTER1, "POSITION", 
    OWNERTYPE, CODEPICADDR, MOBILE_DIGEST, AGENTNO, WECHATCONSCODE
  </sql>
  <select id="selectByPrimaryKey" parameterType="java.lang.String" resultMap="BaseResultMap">
    <!--@mbg.generated-->
    select 
    <include refid="Base_Column_List" />
    from CM_CONSULTANT
    where CONSCODE = #{conscode,jdbcType=VARCHAR}
  </select>
  <delete id="deleteByPrimaryKey" parameterType="java.lang.String">
    <!--@mbg.generated-->
    delete from CM_CONSULTANT
    where CONSCODE = #{conscode,jdbcType=VARCHAR}
  </delete>
  <insert id="insert" parameterType="com.howbuy.crm.account.dao.po.consultant.CmConsultantPO">
    <!--@mbg.generated-->
    insert into CM_CONSULTANT (CONSCODE, CONSNAME, CONSLEVEL, 
      TEAMCODE, "CHARACTER", CONSSTATUS, 
      RECSTAT, CHECKFLAG, CREATOR, 
      MODIFIER, CHECKER, CREDT, 
      MODDT, TELNO, MOBILE, 
      DEPTCODE, OUTLETCODE, PICTUREURL, 
      EMAIL, RESUME, STARTDT, 
      ENDDT, EMPCARDNO, LOGINFLAG, 
      ISSENIORMGR, WORKPLACE, DIALCHANNEL, 
      IP, SALARYAMTBEFORE, ISVIRTUAL, 
      ISINSIDE, PICADDR, PICADDR1, 
      CHARACTER1, "POSITION", OWNERTYPE, 
      CODEPICADDR, MOBILE_DIGEST, AGENTNO, 
      WECHATCONSCODE)
    values (#{conscode,jdbcType=VARCHAR}, #{consname,jdbcType=VARCHAR}, #{conslevel,jdbcType=VARCHAR}, 
      #{teamcode,jdbcType=VARCHAR}, #{character,jdbcType=VARCHAR}, #{consstatus,jdbcType=VARCHAR}, 
      #{recstat,jdbcType=VARCHAR}, #{checkflag,jdbcType=VARCHAR}, #{creator,jdbcType=VARCHAR}, 
      #{modifier,jdbcType=VARCHAR}, #{checker,jdbcType=VARCHAR}, #{credt,jdbcType=VARCHAR}, 
      #{moddt,jdbcType=VARCHAR}, #{telno,jdbcType=VARCHAR}, #{mobile,jdbcType=VARCHAR}, 
      #{deptcode,jdbcType=VARCHAR}, #{outletcode,jdbcType=VARCHAR}, #{pictureurl,jdbcType=VARCHAR}, 
      #{email,jdbcType=VARCHAR}, #{resume,jdbcType=CLOB}, #{startdt,jdbcType=VARCHAR}, 
      #{enddt,jdbcType=VARCHAR}, #{empcardno,jdbcType=VARCHAR}, #{loginflag,jdbcType=VARCHAR}, 
      #{isseniormgr,jdbcType=VARCHAR}, #{workplace,jdbcType=VARCHAR}, #{dialchannel,jdbcType=VARCHAR}, 
      #{ip,jdbcType=VARCHAR}, #{salaryamtbefore,jdbcType=DECIMAL}, #{isvirtual,jdbcType=VARCHAR}, 
      #{isinside,jdbcType=VARCHAR}, #{picaddr,jdbcType=VARCHAR}, #{picaddr1,jdbcType=VARCHAR}, 
      #{character1,jdbcType=VARCHAR}, #{position,jdbcType=VARCHAR}, #{ownertype,jdbcType=VARCHAR}, 
      #{codepicaddr,jdbcType=VARCHAR}, #{mobileDigest,jdbcType=VARCHAR}, #{agentno,jdbcType=VARCHAR}, 
      #{wechatconscode,jdbcType=VARCHAR})
  </insert>
  <insert id="insertSelective" parameterType="com.howbuy.crm.account.dao.po.consultant.CmConsultantPO">
    <!--@mbg.generated-->
    insert into CM_CONSULTANT
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="conscode != null">
        CONSCODE,
      </if>
      <if test="consname != null">
        CONSNAME,
      </if>
      <if test="conslevel != null">
        CONSLEVEL,
      </if>
      <if test="teamcode != null">
        TEAMCODE,
      </if>
      <if test="character != null">
        "CHARACTER",
      </if>
      <if test="consstatus != null">
        CONSSTATUS,
      </if>
      <if test="recstat != null">
        RECSTAT,
      </if>
      <if test="checkflag != null">
        CHECKFLAG,
      </if>
      <if test="creator != null">
        CREATOR,
      </if>
      <if test="modifier != null">
        MODIFIER,
      </if>
      <if test="checker != null">
        CHECKER,
      </if>
      <if test="credt != null">
        CREDT,
      </if>
      <if test="moddt != null">
        MODDT,
      </if>
      <if test="telno != null">
        TELNO,
      </if>
      <if test="mobile != null">
        MOBILE,
      </if>
      <if test="deptcode != null">
        DEPTCODE,
      </if>
      <if test="outletcode != null">
        OUTLETCODE,
      </if>
      <if test="pictureurl != null">
        PICTUREURL,
      </if>
      <if test="email != null">
        EMAIL,
      </if>
      <if test="resume != null">
        RESUME,
      </if>
      <if test="startdt != null">
        STARTDT,
      </if>
      <if test="enddt != null">
        ENDDT,
      </if>
      <if test="empcardno != null">
        EMPCARDNO,
      </if>
      <if test="loginflag != null">
        LOGINFLAG,
      </if>
      <if test="isseniormgr != null">
        ISSENIORMGR,
      </if>
      <if test="workplace != null">
        WORKPLACE,
      </if>
      <if test="dialchannel != null">
        DIALCHANNEL,
      </if>
      <if test="ip != null">
        IP,
      </if>
      <if test="salaryamtbefore != null">
        SALARYAMTBEFORE,
      </if>
      <if test="isvirtual != null">
        ISVIRTUAL,
      </if>
      <if test="isinside != null">
        ISINSIDE,
      </if>
      <if test="picaddr != null">
        PICADDR,
      </if>
      <if test="picaddr1 != null">
        PICADDR1,
      </if>
      <if test="character1 != null">
        CHARACTER1,
      </if>
      <if test="position != null">
        "POSITION",
      </if>
      <if test="ownertype != null">
        OWNERTYPE,
      </if>
      <if test="codepicaddr != null">
        CODEPICADDR,
      </if>
      <if test="mobileDigest != null">
        MOBILE_DIGEST,
      </if>
      <if test="agentno != null">
        AGENTNO,
      </if>
      <if test="wechatconscode != null">
        WECHATCONSCODE,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="conscode != null">
        #{conscode,jdbcType=VARCHAR},
      </if>
      <if test="consname != null">
        #{consname,jdbcType=VARCHAR},
      </if>
      <if test="conslevel != null">
        #{conslevel,jdbcType=VARCHAR},
      </if>
      <if test="teamcode != null">
        #{teamcode,jdbcType=VARCHAR},
      </if>
      <if test="character != null">
        #{character,jdbcType=VARCHAR},
      </if>
      <if test="consstatus != null">
        #{consstatus,jdbcType=VARCHAR},
      </if>
      <if test="recstat != null">
        #{recstat,jdbcType=VARCHAR},
      </if>
      <if test="checkflag != null">
        #{checkflag,jdbcType=VARCHAR},
      </if>
      <if test="creator != null">
        #{creator,jdbcType=VARCHAR},
      </if>
      <if test="modifier != null">
        #{modifier,jdbcType=VARCHAR},
      </if>
      <if test="checker != null">
        #{checker,jdbcType=VARCHAR},
      </if>
      <if test="credt != null">
        #{credt,jdbcType=VARCHAR},
      </if>
      <if test="moddt != null">
        #{moddt,jdbcType=VARCHAR},
      </if>
      <if test="telno != null">
        #{telno,jdbcType=VARCHAR},
      </if>
      <if test="mobile != null">
        #{mobile,jdbcType=VARCHAR},
      </if>
      <if test="deptcode != null">
        #{deptcode,jdbcType=VARCHAR},
      </if>
      <if test="outletcode != null">
        #{outletcode,jdbcType=VARCHAR},
      </if>
      <if test="pictureurl != null">
        #{pictureurl,jdbcType=VARCHAR},
      </if>
      <if test="email != null">
        #{email,jdbcType=VARCHAR},
      </if>
      <if test="resume != null">
        #{resume,jdbcType=CLOB},
      </if>
      <if test="startdt != null">
        #{startdt,jdbcType=VARCHAR},
      </if>
      <if test="enddt != null">
        #{enddt,jdbcType=VARCHAR},
      </if>
      <if test="empcardno != null">
        #{empcardno,jdbcType=VARCHAR},
      </if>
      <if test="loginflag != null">
        #{loginflag,jdbcType=VARCHAR},
      </if>
      <if test="isseniormgr != null">
        #{isseniormgr,jdbcType=VARCHAR},
      </if>
      <if test="workplace != null">
        #{workplace,jdbcType=VARCHAR},
      </if>
      <if test="dialchannel != null">
        #{dialchannel,jdbcType=VARCHAR},
      </if>
      <if test="ip != null">
        #{ip,jdbcType=VARCHAR},
      </if>
      <if test="salaryamtbefore != null">
        #{salaryamtbefore,jdbcType=DECIMAL},
      </if>
      <if test="isvirtual != null">
        #{isvirtual,jdbcType=VARCHAR},
      </if>
      <if test="isinside != null">
        #{isinside,jdbcType=VARCHAR},
      </if>
      <if test="picaddr != null">
        #{picaddr,jdbcType=VARCHAR},
      </if>
      <if test="picaddr1 != null">
        #{picaddr1,jdbcType=VARCHAR},
      </if>
      <if test="character1 != null">
        #{character1,jdbcType=VARCHAR},
      </if>
      <if test="position != null">
        #{position,jdbcType=VARCHAR},
      </if>
      <if test="ownertype != null">
        #{ownertype,jdbcType=VARCHAR},
      </if>
      <if test="codepicaddr != null">
        #{codepicaddr,jdbcType=VARCHAR},
      </if>
      <if test="mobileDigest != null">
        #{mobileDigest,jdbcType=VARCHAR},
      </if>
      <if test="agentno != null">
        #{agentno,jdbcType=VARCHAR},
      </if>
      <if test="wechatconscode != null">
        #{wechatconscode,jdbcType=VARCHAR},
      </if>
    </trim>
  </insert>
  <update id="updateByPrimaryKeySelective" parameterType="com.howbuy.crm.account.dao.po.consultant.CmConsultantPO">
    <!--@mbg.generated-->
    update CM_CONSULTANT
    <set>
      <if test="consname != null">
        CONSNAME = #{consname,jdbcType=VARCHAR},
      </if>
      <if test="conslevel != null">
        CONSLEVEL = #{conslevel,jdbcType=VARCHAR},
      </if>
      <if test="teamcode != null">
        TEAMCODE = #{teamcode,jdbcType=VARCHAR},
      </if>
      <if test="character != null">
        "CHARACTER" = #{character,jdbcType=VARCHAR},
      </if>
      <if test="consstatus != null">
        CONSSTATUS = #{consstatus,jdbcType=VARCHAR},
      </if>
      <if test="recstat != null">
        RECSTAT = #{recstat,jdbcType=VARCHAR},
      </if>
      <if test="checkflag != null">
        CHECKFLAG = #{checkflag,jdbcType=VARCHAR},
      </if>
      <if test="creator != null">
        CREATOR = #{creator,jdbcType=VARCHAR},
      </if>
      <if test="modifier != null">
        MODIFIER = #{modifier,jdbcType=VARCHAR},
      </if>
      <if test="checker != null">
        CHECKER = #{checker,jdbcType=VARCHAR},
      </if>
      <if test="credt != null">
        CREDT = #{credt,jdbcType=VARCHAR},
      </if>
      <if test="moddt != null">
        MODDT = #{moddt,jdbcType=VARCHAR},
      </if>
      <if test="telno != null">
        TELNO = #{telno,jdbcType=VARCHAR},
      </if>
      <if test="mobile != null">
        MOBILE = #{mobile,jdbcType=VARCHAR},
      </if>
      <if test="deptcode != null">
        DEPTCODE = #{deptcode,jdbcType=VARCHAR},
      </if>
      <if test="outletcode != null">
        OUTLETCODE = #{outletcode,jdbcType=VARCHAR},
      </if>
      <if test="pictureurl != null">
        PICTUREURL = #{pictureurl,jdbcType=VARCHAR},
      </if>
      <if test="email != null">
        EMAIL = #{email,jdbcType=VARCHAR},
      </if>
      <if test="resume != null">
        RESUME = #{resume,jdbcType=CLOB},
      </if>
      <if test="startdt != null">
        STARTDT = #{startdt,jdbcType=VARCHAR},
      </if>
      <if test="enddt != null">
        ENDDT = #{enddt,jdbcType=VARCHAR},
      </if>
      <if test="empcardno != null">
        EMPCARDNO = #{empcardno,jdbcType=VARCHAR},
      </if>
      <if test="loginflag != null">
        LOGINFLAG = #{loginflag,jdbcType=VARCHAR},
      </if>
      <if test="isseniormgr != null">
        ISSENIORMGR = #{isseniormgr,jdbcType=VARCHAR},
      </if>
      <if test="workplace != null">
        WORKPLACE = #{workplace,jdbcType=VARCHAR},
      </if>
      <if test="dialchannel != null">
        DIALCHANNEL = #{dialchannel,jdbcType=VARCHAR},
      </if>
      <if test="ip != null">
        IP = #{ip,jdbcType=VARCHAR},
      </if>
      <if test="salaryamtbefore != null">
        SALARYAMTBEFORE = #{salaryamtbefore,jdbcType=DECIMAL},
      </if>
      <if test="isvirtual != null">
        ISVIRTUAL = #{isvirtual,jdbcType=VARCHAR},
      </if>
      <if test="isinside != null">
        ISINSIDE = #{isinside,jdbcType=VARCHAR},
      </if>
      <if test="picaddr != null">
        PICADDR = #{picaddr,jdbcType=VARCHAR},
      </if>
      <if test="picaddr1 != null">
        PICADDR1 = #{picaddr1,jdbcType=VARCHAR},
      </if>
      <if test="character1 != null">
        CHARACTER1 = #{character1,jdbcType=VARCHAR},
      </if>
      <if test="position != null">
        "POSITION" = #{position,jdbcType=VARCHAR},
      </if>
      <if test="ownertype != null">
        OWNERTYPE = #{ownertype,jdbcType=VARCHAR},
      </if>
      <if test="codepicaddr != null">
        CODEPICADDR = #{codepicaddr,jdbcType=VARCHAR},
      </if>
      <if test="mobileDigest != null">
        MOBILE_DIGEST = #{mobileDigest,jdbcType=VARCHAR},
      </if>
      <if test="agentno != null">
        AGENTNO = #{agentno,jdbcType=VARCHAR},
      </if>
      <if test="wechatconscode != null">
        WECHATCONSCODE = #{wechatconscode,jdbcType=VARCHAR},
      </if>
    </set>
    where CONSCODE = #{conscode,jdbcType=VARCHAR}
  </update>
  <update id="updateByPrimaryKey" parameterType="com.howbuy.crm.account.dao.po.consultant.CmConsultantPO">
    <!--@mbg.generated-->
    update CM_CONSULTANT
    set CONSNAME = #{consname,jdbcType=VARCHAR},
      CONSLEVEL = #{conslevel,jdbcType=VARCHAR},
      TEAMCODE = #{teamcode,jdbcType=VARCHAR},
      "CHARACTER" = #{character,jdbcType=VARCHAR},
      CONSSTATUS = #{consstatus,jdbcType=VARCHAR},
      RECSTAT = #{recstat,jdbcType=VARCHAR},
      CHECKFLAG = #{checkflag,jdbcType=VARCHAR},
      CREATOR = #{creator,jdbcType=VARCHAR},
      MODIFIER = #{modifier,jdbcType=VARCHAR},
      CHECKER = #{checker,jdbcType=VARCHAR},
      CREDT = #{credt,jdbcType=VARCHAR},
      MODDT = #{moddt,jdbcType=VARCHAR},
      TELNO = #{telno,jdbcType=VARCHAR},
      MOBILE = #{mobile,jdbcType=VARCHAR},
      DEPTCODE = #{deptcode,jdbcType=VARCHAR},
      OUTLETCODE = #{outletcode,jdbcType=VARCHAR},
      PICTUREURL = #{pictureurl,jdbcType=VARCHAR},
      EMAIL = #{email,jdbcType=VARCHAR},
      RESUME = #{resume,jdbcType=CLOB},
      STARTDT = #{startdt,jdbcType=VARCHAR},
      ENDDT = #{enddt,jdbcType=VARCHAR},
      EMPCARDNO = #{empcardno,jdbcType=VARCHAR},
      LOGINFLAG = #{loginflag,jdbcType=VARCHAR},
      ISSENIORMGR = #{isseniormgr,jdbcType=VARCHAR},
      WORKPLACE = #{workplace,jdbcType=VARCHAR},
      DIALCHANNEL = #{dialchannel,jdbcType=VARCHAR},
      IP = #{ip,jdbcType=VARCHAR},
      SALARYAMTBEFORE = #{salaryamtbefore,jdbcType=DECIMAL},
      ISVIRTUAL = #{isvirtual,jdbcType=VARCHAR},
      ISINSIDE = #{isinside,jdbcType=VARCHAR},
      PICADDR = #{picaddr,jdbcType=VARCHAR},
      PICADDR1 = #{picaddr1,jdbcType=VARCHAR},
      CHARACTER1 = #{character1,jdbcType=VARCHAR},
      "POSITION" = #{position,jdbcType=VARCHAR},
      OWNERTYPE = #{ownertype,jdbcType=VARCHAR},
      CODEPICADDR = #{codepicaddr,jdbcType=VARCHAR},
      MOBILE_DIGEST = #{mobileDigest,jdbcType=VARCHAR},
      AGENTNO = #{agentno,jdbcType=VARCHAR},
      WECHATCONSCODE = #{wechatconscode,jdbcType=VARCHAR}
    where CONSCODE = #{conscode,jdbcType=VARCHAR}
  </update>

  <select id="getAllNeedRefreshWechatConsCode" resultType="java.lang.String">
    select
           a.wechatconscode
    from CM_CONSULTANT a
    where a.consstatus = '1'
      and a.outletcode in
          (select t.orgcode
           from HB_ORGANIZATION t
           where t.status = '0' start
           with t.orgcode in ('10', '1', '3') connect by PRIOR orgcode = parentorgcode)
      and a.isvirtual = '0'
    </select>

  <select id="countByMobileDigest" resultType="int">
    select count(1)
    from CM_CONSULTANT
    where MOBILE_DIGEST = #{mobileDigest,jdbcType=VARCHAR}
      AND CONSSTATUS = '1'
  </select>

  <select id="listConsultantByConsCodes" resultMap="BaseResultMap">
    select
    <include refid="Base_Column_List" />
    from CM_CONSULTANT
    where CONSCODE in
    <foreach collection="consCodes" item="consCode" open="(" separator="," close=")">
      #{consCode}
    </foreach>
  </select>
</mapper>