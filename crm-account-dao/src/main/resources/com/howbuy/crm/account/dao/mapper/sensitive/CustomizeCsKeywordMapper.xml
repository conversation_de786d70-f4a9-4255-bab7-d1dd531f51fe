<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.howbuy.crm.account.dao.mapper.customize.sensitive.CustomizeCsKeywordMapper">

    <select id="listKeywordNameByModule" resultType="java.lang.String">
        select
            KEYWORDNAME
        from cs_keyword
        where KEYWORDMODULE = #{moduleType,jdbcType=VARCHAR}
        and ISVALID = 0
        order by CREDDT desc
    </select>
</mapper>