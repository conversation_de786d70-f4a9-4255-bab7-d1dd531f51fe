<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.howbuy.crm.account.dao.mapper.custinfo.CmConscustMapper">
  <resultMap id="BaseResultMap" type="com.howbuy.crm.account.dao.po.custinfo.CmConscustPO">
    <!--@mbg.generated-->
    <!--@Table CM_CONSCUST-->
    <id column="CONSCUSTNO" jdbcType="VARCHAR" property="conscustno" />
    <result column="CONSCUSTLVL" jdbcType="VARCHAR" property="conscustlvl" />
    <result column="CONSCUSTGRADE" jdbcType="DECIMAL" property="conscustgrade" />
    <result column="CONSCUSTSTATUS" jdbcType="VARCHAR" property="conscuststatus" />
    <result column="IDTYPE" jdbcType="VARCHAR" property="idtype" />
    <result column="CUSTNAME" jdbcType="VARCHAR" property="custname" />
    <result column="PROVCODE" jdbcType="VARCHAR" property="provcode" />
    <result column="CITYCODE" jdbcType="VARCHAR" property="citycode" />
    <result column="EDULEVEL" jdbcType="VARCHAR" property="edulevel" />
    <result column="VOCATION" jdbcType="VARCHAR" property="vocation" />
    <result column="INCLEVEL" jdbcType="VARCHAR" property="inclevel" />
    <result column="BIRTHDAY" jdbcType="VARCHAR" property="birthday" />
    <result column="GENDER" jdbcType="VARCHAR" property="gender" />
    <result column="MARRIED" jdbcType="VARCHAR" property="married" />
    <result column="PINCOME" jdbcType="VARCHAR" property="pincome" />
    <result column="FINCOME" jdbcType="VARCHAR" property="fincome" />
    <result column="DECISIONFLAG" jdbcType="VARCHAR" property="decisionflag" />
    <result column="INTERESTS" jdbcType="VARCHAR" property="interests" />
    <result column="FAMILYCONDITION" jdbcType="VARCHAR" property="familycondition" />
    <result column="CONTACTTIME" jdbcType="VARCHAR" property="contacttime" />
    <result column="SENDINFOFLAG" jdbcType="VARCHAR" property="sendinfoflag" />
    <result column="RECVTELFLAG" jdbcType="VARCHAR" property="recvtelflag" />
    <result column="RECVEMAILFLAG" jdbcType="VARCHAR" property="recvemailflag" />
    <result column="RECVMSGFLAG" jdbcType="VARCHAR" property="recvmsgflag" />
    <result column="COMPANY" jdbcType="VARCHAR" property="company" />
    <result column="RISKLEVEL" jdbcType="VARCHAR" property="risklevel" />
    <result column="SELFRISKLEVEL" jdbcType="VARCHAR" property="selfrisklevel" />
    <result column="POSTCODE" jdbcType="VARCHAR" property="postcode" />
    <result column="FAX" jdbcType="VARCHAR" property="fax" />
    <result column="OFFICETELNO" jdbcType="VARCHAR" property="officetelno" />
    <result column="SOURCE" jdbcType="VARCHAR" property="source" />
    <result column="KNOWCHAN" jdbcType="VARCHAR" property="knowchan" />
    <result column="OTHERCHAN" jdbcType="VARCHAR" property="otherchan" />
    <result column="OTHERINVEST" jdbcType="VARCHAR" property="otherinvest" />
    <result column="SALON" jdbcType="VARCHAR" property="salon" />
    <result column="BEFOREINVEST" jdbcType="VARCHAR" property="beforeinvest" />
    <result column="SELFDEFFLAG" jdbcType="VARCHAR" property="selfdefflag" />
    <result column="VISITFQCY" jdbcType="VARCHAR" property="visitfqcy" />
    <result column="DEVDIRECTION" jdbcType="VARCHAR" property="devdirection" />
    <result column="SALEDIRECTION" jdbcType="VARCHAR" property="saledirection" />
    <result column="SUBSOURCE" jdbcType="VARCHAR" property="subsource" />
    <result column="SUBSOURCETYPE" jdbcType="VARCHAR" property="subsourcetype" />
    <result column="POSTCODE2" jdbcType="VARCHAR" property="postcode2" />
    <result column="KNOWHOWBUY" jdbcType="VARCHAR" property="knowhowbuy" />
    <result column="SUBKNOW" jdbcType="VARCHAR" property="subknow" />
    <result column="SUBKNOWTYPE" jdbcType="VARCHAR" property="subknowtype" />
    <result column="BUYINGPROD" jdbcType="VARCHAR" property="buyingprod" />
    <result column="BUYEDPROD" jdbcType="VARCHAR" property="buyedprod" />
    <result column="SPECIALFLAG" jdbcType="VARCHAR" property="specialflag" />
    <result column="DLVYMODE" jdbcType="VARCHAR" property="dlvymode" />
    <result column="REMARK" jdbcType="VARCHAR" property="remark" />
    <result column="REGDT" jdbcType="VARCHAR" property="regdt" />
    <result column="UDDT" jdbcType="VARCHAR" property="uddt" />
    <result column="PRIRISKLEVEL" jdbcType="VARCHAR" property="pririsklevel" />
    <result column="LINKMAN" jdbcType="VARCHAR" property="linkman" />
    <result column="LINKPOSTCODE" jdbcType="VARCHAR" property="linkpostcode" />
    <result column="CAPACITY" jdbcType="VARCHAR" property="capacity" />
    <result column="GPSINVESTLEVEL" jdbcType="VARCHAR" property="gpsinvestlevel" />
    <result column="GPSRISKLEVEL" jdbcType="VARCHAR" property="gpsrisklevel" />
    <result column="ISBOSS" jdbcType="VARCHAR" property="isboss" />
    <result column="FINANCENEED" jdbcType="VARCHAR" property="financeneed" />
    <result column="ISJOINCLUB" jdbcType="VARCHAR" property="isjoinclub" />
    <result column="ISRISKTIP" jdbcType="VARCHAR" property="isrisktip" />
    <result column="CUSTSOURCEREMARK" jdbcType="VARCHAR" property="custsourceremark" />
    <result column="PMARKETAMT" jdbcType="DECIMAL" property="pmarketamt" />
    <result column="ISWRITEBOOK" jdbcType="VARCHAR" property="iswritebook" />
    <result column="LATESTTRADEDT" jdbcType="VARCHAR" property="latesttradedt" />
    <result column="RSTOHIGHREASON" jdbcType="VARCHAR" property="rstohighreason" />
    <result column="ISPUBTRADE" jdbcType="VARCHAR" property="ispubtrade" />
    <result column="INVSTTYPE" jdbcType="VARCHAR" property="invsttype" />
    <result column="SOURCE2" jdbcType="VARCHAR" property="source2" />
    <result column="SUBSOURCE2" jdbcType="VARCHAR" property="subsource2" />
    <result column="SUBSOURCETYPE2" jdbcType="VARCHAR" property="subsourcetype2" />
    <result column="VIPUSERNAME" jdbcType="VARCHAR" property="vipusername" />
    <result column="WECHATCODE" jdbcType="VARCHAR" property="wechatcode" />
    <result column="NEWSOURCENO" jdbcType="VARCHAR" property="newsourceno" />
    <result column="HBONE_NO" jdbcType="VARCHAR" property="hboneNo" />
    <result column="NEWSOURCENO2" jdbcType="VARCHAR" property="newsourceno2" />
    <result column="MERGEDT" jdbcType="VARCHAR" property="mergedt" />
    <result column="HOPETRADETYPE" jdbcType="VARCHAR" property="hopetradetype" />
    <result column="CUSTSTATUS" jdbcType="VARCHAR" property="custstatus" />
    <result column="PREMIUM_AMOUNT" jdbcType="DECIMAL" property="premiumAmount" />
    <result column="VALIDITY" jdbcType="VARCHAR" property="validity" />
    <result column="VALIDITYDT" jdbcType="VARCHAR" property="validitydt" />
    <result column="NATURE" jdbcType="VARCHAR" property="nature" />
    <result column="APTITUDE" jdbcType="VARCHAR" property="aptitude" />
    <result column="SCOPEBUSINESS" jdbcType="VARCHAR" property="scopebusiness" />
    <result column="RESTYPE" jdbcType="VARCHAR" property="restype" />
    <result column="ACREGDT" jdbcType="VARCHAR" property="acregdt" />
    <result column="JOINCLUBDT" jdbcType="VARCHAR" property="joinclubdt" />
    <result column="PROVCODE_MOBILE" jdbcType="VARCHAR" property="provcodeMobile" />
    <result column="CITYCODE_MOBILE" jdbcType="VARCHAR" property="citycodeMobile" />
    <result column="ORGTYPE" jdbcType="VARCHAR" property="orgtype" />
    <result column="PINYIN" jdbcType="VARCHAR" property="pinyin" />
    <result column="IDNO_DIGEST" jdbcType="VARCHAR" property="idnoDigest" />
    <result column="IDNO_MASK" jdbcType="VARCHAR" property="idnoMask" />
    <result column="CUSTNAME_DIGEST" jdbcType="VARCHAR" property="custnameDigest" />
    <result column="CUSTNAME_MASK" jdbcType="VARCHAR" property="custnameMask" />
    <result column="ADDR_DIGEST" jdbcType="VARCHAR" property="addrDigest" />
    <result column="ADDR_MASK" jdbcType="VARCHAR" property="addrMask" />
    <result column="MOBILE_DIGEST" jdbcType="VARCHAR" property="mobileDigest" />
    <result column="MOBILE_MASK" jdbcType="VARCHAR" property="mobileMask" />
    <result column="TELNO_DIGEST" jdbcType="VARCHAR" property="telnoDigest" />
    <result column="TELNO_MASK" jdbcType="VARCHAR" property="telnoMask" />
    <result column="EMAIL_DIGEST" jdbcType="VARCHAR" property="emailDigest" />
    <result column="EMAIL_MASK" jdbcType="VARCHAR" property="emailMask" />
    <result column="ADDR2_DIGEST" jdbcType="VARCHAR" property="addr2Digest" />
    <result column="ADDR2_MASK" jdbcType="VARCHAR" property="addr2Mask" />
    <result column="MOBILE2_DIGEST" jdbcType="VARCHAR" property="mobile2Digest" />
    <result column="MOBILE2_MASK" jdbcType="VARCHAR" property="mobile2Mask" />
    <result column="EMAIL2_DIGEST" jdbcType="VARCHAR" property="email2Digest" />
    <result column="EMAIL2_MASK" jdbcType="VARCHAR" property="email2Mask" />
    <result column="LINKMAN_DIGEST" jdbcType="VARCHAR" property="linkmanDigest" />
    <result column="LINKMAN_MASK" jdbcType="VARCHAR" property="linkmanMask" />
    <result column="LINKTEL_DIGEST" jdbcType="VARCHAR" property="linktelDigest" />
    <result column="LINKTEL_MASK" jdbcType="VARCHAR" property="linktelMask" />
    <result column="LINKMOBILE_DIGEST" jdbcType="VARCHAR" property="linkmobileDigest" />
    <result column="LINKMOBILE_MASK" jdbcType="VARCHAR" property="linkmobileMask" />
    <result column="LINKEMAIL_DIGEST" jdbcType="VARCHAR" property="linkemailDigest" />
    <result column="LINKEMAIL_MASK" jdbcType="VARCHAR" property="linkemailMask" />
    <result column="LINKADDR_DIGEST" jdbcType="VARCHAR" property="linkaddrDigest" />
    <result column="LINKADDR_MASK" jdbcType="VARCHAR" property="linkaddrMask" />
    <result column="ISVIRTUALSHARER" jdbcType="VARCHAR" property="isvirtualsharer" />
    <result column="VALIDITYST" jdbcType="VARCHAR" property="validityst" />
    <result column="MOBILE_AREA_CODE" jdbcType="VARCHAR" property="mobileAreaCode" />
    <result column="MOBILE2_AREA_CODE" jdbcType="VARCHAR" property="mobile2AreaCode" />
    <result column="LINKMOBILE_AREA_CODE" jdbcType="VARCHAR" property="linkmobileAreaCode" />
    <result column="ID_SIGN_AREA_CODE" jdbcType="VARCHAR" property="idSignAreaCode" />
    <result column="NATION_CODE" jdbcType="VARCHAR" property="nationCode" />
    <result column="HBONE_TIMESTAMP" jdbcType="TIMESTAMP" property="hboneTimestamp" />
    <result column="CREATE_TIMESTAMP" jdbcType="TIMESTAMP" property="createTimestamp" />
    <result column="COUNTY_CODE" jdbcType="VARCHAR" property="countyCode" />
  </resultMap>
  <sql id="Base_Column_List">
    <!--@mbg.generated-->
    CONSCUSTNO, CONSCUSTLVL, CONSCUSTGRADE, CONSCUSTSTATUS, IDTYPE, CUSTNAME, PROVCODE,
    CITYCODE, EDULEVEL, VOCATION, INCLEVEL, BIRTHDAY, GENDER, MARRIED, PINCOME, FINCOME,
    DECISIONFLAG, INTERESTS, FAMILYCONDITION, CONTACTTIME, SENDINFOFLAG, RECVTELFLAG,
    RECVEMAILFLAG, RECVMSGFLAG, COMPANY, RISKLEVEL, SELFRISKLEVEL, POSTCODE, FAX, OFFICETELNO,
    "SOURCE", KNOWCHAN, OTHERCHAN, OTHERINVEST, SALON, BEFOREINVEST, SELFDEFFLAG, VISITFQCY,
    DEVDIRECTION, SALEDIRECTION, SUBSOURCE, SUBSOURCETYPE, POSTCODE2, KNOWHOWBUY, SUBKNOW,
    SUBKNOWTYPE, BUYINGPROD, BUYEDPROD, SPECIALFLAG, DLVYMODE, REMARK, REGDT, UDDT, PRIRISKLEVEL,
    LINKMAN, LINKPOSTCODE, CAPACITY, GPSINVESTLEVEL, GPSRISKLEVEL, ISBOSS, FINANCENEED,
    ISJOINCLUB, ISRISKTIP, CUSTSOURCEREMARK, PMARKETAMT, ISWRITEBOOK, LATESTTRADEDT,
    RSTOHIGHREASON, ISPUBTRADE, INVSTTYPE, SOURCE2, SUBSOURCE2, SUBSOURCETYPE2, VIPUSERNAME,
    WECHATCODE, NEWSOURCENO, HBONE_NO, NEWSOURCENO2, MERGEDT, HOPETRADETYPE, CUSTSTATUS,
    PREMIUM_AMOUNT, VALIDITY, VALIDITYDT, NATURE, APTITUDE, SCOPEBUSINESS, RESTYPE, ACREGDT,
    JOINCLUBDT, PROVCODE_MOBILE, CITYCODE_MOBILE, ORGTYPE, PINYIN, IDNO_DIGEST, IDNO_MASK,
    CUSTNAME_DIGEST, CUSTNAME_MASK, ADDR_DIGEST, ADDR_MASK, MOBILE_DIGEST, MOBILE_MASK,
    TELNO_DIGEST, TELNO_MASK, EMAIL_DIGEST, EMAIL_MASK, ADDR2_DIGEST, ADDR2_MASK, MOBILE2_DIGEST,
    MOBILE2_MASK, EMAIL2_DIGEST, EMAIL2_MASK, LINKMAN_DIGEST, LINKMAN_MASK, LINKTEL_DIGEST,
    LINKTEL_MASK, LINKMOBILE_DIGEST, LINKMOBILE_MASK, LINKEMAIL_DIGEST, LINKEMAIL_MASK,
    LINKADDR_DIGEST, LINKADDR_MASK, ISVIRTUALSHARER, VALIDITYST, MOBILE_AREA_CODE, MOBILE2_AREA_CODE,
    LINKMOBILE_AREA_CODE, ID_SIGN_AREA_CODE, NATION_CODE, HBONE_TIMESTAMP, CREATE_TIMESTAMP,
    COUNTY_CODE
  </sql>
  <select id="selectByPrimaryKey" parameterType="java.lang.String" resultMap="BaseResultMap">
    <!--@mbg.generated-->
    select
    <include refid="Base_Column_List" />
    from CM_CONSCUST
    where CONSCUSTNO = #{conscustno,jdbcType=VARCHAR}
  </select>
  <delete id="deleteByPrimaryKey" parameterType="java.lang.String">
    <!--@mbg.generated-->
    delete from CM_CONSCUST
    where CONSCUSTNO = #{conscustno,jdbcType=VARCHAR}
  </delete>
  <insert id="insert" parameterType="com.howbuy.crm.account.dao.po.custinfo.CmConscustPO">
    <!--@mbg.generated-->
    insert into CM_CONSCUST (CONSCUSTNO, CONSCUSTLVL, CONSCUSTGRADE,
    CONSCUSTSTATUS, IDTYPE, CUSTNAME,
    PROVCODE, CITYCODE, EDULEVEL,
    VOCATION, INCLEVEL, BIRTHDAY,
    GENDER, MARRIED, PINCOME,
    FINCOME, DECISIONFLAG, INTERESTS,
    FAMILYCONDITION, CONTACTTIME, SENDINFOFLAG,
    RECVTELFLAG, RECVEMAILFLAG, RECVMSGFLAG,
    COMPANY, RISKLEVEL, SELFRISKLEVEL,
    POSTCODE, FAX, OFFICETELNO,
    "SOURCE", KNOWCHAN, OTHERCHAN,
    OTHERINVEST, SALON, BEFOREINVEST,
    SELFDEFFLAG, VISITFQCY, DEVDIRECTION,
    SALEDIRECTION, SUBSOURCE, SUBSOURCETYPE,
    POSTCODE2, KNOWHOWBUY, SUBKNOW,
    SUBKNOWTYPE, BUYINGPROD, BUYEDPROD,
    SPECIALFLAG, DLVYMODE, REMARK,
    REGDT, UDDT, PRIRISKLEVEL,
    LINKMAN, LINKPOSTCODE, CAPACITY,
    GPSINVESTLEVEL, GPSRISKLEVEL, ISBOSS,
    FINANCENEED, ISJOINCLUB, ISRISKTIP,
    CUSTSOURCEREMARK, PMARKETAMT, ISWRITEBOOK,
    LATESTTRADEDT, RSTOHIGHREASON, ISPUBTRADE,
    INVSTTYPE, SOURCE2, SUBSOURCE2,
    SUBSOURCETYPE2, VIPUSERNAME, WECHATCODE,
    NEWSOURCENO, HBONE_NO, NEWSOURCENO2,
    MERGEDT, HOPETRADETYPE, CUSTSTATUS,
    PREMIUM_AMOUNT, VALIDITY, VALIDITYDT,
    NATURE, APTITUDE, SCOPEBUSINESS,
    RESTYPE, ACREGDT, JOINCLUBDT,
    PROVCODE_MOBILE, CITYCODE_MOBILE, ORGTYPE,
    PINYIN, IDNO_DIGEST, IDNO_MASK,
    CUSTNAME_DIGEST, CUSTNAME_MASK, ADDR_DIGEST,
    ADDR_MASK, MOBILE_DIGEST, MOBILE_MASK,
    TELNO_DIGEST, TELNO_MASK, EMAIL_DIGEST,
    EMAIL_MASK, ADDR2_DIGEST, ADDR2_MASK,
    MOBILE2_DIGEST, MOBILE2_MASK, EMAIL2_DIGEST,
    EMAIL2_MASK, LINKMAN_DIGEST, LINKMAN_MASK,
    LINKTEL_DIGEST, LINKTEL_MASK, LINKMOBILE_DIGEST,
    LINKMOBILE_MASK, LINKEMAIL_DIGEST, LINKEMAIL_MASK,
    LINKADDR_DIGEST, LINKADDR_MASK, ISVIRTUALSHARER,
    VALIDITYST, MOBILE_AREA_CODE, MOBILE2_AREA_CODE,
    LINKMOBILE_AREA_CODE, ID_SIGN_AREA_CODE, NATION_CODE,
    HBONE_TIMESTAMP, CREATE_TIMESTAMP, COUNTY_CODE
    )
    values (#{conscustno,jdbcType=VARCHAR}, #{conscustlvl,jdbcType=VARCHAR}, #{conscustgrade,jdbcType=DECIMAL},
    #{conscuststatus,jdbcType=VARCHAR}, #{idtype,jdbcType=VARCHAR}, #{custname,jdbcType=VARCHAR},
    #{provcode,jdbcType=VARCHAR}, #{citycode,jdbcType=VARCHAR}, #{edulevel,jdbcType=VARCHAR},
    #{vocation,jdbcType=VARCHAR}, #{inclevel,jdbcType=VARCHAR}, #{birthday,jdbcType=VARCHAR},
    #{gender,jdbcType=VARCHAR}, #{married,jdbcType=VARCHAR}, #{pincome,jdbcType=VARCHAR},
    #{fincome,jdbcType=VARCHAR}, #{decisionflag,jdbcType=VARCHAR}, #{interests,jdbcType=VARCHAR},
    #{familycondition,jdbcType=VARCHAR}, #{contacttime,jdbcType=VARCHAR}, #{sendinfoflag,jdbcType=VARCHAR},
    #{recvtelflag,jdbcType=VARCHAR}, #{recvemailflag,jdbcType=VARCHAR}, #{recvmsgflag,jdbcType=VARCHAR},
    #{company,jdbcType=VARCHAR}, #{risklevel,jdbcType=VARCHAR}, #{selfrisklevel,jdbcType=VARCHAR},
    #{postcode,jdbcType=VARCHAR}, #{fax,jdbcType=VARCHAR}, #{officetelno,jdbcType=VARCHAR},
    #{source,jdbcType=VARCHAR}, #{knowchan,jdbcType=VARCHAR}, #{otherchan,jdbcType=VARCHAR},
    #{otherinvest,jdbcType=VARCHAR}, #{salon,jdbcType=VARCHAR}, #{beforeinvest,jdbcType=VARCHAR},
    #{selfdefflag,jdbcType=VARCHAR}, #{visitfqcy,jdbcType=VARCHAR}, #{devdirection,jdbcType=VARCHAR},
    #{saledirection,jdbcType=VARCHAR}, #{subsource,jdbcType=VARCHAR}, #{subsourcetype,jdbcType=VARCHAR},
    #{postcode2,jdbcType=VARCHAR}, #{knowhowbuy,jdbcType=VARCHAR}, #{subknow,jdbcType=VARCHAR},
    #{subknowtype,jdbcType=VARCHAR}, #{buyingprod,jdbcType=VARCHAR}, #{buyedprod,jdbcType=VARCHAR},
    #{specialflag,jdbcType=VARCHAR}, #{dlvymode,jdbcType=VARCHAR}, #{remark,jdbcType=VARCHAR},
    #{regdt,jdbcType=VARCHAR}, #{uddt,jdbcType=VARCHAR}, #{pririsklevel,jdbcType=VARCHAR},
    #{linkman,jdbcType=VARCHAR}, #{linkpostcode,jdbcType=VARCHAR}, #{capacity,jdbcType=VARCHAR},
    #{gpsinvestlevel,jdbcType=VARCHAR}, #{gpsrisklevel,jdbcType=VARCHAR}, #{isboss,jdbcType=VARCHAR},
    #{financeneed,jdbcType=VARCHAR}, #{isjoinclub,jdbcType=VARCHAR}, #{isrisktip,jdbcType=VARCHAR},
    #{custsourceremark,jdbcType=VARCHAR}, #{pmarketamt,jdbcType=DECIMAL}, #{iswritebook,jdbcType=VARCHAR},
    #{latesttradedt,jdbcType=VARCHAR}, #{rstohighreason,jdbcType=VARCHAR}, #{ispubtrade,jdbcType=VARCHAR},
    #{invsttype,jdbcType=VARCHAR}, #{source2,jdbcType=VARCHAR}, #{subsource2,jdbcType=VARCHAR},
    #{subsourcetype2,jdbcType=VARCHAR}, #{vipusername,jdbcType=VARCHAR}, #{wechatcode,jdbcType=VARCHAR},
    #{newsourceno,jdbcType=VARCHAR}, #{hboneNo,jdbcType=VARCHAR}, #{newsourceno2,jdbcType=VARCHAR},
    #{mergedt,jdbcType=VARCHAR}, #{hopetradetype,jdbcType=VARCHAR}, #{custstatus,jdbcType=VARCHAR},
    #{premiumAmount,jdbcType=DECIMAL}, #{validity,jdbcType=VARCHAR}, #{validitydt,jdbcType=VARCHAR},
    #{nature,jdbcType=VARCHAR}, #{aptitude,jdbcType=VARCHAR}, #{scopebusiness,jdbcType=VARCHAR},
    #{restype,jdbcType=VARCHAR}, #{acregdt,jdbcType=VARCHAR}, #{joinclubdt,jdbcType=VARCHAR},
    #{provcodeMobile,jdbcType=VARCHAR}, #{citycodeMobile,jdbcType=VARCHAR}, #{orgtype,jdbcType=VARCHAR},
    #{pinyin,jdbcType=VARCHAR}, #{idnoDigest,jdbcType=VARCHAR}, #{idnoMask,jdbcType=VARCHAR},
    #{custnameDigest,jdbcType=VARCHAR}, #{custnameMask,jdbcType=VARCHAR}, #{addrDigest,jdbcType=VARCHAR},
    #{addrMask,jdbcType=VARCHAR}, #{mobileDigest,jdbcType=VARCHAR}, #{mobileMask,jdbcType=VARCHAR},
    #{telnoDigest,jdbcType=VARCHAR}, #{telnoMask,jdbcType=VARCHAR}, #{emailDigest,jdbcType=VARCHAR},
    #{emailMask,jdbcType=VARCHAR}, #{addr2Digest,jdbcType=VARCHAR}, #{addr2Mask,jdbcType=VARCHAR},
    #{mobile2Digest,jdbcType=VARCHAR}, #{mobile2Mask,jdbcType=VARCHAR}, #{email2Digest,jdbcType=VARCHAR},
    #{email2Mask,jdbcType=VARCHAR}, #{linkmanDigest,jdbcType=VARCHAR}, #{linkmanMask,jdbcType=VARCHAR},
    #{linktelDigest,jdbcType=VARCHAR}, #{linktelMask,jdbcType=VARCHAR}, #{linkmobileDigest,jdbcType=VARCHAR},
    #{linkmobileMask,jdbcType=VARCHAR}, #{linkemailDigest,jdbcType=VARCHAR}, #{linkemailMask,jdbcType=VARCHAR},
    #{linkaddrDigest,jdbcType=VARCHAR}, #{linkaddrMask,jdbcType=VARCHAR}, #{isvirtualsharer,jdbcType=VARCHAR},
    #{validityst,jdbcType=VARCHAR}, #{mobileAreaCode,jdbcType=VARCHAR}, #{mobile2AreaCode,jdbcType=VARCHAR},
    #{linkmobileAreaCode,jdbcType=VARCHAR}, #{idSignAreaCode,jdbcType=VARCHAR}, #{nationCode,jdbcType=VARCHAR},
    #{hboneTimestamp,jdbcType=TIMESTAMP}, #{createTimestamp,jdbcType=TIMESTAMP}, #{countyCode,jdbcType=VARCHAR}
    )
  </insert>
  <insert id="insertSelective" parameterType="com.howbuy.crm.account.dao.po.custinfo.CmConscustPO">
    <!--@mbg.generated-->
    insert into CM_CONSCUST
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="conscustno != null">
        CONSCUSTNO,
      </if>
      <if test="conscustlvl != null">
        CONSCUSTLVL,
      </if>
      <if test="conscustgrade != null">
        CONSCUSTGRADE,
      </if>
      <if test="conscuststatus != null">
        CONSCUSTSTATUS,
      </if>
      <if test="idtype != null">
        IDTYPE,
      </if>
      <if test="custname != null">
        CUSTNAME,
      </if>
      <if test="provcode != null">
        PROVCODE,
      </if>
      <if test="citycode != null">
        CITYCODE,
      </if>
      <if test="edulevel != null">
        EDULEVEL,
      </if>
      <if test="vocation != null">
        VOCATION,
      </if>
      <if test="inclevel != null">
        INCLEVEL,
      </if>
      <if test="birthday != null">
        BIRTHDAY,
      </if>
      <if test="gender != null">
        GENDER,
      </if>
      <if test="married != null">
        MARRIED,
      </if>
      <if test="pincome != null">
        PINCOME,
      </if>
      <if test="fincome != null">
        FINCOME,
      </if>
      <if test="decisionflag != null">
        DECISIONFLAG,
      </if>
      <if test="interests != null">
        INTERESTS,
      </if>
      <if test="familycondition != null">
        FAMILYCONDITION,
      </if>
      <if test="contacttime != null">
        CONTACTTIME,
      </if>
      <if test="sendinfoflag != null">
        SENDINFOFLAG,
      </if>
      <if test="recvtelflag != null">
        RECVTELFLAG,
      </if>
      <if test="recvemailflag != null">
        RECVEMAILFLAG,
      </if>
      <if test="recvmsgflag != null">
        RECVMSGFLAG,
      </if>
      <if test="company != null">
        COMPANY,
      </if>
      <if test="risklevel != null">
        RISKLEVEL,
      </if>
      <if test="selfrisklevel != null">
        SELFRISKLEVEL,
      </if>
      <if test="postcode != null">
        POSTCODE,
      </if>
      <if test="fax != null">
        FAX,
      </if>
      <if test="officetelno != null">
        OFFICETELNO,
      </if>
      <if test="source != null">
        "SOURCE",
      </if>
      <if test="knowchan != null">
        KNOWCHAN,
      </if>
      <if test="otherchan != null">
        OTHERCHAN,
      </if>
      <if test="otherinvest != null">
        OTHERINVEST,
      </if>
      <if test="salon != null">
        SALON,
      </if>
      <if test="beforeinvest != null">
        BEFOREINVEST,
      </if>
      <if test="selfdefflag != null">
        SELFDEFFLAG,
      </if>
      <if test="visitfqcy != null">
        VISITFQCY,
      </if>
      <if test="devdirection != null">
        DEVDIRECTION,
      </if>
      <if test="saledirection != null">
        SALEDIRECTION,
      </if>
      <if test="subsource != null">
        SUBSOURCE,
      </if>
      <if test="subsourcetype != null">
        SUBSOURCETYPE,
      </if>
      <if test="postcode2 != null">
        POSTCODE2,
      </if>
      <if test="knowhowbuy != null">
        KNOWHOWBUY,
      </if>
      <if test="subknow != null">
        SUBKNOW,
      </if>
      <if test="subknowtype != null">
        SUBKNOWTYPE,
      </if>
      <if test="buyingprod != null">
        BUYINGPROD,
      </if>
      <if test="buyedprod != null">
        BUYEDPROD,
      </if>
      <if test="specialflag != null">
        SPECIALFLAG,
      </if>
      <if test="dlvymode != null">
        DLVYMODE,
      </if>
      <if test="remark != null">
        REMARK,
      </if>
      <if test="regdt != null">
        REGDT,
      </if>
      <if test="uddt != null">
        UDDT,
      </if>
      <if test="pririsklevel != null">
        PRIRISKLEVEL,
      </if>
      <if test="linkman != null">
        LINKMAN,
      </if>
      <if test="linkpostcode != null">
        LINKPOSTCODE,
      </if>
      <if test="capacity != null">
        CAPACITY,
      </if>
      <if test="gpsinvestlevel != null">
        GPSINVESTLEVEL,
      </if>
      <if test="gpsrisklevel != null">
        GPSRISKLEVEL,
      </if>
      <if test="isboss != null">
        ISBOSS,
      </if>
      <if test="financeneed != null">
        FINANCENEED,
      </if>
      <if test="isjoinclub != null">
        ISJOINCLUB,
      </if>
      <if test="isrisktip != null">
        ISRISKTIP,
      </if>
      <if test="custsourceremark != null">
        CUSTSOURCEREMARK,
      </if>
      <if test="pmarketamt != null">
        PMARKETAMT,
      </if>
      <if test="iswritebook != null">
        ISWRITEBOOK,
      </if>
      <if test="latesttradedt != null">
        LATESTTRADEDT,
      </if>
      <if test="rstohighreason != null">
        RSTOHIGHREASON,
      </if>
      <if test="ispubtrade != null">
        ISPUBTRADE,
      </if>
      <if test="invsttype != null">
        INVSTTYPE,
      </if>
      <if test="source2 != null">
        SOURCE2,
      </if>
      <if test="subsource2 != null">
        SUBSOURCE2,
      </if>
      <if test="subsourcetype2 != null">
        SUBSOURCETYPE2,
      </if>
      <if test="vipusername != null">
        VIPUSERNAME,
      </if>
      <if test="wechatcode != null">
        WECHATCODE,
      </if>
      <if test="newsourceno != null">
        NEWSOURCENO,
      </if>
      <if test="hboneNo != null">
        HBONE_NO,
      </if>
      <if test="newsourceno2 != null">
        NEWSOURCENO2,
      </if>
      <if test="mergedt != null">
        MERGEDT,
      </if>
      <if test="hopetradetype != null">
        HOPETRADETYPE,
      </if>
      <if test="custstatus != null">
        CUSTSTATUS,
      </if>
      <if test="premiumAmount != null">
        PREMIUM_AMOUNT,
      </if>
      <if test="validity != null">
        VALIDITY,
      </if>
      <if test="validitydt != null">
        VALIDITYDT,
      </if>
      <if test="nature != null">
        NATURE,
      </if>
      <if test="aptitude != null">
        APTITUDE,
      </if>
      <if test="scopebusiness != null">
        SCOPEBUSINESS,
      </if>
      <if test="restype != null">
        RESTYPE,
      </if>
      <if test="acregdt != null">
        ACREGDT,
      </if>
      <if test="joinclubdt != null">
        JOINCLUBDT,
      </if>
      <if test="provcodeMobile != null">
        PROVCODE_MOBILE,
      </if>
      <if test="citycodeMobile != null">
        CITYCODE_MOBILE,
      </if>
      <if test="orgtype != null">
        ORGTYPE,
      </if>
      <if test="pinyin != null">
        PINYIN,
      </if>
      <if test="idnoDigest != null">
        IDNO_DIGEST,
      </if>
      <if test="idnoMask != null">
        IDNO_MASK,
      </if>
      <if test="custnameDigest != null">
        CUSTNAME_DIGEST,
      </if>
      <if test="custnameMask != null">
        CUSTNAME_MASK,
      </if>
      <if test="addrDigest != null">
        ADDR_DIGEST,
      </if>
      <if test="addrMask != null">
        ADDR_MASK,
      </if>
      <if test="mobileDigest != null">
        MOBILE_DIGEST,
      </if>
      <if test="mobileMask != null">
        MOBILE_MASK,
      </if>
      <if test="telnoDigest != null">
        TELNO_DIGEST,
      </if>
      <if test="telnoMask != null">
        TELNO_MASK,
      </if>
      <if test="emailDigest != null">
        EMAIL_DIGEST,
      </if>
      <if test="emailMask != null">
        EMAIL_MASK,
      </if>
      <if test="addr2Digest != null">
        ADDR2_DIGEST,
      </if>
      <if test="addr2Mask != null">
        ADDR2_MASK,
      </if>
      <if test="mobile2Digest != null">
        MOBILE2_DIGEST,
      </if>
      <if test="mobile2Mask != null">
        MOBILE2_MASK,
      </if>
      <if test="email2Digest != null">
        EMAIL2_DIGEST,
      </if>
      <if test="email2Mask != null">
        EMAIL2_MASK,
      </if>
      <if test="linkmanDigest != null">
        LINKMAN_DIGEST,
      </if>
      <if test="linkmanMask != null">
        LINKMAN_MASK,
      </if>
      <if test="linktelDigest != null">
        LINKTEL_DIGEST,
      </if>
      <if test="linktelMask != null">
        LINKTEL_MASK,
      </if>
      <if test="linkmobileDigest != null">
        LINKMOBILE_DIGEST,
      </if>
      <if test="linkmobileMask != null">
        LINKMOBILE_MASK,
      </if>
      <if test="linkemailDigest != null">
        LINKEMAIL_DIGEST,
      </if>
      <if test="linkemailMask != null">
        LINKEMAIL_MASK,
      </if>
      <if test="linkaddrDigest != null">
        LINKADDR_DIGEST,
      </if>
      <if test="linkaddrMask != null">
        LINKADDR_MASK,
      </if>
      <if test="isvirtualsharer != null">
        ISVIRTUALSHARER,
      </if>
      <if test="validityst != null">
        VALIDITYST,
      </if>
      <if test="mobileAreaCode != null">
        MOBILE_AREA_CODE,
      </if>
      <if test="mobile2AreaCode != null">
        MOBILE2_AREA_CODE,
      </if>
      <if test="linkmobileAreaCode != null">
        LINKMOBILE_AREA_CODE,
      </if>
      <if test="idSignAreaCode != null">
        ID_SIGN_AREA_CODE,
      </if>
      <if test="nationCode != null">
        NATION_CODE,
      </if>
      <if test="hboneTimestamp != null">
        HBONE_TIMESTAMP,
      </if>
      <if test="createTimestamp != null">
        CREATE_TIMESTAMP,
      </if>
      <if test="countyCode != null">
        COUNTY_CODE,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="conscustno != null">
        #{conscustno,jdbcType=VARCHAR},
      </if>
      <if test="conscustlvl != null">
        #{conscustlvl,jdbcType=VARCHAR},
      </if>
      <if test="conscustgrade != null">
        #{conscustgrade,jdbcType=DECIMAL},
      </if>
      <if test="conscuststatus != null">
        #{conscuststatus,jdbcType=VARCHAR},
      </if>
      <if test="idtype != null">
        #{idtype,jdbcType=VARCHAR},
      </if>
      <if test="custname != null">
        #{custname,jdbcType=VARCHAR},
      </if>
      <if test="provcode != null">
        #{provcode,jdbcType=VARCHAR},
      </if>
      <if test="citycode != null">
        #{citycode,jdbcType=VARCHAR},
      </if>
      <if test="edulevel != null">
        #{edulevel,jdbcType=VARCHAR},
      </if>
      <if test="vocation != null">
        #{vocation,jdbcType=VARCHAR},
      </if>
      <if test="inclevel != null">
        #{inclevel,jdbcType=VARCHAR},
      </if>
      <if test="birthday != null">
        #{birthday,jdbcType=VARCHAR},
      </if>
      <if test="gender != null">
        #{gender,jdbcType=VARCHAR},
      </if>
      <if test="married != null">
        #{married,jdbcType=VARCHAR},
      </if>
      <if test="pincome != null">
        #{pincome,jdbcType=VARCHAR},
      </if>
      <if test="fincome != null">
        #{fincome,jdbcType=VARCHAR},
      </if>
      <if test="decisionflag != null">
        #{decisionflag,jdbcType=VARCHAR},
      </if>
      <if test="interests != null">
        #{interests,jdbcType=VARCHAR},
      </if>
      <if test="familycondition != null">
        #{familycondition,jdbcType=VARCHAR},
      </if>
      <if test="contacttime != null">
        #{contacttime,jdbcType=VARCHAR},
      </if>
      <if test="sendinfoflag != null">
        #{sendinfoflag,jdbcType=VARCHAR},
      </if>
      <if test="recvtelflag != null">
        #{recvtelflag,jdbcType=VARCHAR},
      </if>
      <if test="recvemailflag != null">
        #{recvemailflag,jdbcType=VARCHAR},
      </if>
      <if test="recvmsgflag != null">
        #{recvmsgflag,jdbcType=VARCHAR},
      </if>
      <if test="company != null">
        #{company,jdbcType=VARCHAR},
      </if>
      <if test="risklevel != null">
        #{risklevel,jdbcType=VARCHAR},
      </if>
      <if test="selfrisklevel != null">
        #{selfrisklevel,jdbcType=VARCHAR},
      </if>
      <if test="postcode != null">
        #{postcode,jdbcType=VARCHAR},
      </if>
      <if test="fax != null">
        #{fax,jdbcType=VARCHAR},
      </if>
      <if test="officetelno != null">
        #{officetelno,jdbcType=VARCHAR},
      </if>
      <if test="source != null">
        #{source,jdbcType=VARCHAR},
      </if>
      <if test="knowchan != null">
        #{knowchan,jdbcType=VARCHAR},
      </if>
      <if test="otherchan != null">
        #{otherchan,jdbcType=VARCHAR},
      </if>
      <if test="otherinvest != null">
        #{otherinvest,jdbcType=VARCHAR},
      </if>
      <if test="salon != null">
        #{salon,jdbcType=VARCHAR},
      </if>
      <if test="beforeinvest != null">
        #{beforeinvest,jdbcType=VARCHAR},
      </if>
      <if test="selfdefflag != null">
        #{selfdefflag,jdbcType=VARCHAR},
      </if>
      <if test="visitfqcy != null">
        #{visitfqcy,jdbcType=VARCHAR},
      </if>
      <if test="devdirection != null">
        #{devdirection,jdbcType=VARCHAR},
      </if>
      <if test="saledirection != null">
        #{saledirection,jdbcType=VARCHAR},
      </if>
      <if test="subsource != null">
        #{subsource,jdbcType=VARCHAR},
      </if>
      <if test="subsourcetype != null">
        #{subsourcetype,jdbcType=VARCHAR},
      </if>
      <if test="postcode2 != null">
        #{postcode2,jdbcType=VARCHAR},
      </if>
      <if test="knowhowbuy != null">
        #{knowhowbuy,jdbcType=VARCHAR},
      </if>
      <if test="subknow != null">
        #{subknow,jdbcType=VARCHAR},
      </if>
      <if test="subknowtype != null">
        #{subknowtype,jdbcType=VARCHAR},
      </if>
      <if test="buyingprod != null">
        #{buyingprod,jdbcType=VARCHAR},
      </if>
      <if test="buyedprod != null">
        #{buyedprod,jdbcType=VARCHAR},
      </if>
      <if test="specialflag != null">
        #{specialflag,jdbcType=VARCHAR},
      </if>
      <if test="dlvymode != null">
        #{dlvymode,jdbcType=VARCHAR},
      </if>
      <if test="remark != null">
        #{remark,jdbcType=VARCHAR},
      </if>
      <if test="regdt != null">
        #{regdt,jdbcType=VARCHAR},
      </if>
      <if test="uddt != null">
        #{uddt,jdbcType=VARCHAR},
      </if>
      <if test="pririsklevel != null">
        #{pririsklevel,jdbcType=VARCHAR},
      </if>
      <if test="linkman != null">
        #{linkman,jdbcType=VARCHAR},
      </if>
      <if test="linkpostcode != null">
        #{linkpostcode,jdbcType=VARCHAR},
      </if>
      <if test="capacity != null">
        #{capacity,jdbcType=VARCHAR},
      </if>
      <if test="gpsinvestlevel != null">
        #{gpsinvestlevel,jdbcType=VARCHAR},
      </if>
      <if test="gpsrisklevel != null">
        #{gpsrisklevel,jdbcType=VARCHAR},
      </if>
      <if test="isboss != null">
        #{isboss,jdbcType=VARCHAR},
      </if>
      <if test="financeneed != null">
        #{financeneed,jdbcType=VARCHAR},
      </if>
      <if test="isjoinclub != null">
        #{isjoinclub,jdbcType=VARCHAR},
      </if>
      <if test="isrisktip != null">
        #{isrisktip,jdbcType=VARCHAR},
      </if>
      <if test="custsourceremark != null">
        #{custsourceremark,jdbcType=VARCHAR},
      </if>
      <if test="pmarketamt != null">
        #{pmarketamt,jdbcType=DECIMAL},
      </if>
      <if test="iswritebook != null">
        #{iswritebook,jdbcType=VARCHAR},
      </if>
      <if test="latesttradedt != null">
        #{latesttradedt,jdbcType=VARCHAR},
      </if>
      <if test="rstohighreason != null">
        #{rstohighreason,jdbcType=VARCHAR},
      </if>
      <if test="ispubtrade != null">
        #{ispubtrade,jdbcType=VARCHAR},
      </if>
      <if test="invsttype != null">
        #{invsttype,jdbcType=VARCHAR},
      </if>
      <if test="source2 != null">
        #{source2,jdbcType=VARCHAR},
      </if>
      <if test="subsource2 != null">
        #{subsource2,jdbcType=VARCHAR},
      </if>
      <if test="subsourcetype2 != null">
        #{subsourcetype2,jdbcType=VARCHAR},
      </if>
      <if test="vipusername != null">
        #{vipusername,jdbcType=VARCHAR},
      </if>
      <if test="wechatcode != null">
        #{wechatcode,jdbcType=VARCHAR},
      </if>
      <if test="newsourceno != null">
        #{newsourceno,jdbcType=VARCHAR},
      </if>
      <if test="hboneNo != null">
        #{hboneNo,jdbcType=VARCHAR},
      </if>
      <if test="newsourceno2 != null">
        #{newsourceno2,jdbcType=VARCHAR},
      </if>
      <if test="mergedt != null">
        #{mergedt,jdbcType=VARCHAR},
      </if>
      <if test="hopetradetype != null">
        #{hopetradetype,jdbcType=VARCHAR},
      </if>
      <if test="custstatus != null">
        #{custstatus,jdbcType=VARCHAR},
      </if>
      <if test="premiumAmount != null">
        #{premiumAmount,jdbcType=DECIMAL},
      </if>
      <if test="validity != null">
        #{validity,jdbcType=VARCHAR},
      </if>
      <if test="validitydt != null">
        #{validitydt,jdbcType=VARCHAR},
      </if>
      <if test="nature != null">
        #{nature,jdbcType=VARCHAR},
      </if>
      <if test="aptitude != null">
        #{aptitude,jdbcType=VARCHAR},
      </if>
      <if test="scopebusiness != null">
        #{scopebusiness,jdbcType=VARCHAR},
      </if>
      <if test="restype != null">
        #{restype,jdbcType=VARCHAR},
      </if>
      <if test="acregdt != null">
        #{acregdt,jdbcType=VARCHAR},
      </if>
      <if test="joinclubdt != null">
        #{joinclubdt,jdbcType=VARCHAR},
      </if>
      <if test="provcodeMobile != null">
        #{provcodeMobile,jdbcType=VARCHAR},
      </if>
      <if test="citycodeMobile != null">
        #{citycodeMobile,jdbcType=VARCHAR},
      </if>
      <if test="orgtype != null">
        #{orgtype,jdbcType=VARCHAR},
      </if>
      <if test="pinyin != null">
        #{pinyin,jdbcType=VARCHAR},
      </if>
      <if test="idnoDigest != null">
        #{idnoDigest,jdbcType=VARCHAR},
      </if>
      <if test="idnoMask != null">
        #{idnoMask,jdbcType=VARCHAR},
      </if>
      <if test="custnameDigest != null">
        #{custnameDigest,jdbcType=VARCHAR},
      </if>
      <if test="custnameMask != null">
        #{custnameMask,jdbcType=VARCHAR},
      </if>
      <if test="addrDigest != null">
        #{addrDigest,jdbcType=VARCHAR},
      </if>
      <if test="addrMask != null">
        #{addrMask,jdbcType=VARCHAR},
      </if>
      <if test="mobileDigest != null">
        #{mobileDigest,jdbcType=VARCHAR},
      </if>
      <if test="mobileMask != null">
        #{mobileMask,jdbcType=VARCHAR},
      </if>
      <if test="telnoDigest != null">
        #{telnoDigest,jdbcType=VARCHAR},
      </if>
      <if test="telnoMask != null">
        #{telnoMask,jdbcType=VARCHAR},
      </if>
      <if test="emailDigest != null">
        #{emailDigest,jdbcType=VARCHAR},
      </if>
      <if test="emailMask != null">
        #{emailMask,jdbcType=VARCHAR},
      </if>
      <if test="addr2Digest != null">
        #{addr2Digest,jdbcType=VARCHAR},
      </if>
      <if test="addr2Mask != null">
        #{addr2Mask,jdbcType=VARCHAR},
      </if>
      <if test="mobile2Digest != null">
        #{mobile2Digest,jdbcType=VARCHAR},
      </if>
      <if test="mobile2Mask != null">
        #{mobile2Mask,jdbcType=VARCHAR},
      </if>
      <if test="email2Digest != null">
        #{email2Digest,jdbcType=VARCHAR},
      </if>
      <if test="email2Mask != null">
        #{email2Mask,jdbcType=VARCHAR},
      </if>
      <if test="linkmanDigest != null">
        #{linkmanDigest,jdbcType=VARCHAR},
      </if>
      <if test="linkmanMask != null">
        #{linkmanMask,jdbcType=VARCHAR},
      </if>
      <if test="linktelDigest != null">
        #{linktelDigest,jdbcType=VARCHAR},
      </if>
      <if test="linktelMask != null">
        #{linktelMask,jdbcType=VARCHAR},
      </if>
      <if test="linkmobileDigest != null">
        #{linkmobileDigest,jdbcType=VARCHAR},
      </if>
      <if test="linkmobileMask != null">
        #{linkmobileMask,jdbcType=VARCHAR},
      </if>
      <if test="linkemailDigest != null">
        #{linkemailDigest,jdbcType=VARCHAR},
      </if>
      <if test="linkemailMask != null">
        #{linkemailMask,jdbcType=VARCHAR},
      </if>
      <if test="linkaddrDigest != null">
        #{linkaddrDigest,jdbcType=VARCHAR},
      </if>
      <if test="linkaddrMask != null">
        #{linkaddrMask,jdbcType=VARCHAR},
      </if>
      <if test="isvirtualsharer != null">
        #{isvirtualsharer,jdbcType=VARCHAR},
      </if>
      <if test="validityst != null">
        #{validityst,jdbcType=VARCHAR},
      </if>
      <if test="mobileAreaCode != null">
        #{mobileAreaCode,jdbcType=VARCHAR},
      </if>
      <if test="mobile2AreaCode != null">
        #{mobile2AreaCode,jdbcType=VARCHAR},
      </if>
      <if test="linkmobileAreaCode != null">
        #{linkmobileAreaCode,jdbcType=VARCHAR},
      </if>
      <if test="idSignAreaCode != null">
        #{idSignAreaCode,jdbcType=VARCHAR},
      </if>
      <if test="nationCode != null">
        #{nationCode,jdbcType=VARCHAR},
      </if>
      <if test="hboneTimestamp != null">
        #{hboneTimestamp,jdbcType=TIMESTAMP},
      </if>
      <if test="createTimestamp != null">
        #{createTimestamp,jdbcType=TIMESTAMP},
      </if>
      <if test="countyCode != null">
        #{countyCode,jdbcType=VARCHAR},
      </if>
    </trim>
  </insert>
  <update id="updateByPrimaryKeySelective" parameterType="com.howbuy.crm.account.dao.po.custinfo.CmConscustPO">
    <!--@mbg.generated-->
    update CM_CONSCUST
    <set>
      <if test="conscustlvl != null">
        CONSCUSTLVL = #{conscustlvl,jdbcType=VARCHAR},
      </if>
      <if test="conscustgrade != null">
        CONSCUSTGRADE = #{conscustgrade,jdbcType=DECIMAL},
      </if>
      <if test="conscuststatus != null">
        CONSCUSTSTATUS = #{conscuststatus,jdbcType=VARCHAR},
      </if>
      <if test="idtype != null">
        IDTYPE = #{idtype,jdbcType=VARCHAR},
      </if>
      <if test="custname != null">
        CUSTNAME = #{custname,jdbcType=VARCHAR},
      </if>
      <if test="provcode != null">
        PROVCODE = #{provcode,jdbcType=VARCHAR},
      </if>
      <if test="citycode != null">
        CITYCODE = #{citycode,jdbcType=VARCHAR},
      </if>
      <if test="edulevel != null">
        EDULEVEL = #{edulevel,jdbcType=VARCHAR},
      </if>
      <if test="vocation != null">
        VOCATION = #{vocation,jdbcType=VARCHAR},
      </if>
      <if test="inclevel != null">
        INCLEVEL = #{inclevel,jdbcType=VARCHAR},
      </if>
      <if test="birthday != null">
        BIRTHDAY = #{birthday,jdbcType=VARCHAR},
      </if>
      <if test="gender != null">
        GENDER = #{gender,jdbcType=VARCHAR},
      </if>
      <if test="married != null">
        MARRIED = #{married,jdbcType=VARCHAR},
      </if>
      <if test="pincome != null">
        PINCOME = #{pincome,jdbcType=VARCHAR},
      </if>
      <if test="fincome != null">
        FINCOME = #{fincome,jdbcType=VARCHAR},
      </if>
      <if test="decisionflag != null">
        DECISIONFLAG = #{decisionflag,jdbcType=VARCHAR},
      </if>
      <if test="interests != null">
        INTERESTS = #{interests,jdbcType=VARCHAR},
      </if>
      <if test="familycondition != null">
        FAMILYCONDITION = #{familycondition,jdbcType=VARCHAR},
      </if>
      <if test="contacttime != null">
        CONTACTTIME = #{contacttime,jdbcType=VARCHAR},
      </if>
      <if test="sendinfoflag != null">
        SENDINFOFLAG = #{sendinfoflag,jdbcType=VARCHAR},
      </if>
      <if test="recvtelflag != null">
        RECVTELFLAG = #{recvtelflag,jdbcType=VARCHAR},
      </if>
      <if test="recvemailflag != null">
        RECVEMAILFLAG = #{recvemailflag,jdbcType=VARCHAR},
      </if>
      <if test="recvmsgflag != null">
        RECVMSGFLAG = #{recvmsgflag,jdbcType=VARCHAR},
      </if>
      <if test="company != null">
        COMPANY = #{company,jdbcType=VARCHAR},
      </if>
      <if test="risklevel != null">
        RISKLEVEL = #{risklevel,jdbcType=VARCHAR},
      </if>
      <if test="selfrisklevel != null">
        SELFRISKLEVEL = #{selfrisklevel,jdbcType=VARCHAR},
      </if>
      <if test="postcode != null">
        POSTCODE = #{postcode,jdbcType=VARCHAR},
      </if>
      <if test="fax != null">
        FAX = #{fax,jdbcType=VARCHAR},
      </if>
      <if test="officetelno != null">
        OFFICETELNO = #{officetelno,jdbcType=VARCHAR},
      </if>
      <if test="source != null">
        "SOURCE" = #{source,jdbcType=VARCHAR},
      </if>
      <if test="knowchan != null">
        KNOWCHAN = #{knowchan,jdbcType=VARCHAR},
      </if>
      <if test="otherchan != null">
        OTHERCHAN = #{otherchan,jdbcType=VARCHAR},
      </if>
      <if test="otherinvest != null">
        OTHERINVEST = #{otherinvest,jdbcType=VARCHAR},
      </if>
      <if test="salon != null">
        SALON = #{salon,jdbcType=VARCHAR},
      </if>
      <if test="beforeinvest != null">
        BEFOREINVEST = #{beforeinvest,jdbcType=VARCHAR},
      </if>
      <if test="selfdefflag != null">
        SELFDEFFLAG = #{selfdefflag,jdbcType=VARCHAR},
      </if>
      <if test="visitfqcy != null">
        VISITFQCY = #{visitfqcy,jdbcType=VARCHAR},
      </if>
      <if test="devdirection != null">
        DEVDIRECTION = #{devdirection,jdbcType=VARCHAR},
      </if>
      <if test="saledirection != null">
        SALEDIRECTION = #{saledirection,jdbcType=VARCHAR},
      </if>
      <if test="subsource != null">
        SUBSOURCE = #{subsource,jdbcType=VARCHAR},
      </if>
      <if test="subsourcetype != null">
        SUBSOURCETYPE = #{subsourcetype,jdbcType=VARCHAR},
      </if>
      <if test="postcode2 != null">
        POSTCODE2 = #{postcode2,jdbcType=VARCHAR},
      </if>
      <if test="knowhowbuy != null">
        KNOWHOWBUY = #{knowhowbuy,jdbcType=VARCHAR},
      </if>
      <if test="subknow != null">
        SUBKNOW = #{subknow,jdbcType=VARCHAR},
      </if>
      <if test="subknowtype != null">
        SUBKNOWTYPE = #{subknowtype,jdbcType=VARCHAR},
      </if>
      <if test="buyingprod != null">
        BUYINGPROD = #{buyingprod,jdbcType=VARCHAR},
      </if>
      <if test="buyedprod != null">
        BUYEDPROD = #{buyedprod,jdbcType=VARCHAR},
      </if>
      <if test="specialflag != null">
        SPECIALFLAG = #{specialflag,jdbcType=VARCHAR},
      </if>
      <if test="dlvymode != null">
        DLVYMODE = #{dlvymode,jdbcType=VARCHAR},
      </if>
      <if test="remark != null">
        REMARK = #{remark,jdbcType=VARCHAR},
      </if>
      <if test="regdt != null">
        REGDT = #{regdt,jdbcType=VARCHAR},
      </if>
      <if test="uddt != null">
        UDDT = #{uddt,jdbcType=VARCHAR},
      </if>
      <if test="pririsklevel != null">
        PRIRISKLEVEL = #{pririsklevel,jdbcType=VARCHAR},
      </if>
      <if test="linkman != null">
        LINKMAN = #{linkman,jdbcType=VARCHAR},
      </if>
      <if test="linkpostcode != null">
        LINKPOSTCODE = #{linkpostcode,jdbcType=VARCHAR},
      </if>
      <if test="capacity != null">
        CAPACITY = #{capacity,jdbcType=VARCHAR},
      </if>
      <if test="gpsinvestlevel != null">
        GPSINVESTLEVEL = #{gpsinvestlevel,jdbcType=VARCHAR},
      </if>
      <if test="gpsrisklevel != null">
        GPSRISKLEVEL = #{gpsrisklevel,jdbcType=VARCHAR},
      </if>
      <if test="isboss != null">
        ISBOSS = #{isboss,jdbcType=VARCHAR},
      </if>
      <if test="financeneed != null">
        FINANCENEED = #{financeneed,jdbcType=VARCHAR},
      </if>
      <if test="isjoinclub != null">
        ISJOINCLUB = #{isjoinclub,jdbcType=VARCHAR},
      </if>
      <if test="isrisktip != null">
        ISRISKTIP = #{isrisktip,jdbcType=VARCHAR},
      </if>
      <if test="custsourceremark != null">
        CUSTSOURCEREMARK = #{custsourceremark,jdbcType=VARCHAR},
      </if>
      <if test="pmarketamt != null">
        PMARKETAMT = #{pmarketamt,jdbcType=DECIMAL},
      </if>
      <if test="iswritebook != null">
        ISWRITEBOOK = #{iswritebook,jdbcType=VARCHAR},
      </if>
      <if test="latesttradedt != null">
        LATESTTRADEDT = #{latesttradedt,jdbcType=VARCHAR},
      </if>
      <if test="rstohighreason != null">
        RSTOHIGHREASON = #{rstohighreason,jdbcType=VARCHAR},
      </if>
      <if test="ispubtrade != null">
        ISPUBTRADE = #{ispubtrade,jdbcType=VARCHAR},
      </if>
      <if test="invsttype != null">
        INVSTTYPE = #{invsttype,jdbcType=VARCHAR},
      </if>
      <if test="source2 != null">
        SOURCE2 = #{source2,jdbcType=VARCHAR},
      </if>
      <if test="subsource2 != null">
        SUBSOURCE2 = #{subsource2,jdbcType=VARCHAR},
      </if>
      <if test="subsourcetype2 != null">
        SUBSOURCETYPE2 = #{subsourcetype2,jdbcType=VARCHAR},
      </if>
      <if test="vipusername != null">
        VIPUSERNAME = #{vipusername,jdbcType=VARCHAR},
      </if>
      <if test="wechatcode != null">
        WECHATCODE = #{wechatcode,jdbcType=VARCHAR},
      </if>
      <if test="newsourceno != null">
        NEWSOURCENO = #{newsourceno,jdbcType=VARCHAR},
      </if>
      <if test="hboneNo != null">
        HBONE_NO = #{hboneNo,jdbcType=VARCHAR},
      </if>
      <if test="newsourceno2 != null">
        NEWSOURCENO2 = #{newsourceno2,jdbcType=VARCHAR},
      </if>
      <if test="mergedt != null">
        MERGEDT = #{mergedt,jdbcType=VARCHAR},
      </if>
      <if test="hopetradetype != null">
        HOPETRADETYPE = #{hopetradetype,jdbcType=VARCHAR},
      </if>
      <if test="custstatus != null">
        CUSTSTATUS = #{custstatus,jdbcType=VARCHAR},
      </if>
      <if test="premiumAmount != null">
        PREMIUM_AMOUNT = #{premiumAmount,jdbcType=DECIMAL},
      </if>
      <if test="validity != null">
        VALIDITY = #{validity,jdbcType=VARCHAR},
      </if>
      <if test="validitydt != null">
        VALIDITYDT = #{validitydt,jdbcType=VARCHAR},
      </if>
      <if test="nature != null">
        NATURE = #{nature,jdbcType=VARCHAR},
      </if>
      <if test="aptitude != null">
        APTITUDE = #{aptitude,jdbcType=VARCHAR},
      </if>
      <if test="scopebusiness != null">
        SCOPEBUSINESS = #{scopebusiness,jdbcType=VARCHAR},
      </if>
      <if test="restype != null">
        RESTYPE = #{restype,jdbcType=VARCHAR},
      </if>
      <if test="acregdt != null">
        ACREGDT = #{acregdt,jdbcType=VARCHAR},
      </if>
      <if test="joinclubdt != null">
        JOINCLUBDT = #{joinclubdt,jdbcType=VARCHAR},
      </if>
      <if test="provcodeMobile != null">
        PROVCODE_MOBILE = #{provcodeMobile,jdbcType=VARCHAR},
      </if>
      <if test="citycodeMobile != null">
        CITYCODE_MOBILE = #{citycodeMobile,jdbcType=VARCHAR},
      </if>
      <if test="orgtype != null">
        ORGTYPE = #{orgtype,jdbcType=VARCHAR},
      </if>
      <if test="pinyin != null">
        PINYIN = #{pinyin,jdbcType=VARCHAR},
      </if>
      <if test="idnoDigest != null">
        IDNO_DIGEST = #{idnoDigest,jdbcType=VARCHAR},
      </if>
      <if test="idnoMask != null">
        IDNO_MASK = #{idnoMask,jdbcType=VARCHAR},
      </if>
      <if test="custnameDigest != null">
        CUSTNAME_DIGEST = #{custnameDigest,jdbcType=VARCHAR},
      </if>
      <if test="custnameMask != null">
        CUSTNAME_MASK = #{custnameMask,jdbcType=VARCHAR},
      </if>
      <if test="addrDigest != null">
        ADDR_DIGEST = #{addrDigest,jdbcType=VARCHAR},
      </if>
      <if test="addrMask != null">
        ADDR_MASK = #{addrMask,jdbcType=VARCHAR},
      </if>
      <if test="mobileDigest != null">
        MOBILE_DIGEST = #{mobileDigest,jdbcType=VARCHAR},
      </if>
      <if test="mobileMask != null">
        MOBILE_MASK = #{mobileMask,jdbcType=VARCHAR},
      </if>
      <if test="telnoDigest != null">
        TELNO_DIGEST = #{telnoDigest,jdbcType=VARCHAR},
      </if>
      <if test="telnoMask != null">
        TELNO_MASK = #{telnoMask,jdbcType=VARCHAR},
      </if>
      <if test="emailDigest != null">
        EMAIL_DIGEST = #{emailDigest,jdbcType=VARCHAR},
      </if>
      <if test="emailMask != null">
        EMAIL_MASK = #{emailMask,jdbcType=VARCHAR},
      </if>
      <if test="addr2Digest != null">
        ADDR2_DIGEST = #{addr2Digest,jdbcType=VARCHAR},
      </if>
      <if test="addr2Mask != null">
        ADDR2_MASK = #{addr2Mask,jdbcType=VARCHAR},
      </if>
      <if test="mobile2Digest != null">
        MOBILE2_DIGEST = #{mobile2Digest,jdbcType=VARCHAR},
      </if>
      <if test="mobile2Mask != null">
        MOBILE2_MASK = #{mobile2Mask,jdbcType=VARCHAR},
      </if>
      <if test="email2Digest != null">
        EMAIL2_DIGEST = #{email2Digest,jdbcType=VARCHAR},
      </if>
      <if test="email2Mask != null">
        EMAIL2_MASK = #{email2Mask,jdbcType=VARCHAR},
      </if>
      <if test="linkmanDigest != null">
        LINKMAN_DIGEST = #{linkmanDigest,jdbcType=VARCHAR},
      </if>
      <if test="linkmanMask != null">
        LINKMAN_MASK = #{linkmanMask,jdbcType=VARCHAR},
      </if>
      <if test="linktelDigest != null">
        LINKTEL_DIGEST = #{linktelDigest,jdbcType=VARCHAR},
      </if>
      <if test="linktelMask != null">
        LINKTEL_MASK = #{linktelMask,jdbcType=VARCHAR},
      </if>
      <if test="linkmobileDigest != null">
        LINKMOBILE_DIGEST = #{linkmobileDigest,jdbcType=VARCHAR},
      </if>
      <if test="linkmobileMask != null">
        LINKMOBILE_MASK = #{linkmobileMask,jdbcType=VARCHAR},
      </if>
      <if test="linkemailDigest != null">
        LINKEMAIL_DIGEST = #{linkemailDigest,jdbcType=VARCHAR},
      </if>
      <if test="linkemailMask != null">
        LINKEMAIL_MASK = #{linkemailMask,jdbcType=VARCHAR},
      </if>
      <if test="linkaddrDigest != null">
        LINKADDR_DIGEST = #{linkaddrDigest,jdbcType=VARCHAR},
      </if>
      <if test="linkaddrMask != null">
        LINKADDR_MASK = #{linkaddrMask,jdbcType=VARCHAR},
      </if>
      <if test="isvirtualsharer != null">
        ISVIRTUALSHARER = #{isvirtualsharer,jdbcType=VARCHAR},
      </if>
      <if test="validityst != null">
        VALIDITYST = #{validityst,jdbcType=VARCHAR},
      </if>
      <if test="mobileAreaCode != null">
        MOBILE_AREA_CODE = #{mobileAreaCode,jdbcType=VARCHAR},
      </if>
      <if test="mobile2AreaCode != null">
        MOBILE2_AREA_CODE = #{mobile2AreaCode,jdbcType=VARCHAR},
      </if>
      <if test="linkmobileAreaCode != null">
        LINKMOBILE_AREA_CODE = #{linkmobileAreaCode,jdbcType=VARCHAR},
      </if>
      <if test="idSignAreaCode != null">
        ID_SIGN_AREA_CODE = #{idSignAreaCode,jdbcType=VARCHAR},
      </if>
      <if test="nationCode != null">
        NATION_CODE = #{nationCode,jdbcType=VARCHAR},
      </if>
      <if test="hboneTimestamp != null">
        HBONE_TIMESTAMP = #{hboneTimestamp,jdbcType=TIMESTAMP},
      </if>
      <if test="createTimestamp != null">
        CREATE_TIMESTAMP = #{createTimestamp,jdbcType=TIMESTAMP},
      </if>
      <if test="countyCode != null">
        COUNTY_CODE = #{countyCode,jdbcType=VARCHAR},
      </if>
    </set>
    where CONSCUSTNO = #{conscustno,jdbcType=VARCHAR}
  </update>
  <update id="updateByPrimaryKey" parameterType="com.howbuy.crm.account.dao.po.custinfo.CmConscustPO">
    <!--@mbg.generated-->
    update CM_CONSCUST
    set CONSCUSTLVL = #{conscustlvl,jdbcType=VARCHAR},
    CONSCUSTGRADE = #{conscustgrade,jdbcType=DECIMAL},
    CONSCUSTSTATUS = #{conscuststatus,jdbcType=VARCHAR},
    IDTYPE = #{idtype,jdbcType=VARCHAR},
    CUSTNAME = #{custname,jdbcType=VARCHAR},
    PROVCODE = #{provcode,jdbcType=VARCHAR},
    CITYCODE = #{citycode,jdbcType=VARCHAR},
    EDULEVEL = #{edulevel,jdbcType=VARCHAR},
    VOCATION = #{vocation,jdbcType=VARCHAR},
    INCLEVEL = #{inclevel,jdbcType=VARCHAR},
    BIRTHDAY = #{birthday,jdbcType=VARCHAR},
    GENDER = #{gender,jdbcType=VARCHAR},
    MARRIED = #{married,jdbcType=VARCHAR},
    PINCOME = #{pincome,jdbcType=VARCHAR},
    FINCOME = #{fincome,jdbcType=VARCHAR},
    DECISIONFLAG = #{decisionflag,jdbcType=VARCHAR},
    INTERESTS = #{interests,jdbcType=VARCHAR},
    FAMILYCONDITION = #{familycondition,jdbcType=VARCHAR},
    CONTACTTIME = #{contacttime,jdbcType=VARCHAR},
    SENDINFOFLAG = #{sendinfoflag,jdbcType=VARCHAR},
    RECVTELFLAG = #{recvtelflag,jdbcType=VARCHAR},
    RECVEMAILFLAG = #{recvemailflag,jdbcType=VARCHAR},
    RECVMSGFLAG = #{recvmsgflag,jdbcType=VARCHAR},
    COMPANY = #{company,jdbcType=VARCHAR},
    RISKLEVEL = #{risklevel,jdbcType=VARCHAR},
    SELFRISKLEVEL = #{selfrisklevel,jdbcType=VARCHAR},
    POSTCODE = #{postcode,jdbcType=VARCHAR},
    FAX = #{fax,jdbcType=VARCHAR},
    OFFICETELNO = #{officetelno,jdbcType=VARCHAR},
    "SOURCE" = #{source,jdbcType=VARCHAR},
    KNOWCHAN = #{knowchan,jdbcType=VARCHAR},
    OTHERCHAN = #{otherchan,jdbcType=VARCHAR},
    OTHERINVEST = #{otherinvest,jdbcType=VARCHAR},
    SALON = #{salon,jdbcType=VARCHAR},
    BEFOREINVEST = #{beforeinvest,jdbcType=VARCHAR},
    SELFDEFFLAG = #{selfdefflag,jdbcType=VARCHAR},
    VISITFQCY = #{visitfqcy,jdbcType=VARCHAR},
    DEVDIRECTION = #{devdirection,jdbcType=VARCHAR},
    SALEDIRECTION = #{saledirection,jdbcType=VARCHAR},
    SUBSOURCE = #{subsource,jdbcType=VARCHAR},
    SUBSOURCETYPE = #{subsourcetype,jdbcType=VARCHAR},
    POSTCODE2 = #{postcode2,jdbcType=VARCHAR},
    KNOWHOWBUY = #{knowhowbuy,jdbcType=VARCHAR},
    SUBKNOW = #{subknow,jdbcType=VARCHAR},
    SUBKNOWTYPE = #{subknowtype,jdbcType=VARCHAR},
    BUYINGPROD = #{buyingprod,jdbcType=VARCHAR},
    BUYEDPROD = #{buyedprod,jdbcType=VARCHAR},
    SPECIALFLAG = #{specialflag,jdbcType=VARCHAR},
    DLVYMODE = #{dlvymode,jdbcType=VARCHAR},
    REMARK = #{remark,jdbcType=VARCHAR},
    REGDT = #{regdt,jdbcType=VARCHAR},
    UDDT = #{uddt,jdbcType=VARCHAR},
    PRIRISKLEVEL = #{pririsklevel,jdbcType=VARCHAR},
    LINKMAN = #{linkman,jdbcType=VARCHAR},
    LINKPOSTCODE = #{linkpostcode,jdbcType=VARCHAR},
    CAPACITY = #{capacity,jdbcType=VARCHAR},
    GPSINVESTLEVEL = #{gpsinvestlevel,jdbcType=VARCHAR},
    GPSRISKLEVEL = #{gpsrisklevel,jdbcType=VARCHAR},
    ISBOSS = #{isboss,jdbcType=VARCHAR},
    FINANCENEED = #{financeneed,jdbcType=VARCHAR},
    ISJOINCLUB = #{isjoinclub,jdbcType=VARCHAR},
    ISRISKTIP = #{isrisktip,jdbcType=VARCHAR},
    CUSTSOURCEREMARK = #{custsourceremark,jdbcType=VARCHAR},
    PMARKETAMT = #{pmarketamt,jdbcType=DECIMAL},
    ISWRITEBOOK = #{iswritebook,jdbcType=VARCHAR},
    LATESTTRADEDT = #{latesttradedt,jdbcType=VARCHAR},
    RSTOHIGHREASON = #{rstohighreason,jdbcType=VARCHAR},
    ISPUBTRADE = #{ispubtrade,jdbcType=VARCHAR},
    INVSTTYPE = #{invsttype,jdbcType=VARCHAR},
    SOURCE2 = #{source2,jdbcType=VARCHAR},
    SUBSOURCE2 = #{subsource2,jdbcType=VARCHAR},
    SUBSOURCETYPE2 = #{subsourcetype2,jdbcType=VARCHAR},
    VIPUSERNAME = #{vipusername,jdbcType=VARCHAR},
    WECHATCODE = #{wechatcode,jdbcType=VARCHAR},
    NEWSOURCENO = #{newsourceno,jdbcType=VARCHAR},
    HBONE_NO = #{hboneNo,jdbcType=VARCHAR},
    NEWSOURCENO2 = #{newsourceno2,jdbcType=VARCHAR},
    MERGEDT = #{mergedt,jdbcType=VARCHAR},
    HOPETRADETYPE = #{hopetradetype,jdbcType=VARCHAR},
    CUSTSTATUS = #{custstatus,jdbcType=VARCHAR},
    PREMIUM_AMOUNT = #{premiumAmount,jdbcType=DECIMAL},
    VALIDITY = #{validity,jdbcType=VARCHAR},
    VALIDITYDT = #{validitydt,jdbcType=VARCHAR},
    NATURE = #{nature,jdbcType=VARCHAR},
    APTITUDE = #{aptitude,jdbcType=VARCHAR},
    SCOPEBUSINESS = #{scopebusiness,jdbcType=VARCHAR},
    RESTYPE = #{restype,jdbcType=VARCHAR},
    ACREGDT = #{acregdt,jdbcType=VARCHAR},
    JOINCLUBDT = #{joinclubdt,jdbcType=VARCHAR},
    PROVCODE_MOBILE = #{provcodeMobile,jdbcType=VARCHAR},
    CITYCODE_MOBILE = #{citycodeMobile,jdbcType=VARCHAR},
    ORGTYPE = #{orgtype,jdbcType=VARCHAR},
    PINYIN = #{pinyin,jdbcType=VARCHAR},
    IDNO_DIGEST = #{idnoDigest,jdbcType=VARCHAR},
    IDNO_MASK = #{idnoMask,jdbcType=VARCHAR},
    CUSTNAME_DIGEST = #{custnameDigest,jdbcType=VARCHAR},
    CUSTNAME_MASK = #{custnameMask,jdbcType=VARCHAR},
    ADDR_DIGEST = #{addrDigest,jdbcType=VARCHAR},
    ADDR_MASK = #{addrMask,jdbcType=VARCHAR},
    MOBILE_DIGEST = #{mobileDigest,jdbcType=VARCHAR},
    MOBILE_MASK = #{mobileMask,jdbcType=VARCHAR},
    TELNO_DIGEST = #{telnoDigest,jdbcType=VARCHAR},
    TELNO_MASK = #{telnoMask,jdbcType=VARCHAR},
    EMAIL_DIGEST = #{emailDigest,jdbcType=VARCHAR},
    EMAIL_MASK = #{emailMask,jdbcType=VARCHAR},
    ADDR2_DIGEST = #{addr2Digest,jdbcType=VARCHAR},
    ADDR2_MASK = #{addr2Mask,jdbcType=VARCHAR},
    MOBILE2_DIGEST = #{mobile2Digest,jdbcType=VARCHAR},
    MOBILE2_MASK = #{mobile2Mask,jdbcType=VARCHAR},
    EMAIL2_DIGEST = #{email2Digest,jdbcType=VARCHAR},
    EMAIL2_MASK = #{email2Mask,jdbcType=VARCHAR},
    LINKMAN_DIGEST = #{linkmanDigest,jdbcType=VARCHAR},
    LINKMAN_MASK = #{linkmanMask,jdbcType=VARCHAR},
    LINKTEL_DIGEST = #{linktelDigest,jdbcType=VARCHAR},
    LINKTEL_MASK = #{linktelMask,jdbcType=VARCHAR},
    LINKMOBILE_DIGEST = #{linkmobileDigest,jdbcType=VARCHAR},
    LINKMOBILE_MASK = #{linkmobileMask,jdbcType=VARCHAR},
    LINKEMAIL_DIGEST = #{linkemailDigest,jdbcType=VARCHAR},
    LINKEMAIL_MASK = #{linkemailMask,jdbcType=VARCHAR},
    LINKADDR_DIGEST = #{linkaddrDigest,jdbcType=VARCHAR},
    LINKADDR_MASK = #{linkaddrMask,jdbcType=VARCHAR},
    ISVIRTUALSHARER = #{isvirtualsharer,jdbcType=VARCHAR},
    VALIDITYST = #{validityst,jdbcType=VARCHAR},
    MOBILE_AREA_CODE = #{mobileAreaCode,jdbcType=VARCHAR},
    MOBILE2_AREA_CODE = #{mobile2AreaCode,jdbcType=VARCHAR},
    LINKMOBILE_AREA_CODE = #{linkmobileAreaCode,jdbcType=VARCHAR},
    ID_SIGN_AREA_CODE = #{idSignAreaCode,jdbcType=VARCHAR},
    NATION_CODE = #{nationCode,jdbcType=VARCHAR},
    HBONE_TIMESTAMP = #{hboneTimestamp,jdbcType=TIMESTAMP},
    CREATE_TIMESTAMP = #{createTimestamp,jdbcType=TIMESTAMP},
    COUNTY_CODE = #{countyCode,jdbcType=VARCHAR}
    where CONSCUSTNO = #{conscustno,jdbcType=VARCHAR}
  </update>

  <update id="upHboneNoWhenNull">
    UPDATE CM_CONSCUST T
    <set>
      T.HBONE_NO=#{newHboneNo,jdbcType=VARCHAR}
    </set>
    where conscustno = #{custNo,jdbcType=VARCHAR}
      AND T.HBONE_NO IS NULL
  </update>

  <update id="upHboneNoToNull">
    UPDATE CM_CONSCUST T
    <set>
      T.HBONE_NO=NULL
    </set>
    where T.conscustno = #{custNo,jdbcType=VARCHAR}
      AND T.HBONE_NO = #{hboneNo,jdbcType=VARCHAR}
  </update>

  <select id="queryCustByHboneNo" resultMap="BaseResultMap">
    select
    <include refid="Base_Column_List"/>
    FROM CM_CONSCUST
    WHERE HBONE_NO = #{hboneNo,jdbcType=VARCHAR}
    AND CONSCUSTSTATUS = '0'
  </select>

  <select id="querySimpleConsCustList" resultType="com.howbuy.crm.account.dao.bo.consultant.SimpleConsCustDTO">
    select
    CONSCUSTNO AS consCustNo,
    CUSTNAME AS custName
    FROM CM_CONSCUST
    WHERE CONSCUSTNO in
      <foreach item="item" collection="list" separator="," open="(" close=")">
         #{item,jdbcType=VARCHAR}
      </foreach>
  </select>

</mapper>