<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.howbuy.crm.account.dao.mapper.constant.HbConstantMapper">
  <resultMap id="BaseResultMap" type="com.howbuy.crm.account.dao.po.constant.HbConstantPO">
    <!--@mbg.generated-->
    <!--@Table HB_CONSTANT-->
    <id column="ID" jdbcType="VARCHAR" property="id" />
    <result column="TYPECODE" jdbcType="VARCHAR" property="typecode" />
    <result column="TYPEDESC" jdbcType="VARCHAR" property="typedesc" />
    <result column="CODEDESC" jdbcType="VARCHAR" property="codedesc" />
    <result column="CONSTCODE" jdbcType="VARCHAR" property="constcode" />
    <result column="CONSTDESC" jdbcType="VARCHAR" property="constdesc" />
    <result column="CONSTLEVEL" jdbcType="DECIMAL" property="constlevel" />
    <result column="ISVALID" jdbcType="CHAR" property="isvalid" />
    <result column="CONSTALIAS" jdbcType="VARCHAR" property="constalias" />
    <result column="CONSTEXT1" jdbcType="VARCHAR" property="constext1" />
    <result column="CONSTEXT2" jdbcType="VARCHAR" property="constext2" />
    <result column="CONSTEXT3" jdbcType="VARCHAR" property="constext3" />
    <result column="CREATOR" jdbcType="VARCHAR" property="creator" />
    <result column="MODIFIER" jdbcType="VARCHAR" property="modifier" />
    <result column="CHECKER" jdbcType="VARCHAR" property="checker" />
    <result column="CREDATE" jdbcType="TIMESTAMP" property="credate" />
    <result column="MODDATE" jdbcType="TIMESTAMP" property="moddate" />
    <result column="CHECKDATE" jdbcType="TIMESTAMP" property="checkdate" />
    <result column="UPDATEID" jdbcType="VARCHAR" property="updateid" />
    <result column="RESOURCEID" jdbcType="VARCHAR" property="resourceid" />
  </resultMap>
  <sql id="Base_Column_List">
    <!--@mbg.generated-->
    ID, TYPECODE, TYPEDESC, CODEDESC, CONSTCODE, CONSTDESC, CONSTLEVEL, ISVALID, CONSTALIAS, 
    CONSTEXT1, CONSTEXT2, CONSTEXT3, CREATOR, MODIFIER, CHECKER, CREDATE, MODDATE, CHECKDATE, 
    UPDATEID, RESOURCEID
  </sql>
  <select id="selectByPrimaryKey" parameterType="java.lang.String" resultMap="BaseResultMap">
    <!--@mbg.generated-->
    select 
    <include refid="Base_Column_List" />
    from HB_CONSTANT
    where ID = #{id,jdbcType=VARCHAR}
  </select>
  <delete id="deleteByPrimaryKey" parameterType="java.lang.String">
    <!--@mbg.generated-->
    delete from HB_CONSTANT
    where ID = #{id,jdbcType=VARCHAR}
  </delete>
  <insert id="insert" parameterType="com.howbuy.crm.account.dao.po.constant.HbConstantPO">
    <!--@mbg.generated-->
    insert into HB_CONSTANT (ID, TYPECODE, TYPEDESC, 
      CODEDESC, CONSTCODE, CONSTDESC, 
      CONSTLEVEL, ISVALID, CONSTALIAS, 
      CONSTEXT1, CONSTEXT2, CONSTEXT3, 
      CREATOR, MODIFIER, CHECKER, 
      CREDATE, MODDATE, CHECKDATE, 
      UPDATEID, RESOURCEID)
    values (#{id,jdbcType=VARCHAR}, #{typecode,jdbcType=VARCHAR}, #{typedesc,jdbcType=VARCHAR}, 
      #{codedesc,jdbcType=VARCHAR}, #{constcode,jdbcType=VARCHAR}, #{constdesc,jdbcType=VARCHAR}, 
      #{constlevel,jdbcType=DECIMAL}, #{isvalid,jdbcType=CHAR}, #{constalias,jdbcType=VARCHAR}, 
      #{constext1,jdbcType=VARCHAR}, #{constext2,jdbcType=VARCHAR}, #{constext3,jdbcType=VARCHAR}, 
      #{creator,jdbcType=VARCHAR}, #{modifier,jdbcType=VARCHAR}, #{checker,jdbcType=VARCHAR}, 
      #{credate,jdbcType=TIMESTAMP}, #{moddate,jdbcType=TIMESTAMP}, #{checkdate,jdbcType=TIMESTAMP}, 
      #{updateid,jdbcType=VARCHAR}, #{resourceid,jdbcType=VARCHAR})
  </insert>
  <insert id="insertSelective" parameterType="com.howbuy.crm.account.dao.po.constant.HbConstantPO">
    <!--@mbg.generated-->
    insert into HB_CONSTANT
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="id != null">
        ID,
      </if>
      <if test="typecode != null">
        TYPECODE,
      </if>
      <if test="typedesc != null">
        TYPEDESC,
      </if>
      <if test="codedesc != null">
        CODEDESC,
      </if>
      <if test="constcode != null">
        CONSTCODE,
      </if>
      <if test="constdesc != null">
        CONSTDESC,
      </if>
      <if test="constlevel != null">
        CONSTLEVEL,
      </if>
      <if test="isvalid != null">
        ISVALID,
      </if>
      <if test="constalias != null">
        CONSTALIAS,
      </if>
      <if test="constext1 != null">
        CONSTEXT1,
      </if>
      <if test="constext2 != null">
        CONSTEXT2,
      </if>
      <if test="constext3 != null">
        CONSTEXT3,
      </if>
      <if test="creator != null">
        CREATOR,
      </if>
      <if test="modifier != null">
        MODIFIER,
      </if>
      <if test="checker != null">
        CHECKER,
      </if>
      <if test="credate != null">
        CREDATE,
      </if>
      <if test="moddate != null">
        MODDATE,
      </if>
      <if test="checkdate != null">
        CHECKDATE,
      </if>
      <if test="updateid != null">
        UPDATEID,
      </if>
      <if test="resourceid != null">
        RESOURCEID,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="id != null">
        #{id,jdbcType=VARCHAR},
      </if>
      <if test="typecode != null">
        #{typecode,jdbcType=VARCHAR},
      </if>
      <if test="typedesc != null">
        #{typedesc,jdbcType=VARCHAR},
      </if>
      <if test="codedesc != null">
        #{codedesc,jdbcType=VARCHAR},
      </if>
      <if test="constcode != null">
        #{constcode,jdbcType=VARCHAR},
      </if>
      <if test="constdesc != null">
        #{constdesc,jdbcType=VARCHAR},
      </if>
      <if test="constlevel != null">
        #{constlevel,jdbcType=DECIMAL},
      </if>
      <if test="isvalid != null">
        #{isvalid,jdbcType=CHAR},
      </if>
      <if test="constalias != null">
        #{constalias,jdbcType=VARCHAR},
      </if>
      <if test="constext1 != null">
        #{constext1,jdbcType=VARCHAR},
      </if>
      <if test="constext2 != null">
        #{constext2,jdbcType=VARCHAR},
      </if>
      <if test="constext3 != null">
        #{constext3,jdbcType=VARCHAR},
      </if>
      <if test="creator != null">
        #{creator,jdbcType=VARCHAR},
      </if>
      <if test="modifier != null">
        #{modifier,jdbcType=VARCHAR},
      </if>
      <if test="checker != null">
        #{checker,jdbcType=VARCHAR},
      </if>
      <if test="credate != null">
        #{credate,jdbcType=TIMESTAMP},
      </if>
      <if test="moddate != null">
        #{moddate,jdbcType=TIMESTAMP},
      </if>
      <if test="checkdate != null">
        #{checkdate,jdbcType=TIMESTAMP},
      </if>
      <if test="updateid != null">
        #{updateid,jdbcType=VARCHAR},
      </if>
      <if test="resourceid != null">
        #{resourceid,jdbcType=VARCHAR},
      </if>
    </trim>
  </insert>
  <update id="updateByPrimaryKeySelective" parameterType="com.howbuy.crm.account.dao.po.constant.HbConstantPO">
    <!--@mbg.generated-->
    update HB_CONSTANT
    <set>
      <if test="typecode != null">
        TYPECODE = #{typecode,jdbcType=VARCHAR},
      </if>
      <if test="typedesc != null">
        TYPEDESC = #{typedesc,jdbcType=VARCHAR},
      </if>
      <if test="codedesc != null">
        CODEDESC = #{codedesc,jdbcType=VARCHAR},
      </if>
      <if test="constcode != null">
        CONSTCODE = #{constcode,jdbcType=VARCHAR},
      </if>
      <if test="constdesc != null">
        CONSTDESC = #{constdesc,jdbcType=VARCHAR},
      </if>
      <if test="constlevel != null">
        CONSTLEVEL = #{constlevel,jdbcType=DECIMAL},
      </if>
      <if test="isvalid != null">
        ISVALID = #{isvalid,jdbcType=CHAR},
      </if>
      <if test="constalias != null">
        CONSTALIAS = #{constalias,jdbcType=VARCHAR},
      </if>
      <if test="constext1 != null">
        CONSTEXT1 = #{constext1,jdbcType=VARCHAR},
      </if>
      <if test="constext2 != null">
        CONSTEXT2 = #{constext2,jdbcType=VARCHAR},
      </if>
      <if test="constext3 != null">
        CONSTEXT3 = #{constext3,jdbcType=VARCHAR},
      </if>
      <if test="creator != null">
        CREATOR = #{creator,jdbcType=VARCHAR},
      </if>
      <if test="modifier != null">
        MODIFIER = #{modifier,jdbcType=VARCHAR},
      </if>
      <if test="checker != null">
        CHECKER = #{checker,jdbcType=VARCHAR},
      </if>
      <if test="credate != null">
        CREDATE = #{credate,jdbcType=TIMESTAMP},
      </if>
      <if test="moddate != null">
        MODDATE = #{moddate,jdbcType=TIMESTAMP},
      </if>
      <if test="checkdate != null">
        CHECKDATE = #{checkdate,jdbcType=TIMESTAMP},
      </if>
      <if test="updateid != null">
        UPDATEID = #{updateid,jdbcType=VARCHAR},
      </if>
      <if test="resourceid != null">
        RESOURCEID = #{resourceid,jdbcType=VARCHAR},
      </if>
    </set>
    where ID = #{id,jdbcType=VARCHAR}
  </update>
  <update id="updateByPrimaryKey" parameterType="com.howbuy.crm.account.dao.po.constant.HbConstantPO">
    <!--@mbg.generated-->
    update HB_CONSTANT
    set TYPECODE = #{typecode,jdbcType=VARCHAR},
      TYPEDESC = #{typedesc,jdbcType=VARCHAR},
      CODEDESC = #{codedesc,jdbcType=VARCHAR},
      CONSTCODE = #{constcode,jdbcType=VARCHAR},
      CONSTDESC = #{constdesc,jdbcType=VARCHAR},
      CONSTLEVEL = #{constlevel,jdbcType=DECIMAL},
      ISVALID = #{isvalid,jdbcType=CHAR},
      CONSTALIAS = #{constalias,jdbcType=VARCHAR},
      CONSTEXT1 = #{constext1,jdbcType=VARCHAR},
      CONSTEXT2 = #{constext2,jdbcType=VARCHAR},
      CONSTEXT3 = #{constext3,jdbcType=VARCHAR},
      CREATOR = #{creator,jdbcType=VARCHAR},
      MODIFIER = #{modifier,jdbcType=VARCHAR},
      CHECKER = #{checker,jdbcType=VARCHAR},
      CREDATE = #{credate,jdbcType=TIMESTAMP},
      MODDATE = #{moddate,jdbcType=TIMESTAMP},
      CHECKDATE = #{checkdate,jdbcType=TIMESTAMP},
      UPDATEID = #{updateid,jdbcType=VARCHAR},
      RESOURCEID = #{resourceid,jdbcType=VARCHAR}
    where ID = #{id,jdbcType=VARCHAR}
  </update>


  <select id="listByTypeCode" resultMap="BaseResultMap">
    select
    <include refid="Base_Column_List" />
    from HB_CONSTANT
    where TYPECODE = #{typecode,jdbcType=VARCHAR}
  </select>

  <select id="getConsText2ConstantByCode" resultMap="BaseResultMap" parameterType="string">
    select
    <include refid="Base_Column_List"/>
    from HB_CONSTANT
    where constcode = (select constext2
                       from HB_CONSTANT
                       where constcode = #{constCode,jdbcType=VARCHAR}
                         and TYPECODE = #{typeCode,jdbcType=VARCHAR})
      and TYPECODE = #{textTypeCode,jdbcType=VARCHAR}
  </select>

  <select id="listConstantCodeByConsText2" parameterType="string" resultType="string">
    select constcode
    from HB_CONSTANT
    where constext2 = #{consText2,jdbcType=VARCHAR}
      and TYPECODE = #{typeCode,jdbcType=VARCHAR}
  </select>

</mapper>