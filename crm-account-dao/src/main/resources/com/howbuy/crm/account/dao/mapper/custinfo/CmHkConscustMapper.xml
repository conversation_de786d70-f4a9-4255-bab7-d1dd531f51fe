<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.howbuy.crm.account.dao.mapper.custinfo.CmHkConscustMapper">
  <resultMap id="BaseResultMap" type="com.howbuy.crm.account.dao.po.custinfo.CmHkConscustPO">
    <!--@mbg.generated-->
    <!--@Table CM_HK_CONSCUST-->
    <id column="ID" jdbcType="DECIMAL" property="id" />
    <result column="CONSCUSTNO" jdbcType="VARCHAR" property="conscustno" />
    <result column="HKCUSTID" jdbcType="VARCHAR" property="hkcustid" />
    <result column="CREATOR" jdbcType="VARCHAR" property="creator" />
    <result column="CREDT" jdbcType="TIMESTAMP" property="credt" />
    <result column="MODIFIER" jdbcType="VARCHAR" property="modifier" />
    <result column="MODDT" jdbcType="TIMESTAMP" property="moddt" />
    <result column="HK_TX_ACCT_NO" jdbcType="VARCHAR" property="hkTxAcctNo" />
  </resultMap>
  <sql id="Base_Column_List">
    <!--@mbg.generated-->
    ID, CONSCUSTNO, HKCUSTID, CREATOR, CREDT, MODIFIER, MODDT, HK_TX_ACCT_NO
  </sql>
  <select id="selectByPrimaryKey" parameterType="java.math.BigDecimal" resultMap="BaseResultMap">
    <!--@mbg.generated-->
    select 
    <include refid="Base_Column_List" />
    from CM_HK_CONSCUST
    where ID = #{id,jdbcType=DECIMAL}
  </select>
  <delete id="deleteByPrimaryKey" parameterType="java.math.BigDecimal">
    <!--@mbg.generated-->
    delete from CM_HK_CONSCUST
    where ID = #{id,jdbcType=DECIMAL}
  </delete>
  <insert id="insert" parameterType="com.howbuy.crm.account.dao.po.custinfo.CmHkConscustPO">
    <!--@mbg.generated-->
    insert into CM_HK_CONSCUST (ID, CONSCUSTNO, HKCUSTID, 
      CREATOR, CREDT, MODIFIER, 
      MODDT, HK_TX_ACCT_NO)
    values (SEQ_CM_HK_CONSCUST.NEXTVAL, #{conscustno,jdbcType=VARCHAR}, #{hkcustid,jdbcType=VARCHAR},
      #{creator,jdbcType=VARCHAR}, #{credt,jdbcType=TIMESTAMP}, #{modifier,jdbcType=VARCHAR}, 
      #{moddt,jdbcType=TIMESTAMP}, #{hkTxAcctNo,jdbcType=VARCHAR})
  </insert>

  <update id="updateByPrimaryKeySelective" parameterType="com.howbuy.crm.account.dao.po.custinfo.CmHkConscustPO">
    <!--@mbg.generated-->
    update CM_HK_CONSCUST
    <set>
      <if test="conscustno != null">
        CONSCUSTNO = #{conscustno,jdbcType=VARCHAR},
      </if>
      <if test="hkcustid != null">
        HKCUSTID = #{hkcustid,jdbcType=VARCHAR},
      </if>
      <if test="creator != null">
        CREATOR = #{creator,jdbcType=VARCHAR},
      </if>
      <if test="credt != null">
        CREDT = #{credt,jdbcType=TIMESTAMP},
      </if>
      <if test="modifier != null">
        MODIFIER = #{modifier,jdbcType=VARCHAR},
      </if>
      <if test="moddt != null">
        MODDT = #{moddt,jdbcType=TIMESTAMP},
      </if>
      <if test="hkTxAcctNo != null">
        HK_TX_ACCT_NO = #{hkTxAcctNo,jdbcType=VARCHAR},
      </if>
    </set>
    where ID = #{id,jdbcType=DECIMAL}
  </update>
  <update id="updateByPrimaryKey" parameterType="com.howbuy.crm.account.dao.po.custinfo.CmHkConscustPO">
    <!--@mbg.generated-->
    update CM_HK_CONSCUST
    set CONSCUSTNO = #{conscustno,jdbcType=VARCHAR},
      HKCUSTID = #{hkcustid,jdbcType=VARCHAR},
      CREATOR = #{creator,jdbcType=VARCHAR},
      CREDT = #{credt,jdbcType=TIMESTAMP},
      MODIFIER = #{modifier,jdbcType=VARCHAR},
      MODDT = #{moddt,jdbcType=TIMESTAMP},
      HK_TX_ACCT_NO = #{hkTxAcctNo,jdbcType=VARCHAR}
    where ID = #{id,jdbcType=DECIMAL}
  </update>

  <insert id="backUpHkAcctById" parameterType="java.math.BigDecimal">
    <!--@mbg.generated-->
    INSERT INTO CM_HK_CONSCUST_HIS(<include refid="Base_Column_List" /> )
    SELECT
    <include refid="Base_Column_List" />
    from CM_HK_CONSCUST
    where ID = #{id,jdbcType=DECIMAL}
  </insert>


  <select id="selectByCustNo" parameterType="string" resultMap="BaseResultMap">
    <!--@mbg.generated-->
    select
    <include refid="Base_Column_List" />
    from CM_HK_CONSCUST
    where CONSCUSTNO = #{custNo,jdbcType=VARCHAR}
  </select>
  <select id="selectByEbrokerId" parameterType="string" resultMap="BaseResultMap">
    <!--@mbg.generated-->
    select
    <include refid="Base_Column_List" />
    from CM_HK_CONSCUST
    where HKCUSTID = #{ebrokerId,jdbcType=VARCHAR}
  </select>
  <select id="selectByHkTxAcctNo" parameterType="string" resultMap="BaseResultMap">
    <!--@mbg.generated-->
    select
    <include refid="Base_Column_List" />
    from CM_HK_CONSCUST
    where HK_TX_ACCT_NO = #{hkTxAcctNo,jdbcType=VARCHAR}
    </select>

  <select id="selectByReqVO" parameterType="com.howbuy.crm.account.dao.req.custinfo.CmHkCustReqVO" resultMap="BaseResultMap">
    select
    <include refid="Base_Column_List" />
    from CM_HK_CONSCUST
    <where>
      <if test="conscustno != null and conscustno != ''">
        AND CONSCUSTNO = #{conscustno,jdbcType=VARCHAR}
      </if>
      <if test="hkcustid != null and hkcustid != '' ">
        AND HKCUSTID = #{hkcustid,jdbcType=VARCHAR}
      </if>
      <if test="hkTxAcctNo!= null and hkTxAcctNo != '' ">
        AND HK_TX_ACCT_NO = #{hkTxAcctNo,jdbcType=VARCHAR}
        </if>
      <if test="id!= null">
        AND ID = #{id,jdbcType=DECIMAL}
        </if>
    </where>
  </select>

  <select id="count" resultType="long">
    select count(1)
    from CM_HK_CONSCUST
  </select>

  <select id="selectPageByVo" resultMap="BaseResultMap">
    select
    <include refid="Base_Column_List" />
    from CM_HK_CONSCUST
  </select>

  <update id="batchUpdateEbrokerIdByHkCustNo">
    DECLARE
    begin
    <foreach collection="list" item="item">
      update CM_HK_CONSCUST
      set HKCUSTID = #{item.ebrokerId,jdbcType=VARCHAR}
      where HK_TX_ACCT_NO = #{item.hkCustNo,jdbcType=VARCHAR};
    </foreach>
    commit;
    END;
  </update>

</mapper>