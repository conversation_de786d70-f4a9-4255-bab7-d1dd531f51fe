<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.howbuy.crm.account.dao.mapper.custinfo.CmConscustDeliveryAddressMapper">
  <resultMap id="BaseResultMap" type="com.howbuy.crm.account.dao.po.custinfo.CmConscustDeliveryAddressPO">
    <!--@mbg.generated-->
    <!--@Table CM_CONSCUST_DELIVERY_ADDRESS-->
    <id column="ID" jdbcType="DECIMAL" property="id" />
    <result column="CONSCUSTNO" jdbcType="VARCHAR" property="conscustno" />
    <result column="RECEIVER_NAME" jdbcType="VARCHAR" property="receiverName" />
    <result column="MOBILE_AREA_CODE" jdbcType="VARCHAR" property="mobileAreaCode" />
    <result column="MOBILE_DIGEST" jdbcType="VARCHAR" property="mobileDigest" />
    <result column="MOBILE_MASK" jdbcType="VARCHAR" property="mobileMask" />
    <result column="MOBILE_CIPHER" jdbcType="VARCHAR" property="mobileCipher" />
    <result column="NATION_CODE" jdbcType="VARCHAR" property="nationCode" />
    <result column="PROV_CODE" jdbcType="VARCHAR" property="provCode" />
    <result column="CITY_CODE" jdbcType="VARCHAR" property="cityCode" />
    <result column="COUNTY_CODE" jdbcType="VARCHAR" property="countyCode" />
    <result column="ADDR_DIGEST" jdbcType="VARCHAR" property="addrDigest" />
    <result column="ADDR_MASK" jdbcType="VARCHAR" property="addrMask" />
    <result column="ADDR_CIPHER" jdbcType="VARCHAR" property="addrCipher" />
    <result column="REMARK" jdbcType="VARCHAR" property="remark" />
    <result column="REC_STAT" jdbcType="VARCHAR" property="recStat" />
    <result column="CREATOR" jdbcType="VARCHAR" property="creator" />
    <result column="CREATE_TIMESTAMP" jdbcType="TIMESTAMP" property="createTimestamp" />
    <result column="MODIFIER" jdbcType="VARCHAR" property="modifier" />
    <result column="MODIFY_TIMESTAMP" jdbcType="TIMESTAMP" property="modifyTimestamp" />
  </resultMap>
  <sql id="Base_Column_List">
    <!--@mbg.generated-->
    ID, CONSCUSTNO, RECEIVER_NAME, MOBILE_DIGEST, MOBILE_MASK, MOBILE_CIPHER, PROV_CODE, 
    CITY_CODE, COUNTY_CODE, ADDR_DIGEST, ADDR_MASK, ADDR_CIPHER, REMARK, REC_STAT, CREATOR,
    CREATE_TIMESTAMP, MODIFIER, MODIFY_TIMESTAMP, MOBILE_AREA_CODE, NATION_CODE
  </sql>
  <select id="selectByPrimaryKey" parameterType="java.math.BigDecimal" resultMap="BaseResultMap">
    <!--@mbg.generated-->
    select 
    <include refid="Base_Column_List" />
    from CM_CONSCUST_DELIVERY_ADDRESS
    where ID = #{id,jdbcType=DECIMAL}
  </select>
  <delete id="deleteByPrimaryKey" parameterType="java.math.BigDecimal">
    <!--@mbg.generated-->
    delete from CM_CONSCUST_DELIVERY_ADDRESS
    where ID = #{id,jdbcType=DECIMAL}
  </delete>
  <insert id="insert" parameterType="com.howbuy.crm.account.dao.po.custinfo.CmConscustDeliveryAddressPO">
    <!--@mbg.generated-->
    <selectKey keyProperty="id" order="BEFORE" resultType="java.math.BigDecimal">
      select SEQ_CONSCUST_DELIVERY_ADDRESS.nextval from dual
    </selectKey>
    insert into CM_CONSCUST_DELIVERY_ADDRESS (ID, CONSCUSTNO, RECEIVER_NAME, MOBILE_AREA_CODE,
      MOBILE_DIGEST, MOBILE_MASK, MOBILE_CIPHER,
      NATION_CODE, PROV_CODE, CITY_CODE, COUNTY_CODE,
      ADDR_DIGEST, ADDR_MASK, ADDR_CIPHER, 
      REMARK, REC_STAT, CREATOR, 
      CREATE_TIMESTAMP, MODIFIER, MODIFY_TIMESTAMP
      )
    values (#{id,jdbcType=DECIMAL}, #{conscustno,jdbcType=VARCHAR}, #{receiverName,jdbcType=VARCHAR}, #{mobileAreaCode,jdbcType=VARCHAR},
      #{mobileDigest,jdbcType=VARCHAR}, #{mobileMask,jdbcType=VARCHAR}, #{mobileCipher,jdbcType=VARCHAR}, #{nationCode,jdbcType=VARCHAR},
      #{provCode,jdbcType=VARCHAR}, #{cityCode,jdbcType=VARCHAR}, #{countyCode,jdbcType=VARCHAR}, 
      #{addrDigest,jdbcType=VARCHAR}, #{addrMask,jdbcType=VARCHAR}, #{addrCipher,jdbcType=VARCHAR}, 
      #{remark,jdbcType=VARCHAR}, #{recStat,jdbcType=VARCHAR}, #{creator,jdbcType=VARCHAR}, 
      #{createTimestamp,jdbcType=TIMESTAMP}, #{modifier,jdbcType=VARCHAR}, #{modifyTimestamp,jdbcType=TIMESTAMP}
      )
  </insert>
  <insert id="insertSelective" parameterType="com.howbuy.crm.account.dao.po.custinfo.CmConscustDeliveryAddressPO">
    <!--@mbg.generated-->
    <selectKey keyProperty="id" order="BEFORE" resultType="java.math.BigDecimal">
      select SEQ_CONSCUST_DELIVERY_ADDRESS.nextval from dual
    </selectKey>
    insert into CM_CONSCUST_DELIVERY_ADDRESS
    <trim prefix="(" suffix=")" suffixOverrides=",">
      ID,
      <if test="conscustno != null">
        CONSCUSTNO,
      </if>
      <if test="receiverName != null">
        RECEIVER_NAME,
      </if>
      <if test="mobileAreaCode != null">
        MOBILE_AREA_CODE,
      </if>
      <if test="mobileDigest != null">
        MOBILE_DIGEST,
      </if>
      <if test="mobileMask != null">
        MOBILE_MASK,
      </if>
      <if test="mobileCipher != null">
        MOBILE_CIPHER,
      </if>
      <if test="nationCode != null">
        NATION_CODE,
      </if>
      <if test="provCode != null">
        PROV_CODE,
      </if>
      <if test="cityCode != null">
        CITY_CODE,
      </if>
      <if test="countyCode != null">
        COUNTY_CODE,
      </if>
      <if test="addrDigest != null">
        ADDR_DIGEST,
      </if>
      <if test="addrMask != null">
        ADDR_MASK,
      </if>
      <if test="addrCipher != null">
        ADDR_CIPHER,
      </if>
      <if test="remark != null">
        REMARK,
      </if>
      <if test="recStat != null">
        REC_STAT,
      </if>
      <if test="creator != null">
        CREATOR,
      </if>
      <if test="createTimestamp != null">
        CREATE_TIMESTAMP,
      </if>
      <if test="modifier != null">
        MODIFIER,
      </if>
      <if test="modifyTimestamp != null">
        MODIFY_TIMESTAMP,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      #{id,jdbcType=DECIMAL},
      <if test="conscustno != null">
        #{conscustno,jdbcType=VARCHAR},
      </if>
      <if test="receiverName != null">
        #{receiverName,jdbcType=VARCHAR},
      </if>
      <if test="mobileAreaCode != null">
        #{mobileAreaCode,jdbcType=VARCHAR},
      </if>
      <if test="mobileDigest != null">
        #{mobileDigest,jdbcType=VARCHAR},
      </if>
      <if test="mobileMask != null">
        #{mobileMask,jdbcType=VARCHAR},
      </if>
      <if test="mobileCipher != null">
        #{mobileCipher,jdbcType=VARCHAR},
      </if>
      <if test="nationCode != null">
        #{nationCode,jdbcType=VARCHAR},
      </if>
      <if test="provCode != null">
        #{provCode,jdbcType=VARCHAR},
      </if>
      <if test="cityCode != null">
        #{cityCode,jdbcType=VARCHAR},
      </if>
      <if test="countyCode != null">
        #{countyCode,jdbcType=VARCHAR},
      </if>
      <if test="addrDigest != null">
        #{addrDigest,jdbcType=VARCHAR},
      </if>
      <if test="addrMask != null">
        #{addrMask,jdbcType=VARCHAR},
      </if>
      <if test="addrCipher != null">
        #{addrCipher,jdbcType=VARCHAR},
      </if>
      <if test="remark != null">
        #{remark,jdbcType=VARCHAR},
      </if>
      <if test="recStat != null">
        #{recStat,jdbcType=VARCHAR},
      </if>
      <if test="creator != null">
        #{creator,jdbcType=VARCHAR},
      </if>
      <if test="createTimestamp != null">
        #{createTimestamp,jdbcType=TIMESTAMP},
      </if>
      <if test="modifier != null">
        #{modifier,jdbcType=VARCHAR},
      </if>
      <if test="modifyTimestamp != null">
        #{modifyTimestamp,jdbcType=TIMESTAMP},
      </if>
    </trim>
  </insert>
  <update id="updateByPrimaryKeySelective" parameterType="com.howbuy.crm.account.dao.po.custinfo.CmConscustDeliveryAddressPO">
    <!--@mbg.generated-->
    update CM_CONSCUST_DELIVERY_ADDRESS
    <set>
      <if test="conscustno != null">
        CONSCUSTNO = #{conscustno,jdbcType=VARCHAR},
      </if>
      <if test="receiverName != null">
        RECEIVER_NAME = #{receiverName,jdbcType=VARCHAR},
      </if>
      <if test="mobileAreaCode != null">
        MOBILE_AREA_CODE = #{mobileAreaCode,jdbcType=VARCHAR},
      </if>
      <if test="mobileDigest != null">
        MOBILE_DIGEST = #{mobileDigest,jdbcType=VARCHAR},
      </if>
      <if test="mobileMask != null">
        MOBILE_MASK = #{mobileMask,jdbcType=VARCHAR},
      </if>
      <if test="mobileCipher != null">
        MOBILE_CIPHER = #{mobileCipher,jdbcType=VARCHAR},
      </if>
      <if test="nationCode != null">
        NATION_CODE = #{nationCode,jdbcType=VARCHAR},
      </if>
      <if test="provCode != null">
        PROV_CODE = #{provCode,jdbcType=VARCHAR},
      </if>
      <if test="cityCode != null">
        CITY_CODE = #{cityCode,jdbcType=VARCHAR},
      </if>
      <if test="countyCode != null">
        COUNTY_CODE = #{countyCode,jdbcType=VARCHAR},
      </if>
      <if test="addrDigest != null">
        ADDR_DIGEST = #{addrDigest,jdbcType=VARCHAR},
      </if>
      <if test="addrMask != null">
        ADDR_MASK = #{addrMask,jdbcType=VARCHAR},
      </if>
      <if test="addrCipher != null">
        ADDR_CIPHER = #{addrCipher,jdbcType=VARCHAR},
      </if>
      <if test="remark != null">
        REMARK = #{remark,jdbcType=VARCHAR},
      </if>
      <if test="recStat != null">
        REC_STAT = #{recStat,jdbcType=VARCHAR},
      </if>
      <if test="creator != null">
        CREATOR = #{creator,jdbcType=VARCHAR},
      </if>
      <if test="createTimestamp != null">
        CREATE_TIMESTAMP = #{createTimestamp,jdbcType=TIMESTAMP},
      </if>
      <if test="modifier != null">
        MODIFIER = #{modifier,jdbcType=VARCHAR},
      </if>
      <if test="modifyTimestamp != null">
        MODIFY_TIMESTAMP = #{modifyTimestamp,jdbcType=TIMESTAMP},
      </if>
    </set>
    where ID = #{id,jdbcType=DECIMAL}
  </update>
  <update id="updateByPrimaryKey" parameterType="com.howbuy.crm.account.dao.po.custinfo.CmConscustDeliveryAddressPO">
    <!--@mbg.generated-->
    update CM_CONSCUST_DELIVERY_ADDRESS
    set CONSCUSTNO = #{conscustno,jdbcType=VARCHAR},
      RECEIVER_NAME = #{receiverName,jdbcType=VARCHAR},
      MOBILE_AREA_CODE = #{mobileAreaCode,jdbcType=VARCHAR},
      MOBILE_DIGEST = #{mobileDigest,jdbcType=VARCHAR},
      MOBILE_MASK = #{mobileMask,jdbcType=VARCHAR},
      MOBILE_CIPHER = #{mobileCipher,jdbcType=VARCHAR},
      NATION_CODE = #{nationCode,jdbcType=VARCHAR},
      PROV_CODE = #{provCode,jdbcType=VARCHAR},
      CITY_CODE = #{cityCode,jdbcType=VARCHAR},
      COUNTY_CODE = #{countyCode,jdbcType=VARCHAR},
      ADDR_DIGEST = #{addrDigest,jdbcType=VARCHAR},
      ADDR_MASK = #{addrMask,jdbcType=VARCHAR},
      ADDR_CIPHER = #{addrCipher,jdbcType=VARCHAR},
      REMARK = #{remark,jdbcType=VARCHAR},
      REC_STAT = #{recStat,jdbcType=VARCHAR},
      CREATOR = #{creator,jdbcType=VARCHAR},
      CREATE_TIMESTAMP = #{createTimestamp,jdbcType=TIMESTAMP},
      MODIFIER = #{modifier,jdbcType=VARCHAR},
      MODIFY_TIMESTAMP = #{modifyTimestamp,jdbcType=TIMESTAMP}
    where ID = #{id,jdbcType=DECIMAL}
  </update>

  <select id="listCustDeliveryAddressByCustNo" resultMap="BaseResultMap">
    <!--@mbg.generated-->
    select
    <include refid="Base_Column_List" />
    from CM_CONSCUST_DELIVERY_ADDRESS
    where CONSCUSTNO = #{custNo,jdbcType=VARCHAR}
    and REC_STAT = '1'
  </select>
</mapper>