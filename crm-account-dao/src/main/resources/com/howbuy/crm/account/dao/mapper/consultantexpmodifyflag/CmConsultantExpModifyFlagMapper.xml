<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.howbuy.crm.account.dao.mapper.consultantexpmodifyflag.CmConsultantExpModifyFlagMapper">
  <resultMap id="BaseResultMap" type="com.howbuy.crm.account.dao.po.consultantexp.CmConsultantExpModifyFlagPO">
    <!--@mbg.generated-->
    <!--@Table CM_CONSULTANT_EXP_MODIFY_FLAG-->
    <id column="ID" jdbcType="DECIMAL" property="id" />
    <result column="USERID" jdbcType="VARCHAR" property="userid" />
    <result column="CONSNAME_FLAG" jdbcType="VARCHAR" property="consnameFlag" />
    <result column="CITYCODE_FLAG" jdbcType="VARCHAR" property="citycodeFlag" />
    <result column="CENTER_ORG_FLAG" jdbcType="VARCHAR" property="centerOrgFlag" />
    <result column="WORKTYPE_FLAG" jdbcType="VARCHAR" property="worktypeFlag" />
    <result column="WORKSTATE_FLAG" jdbcType="VARCHAR" property="workstateFlag" />
    <result column="CURMONTHLEVEL_FLAG" jdbcType="VARCHAR" property="curmonthlevelFlag" />
    <result column="USERLEVEL_FLAG" jdbcType="VARCHAR" property="userlevelFlag" />
    <result column="CURMONTHSALARY_FLAG" jdbcType="VARCHAR" property="curmonthsalaryFlag" />
    <result column="SUBPOSITIONS_FLAG" jdbcType="VARCHAR" property="subpositionsFlag" />
    <result column="STARTDT_FLAG" jdbcType="VARCHAR" property="startdtFlag" />
    <result column="STARTLEVEL_FLAG" jdbcType="VARCHAR" property="startlevelFlag" />
    <result column="SALARY_FLAG" jdbcType="VARCHAR" property="salaryFlag" />
    <result column="REGULARDT_FLAG" jdbcType="VARCHAR" property="regulardtFlag" />
    <result column="REGULARLEVEL_FLAG" jdbcType="VARCHAR" property="regularlevelFlag" />
    <result column="REGULARSALARY_FLAG" jdbcType="VARCHAR" property="regularsalaryFlag" />
    <result column="QUITDT_FLAG" jdbcType="VARCHAR" property="quitdtFlag" />
    <result column="QUITLEVEL_FLAG" jdbcType="VARCHAR" property="quitlevelFlag" />
    <result column="QUITSALARY_FLAG" jdbcType="VARCHAR" property="quitsalaryFlag" />
    <result column="BACKGROUND_FLAG" jdbcType="VARCHAR" property="backgroundFlag" />
    <result column="SOURCE_FLAG" jdbcType="VARCHAR" property="sourceFlag" />
    <result column="BEFOREPOSITIONAGE_FLAG" jdbcType="VARCHAR" property="beforepositionageFlag" />
    <result column="RECRUIT_FLAG" jdbcType="VARCHAR" property="recruitFlag" />
    <result column="RECOMMEND_FLAG" jdbcType="VARCHAR" property="recommendFlag" />
    <result column="RECOMMENDUSERNO_FLAG" jdbcType="VARCHAR" property="recommendusernoFlag" />
    <result column="RECOMMENDTYPE_FLAG" jdbcType="VARCHAR" property="recommendtypeFlag" />
    <result column="CREATOR" jdbcType="VARCHAR" property="creator" />
    <result column="MODIFIER" jdbcType="VARCHAR" property="modifier" />
    <result column="CREDT" jdbcType="TIMESTAMP" property="credt" />
    <result column="MODDT" jdbcType="TIMESTAMP" property="moddt" />
    <result column="DT3M_FLAG" jdbcType="VARCHAR" property="dt3mFlag" />
    <result column="DT12M_FLAG" jdbcType="VARCHAR" property="dt12mFlag" />
  </resultMap>
  <sql id="Base_Column_List">
    <!--@mbg.generated-->
    ID, USERID, CONSNAME_FLAG, CITYCODE_FLAG, CENTER_ORG_FLAG, WORKTYPE_FLAG, WORKSTATE_FLAG, 
    CURMONTHLEVEL_FLAG, USERLEVEL_FLAG, CURMONTHSALARY_FLAG, SUBPOSITIONS_FLAG, STARTDT_FLAG, 
    STARTLEVEL_FLAG, SALARY_FLAG, REGULARDT_FLAG, REGULARLEVEL_FLAG, REGULARSALARY_FLAG, 
    QUITDT_FLAG, QUITLEVEL_FLAG, QUITSALARY_FLAG, BACKGROUND_FLAG, SOURCE_FLAG, BEFOREPOSITIONAGE_FLAG, 
    RECRUIT_FLAG, RECOMMEND_FLAG, RECOMMENDUSERNO_FLAG, RECOMMENDTYPE_FLAG, CREATOR, 
    MODIFIER, CREDT, MODDT, DT3M_FLAG, DT12M_FLAG
  </sql>

  <select id="selectAll" resultMap="BaseResultMap">
    select
    <include refid="Base_Column_List" />
    from CM_CONSULTANT_EXP_MODIFY_FLAG
    where userid is not null
  </select>

  <insert id="mergeExpModifyFlagByList" parameterType="list">
    merge into CM_CONSULTANT_EXP_MODIFY_FLAG t1
    using (
    <foreach collection="list" item="item" index="index" open="(" close=")" separator="union all">
      select
      #{item.userid,jdbcType=VARCHAR} as userid,
      #{item.consnameFlag,jdbcType=VARCHAR} as consname_Flag,
      #{item.citycodeFlag,jdbcType=VARCHAR} as citycode_Flag,
      #{item.centerOrgFlag,jdbcType=VARCHAR} as center_Org_Flag,
      #{item.startdtFlag,jdbcType=VARCHAR} as startdt_Flag,
      #{item.startlevelFlag,jdbcType=VARCHAR} as startlevel_Flag,
      #{item.salaryFlag,jdbcType=VARCHAR} as salary_Flag,
      #{item.dt3mFlag,jdbcType=VARCHAR} as dt3m_Flag,
      #{item.regularlevelFlag,jdbcType=VARCHAR} as regularlevel_Flag,
      #{item.regularsalaryFlag,jdbcType=VARCHAR} as regularsalary_Flag,
      #{item.dt12mFlag,jdbcType=VARCHAR} as dt12m_Flag,
      #{item.quitdtFlag,jdbcType=VARCHAR} as quitdt_Flag,
      #{item.quitlevelFlag,jdbcType=VARCHAR} as quitlevel_Flag,
      #{item.quitsalaryFlag,jdbcType=VARCHAR} as quitsalary_Flag,
      #{item.backgroundFlag,jdbcType=VARCHAR} as background_Flag,
      #{item.sourceFlag,jdbcType=VARCHAR} as source_Flag,
      #{item.beforepositionageFlag,jdbcType=VARCHAR} as beforepositionage_Flag,
      #{item.recruitFlag,jdbcType=VARCHAR} as recruit_Flag,
      #{item.recommendFlag,jdbcType=VARCHAR} as recommend_Flag,
      #{item.recommendusernoFlag,jdbcType=VARCHAR} as recommenduserno_Flag,
      #{item.recommendtypeFlag,jdbcType=VARCHAR} as recommendtype_Flag,
      #{item.regulardtFlag,jdbcType=VARCHAR} as regulardt_flag
      from dual
    </foreach>
    )t2
    on (t1.userid=t2.userid)
    when matched then
    update set
      t1.consname_Flag = t2.consname_Flag
      ,t1.citycode_Flag = t2.citycode_Flag
      ,t1.center_Org_Flag = t2.center_Org_Flag
      ,t1.startdt_Flag = t2.startdt_Flag
      ,t1.startlevel_Flag = t2.startlevel_Flag
      ,t1.salary_Flag = t2.salary_Flag
      ,t1.dt3m_Flag = t2.dt3m_Flag
      ,t1.regularlevel_Flag = t2.regularlevel_Flag
      ,t1.regularsalary_Flag = t2.regularsalary_Flag
      ,t1.dt12m_Flag = t2.dt12m_Flag
      ,t1.quitdt_Flag = t2.quitdt_Flag
      ,t1.quitlevel_Flag = t2.quitlevel_Flag
      ,t1.quitsalary_Flag = t2.quitsalary_Flag
      ,t1.background_Flag = t2.background_Flag
      ,t1.source_Flag = t2.source_Flag
      ,t1.beforepositionage_Flag = t2.beforepositionage_Flag
      ,t1.recruit_Flag = t2.recruit_Flag
      ,t1.recommend_Flag = t2.recommend_Flag
      ,t1.recommenduserno_Flag = t2.recommenduserno_Flag
      ,t1.recommendtype_Flag = t2.recommendtype_Flag
    ,t1.regulardt_flag = t2.regulardt_flag
      ,t1.modifier = 'auto'
      ,t1.moddt = sysdate
    when not matched then
      insert (t1.id, t1.userid,t1.consname_Flag,t1.citycode_Flag,t1.center_Org_Flag,t1.startdt_Flag,t1.startlevel_Flag,t1.salary_Flag,
            t1.dt3m_Flag,t1.regularlevel_Flag,t1.regularsalary_Flag,t1.dt12m_Flag,t1.quitdt_Flag,t1.quitlevel_Flag,t1.quitsalary_Flag,
            t1.background_Flag,t1.source_Flag,t1.beforepositionage_Flag,t1.recruit_Flag,t1.recommend_Flag,t1.recommenduserno_Flag,
            t1.recommendtype_Flag,t1.creator,t1.modifier,t1.credt,t1.moddt,t1.regulardt_flag)
      values(CM_BEISION_POS_LEVEL_CONF_SEQ.nextval,t2.userid,t2.consname_Flag,t2.citycode_Flag,t2.center_Org_Flag,t2.startdt_Flag,t2.startlevel_Flag,t2.salary_Flag,
            t2.dt3m_Flag,t2.regularlevel_Flag,t2.regularsalary_Flag,t2.dt12m_Flag,t2.quitdt_Flag,t2.quitlevel_Flag,t2.quitsalary_Flag,
            t2.background_Flag,t2.source_Flag,t2.beforepositionage_Flag,t2.recruit_Flag,t2.recommend_Flag,t2.recommenduserno_Flag,
            t2.recommendtype_Flag,'auto','auto',sysdate,sysdate,t2.regulardt_flag)
  </insert>

  <select id="selectByUseridList" parameterType="java.lang.String" resultMap="BaseResultMap">
    select
    <include refid="Base_Column_List" />
    from CM_CONSULTANT_EXP_MODIFY_FLAG
    where userid in
    <foreach collection="list" item="item" index="index" open="(" close=")" separator=",">
        #{item}
    </foreach>
  </select>

</mapper>