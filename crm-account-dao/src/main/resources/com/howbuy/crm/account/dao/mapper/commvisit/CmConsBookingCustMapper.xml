<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.howbuy.crm.account.dao.mapper.commvisit.CmConsBookingCustMapper">
  <resultMap id="BaseResultMap" type="com.howbuy.crm.account.dao.po.commvisit.CmConsBookingCustPO">
    <!--@mbg.generated-->
    <!--@Table CM_CONSBOOKINGCUST-->
    <id column="CONSBOOKINGID" jdbcType="VARCHAR" property="consbookingid" />
    <result column="CONSCUSTNO" jdbcType="VARCHAR" property="conscustno" />
    <result column="BOOKINGSTATUS" jdbcType="VARCHAR" property="bookingstatus" />
    <result column="BOOKINGCONS" jdbcType="VARCHAR" property="bookingcons" />
    <result column="CONTENT" jdbcType="VARCHAR" property="content" />
    <result column="BOOKINGDT" jdbcType="VARCHAR" property="bookingdt" />
    <result column="CREATOR" jdbcType="VARCHAR" property="creator" />
    <result column="CREDT" jdbcType="VARCHAR" property="credt" />
    <result column="MODDT" jdbcType="VARCHAR" property="moddt" />
    <result column="STIMESTAMP" jdbcType="TIMESTAMP" property="stimestamp" />
    <result column="BOOKINGSTARTTIME" jdbcType="VARCHAR" property="bookingstarttime" />
    <result column="BOOKINGENDTIME" jdbcType="VARCHAR" property="bookingendtime" />
    <result column="VISITTYPE" jdbcType="VARCHAR" property="visittype" />
    <result column="VISITCLASSIFY" jdbcType="VARCHAR" property="visitclassify" />
  </resultMap>
  <sql id="Base_Column_List">
    <!--@mbg.generated-->
    CONSBOOKINGID, CONSCUSTNO, BOOKINGSTATUS, BOOKINGCONS, CONTENT, BOOKINGDT, CREATOR, 
    CREDT, MODDT, STIMESTAMP, BOOKINGSTARTTIME, BOOKINGENDTIME, VISITTYPE, VISITCLASSIFY
  </sql>
  <select id="selectByPrimaryKey" parameterType="java.lang.String" resultMap="BaseResultMap">
    <!--@mbg.generated-->
    select 
    <include refid="Base_Column_List" />
    from CM_CONSBOOKINGCUST
    where CONSBOOKINGID = #{consbookingid,jdbcType=VARCHAR}
  </select>
  <delete id="deleteByPrimaryKey" parameterType="java.lang.String">
    <!--@mbg.generated-->
    delete from CM_CONSBOOKINGCUST
    where CONSBOOKINGID = #{consbookingid,jdbcType=VARCHAR}
  </delete>
  <insert id="insert" parameterType="com.howbuy.crm.account.dao.po.commvisit.CmConsBookingCustPO">
    <!--@mbg.generated-->
    insert into CM_CONSBOOKINGCUST (CONSBOOKINGID, CONSCUSTNO, BOOKINGSTATUS, 
      BOOKINGCONS, CONTENT, BOOKINGDT, 
      CREATOR, CREDT, MODDT, 
      STIMESTAMP, BOOKINGSTARTTIME, BOOKINGENDTIME, 
      VISITTYPE, VISITCLASSIFY)
    values (#{consbookingid,jdbcType=VARCHAR}, #{conscustno,jdbcType=VARCHAR}, #{bookingstatus,jdbcType=VARCHAR}, 
      #{bookingcons,jdbcType=VARCHAR}, #{content,jdbcType=VARCHAR}, #{bookingdt,jdbcType=VARCHAR}, 
      #{creator,jdbcType=VARCHAR}, #{credt,jdbcType=VARCHAR}, #{moddt,jdbcType=VARCHAR}, 
      #{stimestamp,jdbcType=TIMESTAMP}, #{bookingstarttime,jdbcType=VARCHAR}, #{bookingendtime,jdbcType=VARCHAR}, 
      #{visittype,jdbcType=VARCHAR}, #{visitclassify,jdbcType=VARCHAR})
  </insert>
  <insert id="insertSelective" parameterType="com.howbuy.crm.account.dao.po.commvisit.CmConsBookingCustPO">
    <!--@mbg.generated-->
    insert into CM_CONSBOOKINGCUST
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="consbookingid != null">
        CONSBOOKINGID,
      </if>
      <if test="conscustno != null">
        CONSCUSTNO,
      </if>
      <if test="bookingstatus != null">
        BOOKINGSTATUS,
      </if>
      <if test="bookingcons != null">
        BOOKINGCONS,
      </if>
      <if test="content != null">
        CONTENT,
      </if>
      <if test="bookingdt != null">
        BOOKINGDT,
      </if>
      <if test="creator != null">
        CREATOR,
      </if>
      <if test="credt != null">
        CREDT,
      </if>
      <if test="moddt != null">
        MODDT,
      </if>
      <if test="stimestamp != null">
        STIMESTAMP,
      </if>
      <if test="bookingstarttime != null">
        BOOKINGSTARTTIME,
      </if>
      <if test="bookingendtime != null">
        BOOKINGENDTIME,
      </if>
      <if test="visittype != null">
        VISITTYPE,
      </if>
      <if test="visitclassify != null">
        VISITCLASSIFY,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="consbookingid != null">
        #{consbookingid,jdbcType=VARCHAR},
      </if>
      <if test="conscustno != null">
        #{conscustno,jdbcType=VARCHAR},
      </if>
      <if test="bookingstatus != null">
        #{bookingstatus,jdbcType=VARCHAR},
      </if>
      <if test="bookingcons != null">
        #{bookingcons,jdbcType=VARCHAR},
      </if>
      <if test="content != null">
        #{content,jdbcType=VARCHAR},
      </if>
      <if test="bookingdt != null">
        #{bookingdt,jdbcType=VARCHAR},
      </if>
      <if test="creator != null">
        #{creator,jdbcType=VARCHAR},
      </if>
      <if test="credt != null">
        #{credt,jdbcType=VARCHAR},
      </if>
      <if test="moddt != null">
        #{moddt,jdbcType=VARCHAR},
      </if>
      <if test="stimestamp != null">
        #{stimestamp,jdbcType=TIMESTAMP},
      </if>
      <if test="bookingstarttime != null">
        #{bookingstarttime,jdbcType=VARCHAR},
      </if>
      <if test="bookingendtime != null">
        #{bookingendtime,jdbcType=VARCHAR},
      </if>
      <if test="visittype != null">
        #{visittype,jdbcType=VARCHAR},
      </if>
      <if test="visitclassify != null">
        #{visitclassify,jdbcType=VARCHAR},
      </if>
    </trim>
  </insert>
  <update id="updateByPrimaryKeySelective" parameterType="com.howbuy.crm.account.dao.po.commvisit.CmConsBookingCustPO">
    <!--@mbg.generated-->
    update CM_CONSBOOKINGCUST
    <set>
      <if test="conscustno != null">
        CONSCUSTNO = #{conscustno,jdbcType=VARCHAR},
      </if>
      <if test="bookingstatus != null">
        BOOKINGSTATUS = #{bookingstatus,jdbcType=VARCHAR},
      </if>
      <if test="bookingcons != null">
        BOOKINGCONS = #{bookingcons,jdbcType=VARCHAR},
      </if>
      <if test="content != null">
        CONTENT = #{content,jdbcType=VARCHAR},
      </if>
      <if test="bookingdt != null">
        BOOKINGDT = #{bookingdt,jdbcType=VARCHAR},
      </if>
      <if test="creator != null">
        CREATOR = #{creator,jdbcType=VARCHAR},
      </if>
      <if test="credt != null">
        CREDT = #{credt,jdbcType=VARCHAR},
      </if>
      <if test="moddt != null">
        MODDT = #{moddt,jdbcType=VARCHAR},
      </if>
      <if test="stimestamp != null">
        STIMESTAMP = #{stimestamp,jdbcType=TIMESTAMP},
      </if>
      <if test="bookingstarttime != null">
        BOOKINGSTARTTIME = #{bookingstarttime,jdbcType=VARCHAR},
      </if>
      <if test="bookingendtime != null">
        BOOKINGENDTIME = #{bookingendtime,jdbcType=VARCHAR},
      </if>
      <if test="visittype != null">
        VISITTYPE = #{visittype,jdbcType=VARCHAR},
      </if>
      <if test="visitclassify != null">
        VISITCLASSIFY = #{visitclassify,jdbcType=VARCHAR},
      </if>
    </set>
    where CONSBOOKINGID = #{consbookingid,jdbcType=VARCHAR}
  </update>
  <update id="updateByPrimaryKey" parameterType="com.howbuy.crm.account.dao.po.commvisit.CmConsBookingCustPO">
    <!--@mbg.generated-->
    update CM_CONSBOOKINGCUST
    set CONSCUSTNO = #{conscustno,jdbcType=VARCHAR},
      BOOKINGSTATUS = #{bookingstatus,jdbcType=VARCHAR},
      BOOKINGCONS = #{bookingcons,jdbcType=VARCHAR},
      CONTENT = #{content,jdbcType=VARCHAR},
      BOOKINGDT = #{bookingdt,jdbcType=VARCHAR},
      CREATOR = #{creator,jdbcType=VARCHAR},
      CREDT = #{credt,jdbcType=VARCHAR},
      MODDT = #{moddt,jdbcType=VARCHAR},
      STIMESTAMP = #{stimestamp,jdbcType=TIMESTAMP},
      BOOKINGSTARTTIME = #{bookingstarttime,jdbcType=VARCHAR},
      BOOKINGENDTIME = #{bookingendtime,jdbcType=VARCHAR},
      VISITTYPE = #{visittype,jdbcType=VARCHAR},
      VISITCLASSIFY = #{visitclassify,jdbcType=VARCHAR}
    where CONSBOOKINGID = #{consbookingid,jdbcType=VARCHAR}
  </update>
</mapper>