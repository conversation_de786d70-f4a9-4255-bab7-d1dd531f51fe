<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.howbuy.crm.account.dao.mapper.custinfo.CmCustconstantMapper">
  <resultMap id="BaseResultMap" type="com.howbuy.crm.account.dao.po.custinfo.CmCustconstantPO">
    <!--@mbg.generated-->
    <!--@Table CM_CUSTCONSTANT-->
    <id column="CUSTNO" jdbcType="VARCHAR" property="custno" />
    <result column="CONSCODE" jdbcType="VARCHAR" property="conscode" />
    <result column="STARTDT" jdbcType="VARCHAR" property="startdt" />
    <result column="ENDDT" jdbcType="VARCHAR" property="enddt" />
    <result column="MEMO" jdbcType="VARCHAR" property="memo" />
    <result column="RECSTAT" jdbcType="VARCHAR" property="recstat" />
    <result column="CHECKFLAG" jdbcType="VARCHAR" property="checkflag" />
    <result column="CREATOR" jdbcType="VARCHAR" property="creator" />
    <result column="MODIFIER" jdbcType="VARCHAR" property="modifier" />
    <result column="CHECKER" jdbcType="VARCHAR" property="checker" />
    <result column="CREDT" jdbcType="VARCHAR" property="credt" />
    <result column="MODDT" jdbcType="VARCHAR" property="moddt" />
    <result column="BINDDATE" jdbcType="TIMESTAMP" property="binddate" />
    <result column="BEFOREHISID" jdbcType="VARCHAR" property="beforehisid" />
    <result column="OPERATE_DATE" jdbcType="TIMESTAMP" property="operateDate" />
  </resultMap>
  <sql id="Base_Column_List">
    <!--@mbg.generated-->
    CUSTNO, CONSCODE, STARTDT, ENDDT, MEMO, RECSTAT, CHECKFLAG, CREATOR, MODIFIER, CHECKER, 
    CREDT, MODDT, BINDDATE, BEFOREHISID, OPERATE_DATE
  </sql>
  <select id="selectByPrimaryKey" parameterType="java.lang.String" resultMap="BaseResultMap">
    <!--@mbg.generated-->
    select 
    <include refid="Base_Column_List" />
    from CM_CUSTCONSTANT
    where CUSTNO = #{custno,jdbcType=VARCHAR}
  </select>
  <delete id="deleteByPrimaryKey" parameterType="java.lang.String">
    <!--@mbg.generated-->
    delete from CM_CUSTCONSTANT
    where CUSTNO = #{custno,jdbcType=VARCHAR}
  </delete>
  <insert id="insert" parameterType="com.howbuy.crm.account.dao.po.custinfo.CmCustconstantPO">
    <!--@mbg.generated-->
    insert into CM_CUSTCONSTANT (CUSTNO, CONSCODE, STARTDT, 
      ENDDT, MEMO, RECSTAT, 
      CHECKFLAG, CREATOR, MODIFIER, 
      CHECKER, CREDT, MODDT, 
      BINDDATE, BEFOREHISID, OPERATE_DATE
      )
    values (#{custno,jdbcType=VARCHAR}, #{conscode,jdbcType=VARCHAR}, #{startdt,jdbcType=VARCHAR}, 
      #{enddt,jdbcType=VARCHAR}, #{memo,jdbcType=VARCHAR}, #{recstat,jdbcType=VARCHAR}, 
      #{checkflag,jdbcType=VARCHAR}, #{creator,jdbcType=VARCHAR}, #{modifier,jdbcType=VARCHAR}, 
      #{checker,jdbcType=VARCHAR}, #{credt,jdbcType=VARCHAR}, #{moddt,jdbcType=VARCHAR}, 
      #{binddate,jdbcType=TIMESTAMP}, #{beforehisid,jdbcType=VARCHAR}, #{operateDate,jdbcType=TIMESTAMP}
      )
  </insert>
  <insert id="insertSelective" parameterType="com.howbuy.crm.account.dao.po.custinfo.CmCustconstantPO">
    <!--@mbg.generated-->
    insert into CM_CUSTCONSTANT
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="custno != null">
        CUSTNO,
      </if>
      <if test="conscode != null">
        CONSCODE,
      </if>
      <if test="startdt != null">
        STARTDT,
      </if>
      <if test="enddt != null">
        ENDDT,
      </if>
      <if test="memo != null">
        MEMO,
      </if>
      <if test="recstat != null">
        RECSTAT,
      </if>
      <if test="checkflag != null">
        CHECKFLAG,
      </if>
      <if test="creator != null">
        CREATOR,
      </if>
      <if test="modifier != null">
        MODIFIER,
      </if>
      <if test="checker != null">
        CHECKER,
      </if>
      <if test="credt != null">
        CREDT,
      </if>
      <if test="moddt != null">
        MODDT,
      </if>
      <if test="binddate != null">
        BINDDATE,
      </if>
      <if test="beforehisid != null">
        BEFOREHISID,
      </if>
      <if test="operateDate != null">
        OPERATE_DATE,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="custno != null">
        #{custno,jdbcType=VARCHAR},
      </if>
      <if test="conscode != null">
        #{conscode,jdbcType=VARCHAR},
      </if>
      <if test="startdt != null">
        #{startdt,jdbcType=VARCHAR},
      </if>
      <if test="enddt != null">
        #{enddt,jdbcType=VARCHAR},
      </if>
      <if test="memo != null">
        #{memo,jdbcType=VARCHAR},
      </if>
      <if test="recstat != null">
        #{recstat,jdbcType=VARCHAR},
      </if>
      <if test="checkflag != null">
        #{checkflag,jdbcType=VARCHAR},
      </if>
      <if test="creator != null">
        #{creator,jdbcType=VARCHAR},
      </if>
      <if test="modifier != null">
        #{modifier,jdbcType=VARCHAR},
      </if>
      <if test="checker != null">
        #{checker,jdbcType=VARCHAR},
      </if>
      <if test="credt != null">
        #{credt,jdbcType=VARCHAR},
      </if>
      <if test="moddt != null">
        #{moddt,jdbcType=VARCHAR},
      </if>
      <if test="binddate != null">
        #{binddate,jdbcType=TIMESTAMP},
      </if>
      <if test="beforehisid != null">
        #{beforehisid,jdbcType=VARCHAR},
      </if>
      <if test="operateDate != null">
        #{operateDate,jdbcType=TIMESTAMP},
      </if>
    </trim>
  </insert>
  <update id="updateByPrimaryKeySelective" parameterType="com.howbuy.crm.account.dao.po.custinfo.CmCustconstantPO">
    <!--@mbg.generated-->
    update CM_CUSTCONSTANT
    <set>
      <if test="conscode != null">
        CONSCODE = #{conscode,jdbcType=VARCHAR},
      </if>
      <if test="startdt != null">
        STARTDT = #{startdt,jdbcType=VARCHAR},
      </if>
      <if test="enddt != null">
        ENDDT = #{enddt,jdbcType=VARCHAR},
      </if>
      <if test="memo != null">
        MEMO = #{memo,jdbcType=VARCHAR},
      </if>
      <if test="recstat != null">
        RECSTAT = #{recstat,jdbcType=VARCHAR},
      </if>
      <if test="checkflag != null">
        CHECKFLAG = #{checkflag,jdbcType=VARCHAR},
      </if>
      <if test="creator != null">
        CREATOR = #{creator,jdbcType=VARCHAR},
      </if>
      <if test="modifier != null">
        MODIFIER = #{modifier,jdbcType=VARCHAR},
      </if>
      <if test="checker != null">
        CHECKER = #{checker,jdbcType=VARCHAR},
      </if>
      <if test="credt != null">
        CREDT = #{credt,jdbcType=VARCHAR},
      </if>
      <if test="moddt != null">
        MODDT = #{moddt,jdbcType=VARCHAR},
      </if>
      <if test="binddate != null">
        BINDDATE = #{binddate,jdbcType=TIMESTAMP},
      </if>
      <if test="beforehisid != null">
        BEFOREHISID = #{beforehisid,jdbcType=VARCHAR},
      </if>
      <if test="operateDate != null">
        OPERATE_DATE = #{operateDate,jdbcType=TIMESTAMP},
      </if>
    </set>
    where CUSTNO = #{custno,jdbcType=VARCHAR}
  </update>
  <update id="updateByPrimaryKey" parameterType="com.howbuy.crm.account.dao.po.custinfo.CmCustconstantPO">
    <!--@mbg.generated-->
    update CM_CUSTCONSTANT
    set CONSCODE = #{conscode,jdbcType=VARCHAR},
      STARTDT = #{startdt,jdbcType=VARCHAR},
      ENDDT = #{enddt,jdbcType=VARCHAR},
      MEMO = #{memo,jdbcType=VARCHAR},
      RECSTAT = #{recstat,jdbcType=VARCHAR},
      CHECKFLAG = #{checkflag,jdbcType=VARCHAR},
      CREATOR = #{creator,jdbcType=VARCHAR},
      MODIFIER = #{modifier,jdbcType=VARCHAR},
      CHECKER = #{checker,jdbcType=VARCHAR},
      CREDT = #{credt,jdbcType=VARCHAR},
      MODDT = #{moddt,jdbcType=VARCHAR},
      BINDDATE = #{binddate,jdbcType=TIMESTAMP},
      BEFOREHISID = #{beforehisid,jdbcType=VARCHAR},
      OPERATE_DATE = #{operateDate,jdbcType=TIMESTAMP}
    where CUSTNO = #{custno,jdbcType=VARCHAR}
  </update>


  <select id="countCustNumByConsCode" parameterType="string" resultType="integer">
    SELECT NVL(COUNT(1),0)
    FROM CM_CUSTCONSTANT
    WHERE
    CONSCODE = #{consCode,jdbcType=VARCHAR}
  </select>


  <!-- 根据投顾客户号查询分配时间 -->
  <select id="selectBindDateByCustNo" parameterType="java.lang.String" resultType="java.util.Date">
    SELECT nvl(BINDDATE,OPERATE_DATE) BINDDATE
    FROM CM_CUSTCONSTANT
    WHERE custNo = #{custNo,jdbcType=VARCHAR}
  </select>
</mapper>