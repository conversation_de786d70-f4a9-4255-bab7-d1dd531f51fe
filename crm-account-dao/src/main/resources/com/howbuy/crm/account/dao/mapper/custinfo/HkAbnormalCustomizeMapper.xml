<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.howbuy.crm.account.dao.mapper.customize.custinfo.HkAbnormalCustomizeMapper">
  <resultMap id="BaseResultMap" type="com.howbuy.crm.account.dao.po.custinfo.CmHkAbnormalCustInfoPO">
    <!--@mbg.generated-->
    <!--@Table CM_HK_ABNORMAL_CUST_INFO-->
    <id column="ID" jdbcType="VARCHAR" property="id" />
    <result column="MESSAGE_CLIENT_ID" jdbcType="VARCHAR" property="messageClientId" />
    <result column="HK_TX_ACCT_NO" jdbcType="VARCHAR" property="hkTxAcctNo" />
    <result column="CUST_NAME" jdbcType="VARCHAR" property="custName" />
    <result column="INVEST_TYPE" jdbcType="VARCHAR" property="investType" />
    <result column="MOBILE_AREA_CODE" jdbcType="VARCHAR" property="mobileAreaCode" />
    <result column="MOBILE_DIGEST" jdbcType="VARCHAR" property="mobileDigest" />
    <result column="MOBILE_MASK" jdbcType="VARCHAR" property="mobileMask" />
    <result column="MOBILE_CIPHER" jdbcType="VARCHAR" property="mobileCipher" />
    <result column="ID_SIGN_AREA_CODE" jdbcType="VARCHAR" property="idSignAreaCode" />
    <result column="ID_TYPE" jdbcType="VARCHAR" property="idType" />
    <result column="ID_NO_DIGEST" jdbcType="VARCHAR" property="idNoDigest" />
    <result column="ID_NO_MASK" jdbcType="VARCHAR" property="idNoMask" />
    <result column="ID_NO_CIPHER" jdbcType="VARCHAR" property="idNoCipher" />
    <result column="HBONE_NO" jdbcType="VARCHAR" property="hboneNo" />
    <result column="ABNORMAL_SOURCE" jdbcType="VARCHAR" property="abnormalSource" />
    <result column="ABNORMAL_SCENE_TYPE" jdbcType="VARCHAR" property="abnormalSceneType" />
    <result column="CREATOR" jdbcType="VARCHAR" property="creator" />
    <result column="CREATE_TIMESTAMP" jdbcType="TIMESTAMP" property="createTimestamp" />
    <result column="MODIFIER" jdbcType="VARCHAR" property="modifier" />
    <result column="MODIFY_TIMESTAMP" jdbcType="TIMESTAMP" property="modifyTimestamp" />
    <result column="REC_STAT" jdbcType="VARCHAR" property="recStat" />
    <result column="DEAL_STATUS" jdbcType="VARCHAR" property="dealStatus" />
    <result column="DEAL_OPERATOR" jdbcType="VARCHAR" property="dealOperator" />
    <result column="DEAL_REMARK" jdbcType="VARCHAR" property="dealRemark" />
    <result column="DEAL_TIMESTAMP" jdbcType="TIMESTAMP" property="dealTimestamp" />
    <result column="ABNORMAL_LEVEL" jdbcType="VARCHAR" property="abnormalLevel" />
    <result column="MESSAGE_ID" jdbcType="VARCHAR" property="messageId" />
  </resultMap>
  <sql id="Base_Column_List">
    <!--@mbg.generated-->
    ID, MESSAGE_CLIENT_ID, HK_TX_ACCT_NO, CUST_NAME, INVEST_TYPE, MOBILE_AREA_CODE, MOBILE_DIGEST, 
    MOBILE_MASK, MOBILE_CIPHER, ID_SIGN_AREA_CODE, ID_TYPE, ID_NO_DIGEST, ID_NO_MASK, 
    ID_NO_CIPHER, HBONE_NO, ABNORMAL_SOURCE, ABNORMAL_SCENE_TYPE, CREATOR, CREATE_TIMESTAMP, 
    MODIFIER, MODIFY_TIMESTAMP, REC_STAT, DEAL_STATUS, DEAL_OPERATOR, DEAL_REMARK, DEAL_TIMESTAMP
  </sql>


  <select id="selectPageByVo" parameterType="com.howbuy.crm.account.dao.req.custinfo.AbnormalCustReqVO"
           resultMap="BaseResultMap">
    <!--@mbg.generated-->
    select
      C.ID, C.MESSAGE_CLIENT_ID, C.HK_TX_ACCT_NO, C.CUST_NAME, C.INVEST_TYPE, C.MOBILE_AREA_CODE, C.MOBILE_DIGEST,
      C.MOBILE_MASK, C.MOBILE_CIPHER, C.ID_SIGN_AREA_CODE, C.ID_TYPE, C.ID_NO_DIGEST, C.ID_NO_MASK,
      C.ID_NO_CIPHER, C.HBONE_NO, C.ABNORMAL_SOURCE, C.ABNORMAL_SCENE_TYPE, C.CREATOR, C.CREATE_TIMESTAMP,
      C.MODIFIER, C.MODIFY_TIMESTAMP, C.REC_STAT, C.DEAL_STATUS, C.DEAL_OPERATOR, C.DEAL_REMARK, C.DEAL_TIMESTAMP, C.ABNORMAL_LEVEL, C.MESSAGE_ID
    from CM_HK_ABNORMAL_CUST_INFO C
    LEFT JOIN CM_HK_CONSCUST CT ON CT.HK_TX_ACCT_NO=C.HK_TX_ACCT_NO
    where
    ( 1=1
    <if test="id != null and id != '' "> AND C.ID = #{id,jdbcType=VARCHAR} </if>
    <if test="hkTxAcctNo != null and hkTxAcctNo != ''"> AND C.HK_TX_ACCT_NO = #{hkTxAcctNo,jdbcType=VARCHAR} </if>
    <if test="custNo != null and custNo != ''">  AND CT.CONSCUSTNO = #{custNo,jdbcType=VARCHAR} </if>
    <if test="custName != null and custName != ''"> AND C.CUST_NAME = #{custName,jdbcType=VARCHAR} </if>
    <if test="investType != null and investType != ''"> AND C.INVEST_TYPE = #{investType,jdbcType=VARCHAR} </if>
    <if test="mobileDigest != null and mobileDigest != ''"> AND C.MOBILE_DIGEST = #{mobileDigest,jdbcType=VARCHAR} </if>
    <if test="idNoDigest != null and idNoDigest != ''"> AND C.ID_NO_DIGEST = #{idNoDigest,jdbcType=VARCHAR} </if>
    <if test="abnormalSourceList != null and abnormalSourceList.size() != 0">
        AND C.ABNORMAL_SOURCE IN
        <foreach collection="abnormalSourceList" item="item" index="index" open="(" separator="," close=")">
            #{item}
        </foreach>
    </if>
    <if test="dealStatusList != null and dealStatusList.size() != 0">
        AND C.DEAL_STATUS IN
        <foreach collection="dealStatusList" item="item" index="index" open="(" separator="," close=")">
            #{item}
        </foreach>
    </if>
      <if test="abnormalIdList != null and abnormalIdList.size() != 0">
          AND C.ABNORMAL_SCENE_TYPE IN
          <foreach collection="abnormalIdList" item="item" index="index" open="(" separator="," close=")">
              #{item}
          </foreach>
      </if>
      <if test="abnormalLevelList != null and abnormalLevelList.size() != 0">
          AND C.ABNORMAL_LEVEL IN
          <foreach collection="abnormalLevelList" item="item" index="index" open="(" separator="," close=")">
              #{item}
          </foreach>
      </if>
    <if test="createBginDdate != null"> AND C.CREATE_TIMESTAMP &gt;= #{createBginDdate,jdbcType=TIMESTAMP} </if>
    <if test="createEndDate != null">   AND C.CREATE_TIMESTAMP &lt;= #{createEndDate,jdbcType=TIMESTAMP} </if>
    )
      <!--以下为 作用于 子表的-->
      <if test="   (custName != null and custName != '')
                or (hkTxAcctNo != null and hkTxAcctNo != '')
                or (custNo != null and custNo != '')
                or (mobileDigest != null and mobileDigest != '')
                or (idNoDigest != null and idNoDigest != '') ">
          OR EXISTS (
          SELECT 1 FROM CM_HK_ABNORMAL_RELATED_CUST S
          LEFT JOIN CM_CONSCUST T ON S.CUST_NO=T.CONSCUSTNO
          LEFT JOIN CM_HK_CONSCUST CM ON CM.CONSCUSTNO=T.CONSCUSTNO
          LEFT JOIN CM_HBONE_ABNORMAL_CUST_INFO MAIN ON MAIN.ID= S.ABNORMAL_ID
          WHERE S.ABNORMAL_ID = C.ID
          AND T.CONSCUSTSTATUS = '0'
          <if test="custName != null and custName != ''"> AND T.CUSTNAME = #{custName,jdbcType=VARCHAR} </if>
          <if test="hkTxAcctNo != null and hkTxAcctNo != ''"> AND CM.HK_TX_ACCT_NO = #{hkTxAcctNo,jdbcType=VARCHAR} </if>
          <if test="custNo != null and custNo != ''"> AND S.CUST_NO = #{custNo,jdbcType=VARCHAR} </if>
          <if test="mobileDigest != null and mobileDigest != ''"> AND T.MOBILE_DIGEST = #{mobileDigest,jdbcType=VARCHAR}</if>
          <if test="idNoDigest != null and idNoDigest != ''"> AND T.IDNO_DIGEST = #{idNoDigest,jdbcType=VARCHAR}</if>

          <!--主表条件 需要再次作用一遍-->
          <if test="id != null and id != '' "> AND MAIN.ID = #{id,jdbcType=VARCHAR} </if>
          <if test="abnormalSourceList != null and abnormalSourceList.size() != 0">
              AND MAIN.ABNORMAL_SOURCE IN
              <foreach collection="abnormalSourceList" item="item" index="index" open="(" separator="," close=")">
                  #{item}
              </foreach>
          </if>
          <if test="dealStatusList != null and dealStatusList.size() != 0">
              AND MAIN.DEAL_STATUS IN
              <foreach collection="dealStatusList" item="item" index="index" open="(" separator="," close=")">
                  #{item}
              </foreach>
          </if>
          <if test="createBginDdate != null"> AND MAIN.CREATE_TIMESTAMP &gt;= #{createBginDdate,jdbcType=TIMESTAMP} </if>
          <if test="createEndDate != null">   AND MAIN.CREATE_TIMESTAMP &lt;= #{createEndDate,jdbcType=TIMESTAMP} </if>
          )

      </if>
    ORDER BY C.DEAL_STATUS ASC, C.CREATE_TIMESTAMP DESC,C.ID DESC
  </select>



    <resultMap id="DetailResultMap" type="com.howbuy.crm.account.dao.po.custinfo.CmHkAbnormalRelatedCustPO">
        <!--@mbg.generated-->
        <!--@Table CM_HK_ABNORMAL_RELATED_CUST-->
        <id column="ID" jdbcType="VARCHAR" property="id" />
        <result column="ABNORMAL_ID" jdbcType="VARCHAR" property="abnormalId" />
        <result column="CUST_NO" jdbcType="VARCHAR" property="custNo" />
        <result column="CUST_NAME" jdbcType="VARCHAR" property="custName" />
        <result column="INVEST_TYPE" jdbcType="VARCHAR" property="investType" />
        <result column="MOBILE_AREA_CODE" jdbcType="VARCHAR" property="mobileAreaCode" />
        <result column="MOBILE_DIGEST" jdbcType="VARCHAR" property="mobileDigest" />
        <result column="MOBILE_MASK" jdbcType="VARCHAR" property="mobileMask" />
        <result column="MOBILE_CIPHER" jdbcType="VARCHAR" property="mobileCipher" />
        <result column="ID_SIGN_AREA_CODE" jdbcType="VARCHAR" property="idSignAreaCode" />
        <result column="ID_TYPE" jdbcType="VARCHAR" property="idType" />
        <result column="ID_NO_DIGEST" jdbcType="VARCHAR" property="idNoDigest" />
        <result column="ID_NO_MASK" jdbcType="VARCHAR" property="idNoMask" />
        <result column="ID_NO_CIPHER" jdbcType="VARCHAR" property="idNoCipher" />
        <result column="HBONE_NO" jdbcType="VARCHAR" property="hboneNo" />
        <result column="HK_TX_ACCT_NO" jdbcType="VARCHAR" property="hkTxAcctNo" />
        <result column="CONS_CODE" jdbcType="VARCHAR" property="consCode" />
        <result column="CREATOR" jdbcType="VARCHAR" property="creator" />
        <result column="CREATE_TIMESTAMP" jdbcType="TIMESTAMP" property="createTimestamp" />
        <result column="MODIFIER" jdbcType="VARCHAR" property="modifier" />
        <result column="MODIFY_TIMESTAMP" jdbcType="TIMESTAMP" property="modifyTimestamp" />
    </resultMap>

    <select id="selectRelatedListByMainIdList" resultMap="DetailResultMap">
        SELECT
        S.ID, S.ABNORMAL_ID, S.CUST_NO,
        T.CUSTNAME AS CUST_NAME, T.INVSTTYPE AS INVEST_TYPE,
        T.MOBILE_AREA_CODE, T.MOBILE_DIGEST,
        T.MOBILE_MASK, CI.MOBILE_CIPHER, T.ID_SIGN_AREA_CODE, T.IDTYPE AS ID_TYPE, T.IDNO_DIGEST AS ID_NO_DIGEST, T.IDNO_MASK AS ID_NO_MASK,
        CI.IDNO_CIPHER AS ID_NO_CIPHER, T.HBONE_NO, CM.HK_TX_ACCT_NO, CC.CONSCODE AS CONS_CODE,
        S.CREATOR, S.CREATE_TIMESTAMP, S.MODIFIER,S.MODIFY_TIMESTAMP
        FROM CM_HK_ABNORMAL_RELATED_CUST S
        LEFT JOIN CM_CONSCUST T ON S.CUST_NO=T.CONSCUSTNO
        LEFT JOIN CM_CONSCUST_CIPHER CI ON CI.CONSCUSTNO=T.CONSCUSTNO
        LEFT JOIN CM_HK_CONSCUST CM ON CM.CONSCUSTNO=T.CONSCUSTNO
        LEFT JOIN CM_CUSTCONSTANT CC ON CC.CUSTNO=T.CONSCUSTNO
        WHERE T.CONSCUSTSTATUS = '0'
        AND ( S.ABNORMAL_ID IN
        <foreach item="item" index="index" collection="abnormalIdList" separator=" OR S.ABNORMAL_ID  IN ">
            <foreach collection="item" item="mId" open="(" separator="," close=")">
                #{mId}
            </foreach>
        </foreach>
        )
        ORDER BY T.REGDT DESC, S.CUST_NO DESC
    </select>

    <select id="selectRelatedListByDetailList" resultMap="DetailResultMap">
        SELECT
        S.ID, S.ABNORMAL_ID, S.CUST_NO,
        T.CUSTNAME AS CUST_NAME, T.INVSTTYPE AS INVEST_TYPE,
        T.MOBILE_AREA_CODE, T.MOBILE_DIGEST,
        T.MOBILE_MASK, CI.MOBILE_CIPHER, T.ID_SIGN_AREA_CODE, T.IDTYPE AS ID_TYPE, T.IDNO_DIGEST AS ID_NO_DIGEST, T.IDNO_MASK AS ID_NO_MASK,
        CI.IDNO_CIPHER AS ID_NO_CIPHER, T.HBONE_NO, CM.HK_TX_ACCT_NO, CC.CONSCODE AS CONS_CODE,
        S.CREATOR, S.CREATE_TIMESTAMP, S.MODIFIER,S.MODIFY_TIMESTAMP
        FROM CM_HK_ABNORMAL_RELATED_CUST S
        LEFT JOIN CM_CONSCUST T ON S.CUST_NO=T.CONSCUSTNO
        LEFT JOIN CM_CONSCUST_CIPHER CI ON CI.CONSCUSTNO=T.CONSCUSTNO
        LEFT JOIN CM_HK_CONSCUST CM ON CM.CONSCUSTNO=T.CONSCUSTNO
        LEFT JOIN CM_CUSTCONSTANT CC ON CC.CUSTNO=T.CONSCUSTNO
        WHERE T.CONSCUSTSTATUS = '0'
        AND ( S.ID IN
        <foreach item="item" index="index" collection="detailIdList" separator=" OR S.ABNORMAL_ID  IN ">
            <foreach collection="item" item="mId" open="(" separator="," close=")">
                #{mId}
            </foreach>
        </foreach>
        )
        ORDER BY T.REGDT DESC, S.CUST_NO DESC
    </select>

</mapper>