<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">


<mapper namespace="com.howbuy.crm.account.dao.mapper.consultantexp.CmConsultantExpMapper">
	<insert id="mergeCmConsultantExpByBeisen" parameterType="list">
		merge into CM_CONSULTANT_EXP t1
			using (
				<foreach collection="list" item="item" index="index" open="(" close=")" separator="union all">
					select
					     #{item.userid,jdbcType=VARCHAR} as userid,
						#{item.userno,jdbcType=VARCHAR} as Userno,
						#{item.beisenid,jdbcType=VARCHAR} as Beisenid,
						#{item.consname,jdbcType=VARCHAR} as Consname,
						#{item.countyCode,jdbcType=VARCHAR} as County_Code,
						#{item.email,jdbcType=VARCHAR} as Email,
						#{item.outletcode,jdbcType=VARCHAR} as Outletcode,
						#{item.gender,jdbcType=VARCHAR} as Gender,
						#{item.birthday,jdbcType=VARCHAR} as Birthday,
						#{item.edulevel,jdbcType=VARCHAR} as Edulevel,
						#{item.centerOrg,jdbcType=VARCHAR} as Center_Org,
						#{item.worktype,jdbcType=VARCHAR} as Worktype,
						#{item.workstate,jdbcType=VARCHAR} as Workstate,
						#{item.curmonthlevel,jdbcType=VARCHAR} as Curmonthlevel,
						#{item.curmonthsalary,jdbcType=VARCHAR} as Curmonthsalary,
						#{item.subpositions,jdbcType=VARCHAR} as Subpositions,
						#{item.startdt,jdbcType=VARCHAR} as Startdt,
						#{item.startlevel,jdbcType=VARCHAR} as Startlevel,
						#{item.joinRank,jdbcType=VARCHAR} as Join_Rank,
						#{item.salary,jdbcType=VARCHAR} as Salary,
						#{item.joinSsb,jdbcType=VARCHAR} as Join_Ssb,
						#{item.dt3m,jdbcType=VARCHAR} as Dt3m,
						#{item.regulardt,jdbcType=VARCHAR} as Regulardt,
						#{item.regularlevel,jdbcType=VARCHAR} as Regularlevel,
						#{item.regularRank,jdbcType=VARCHAR} as Regular_Rank,
						#{item.regularsalary,jdbcType=VARCHAR} as Regularsalary,
						#{item.regularSsb,jdbcType=VARCHAR} as Regular_Ssb,
						#{item.dt12m,jdbcType=VARCHAR} as Dt12m,
						#{item.quitdt,jdbcType=VARCHAR} as Quitdt,
						#{item.quitlevel,jdbcType=VARCHAR} as Quitlevel,
						#{item.quitsalary,jdbcType=VARCHAR} as Quitsalary,
						#{item.servingage,jdbcType=VARCHAR} as Servingage,
						#{item.jjcardno,jdbcType=VARCHAR} as Jjcardno,
						#{item.attachtype,jdbcType=VARCHAR} as Attachtype,
						#{item.background,jdbcType=VARCHAR} as Background,
						#{item.source,jdbcType=VARCHAR} as Source,
						#{item.beforepositionage,jdbcType=VARCHAR} as Beforepositionage,
						#{item.recruit,jdbcType=VARCHAR} as Recruit,
						#{item.recommend,jdbcType=VARCHAR} as Recommend,
						#{item.recommenduserno,jdbcType=VARCHAR} as Recommenduserno,
						#{item.recommendtype,jdbcType=VARCHAR} as Recommendtype,
					    #{item.modor,jdbcType=VARCHAR} as modor,
					    #{item.checkflag,jdbcType=VARCHAR} as checkflag,
						#{item.provcode,jdbcType=VARCHAR} as provcode,
						#{item.citycode,jdbcType=VARCHAR} as citycode
					   from dual
				</foreach>
			)t2
			on (t1.userid=t2.userid)
			when matched then
				update set
					t1.Beisenid          = t2.Beisenid
					,t1.Consname          = t2.Consname
					,t1.County_Code       = t2.County_Code
					,t1.Email             = t2.Email
					,t1.Outletcode        = t2.Outletcode
					,t1.Gender            = t2.Gender
					,t1.Birthday          = t2.Birthday
					,t1.Edulevel          = t2.Edulevel
					,t1.Center_Org        = t2.Center_Org
					,t1.Worktype          = t2.Worktype
					,t1.Workstate         = t2.Workstate
					,t1.Curmonthlevel     = t2.Curmonthlevel
					,t1.Curmonthsalary    = t2.Curmonthsalary
					,t1.Subpositions      = t2.Subpositions
					,t1.Startdt           = t2.Startdt
					,t1.Startlevel        = t2.Startlevel
					,t1.Join_Rank         = t2.Join_Rank
					,t1.Salary            = t2.Salary
					,t1.Join_Ssb          = t2.Join_Ssb
					,t1.Dt3m              = t2.Dt3m
					,t1.Regulardt         = t2.Regulardt
					,t1.Regularlevel      = t2.Regularlevel
					,t1.Regular_Rank      = t2.Regular_Rank
					,t1.Regularsalary     = t2.Regularsalary
					,t1.Regular_Ssb       = t2.Regular_Ssb
					,t1.Dt12m             = t2.Dt12m
					,t1.Quitdt            = t2.Quitdt
					,t1.Quitlevel         = t2.Quitlevel
					,t1.Quitsalary        = t2.Quitsalary
					,t1.Servingage        = t2.Servingage
					,t1.Jjcardno          = t2.Jjcardno
					,t1.Attachtype        = t2.Attachtype
					,t1.Background        = t2.Background
					,t1.Source            = t2.Source
					,t1.Beforepositionage = t2.Beforepositionage
					,t1.Recruit           = t2.Recruit
					,t1.Recommend         = t2.Recommend
					,t1.Recommenduserno   = t2.Recommenduserno
					,t1.Recommendtype     = t2.Recommendtype
		            ,t1.modor             = t2.modor
					,t1.provcode         = t2.provcode
					,t1.citycode         = t2.citycode
					,t1.checkflag        = t2.checkflag
				    ,t1.userno 	         = t2.userno
		            ,t1.moddt = sysdate
	</insert>

	<select id="listCmConsultantExpByUserNo" parameterType="List" resultType="com.howbuy.crm.account.dao.po.consultantexp.CmConsultantExpPO">
		SELECT  T1.USERID as conscode,
		T1.CONSNAME,
		T1.EMAIL,
		T1.OUTLETCODE,
		T1.TEAMCODE,
		T1.USERID,
		T1.USERNO,
		T1.PROVCODE,
		T1.CITYCODE,
		T1.GENDER,
		T1.BIRTHDAY,
		T1.EDULEVEL,
		T1.WORKTYPE,
		T1.WORKSTATE,
		T1.CURMONTHLEVEL,
		T1.STARTDT,
		T1.STARTLEVEL,
		T1.SALARY,
		T1.PROBATIONENDDT,
		T1.REGULARDT,
		T1.REGULARLEVEL,
		T1.REGULARSALARY,
		T1.QUITDT,
		T1.QUITLEVEL,
		T1.QUITSALARY,
		T1.QUITREASON,
		T1.SERVINGAGE,
		T1.CHECKFLAG,
		T1.CHECKOR,
		T1.CREATOR,
		T1.CREATDT,
		T1.MODOR,
		T1.MODDT,
		t1.jjcardno,
		t1.attachtype,
		t1.background,
		t1.source,
		t1.beforepositiontype,
		t1.beforepositionage,
		t1.recruit,
		t1.recommend,
		t1.recommenduserno,
		t1.recommendtype,
		t1.remark,
		T1.SUBPOSITIONS,
		T1.NEXTTESTDATE,
		T1.PROBATIONRESULT3M,
		T1.PROBATIONRESULT6M,
		T1.CURMONTHSALARY,
		T1.QUITINFO,
		T1.PROMOTEDATE,
		T1.BXCOMMISSIONWAY,
		T1.BEISENID,
		T1.center_org as centerOrg,
		T1.PROBATION_RESULT_12M as probationResult12M,
		T1.ADJUST_SERVING_MONTH as adjustServingMonth,
		T1.adjust_manage_serving_month as adjustManageServingMonth,
		t2.constlevel,
		T1.DT3M as dt3m,
		T1.DT12M as dt12m,
		T1.NEXT_TEST_PERIOD as nextTestPeriod,
		T1.PROBATION_SALARY_3M as probationSalary3m,
		T1.SALARY_12M as salary12m,
		T1.JOIN_RANK as joinRank,
		T1.PROBATION_RANK_3M as probationRank3m,
		T1.REGULAR_RANK as regularRank,
		T1.RANK_12M as rank12m,
		T1.JOIN_SSB as joinSsb,
		T1.PROBATION_SSB_3M as probationSsb3m,
		T1.REGULAR_SSB as regularSsb,
		T1.SSB_12M as ssb12m,
		T1.PROBATIONLEVEL_3M as probationLevel3m,
		T1.TESTLEVEL_12M as testLevel12m,
		T1.COUNTY_CODE countyCode,
		T1.IN_TRANSIT_REMARK as inTransitRemark
		FROM CM_CONSULTANT_EXP T1
		left join (SELECT userid, max(constlevel) as constlevel
		FROM (select userid,
		startdt,
		regexp_substr(CURMONTHLEVEL, '[^,]+', 1, level) curmonthlevel
		from CM_CONSULTANT_EXP
		connect by level &lt;= regexp_count(curmonthlevel, ',') + 1
		and userid = prior userid
		and prior dbms_random.value is not null) consultexp
		left join hb_constant hbconstant
		on consultexp.curmonthlevel = hbconstant.constcode
		and hbconstant.typecode = 'hrpositionslevel'
		group by userid) t2
		on t1.userid = t2.userid
		where 1=1
		<if test="userNos != null and userNos.size()>0 ">
			and t1.userNo in
			<foreach collection="userNos" item="id" open="(" separator="," close=")">
				#{id}
			</foreach>
		</if>
	</select>

	<select id="getCmConsultantExpByConsCode"
			resultType="com.howbuy.crm.account.dao.po.consultantexp.CmConsultantExpPO">
		SELECT  T1.USERID as conscode,
		T1.CONSNAME,
		T1.EMAIL,
		T1.OUTLETCODE,
		T1.TEAMCODE,
		T1.USERID,
		T1.USERNO,
		T1.PROVCODE,
		T1.CITYCODE,
		T1.GENDER,
		T1.BIRTHDAY,
		T1.EDULEVEL,
		T1.WORKTYPE,
		T1.WORKSTATE,
		T1.CURMONTHLEVEL,
		T1.STARTDT,
		T1.STARTLEVEL,
		T1.SALARY,
		T1.PROBATIONENDDT,
		T1.REGULARDT,
		T1.REGULARLEVEL,
		T1.REGULARSALARY,
		T1.QUITDT,
		T1.QUITLEVEL,
		T1.QUITSALARY,
		T1.QUITREASON,
		T1.SERVINGAGE,
		T1.CHECKFLAG,
		T1.CHECKOR,
		T1.CREATOR,
		T1.CREATDT,
		T1.MODOR,
		T1.MODDT,
		t1.jjcardno,
		t1.attachtype,
		t1.background,
		t1.source,
		t1.beforepositiontype,
		t1.beforepositionage,
		t1.recruit,
		t1.recommend,
		t1.recommenduserno,
		t1.recommendtype,
		t1.remark,
		T1.SUBPOSITIONS,
		T1.NEXTTESTDATE,
		T1.PROBATIONRESULT3M,
		T1.PROBATIONRESULT6M,
		T1.CURMONTHSALARY,
		T1.QUITINFO,
		T1.PROMOTEDATE,
		T1.BXCOMMISSIONWAY,
		T1.BEISENID,
		T1.center_org as centerOrg,
		T1.PROBATION_RESULT_12M as probationResult12M,
		T1.ADJUST_SERVING_MONTH as adjustServingMonth,
		T1.adjust_manage_serving_month as adjustManageServingMonth,
		t2.constlevel,
		T1.DT3M as dt3m,
		T1.DT12M as dt12m,
		T1.NEXT_TEST_PERIOD as nextTestPeriod,
		T1.PROBATION_SALARY_3M as probationSalary3m,
		T1.SALARY_12M as salary12m,
		T1.JOIN_RANK as joinRank,
		T1.PROBATION_RANK_3M as probationRank3m,
		T1.REGULAR_RANK as regularRank,
		T1.RANK_12M as rank12m,
		T1.JOIN_SSB as joinSsb,
		T1.PROBATION_SSB_3M as probationSsb3m,
		T1.REGULAR_SSB as regularSsb,
		T1.SSB_12M as ssb12m,
		T1.PROBATIONLEVEL_3M as probationLevel3m,
		T1.TESTLEVEL_12M as testLevel12m,
		T1.COUNTY_CODE countyCode,
		T1.IN_TRANSIT_REMARK as inTransitRemark
		FROM CM_CONSULTANT_EXP T1
		left join (SELECT userid, max(constlevel) as constlevel
		FROM (select userid,
		startdt,
		regexp_substr(CURMONTHLEVEL, '[^,]+', 1, level) curmonthlevel
		from CM_CONSULTANT_EXP
		connect by level &lt;= regexp_count(curmonthlevel, ',') + 1
		and userid = prior userid
		and prior dbms_random.value is not null) consultexp
		left join hb_constant hbconstant
		on consultexp.curmonthlevel = hbconstant.constcode
		and hbconstant.typecode = 'hrpositionslevel'
		group by userid) t2
		on t1.userid = t2.userid
		where t1.userid = #{consCode}
	</select>

	<select id="listLeadersByOrgCodeAndLevel" resultType="java.lang.String">
		SELECT DISTINCT T1.USERID
		FROM CM_CONSULTANT_EXP T1
		WHERE T1.WORKTYPE != '2'
		  AND T1.OUTLETCODE = #{orgcode}
		<if test="userlevel != null">
			and exists((select regexp_substr(T1.CURMONTHLEVEL, '[^,]+', 1, level)
						from dual
						connect by regexp_substr(T1.CURMONTHLEVEL, '[^,]+', 1, level) is not null)
					   intersect
					   (select regexp_substr(#{userlevel}, '[^|]+', 1, level)
						from dual
						connect by regexp_substr(#{userlevel}, '[^|]+', 1, level) is not null))
		</if>
	</select>

	<select id="listFLeadersByOrgCode" resultType="java.lang.String">
		select b.USERID
		from CM_CONSULTANT_EXP_ORG a,
			 CM_CONSULTANT_EXP b
		where a.userid = b.userid
		  AND b.WORKTYPE != '2'
		  and a.orgcode = #{orgcode}
	</select>

	<select id="getRegionalSubtotalByOrgCode" resultType="java.lang.String">
		select b.userid regionalSubtotal
		from cm_consultant_exp_org a, cm_consultant_exp b
		where a.userid = b.userid
		  and b.worktype != '2'
		  and a.orgcode = #{orgCode,jdbcType=VARCHAR}
	</select>

	<select id="getRegionalSubtotalNameByOrgCodeList" resultType="com.howbuy.crm.account.dao.po.consultant.RegionalSubtotalDTO">
		select a.orgcode orgCode, b.userid regionalSubtotal, b.consname regionalSubtotalName
		from cm_consultant_exp_org a, cm_consultant_exp b
		where a.userid = b.userid
		and b.worktype != '2'
		and a.orgcode in
		<foreach collection="orgCodes" item="orgCode" open="(" separator="," close=")">
			#{orgCode}
		</foreach>
	</select>
</mapper>