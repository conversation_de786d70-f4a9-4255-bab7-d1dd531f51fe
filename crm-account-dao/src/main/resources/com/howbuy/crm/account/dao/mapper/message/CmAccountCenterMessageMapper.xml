<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.howbuy.crm.account.dao.mapper.message.CmAccountCenterMessageMapper">
  <resultMap id="BaseResultMap" type="com.howbuy.crm.account.dao.po.message.CmAccountCenterMessagePO">
    <!--@mbg.generated-->
    <!--@Table CM_ACCOUNT_CENTER_MESSAGE-->
    <id column="ID" jdbcType="VARCHAR" property="id" />
    <result column="ACCOUNT_TYPE" jdbcType="VARCHAR" property="accountType" />
    <result column="TOPIC" jdbcType="VARCHAR" property="topic" />
    <result column="TAG" jdbcType="VARCHAR" property="tag" />
    <result column="MQ_MESSAGE_TEXT" jdbcType="VARCHAR" property="mqMessageText" />
  </resultMap>
  <sql id="Base_Column_List">
    <!--@mbg.generated-->
    ID, ACCOUNT_TYPE, TOPIC, TAG, MQ_MESSAGE_TEXT
  </sql>
  <insert id="insert" parameterType="com.howbuy.crm.account.dao.po.message.CmAccountCenterMessagePO">
    <!--@mbg.generated-->
    insert into CM_ACCOUNT_CENTER_MESSAGE (ID, ACCOUNT_TYPE, TOPIC, 
      TAG, MQ_MESSAGE_TEXT)
    values (#{id,jdbcType=VARCHAR}, #{accountType,jdbcType=VARCHAR}, #{topic,jdbcType=VARCHAR},
      #{tag,jdbcType=VARCHAR}, #{mqMessageText,jdbcType=VARCHAR})
  </insert>
  <insert id="insertSelective" parameterType="com.howbuy.crm.account.dao.po.message.CmAccountCenterMessagePO">
    <!--@mbg.generated-->
    insert into CM_ACCOUNT_CENTER_MESSAGE
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="id != null">
        ID,
      </if>
      <if test="accountType != null">
        ACCOUNT_TYPE,
      </if>
      <if test="topic != null">
        TOPIC,
      </if>
      <if test="tag != null">
        TAG,
      </if>
      <if test="mqMessageText != null">
        MQ_MESSAGE_TEXT,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="id != null">
        #{id,jdbcType=OTHER},
      </if>
      <if test="accountType != null">
        #{accountType,jdbcType=VARCHAR},
      </if>
      <if test="topic != null">
        #{topic,jdbcType=VARCHAR},
      </if>
      <if test="tag != null">
        #{tag,jdbcType=VARCHAR},
      </if>
      <if test="mqMessageText != null">
        #{mqMessageText,jdbcType=VARCHAR},
      </if>
    </trim>
  </insert>
  <select id="selectByPrimaryKey" parameterType="java.lang.String" resultMap="BaseResultMap">
    <!--@mbg.generated-->
    select
    <include refid="Base_Column_List" />
    from CM_ACCOUNT_CENTER_MESSAGE
    where ID = #{id,jdbcType=VARCHAR}
  </select>
</mapper>