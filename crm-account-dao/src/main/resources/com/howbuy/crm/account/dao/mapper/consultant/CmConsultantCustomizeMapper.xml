<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.howbuy.crm.account.dao.mapper.customize.consultant.CmConsultantCustomizeMapper">

    <!--base simple result Map-->
    <!--@mbg.generated-->
    <!--@Table CM_CONSULTANT-->
    <resultMap id="BaseSimpleResultMap" type="com.howbuy.crm.account.dao.po.consultant.ConsultantSimpleInfoDto">
        <result column="CONSCODE" property="consCode" jdbcType="VARCHAR"/>
        <result column="CONSNAME" property="consName" jdbcType="VARCHAR"/>
        <result column="CONSLEVEL" property="consLevel" jdbcType="VARCHAR"/>
        <result column="CONSSTATUS" property="consStatus" jdbcType="VARCHAR"/>
        <result column="ISVIRTUAL" property="isVirtual" jdbcType="VARCHAR"/>
        <result column="OUTLETCODE" property="outletCode" jdbcType="VARCHAR"/>
        <result column="TEAMCODE" property="teamCode" jdbcType="VARCHAR"/>
        <result column="MOBILE" property="mobile" jdbcType="VARCHAR"/>
        <result column="PICADDR" property="picAddr" jdbcType="VARCHAR"/>
        <result column="CODEPICADDR" property="codePicAddr" jdbcType="VARCHAR"/>
        <result column="POSITION" property="position" jdbcType="VARCHAR"/>
        <result column="EMAIL" property="email" jdbcType="VARCHAR"/>
        <result column="CENTERORGCODE" property="centerOrgCode" jdbcType="VARCHAR"/>
    </resultMap>
    <!-- 基础结果集对应的表字段 -->
    <sql id="Base_SIMPLE_Column_List">
        CONSCODE,CONSNAME,CONSLEVEL,CONSSTATUS,ISVIRTUAL,OUTLETCODE,TEAMCODE,
        MOBILE,PICADDR,CODEPICADDR,POSITION,EMAIL
    </sql>


    <select id="getListByConscodes"
            resultMap="com.howbuy.crm.account.dao.mapper.consultant.CmConsultantMapper.BaseResultMap">
        select
        <include refid="com.howbuy.crm.account.dao.mapper.consultant.CmConsultantMapper.Base_Column_List"/>
        from CM_CONSULTANT
        where consstatus = '1'
          and CONSCODE in
        <foreach collection="consCodeList" index="index" item="item" open="(" close=")" separator=",">
            #{item}
        </foreach>
    </select>

    <select id="queryListByVo" resultMap="com.howbuy.crm.account.dao.mapper.consultant.CmConsultantMapper.BaseResultMap"
            parameterType="com.howbuy.crm.account.dao.req.consultant.QueryConsultantReqVO">
        select
        <include refid="com.howbuy.crm.account.dao.mapper.consultant.CmConsultantMapper.Base_Column_List"/>
        from CM_CONSULTANT
        where 1=1
        <if test='consStatus == "1"'>
            and consstatus = '1'
        </if>
        <if test="userId != null">
            and WECHATCONSCODE = #{userId,jdbcType=VARCHAR}
        </if>
        <if test="consCodeList != null and consCodeList.size() != 0">
            and CONSCODE in
            <foreach collection="consCodeList" index="index" item="item" open="(" close=")" separator=",">
                #{item}
            </foreach>
        </if>
    </select>

    <select id="getContainQuitListByConsCodes"
            resultMap="com.howbuy.crm.account.dao.mapper.consultant.CmConsultantMapper.BaseResultMap">
        select
        <include refid="com.howbuy.crm.account.dao.mapper.consultant.CmConsultantMapper.Base_Column_List"/>
        from CM_CONSULTANT
        where CONSCODE in
        <foreach collection="consCodeList" index="index" item="item" open="(" close=")" separator=",">
            #{item}
        </foreach>
    </select>

    <select id="searchConsultant" resultType="com.howbuy.crm.account.dao.po.consultant.SearchConsultantDTO">
        select conscode as consCode,consname as consName, outletcode as orgCode from CM_CONSULTANT
        where
            consstatus = '1'
        <if test="keyword != null and keyword != ''">
            and CONSNAME like '%' || #{keyword,jdbcType=VARCHAR} || '%'
        </if>
        <if test="isPm">
            and conscode in (
                select usercode from hb_userrole where rolecode in
                    <foreach collection="pmRoleCodeList" index="index" item="item" open="(" close=")" separator=",">
                        #{item}
                    </foreach>
            )
        </if>
    </select>

    <select id="getConsCodeByName" resultType="string">
        select conscode from CM_CONSULTANT where consname = #{consName}
    </select>

    <select id="getSimpleConsultList" resultMap="BaseSimpleResultMap">
        WITH CENTER_CODE AS (
        SELECT T.ORGCODE,T.ORGNAME,T.PARENTORGCODE,CONNECT_BY_ROOT orgcode AS CENTERORGCODE
        FROM  HB_ORGANIZATION T
        START WITH T.PARENTORGCODE = '0' <!-- ORGCODE=0-HOWBUY直属下的-->
        CONNECT BY T.PARENTORGCODE = PRIOR T.ORGCODE )
        SELECT
        <include refid="Base_SIMPLE_Column_List" />
        ,C.CENTERORGCODE
        FROM CM_CONSULTANT T
        LEFT JOIN CENTER_CODE C  ON T.OUTLETCODE=C.ORGCODE
    </select>

    <select id="getSimpleConsultantByCode" parameterType="string" resultMap="BaseSimpleResultMap">
        WITH CENTER_CODE AS (
        SELECT T.ORGCODE,T.ORGNAME,T.PARENTORGCODE,CONNECT_BY_ROOT orgcode AS CENTERORGCODE
        FROM  HB_ORGANIZATION T
        START WITH T.PARENTORGCODE = '0' <!-- ORGCODE=0-HOWBUY直属下的-->
        CONNECT BY T.PARENTORGCODE = PRIOR T.ORGCODE )
        SELECT <include refid="Base_SIMPLE_Column_List" />
        ,C.CENTERORGCODE
        FROM CM_CONSULTANT T
        LEFT JOIN CENTER_CODE C  ON T.OUTLETCODE=C.ORGCODE
        <where>
            AND T.CONSCODE = #{consCode, jdbcType=VARCHAR}
        </where>
    </select>
</mapper>