<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.howbuy.crm.account.dao.mapper.custinfo.CmConscusthisMapper">
  <resultMap id="BaseResultMap" type="com.howbuy.crm.account.dao.po.custinfo.CmConscusthisPO">
    <!--@mbg.generated-->
    <!--@Table CM_CONSCUSTHIS-->
    <result column="APPSERIALNO" jdbcType="VARCHAR" property="appserialno" />
    <result column="CONSCUSTNO" jdbcType="VARCHAR" property="conscustno" />
    <result column="CONSCUSTLVL" jdbcType="VARCHAR" property="conscustlvl" />
    <result column="CONSCUSTGRADE" jdbcType="DECIMAL" property="conscustgrade" />
    <result column="CONSCUSTSTATUS" jdbcType="VARCHAR" property="conscuststatus" />
    <result column="IDTYPE" jdbcType="VARCHAR" property="idtype" />
    <result column="IDNO_TM" jdbcType="VARCHAR" property="idnoTm" />
    <result column="CUSTNAME" jdbcType="VARCHAR" property="custname" />
    <result column="PROVCODE" jdbcType="VARCHAR" property="provcode" />
    <result column="CITYCODE" jdbcType="VARCHAR" property="citycode" />
    <result column="EDULEVEL" jdbcType="VARCHAR" property="edulevel" />
    <result column="VOCATION" jdbcType="VARCHAR" property="vocation" />
    <result column="INCLEVEL" jdbcType="VARCHAR" property="inclevel" />
    <result column="BIRTHDAY" jdbcType="VARCHAR" property="birthday" />
    <result column="GENDER" jdbcType="VARCHAR" property="gender" />
    <result column="MARRIED" jdbcType="VARCHAR" property="married" />
    <result column="PINCOME" jdbcType="VARCHAR" property="pincome" />
    <result column="FINCOME" jdbcType="VARCHAR" property="fincome" />
    <result column="DECISIONFLAG" jdbcType="VARCHAR" property="decisionflag" />
    <result column="INTERESTS" jdbcType="VARCHAR" property="interests" />
    <result column="FAMILYCONDITION" jdbcType="VARCHAR" property="familycondition" />
    <result column="CONTACTTIME" jdbcType="VARCHAR" property="contacttime" />
    <result column="SENDINFOFLAG" jdbcType="VARCHAR" property="sendinfoflag" />
    <result column="RECVTELFLAG" jdbcType="VARCHAR" property="recvtelflag" />
    <result column="RECVEMAILFLAG" jdbcType="VARCHAR" property="recvemailflag" />
    <result column="RECVMSGFLAG" jdbcType="VARCHAR" property="recvmsgflag" />
    <result column="COMPANY" jdbcType="VARCHAR" property="company" />
    <result column="RISKLEVEL" jdbcType="VARCHAR" property="risklevel" />
    <result column="SELFRISKLEVEL" jdbcType="VARCHAR" property="selfrisklevel" />
    <result column="ADDR_TM" jdbcType="VARCHAR" property="addrTm" />
    <result column="POSTCODE" jdbcType="VARCHAR" property="postcode" />
    <result column="MOBILE_TM" jdbcType="VARCHAR" property="mobileTm" />
    <result column="TELNO_TM" jdbcType="VARCHAR" property="telnoTm" />
    <result column="FAX" jdbcType="VARCHAR" property="fax" />
    <result column="EMAIL_TM" jdbcType="VARCHAR" property="emailTm" />
    <result column="OFFICETELNO" jdbcType="VARCHAR" property="officetelno" />
    <result column="SOURCE" jdbcType="VARCHAR" property="source" />
    <result column="KNOWCHAN" jdbcType="VARCHAR" property="knowchan" />
    <result column="OTHERCHAN" jdbcType="VARCHAR" property="otherchan" />
    <result column="OTHERINVEST" jdbcType="VARCHAR" property="otherinvest" />
    <result column="SALON" jdbcType="VARCHAR" property="salon" />
    <result column="BEFOREINVEST" jdbcType="VARCHAR" property="beforeinvest" />
    <result column="SELFDEFFLAG" jdbcType="VARCHAR" property="selfdefflag" />
    <result column="VISITFQCY" jdbcType="VARCHAR" property="visitfqcy" />
    <result column="SALEDIRECTION" jdbcType="VARCHAR" property="saledirection" />
    <result column="DEVDIRECTION" jdbcType="VARCHAR" property="devdirection" />
    <result column="SUBSOURCE" jdbcType="VARCHAR" property="subsource" />
    <result column="SUBSOURCETYPE" jdbcType="VARCHAR" property="subsourcetype" />
    <result column="REMARK" jdbcType="VARCHAR" property="remark" />
    <result column="ADDR2_TM" jdbcType="VARCHAR" property="addr2Tm" />
    <result column="POSTCODE2" jdbcType="VARCHAR" property="postcode2" />
    <result column="MOBILE2_TM" jdbcType="VARCHAR" property="mobile2Tm" />
    <result column="EMAIL2_TM" jdbcType="VARCHAR" property="email2Tm" />
    <result column="KNOWHOWBUY" jdbcType="VARCHAR" property="knowhowbuy" />
    <result column="SUBKNOW" jdbcType="VARCHAR" property="subknow" />
    <result column="SUBKNOWTYPE" jdbcType="VARCHAR" property="subknowtype" />
    <result column="BUYINGPROD" jdbcType="VARCHAR" property="buyingprod" />
    <result column="BUYEDPROD" jdbcType="VARCHAR" property="buyedprod" />
    <result column="FREEPROD" jdbcType="VARCHAR" property="freeprod" />
    <result column="SPECIALFLAG" jdbcType="VARCHAR" property="specialflag" />
    <result column="DLVYMODE" jdbcType="VARCHAR" property="dlvymode" />
    <result column="SPECIALREASON" jdbcType="VARCHAR" property="specialreason" />
    <result column="CREATOR" jdbcType="VARCHAR" property="creator" />
    <result column="CHECKER" jdbcType="VARCHAR" property="checker" />
    <result column="STIMESTAMP" jdbcType="TIMESTAMP" property="stimestamp" />
    <result column="PRIRISKLEVEL" jdbcType="VARCHAR" property="pririsklevel" />
    <result column="LINKMAN" jdbcType="VARCHAR" property="linkman" />
    <result column="LINKTEL_TM" jdbcType="VARCHAR" property="linktelTm" />
    <result column="LINKMOBILE_TM" jdbcType="VARCHAR" property="linkmobileTm" />
    <result column="LINKEMAIL_TM" jdbcType="VARCHAR" property="linkemailTm" />
    <result column="LINKPOSTCODE" jdbcType="VARCHAR" property="linkpostcode" />
    <result column="LINKADDR_TM" jdbcType="VARCHAR" property="linkaddrTm" />
    <result column="CAPACITY" jdbcType="VARCHAR" property="capacity" />
    <result column="GPSINVESTLEVEL" jdbcType="VARCHAR" property="gpsinvestlevel" />
    <result column="GPSRISKLEVEL" jdbcType="VARCHAR" property="gpsrisklevel" />
    <result column="ISBOSS" jdbcType="VARCHAR" property="isboss" />
    <result column="FINANCENEED" jdbcType="VARCHAR" property="financeneed" />
    <result column="ISJOINCLUB" jdbcType="VARCHAR" property="isjoinclub" />
    <result column="EXPLANATION" jdbcType="VARCHAR" property="explanation" />
    <result column="ISRISKTIP" jdbcType="VARCHAR" property="isrisktip" />
    <result column="CUSTSOURCEREMARK" jdbcType="VARCHAR" property="custsourceremark" />
    <result column="PMARKETAMT" jdbcType="DECIMAL" property="pmarketamt" />
    <result column="ISWRITEBOOK" jdbcType="VARCHAR" property="iswritebook" />
    <result column="INVSTTYPE" jdbcType="VARCHAR" property="invsttype" />
    <result column="SOURCE2" jdbcType="VARCHAR" property="source2" />
    <result column="SUBSOURCE2" jdbcType="VARCHAR" property="subsource2" />
    <result column="SUBSOURCETYPE2" jdbcType="VARCHAR" property="subsourcetype2" />
    <result column="VIPUSERNAME" jdbcType="VARCHAR" property="vipusername" />
    <result column="WECHATCODE" jdbcType="VARCHAR" property="wechatcode" />
    <result column="NEWSOURCENO" jdbcType="VARCHAR" property="newsourceno" />
    <result column="HBONE_NO" jdbcType="VARCHAR" property="hboneNo" />
    <result column="NEWSOURCENO2" jdbcType="VARCHAR" property="newsourceno2" />
    <result column="HOPETRADETYPE" jdbcType="VARCHAR" property="hopetradetype" />
    <result column="VALIDITY" jdbcType="VARCHAR" property="validity" />
    <result column="VALIDITYDT" jdbcType="VARCHAR" property="validitydt" />
    <result column="NATURE" jdbcType="VARCHAR" property="nature" />
    <result column="APTITUDE" jdbcType="VARCHAR" property="aptitude" />
    <result column="SCOPEBUSINESS" jdbcType="VARCHAR" property="scopebusiness" />
    <result column="RESTYPE" jdbcType="VARCHAR" property="restype" />
    <result column="PROVCODE_MOBILE" jdbcType="VARCHAR" property="provcodeMobile" />
    <result column="CITYCODE_MOBILE" jdbcType="VARCHAR" property="citycodeMobile" />
    <result column="ORGTYPE" jdbcType="VARCHAR" property="orgtype" />
    <result column="PINYIN" jdbcType="VARCHAR" property="pinyin" />
    <result column="IDNO_DIGEST" jdbcType="VARCHAR" property="idnoDigest" />
    <result column="IDNO_MASK" jdbcType="VARCHAR" property="idnoMask" />
    <result column="CUSTNAME_DIGEST" jdbcType="VARCHAR" property="custnameDigest" />
    <result column="CUSTNAME_MASK" jdbcType="VARCHAR" property="custnameMask" />
    <result column="ADDR_DIGEST" jdbcType="VARCHAR" property="addrDigest" />
    <result column="ADDR_MASK" jdbcType="VARCHAR" property="addrMask" />
    <result column="MOBILE_DIGEST" jdbcType="VARCHAR" property="mobileDigest" />
    <result column="MOBILE_MASK" jdbcType="VARCHAR" property="mobileMask" />
    <result column="TELNO_DIGEST" jdbcType="VARCHAR" property="telnoDigest" />
    <result column="TELNO_MASK" jdbcType="VARCHAR" property="telnoMask" />
    <result column="EMAIL_DIGEST" jdbcType="VARCHAR" property="emailDigest" />
    <result column="EMAIL_MASK" jdbcType="VARCHAR" property="emailMask" />
    <result column="ADDR2_DIGEST" jdbcType="VARCHAR" property="addr2Digest" />
    <result column="ADDR2_MASK" jdbcType="VARCHAR" property="addr2Mask" />
    <result column="MOBILE2_DIGEST" jdbcType="VARCHAR" property="mobile2Digest" />
    <result column="MOBILE2_MASK" jdbcType="VARCHAR" property="mobile2Mask" />
    <result column="EMAIL2_DIGEST" jdbcType="VARCHAR" property="email2Digest" />
    <result column="EMAIL2_MASK" jdbcType="VARCHAR" property="email2Mask" />
    <result column="LINKMAN_DIGEST" jdbcType="VARCHAR" property="linkmanDigest" />
    <result column="LINKMAN_MASK" jdbcType="VARCHAR" property="linkmanMask" />
    <result column="LINKTEL_DIGEST" jdbcType="VARCHAR" property="linktelDigest" />
    <result column="LINKTEL_MASK" jdbcType="VARCHAR" property="linktelMask" />
    <result column="LINKMOBILE_DIGEST" jdbcType="VARCHAR" property="linkmobileDigest" />
    <result column="LINKMOBILE_MASK" jdbcType="VARCHAR" property="linkmobileMask" />
    <result column="LINKEMAIL_DIGEST" jdbcType="VARCHAR" property="linkemailDigest" />
    <result column="LINKEMAIL_MASK" jdbcType="VARCHAR" property="linkemailMask" />
    <result column="LINKADDR_DIGEST" jdbcType="VARCHAR" property="linkaddrDigest" />
    <result column="LINKADDR_MASK" jdbcType="VARCHAR" property="linkaddrMask" />
    <result column="ISVIRTUALSHARER" jdbcType="VARCHAR" property="isvirtualsharer" />
    <result column="MOBILE_AREA_CODE" jdbcType="VARCHAR" property="mobileAreaCode" />
    <result column="MOBILE2_AREA_CODE" jdbcType="VARCHAR" property="mobile2AreaCode" />
    <result column="LINKMOBILE_AREA_CODE" jdbcType="VARCHAR" property="linkmobileAreaCode" />
    <result column="ID_SIGN_AREA_CODE" jdbcType="VARCHAR" property="idSignAreaCode" />
    <result column="NATION_CODE" jdbcType="VARCHAR" property="nationCode" />
    <result column="HBONE_TIMESTAMP" jdbcType="TIMESTAMP" property="hboneTimestamp" />
    <result column="COUNTY_CODE" jdbcType="VARCHAR" property="countyCode" />
  </resultMap>
  <sql id="Base_Column_List">
    <!--@mbg.generated-->
    APPSERIALNO, CONSCUSTNO, CONSCUSTLVL, CONSCUSTGRADE, CONSCUSTSTATUS, IDTYPE, IDNO_TM, 
    CUSTNAME, PROVCODE, CITYCODE, EDULEVEL, VOCATION, INCLEVEL, BIRTHDAY, GENDER, MARRIED, 
    PINCOME, FINCOME, DECISIONFLAG, INTERESTS, FAMILYCONDITION, CONTACTTIME, SENDINFOFLAG, 
    RECVTELFLAG, RECVEMAILFLAG, RECVMSGFLAG, COMPANY, RISKLEVEL, SELFRISKLEVEL, ADDR_TM, 
    POSTCODE, MOBILE_TM, TELNO_TM, FAX, EMAIL_TM, OFFICETELNO, "SOURCE", KNOWCHAN, OTHERCHAN, 
    OTHERINVEST, SALON, BEFOREINVEST, SELFDEFFLAG, VISITFQCY, SALEDIRECTION, DEVDIRECTION, 
    SUBSOURCE, SUBSOURCETYPE, REMARK, ADDR2_TM, POSTCODE2, MOBILE2_TM, EMAIL2_TM, KNOWHOWBUY, 
    SUBKNOW, SUBKNOWTYPE, BUYINGPROD, BUYEDPROD, FREEPROD, SPECIALFLAG, DLVYMODE, SPECIALREASON, 
    CREATOR, CHECKER, STIMESTAMP, PRIRISKLEVEL, LINKMAN, LINKTEL_TM, LINKMOBILE_TM, LINKEMAIL_TM, 
    LINKPOSTCODE, LINKADDR_TM, CAPACITY, GPSINVESTLEVEL, GPSRISKLEVEL, ISBOSS, FINANCENEED, 
    ISJOINCLUB, EXPLANATION, ISRISKTIP, CUSTSOURCEREMARK, PMARKETAMT, ISWRITEBOOK, INVSTTYPE, 
    SOURCE2, SUBSOURCE2, SUBSOURCETYPE2, VIPUSERNAME, WECHATCODE, NEWSOURCENO, HBONE_NO, 
    NEWSOURCENO2, HOPETRADETYPE, VALIDITY, VALIDITYDT, NATURE, APTITUDE, SCOPEBUSINESS, 
    RESTYPE, PROVCODE_MOBILE, CITYCODE_MOBILE, ORGTYPE, PINYIN, IDNO_DIGEST, IDNO_MASK, 
    CUSTNAME_DIGEST, CUSTNAME_MASK, ADDR_DIGEST, ADDR_MASK, MOBILE_DIGEST, MOBILE_MASK, 
    TELNO_DIGEST, TELNO_MASK, EMAIL_DIGEST, EMAIL_MASK, ADDR2_DIGEST, ADDR2_MASK, MOBILE2_DIGEST, 
    MOBILE2_MASK, EMAIL2_DIGEST, EMAIL2_MASK, LINKMAN_DIGEST, LINKMAN_MASK, LINKTEL_DIGEST, 
    LINKTEL_MASK, LINKMOBILE_DIGEST, LINKMOBILE_MASK, LINKEMAIL_DIGEST, LINKEMAIL_MASK, 
    LINKADDR_DIGEST, LINKADDR_MASK, ISVIRTUALSHARER, MOBILE_AREA_CODE, MOBILE2_AREA_CODE, 
    LINKMOBILE_AREA_CODE, ID_SIGN_AREA_CODE, NATION_CODE, HBONE_TIMESTAMP, COUNTY_CODE
  </sql>
  <insert id="insert" parameterType="com.howbuy.crm.account.dao.po.custinfo.CmConscusthisPO">
    <!--@mbg.generated-->
    insert into CM_CONSCUSTHIS (APPSERIALNO, CONSCUSTNO, CONSCUSTLVL, 
      CONSCUSTGRADE, CONSCUSTSTATUS, IDTYPE, 
      IDNO_TM, CUSTNAME, PROVCODE, 
      CITYCODE, EDULEVEL, VOCATION, 
      INCLEVEL, BIRTHDAY, GENDER, 
      MARRIED, PINCOME, FINCOME, 
      DECISIONFLAG, INTERESTS, FAMILYCONDITION, 
      CONTACTTIME, SENDINFOFLAG, RECVTELFLAG, 
      RECVEMAILFLAG, RECVMSGFLAG, COMPANY, 
      RISKLEVEL, SELFRISKLEVEL, ADDR_TM, 
      POSTCODE, MOBILE_TM, TELNO_TM, 
      FAX, EMAIL_TM, OFFICETELNO, 
      "SOURCE", KNOWCHAN, OTHERCHAN, 
      OTHERINVEST, SALON, BEFOREINVEST, 
      SELFDEFFLAG, VISITFQCY, SALEDIRECTION, 
      DEVDIRECTION, SUBSOURCE, SUBSOURCETYPE, 
      REMARK, ADDR2_TM, POSTCODE2, 
      MOBILE2_TM, EMAIL2_TM, KNOWHOWBUY, 
      SUBKNOW, SUBKNOWTYPE, BUYINGPROD, 
      BUYEDPROD, FREEPROD, SPECIALFLAG, 
      DLVYMODE, SPECIALREASON, CREATOR, 
      CHECKER, STIMESTAMP, PRIRISKLEVEL, 
      LINKMAN, LINKTEL_TM, LINKMOBILE_TM, 
      LINKEMAIL_TM, LINKPOSTCODE, LINKADDR_TM, 
      CAPACITY, GPSINVESTLEVEL, GPSRISKLEVEL, 
      ISBOSS, FINANCENEED, ISJOINCLUB, 
      EXPLANATION, ISRISKTIP, CUSTSOURCEREMARK, 
      PMARKETAMT, ISWRITEBOOK, INVSTTYPE, 
      SOURCE2, SUBSOURCE2, SUBSOURCETYPE2, 
      VIPUSERNAME, WECHATCODE, NEWSOURCENO, 
      HBONE_NO, NEWSOURCENO2, HOPETRADETYPE, 
      VALIDITY, VALIDITYDT, NATURE, 
      APTITUDE, SCOPEBUSINESS, RESTYPE, 
      PROVCODE_MOBILE, CITYCODE_MOBILE, ORGTYPE, 
      PINYIN, IDNO_DIGEST, IDNO_MASK, 
      CUSTNAME_DIGEST, CUSTNAME_MASK, ADDR_DIGEST, 
      ADDR_MASK, MOBILE_DIGEST, MOBILE_MASK, 
      TELNO_DIGEST, TELNO_MASK, EMAIL_DIGEST, 
      EMAIL_MASK, ADDR2_DIGEST, ADDR2_MASK, 
      MOBILE2_DIGEST, MOBILE2_MASK, EMAIL2_DIGEST, 
      EMAIL2_MASK, LINKMAN_DIGEST, LINKMAN_MASK, 
      LINKTEL_DIGEST, LINKTEL_MASK, LINKMOBILE_DIGEST, 
      LINKMOBILE_MASK, LINKEMAIL_DIGEST, LINKEMAIL_MASK, 
      LINKADDR_DIGEST, LINKADDR_MASK, ISVIRTUALSHARER, 
      MOBILE_AREA_CODE, MOBILE2_AREA_CODE, LINKMOBILE_AREA_CODE,
      ID_SIGN_AREA_CODE, NATION_CODE, HBONE_TIMESTAMP, 
      COUNTY_CODE)
    values (#{appserialno,jdbcType=VARCHAR}, #{conscustno,jdbcType=VARCHAR}, #{conscustlvl,jdbcType=VARCHAR}, 
      #{conscustgrade,jdbcType=DECIMAL}, #{conscuststatus,jdbcType=VARCHAR}, #{idtype,jdbcType=VARCHAR}, 
      #{idnoTm,jdbcType=VARCHAR}, #{custname,jdbcType=VARCHAR}, #{provcode,jdbcType=VARCHAR}, 
      #{citycode,jdbcType=VARCHAR}, #{edulevel,jdbcType=VARCHAR}, #{vocation,jdbcType=VARCHAR}, 
      #{inclevel,jdbcType=VARCHAR}, #{birthday,jdbcType=VARCHAR}, #{gender,jdbcType=VARCHAR}, 
      #{married,jdbcType=VARCHAR}, #{pincome,jdbcType=VARCHAR}, #{fincome,jdbcType=VARCHAR}, 
      #{decisionflag,jdbcType=VARCHAR}, #{interests,jdbcType=VARCHAR}, #{familycondition,jdbcType=VARCHAR}, 
      #{contacttime,jdbcType=VARCHAR}, #{sendinfoflag,jdbcType=VARCHAR}, #{recvtelflag,jdbcType=VARCHAR}, 
      #{recvemailflag,jdbcType=VARCHAR}, #{recvmsgflag,jdbcType=VARCHAR}, #{company,jdbcType=VARCHAR}, 
      #{risklevel,jdbcType=VARCHAR}, #{selfrisklevel,jdbcType=VARCHAR}, #{addrTm,jdbcType=VARCHAR}, 
      #{postcode,jdbcType=VARCHAR}, #{mobileTm,jdbcType=VARCHAR}, #{telnoTm,jdbcType=VARCHAR}, 
      #{fax,jdbcType=VARCHAR}, #{emailTm,jdbcType=VARCHAR}, #{officetelno,jdbcType=VARCHAR}, 
      #{source,jdbcType=VARCHAR}, #{knowchan,jdbcType=VARCHAR}, #{otherchan,jdbcType=VARCHAR}, 
      #{otherinvest,jdbcType=VARCHAR}, #{salon,jdbcType=VARCHAR}, #{beforeinvest,jdbcType=VARCHAR}, 
      #{selfdefflag,jdbcType=VARCHAR}, #{visitfqcy,jdbcType=VARCHAR}, #{saledirection,jdbcType=VARCHAR}, 
      #{devdirection,jdbcType=VARCHAR}, #{subsource,jdbcType=VARCHAR}, #{subsourcetype,jdbcType=VARCHAR}, 
      #{remark,jdbcType=VARCHAR}, #{addr2Tm,jdbcType=VARCHAR}, #{postcode2,jdbcType=VARCHAR}, 
      #{mobile2Tm,jdbcType=VARCHAR}, #{email2Tm,jdbcType=VARCHAR}, #{knowhowbuy,jdbcType=VARCHAR}, 
      #{subknow,jdbcType=VARCHAR}, #{subknowtype,jdbcType=VARCHAR}, #{buyingprod,jdbcType=VARCHAR}, 
      #{buyedprod,jdbcType=VARCHAR}, #{freeprod,jdbcType=VARCHAR}, #{specialflag,jdbcType=VARCHAR}, 
      #{dlvymode,jdbcType=VARCHAR}, #{specialreason,jdbcType=VARCHAR}, #{creator,jdbcType=VARCHAR}, 
      #{checker,jdbcType=VARCHAR}, #{stimestamp,jdbcType=TIMESTAMP}, #{pririsklevel,jdbcType=VARCHAR}, 
      #{linkman,jdbcType=VARCHAR}, #{linktelTm,jdbcType=VARCHAR}, #{linkmobileTm,jdbcType=VARCHAR}, 
      #{linkemailTm,jdbcType=VARCHAR}, #{linkpostcode,jdbcType=VARCHAR}, #{linkaddrTm,jdbcType=VARCHAR}, 
      #{capacity,jdbcType=VARCHAR}, #{gpsinvestlevel,jdbcType=VARCHAR}, #{gpsrisklevel,jdbcType=VARCHAR}, 
      #{isboss,jdbcType=VARCHAR}, #{financeneed,jdbcType=VARCHAR}, #{isjoinclub,jdbcType=VARCHAR}, 
      #{explanation,jdbcType=VARCHAR}, #{isrisktip,jdbcType=VARCHAR}, #{custsourceremark,jdbcType=VARCHAR}, 
      #{pmarketamt,jdbcType=DECIMAL}, #{iswritebook,jdbcType=VARCHAR}, #{invsttype,jdbcType=VARCHAR}, 
      #{source2,jdbcType=VARCHAR}, #{subsource2,jdbcType=VARCHAR}, #{subsourcetype2,jdbcType=VARCHAR}, 
      #{vipusername,jdbcType=VARCHAR}, #{wechatcode,jdbcType=VARCHAR}, #{newsourceno,jdbcType=VARCHAR}, 
      #{hboneNo,jdbcType=VARCHAR}, #{newsourceno2,jdbcType=VARCHAR}, #{hopetradetype,jdbcType=VARCHAR}, 
      #{validity,jdbcType=VARCHAR}, #{validitydt,jdbcType=VARCHAR}, #{nature,jdbcType=VARCHAR}, 
      #{aptitude,jdbcType=VARCHAR}, #{scopebusiness,jdbcType=VARCHAR}, #{restype,jdbcType=VARCHAR}, 
      #{provcodeMobile,jdbcType=VARCHAR}, #{citycodeMobile,jdbcType=VARCHAR}, #{orgtype,jdbcType=VARCHAR}, 
      #{pinyin,jdbcType=VARCHAR}, #{idnoDigest,jdbcType=VARCHAR}, #{idnoMask,jdbcType=VARCHAR}, 
      #{custnameDigest,jdbcType=VARCHAR}, #{custnameMask,jdbcType=VARCHAR}, #{addrDigest,jdbcType=VARCHAR}, 
      #{addrMask,jdbcType=VARCHAR}, #{mobileDigest,jdbcType=VARCHAR}, #{mobileMask,jdbcType=VARCHAR}, 
      #{telnoDigest,jdbcType=VARCHAR}, #{telnoMask,jdbcType=VARCHAR}, #{emailDigest,jdbcType=VARCHAR}, 
      #{emailMask,jdbcType=VARCHAR}, #{addr2Digest,jdbcType=VARCHAR}, #{addr2Mask,jdbcType=VARCHAR}, 
      #{mobile2Digest,jdbcType=VARCHAR}, #{mobile2Mask,jdbcType=VARCHAR}, #{email2Digest,jdbcType=VARCHAR}, 
      #{email2Mask,jdbcType=VARCHAR}, #{linkmanDigest,jdbcType=VARCHAR}, #{linkmanMask,jdbcType=VARCHAR}, 
      #{linktelDigest,jdbcType=VARCHAR}, #{linktelMask,jdbcType=VARCHAR}, #{linkmobileDigest,jdbcType=VARCHAR}, 
      #{linkmobileMask,jdbcType=VARCHAR}, #{linkemailDigest,jdbcType=VARCHAR}, #{linkemailMask,jdbcType=VARCHAR}, 
      #{linkaddrDigest,jdbcType=VARCHAR}, #{linkaddrMask,jdbcType=VARCHAR}, #{isvirtualsharer,jdbcType=VARCHAR}, 
      #{mobileAreaCode,jdbcType=VARCHAR}, #{mobile2AreaCode,jdbcType=VARCHAR}, #{linkmobileAreaCode,jdbcType=VARCHAR}, 
      #{idSignAreaCode,jdbcType=VARCHAR}, #{nationCode,jdbcType=VARCHAR}, #{hboneTimestamp,jdbcType=TIMESTAMP}, 
      #{countyCode,jdbcType=VARCHAR})
  </insert>
  <insert id="insertSelective" parameterType="com.howbuy.crm.account.dao.po.custinfo.CmConscusthisPO">
    <!--@mbg.generated-->
    insert into CM_CONSCUSTHIS
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="appserialno != null">
        APPSERIALNO,
      </if>
      <if test="conscustno != null">
        CONSCUSTNO,
      </if>
      <if test="conscustlvl != null">
        CONSCUSTLVL,
      </if>
      <if test="conscustgrade != null">
        CONSCUSTGRADE,
      </if>
      <if test="conscuststatus != null">
        CONSCUSTSTATUS,
      </if>
      <if test="idtype != null">
        IDTYPE,
      </if>
      <if test="idnoTm != null">
        IDNO_TM,
      </if>
      <if test="custname != null">
        CUSTNAME,
      </if>
      <if test="provcode != null">
        PROVCODE,
      </if>
      <if test="citycode != null">
        CITYCODE,
      </if>
      <if test="edulevel != null">
        EDULEVEL,
      </if>
      <if test="vocation != null">
        VOCATION,
      </if>
      <if test="inclevel != null">
        INCLEVEL,
      </if>
      <if test="birthday != null">
        BIRTHDAY,
      </if>
      <if test="gender != null">
        GENDER,
      </if>
      <if test="married != null">
        MARRIED,
      </if>
      <if test="pincome != null">
        PINCOME,
      </if>
      <if test="fincome != null">
        FINCOME,
      </if>
      <if test="decisionflag != null">
        DECISIONFLAG,
      </if>
      <if test="interests != null">
        INTERESTS,
      </if>
      <if test="familycondition != null">
        FAMILYCONDITION,
      </if>
      <if test="contacttime != null">
        CONTACTTIME,
      </if>
      <if test="sendinfoflag != null">
        SENDINFOFLAG,
      </if>
      <if test="recvtelflag != null">
        RECVTELFLAG,
      </if>
      <if test="recvemailflag != null">
        RECVEMAILFLAG,
      </if>
      <if test="recvmsgflag != null">
        RECVMSGFLAG,
      </if>
      <if test="company != null">
        COMPANY,
      </if>
      <if test="risklevel != null">
        RISKLEVEL,
      </if>
      <if test="selfrisklevel != null">
        SELFRISKLEVEL,
      </if>
      <if test="addrTm != null">
        ADDR_TM,
      </if>
      <if test="postcode != null">
        POSTCODE,
      </if>
      <if test="mobileTm != null">
        MOBILE_TM,
      </if>
      <if test="telnoTm != null">
        TELNO_TM,
      </if>
      <if test="fax != null">
        FAX,
      </if>
      <if test="emailTm != null">
        EMAIL_TM,
      </if>
      <if test="officetelno != null">
        OFFICETELNO,
      </if>
      <if test="source != null">
        "SOURCE",
      </if>
      <if test="knowchan != null">
        KNOWCHAN,
      </if>
      <if test="otherchan != null">
        OTHERCHAN,
      </if>
      <if test="otherinvest != null">
        OTHERINVEST,
      </if>
      <if test="salon != null">
        SALON,
      </if>
      <if test="beforeinvest != null">
        BEFOREINVEST,
      </if>
      <if test="selfdefflag != null">
        SELFDEFFLAG,
      </if>
      <if test="visitfqcy != null">
        VISITFQCY,
      </if>
      <if test="saledirection != null">
        SALEDIRECTION,
      </if>
      <if test="devdirection != null">
        DEVDIRECTION,
      </if>
      <if test="subsource != null">
        SUBSOURCE,
      </if>
      <if test="subsourcetype != null">
        SUBSOURCETYPE,
      </if>
      <if test="remark != null">
        REMARK,
      </if>
      <if test="addr2Tm != null">
        ADDR2_TM,
      </if>
      <if test="postcode2 != null">
        POSTCODE2,
      </if>
      <if test="mobile2Tm != null">
        MOBILE2_TM,
      </if>
      <if test="email2Tm != null">
        EMAIL2_TM,
      </if>
      <if test="knowhowbuy != null">
        KNOWHOWBUY,
      </if>
      <if test="subknow != null">
        SUBKNOW,
      </if>
      <if test="subknowtype != null">
        SUBKNOWTYPE,
      </if>
      <if test="buyingprod != null">
        BUYINGPROD,
      </if>
      <if test="buyedprod != null">
        BUYEDPROD,
      </if>
      <if test="freeprod != null">
        FREEPROD,
      </if>
      <if test="specialflag != null">
        SPECIALFLAG,
      </if>
      <if test="dlvymode != null">
        DLVYMODE,
      </if>
      <if test="specialreason != null">
        SPECIALREASON,
      </if>
      <if test="creator != null">
        CREATOR,
      </if>
      <if test="checker != null">
        CHECKER,
      </if>
      <if test="stimestamp != null">
        STIMESTAMP,
      </if>
      <if test="pririsklevel != null">
        PRIRISKLEVEL,
      </if>
      <if test="linkman != null">
        LINKMAN,
      </if>
      <if test="linktelTm != null">
        LINKTEL_TM,
      </if>
      <if test="linkmobileTm != null">
        LINKMOBILE_TM,
      </if>
      <if test="linkemailTm != null">
        LINKEMAIL_TM,
      </if>
      <if test="linkpostcode != null">
        LINKPOSTCODE,
      </if>
      <if test="linkaddrTm != null">
        LINKADDR_TM,
      </if>
      <if test="capacity != null">
        CAPACITY,
      </if>
      <if test="gpsinvestlevel != null">
        GPSINVESTLEVEL,
      </if>
      <if test="gpsrisklevel != null">
        GPSRISKLEVEL,
      </if>
      <if test="isboss != null">
        ISBOSS,
      </if>
      <if test="financeneed != null">
        FINANCENEED,
      </if>
      <if test="isjoinclub != null">
        ISJOINCLUB,
      </if>
      <if test="explanation != null">
        EXPLANATION,
      </if>
      <if test="isrisktip != null">
        ISRISKTIP,
      </if>
      <if test="custsourceremark != null">
        CUSTSOURCEREMARK,
      </if>
      <if test="pmarketamt != null">
        PMARKETAMT,
      </if>
      <if test="iswritebook != null">
        ISWRITEBOOK,
      </if>
      <if test="invsttype != null">
        INVSTTYPE,
      </if>
      <if test="source2 != null">
        SOURCE2,
      </if>
      <if test="subsource2 != null">
        SUBSOURCE2,
      </if>
      <if test="subsourcetype2 != null">
        SUBSOURCETYPE2,
      </if>
      <if test="vipusername != null">
        VIPUSERNAME,
      </if>
      <if test="wechatcode != null">
        WECHATCODE,
      </if>
      <if test="newsourceno != null">
        NEWSOURCENO,
      </if>
      <if test="hboneNo != null">
        HBONE_NO,
      </if>
      <if test="newsourceno2 != null">
        NEWSOURCENO2,
      </if>
      <if test="hopetradetype != null">
        HOPETRADETYPE,
      </if>
      <if test="validity != null">
        VALIDITY,
      </if>
      <if test="validitydt != null">
        VALIDITYDT,
      </if>
      <if test="nature != null">
        NATURE,
      </if>
      <if test="aptitude != null">
        APTITUDE,
      </if>
      <if test="scopebusiness != null">
        SCOPEBUSINESS,
      </if>
      <if test="restype != null">
        RESTYPE,
      </if>
      <if test="provcodeMobile != null">
        PROVCODE_MOBILE,
      </if>
      <if test="citycodeMobile != null">
        CITYCODE_MOBILE,
      </if>
      <if test="orgtype != null">
        ORGTYPE,
      </if>
      <if test="pinyin != null">
        PINYIN,
      </if>
      <if test="idnoDigest != null">
        IDNO_DIGEST,
      </if>
      <if test="idnoMask != null">
        IDNO_MASK,
      </if>
      <if test="custnameDigest != null">
        CUSTNAME_DIGEST,
      </if>
      <if test="custnameMask != null">
        CUSTNAME_MASK,
      </if>
      <if test="addrDigest != null">
        ADDR_DIGEST,
      </if>
      <if test="addrMask != null">
        ADDR_MASK,
      </if>
      <if test="mobileDigest != null">
        MOBILE_DIGEST,
      </if>
      <if test="mobileMask != null">
        MOBILE_MASK,
      </if>
      <if test="telnoDigest != null">
        TELNO_DIGEST,
      </if>
      <if test="telnoMask != null">
        TELNO_MASK,
      </if>
      <if test="emailDigest != null">
        EMAIL_DIGEST,
      </if>
      <if test="emailMask != null">
        EMAIL_MASK,
      </if>
      <if test="addr2Digest != null">
        ADDR2_DIGEST,
      </if>
      <if test="addr2Mask != null">
        ADDR2_MASK,
      </if>
      <if test="mobile2Digest != null">
        MOBILE2_DIGEST,
      </if>
      <if test="mobile2Mask != null">
        MOBILE2_MASK,
      </if>
      <if test="email2Digest != null">
        EMAIL2_DIGEST,
      </if>
      <if test="email2Mask != null">
        EMAIL2_MASK,
      </if>
      <if test="linkmanDigest != null">
        LINKMAN_DIGEST,
      </if>
      <if test="linkmanMask != null">
        LINKMAN_MASK,
      </if>
      <if test="linktelDigest != null">
        LINKTEL_DIGEST,
      </if>
      <if test="linktelMask != null">
        LINKTEL_MASK,
      </if>
      <if test="linkmobileDigest != null">
        LINKMOBILE_DIGEST,
      </if>
      <if test="linkmobileMask != null">
        LINKMOBILE_MASK,
      </if>
      <if test="linkemailDigest != null">
        LINKEMAIL_DIGEST,
      </if>
      <if test="linkemailMask != null">
        LINKEMAIL_MASK,
      </if>
      <if test="linkaddrDigest != null">
        LINKADDR_DIGEST,
      </if>
      <if test="linkaddrMask != null">
        LINKADDR_MASK,
      </if>
      <if test="isvirtualsharer != null">
        ISVIRTUALSHARER,
      </if>
      <if test="mobileAreaCode != null">
        MOBILE_AREA_CODE,
      </if>
      <if test="mobile2AreaCode != null">
        MOBILE2_AREA_CODE,
      </if>
      <if test="linkmobileAreaCode != null">
        LINKMOBILE_AREA_CODE,
      </if>
      <if test="idSignAreaCode != null">
        ID_SIGN_AREA_CODE,
      </if>
      <if test="nationCode != null">
        NATION_CODE,
      </if>
      <if test="hboneTimestamp != null">
        HBONE_TIMESTAMP,
      </if>
      <if test="countyCode != null">
        COUNTY_CODE,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="appserialno != null">
        #{appserialno,jdbcType=VARCHAR},
      </if>
      <if test="conscustno != null">
        #{conscustno,jdbcType=VARCHAR},
      </if>
      <if test="conscustlvl != null">
        #{conscustlvl,jdbcType=VARCHAR},
      </if>
      <if test="conscustgrade != null">
        #{conscustgrade,jdbcType=DECIMAL},
      </if>
      <if test="conscuststatus != null">
        #{conscuststatus,jdbcType=VARCHAR},
      </if>
      <if test="idtype != null">
        #{idtype,jdbcType=VARCHAR},
      </if>
      <if test="idnoTm != null">
        #{idnoTm,jdbcType=VARCHAR},
      </if>
      <if test="custname != null">
        #{custname,jdbcType=VARCHAR},
      </if>
      <if test="provcode != null">
        #{provcode,jdbcType=VARCHAR},
      </if>
      <if test="citycode != null">
        #{citycode,jdbcType=VARCHAR},
      </if>
      <if test="edulevel != null">
        #{edulevel,jdbcType=VARCHAR},
      </if>
      <if test="vocation != null">
        #{vocation,jdbcType=VARCHAR},
      </if>
      <if test="inclevel != null">
        #{inclevel,jdbcType=VARCHAR},
      </if>
      <if test="birthday != null">
        #{birthday,jdbcType=VARCHAR},
      </if>
      <if test="gender != null">
        #{gender,jdbcType=VARCHAR},
      </if>
      <if test="married != null">
        #{married,jdbcType=VARCHAR},
      </if>
      <if test="pincome != null">
        #{pincome,jdbcType=VARCHAR},
      </if>
      <if test="fincome != null">
        #{fincome,jdbcType=VARCHAR},
      </if>
      <if test="decisionflag != null">
        #{decisionflag,jdbcType=VARCHAR},
      </if>
      <if test="interests != null">
        #{interests,jdbcType=VARCHAR},
      </if>
      <if test="familycondition != null">
        #{familycondition,jdbcType=VARCHAR},
      </if>
      <if test="contacttime != null">
        #{contacttime,jdbcType=VARCHAR},
      </if>
      <if test="sendinfoflag != null">
        #{sendinfoflag,jdbcType=VARCHAR},
      </if>
      <if test="recvtelflag != null">
        #{recvtelflag,jdbcType=VARCHAR},
      </if>
      <if test="recvemailflag != null">
        #{recvemailflag,jdbcType=VARCHAR},
      </if>
      <if test="recvmsgflag != null">
        #{recvmsgflag,jdbcType=VARCHAR},
      </if>
      <if test="company != null">
        #{company,jdbcType=VARCHAR},
      </if>
      <if test="risklevel != null">
        #{risklevel,jdbcType=VARCHAR},
      </if>
      <if test="selfrisklevel != null">
        #{selfrisklevel,jdbcType=VARCHAR},
      </if>
      <if test="addrTm != null">
        #{addrTm,jdbcType=VARCHAR},
      </if>
      <if test="postcode != null">
        #{postcode,jdbcType=VARCHAR},
      </if>
      <if test="mobileTm != null">
        #{mobileTm,jdbcType=VARCHAR},
      </if>
      <if test="telnoTm != null">
        #{telnoTm,jdbcType=VARCHAR},
      </if>
      <if test="fax != null">
        #{fax,jdbcType=VARCHAR},
      </if>
      <if test="emailTm != null">
        #{emailTm,jdbcType=VARCHAR},
      </if>
      <if test="officetelno != null">
        #{officetelno,jdbcType=VARCHAR},
      </if>
      <if test="source != null">
        #{source,jdbcType=VARCHAR},
      </if>
      <if test="knowchan != null">
        #{knowchan,jdbcType=VARCHAR},
      </if>
      <if test="otherchan != null">
        #{otherchan,jdbcType=VARCHAR},
      </if>
      <if test="otherinvest != null">
        #{otherinvest,jdbcType=VARCHAR},
      </if>
      <if test="salon != null">
        #{salon,jdbcType=VARCHAR},
      </if>
      <if test="beforeinvest != null">
        #{beforeinvest,jdbcType=VARCHAR},
      </if>
      <if test="selfdefflag != null">
        #{selfdefflag,jdbcType=VARCHAR},
      </if>
      <if test="visitfqcy != null">
        #{visitfqcy,jdbcType=VARCHAR},
      </if>
      <if test="saledirection != null">
        #{saledirection,jdbcType=VARCHAR},
      </if>
      <if test="devdirection != null">
        #{devdirection,jdbcType=VARCHAR},
      </if>
      <if test="subsource != null">
        #{subsource,jdbcType=VARCHAR},
      </if>
      <if test="subsourcetype != null">
        #{subsourcetype,jdbcType=VARCHAR},
      </if>
      <if test="remark != null">
        #{remark,jdbcType=VARCHAR},
      </if>
      <if test="addr2Tm != null">
        #{addr2Tm,jdbcType=VARCHAR},
      </if>
      <if test="postcode2 != null">
        #{postcode2,jdbcType=VARCHAR},
      </if>
      <if test="mobile2Tm != null">
        #{mobile2Tm,jdbcType=VARCHAR},
      </if>
      <if test="email2Tm != null">
        #{email2Tm,jdbcType=VARCHAR},
      </if>
      <if test="knowhowbuy != null">
        #{knowhowbuy,jdbcType=VARCHAR},
      </if>
      <if test="subknow != null">
        #{subknow,jdbcType=VARCHAR},
      </if>
      <if test="subknowtype != null">
        #{subknowtype,jdbcType=VARCHAR},
      </if>
      <if test="buyingprod != null">
        #{buyingprod,jdbcType=VARCHAR},
      </if>
      <if test="buyedprod != null">
        #{buyedprod,jdbcType=VARCHAR},
      </if>
      <if test="freeprod != null">
        #{freeprod,jdbcType=VARCHAR},
      </if>
      <if test="specialflag != null">
        #{specialflag,jdbcType=VARCHAR},
      </if>
      <if test="dlvymode != null">
        #{dlvymode,jdbcType=VARCHAR},
      </if>
      <if test="specialreason != null">
        #{specialreason,jdbcType=VARCHAR},
      </if>
      <if test="creator != null">
        #{creator,jdbcType=VARCHAR},
      </if>
      <if test="checker != null">
        #{checker,jdbcType=VARCHAR},
      </if>
      <if test="stimestamp != null">
        #{stimestamp,jdbcType=TIMESTAMP},
      </if>
      <if test="pririsklevel != null">
        #{pririsklevel,jdbcType=VARCHAR},
      </if>
      <if test="linkman != null">
        #{linkman,jdbcType=VARCHAR},
      </if>
      <if test="linktelTm != null">
        #{linktelTm,jdbcType=VARCHAR},
      </if>
      <if test="linkmobileTm != null">
        #{linkmobileTm,jdbcType=VARCHAR},
      </if>
      <if test="linkemailTm != null">
        #{linkemailTm,jdbcType=VARCHAR},
      </if>
      <if test="linkpostcode != null">
        #{linkpostcode,jdbcType=VARCHAR},
      </if>
      <if test="linkaddrTm != null">
        #{linkaddrTm,jdbcType=VARCHAR},
      </if>
      <if test="capacity != null">
        #{capacity,jdbcType=VARCHAR},
      </if>
      <if test="gpsinvestlevel != null">
        #{gpsinvestlevel,jdbcType=VARCHAR},
      </if>
      <if test="gpsrisklevel != null">
        #{gpsrisklevel,jdbcType=VARCHAR},
      </if>
      <if test="isboss != null">
        #{isboss,jdbcType=VARCHAR},
      </if>
      <if test="financeneed != null">
        #{financeneed,jdbcType=VARCHAR},
      </if>
      <if test="isjoinclub != null">
        #{isjoinclub,jdbcType=VARCHAR},
      </if>
      <if test="explanation != null">
        #{explanation,jdbcType=VARCHAR},
      </if>
      <if test="isrisktip != null">
        #{isrisktip,jdbcType=VARCHAR},
      </if>
      <if test="custsourceremark != null">
        #{custsourceremark,jdbcType=VARCHAR},
      </if>
      <if test="pmarketamt != null">
        #{pmarketamt,jdbcType=DECIMAL},
      </if>
      <if test="iswritebook != null">
        #{iswritebook,jdbcType=VARCHAR},
      </if>
      <if test="invsttype != null">
        #{invsttype,jdbcType=VARCHAR},
      </if>
      <if test="source2 != null">
        #{source2,jdbcType=VARCHAR},
      </if>
      <if test="subsource2 != null">
        #{subsource2,jdbcType=VARCHAR},
      </if>
      <if test="subsourcetype2 != null">
        #{subsourcetype2,jdbcType=VARCHAR},
      </if>
      <if test="vipusername != null">
        #{vipusername,jdbcType=VARCHAR},
      </if>
      <if test="wechatcode != null">
        #{wechatcode,jdbcType=VARCHAR},
      </if>
      <if test="newsourceno != null">
        #{newsourceno,jdbcType=VARCHAR},
      </if>
      <if test="hboneNo != null">
        #{hboneNo,jdbcType=VARCHAR},
      </if>
      <if test="newsourceno2 != null">
        #{newsourceno2,jdbcType=VARCHAR},
      </if>
      <if test="hopetradetype != null">
        #{hopetradetype,jdbcType=VARCHAR},
      </if>
      <if test="validity != null">
        #{validity,jdbcType=VARCHAR},
      </if>
      <if test="validitydt != null">
        #{validitydt,jdbcType=VARCHAR},
      </if>
      <if test="nature != null">
        #{nature,jdbcType=VARCHAR},
      </if>
      <if test="aptitude != null">
        #{aptitude,jdbcType=VARCHAR},
      </if>
      <if test="scopebusiness != null">
        #{scopebusiness,jdbcType=VARCHAR},
      </if>
      <if test="restype != null">
        #{restype,jdbcType=VARCHAR},
      </if>
      <if test="provcodeMobile != null">
        #{provcodeMobile,jdbcType=VARCHAR},
      </if>
      <if test="citycodeMobile != null">
        #{citycodeMobile,jdbcType=VARCHAR},
      </if>
      <if test="orgtype != null">
        #{orgtype,jdbcType=VARCHAR},
      </if>
      <if test="pinyin != null">
        #{pinyin,jdbcType=VARCHAR},
      </if>
      <if test="idnoDigest != null">
        #{idnoDigest,jdbcType=VARCHAR},
      </if>
      <if test="idnoMask != null">
        #{idnoMask,jdbcType=VARCHAR},
      </if>
      <if test="custnameDigest != null">
        #{custnameDigest,jdbcType=VARCHAR},
      </if>
      <if test="custnameMask != null">
        #{custnameMask,jdbcType=VARCHAR},
      </if>
      <if test="addrDigest != null">
        #{addrDigest,jdbcType=VARCHAR},
      </if>
      <if test="addrMask != null">
        #{addrMask,jdbcType=VARCHAR},
      </if>
      <if test="mobileDigest != null">
        #{mobileDigest,jdbcType=VARCHAR},
      </if>
      <if test="mobileMask != null">
        #{mobileMask,jdbcType=VARCHAR},
      </if>
      <if test="telnoDigest != null">
        #{telnoDigest,jdbcType=VARCHAR},
      </if>
      <if test="telnoMask != null">
        #{telnoMask,jdbcType=VARCHAR},
      </if>
      <if test="emailDigest != null">
        #{emailDigest,jdbcType=VARCHAR},
      </if>
      <if test="emailMask != null">
        #{emailMask,jdbcType=VARCHAR},
      </if>
      <if test="addr2Digest != null">
        #{addr2Digest,jdbcType=VARCHAR},
      </if>
      <if test="addr2Mask != null">
        #{addr2Mask,jdbcType=VARCHAR},
      </if>
      <if test="mobile2Digest != null">
        #{mobile2Digest,jdbcType=VARCHAR},
      </if>
      <if test="mobile2Mask != null">
        #{mobile2Mask,jdbcType=VARCHAR},
      </if>
      <if test="email2Digest != null">
        #{email2Digest,jdbcType=VARCHAR},
      </if>
      <if test="email2Mask != null">
        #{email2Mask,jdbcType=VARCHAR},
      </if>
      <if test="linkmanDigest != null">
        #{linkmanDigest,jdbcType=VARCHAR},
      </if>
      <if test="linkmanMask != null">
        #{linkmanMask,jdbcType=VARCHAR},
      </if>
      <if test="linktelDigest != null">
        #{linktelDigest,jdbcType=VARCHAR},
      </if>
      <if test="linktelMask != null">
        #{linktelMask,jdbcType=VARCHAR},
      </if>
      <if test="linkmobileDigest != null">
        #{linkmobileDigest,jdbcType=VARCHAR},
      </if>
      <if test="linkmobileMask != null">
        #{linkmobileMask,jdbcType=VARCHAR},
      </if>
      <if test="linkemailDigest != null">
        #{linkemailDigest,jdbcType=VARCHAR},
      </if>
      <if test="linkemailMask != null">
        #{linkemailMask,jdbcType=VARCHAR},
      </if>
      <if test="linkaddrDigest != null">
        #{linkaddrDigest,jdbcType=VARCHAR},
      </if>
      <if test="linkaddrMask != null">
        #{linkaddrMask,jdbcType=VARCHAR},
      </if>
      <if test="isvirtualsharer != null">
        #{isvirtualsharer,jdbcType=VARCHAR},
      </if>
      <if test="mobileAreaCode != null">
        #{mobileAreaCode,jdbcType=VARCHAR},
      </if>
      <if test="mobile2AreaCode != null">
        #{mobile2AreaCode,jdbcType=VARCHAR},
      </if>
      <if test="linkmobileAreaCode != null">
        #{linkmobileAreaCode,jdbcType=VARCHAR},
      </if>
      <if test="idSignAreaCode != null">
        #{idSignAreaCode,jdbcType=VARCHAR},
      </if>
      <if test="nationCode != null">
        #{nationCode,jdbcType=VARCHAR},
      </if>
      <if test="hboneTimestamp != null">
        #{hboneTimestamp,jdbcType=TIMESTAMP},
      </if>
      <if test="countyCode != null">
        #{countyCode,jdbcType=VARCHAR},
      </if>
    </trim>
  </insert>
</mapper>