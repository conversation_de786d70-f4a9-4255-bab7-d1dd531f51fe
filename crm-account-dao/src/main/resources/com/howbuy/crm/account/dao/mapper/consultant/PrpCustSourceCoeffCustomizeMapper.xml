<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.howbuy.crm.account.dao.mapper.customize.consultant.PrpCustSourceCoeffCustomizeMapper">

    <!-- 客户来源系数配置结果映射 -->
    <resultMap id="CmCustSourceCoeffResultMap" type="com.howbuy.crm.account.dao.bo.consultant.CmCustSourceCoeffBO">
        <result column="SOURCE_TYPE" jdbcType="VARCHAR" property="sourceType"/>
        <result column="ZB_COEFF" jdbcType="DECIMAL" property="zbCoeff"/>
        <result column="START_POINT" jdbcType="VARCHAR" property="startPoint"/>
        <result column="MANAGE_COEFF" jdbcType="DECIMAL" property="manageCoeff"/>
        <result column="MANAGE_COEFF_REGIONALSUBTOTAL" jdbcType="DECIMAL" property="manageCoeffRegionalsubtotal"/>
        <result column="MANAGE_COEFF_REGIONALTOTAL" jdbcType="DECIMAL" property="manageCoeffRegionaltotal"/>
        <result column="CXB" jdbcType="VARCHAR" property="cxb"/>
    </resultMap>

    <!-- 根据来源类型查询客户来源系数配置 -->
    <select id="selectCustSourceCoeffBySourceType" resultType="com.howbuy.crm.account.dao.bo.consultant.CmCustSourceCoeffBO">
        SELECT source_type as sourceType,
            manage_coeff as manageCoeff,
            manage_coeff_regionalsubtotal as manageCoeffRegionalsubtotal,
            manage_coeff_regionaltotal as manageCoeffRegionaltotal,
            start_point as startPoint,
            zb_coeff as zbCoeff,
            cxb
        FROM cm_prp_cust_source_coeff
        WHERE source_type = #{sourceType}
        AND TO_CHAR(SYSDATE, 'YYYYMMDD') BETWEEN start_dt AND NVL(end_dt, '********')
    </select>

</mapper> 