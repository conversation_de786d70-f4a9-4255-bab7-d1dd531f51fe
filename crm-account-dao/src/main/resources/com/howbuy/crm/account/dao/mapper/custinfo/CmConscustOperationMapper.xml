<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.howbuy.crm.account.dao.mapper.custinfo.CmConscustOperationMapper">
  <resultMap id="BaseResultMap" type="com.howbuy.crm.account.dao.po.custinfo.CmConscustOperationPO">
    <!--@mbg.generated-->
    <!--@Table CM_CONSCUST_OPERATION-->
    <id column="OPERATIONID" jdbcType="VARCHAR" property="operationid" />
    <result column="CONSCUSTNO" jdbcType="VARCHAR" property="conscustno" />
    <result column="CITYCODE" jdbcType="VARCHAR" property="citycode" />
    <result column="CUSTSOURCEREMARK" jdbcType="VARCHAR" property="custsourceremark" />
    <result column="NEWSOURCENO" jdbcType="VARCHAR" property="newsourceno" />
    <result column="CONSCUSTLVL" jdbcType="VARCHAR" property="conscustlvl" />
    <result column="CONSCUSTGRADE" jdbcType="DECIMAL" property="conscustgrade" />
    <result column="REGDT" jdbcType="VARCHAR" property="regdt" />
    <result column="TRANSFERAPPLY" jdbcType="VARCHAR" property="transferapply" />
    <result column="OPSTATUS" jdbcType="VARCHAR" property="opstatus" />
    <result column="STATUS" jdbcType="VARCHAR" property="status" />
    <result column="CUSTNAME" jdbcType="VARCHAR" property="custname" />
    <result column="CREATOR" jdbcType="VARCHAR" property="creator" />
    <result column="CREATDT" jdbcType="TIMESTAMP" property="creatdt" />
    <result column="PROVCODE" jdbcType="VARCHAR" property="provcode" />
    <result column="OPENSOURCE" jdbcType="VARCHAR" property="opensource" />
    <result column="MOBILE_DIGEST" jdbcType="VARCHAR" property="mobileDigest" />
    <result column="MOBILE_MASK" jdbcType="VARCHAR" property="mobileMask" />
    <result column="MOBILE_CIPHER" jdbcType="VARCHAR" property="mobileCipher" />
    <result column="SOURCECHANNEL" jdbcType="VARCHAR" property="sourcechannel" />
    <result column="MOBILE_AREA_CODE" jdbcType="VARCHAR" property="mobileAreaCode" />
    <result column="COUNTY_CODE" jdbcType="VARCHAR" property="countyCode" />
  </resultMap>
  <sql id="Base_Column_List">
    <!--@mbg.generated-->
    OPERATIONID, CONSCUSTNO, CITYCODE, CUSTSOURCEREMARK, NEWSOURCENO, CONSCUSTLVL, CONSCUSTGRADE,
    REGDT, TRANSFERAPPLY, OPSTATUS, "STATUS", CUSTNAME, CREATOR, CREATDT, PROVCODE, OPENSOURCE,
    MOBILE_DIGEST, MOBILE_MASK, MOBILE_CIPHER, SOURCECHANNEL, MOBILE_AREA_CODE, COUNTY_CODE
  </sql>
  <select id="selectByPrimaryKey" parameterType="java.lang.String" resultMap="BaseResultMap">
    <!--@mbg.generated-->
    select 
    <include refid="Base_Column_List" />
    from CM_CONSCUST_OPERATION
    where OPERATIONID = #{operationid,jdbcType=VARCHAR}
  </select>
  <delete id="deleteByPrimaryKey" parameterType="java.lang.String">
    <!--@mbg.generated-->
    delete from CM_CONSCUST_OPERATION
    where OPERATIONID = #{operationid,jdbcType=VARCHAR}
  </delete>
  <insert id="insert" parameterType="com.howbuy.crm.account.dao.po.custinfo.CmConscustOperationPO">
    <!--@mbg.generated-->
    insert into CM_CONSCUST_OPERATION (OPERATIONID, CONSCUSTNO, CITYCODE,
    CUSTSOURCEREMARK, NEWSOURCENO, CONSCUSTLVL,
    CONSCUSTGRADE, REGDT, TRANSFERAPPLY,
    OPSTATUS, "STATUS", CUSTNAME,
    CREATOR, CREATDT, PROVCODE,
    OPENSOURCE, MOBILE_DIGEST, MOBILE_MASK,
    MOBILE_CIPHER, SOURCECHANNEL, MOBILE_AREA_CODE,
    COUNTY_CODE)
    values (#{operationid,jdbcType=VARCHAR}, #{conscustno,jdbcType=VARCHAR}, #{citycode,jdbcType=VARCHAR},
    #{custsourceremark,jdbcType=VARCHAR}, #{newsourceno,jdbcType=VARCHAR}, #{conscustlvl,jdbcType=VARCHAR},
    #{conscustgrade,jdbcType=DECIMAL}, #{regdt,jdbcType=VARCHAR}, #{transferapply,jdbcType=VARCHAR},
    #{opstatus,jdbcType=VARCHAR}, #{status,jdbcType=VARCHAR}, #{custname,jdbcType=VARCHAR},
    #{creator,jdbcType=VARCHAR}, #{creatdt,jdbcType=TIMESTAMP}, #{provcode,jdbcType=VARCHAR},
    #{opensource,jdbcType=VARCHAR}, #{mobileDigest,jdbcType=VARCHAR}, #{mobileMask,jdbcType=VARCHAR},
    #{mobileCipher,jdbcType=VARCHAR}, #{sourcechannel,jdbcType=VARCHAR}, #{mobileAreaCode,jdbcType=VARCHAR},
    #{countyCode,jdbcType=VARCHAR})
  </insert>
  <insert id="insertSelective" parameterType="com.howbuy.crm.account.dao.po.custinfo.CmConscustOperationPO">
    <!--@mbg.generated-->
    insert into CM_CONSCUST_OPERATION
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="operationid != null">
        OPERATIONID,
      </if>
      <if test="conscustno != null">
        CONSCUSTNO,
      </if>
      <if test="citycode != null">
        CITYCODE,
      </if>
      <if test="custsourceremark != null">
        CUSTSOURCEREMARK,
      </if>
      <if test="newsourceno != null">
        NEWSOURCENO,
      </if>
      <if test="conscustlvl != null">
        CONSCUSTLVL,
      </if>
      <if test="conscustgrade != null">
        CONSCUSTGRADE,
      </if>
      <if test="regdt != null">
        REGDT,
      </if>
      <if test="transferapply != null">
        TRANSFERAPPLY,
      </if>
      <if test="opstatus != null">
        OPSTATUS,
      </if>
      <if test="status != null">
        "STATUS",
      </if>
      <if test="custname != null">
        CUSTNAME,
      </if>
      <if test="creator != null">
        CREATOR,
      </if>
      <if test="creatdt != null">
        CREATDT,
      </if>
      <if test="provcode != null">
        PROVCODE,
      </if>
      <if test="opensource != null">
        OPENSOURCE,
      </if>
      <if test="mobileDigest != null">
        MOBILE_DIGEST,
      </if>
      <if test="mobileMask != null">
        MOBILE_MASK,
      </if>
      <if test="mobileCipher != null">
        MOBILE_CIPHER,
      </if>
      <if test="sourcechannel != null">
        SOURCECHANNEL,
      </if>
      <if test="mobileAreaCode != null">
        MOBILE_AREA_CODE,
      </if>
      <if test="countyCode != null">
        COUNTY_CODE,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="operationid != null">
        #{operationid,jdbcType=VARCHAR},
      </if>
      <if test="conscustno != null">
        #{conscustno,jdbcType=VARCHAR},
      </if>
      <if test="citycode != null">
        #{citycode,jdbcType=VARCHAR},
      </if>
      <if test="custsourceremark != null">
        #{custsourceremark,jdbcType=VARCHAR},
      </if>
      <if test="newsourceno != null">
        #{newsourceno,jdbcType=VARCHAR},
      </if>
      <if test="conscustlvl != null">
        #{conscustlvl,jdbcType=VARCHAR},
      </if>
      <if test="conscustgrade != null">
        #{conscustgrade,jdbcType=DECIMAL},
      </if>
      <if test="regdt != null">
        #{regdt,jdbcType=VARCHAR},
      </if>
      <if test="transferapply != null">
        #{transferapply,jdbcType=VARCHAR},
      </if>
      <if test="opstatus != null">
        #{opstatus,jdbcType=VARCHAR},
      </if>
      <if test="status != null">
        #{status,jdbcType=VARCHAR},
      </if>
      <if test="custname != null">
        #{custname,jdbcType=VARCHAR},
      </if>
      <if test="creator != null">
        #{creator,jdbcType=VARCHAR},
      </if>
      <if test="creatdt != null">
        #{creatdt,jdbcType=TIMESTAMP},
      </if>
      <if test="provcode != null">
        #{provcode,jdbcType=VARCHAR},
      </if>
      <if test="opensource != null">
        #{opensource,jdbcType=VARCHAR},
      </if>
      <if test="mobileDigest != null">
        #{mobileDigest,jdbcType=VARCHAR},
      </if>
      <if test="mobileMask != null">
        #{mobileMask,jdbcType=VARCHAR},
      </if>
      <if test="mobileCipher != null">
        #{mobileCipher,jdbcType=VARCHAR},
      </if>
      <if test="sourcechannel != null">
        #{sourcechannel,jdbcType=VARCHAR},
      </if>
      <if test="mobileAreaCode != null">
        #{mobileAreaCode,jdbcType=VARCHAR},
      </if>
      <if test="countyCode != null">
        #{countyCode,jdbcType=VARCHAR},
      </if>
    </trim>
  </insert>
  <update id="updateByPrimaryKeySelective" parameterType="com.howbuy.crm.account.dao.po.custinfo.CmConscustOperationPO">
    <!--@mbg.generated-->
    update CM_CONSCUST_OPERATION
    <set>
      <if test="conscustno != null">
        CONSCUSTNO = #{conscustno,jdbcType=VARCHAR},
      </if>
      <if test="citycode != null">
        CITYCODE = #{citycode,jdbcType=VARCHAR},
      </if>
      <if test="custsourceremark != null">
        CUSTSOURCEREMARK = #{custsourceremark,jdbcType=VARCHAR},
      </if>
      <if test="newsourceno != null">
        NEWSOURCENO = #{newsourceno,jdbcType=VARCHAR},
      </if>
      <if test="conscustlvl != null">
        CONSCUSTLVL = #{conscustlvl,jdbcType=VARCHAR},
      </if>
      <if test="conscustgrade != null">
        CONSCUSTGRADE = #{conscustgrade,jdbcType=DECIMAL},
      </if>
      <if test="regdt != null">
        REGDT = #{regdt,jdbcType=VARCHAR},
      </if>
      <if test="transferapply != null">
        TRANSFERAPPLY = #{transferapply,jdbcType=VARCHAR},
      </if>
      <if test="opstatus != null">
        OPSTATUS = #{opstatus,jdbcType=VARCHAR},
      </if>
      <if test="status != null">
        "STATUS" = #{status,jdbcType=VARCHAR},
      </if>
      <if test="custname != null">
        CUSTNAME = #{custname,jdbcType=VARCHAR},
      </if>
      <if test="creator != null">
        CREATOR = #{creator,jdbcType=VARCHAR},
      </if>
      <if test="creatdt != null">
        CREATDT = #{creatdt,jdbcType=TIMESTAMP},
      </if>
      <if test="provcode != null">
        PROVCODE = #{provcode,jdbcType=VARCHAR},
      </if>
      <if test="opensource != null">
        OPENSOURCE = #{opensource,jdbcType=VARCHAR},
      </if>
      <if test="mobileDigest != null">
        MOBILE_DIGEST = #{mobileDigest,jdbcType=VARCHAR},
      </if>
      <if test="mobileMask != null">
        MOBILE_MASK = #{mobileMask,jdbcType=VARCHAR},
      </if>
      <if test="mobileCipher != null">
        MOBILE_CIPHER = #{mobileCipher,jdbcType=VARCHAR},
      </if>
      <if test="sourcechannel != null">
        SOURCECHANNEL = #{sourcechannel,jdbcType=VARCHAR},
      </if>
      <if test="mobileAreaCode != null">
        MOBILE_AREA_CODE = #{mobileAreaCode,jdbcType=VARCHAR},
      </if>
      <if test="countyCode != null">
        COUNTY_CODE = #{countyCode,jdbcType=VARCHAR},
      </if>
    </set>
    where OPERATIONID = #{operationid,jdbcType=VARCHAR}
  </update>
  <update id="updateByPrimaryKey" parameterType="com.howbuy.crm.account.dao.po.custinfo.CmConscustOperationPO">
    <!--@mbg.generated-->
    update CM_CONSCUST_OPERATION
    set CONSCUSTNO = #{conscustno,jdbcType=VARCHAR},
    CITYCODE = #{citycode,jdbcType=VARCHAR},
    CUSTSOURCEREMARK = #{custsourceremark,jdbcType=VARCHAR},
    NEWSOURCENO = #{newsourceno,jdbcType=VARCHAR},
    CONSCUSTLVL = #{conscustlvl,jdbcType=VARCHAR},
    CONSCUSTGRADE = #{conscustgrade,jdbcType=DECIMAL},
    REGDT = #{regdt,jdbcType=VARCHAR},
    TRANSFERAPPLY = #{transferapply,jdbcType=VARCHAR},
    OPSTATUS = #{opstatus,jdbcType=VARCHAR},
    "STATUS" = #{status,jdbcType=VARCHAR},
    CUSTNAME = #{custname,jdbcType=VARCHAR},
    CREATOR = #{creator,jdbcType=VARCHAR},
    CREATDT = #{creatdt,jdbcType=TIMESTAMP},
    PROVCODE = #{provcode,jdbcType=VARCHAR},
    OPENSOURCE = #{opensource,jdbcType=VARCHAR},
    MOBILE_DIGEST = #{mobileDigest,jdbcType=VARCHAR},
    MOBILE_MASK = #{mobileMask,jdbcType=VARCHAR},
    MOBILE_CIPHER = #{mobileCipher,jdbcType=VARCHAR},
    SOURCECHANNEL = #{sourcechannel,jdbcType=VARCHAR},
    MOBILE_AREA_CODE = #{mobileAreaCode,jdbcType=VARCHAR},
    COUNTY_CODE = #{countyCode,jdbcType=VARCHAR}
    where OPERATIONID = #{operationid,jdbcType=VARCHAR}
  </update>
</mapper>