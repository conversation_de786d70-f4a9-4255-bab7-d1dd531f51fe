<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.howbuy.crm.account.dao.mapper.beisenorg.CmBeisenOrgConfigMapper">
  <resultMap id="BaseResultMap" type="com.howbuy.crm.account.dao.po.beisenorg.CmBeisenOrgConfigPO">
    <!--@mbg.generated-->
    <!--@Table CM_BEISEN_ORG_CONFIG-->
    <id column="ID" jdbcType="DECIMAL" property="id" />
    <result column="ORG_ID_BEISEN" jdbcType="VARCHAR" property="orgIdBeisen" />
    <result column="ORG_NAME_BEISEN" jdbcType="VARCHAR" property="orgNameBeisen" />
    <result column="ORG_CODE" jdbcType="VARCHAR" property="orgCode" />
    <result column="CENTER_ORG" jdbcType="VARCHAR" property="centerOrg" />
    <result column="START_DATE" jdbcType="VARCHAR" property="startDate" />
    <result column="END_DATE" jdbcType="VARCHAR" property="endDate" />
    <result column="CREATOR" jdbcType="VARCHAR" property="creator" />
    <result column="MODIFIER" jdbcType="VARCHAR" property="modifier" />
    <result column="CREDT" jdbcType="TIMESTAMP" property="credt" />
    <result column="MODDT" jdbcType="TIMESTAMP" property="moddt" />
  </resultMap>
  <sql id="Base_Column_List">
    <!--@mbg.generated-->
    ID, ORG_ID_BEISEN, ORG_NAME_BEISEN, ORG_CODE, CENTER_ORG,
    START_DATE, END_DATE, CREATOR, MODIFIER, CREDT, MODDT
  </sql>
  <select id="selectByPrimaryKey" parameterType="long" resultMap="BaseResultMap">
    <!--@mbg.generated-->
    select 
    <include refid="Base_Column_List" />
    from CM_BEISEN_ORG_CONFIG
    where ID = #{id,jdbcType=BIGINT}
  </select>
  <delete id="deleteByPrimaryKey" parameterType="long">
    <!--@mbg.generated-->
    delete from CM_BEISEN_ORG_CONFIG
    where ID = #{id,jdbcType=BIGINT}
  </delete>
  <insert id="insert" parameterType="com.howbuy.crm.account.dao.po.beisenorg.CmBeisenOrgConfigPO">
    <!--@mbg.generated-->
    insert into CM_BEISEN_ORG_CONFIG (ID, ORG_ID_BEISEN, ORG_NAME_BEISEN, 
      ORG_CODE,CENTER_ORG, START_DATE, END_DATE,
      CREATOR, MODIFIER, CREDT, MODDT)
    values (#{id,jdbcType=DECIMAL}, #{orgIdBeisen,jdbcType=VARCHAR}, #{orgNameBeisen,jdbcType=VARCHAR}, 
      #{orgCode,jdbcType=VARCHAR}, #{centerOrg,jdbcType=VARCHAR}, #{startDate,jdbcType=VARCHAR},
      #{endDate,jdbcType=VARCHAR}, #{creator,jdbcType=VARCHAR}, #{modifier,jdbcType=VARCHAR},
      #{credt,jdbcType=TIMESTAMP}, #{moddt,jdbcType=TIMESTAMP})
  </insert>
  <insert id="insertSelective" parameterType="com.howbuy.crm.account.dao.po.beisenorg.CmBeisenOrgConfigPO">
    <!--@mbg.generated-->
    insert into CM_BEISEN_ORG_CONFIG
    <trim prefix="(" suffix=")" suffixOverrides=",">
        ID,
      <if test="orgIdBeisen != null">
        ORG_ID_BEISEN,
      </if>
      <if test="orgNameBeisen != null">
        ORG_NAME_BEISEN,
      </if>
      <if test="orgCode != null">
        ORG_CODE,
      </if>
      <if test="centerOrg != null">
        CENTER_ORG,
      </if>
      <if test="startDate != null">
        START_DATE,
      </if>
      <if test="endDate != null">
        END_DATE,
      </if>
      <if test="creator != null">
        CREATOR,
      </if>
      <if test="modifier != null">
        MODIFIER,
      </if>
      <if test="credt != null">
        CREDT,
      </if>
      <if test="moddt != null">
        MODDT,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
        CM_BEISION_POS_LEVEL_CONF_SEQ.nextval,
      <if test="orgIdBeisen != null">
        #{orgIdBeisen,jdbcType=VARCHAR},
      </if>
      <if test="orgNameBeisen != null">
        #{orgNameBeisen,jdbcType=VARCHAR},
      </if>
      <if test="orgCode != null">
        #{orgCode,jdbcType=VARCHAR},
      </if>
      <if test="centerOrg != null">
        #{centerOrg,jdbcType=VARCHAR},
      </if>
      <if test="startDate != null">
        #{startDate,jdbcType=VARCHAR},
      </if>
      <if test="endDate != null">
        #{endDate,jdbcType=VARCHAR},
      </if>
      <if test="creator != null">
        #{creator,jdbcType=VARCHAR},
      </if>
      <if test="modifier != null">
        #{modifier,jdbcType=VARCHAR},
      </if>
      <if test="credt != null">
        #{credt,jdbcType=TIMESTAMP},
      </if>
      <if test="moddt != null">
        #{moddt,jdbcType=TIMESTAMP},
      </if>
    </trim>
  </insert>
  <update id="updateByPrimaryKeySelective" parameterType="com.howbuy.crm.account.dao.po.beisenorg.CmBeisenOrgConfigPO">
    <!--@mbg.generated-->
    update CM_BEISEN_ORG_CONFIG
    <set>
      <if test="orgIdBeisen != null">
        ORG_ID_BEISEN = #{orgIdBeisen,jdbcType=VARCHAR},
      </if>
      <if test="orgNameBeisen != null">
        ORG_NAME_BEISEN = #{orgNameBeisen,jdbcType=VARCHAR},
      </if>
      <if test="orgCode != null">
        ORG_CODE = #{orgCode,jdbcType=VARCHAR},
      </if>
      <if test="centerOrg != null">
        CENTER_ORG = #{centerOrg,jdbcType=VARCHAR},
      </if>
      <if test="startDate != null">
        START_DATE = #{startDate,jdbcType=VARCHAR},
      </if>
      <if test="endDate != null">
        END_DATE = #{endDate,jdbcType=VARCHAR},
      </if>
      <if test="creator != null">
        CREATOR = #{creator,jdbcType=VARCHAR},
      </if>
      <if test="modifier != null">
        MODIFIER = #{modifier,jdbcType=VARCHAR},
      </if>
      <if test="credt != null">
        CREDT = #{credt,jdbcType=TIMESTAMP},
      </if>
      <if test="moddt != null">
        MODDT = #{moddt,jdbcType=TIMESTAMP},
      </if>
    </set>
    where ID = #{id,jdbcType=DECIMAL}
  </update>
  <update id="updateByPrimaryKey" parameterType="com.howbuy.crm.account.dao.po.beisenorg.CmBeisenOrgConfigPO">
    <!--@mbg.generated-->
    update CM_BEISEN_ORG_CONFIG
    set ORG_ID_BEISEN = #{orgIdBeisen,jdbcType=VARCHAR},
      ORG_NAME_BEISEN = #{orgNameBeisen,jdbcType=VARCHAR},
      ORG_CODE = #{orgCode,jdbcType=VARCHAR},
      CENTER_ORG = #{centerOrg,jdbcType=VARCHAR},
      START_DATE = #{startDate,jdbcType=VARCHAR},
      END_DATE = #{endDate,jdbcType=VARCHAR},
      MODIFIER = #{modifier,jdbcType=VARCHAR},
      MODDT = #{moddt,jdbcType=TIMESTAMP}
    where ID = #{id,jdbcType=DECIMAL}
  </update>

  <select id="queryList" parameterType="com.howbuy.crm.account.dao.req.beisen.QueryBeisenOrgConfigVO"
          resultMap="BaseResultMap">
    select t1.ID, t1.ORG_ID_BEISEN, t3.ORG_NAME_BEISEN, ORG_CODE,
    START_DATE, END_DATE,t2.constdesc as CENTER_ORG
    from CM_BEISEN_ORG_CONFIG t1
    left join hb_constant t2
    on t1.center_org = t2.constcode
    and t2.typecode = 'consultExpCenterOrg'
    left join CM_BEISEN_ORG t3
    on t1.org_id_beisen = t3.org_id_beisen
    <where>
      <if test="beisenOrgName != null and beisenOrgName != ''">
        and t3.ORG_NAME_BEISEN like '%'||#{beisenOrgName,jdbcType=VARCHAR}||'%'
      </if>
      <if test="orgCode != null and orgCode != '' and orgCode != '0'.toString() ">
        and t1.ORG_CODE in (
          SELECT ORGCODE
          FROM HB_ORGANIZATION T
          WHERE T.STATUS = '0'
          START WITH T.ORGCODE = #{orgCode}
          CONNECT BY PRIOR ORGCODE = PARENTORGCODE
        )
      </if>
      <if test="startDate != null and startDate != ''">
        and START_DATE >= #{startDate,jdbcType=VARCHAR}
      </if>
      <if test="endDate != null and endDate != ''">
        and END_DATE &lt;= #{endDate,jdbcType=VARCHAR}
      </if>
    </where>
    order by t1.CREDT desc
  </select>

  <select id="selectByOrgId" resultType="long" parameterType="string">
    select id from CM_BEISEN_ORG_CONFIG
    where ORG_ID_BEISEN = #{orgIdBeisen,jdbcType=VARCHAR}
  </select>

  <select id="selectByInterval" resultType="long" parameterType="string">
    select id from CM_BEISEN_ORG_CONFIG
    where
      <if test="endDate == null or endDate == ''">
       START_DATE &lt;= '********'
       and nvl(END_DATE, '********') >= #{startDate,jdbcType=VARCHAR}
      </if>
      <if test="endDate != null and endDate != ''">
        START_DATE &lt;= #{endDate,jdbcType=VARCHAR}
       and nvl(END_DATE, '********') >= #{startDate,jdbcType=VARCHAR}
      </if>
    and ORG_ID_BEISEN = #{orgIdBeisen,jdbcType=VARCHAR}
  </select>

  <select id="selectBeisenOrg" resultType="com.howbuy.crm.account.dao.po.beisen.CmBeisenOrgDO">
    SELECT ORG_ID_BEISEN, ORG_NAME_BEISEN
    FROM CM_BEISEN_ORG
  </select>
</mapper>