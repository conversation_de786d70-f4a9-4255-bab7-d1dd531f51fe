<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.howbuy.crm.account.dao.mapper.organization.HbOrganizationMapper">
  <resultMap id="BaseResultMap" type="com.howbuy.crm.account.dao.po.organization.HbOrganizationPO">
    <!--@mbg.generated-->
    <!--@Table HB_ORGANIZATION-->
    <result column="ORGCODE" jdbcType="OTHER" property="orgcode" />
    <result column="ORGNAME" jdbcType="OTHER" property="orgname" />
    <result column="ENGLISHNAME" jdbcType="OTHER" property="englishname" />
    <result column="PARENTORGCODE" jdbcType="OTHER" property="parentorgcode" />
    <result column="SORT" jdbcType="OTHER" property="sort" />
    <result column="ORGTYPE" jdbcType="OTHER" property="orgtype" />
    <result column="STATUS" jdbcType="OTHER" property="status" />
    <result column="SHOWFLAG" jdbcType="OTHER" property="showflag" />
    <result column="PROVINCE" jdbcType="OTHER" property="province" />
    <result column="PROVINCENAME" jdbcType="OTHER" property="provincename" />
    <result column="CITY" jdbcType="OTHER" property="city" />
    <result column="CITYNAME" jdbcType="OTHER" property="cityname" />
    <result column="AREA" jdbcType="OTHER" property="area" />
    <result column="AREANAME" jdbcType="OTHER" property="areaname" />
    <result column="ADDRESS" jdbcType="OTHER" property="address" />
    <result column="RECSTAT" jdbcType="OTHER" property="recstat" />
    <result column="CHECKFLAG" jdbcType="OTHER" property="checkflag" />
    <result column="TELNO" jdbcType="OTHER" property="telno" />
    <result column="FAX" jdbcType="OTHER" property="fax" />
    <result column="CREATOR" jdbcType="OTHER" property="creator" />
    <result column="MODIFIER" jdbcType="OTHER" property="modifier" />
    <result column="CHECKER" jdbcType="OTHER" property="checker" />
    <result column="CREDATE" jdbcType="OTHER" property="credate" />
    <result column="MODDATE" jdbcType="OTHER" property="moddate" />
    <result column="CHECKDATE" jdbcType="OTHER" property="checkdate" />
    <result column="SYSCODE" jdbcType="OTHER" property="syscode" />
    <result column="SYSNAME" jdbcType="OTHER" property="sysname" />
  </resultMap>
  <sql id="Base_Column_List">
    <!--@mbg.generated-->
    ORGCODE, ORGNAME, ENGLISHNAME, PARENTORGCODE, SORT, ORGTYPE, "STATUS", SHOWFLAG, 
    PROVINCE, PROVINCENAME, CITY, CITYNAME, AREA, AREANAME, ADDRESS, RECSTAT, CHECKFLAG, 
    TELNO, FAX, CREATOR, MODIFIER, CHECKER, CREDATE, MODDATE, CHECKDATE, SYSCODE, SYSNAME
  </sql>
  <insert id="insert" parameterType="com.howbuy.crm.account.dao.po.organization.HbOrganizationPO">
    <!--@mbg.generated-->
    insert into HB_ORGANIZATION (ORGCODE, ORGNAME, ENGLISHNAME, 
      PARENTORGCODE, SORT, ORGTYPE, 
      "STATUS", SHOWFLAG, PROVINCE, 
      PROVINCENAME, CITY, CITYNAME, 
      AREA, AREANAME, ADDRESS, 
      RECSTAT, CHECKFLAG, TELNO, 
      FAX, CREATOR, MODIFIER, CHECKER, 
      CREDATE, MODDATE, CHECKDATE, 
      SYSCODE, SYSNAME)
    values (#{orgcode,jdbcType=OTHER}, #{orgname,jdbcType=OTHER}, #{englishname,jdbcType=OTHER}, 
      #{parentorgcode,jdbcType=OTHER}, #{sort,jdbcType=OTHER}, #{orgtype,jdbcType=OTHER}, 
      #{status,jdbcType=OTHER}, #{showflag,jdbcType=OTHER}, #{province,jdbcType=OTHER}, 
      #{provincename,jdbcType=OTHER}, #{city,jdbcType=OTHER}, #{cityname,jdbcType=OTHER}, 
      #{area,jdbcType=OTHER}, #{areaname,jdbcType=OTHER}, #{address,jdbcType=OTHER}, 
      #{recstat,jdbcType=OTHER}, #{checkflag,jdbcType=OTHER}, #{telno,jdbcType=OTHER}, 
      #{fax,jdbcType=OTHER}, #{creator,jdbcType=OTHER}, #{modifier,jdbcType=OTHER}, #{checker,jdbcType=OTHER}, 
      #{credate,jdbcType=OTHER}, #{moddate,jdbcType=OTHER}, #{checkdate,jdbcType=OTHER}, 
      #{syscode,jdbcType=OTHER}, #{sysname,jdbcType=OTHER})
  </insert>
  <insert id="insertSelective" parameterType="com.howbuy.crm.account.dao.po.organization.HbOrganizationPO">
    <!--@mbg.generated-->
    insert into HB_ORGANIZATION
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="orgcode != null">
        ORGCODE,
      </if>
      <if test="orgname != null">
        ORGNAME,
      </if>
      <if test="englishname != null">
        ENGLISHNAME,
      </if>
      <if test="parentorgcode != null">
        PARENTORGCODE,
      </if>
      <if test="sort != null">
        SORT,
      </if>
      <if test="orgtype != null">
        ORGTYPE,
      </if>
      <if test="status != null">
        "STATUS",
      </if>
      <if test="showflag != null">
        SHOWFLAG,
      </if>
      <if test="province != null">
        PROVINCE,
      </if>
      <if test="provincename != null">
        PROVINCENAME,
      </if>
      <if test="city != null">
        CITY,
      </if>
      <if test="cityname != null">
        CITYNAME,
      </if>
      <if test="area != null">
        AREA,
      </if>
      <if test="areaname != null">
        AREANAME,
      </if>
      <if test="address != null">
        ADDRESS,
      </if>
      <if test="recstat != null">
        RECSTAT,
      </if>
      <if test="checkflag != null">
        CHECKFLAG,
      </if>
      <if test="telno != null">
        TELNO,
      </if>
      <if test="fax != null">
        FAX,
      </if>
      <if test="creator != null">
        CREATOR,
      </if>
      <if test="modifier != null">
        MODIFIER,
      </if>
      <if test="checker != null">
        CHECKER,
      </if>
      <if test="credate != null">
        CREDATE,
      </if>
      <if test="moddate != null">
        MODDATE,
      </if>
      <if test="checkdate != null">
        CHECKDATE,
      </if>
      <if test="syscode != null">
        SYSCODE,
      </if>
      <if test="sysname != null">
        SYSNAME,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="orgcode != null">
        #{orgcode,jdbcType=OTHER},
      </if>
      <if test="orgname != null">
        #{orgname,jdbcType=OTHER},
      </if>
      <if test="englishname != null">
        #{englishname,jdbcType=OTHER},
      </if>
      <if test="parentorgcode != null">
        #{parentorgcode,jdbcType=OTHER},
      </if>
      <if test="sort != null">
        #{sort,jdbcType=OTHER},
      </if>
      <if test="orgtype != null">
        #{orgtype,jdbcType=OTHER},
      </if>
      <if test="status != null">
        #{status,jdbcType=OTHER},
      </if>
      <if test="showflag != null">
        #{showflag,jdbcType=OTHER},
      </if>
      <if test="province != null">
        #{province,jdbcType=OTHER},
      </if>
      <if test="provincename != null">
        #{provincename,jdbcType=OTHER},
      </if>
      <if test="city != null">
        #{city,jdbcType=OTHER},
      </if>
      <if test="cityname != null">
        #{cityname,jdbcType=OTHER},
      </if>
      <if test="area != null">
        #{area,jdbcType=OTHER},
      </if>
      <if test="areaname != null">
        #{areaname,jdbcType=OTHER},
      </if>
      <if test="address != null">
        #{address,jdbcType=OTHER},
      </if>
      <if test="recstat != null">
        #{recstat,jdbcType=OTHER},
      </if>
      <if test="checkflag != null">
        #{checkflag,jdbcType=OTHER},
      </if>
      <if test="telno != null">
        #{telno,jdbcType=OTHER},
      </if>
      <if test="fax != null">
        #{fax,jdbcType=OTHER},
      </if>
      <if test="creator != null">
        #{creator,jdbcType=OTHER},
      </if>
      <if test="modifier != null">
        #{modifier,jdbcType=OTHER},
      </if>
      <if test="checker != null">
        #{checker,jdbcType=OTHER},
      </if>
      <if test="credate != null">
        #{credate,jdbcType=OTHER},
      </if>
      <if test="moddate != null">
        #{moddate,jdbcType=OTHER},
      </if>
      <if test="checkdate != null">
        #{checkdate,jdbcType=OTHER},
      </if>
      <if test="syscode != null">
        #{syscode,jdbcType=OTHER},
      </if>
      <if test="sysname != null">
        #{sysname,jdbcType=OTHER},
      </if>
    </trim>
  </insert>

  <select id="getOrgNameByOrgCodeList" resultType="com.howbuy.crm.account.dao.po.organization.OrgCodeNameDTO">
    select ORGCODE as orgCode, orgname as orgName
    from HB_ORGANIZATION
    where ORGCODE in
    <foreach item="item" index="index" collection="list" open="(" separator="," close=")">
      #{item}
    </foreach>
  </select>
</mapper>