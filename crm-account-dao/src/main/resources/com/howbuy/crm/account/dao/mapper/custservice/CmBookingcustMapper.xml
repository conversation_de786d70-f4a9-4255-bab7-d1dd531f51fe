<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.howbuy.crm.account.dao.mapper.custservice.CmBookingcustMapper">
  <resultMap id="BaseResultMap" type="com.howbuy.crm.account.dao.po.custservice.CmBookingcustPO">
    <!--@mbg.generated-->
    <!--@Table CM_BOOKINGCUST-->
    <id column="ID" jdbcType="DECIMAL" property="id" />
    <result column="BOOKINGDT" jdbcType="VARCHAR" property="bookingdt" />
    <result column="CUSTNAME" jdbcType="VARCHAR" property="custname" />
    <result column="BOOKINGTYPE" jdbcType="VARCHAR" property="bookingtype" />
    <result column="ACTIVITYTYPE" jdbcType="VARCHAR" property="activitytype" />
    <result column="CHANNELCODE" jdbcType="VARCHAR" property="channelcode" />
    <result column="BOOKINGCONTENT" jdbcType="VARCHAR" property="bookingcontent" />
    <result column="SOURCEADDR" jdbcType="VARCHAR" property="sourceaddr" />
    <result column="HANDLESTAT" jdbcType="VARCHAR" property="handlestat" />
    <result column="CONSCUSTNO" jdbcType="VARCHAR" property="conscustno" />
    <result column="RECSTAT" jdbcType="VARCHAR" property="recstat" />
    <result column="CREATOR" jdbcType="VARCHAR" property="creator" />
    <result column="MODIFIER" jdbcType="VARCHAR" property="modifier" />
    <result column="CREDT" jdbcType="VARCHAR" property="credt" />
    <result column="MODDT" jdbcType="VARCHAR" property="moddt" />
    <result column="SYNC_DATE" jdbcType="TIMESTAMP" property="syncDate" />
    <result column="VALIDATEFLAG" jdbcType="DECIMAL" property="validateflag" />
    <result column="FRAUDFLAG" jdbcType="DECIMAL" property="fraudflag" />
    <result column="FRAUDINFO" jdbcType="VARCHAR" property="fraudinfo" />
    <result column="READFLAG" jdbcType="DECIMAL" property="readflag" />
    <result column="BOOKINGSERIALNO" jdbcType="VARCHAR" property="bookingserialno" />
    <result column="BOOKINGDETAILDT" jdbcType="VARCHAR" property="bookingdetaildt" />
    <result column="WIRELESS_CHANNEL" jdbcType="VARCHAR" property="wirelessChannel" />
    <result column="QUALIFIED_STATUS" jdbcType="VARCHAR" property="qualifiedStatus" />
    <result column="STARTHOUR" jdbcType="VARCHAR" property="starthour" />
    <result column="ENDHOUR" jdbcType="VARCHAR" property="endhour" />
    <result column="REG_OUTLET_CODE" jdbcType="VARCHAR" property="regOutletCode" />
    <result column="SOURCE_SYS" jdbcType="VARCHAR" property="sourceSys" />
    <result column="HBONE_NO" jdbcType="VARCHAR" property="hboneNo" />
    <result column="HBONE_REG_OUTLET_CODE" jdbcType="VARCHAR" property="hboneRegOutletCode" />
    <result column="OUTLET_CODE" jdbcType="VARCHAR" property="outletCode" />
    <result column="MOBILE_MASK" jdbcType="VARCHAR" property="mobileMask" />
    <result column="MOBILE_DIGEST" jdbcType="VARCHAR" property="mobileDigest" />
    <result column="MOBILE_CIPHER" jdbcType="VARCHAR" property="mobileCipher" />
    <result column="EMAIL_MASK" jdbcType="VARCHAR" property="emailMask" />
    <result column="EMAIL_DIGEST" jdbcType="VARCHAR" property="emailDigest" />
    <result column="EMAIL_CIPHER" jdbcType="VARCHAR" property="emailCipher" />
    <result column="DISCODE" jdbcType="VARCHAR" property="discode" />
  </resultMap>
  <sql id="Base_Column_List">
    <!--@mbg.generated-->
    ID, BOOKINGDT, CUSTNAME, BOOKINGTYPE, ACTIVITYTYPE, CHANNELCODE, BOOKINGCONTENT, 
    SOURCEADDR, HANDLESTAT, CONSCUSTNO, RECSTAT, CREATOR, MODIFIER, CREDT, MODDT, SYNC_DATE, 
    VALIDATEFLAG, FRAUDFLAG, FRAUDINFO, READFLAG, BOOKINGSERIALNO, BOOKINGDETAILDT, WIRELESS_CHANNEL, 
    QUALIFIED_STATUS, STARTHOUR, ENDHOUR, REG_OUTLET_CODE, SOURCE_SYS, HBONE_NO, HBONE_REG_OUTLET_CODE, 
    OUTLET_CODE, MOBILE_MASK, MOBILE_DIGEST, MOBILE_CIPHER, EMAIL_MASK, EMAIL_DIGEST, 
    EMAIL_CIPHER, DISCODE
  </sql>
  <select id="selectByPrimaryKey" parameterType="java.lang.Long" resultMap="BaseResultMap">
    <!--@mbg.generated-->
    select 
    <include refid="Base_Column_List" />
    from CM_BOOKINGCUST
    where ID = #{id,jdbcType=DECIMAL}
  </select>
  <delete id="deleteByPrimaryKey" parameterType="java.lang.Long">
    <!--@mbg.generated-->
    delete from CM_BOOKINGCUST
    where ID = #{id,jdbcType=DECIMAL}
  </delete>
  <insert id="insert" parameterType="com.howbuy.crm.account.dao.po.custservice.CmBookingcustPO">
    <!--@mbg.generated-->
    insert into CM_BOOKINGCUST (ID, BOOKINGDT, CUSTNAME, 
      BOOKINGTYPE, ACTIVITYTYPE, CHANNELCODE, 
      BOOKINGCONTENT, SOURCEADDR, HANDLESTAT, 
      CONSCUSTNO, RECSTAT, CREATOR, 
      MODIFIER, CREDT, MODDT, 
      SYNC_DATE, VALIDATEFLAG, FRAUDFLAG, 
      FRAUDINFO, READFLAG, BOOKINGSERIALNO, 
      BOOKINGDETAILDT, WIRELESS_CHANNEL, QUALIFIED_STATUS, 
      STARTHOUR, ENDHOUR, REG_OUTLET_CODE, 
      SOURCE_SYS, HBONE_NO, HBONE_REG_OUTLET_CODE, 
      OUTLET_CODE, MOBILE_MASK, MOBILE_DIGEST, 
      MOBILE_CIPHER, EMAIL_MASK, EMAIL_DIGEST, 
      EMAIL_CIPHER, DISCODE)
    values (#{id,jdbcType=DECIMAL}, #{bookingdt,jdbcType=VARCHAR}, #{custname,jdbcType=VARCHAR}, 
      #{bookingtype,jdbcType=VARCHAR}, #{activitytype,jdbcType=VARCHAR}, #{channelcode,jdbcType=VARCHAR}, 
      #{bookingcontent,jdbcType=VARCHAR}, #{sourceaddr,jdbcType=VARCHAR}, #{handlestat,jdbcType=VARCHAR}, 
      #{conscustno,jdbcType=VARCHAR}, #{recstat,jdbcType=VARCHAR}, #{creator,jdbcType=VARCHAR}, 
      #{modifier,jdbcType=VARCHAR}, #{credt,jdbcType=VARCHAR}, #{moddt,jdbcType=VARCHAR}, 
      #{syncDate,jdbcType=TIMESTAMP}, #{validateflag,jdbcType=DECIMAL}, #{fraudflag,jdbcType=DECIMAL}, 
      #{fraudinfo,jdbcType=VARCHAR}, #{readflag,jdbcType=DECIMAL}, #{bookingserialno,jdbcType=VARCHAR}, 
      #{bookingdetaildt,jdbcType=VARCHAR}, #{wirelessChannel,jdbcType=VARCHAR}, #{qualifiedStatus,jdbcType=VARCHAR}, 
      #{starthour,jdbcType=VARCHAR}, #{endhour,jdbcType=VARCHAR}, #{regOutletCode,jdbcType=VARCHAR}, 
      #{sourceSys,jdbcType=VARCHAR}, #{hboneNo,jdbcType=VARCHAR}, #{hboneRegOutletCode,jdbcType=VARCHAR}, 
      #{outletCode,jdbcType=VARCHAR}, #{mobileMask,jdbcType=VARCHAR}, #{mobileDigest,jdbcType=VARCHAR}, 
      #{mobileCipher,jdbcType=VARCHAR}, #{emailMask,jdbcType=VARCHAR}, #{emailDigest,jdbcType=VARCHAR}, 
      #{emailCipher,jdbcType=VARCHAR}, #{discode,jdbcType=VARCHAR})
  </insert>
  <insert id="insertSelective" parameterType="com.howbuy.crm.account.dao.po.custservice.CmBookingcustPO">
    <!--@mbg.generated-->
    insert into CM_BOOKINGCUST
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="id != null">
        ID,
      </if>
      <if test="bookingdt != null">
        BOOKINGDT,
      </if>
      <if test="custname != null">
        CUSTNAME,
      </if>
      <if test="bookingtype != null">
        BOOKINGTYPE,
      </if>
      <if test="activitytype != null">
        ACTIVITYTYPE,
      </if>
      <if test="channelcode != null">
        CHANNELCODE,
      </if>
      <if test="bookingcontent != null">
        BOOKINGCONTENT,
      </if>
      <if test="sourceaddr != null">
        SOURCEADDR,
      </if>
      <if test="handlestat != null">
        HANDLESTAT,
      </if>
      <if test="conscustno != null">
        CONSCUSTNO,
      </if>
      <if test="recstat != null">
        RECSTAT,
      </if>
      <if test="creator != null">
        CREATOR,
      </if>
      <if test="modifier != null">
        MODIFIER,
      </if>
      <if test="credt != null">
        CREDT,
      </if>
      <if test="moddt != null">
        MODDT,
      </if>
      <if test="syncDate != null">
        SYNC_DATE,
      </if>
      <if test="validateflag != null">
        VALIDATEFLAG,
      </if>
      <if test="fraudflag != null">
        FRAUDFLAG,
      </if>
      <if test="fraudinfo != null">
        FRAUDINFO,
      </if>
      <if test="readflag != null">
        READFLAG,
      </if>
      <if test="bookingserialno != null">
        BOOKINGSERIALNO,
      </if>
      <if test="bookingdetaildt != null">
        BOOKINGDETAILDT,
      </if>
      <if test="wirelessChannel != null">
        WIRELESS_CHANNEL,
      </if>
      <if test="qualifiedStatus != null">
        QUALIFIED_STATUS,
      </if>
      <if test="starthour != null">
        STARTHOUR,
      </if>
      <if test="endhour != null">
        ENDHOUR,
      </if>
      <if test="regOutletCode != null">
        REG_OUTLET_CODE,
      </if>
      <if test="sourceSys != null">
        SOURCE_SYS,
      </if>
      <if test="hboneNo != null">
        HBONE_NO,
      </if>
      <if test="hboneRegOutletCode != null">
        HBONE_REG_OUTLET_CODE,
      </if>
      <if test="outletCode != null">
        OUTLET_CODE,
      </if>
      <if test="mobileMask != null">
        MOBILE_MASK,
      </if>
      <if test="mobileDigest != null">
        MOBILE_DIGEST,
      </if>
      <if test="mobileCipher != null">
        MOBILE_CIPHER,
      </if>
      <if test="emailMask != null">
        EMAIL_MASK,
      </if>
      <if test="emailDigest != null">
        EMAIL_DIGEST,
      </if>
      <if test="emailCipher != null">
        EMAIL_CIPHER,
      </if>
      <if test="discode != null">
        DISCODE,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="id != null">
        #{id,jdbcType=DECIMAL},
      </if>
      <if test="bookingdt != null">
        #{bookingdt,jdbcType=VARCHAR},
      </if>
      <if test="custname != null">
        #{custname,jdbcType=VARCHAR},
      </if>
      <if test="bookingtype != null">
        #{bookingtype,jdbcType=VARCHAR},
      </if>
      <if test="activitytype != null">
        #{activitytype,jdbcType=VARCHAR},
      </if>
      <if test="channelcode != null">
        #{channelcode,jdbcType=VARCHAR},
      </if>
      <if test="bookingcontent != null">
        #{bookingcontent,jdbcType=VARCHAR},
      </if>
      <if test="sourceaddr != null">
        #{sourceaddr,jdbcType=VARCHAR},
      </if>
      <if test="handlestat != null">
        #{handlestat,jdbcType=VARCHAR},
      </if>
      <if test="conscustno != null">
        #{conscustno,jdbcType=VARCHAR},
      </if>
      <if test="recstat != null">
        #{recstat,jdbcType=VARCHAR},
      </if>
      <if test="creator != null">
        #{creator,jdbcType=VARCHAR},
      </if>
      <if test="modifier != null">
        #{modifier,jdbcType=VARCHAR},
      </if>
      <if test="credt != null">
        #{credt,jdbcType=VARCHAR},
      </if>
      <if test="moddt != null">
        #{moddt,jdbcType=VARCHAR},
      </if>
      <if test="syncDate != null">
        #{syncDate,jdbcType=TIMESTAMP},
      </if>
      <if test="validateflag != null">
        #{validateflag,jdbcType=DECIMAL},
      </if>
      <if test="fraudflag != null">
        #{fraudflag,jdbcType=DECIMAL},
      </if>
      <if test="fraudinfo != null">
        #{fraudinfo,jdbcType=VARCHAR},
      </if>
      <if test="readflag != null">
        #{readflag,jdbcType=DECIMAL},
      </if>
      <if test="bookingserialno != null">
        #{bookingserialno,jdbcType=VARCHAR},
      </if>
      <if test="bookingdetaildt != null">
        #{bookingdetaildt,jdbcType=VARCHAR},
      </if>
      <if test="wirelessChannel != null">
        #{wirelessChannel,jdbcType=VARCHAR},
      </if>
      <if test="qualifiedStatus != null">
        #{qualifiedStatus,jdbcType=VARCHAR},
      </if>
      <if test="starthour != null">
        #{starthour,jdbcType=VARCHAR},
      </if>
      <if test="endhour != null">
        #{endhour,jdbcType=VARCHAR},
      </if>
      <if test="regOutletCode != null">
        #{regOutletCode,jdbcType=VARCHAR},
      </if>
      <if test="sourceSys != null">
        #{sourceSys,jdbcType=VARCHAR},
      </if>
      <if test="hboneNo != null">
        #{hboneNo,jdbcType=VARCHAR},
      </if>
      <if test="hboneRegOutletCode != null">
        #{hboneRegOutletCode,jdbcType=VARCHAR},
      </if>
      <if test="outletCode != null">
        #{outletCode,jdbcType=VARCHAR},
      </if>
      <if test="mobileMask != null">
        #{mobileMask,jdbcType=VARCHAR},
      </if>
      <if test="mobileDigest != null">
        #{mobileDigest,jdbcType=VARCHAR},
      </if>
      <if test="mobileCipher != null">
        #{mobileCipher,jdbcType=VARCHAR},
      </if>
      <if test="emailMask != null">
        #{emailMask,jdbcType=VARCHAR},
      </if>
      <if test="emailDigest != null">
        #{emailDigest,jdbcType=VARCHAR},
      </if>
      <if test="emailCipher != null">
        #{emailCipher,jdbcType=VARCHAR},
      </if>
      <if test="discode != null">
        #{discode,jdbcType=VARCHAR},
      </if>
    </trim>
  </insert>
  <update id="updateByPrimaryKeySelective" parameterType="com.howbuy.crm.account.dao.po.custservice.CmBookingcustPO">
    <!--@mbg.generated-->
    update CM_BOOKINGCUST
    <set>
      <if test="bookingdt != null">
        BOOKINGDT = #{bookingdt,jdbcType=VARCHAR},
      </if>
      <if test="custname != null">
        CUSTNAME = #{custname,jdbcType=VARCHAR},
      </if>
      <if test="bookingtype != null">
        BOOKINGTYPE = #{bookingtype,jdbcType=VARCHAR},
      </if>
      <if test="activitytype != null">
        ACTIVITYTYPE = #{activitytype,jdbcType=VARCHAR},
      </if>
      <if test="channelcode != null">
        CHANNELCODE = #{channelcode,jdbcType=VARCHAR},
      </if>
      <if test="bookingcontent != null">
        BOOKINGCONTENT = #{bookingcontent,jdbcType=VARCHAR},
      </if>
      <if test="sourceaddr != null">
        SOURCEADDR = #{sourceaddr,jdbcType=VARCHAR},
      </if>
      <if test="handlestat != null">
        HANDLESTAT = #{handlestat,jdbcType=VARCHAR},
      </if>
      <if test="conscustno != null">
        CONSCUSTNO = #{conscustno,jdbcType=VARCHAR},
      </if>
      <if test="recstat != null">
        RECSTAT = #{recstat,jdbcType=VARCHAR},
      </if>
      <if test="creator != null">
        CREATOR = #{creator,jdbcType=VARCHAR},
      </if>
      <if test="modifier != null">
        MODIFIER = #{modifier,jdbcType=VARCHAR},
      </if>
      <if test="credt != null">
        CREDT = #{credt,jdbcType=VARCHAR},
      </if>
      <if test="moddt != null">
        MODDT = #{moddt,jdbcType=VARCHAR},
      </if>
      <if test="syncDate != null">
        SYNC_DATE = #{syncDate,jdbcType=TIMESTAMP},
      </if>
      <if test="validateflag != null">
        VALIDATEFLAG = #{validateflag,jdbcType=DECIMAL},
      </if>
      <if test="fraudflag != null">
        FRAUDFLAG = #{fraudflag,jdbcType=DECIMAL},
      </if>
      <if test="fraudinfo != null">
        FRAUDINFO = #{fraudinfo,jdbcType=VARCHAR},
      </if>
      <if test="readflag != null">
        READFLAG = #{readflag,jdbcType=DECIMAL},
      </if>
      <if test="bookingserialno != null">
        BOOKINGSERIALNO = #{bookingserialno,jdbcType=VARCHAR},
      </if>
      <if test="bookingdetaildt != null">
        BOOKINGDETAILDT = #{bookingdetaildt,jdbcType=VARCHAR},
      </if>
      <if test="wirelessChannel != null">
        WIRELESS_CHANNEL = #{wirelessChannel,jdbcType=VARCHAR},
      </if>
      <if test="qualifiedStatus != null">
        QUALIFIED_STATUS = #{qualifiedStatus,jdbcType=VARCHAR},
      </if>
      <if test="starthour != null">
        STARTHOUR = #{starthour,jdbcType=VARCHAR},
      </if>
      <if test="endhour != null">
        ENDHOUR = #{endhour,jdbcType=VARCHAR},
      </if>
      <if test="regOutletCode != null">
        REG_OUTLET_CODE = #{regOutletCode,jdbcType=VARCHAR},
      </if>
      <if test="sourceSys != null">
        SOURCE_SYS = #{sourceSys,jdbcType=VARCHAR},
      </if>
      <if test="hboneNo != null">
        HBONE_NO = #{hboneNo,jdbcType=VARCHAR},
      </if>
      <if test="hboneRegOutletCode != null">
        HBONE_REG_OUTLET_CODE = #{hboneRegOutletCode,jdbcType=VARCHAR},
      </if>
      <if test="outletCode != null">
        OUTLET_CODE = #{outletCode,jdbcType=VARCHAR},
      </if>
      <if test="mobileMask != null">
        MOBILE_MASK = #{mobileMask,jdbcType=VARCHAR},
      </if>
      <if test="mobileDigest != null">
        MOBILE_DIGEST = #{mobileDigest,jdbcType=VARCHAR},
      </if>
      <if test="mobileCipher != null">
        MOBILE_CIPHER = #{mobileCipher,jdbcType=VARCHAR},
      </if>
      <if test="emailMask != null">
        EMAIL_MASK = #{emailMask,jdbcType=VARCHAR},
      </if>
      <if test="emailDigest != null">
        EMAIL_DIGEST = #{emailDigest,jdbcType=VARCHAR},
      </if>
      <if test="emailCipher != null">
        EMAIL_CIPHER = #{emailCipher,jdbcType=VARCHAR},
      </if>
      <if test="discode != null">
        DISCODE = #{discode,jdbcType=VARCHAR},
      </if>
    </set>
    where ID = #{id,jdbcType=DECIMAL}
  </update>
  <update id="updateByPrimaryKey" parameterType="com.howbuy.crm.account.dao.po.custservice.CmBookingcustPO">
    <!--@mbg.generated-->
    update CM_BOOKINGCUST
    set BOOKINGDT = #{bookingdt,jdbcType=VARCHAR},
      CUSTNAME = #{custname,jdbcType=VARCHAR},
      BOOKINGTYPE = #{bookingtype,jdbcType=VARCHAR},
      ACTIVITYTYPE = #{activitytype,jdbcType=VARCHAR},
      CHANNELCODE = #{channelcode,jdbcType=VARCHAR},
      BOOKINGCONTENT = #{bookingcontent,jdbcType=VARCHAR},
      SOURCEADDR = #{sourceaddr,jdbcType=VARCHAR},
      HANDLESTAT = #{handlestat,jdbcType=VARCHAR},
      CONSCUSTNO = #{conscustno,jdbcType=VARCHAR},
      RECSTAT = #{recstat,jdbcType=VARCHAR},
      CREATOR = #{creator,jdbcType=VARCHAR},
      MODIFIER = #{modifier,jdbcType=VARCHAR},
      CREDT = #{credt,jdbcType=VARCHAR},
      MODDT = #{moddt,jdbcType=VARCHAR},
      SYNC_DATE = #{syncDate,jdbcType=TIMESTAMP},
      VALIDATEFLAG = #{validateflag,jdbcType=DECIMAL},
      FRAUDFLAG = #{fraudflag,jdbcType=DECIMAL},
      FRAUDINFO = #{fraudinfo,jdbcType=VARCHAR},
      READFLAG = #{readflag,jdbcType=DECIMAL},
      BOOKINGSERIALNO = #{bookingserialno,jdbcType=VARCHAR},
      BOOKINGDETAILDT = #{bookingdetaildt,jdbcType=VARCHAR},
      WIRELESS_CHANNEL = #{wirelessChannel,jdbcType=VARCHAR},
      QUALIFIED_STATUS = #{qualifiedStatus,jdbcType=VARCHAR},
      STARTHOUR = #{starthour,jdbcType=VARCHAR},
      ENDHOUR = #{endhour,jdbcType=VARCHAR},
      REG_OUTLET_CODE = #{regOutletCode,jdbcType=VARCHAR},
      SOURCE_SYS = #{sourceSys,jdbcType=VARCHAR},
      HBONE_NO = #{hboneNo,jdbcType=VARCHAR},
      HBONE_REG_OUTLET_CODE = #{hboneRegOutletCode,jdbcType=VARCHAR},
      OUTLET_CODE = #{outletCode,jdbcType=VARCHAR},
      MOBILE_MASK = #{mobileMask,jdbcType=VARCHAR},
      MOBILE_DIGEST = #{mobileDigest,jdbcType=VARCHAR},
      MOBILE_CIPHER = #{mobileCipher,jdbcType=VARCHAR},
      EMAIL_MASK = #{emailMask,jdbcType=VARCHAR},
      EMAIL_DIGEST = #{emailDigest,jdbcType=VARCHAR},
      EMAIL_CIPHER = #{emailCipher,jdbcType=VARCHAR},
      DISCODE = #{discode,jdbcType=VARCHAR}
    where ID = #{id,jdbcType=DECIMAL}
  </update>
</mapper>