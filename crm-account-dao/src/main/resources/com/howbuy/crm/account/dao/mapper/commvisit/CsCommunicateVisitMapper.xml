<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.howbuy.crm.account.dao.mapper.commvisit.CsCommunicateVisitMapper">
  <resultMap id="BaseResultMap" type="com.howbuy.crm.account.dao.po.commvisit.CsCommunicateVisitPO">
    <!--@mbg.generated-->
    <!--@Table CS_COMMUNICATE_VISIT-->
    <id column="ID" jdbcType="VARCHAR" property="id" />
    <result column="CONSCUSTNO" jdbcType="VARCHAR" property="conscustno" />
    <result column="CONSULTTYPE" jdbcType="VARCHAR" property="consulttype" />
    <result column="COMMINTENT" jdbcType="VARCHAR" property="commintent" />
    <result column="INVESTINTENT" jdbcType="VARCHAR" property="investintent" />
    <result column="AMOUNTFLAG" jdbcType="VARCHAR" property="amountflag" />
    <result column="SPECIALMARK" jdbcType="VARCHAR" property="specialmark" />
    <result column="DEPTFLAG" jdbcType="VARCHAR" property="deptflag" />
    <result column="COMMCONTENT" jdbcType="VARCHAR" property="commcontent" />
    <result column="TASKID" jdbcType="VARCHAR" property="taskid" />
    <result column="BOOKINGCONTENT" jdbcType="VARCHAR" property="bookingcontent" />
    <result column="CALLINID" jdbcType="VARCHAR" property="callinid" />
    <result column="VISITTYPE" jdbcType="VARCHAR" property="visittype" />
    <result column="NEXTDT" jdbcType="VARCHAR" property="nextdt" />
    <result column="CONSBOOKINGID" jdbcType="VARCHAR" property="consbookingid" />
    <result column="NEXTVISITCONTENT" jdbcType="VARCHAR" property="nextvisitcontent" />
    <result column="NEXTSTARTTIME" jdbcType="VARCHAR" property="nextstarttime" />
    <result column="NEXTENDTIME" jdbcType="VARCHAR" property="nextendtime" />
    <result column="NEXTVISITTYPE" jdbcType="VARCHAR" property="nextvisittype" />
    <result column="VISITCLASSIFY" jdbcType="VARCHAR" property="visitclassify" />
    <result column="REMARK" jdbcType="VARCHAR" property="remark" />
    <result column="MODIFYFLAG" jdbcType="VARCHAR" property="modifyflag" />
    <result column="CREATOR" jdbcType="VARCHAR" property="creator" />
    <result column="CREDT" jdbcType="TIMESTAMP" property="credt" />
    <result column="MODIFIER" jdbcType="VARCHAR" property="modifier" />
    <result column="MODDT" jdbcType="TIMESTAMP" property="moddt" />
    <result column="STIMESTAMP" jdbcType="TIMESTAMP" property="stimestamp" />
    <result column="HISFLAG" jdbcType="VARCHAR" property="hisflag" />
    <result column="HISID" jdbcType="VARCHAR" property="hisid" />
  </resultMap>
  <sql id="Base_Column_List">
    <!--@mbg.generated-->
    ID, CONSCUSTNO, CONSULTTYPE, COMMINTENT, INVESTINTENT, AMOUNTFLAG, SPECIALMARK, DEPTFLAG, 
    COMMCONTENT, TASKID, BOOKINGCONTENT, CALLINID, VISITTYPE, NEXTDT, CONSBOOKINGID, 
    NEXTVISITCONTENT, NEXTSTARTTIME, NEXTENDTIME, NEXTVISITTYPE, VISITCLASSIFY, REMARK, 
    MODIFYFLAG, CREATOR, CREDT, MODIFIER, MODDT, STIMESTAMP, HISFLAG, HISID
  </sql>
  <select id="selectByPrimaryKey" parameterType="java.lang.String" resultMap="BaseResultMap">
    <!--@mbg.generated-->
    select 
    <include refid="Base_Column_List" />
    from CS_COMMUNICATE_VISIT
    where ID = #{id,jdbcType=VARCHAR}
  </select>
  <delete id="deleteByPrimaryKey" parameterType="java.lang.String">
    <!--@mbg.generated-->
    delete from CS_COMMUNICATE_VISIT
    where ID = #{id,jdbcType=VARCHAR}
  </delete>
  <insert id="insert" parameterType="com.howbuy.crm.account.dao.po.commvisit.CsCommunicateVisitPO">
    <!--@mbg.generated-->
    insert into CS_COMMUNICATE_VISIT (ID, CONSCUSTNO, CONSULTTYPE, 
      COMMINTENT, INVESTINTENT, AMOUNTFLAG, 
      SPECIALMARK, DEPTFLAG, COMMCONTENT, 
      TASKID, BOOKINGCONTENT, CALLINID, 
      VISITTYPE, NEXTDT, CONSBOOKINGID, 
      NEXTVISITCONTENT, NEXTSTARTTIME, NEXTENDTIME, 
      NEXTVISITTYPE, VISITCLASSIFY, REMARK, 
      MODIFYFLAG, CREATOR, CREDT, 
      MODIFIER, MODDT, STIMESTAMP, 
      HISFLAG, HISID, VISIT_DATE)
    values (#{id,jdbcType=VARCHAR}, #{conscustno,jdbcType=VARCHAR}, #{consulttype,jdbcType=VARCHAR}, 
      #{commintent,jdbcType=VARCHAR}, #{investintent,jdbcType=VARCHAR}, #{amountflag,jdbcType=VARCHAR}, 
      #{specialmark,jdbcType=VARCHAR}, #{deptflag,jdbcType=VARCHAR}, #{commcontent,jdbcType=VARCHAR}, 
      #{taskid,jdbcType=VARCHAR}, #{bookingcontent,jdbcType=VARCHAR}, #{callinid,jdbcType=VARCHAR}, 
      #{visittype,jdbcType=VARCHAR}, #{nextdt,jdbcType=VARCHAR}, #{consbookingid,jdbcType=VARCHAR}, 
      #{nextvisitcontent,jdbcType=VARCHAR}, #{nextstarttime,jdbcType=VARCHAR}, #{nextendtime,jdbcType=VARCHAR}, 
      #{nextvisittype,jdbcType=VARCHAR}, #{visitclassify,jdbcType=VARCHAR}, #{remark,jdbcType=VARCHAR}, 
      #{modifyflag,jdbcType=VARCHAR}, #{creator,jdbcType=VARCHAR}, #{credt,jdbcType=TIMESTAMP}, 
      #{modifier,jdbcType=VARCHAR}, #{moddt,jdbcType=TIMESTAMP}, #{stimestamp,jdbcType=TIMESTAMP}, 
      #{hisflag,jdbcType=VARCHAR}, #{hisid,jdbcType=VARCHAR}, #{visitDate,jdbcType=VARCHAR})
  </insert>
  <insert id="insertSelective" parameterType="com.howbuy.crm.account.dao.po.commvisit.CsCommunicateVisitPO">
    <!--@mbg.generated-->
    insert into CS_COMMUNICATE_VISIT
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="id != null">
        ID,
      </if>
      <if test="conscustno != null">
        CONSCUSTNO,
      </if>
      <if test="consulttype != null">
        CONSULTTYPE,
      </if>
      <if test="commintent != null">
        COMMINTENT,
      </if>
      <if test="investintent != null">
        INVESTINTENT,
      </if>
      <if test="amountflag != null">
        AMOUNTFLAG,
      </if>
      <if test="specialmark != null">
        SPECIALMARK,
      </if>
      <if test="deptflag != null">
        DEPTFLAG,
      </if>
      <if test="commcontent != null">
        COMMCONTENT,
      </if>
      <if test="taskid != null">
        TASKID,
      </if>
      <if test="bookingcontent != null">
        BOOKINGCONTENT,
      </if>
      <if test="callinid != null">
        CALLINID,
      </if>
      <if test="visittype != null">
        VISITTYPE,
      </if>
      <if test="nextdt != null">
        NEXTDT,
      </if>
      <if test="consbookingid != null">
        CONSBOOKINGID,
      </if>
      <if test="nextvisitcontent != null">
        NEXTVISITCONTENT,
      </if>
      <if test="nextstarttime != null">
        NEXTSTARTTIME,
      </if>
      <if test="nextendtime != null">
        NEXTENDTIME,
      </if>
      <if test="nextvisittype != null">
        NEXTVISITTYPE,
      </if>
      <if test="visitclassify != null">
        VISITCLASSIFY,
      </if>
      <if test="remark != null">
        REMARK,
      </if>
      <if test="modifyflag != null">
        MODIFYFLAG,
      </if>
      <if test="creator != null">
        CREATOR,
      </if>
      <if test="credt != null">
        CREDT,
      </if>
      <if test="modifier != null">
        MODIFIER,
      </if>
      <if test="moddt != null">
        MODDT,
      </if>
      <if test="stimestamp != null">
        STIMESTAMP,
      </if>
      <if test="hisflag != null">
        HISFLAG,
      </if>
      <if test="hisid != null">
        HISID,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="id != null">
        #{id,jdbcType=VARCHAR},
      </if>
      <if test="conscustno != null">
        #{conscustno,jdbcType=VARCHAR},
      </if>
      <if test="consulttype != null">
        #{consulttype,jdbcType=VARCHAR},
      </if>
      <if test="commintent != null">
        #{commintent,jdbcType=VARCHAR},
      </if>
      <if test="investintent != null">
        #{investintent,jdbcType=VARCHAR},
      </if>
      <if test="amountflag != null">
        #{amountflag,jdbcType=VARCHAR},
      </if>
      <if test="specialmark != null">
        #{specialmark,jdbcType=VARCHAR},
      </if>
      <if test="deptflag != null">
        #{deptflag,jdbcType=VARCHAR},
      </if>
      <if test="commcontent != null">
        #{commcontent,jdbcType=VARCHAR},
      </if>
      <if test="taskid != null">
        #{taskid,jdbcType=VARCHAR},
      </if>
      <if test="bookingcontent != null">
        #{bookingcontent,jdbcType=VARCHAR},
      </if>
      <if test="callinid != null">
        #{callinid,jdbcType=VARCHAR},
      </if>
      <if test="visittype != null">
        #{visittype,jdbcType=VARCHAR},
      </if>
      <if test="nextdt != null">
        #{nextdt,jdbcType=VARCHAR},
      </if>
      <if test="consbookingid != null">
        #{consbookingid,jdbcType=VARCHAR},
      </if>
      <if test="nextvisitcontent != null">
        #{nextvisitcontent,jdbcType=VARCHAR},
      </if>
      <if test="nextstarttime != null">
        #{nextstarttime,jdbcType=VARCHAR},
      </if>
      <if test="nextendtime != null">
        #{nextendtime,jdbcType=VARCHAR},
      </if>
      <if test="nextvisittype != null">
        #{nextvisittype,jdbcType=VARCHAR},
      </if>
      <if test="visitclassify != null">
        #{visitclassify,jdbcType=VARCHAR},
      </if>
      <if test="remark != null">
        #{remark,jdbcType=VARCHAR},
      </if>
      <if test="modifyflag != null">
        #{modifyflag,jdbcType=VARCHAR},
      </if>
      <if test="creator != null">
        #{creator,jdbcType=VARCHAR},
      </if>
      <if test="credt != null">
        #{credt,jdbcType=TIMESTAMP},
      </if>
      <if test="modifier != null">
        #{modifier,jdbcType=VARCHAR},
      </if>
      <if test="moddt != null">
        #{moddt,jdbcType=TIMESTAMP},
      </if>
      <if test="stimestamp != null">
        #{stimestamp,jdbcType=TIMESTAMP},
      </if>
      <if test="hisflag != null">
        #{hisflag,jdbcType=VARCHAR},
      </if>
      <if test="hisid != null">
        #{hisid,jdbcType=VARCHAR},
      </if>
    </trim>
  </insert>
  <update id="updateByPrimaryKeySelective" parameterType="com.howbuy.crm.account.dao.po.commvisit.CsCommunicateVisitPO">
    <!--@mbg.generated-->
    update CS_COMMUNICATE_VISIT
    <set>
      <if test="conscustno != null">
        CONSCUSTNO = #{conscustno,jdbcType=VARCHAR},
      </if>
      <if test="consulttype != null">
        CONSULTTYPE = #{consulttype,jdbcType=VARCHAR},
      </if>
      <if test="commintent != null">
        COMMINTENT = #{commintent,jdbcType=VARCHAR},
      </if>
      <if test="investintent != null">
        INVESTINTENT = #{investintent,jdbcType=VARCHAR},
      </if>
      <if test="amountflag != null">
        AMOUNTFLAG = #{amountflag,jdbcType=VARCHAR},
      </if>
      <if test="specialmark != null">
        SPECIALMARK = #{specialmark,jdbcType=VARCHAR},
      </if>
      <if test="deptflag != null">
        DEPTFLAG = #{deptflag,jdbcType=VARCHAR},
      </if>
      <if test="commcontent != null">
        COMMCONTENT = #{commcontent,jdbcType=VARCHAR},
      </if>
      <if test="taskid != null">
        TASKID = #{taskid,jdbcType=VARCHAR},
      </if>
      <if test="bookingcontent != null">
        BOOKINGCONTENT = #{bookingcontent,jdbcType=VARCHAR},
      </if>
      <if test="callinid != null">
        CALLINID = #{callinid,jdbcType=VARCHAR},
      </if>
      <if test="visittype != null">
        VISITTYPE = #{visittype,jdbcType=VARCHAR},
      </if>
      <if test="nextdt != null">
        NEXTDT = #{nextdt,jdbcType=VARCHAR},
      </if>
      <if test="consbookingid != null">
        CONSBOOKINGID = #{consbookingid,jdbcType=VARCHAR},
      </if>
      <if test="nextvisitcontent != null">
        NEXTVISITCONTENT = #{nextvisitcontent,jdbcType=VARCHAR},
      </if>
      <if test="nextstarttime != null">
        NEXTSTARTTIME = #{nextstarttime,jdbcType=VARCHAR},
      </if>
      <if test="nextendtime != null">
        NEXTENDTIME = #{nextendtime,jdbcType=VARCHAR},
      </if>
      <if test="nextvisittype != null">
        NEXTVISITTYPE = #{nextvisittype,jdbcType=VARCHAR},
      </if>
      <if test="visitclassify != null">
        VISITCLASSIFY = #{visitclassify,jdbcType=VARCHAR},
      </if>
      <if test="remark != null">
        REMARK = #{remark,jdbcType=VARCHAR},
      </if>
      <if test="modifyflag != null">
        MODIFYFLAG = #{modifyflag,jdbcType=VARCHAR},
      </if>
      <if test="creator != null">
        CREATOR = #{creator,jdbcType=VARCHAR},
      </if>
      <if test="credt != null">
        CREDT = #{credt,jdbcType=TIMESTAMP},
      </if>
      <if test="modifier != null">
        MODIFIER = #{modifier,jdbcType=VARCHAR},
      </if>
      <if test="moddt != null">
        MODDT = #{moddt,jdbcType=TIMESTAMP},
      </if>
      <if test="stimestamp != null">
        STIMESTAMP = #{stimestamp,jdbcType=TIMESTAMP},
      </if>
      <if test="hisflag != null">
        HISFLAG = #{hisflag,jdbcType=VARCHAR},
      </if>
      <if test="hisid != null">
        HISID = #{hisid,jdbcType=VARCHAR},
      </if>
    </set>
    where ID = #{id,jdbcType=VARCHAR}
  </update>
  <update id="updateByPrimaryKey" parameterType="com.howbuy.crm.account.dao.po.commvisit.CsCommunicateVisitPO">
    <!--@mbg.generated-->
    update CS_COMMUNICATE_VISIT
    set CONSCUSTNO = #{conscustno,jdbcType=VARCHAR},
      CONSULTTYPE = #{consulttype,jdbcType=VARCHAR},
      COMMINTENT = #{commintent,jdbcType=VARCHAR},
      INVESTINTENT = #{investintent,jdbcType=VARCHAR},
      AMOUNTFLAG = #{amountflag,jdbcType=VARCHAR},
      SPECIALMARK = #{specialmark,jdbcType=VARCHAR},
      DEPTFLAG = #{deptflag,jdbcType=VARCHAR},
      COMMCONTENT = #{commcontent,jdbcType=VARCHAR},
      TASKID = #{taskid,jdbcType=VARCHAR},
      BOOKINGCONTENT = #{bookingcontent,jdbcType=VARCHAR},
      CALLINID = #{callinid,jdbcType=VARCHAR},
      VISITTYPE = #{visittype,jdbcType=VARCHAR},
      NEXTDT = #{nextdt,jdbcType=VARCHAR},
      CONSBOOKINGID = #{consbookingid,jdbcType=VARCHAR},
      NEXTVISITCONTENT = #{nextvisitcontent,jdbcType=VARCHAR},
      NEXTSTARTTIME = #{nextstarttime,jdbcType=VARCHAR},
      NEXTENDTIME = #{nextendtime,jdbcType=VARCHAR},
      NEXTVISITTYPE = #{nextvisittype,jdbcType=VARCHAR},
      VISITCLASSIFY = #{visitclassify,jdbcType=VARCHAR},
      REMARK = #{remark,jdbcType=VARCHAR},
      MODIFYFLAG = #{modifyflag,jdbcType=VARCHAR},
      CREATOR = #{creator,jdbcType=VARCHAR},
      CREDT = #{credt,jdbcType=TIMESTAMP},
      MODIFIER = #{modifier,jdbcType=VARCHAR},
      MODDT = #{moddt,jdbcType=TIMESTAMP},
      STIMESTAMP = #{stimestamp,jdbcType=TIMESTAMP},
      HISFLAG = #{hisflag,jdbcType=VARCHAR},
      HISID = #{hisid,jdbcType=VARCHAR}
    where ID = #{id,jdbcType=VARCHAR}
  </update>
</mapper>