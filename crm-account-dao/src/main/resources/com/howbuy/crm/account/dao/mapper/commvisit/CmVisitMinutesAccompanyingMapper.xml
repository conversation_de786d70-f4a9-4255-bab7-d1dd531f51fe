<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.howbuy.crm.account.dao.mapper.commvisit.CmVisitMinutesAccompanyingMapper">
  <resultMap id="BaseResultMap" type="com.howbuy.crm.account.dao.po.commvisit.CmVisitMinutesAccompanyingPO">
    <!--@mbg.generated-->
    <!--@Table CM_VISIT_MINUTES_ACCOMPANYING-->
    <id column="ID" jdbcType="VARCHAR" property="id" />
    <result column="VISIT_MINUTES_ID" jdbcType="VARCHAR" property="visitMinutesId" />
    <result column="ACCOMPANYING_TYPE" jdbcType="VARCHAR" property="accompanyingType" />
    <result column="ACCOMPANYING_USER_ID" jdbcType="VARCHAR" property="accompanyingUserId" />
    <result column="ACCOMPANYING_USER_LEVEL" jdbcType="VARCHAR" property="accompanyingUserLevel" />
    <result column="ACCOMPANY_SUMMARY" jdbcType="VARCHAR" property="accompanySummary" />
    <result column="ACCOMPANY_SUGGESTION" jdbcType="VARCHAR" property="accompanySuggestion" />
    <result column="FILL_TIME" jdbcType="TIMESTAMP" property="fillTime" />
    <result column="CREATOR" jdbcType="VARCHAR" property="creator" />
    <result column="CREATE_TIME" jdbcType="TIMESTAMP" property="createTime" />
    <result column="MODIFIER" jdbcType="VARCHAR" property="modifier" />
    <result column="MODIFY_TIME" jdbcType="TIMESTAMP" property="modifyTime" />
  </resultMap>
  <sql id="Base_Column_List">
    <!--@mbg.generated-->
    ID, VISIT_MINUTES_ID, ACCOMPANYING_TYPE, ACCOMPANYING_USER_ID, ACCOMPANYING_USER_LEVEL,
    ACCOMPANY_SUMMARY, ACCOMPANY_SUGGESTION, FILL_TIME, CREATOR, CREATE_TIME, MODIFIER,
    MODIFY_TIME
  </sql>
  <select id="selectByPrimaryKey" parameterType="java.lang.String" resultMap="BaseResultMap">
    <!--@mbg.generated-->
    select
    <include refid="Base_Column_List" />
    from CM_VISIT_MINUTES_ACCOMPANYING
    where ID = #{id,jdbcType=VARCHAR}
  </select>
  <delete id="deleteByPrimaryKey" parameterType="java.lang.String">
    <!--@mbg.generated-->
    delete from CM_VISIT_MINUTES_ACCOMPANYING
    where ID = #{id,jdbcType=VARCHAR}
  </delete>
  <insert id="insert" parameterType="com.howbuy.crm.account.dao.po.commvisit.CmVisitMinutesAccompanyingPO">
    <!--@mbg.generated-->
    insert into CM_VISIT_MINUTES_ACCOMPANYING (ID, VISIT_MINUTES_ID, ACCOMPANYING_TYPE,
      ACCOMPANYING_USER_ID, ACCOMPANYING_USER_LEVEL,
      ACCOMPANY_SUMMARY, ACCOMPANY_SUGGESTION,
      FILL_TIME, CREATOR, CREATE_TIME,
      MODIFIER, MODIFY_TIME)
    values (#{id,jdbcType=VARCHAR}, #{visitMinutesId,jdbcType=VARCHAR}, #{accompanyingType,jdbcType=VARCHAR},
      #{accompanyingUserId,jdbcType=VARCHAR}, #{accompanyingUserLevel,jdbcType=VARCHAR},
      #{accompanySummary,jdbcType=VARCHAR}, #{accompanySuggestion,jdbcType=VARCHAR},
      #{fillTime,jdbcType=TIMESTAMP}, #{creator,jdbcType=VARCHAR}, #{createTime,jdbcType=TIMESTAMP},
      #{modifier,jdbcType=VARCHAR}, #{modifyTime,jdbcType=TIMESTAMP})
  </insert>
  <insert id="insertSelective" parameterType="com.howbuy.crm.account.dao.po.commvisit.CmVisitMinutesAccompanyingPO">
    <!--@mbg.generated-->
    insert into CM_VISIT_MINUTES_ACCOMPANYING
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="id != null">
        ID,
      </if>
      <if test="visitMinutesId != null">
        VISIT_MINUTES_ID,
      </if>
      <if test="accompanyingType != null">
        ACCOMPANYING_TYPE,
      </if>
      <if test="accompanyingUserId != null">
        ACCOMPANYING_USER_ID,
      </if>
      <if test="accompanyingUserLevel != null">
        ACCOMPANYING_USER_LEVEL,
      </if>
      <if test="accompanySummary != null">
        ACCOMPANY_SUMMARY,
      </if>
      <if test="accompanySuggestion != null">
        ACCOMPANY_SUGGESTION,
      </if>
      <if test="fillTime != null">
        FILL_TIME,
      </if>
      <if test="creator != null">
        CREATOR,
      </if>
      <if test="createTime != null">
        CREATE_TIME,
      </if>
      <if test="modifier != null">
        MODIFIER,
      </if>
      <if test="modifyTime != null">
        MODIFY_TIME,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="id != null">
        #{id,jdbcType=VARCHAR},
      </if>
      <if test="visitMinutesId != null">
        #{visitMinutesId,jdbcType=VARCHAR},
      </if>
      <if test="accompanyingType != null">
        #{accompanyingType,jdbcType=VARCHAR},
      </if>
      <if test="accompanyingUserId != null">
        #{accompanyingUserId,jdbcType=VARCHAR},
      </if>
      <if test="accompanyingUserLevel != null">
        #{accompanyingUserLevel,jdbcType=VARCHAR},
      </if>
      <if test="accompanySummary != null">
        #{accompanySummary,jdbcType=VARCHAR},
      </if>
      <if test="accompanySuggestion != null">
        #{accompanySuggestion,jdbcType=VARCHAR},
      </if>
      <if test="fillTime != null">
        #{fillTime,jdbcType=TIMESTAMP},
      </if>
      <if test="creator != null">
        #{creator,jdbcType=VARCHAR},
      </if>
      <if test="createTime != null">
        #{createTime,jdbcType=TIMESTAMP},
      </if>
      <if test="modifier != null">
        #{modifier,jdbcType=VARCHAR},
      </if>
      <if test="modifyTime != null">
        #{modifyTime,jdbcType=TIMESTAMP},
      </if>
    </trim>
  </insert>
  <update id="updateByPrimaryKeySelective" parameterType="com.howbuy.crm.account.dao.po.commvisit.CmVisitMinutesAccompanyingPO">
    <!--@mbg.generated-->
    update CM_VISIT_MINUTES_ACCOMPANYING
    <set>
      <if test="visitMinutesId != null">
        VISIT_MINUTES_ID = #{visitMinutesId,jdbcType=VARCHAR},
      </if>
      <if test="accompanyingType != null">
        ACCOMPANYING_TYPE = #{accompanyingType,jdbcType=VARCHAR},
      </if>
      <if test="accompanyingUserId != null">
        ACCOMPANYING_USER_ID = #{accompanyingUserId,jdbcType=VARCHAR},
      </if>
      <if test="accompanyingUserLevel != null">
        ACCOMPANYING_USER_LEVEL = #{accompanyingUserLevel,jdbcType=VARCHAR},
      </if>
      <if test="accompanySummary != null">
        ACCOMPANY_SUMMARY = #{accompanySummary,jdbcType=VARCHAR},
      </if>
      <if test="accompanySuggestion != null">
        ACCOMPANY_SUGGESTION = #{accompanySuggestion,jdbcType=VARCHAR},
      </if>
      <if test="fillTime != null">
        FILL_TIME = #{fillTime,jdbcType=TIMESTAMP},
      </if>
      <if test="creator != null">
        CREATOR = #{creator,jdbcType=VARCHAR},
      </if>
      <if test="createTime != null">
        CREATE_TIME = #{createTime,jdbcType=TIMESTAMP},
      </if>
      <if test="modifier != null">
        MODIFIER = #{modifier,jdbcType=VARCHAR},
      </if>
      <if test="modifyTime != null">
        MODIFY_TIME = #{modifyTime,jdbcType=TIMESTAMP},
      </if>
    </set>
    where ID = #{id,jdbcType=VARCHAR}
  </update>
  <update id="updateByPrimaryKey" parameterType="com.howbuy.crm.account.dao.po.commvisit.CmVisitMinutesAccompanyingPO">
    <!--@mbg.generated-->
    update CM_VISIT_MINUTES_ACCOMPANYING
    set VISIT_MINUTES_ID = #{visitMinutesId,jdbcType=VARCHAR},
      ACCOMPANYING_TYPE = #{accompanyingType,jdbcType=VARCHAR},
      ACCOMPANYING_USER_ID = #{accompanyingUserId,jdbcType=VARCHAR},
      ACCOMPANYING_USER_LEVEL = #{accompanyingUserLevel,jdbcType=VARCHAR},
      ACCOMPANY_SUMMARY = #{accompanySummary,jdbcType=VARCHAR},
      ACCOMPANY_SUGGESTION = #{accompanySuggestion,jdbcType=VARCHAR},
      FILL_TIME = #{fillTime,jdbcType=TIMESTAMP},
      CREATOR = #{creator,jdbcType=VARCHAR},
      CREATE_TIME = #{createTime,jdbcType=TIMESTAMP},
      MODIFIER = #{modifier,jdbcType=VARCHAR},
      MODIFY_TIME = #{modifyTime,jdbcType=TIMESTAMP}
    where ID = #{id,jdbcType=VARCHAR}
  </update>

  <select id="selectByVisitMinutesId" resultMap="BaseResultMap">
    select
    <include refid="Base_Column_List" />
    from cm_visit_minutes_accompanying
    where visit_minutes_id = #{visitMinutesId}
  </select>

  <select id="selectById" resultMap="BaseResultMap">
    select
    <include refid="Base_Column_List" />
    from cm_visit_minutes_accompanying
    where id = #{id}
  </select>

  <update id="updateFeedback" parameterType="com.howbuy.crm.account.dao.po.commvisit.CmVisitMinutesAccompanyingPO">
    update cm_visit_minutes_accompanying
    set accompany_summary = #{accompanySummary,jdbcType=VARCHAR},
    accompany_suggestion = #{accompanySuggestion,jdbcType=VARCHAR},
    fill_time = #{fillTime,jdbcType=TIMESTAMP},
    modify_time = #{modifyTime,jdbcType=TIMESTAMP},
    modifier = #{modifier,jdbcType=VARCHAR}
    where id = #{id}
  </update>

  <delete id="deleteByVisitMinutesId">
    delete from cm_visit_minutes_accompanying
    where visit_minutes_id = #{visitMinutesId}
  </delete>

</mapper>