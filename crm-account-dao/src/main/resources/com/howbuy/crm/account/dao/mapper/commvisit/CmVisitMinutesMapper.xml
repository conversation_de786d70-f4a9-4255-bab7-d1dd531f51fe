<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.howbuy.crm.account.dao.mapper.commvisit.CmVisitMinutesMapper">
  <resultMap id="BaseResultMap" type="com.howbuy.crm.account.dao.po.commvisit.CmVisitMinutesPO">
    <!--@mbg.generated-->
    <!--@Table CM_VISIT_MINUTES-->
    <id column="ID" jdbcType="VARCHAR" property="id" />
    <result column="COMMUNICATE_ID" jdbcType="VARCHAR" property="communicateId" />
    <result column="CONS_CUST_NO" jdbcType="VARCHAR" property="consCustNo" />
    <result column="VISIT_PURPOSE" jdbcType="VARCHAR" property="visitPurpose" />
    <result column="VISIT_PURPOSE_OTHER" jdbcType="VARCHAR" property="visitPurposeOther" />
    <result column="ASSET_REPORT_ID" jdbcType="VARCHAR" property="assetReportId" />
    <result column="MARKET_VAL" jdbcType="VARCHAR" property="marketVal" />
    <result column="HEALTH_AVG_STAR" jdbcType="VARCHAR" property="healthAvgStar" />
    <result column="GIVE_INFORMATION" jdbcType="VARCHAR" property="giveInformation" />
    <result column="ATTEND_ROLE" jdbcType="VARCHAR" property="attendRole" />
    <result column="PRODUCT_SERVICE_FEEDBACK" jdbcType="VARCHAR" property="productServiceFeedback" />
    <result column="IPS_FEEDBACK" jdbcType="VARCHAR" property="ipsFeedback" />
    <result column="ADD_AMOUNT_RMB" jdbcType="VARCHAR" property="addAmountRmb" />
    <result column="ADD_AMOUNT_FOREIGN" jdbcType="VARCHAR" property="addAmountForeign" />
    <result column="FOCUS_ASSET" jdbcType="VARCHAR" property="focusAsset" />
    <result column="ESTIMATE_NEED_BUSINESS" jdbcType="VARCHAR" property="estimateNeedBusiness" />
    <result column="NEXT_PLAN" jdbcType="VARCHAR" property="nextPlan" />
    <result column="MANAGER_ID" jdbcType="VARCHAR" property="managerId" />
    <result column="MANAGER_SUGGESTION" jdbcType="VARCHAR" property="managerSuggestion" />
    <result column="MANAGER_SUMMARY" jdbcType="VARCHAR" property="managerSummary" />
    <result column="MANAGER_FILL_TIME" jdbcType="TIMESTAMP" property="managerFillTime" />
    <result column="CREATOR" jdbcType="VARCHAR" property="creator" />
    <result column="CREATE_TIME" jdbcType="TIMESTAMP" property="createTime" />
    <result column="MODIFIER" jdbcType="VARCHAR" property="modifier" />
    <result column="MODIFY_TIME" jdbcType="TIMESTAMP" property="modifyTime" />
    <result column="MANAGER_USER_LEVEL" jdbcType="VARCHAR" property="managerUserLevel" />
  </resultMap>
  <sql id="Base_Column_List">
    <!--@mbg.generated-->
    ID, COMMUNICATE_ID, CONS_CUST_NO, VISIT_PURPOSE, VISIT_PURPOSE_OTHER, ASSET_REPORT_ID, 
    MARKET_VAL, HEALTH_AVG_STAR, GIVE_INFORMATION, ATTEND_ROLE, PRODUCT_SERVICE_FEEDBACK, 
    IPS_FEEDBACK, ADD_AMOUNT_RMB, ADD_AMOUNT_FOREIGN, FOCUS_ASSET, ESTIMATE_NEED_BUSINESS, 
    NEXT_PLAN, MANAGER_ID, MANAGER_SUGGESTION, MANAGER_SUMMARY, MANAGER_FILL_TIME, CREATOR, 
    CREATE_TIME, MODIFIER, MODIFY_TIME, MANAGER_USER_LEVEL
  </sql>
  <select id="selectByPrimaryKey" parameterType="java.lang.String" resultMap="BaseResultMap">
    <!--@mbg.generated-->
    select 
    <include refid="Base_Column_List" />
    from CM_VISIT_MINUTES
    where ID = #{id,jdbcType=VARCHAR}
  </select>
  <delete id="deleteByPrimaryKey" parameterType="java.lang.String">
    <!--@mbg.generated-->
    delete from CM_VISIT_MINUTES
    where ID = #{id,jdbcType=VARCHAR}
  </delete>
  <insert id="insert" parameterType="com.howbuy.crm.account.dao.po.commvisit.CmVisitMinutesPO">
    <!--@mbg.generated-->
    insert into CM_VISIT_MINUTES (ID, COMMUNICATE_ID, CONS_CUST_NO, 
      VISIT_PURPOSE, VISIT_PURPOSE_OTHER, ASSET_REPORT_ID, 
      MARKET_VAL, HEALTH_AVG_STAR, GIVE_INFORMATION, 
      ATTEND_ROLE, PRODUCT_SERVICE_FEEDBACK, IPS_FEEDBACK, 
      ADD_AMOUNT_RMB, ADD_AMOUNT_FOREIGN, FOCUS_ASSET, 
      ESTIMATE_NEED_BUSINESS, NEXT_PLAN, MANAGER_ID, 
      MANAGER_SUGGESTION, MANAGER_SUMMARY, MANAGER_FILL_TIME, 
      CREATOR, CREATE_TIME, MODIFIER, 
      MODIFY_TIME, MANAGER_USER_LEVEL)
    values (#{id,jdbcType=VARCHAR}, #{communicateId,jdbcType=VARCHAR}, #{consCustNo,jdbcType=VARCHAR}, 
      #{visitPurpose,jdbcType=VARCHAR}, #{visitPurposeOther,jdbcType=VARCHAR}, #{assetReportId,jdbcType=VARCHAR}, 
      #{marketVal,jdbcType=VARCHAR}, #{healthAvgStar,jdbcType=VARCHAR}, #{giveInformation,jdbcType=VARCHAR}, 
      #{attendRole,jdbcType=VARCHAR}, #{productServiceFeedback,jdbcType=VARCHAR}, #{ipsFeedback,jdbcType=VARCHAR}, 
      #{addAmountRmb,jdbcType=VARCHAR}, #{addAmountForeign,jdbcType=VARCHAR}, #{focusAsset,jdbcType=VARCHAR}, 
      #{estimateNeedBusiness,jdbcType=VARCHAR}, #{nextPlan,jdbcType=VARCHAR}, #{managerId,jdbcType=VARCHAR}, 
      #{managerSuggestion,jdbcType=VARCHAR}, #{managerSummary,jdbcType=VARCHAR}, #{managerFillTime,jdbcType=TIMESTAMP}, 
      #{creator,jdbcType=VARCHAR}, #{createTime,jdbcType=TIMESTAMP}, #{modifier,jdbcType=VARCHAR}, 
      #{modifyTime,jdbcType=TIMESTAMP}, #{managerUserLevel,jdbcType=VARCHAR})
  </insert>
  <insert id="insertSelective" parameterType="com.howbuy.crm.account.dao.po.commvisit.CmVisitMinutesPO">
    <!--@mbg.generated-->
    insert into CM_VISIT_MINUTES
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="id != null">
        ID,
      </if>
      <if test="communicateId != null">
        COMMUNICATE_ID,
      </if>
      <if test="consCustNo != null">
        CONS_CUST_NO,
      </if>
      <if test="visitPurpose != null">
        VISIT_PURPOSE,
      </if>
      <if test="visitPurposeOther != null">
        VISIT_PURPOSE_OTHER,
      </if>
      <if test="assetReportId != null">
        ASSET_REPORT_ID,
      </if>
      <if test="marketVal != null">
        MARKET_VAL,
      </if>
      <if test="healthAvgStar != null">
        HEALTH_AVG_STAR,
      </if>
      <if test="giveInformation != null">
        GIVE_INFORMATION,
      </if>
      <if test="attendRole != null">
        ATTEND_ROLE,
      </if>
      <if test="productServiceFeedback != null">
        PRODUCT_SERVICE_FEEDBACK,
      </if>
      <if test="ipsFeedback != null">
        IPS_FEEDBACK,
      </if>
      <if test="addAmountRmb != null">
        ADD_AMOUNT_RMB,
      </if>
      <if test="addAmountForeign != null">
        ADD_AMOUNT_FOREIGN,
      </if>
      <if test="focusAsset != null">
        FOCUS_ASSET,
      </if>
      <if test="estimateNeedBusiness != null">
        ESTIMATE_NEED_BUSINESS,
      </if>
      <if test="nextPlan != null">
        NEXT_PLAN,
      </if>
      <if test="managerId != null">
        MANAGER_ID,
      </if>
      <if test="managerSuggestion != null">
        MANAGER_SUGGESTION,
      </if>
      <if test="managerSummary != null">
        MANAGER_SUMMARY,
      </if>
      <if test="managerFillTime != null">
        MANAGER_FILL_TIME,
      </if>
      <if test="creator != null">
        CREATOR,
      </if>
      <if test="createTime != null">
        CREATE_TIME,
      </if>
      <if test="modifier != null">
        MODIFIER,
      </if>
      <if test="modifyTime != null">
        MODIFY_TIME,
      </if>
      <if test="managerUserLevel != null">
        MANAGER_USER_LEVEL,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="id != null">
        #{id,jdbcType=VARCHAR},
      </if>
      <if test="communicateId != null">
        #{communicateId,jdbcType=VARCHAR},
      </if>
      <if test="consCustNo != null">
        #{consCustNo,jdbcType=VARCHAR},
      </if>
      <if test="visitPurpose != null">
        #{visitPurpose,jdbcType=VARCHAR},
      </if>
      <if test="visitPurposeOther != null">
        #{visitPurposeOther,jdbcType=VARCHAR},
      </if>
      <if test="assetReportId != null">
        #{assetReportId,jdbcType=VARCHAR},
      </if>
      <if test="marketVal != null">
        #{marketVal,jdbcType=VARCHAR},
      </if>
      <if test="healthAvgStar != null">
        #{healthAvgStar,jdbcType=VARCHAR},
      </if>
      <if test="giveInformation != null">
        #{giveInformation,jdbcType=VARCHAR},
      </if>
      <if test="attendRole != null">
        #{attendRole,jdbcType=VARCHAR},
      </if>
      <if test="productServiceFeedback != null">
        #{productServiceFeedback,jdbcType=VARCHAR},
      </if>
      <if test="ipsFeedback != null">
        #{ipsFeedback,jdbcType=VARCHAR},
      </if>
      <if test="addAmountRmb != null">
        #{addAmountRmb,jdbcType=VARCHAR},
      </if>
      <if test="addAmountForeign != null">
        #{addAmountForeign,jdbcType=VARCHAR},
      </if>
      <if test="focusAsset != null">
        #{focusAsset,jdbcType=VARCHAR},
      </if>
      <if test="estimateNeedBusiness != null">
        #{estimateNeedBusiness,jdbcType=VARCHAR},
      </if>
      <if test="nextPlan != null">
        #{nextPlan,jdbcType=VARCHAR},
      </if>
      <if test="managerId != null">
        #{managerId,jdbcType=VARCHAR},
      </if>
      <if test="managerSuggestion != null">
        #{managerSuggestion,jdbcType=VARCHAR},
      </if>
      <if test="managerSummary != null">
        #{managerSummary,jdbcType=VARCHAR},
      </if>
      <if test="managerFillTime != null">
        #{managerFillTime,jdbcType=TIMESTAMP},
      </if>
      <if test="creator != null">
        #{creator,jdbcType=VARCHAR},
      </if>
      <if test="createTime != null">
        #{createTime,jdbcType=TIMESTAMP},
      </if>
      <if test="modifier != null">
        #{modifier,jdbcType=VARCHAR},
      </if>
      <if test="modifyTime != null">
        #{modifyTime,jdbcType=TIMESTAMP},
      </if>
      <if test="managerUserLevel != null">
        #{managerUserLevel,jdbcType=VARCHAR},
      </if>
    </trim>
  </insert>
  <update id="updateByPrimaryKeySelective" parameterType="com.howbuy.crm.account.dao.po.commvisit.CmVisitMinutesPO">
    <!--@mbg.generated-->
    update CM_VISIT_MINUTES
    <set>
      <if test="communicateId != null">
        COMMUNICATE_ID = #{communicateId,jdbcType=VARCHAR},
      </if>
      <if test="consCustNo != null">
        CONS_CUST_NO = #{consCustNo,jdbcType=VARCHAR},
      </if>
      <if test="visitPurpose != null">
        VISIT_PURPOSE = #{visitPurpose,jdbcType=VARCHAR},
      </if>
      <if test="visitPurposeOther != null">
        VISIT_PURPOSE_OTHER = #{visitPurposeOther,jdbcType=VARCHAR},
      </if>
      <if test="assetReportId != null">
        ASSET_REPORT_ID = #{assetReportId,jdbcType=VARCHAR},
      </if>
      <if test="marketVal != null">
        MARKET_VAL = #{marketVal,jdbcType=VARCHAR},
      </if>
      <if test="healthAvgStar != null">
        HEALTH_AVG_STAR = #{healthAvgStar,jdbcType=VARCHAR},
      </if>
      <if test="giveInformation != null">
        GIVE_INFORMATION = #{giveInformation,jdbcType=VARCHAR},
      </if>
      <if test="attendRole != null">
        ATTEND_ROLE = #{attendRole,jdbcType=VARCHAR},
      </if>
      <if test="productServiceFeedback != null">
        PRODUCT_SERVICE_FEEDBACK = #{productServiceFeedback,jdbcType=VARCHAR},
      </if>
      <if test="ipsFeedback != null">
        IPS_FEEDBACK = #{ipsFeedback,jdbcType=VARCHAR},
      </if>
      <if test="addAmountRmb != null">
        ADD_AMOUNT_RMB = #{addAmountRmb,jdbcType=VARCHAR},
      </if>
      <if test="addAmountForeign != null">
        ADD_AMOUNT_FOREIGN = #{addAmountForeign,jdbcType=VARCHAR},
      </if>
      <if test="focusAsset != null">
        FOCUS_ASSET = #{focusAsset,jdbcType=VARCHAR},
      </if>
      <if test="estimateNeedBusiness != null">
        ESTIMATE_NEED_BUSINESS = #{estimateNeedBusiness,jdbcType=VARCHAR},
      </if>
      <if test="nextPlan != null">
        NEXT_PLAN = #{nextPlan,jdbcType=VARCHAR},
      </if>
      <if test="managerId != null">
        MANAGER_ID = #{managerId,jdbcType=VARCHAR},
      </if>
      <if test="managerSuggestion != null">
        MANAGER_SUGGESTION = #{managerSuggestion,jdbcType=VARCHAR},
      </if>
      <if test="managerSummary != null">
        MANAGER_SUMMARY = #{managerSummary,jdbcType=VARCHAR},
      </if>
      <if test="managerFillTime != null">
        MANAGER_FILL_TIME = #{managerFillTime,jdbcType=TIMESTAMP},
      </if>
      <if test="creator != null">
        CREATOR = #{creator,jdbcType=VARCHAR},
      </if>
      <if test="createTime != null">
        CREATE_TIME = #{createTime,jdbcType=TIMESTAMP},
      </if>
      <if test="modifier != null">
        MODIFIER = #{modifier,jdbcType=VARCHAR},
      </if>
      <if test="modifyTime != null">
        MODIFY_TIME = #{modifyTime,jdbcType=TIMESTAMP},
      </if>
      <if test="managerUserLevel != null">
        MANAGER_USER_LEVEL = #{managerUserLevel,jdbcType=VARCHAR},
      </if>
    </set>
    where ID = #{id,jdbcType=VARCHAR}
  </update>
  <update id="updateByPrimaryKey" parameterType="com.howbuy.crm.account.dao.po.commvisit.CmVisitMinutesPO">
    <!--@mbg.generated-->
    update CM_VISIT_MINUTES
    set COMMUNICATE_ID = #{communicateId,jdbcType=VARCHAR},
      CONS_CUST_NO = #{consCustNo,jdbcType=VARCHAR},
      VISIT_PURPOSE = #{visitPurpose,jdbcType=VARCHAR},
      VISIT_PURPOSE_OTHER = #{visitPurposeOther,jdbcType=VARCHAR},
      ASSET_REPORT_ID = #{assetReportId,jdbcType=VARCHAR},
      MARKET_VAL = #{marketVal,jdbcType=VARCHAR},
      HEALTH_AVG_STAR = #{healthAvgStar,jdbcType=VARCHAR},
      GIVE_INFORMATION = #{giveInformation,jdbcType=VARCHAR},
      ATTEND_ROLE = #{attendRole,jdbcType=VARCHAR},
      PRODUCT_SERVICE_FEEDBACK = #{productServiceFeedback,jdbcType=VARCHAR},
      IPS_FEEDBACK = #{ipsFeedback,jdbcType=VARCHAR},
      ADD_AMOUNT_RMB = #{addAmountRmb,jdbcType=VARCHAR},
      ADD_AMOUNT_FOREIGN = #{addAmountForeign,jdbcType=VARCHAR},
      FOCUS_ASSET = #{focusAsset,jdbcType=VARCHAR},
      ESTIMATE_NEED_BUSINESS = #{estimateNeedBusiness,jdbcType=VARCHAR},
      NEXT_PLAN = #{nextPlan,jdbcType=VARCHAR},
      MANAGER_ID = #{managerId,jdbcType=VARCHAR},
      MANAGER_SUGGESTION = #{managerSuggestion,jdbcType=VARCHAR},
      MANAGER_SUMMARY = #{managerSummary,jdbcType=VARCHAR},
      MANAGER_FILL_TIME = #{managerFillTime,jdbcType=TIMESTAMP},
      CREATOR = #{creator,jdbcType=VARCHAR},
      CREATE_TIME = #{createTime,jdbcType=TIMESTAMP},
      MODIFIER = #{modifier,jdbcType=VARCHAR},
      MODIFY_TIME = #{modifyTime,jdbcType=TIMESTAMP},
      MANAGER_USER_LEVEL = #{managerUserLevel,jdbcType=VARCHAR}
    where ID = #{id,jdbcType=VARCHAR}
  </update>


  <update id="updateMinutesById" parameterType="com.howbuy.crm.account.dao.po.commvisit.CmVisitMinutesPO">
    update CM_VISIT_MINUTES
    <set>
      <if test="attendRole != null">
        ATTEND_ROLE = #{attendRole,jdbcType=VARCHAR},
      </if>
      <if test="productServiceFeedback != null">
        PRODUCT_SERVICE_FEEDBACK = #{productServiceFeedback,jdbcType=VARCHAR},
      </if>
      <if test="ipsFeedback != null">
        IPS_FEEDBACK = #{ipsFeedback,jdbcType=VARCHAR},
      </if>
      <if test="addAmountRmb != null">
        ADD_AMOUNT_RMB = #{addAmountRmb,jdbcType=VARCHAR},
      </if>
      <if test="addAmountForeign != null">
        ADD_AMOUNT_FOREIGN = #{addAmountForeign,jdbcType=VARCHAR},
      </if>
      <if test="focusAsset != null">
        FOCUS_ASSET = #{focusAsset,jdbcType=VARCHAR},
      </if>
      <if test="estimateNeedBusiness != null">
        ESTIMATE_NEED_BUSINESS = #{estimateNeedBusiness,jdbcType=VARCHAR},
      </if>
      <if test="nextPlan != null">
        NEXT_PLAN = #{nextPlan,jdbcType=VARCHAR},
      </if>
      <if test="modifier != null">
        MODIFIER = #{modifier,jdbcType=VARCHAR},
      </if>
      <if test="modifyTime != null">
        MODIFY_TIME = #{modifyTime,jdbcType=TIMESTAMP},
      </if>
    </set>
    where ID = #{id,jdbcType=VARCHAR}
  </update>

  <resultMap id="ListBoResultMap" type="com.howbuy.crm.account.dao.bo.commvisit.CmVisitMinutesBO" extends="BaseResultMap">
    <result property="visitDt" column="visit_date"/>
    <result property="custName" column="custname"/>
    <result property="orgCode" column="outletcode"/>
    <result property="visitType" column="visittype"/>
    <result property="visitTypeName" column="constdesc"/>
    <collection property="accompanyingUserList"
                select="com.howbuy.crm.account.dao.mapper.commvisit.CmVisitMinutesAccompanyingMapper.selectByVisitMinutesId"
                column="id">
    </collection>
  </resultMap>

  <select id="getFeedbackCountById" resultType="int">
    select
    count(1)
    from CM_VISIT_MINUTES
    where
    MANAGER_FILL_TIME is not null
    and ID in <foreach collection="ids" item="id" open="(" separator="," close=")">
      #{id}
    </foreach>
  </select>

  <select id="getVisitMinutesList" resultMap="ListBoResultMap" 
  parameterType="com.howbuy.crm.account.dao.dto.commvisit.VisitMinutesQueryDTO">
    SELECT
    m.id,
    m.create_time,
    t1.visit_date,
    m.visit_purpose,
    m.visit_purpose_other,
    m.cons_cust_no,
    t2.custname,
    t6.outletcode,
    m.creator,
    t1.visittype,
    constant.constdesc,
    m.market_val,
    m.health_avg_star,
    m.give_information,
    m.attend_role,
    m.product_service_feedback,
    m.ips_feedback,
    m.ADD_AMOUNT_RMB,
    m.ADD_AMOUNT_FOREIGN,
    m.focus_asset,
    m.estimate_need_business,
    m.next_plan,
    m.manager_id,
    m.manager_summary,
    m.manager_suggestion
    FROM cm_visit_minutes m
    left join CS_COMMUNICATE_VISIT t1
    on m.communicate_id = t1.id
    left join CM_CONSCUST t2
    on m.cons_cust_no = t2.conscustno
    left join cm_custconstant t4
    on m.cons_cust_no = t4.custno
    left join cm_consultant t5
    on t4.conscode = t5.conscode
    left join cm_consultant t6
    on m.creator = t6.conscode
    left join hb_constant constant
    on t1.visittype = constant.constcode
    and constant.typecode = 'new_visitType'
    WHERE 1=1
    <if test="query.orgCode != null and query.orgCode != ''">
      AND t5.outletcode in (
        SELECT T.ORGCODE
        FROM HB_ORGANIZATION T
        WHERE T.STATUS = 0
        START WITH T.ORGCODE = #{query.orgCode}
        CONNECT BY PRIOR T.ORGCODE = T.PARENTORGCODE
      )
    </if>
    <if test="query.consCode != null and query.consCode != ''">
      AND t5.conscode = #{query.consCode}
    </if>
    <if test="query.visitDateStart != null and query.visitDateStart != ''">
      AND t1.VISIT_DATE >= #{query.visitDateStart}
    </if>
    <if test="query.visitDateEnd != null and query.visitDateEnd != ''">
      AND t1.VISIT_DATE &lt;= #{query.visitDateEnd}
    </if>
    <if test="query.createDateStart != null and query.createDateStart != ''">
      AND m.create_time >= TO_DATE(#{query.createDateStart}, 'YYYYMMDD')
    </if>
    <if test="query.createDateEnd != null and query.createDateEnd != ''">
      AND m.create_time &lt;= TO_DATE(#{query.createDateEnd}, 'YYYYMMDD') + 1
    </if>
    <if test="query.custName != null and query.custName != ''">
      AND t2.CUSTNAME = #{query.custName}
    </if>
    <if test="query.visitPurpose != null and query.visitPurpose.size() > 0">
      AND (
      <foreach collection="query.visitPurpose" item="purpose" separator="OR">
        REGEXP_LIKE(m.visit_purpose, '(^|,)' || #{purpose} || '(,|$)')
      </foreach>
      )
    </if>
    <if test="query.consCustNo != null and query.consCustNo != ''">
      AND m.cons_cust_no = #{query.consCustNo}
    </if>
    <if test="query.accompanyingUserIdList != null and query.accompanyingUserIdList.size() != 0">
      AND exists (select 1 from CM_VISIT_MINUTES_ACCOMPANYING t3
            where t3.visit_minutes_id = m.id
                    and t3.ACCOMPANYING_USER_ID in
                    <foreach collection="query.accompanyingUserIdList" item="userId" separator="," open="(" close=")">
                      #{userId}
                    </foreach>
                    )
    </if>
    <if test="query.managerUserIdList != null and query.managerUserIdList.size() != 0">
      AND m.manager_id in
      <foreach collection="query.managerUserIdList" item="userId" separator="," open="(" close=")">
        #{userId}
      </foreach>
    </if>
    <if test="query.feedbackStatus != null and query.feedbackStatus.size() > 0">
      AND (
      <foreach collection="query.feedbackStatus" item="type" separator=" OR ">
            <if test="type == '1'.toString()">
                <!-- 不存在陪访人反馈 -->
              (
                not exists (select 1 from
                CM_VISIT_MINUTES_ACCOMPANYING
                where visit_minutes_id = m.id
                and accompany_suggestion is not null
                )
                and exists (
                select 1 from
                CM_VISIT_MINUTES_ACCOMPANYING
                where visit_minutes_id = m.id
                and ACCOMPANYING_TYPE != '4'
                )
              )
              or not exists (
                select 1 from
                CM_VISIT_MINUTES_ACCOMPANYING
                where visit_minutes_id = m.id
              )
            </if>
        <if test="type == '2'.toString() ">
           m.manager_suggestion is null
        </if>
        <if test="type == '3'.toString() ">
          <!-- 存在陪访人反馈 
          1）陪访人类型唯一，且【陪访人类型=其他】
          2）任意陪访人，填写了陪访人的“该客户下阶段工作的建议” 
          -->
          exists (select 1 from
          CM_VISIT_MINUTES_ACCOMPANYING
          where visit_minutes_id = m.id
          and accompany_suggestion is not null
          )
          or (not exists (
          select 1 from
          CM_VISIT_MINUTES_ACCOMPANYING
          where visit_minutes_id = m.id
          and ACCOMPANYING_TYPE != '4'
          )
          and exists (
          select 1 from
          CM_VISIT_MINUTES_ACCOMPANYING
          where visit_minutes_id = m.id
          )
          )
        </if>
        <if test="type == '4'.toString() ">
           m.manager_suggestion is not null
        </if>
      </foreach>
      )
    </if>
    ORDER BY m.create_time DESC, t1.visit_date DESC
  </select>

  <select id="getVisitMinutesById" resultMap="ListBoResultMap">
    select
    m.id,
    m.create_time,
    t1.visit_date,
    m.visit_purpose,
    m.visit_purpose_other,
    m.cons_cust_no,
    t2.custname,
    m.creator,
    t1.visittype,
    constant.constdesc,
    m.market_val,
    m.health_avg_star,
    m.give_information,
    m.attend_role,
    m.product_service_feedback,
    m.ips_feedback,
    m.asset_report_id,
    m.ADD_AMOUNT_RMB,
    m.ADD_AMOUNT_FOREIGN,
    m.focus_asset,
    m.estimate_need_business,
    m.next_plan,
    m.manager_id,
    m.manager_summary,
    m.manager_suggestion
    FROM cm_visit_minutes m
    left join CS_COMMUNICATE_VISIT t1
    on m.communicate_id = t1.id
    left join CM_CONSCUST t2
    on m.cons_cust_no = t2.conscustno
    left join hb_constant constant
    on t1.visittype = constant.constcode
    and constant.typecode = 'new_visitType'
    where m.ID = #{id,jdbcType=VARCHAR}
  </select>

  <!-- 更新主管信息 -->
  <update id="updateManager">
    UPDATE cm_visit_minutes
    <set>
        manager_id = #{managerId,jdbcType=VARCHAR},
      manager_user_Level = #{managerLevel,jdbcType=VARCHAR},
      <if test="userId != null">
        modifier = #{userId},
      </if>
      modify_time = sysdate
    </set>
    WHERE id in
    <foreach collection="ids" item="id" separator="," open="(" close=")">
      #{id}
    </foreach>
  </update>

  <!-- 清空主管反馈信息 -->
  <update id="clearManagerFeedback">
    UPDATE cm_visit_minutes
    SET manager_summary = NULL,
    manager_suggestion = NULL,
    manager_fill_time = NULL,
    modify_time = sysdate
    WHERE id = #{id}
  </update>

  <!-- 更新主管反馈信息 -->
  <update id="updateManagerFeedback" parameterType="com.howbuy.crm.account.dao.po.commvisit.CmVisitMinutesPO">
    UPDATE cm_visit_minutes
    <set>
      <if test="managerSummary != null">
        manager_summary = #{managerSummary},
      </if>
      <if test="managerSuggestion != null">
        manager_suggestion = #{managerSuggestion},
      </if>
      <if test="managerFillTime != null">
      manager_fill_time = #{managerFillTime,jdbcType=TIMESTAMP},
      </if>
      modify_time = sysdate,
      modifier = #{modifier}
    </set>
    WHERE id = #{id}
  </update>

  <select id="getMenuName" resultType="string">
    select menu_name from hb_menu where menu_code = #{menuCode,jdbcType=VARCHAR}
  </select>

  <select id="fetchNoFeedbackList" resultType="com.howbuy.crm.account.dao.bo.commvisit.NoFeedbackMsgBO">
    select t1.id as id,
    t1.VISIT_PURPOSE as visitPurpose,
    t1.CONS_CUST_NO as consCustNo,
    t4.CONSNAME as consName,
    t3.CUSTNAME as custName,
    t1.MANAGER_ID as consCode,
    t1.CREATE_TIME as createTime
    from CM_VISIT_MINUTES t1
       left join CM_CONSCUST t3 on t1.CONS_CUST_NO = t3.CONSCUSTNO
       left join CM_CONSULTANT t4 on t1.CREATOR = t4.CONSCODE
    where MANAGER_ID is not null and manager_suggestion is null
      and to_char(t1.CREATE_TIME,'yyyymmdd') = #{curPreDay,jdbcType=VARCHAR}
    union
    SELECT
    t1.id as id,
    t1.VISIT_PURPOSE as visitPurpose,
    t1.CONS_CUST_NO as consCustNo,
    t4.CONSNAME as consName,
    t3.CUSTNAME as custName,
    t2.ACCOMPANYING_USER_ID as consCode,
    t1.CREATE_TIME as createTime
    FROM cm_visit_minutes t1
    INNER JOIN cm_visit_minutes_accompanying t2 ON t1.id = t2.visit_minutes_id
    LEFT JOIN CM_CONSCUST t3 ON t1.CONS_CUST_NO = t3.CONSCUSTNO
    LEFT JOIN CM_CONSULTANT t4 ON t1.CREATOR = t4.CONSCODE
    WHERE
    t2.ACCOMPANYING_USER_ID is not null
    and t2.ACCOMPANYING_TYPE != #{notPushAccompanyingType,jdbcType=VARCHAR}
    AND to_char(t1.CREATE_TIME,'yyyymmdd') = #{curPreDay,jdbcType=VARCHAR}
    AND t1.id IN (
    SELECT visit_minutes_id
    FROM cm_visit_minutes_accompanying
    GROUP BY visit_minutes_id
    HAVING MAX(CASE WHEN ACCOMPANY_SUGGESTION IS NOT NULL THEN 1 ELSE 0 END) = 0
    )
  </select>
</mapper>