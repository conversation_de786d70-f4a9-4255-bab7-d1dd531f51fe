<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.howbuy.crm.account.dao.mapper.consultant.CmConsPerformanceCoeffMapper">



    <!-- 新增人员绩效系数表记录 -->
    <insert id="insert" parameterType="com.howbuy.crm.account.dao.po.consultant.CmConsPerformanceCoeffPO">
        INSERT INTO CM_CONS_PERFORMANCE_COEFF (
            CONS_CUST_NO, CONSCODE, ASSIGN_TIME, SOURCE_TYPE, COMMISSION_COEFF_START,
            CUST_CONVERSION_COEFF, MANAGE_COEFF_SUBTOTAL, MANAGE_COEFF_REGIONALSUBTOTAL,
            MANAGE_COEFF_REGIONALTOTAL, CXB, IS_BIG_V, CENTER_CODE, REG<PERSON>_CODE,
            REG<PERSON><PERSON>SUBTOTAL, OUTLET_CODE, CREATOR, CREDT, MODDT
        ) VALUES (
            #{consCustNo,jdbcType=VARCHAR}, #{conscode,jdbcType=VARCHAR}, #{assignTime,jdbcType=TIMESTAMP},
            #{sourceType,jdbcType=VARCHAR}, #{commissionCoeffStart,jdbcType=VARCHAR},
            #{custConversionCoeff,jdbcType=DECIMAL}, #{manageCoeffSubtotal,jdbcType=DECIMAL},
            #{manageCoeffRegionalsubtotal,jdbcType=DECIMAL}, #{manageCoeffRegionaltotal,jdbcType=DECIMAL},
            #{cxb,jdbcType=VARCHAR}, #{isBigV,jdbcType=VARCHAR}, #{centerCode,jdbcType=VARCHAR},
            #{regionCode,jdbcType=VARCHAR}, #{regionalsubtotal,jdbcType=VARCHAR},
            #{outletCode,jdbcType=VARCHAR}, #{creator,jdbcType=VARCHAR},
            #{credt,jdbcType=TIMESTAMP}, #{moddt,jdbcType=TIMESTAMP}
        )
    </insert>

    <!-- 选择性新增人员绩效系数表记录 -->
    <insert id="insertSelective" parameterType="com.howbuy.crm.account.dao.po.consultant.CmConsPerformanceCoeffPO">
        INSERT INTO CM_CONS_PERFORMANCE_COEFF
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="consCustNo != null">CONS_CUST_NO,</if>
            <if test="conscode != null">CONSCODE,</if>
            <if test="assignTime != null">ASSIGN_TIME,</if>
            <if test="sourceType != null">SOURCE_TYPE,</if>
            <if test="commissionCoeffStart != null">COMMISSION_COEFF_START,</if>
            <if test="custConversionCoeff != null">CUST_CONVERSION_COEFF,</if>
            <if test="manageCoeffSubtotal != null">MANAGE_COEFF_SUBTOTAL,</if>
            <if test="manageCoeffRegionalsubtotal != null">MANAGE_COEFF_REGIONALSUBTOTAL,</if>
            <if test="manageCoeffRegionaltotal != null">MANAGE_COEFF_REGIONALTOTAL,</if>
            <if test="cxb != null">CXB,</if>
            <if test="isBigV != null">IS_BIG_V,</if>
            <if test="centerCode != null">CENTER_CODE,</if>
            <if test="regionCode != null">REGION_CODE,</if>
            <if test="regionalsubtotal != null">REGIONALSUBTOTAL,</if>
            <if test="outletCode != null">OUTLET_CODE,</if>
            <if test="creator != null">CREATOR,</if>
            <if test="modifier != null">MODIFIER,</if>
            <if test="credt != null">CREDT,</if>
            <if test="moddt != null">MODDT,</if>
        </trim>
        <trim prefix="VALUES (" suffix=")" suffixOverrides=",">
            <if test="consCustNo != null">#{consCustNo,jdbcType=VARCHAR},</if>
            <if test="conscode != null">#{conscode,jdbcType=VARCHAR},</if>
            <if test="assignTime != null">#{assignTime,jdbcType=TIMESTAMP},</if>
            <if test="sourceType != null">#{sourceType,jdbcType=VARCHAR},</if>
            <if test="commissionCoeffStart != null">#{commissionCoeffStart,jdbcType=VARCHAR},</if>
            <if test="custConversionCoeff != null">#{custConversionCoeff,jdbcType=DECIMAL},</if>
            <if test="manageCoeffSubtotal != null">#{manageCoeffSubtotal,jdbcType=DECIMAL},</if>
            <if test="manageCoeffRegionalsubtotal != null">#{manageCoeffRegionalsubtotal,jdbcType=DECIMAL},</if>
            <if test="manageCoeffRegionaltotal != null">#{manageCoeffRegionaltotal,jdbcType=DECIMAL},</if>
            <if test="cxb != null">#{cxb,jdbcType=VARCHAR},</if>
            <if test="isBigV != null">#{isBigV,jdbcType=VARCHAR},</if>
            <if test="centerCode != null">#{centerCode,jdbcType=VARCHAR},</if>
            <if test="regionCode != null">#{regionCode,jdbcType=VARCHAR},</if>
            <if test="regionalsubtotal != null">#{regionalsubtotal,jdbcType=VARCHAR},</if>
            <if test="outletCode != null">#{outletCode,jdbcType=VARCHAR},</if>
            <if test="creator != null">#{creator,jdbcType=VARCHAR},</if>
            <if test="modifier != null">#{modifier,jdbcType=VARCHAR},</if>
            <if test="credt != null">#{credt,jdbcType=TIMESTAMP},</if>
            <if test="moddt != null">#{moddt,jdbcType=TIMESTAMP},</if>
        </trim>
    </insert>

    <!-- 根据主键更新人员绩效系数表记录 -->
    <update id="updateByPrimaryKey" parameterType="com.howbuy.crm.account.dao.po.consultant.CmConsPerformanceCoeffPO">
        UPDATE CM_CONS_PERFORMANCE_COEFF
        SET SOURCE_TYPE = #{sourceType,jdbcType=VARCHAR},
            COMMISSION_COEFF_START = #{commissionCoeffStart,jdbcType=VARCHAR},
            CUST_CONVERSION_COEFF = #{custConversionCoeff,jdbcType=DECIMAL},
            MANAGE_COEFF_SUBTOTAL = #{manageCoeffSubtotal,jdbcType=DECIMAL},
            MANAGE_COEFF_REGIONALSUBTOTAL = #{manageCoeffRegionalsubtotal,jdbcType=DECIMAL},
            MANAGE_COEFF_REGIONALTOTAL = #{manageCoeffRegionaltotal,jdbcType=DECIMAL},
            CXB = #{cxb,jdbcType=VARCHAR},
            IS_BIG_V = #{isBigV,jdbcType=VARCHAR},
            CENTER_CODE = #{centerCode,jdbcType=VARCHAR},
            REGION_CODE = #{regionCode,jdbcType=VARCHAR},
            REGIONALSUBTOTAL = #{regionalsubtotal,jdbcType=VARCHAR},
            OUTLET_CODE = #{outletCode,jdbcType=VARCHAR},
            MODIFIER = #{modifier,jdbcType=VARCHAR},
            MODDT = #{moddt,jdbcType=TIMESTAMP}
        WHERE CONS_CUST_NO = #{consCustNo,jdbcType=VARCHAR}
          AND CONSCODE = #{conscode,jdbcType=VARCHAR}
          AND ASSIGN_TIME = #{assignTime,jdbcType=TIMESTAMP}
    </update>

    <!-- 根据主键选择性更新人员绩效系数表记录 -->
    <update id="updateByPrimaryKeySelective" parameterType="com.howbuy.crm.account.dao.po.consultant.CmConsPerformanceCoeffPO">
        UPDATE CM_CONS_PERFORMANCE_COEFF
        <set>
            <if test="sourceType != null">SOURCE_TYPE = #{sourceType,jdbcType=VARCHAR},</if>
            <if test="commissionCoeffStart != null">COMMISSION_COEFF_START = #{commissionCoeffStart,jdbcType=VARCHAR},</if>
            <if test="custConversionCoeff != null">CUST_CONVERSION_COEFF = #{custConversionCoeff,jdbcType=DECIMAL},</if>
            <if test="manageCoeffSubtotal != null">MANAGE_COEFF_SUBTOTAL = #{manageCoeffSubtotal,jdbcType=DECIMAL},</if>
            <if test="manageCoeffRegionalsubtotal != null">MANAGE_COEFF_REGIONALSUBTOTAL = #{manageCoeffRegionalsubtotal,jdbcType=DECIMAL},</if>
            <if test="manageCoeffRegionaltotal != null">MANAGE_COEFF_REGIONALTOTAL = #{manageCoeffRegionaltotal,jdbcType=DECIMAL},</if>
            <if test="cxb != null">CXB = #{cxb,jdbcType=VARCHAR},</if>
            <if test="isBigV != null">IS_BIG_V = #{isBigV,jdbcType=VARCHAR},</if>
            <if test="centerCode != null">CENTER_CODE = #{centerCode,jdbcType=VARCHAR},</if>
            <if test="regionCode != null">REGION_CODE = #{regionCode,jdbcType=VARCHAR},</if>
            <if test="regionalsubtotal != null">REGIONALSUBTOTAL = #{regionalsubtotal,jdbcType=VARCHAR},</if>
            <if test="outletCode != null">OUTLET_CODE = #{outletCode,jdbcType=VARCHAR},</if>
            <if test="modifier != null">MODIFIER = #{modifier,jdbcType=VARCHAR},</if>
            <if test="moddt != null">MODDT = #{moddt,jdbcType=TIMESTAMP},</if>
        </set>
        WHERE CONS_CUST_NO = #{consCustNo,jdbcType=VARCHAR}
          AND CONSCODE = #{conscode,jdbcType=VARCHAR}
          AND ASSIGN_TIME = #{assignTime,jdbcType=TIMESTAMP}
    </update>

    <!-- 根据主键查询人员绩效系数表记录 -->
    <select id="selectByPrimaryKey" resultType="com.howbuy.crm.account.dao.po.consultant.CmConsPerformanceCoeffPO">
        SELECT
            CONS_CUST_NO AS consCustNo,
            CONSCODE AS conscode,
            ASSIGN_TIME AS assignTime,
            SOURCE_TYPE AS sourceType,
            COMMISSION_COEFF_START AS commissionCoeffStart,
            CUST_CONVERSION_COEFF AS custConversionCoeff,
            MANAGE_COEFF_SUBTOTAL AS manageCoeffSubtotal,
            MANAGE_COEFF_REGIONALSUBTOTAL AS manageCoeffRegionalsubtotal,
            MANAGE_COEFF_REGIONALTOTAL AS manageCoeffRegionaltotal,
            CXB AS cxb,
            IS_BIG_V AS isBigV,
            CENTER_CODE AS centerCode,
            REGION_CODE AS regionCode,
            REGIONALSUBTOTAL AS regionalsubtotal,
            OUTLET_CODE AS outletCode,
            CREATOR AS creator,
            MODIFIER AS modifier,
            CREDT AS credt,
            MODDT AS moddt
        FROM CM_CONS_PERFORMANCE_COEFF
        WHERE CONS_CUST_NO = #{consCustNo,jdbcType=VARCHAR}
          AND CONSCODE = #{conscode,jdbcType=VARCHAR}
          AND ASSIGN_TIME = #{assignTime,jdbcType=TIMESTAMP}
    </select>

    <!-- 根据投顾客户号和投顾code查询最新记录 -->
    <select id="selectLatestByConsCustNoAndConsCode" resultType="com.howbuy.crm.account.dao.po.consultant.CmConsPerformanceCoeffPO">
        SELECT * FROM (
            SELECT
                CONS_CUST_NO AS consCustNo,
                CONSCODE AS conscode,
                ASSIGN_TIME AS assignTime,
                SOURCE_TYPE AS sourceType,
                COMMISSION_COEFF_START AS commissionCoeffStart,
                CUST_CONVERSION_COEFF AS custConversionCoeff,
                MANAGE_COEFF_SUBTOTAL AS manageCoeffSubtotal,
                MANAGE_COEFF_REGIONALSUBTOTAL AS manageCoeffRegionalsubtotal,
                MANAGE_COEFF_REGIONALTOTAL AS manageCoeffRegionaltotal,
                CXB AS cxb,
                IS_BIG_V AS isBigV,
                CENTER_CODE AS centerCode,
                REGION_CODE AS regionCode,
                REGIONALSUBTOTAL AS regionalsubtotal,
                OUTLET_CODE AS outletCode,
                CREATOR AS creator,
                MODIFIER AS modifier,
                CREDT AS credt,
                MODDT AS moddt
            FROM CM_CONS_PERFORMANCE_COEFF
            WHERE CONS_CUST_NO = #{consCustNo,jdbcType=VARCHAR}
              AND CONSCODE = #{conscode,jdbcType=VARCHAR}
            ORDER BY ASSIGN_TIME DESC
        )
        WHERE ROWNUM = 1
    </select>

    <!-- 根据客户+投顾查找CM_CONS_PERFORMANCE_COEFF表中分配时间小于等于当前时间数据 -->
    <select id="selectHistoryRecordsByConsCustNo" resultType="com.howbuy.crm.account.dao.po.consultant.CmConsPerformanceCoeffPO">
        SELECT
            CONS_CUST_NO AS consCustNo,
            CONSCODE AS conscode,
            ASSIGN_TIME AS assignTime,
            SOURCE_TYPE AS sourceType,
            COMMISSION_COEFF_START AS commissionCoeffStart,
            CUST_CONVERSION_COEFF AS custConversionCoeff,
            MANAGE_COEFF_SUBTOTAL AS manageCoeffSubtotal,
            MANAGE_COEFF_REGIONALSUBTOTAL AS manageCoeffRegionalsubtotal,
            MANAGE_COEFF_REGIONALTOTAL AS manageCoeffRegionaltotal,
            CXB AS cxb,
            IS_BIG_V AS isBigV,
            CENTER_CODE AS centerCode,
            REGION_CODE AS regionCode,
            REGIONALSUBTOTAL AS regionalsubtotal,
            OUTLET_CODE AS outletCode,
            CREATOR AS creator,
            MODIFIER AS modifier,
            CREDT AS credt,
            MODDT AS moddt
        FROM CM_CONS_PERFORMANCE_COEFF
        WHERE CONS_CUST_NO = #{consCustNo,jdbcType=VARCHAR}
          AND ASSIGN_TIME &lt;= #{currentTime,jdbcType=TIMESTAMP}
        ORDER BY ASSIGN_TIME DESC
    </select>

    <!-- 查询客户最新的重复类型分配记录 -->
    <select id="selectLatestRepeatRecord" resultType="com.howbuy.crm.account.dao.po.consultant.CmConsPerformanceCoeffPO">
        SELECT * FROM (
            SELECT cons_cust_no as consCustNo,
                conscode,
                outlet_code as outletCode,
                center_code AS centerCode,
                region_code AS regionCode,
                regionalsubtotal,
                source_type as sourceType,
                assign_time as assignTime
            FROM cm_cons_performance_coeff 
            WHERE cons_cust_no = #{consCustNo}
            AND source_type IN 
            <foreach collection="sourceTypes" item="item" open="(" separator="," close=")">
                #{item}
            </foreach>
            AND assign_time &lt; #{assignTime}
            ORDER BY assign_time DESC
        ) WHERE ROWNUM = 1
    </select>

    <!-- 查询客户最新记录 -->
    <select id="selectNewRecord" resultType="com.howbuy.crm.account.dao.po.consultant.CmConsPerformanceCoeffPO">
        SELECT * FROM (
        SELECT cons_cust_no as consCustNo,
        conscode,
        source_type as sourceType,
        assign_time as assignTime
        FROM cm_cons_performance_coeff
        WHERE cons_cust_no = #{consCustNo}
        ORDER BY assign_time DESC
        ) WHERE ROWNUM = 1
    </select>

    <!-- 查询指定时间范围内的分配记录 -->
    <select id="selectRecordsByTimeRange" resultType="com.howbuy.crm.account.dao.po.consultant.CmConsPerformanceCoeffPO">
        SELECT cons_cust_no as consCustNo,
            conscode,
            outlet_code as outletCode,
            center_code AS centerCode,
            region_code AS regionCode,
            regionalsubtotal,
            source_type as sourceType,
            assign_time as assignTime
        FROM cm_cons_performance_coeff 
        WHERE cons_cust_no = #{consCustNo}
        AND assign_time >= #{startTime}
        AND assign_time &lt; #{endTime}
        ORDER BY assign_time ASC
    </select>

    <!-- 分页查询人员绩效系数列表 -->
    <select id="selectConsPerformanceCoeffList" resultType="com.howbuy.crm.account.dao.bo.consultant.CmConsPerformanceCoeffBO">
        SELECT
            t.CONS_CUST_NO AS consCustNo,
            t.CONSCODE AS conscode,
            t.ASSIGN_TIME AS assignTime,
            constant.constdesc AS sourceType,
            t.COMMISSION_COEFF_START AS commissionCoeffStart,
            t.CUST_CONVERSION_COEFF AS custConversionCoeff,
            t.MANAGE_COEFF_SUBTOTAL AS manageCoeffSubtotal,
            t.MANAGE_COEFF_REGIONALSUBTOTAL AS manageCoeffRegionalsubtotal,
            t.MANAGE_COEFF_REGIONALTOTAL AS manageCoeffRegionaltotal,
            cxbConstant.constdesc AS cxb,
            t.IS_BIG_V AS isBigV,
            t.CENTER_CODE AS centerCode,
            t.REGION_CODE AS regionCode,
            uRegion.consname AS regionalsubtotal,
            t.OUTLET_CODE AS outletCode,
            t.CREATOR AS creator,
            t.MODIFIER AS modifier,
            t.CREDT AS credt,
            t.MODDT AS moddt,
            u.consname as consName,
            u.outletcode as orgCodeCurrent
        <include refid="selectConsPerformanceCoeffListFromWhere"/>
    </select>

    <select id="selectConsPerformanceCoeffListCount" resultType="int">
        SELECT count(1)
        <include refid="selectConsPerformanceCoeffListFromWhere"/>
    </select>

    <sql id="selectConsPerformanceCoeffListFromWhere">
        FROM CM_CONS_PERFORMANCE_COEFF t
        left join cm_consultant u
        on t.CONSCODE = u.CONSCODE
        left join hb_constant constant
        on t.source_type = constant.constcode
        and constant.typecode = 'custrestype'
        left join hb_constant cxbConstant
        on t.cxb = cxbConstant.constcode
        and cxbConstant.typecode = 'firstrestype'
        left join cm_consultant uRegion
        on t.REGIONALSUBTOTAL = uRegion.CONSCODE
        <where>
            <if test="orgCode != null and orgCode != '' and orgCode != '0'.toString() ">
                AND u.outletcode IN (SELECT ORGCODE
                FROM HB_ORGANIZATION T
                WHERE T.STATUS = '0'
                START WITH T.ORGCODE = #{orgCode}
                CONNECT BY PRIOR ORGCODE = PARENTORGCODE)
            </if>
            <if test="consCode != null and consCode != ''">
                AND t.CONSCODE = #{consCode}
            </if>
            <if test="startAssignDay != null and startAssignDay != ''">
                AND to_char(t.ASSIGN_TIME, 'YYYYMMDD') >= #{startAssignDay}
            </if>
            <if test="endAssignDay != null and endAssignDay != ''">
                AND to_char(t.ASSIGN_TIME, 'YYYYMMDD') &lt;= #{endAssignDay}
            </if>
            <if test="consCustNo != null and consCustNo != ''">
                AND t.CONS_CUST_NO = #{consCustNo}
            </if>
        </where>
        <choose>
            <when test="sortField != null and sortField != '' and sortOrder != null and sortOrder != ''">
                ORDER BY
                <choose>
                    <when test="sortField == 'assignTime'">t.ASSIGN_TIME</when>
                    <!-- 暂时不排序的字段 -->
                    <!-- <when test="sortField == 'consCustNo'">t.CONS_CUST_NO</when>
                    <when test="sortField == 'consCode'">t.CONSCODE</when>
                    <when test="sortField == 'sourceType'">t.SOURCE_TYPE</when>
                    <when test="sortField == 'commissionCoeffStart'">t.COMMISSION_COEFF_START</when>
                    <when test="sortField == 'custConversionCoeff'">t.CUST_CONVERSION_COEFF</when>
                    <when test="sortField == 'manageCoeffSubtotal'">t.MANAGE_COEFF_SUBTOTAL</when>
                    <when test="sortField == 'manageCoeffRegionalsubtotal'">t.MANAGE_COEFF_REGIONALSUBTOTAL</when>
                    <when test="sortField == 'manageCoeffRegionaltotal'">t.MANAGE_COEFF_REGIONALTOTAL</when>
                    <when test="sortField == 'cxb'">t.CXB</when>
                    <when test="sortField == 'isBigV'">t.IS_BIG_V</when> -->
                    <otherwise>t.ASSIGN_TIME</otherwise>
                </choose>
                <choose>
                    <when test="sortOrder.equalsIgnoreCase('asc')">ASC</when>
                    <otherwise>DESC</otherwise>
                </choose>
            </when>
            <otherwise>
                ORDER BY t.ASSIGN_TIME DESC
            </otherwise>
        </choose>
    </sql>

    <!-- 批量查询投顾客户号列表的最新绩效系数记录 -->
    <select id="selectLatestByConsCustNoListAndConsCode" resultType="com.howbuy.crm.account.dao.po.consultant.CmConsPerformanceCoeffPO">
        SELECT * FROM (
            SELECT
                CONS_CUST_NO AS consCustNo,
                CONSCODE AS conscode,
                ASSIGN_TIME AS assignTime,
                SOURCE_TYPE AS sourceType,
                COMMISSION_COEFF_START AS commissionCoeffStart,
                CUST_CONVERSION_COEFF AS custConversionCoeff,
                MANAGE_COEFF_SUBTOTAL AS manageCoeffSubtotal,
                MANAGE_COEFF_REGIONALSUBTOTAL AS manageCoeffRegionalsubtotal,
                MANAGE_COEFF_REGIONALTOTAL AS manageCoeffRegionaltotal,
                CXB AS cxb,
                IS_BIG_V AS isBigV,
                CENTER_CODE AS centerCode,
                REGION_CODE AS regionCode,
                REGIONALSUBTOTAL AS regionalsubtotal,
                OUTLET_CODE AS outletCode,
                CREATOR AS creator,
                MODIFIER AS modifier,
                CREDT AS credt,
                MODDT AS moddt,
                ROW_NUMBER() OVER (PARTITION BY CONS_CUST_NO ORDER BY ASSIGN_TIME DESC) AS rn
            FROM CM_CONS_PERFORMANCE_COEFF
            WHERE CONS_CUST_NO IN
            <foreach collection="consCustNoList" item="item" open="(" separator="," close=")">
                #{item}
            </foreach>
            <if test="conscode != null and conscode != ''">
                AND CONSCODE = #{conscode}
            </if>
        )
        WHERE rn = 1
    </select>

</mapper>