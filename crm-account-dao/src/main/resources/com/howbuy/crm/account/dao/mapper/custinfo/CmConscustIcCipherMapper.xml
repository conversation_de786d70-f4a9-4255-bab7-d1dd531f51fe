<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.howbuy.crm.account.dao.mapper.custinfo.CmConscustIcCipherMapper">
  <resultMap id="BaseResultMap" type="com.howbuy.crm.account.dao.po.custinfo.CmConscustIcCipherPO">
    <!--@mbg.generated-->
    <!--@Table CM_CONSCUST_IC_CIPHER-->
    <id column="CONSCUSTNO" jdbcType="VARCHAR" property="conscustno" />
    <result column="IDNO_CIPHER" jdbcType="VARCHAR" property="idnoCipher" />
    <result column="CUSTNAME_CIPHER" jdbcType="VARCHAR" property="custnameCipher" />
    <result column="ADDR_CIPHER" jdbcType="VARCHAR" property="addrCipher" />
    <result column="MOBILE_CIPHER" jdbcType="VARCHAR" property="mobileCipher" />
    <result column="TELNO_CIPHER" jdbcType="VARCHAR" property="telnoCipher" />
    <result column="EMAIL_CIPHER" jdbcType="VARCHAR" property="emailCipher" />
    <result column="ADDR2_CIPHER" jdbcType="VARCHAR" property="addr2Cipher" />
    <result column="MOBILE2_CIPHER" jdbcType="VARCHAR" property="mobile2Cipher" />
    <result column="EMAIL2_CIPHER" jdbcType="VARCHAR" property="email2Cipher" />
    <result column="LINKMAN_CIPHER" jdbcType="VARCHAR" property="linkmanCipher" />
    <result column="LINKTEL_CIPHER" jdbcType="VARCHAR" property="linktelCipher" />
    <result column="LINKMOBILE_CIPHER" jdbcType="VARCHAR" property="linkmobileCipher" />
    <result column="LINKEMAIL_CIPHER" jdbcType="VARCHAR" property="linkemailCipher" />
    <result column="LINKADDR_CIPHER" jdbcType="VARCHAR" property="linkaddrCipher" />
  </resultMap>
  <sql id="Base_Column_List">
    <!--@mbg.generated-->
    CONSCUSTNO, IDNO_CIPHER, CUSTNAME_CIPHER, ADDR_CIPHER, MOBILE_CIPHER, TELNO_CIPHER, 
    EMAIL_CIPHER, ADDR2_CIPHER, MOBILE2_CIPHER, EMAIL2_CIPHER, LINKMAN_CIPHER, LINKTEL_CIPHER, 
    LINKMOBILE_CIPHER, LINKEMAIL_CIPHER, LINKADDR_CIPHER
  </sql>
  <select id="selectByPrimaryKey" parameterType="java.lang.String" resultMap="BaseResultMap">
    <!--@mbg.generated-->
    select 
    <include refid="Base_Column_List" />
    from CM_CONSCUST_IC_CIPHER
    where CONSCUSTNO = #{conscustno,jdbcType=VARCHAR}
  </select>
  <delete id="deleteByPrimaryKey" parameterType="java.lang.String">
    <!--@mbg.generated-->
    delete from CM_CONSCUST_IC_CIPHER
    where CONSCUSTNO = #{conscustno,jdbcType=VARCHAR}
  </delete>
  <insert id="insert" parameterType="com.howbuy.crm.account.dao.po.custinfo.CmConscustIcCipherPO">
    <!--@mbg.generated-->
    insert into CM_CONSCUST_IC_CIPHER (CONSCUSTNO, IDNO_CIPHER, CUSTNAME_CIPHER, 
      ADDR_CIPHER, MOBILE_CIPHER, TELNO_CIPHER, 
      EMAIL_CIPHER, ADDR2_CIPHER, MOBILE2_CIPHER, 
      EMAIL2_CIPHER, LINKMAN_CIPHER, LINKTEL_CIPHER, 
      LINKMOBILE_CIPHER, LINKEMAIL_CIPHER, LINKADDR_CIPHER
      )
    values (#{conscustno,jdbcType=VARCHAR}, #{idnoCipher,jdbcType=VARCHAR}, #{custnameCipher,jdbcType=VARCHAR}, 
      #{addrCipher,jdbcType=VARCHAR}, #{mobileCipher,jdbcType=VARCHAR}, #{telnoCipher,jdbcType=VARCHAR}, 
      #{emailCipher,jdbcType=VARCHAR}, #{addr2Cipher,jdbcType=VARCHAR}, #{mobile2Cipher,jdbcType=VARCHAR}, 
      #{email2Cipher,jdbcType=VARCHAR}, #{linkmanCipher,jdbcType=VARCHAR}, #{linktelCipher,jdbcType=VARCHAR}, 
      #{linkmobileCipher,jdbcType=VARCHAR}, #{linkemailCipher,jdbcType=VARCHAR}, #{linkaddrCipher,jdbcType=VARCHAR}
      )
  </insert>
  <insert id="insertSelective" parameterType="com.howbuy.crm.account.dao.po.custinfo.CmConscustIcCipherPO">
    <!--@mbg.generated-->
    insert into CM_CONSCUST_IC_CIPHER
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="conscustno != null">
        CONSCUSTNO,
      </if>
      <if test="idnoCipher != null">
        IDNO_CIPHER,
      </if>
      <if test="custnameCipher != null">
        CUSTNAME_CIPHER,
      </if>
      <if test="addrCipher != null">
        ADDR_CIPHER,
      </if>
      <if test="mobileCipher != null">
        MOBILE_CIPHER,
      </if>
      <if test="telnoCipher != null">
        TELNO_CIPHER,
      </if>
      <if test="emailCipher != null">
        EMAIL_CIPHER,
      </if>
      <if test="addr2Cipher != null">
        ADDR2_CIPHER,
      </if>
      <if test="mobile2Cipher != null">
        MOBILE2_CIPHER,
      </if>
      <if test="email2Cipher != null">
        EMAIL2_CIPHER,
      </if>
      <if test="linkmanCipher != null">
        LINKMAN_CIPHER,
      </if>
      <if test="linktelCipher != null">
        LINKTEL_CIPHER,
      </if>
      <if test="linkmobileCipher != null">
        LINKMOBILE_CIPHER,
      </if>
      <if test="linkemailCipher != null">
        LINKEMAIL_CIPHER,
      </if>
      <if test="linkaddrCipher != null">
        LINKADDR_CIPHER,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="conscustno != null">
        #{conscustno,jdbcType=VARCHAR},
      </if>
      <if test="idnoCipher != null">
        #{idnoCipher,jdbcType=VARCHAR},
      </if>
      <if test="custnameCipher != null">
        #{custnameCipher,jdbcType=VARCHAR},
      </if>
      <if test="addrCipher != null">
        #{addrCipher,jdbcType=VARCHAR},
      </if>
      <if test="mobileCipher != null">
        #{mobileCipher,jdbcType=VARCHAR},
      </if>
      <if test="telnoCipher != null">
        #{telnoCipher,jdbcType=VARCHAR},
      </if>
      <if test="emailCipher != null">
        #{emailCipher,jdbcType=VARCHAR},
      </if>
      <if test="addr2Cipher != null">
        #{addr2Cipher,jdbcType=VARCHAR},
      </if>
      <if test="mobile2Cipher != null">
        #{mobile2Cipher,jdbcType=VARCHAR},
      </if>
      <if test="email2Cipher != null">
        #{email2Cipher,jdbcType=VARCHAR},
      </if>
      <if test="linkmanCipher != null">
        #{linkmanCipher,jdbcType=VARCHAR},
      </if>
      <if test="linktelCipher != null">
        #{linktelCipher,jdbcType=VARCHAR},
      </if>
      <if test="linkmobileCipher != null">
        #{linkmobileCipher,jdbcType=VARCHAR},
      </if>
      <if test="linkemailCipher != null">
        #{linkemailCipher,jdbcType=VARCHAR},
      </if>
      <if test="linkaddrCipher != null">
        #{linkaddrCipher,jdbcType=VARCHAR},
      </if>
    </trim>
  </insert>
  <update id="updateByPrimaryKeySelective" parameterType="com.howbuy.crm.account.dao.po.custinfo.CmConscustIcCipherPO">
    <!--@mbg.generated-->
    update CM_CONSCUST_IC_CIPHER
    <set>
      <if test="idnoCipher != null">
        IDNO_CIPHER = #{idnoCipher,jdbcType=VARCHAR},
      </if>
      <if test="custnameCipher != null">
        CUSTNAME_CIPHER = #{custnameCipher,jdbcType=VARCHAR},
      </if>
      <if test="addrCipher != null">
        ADDR_CIPHER = #{addrCipher,jdbcType=VARCHAR},
      </if>
      <if test="mobileCipher != null">
        MOBILE_CIPHER = #{mobileCipher,jdbcType=VARCHAR},
      </if>
      <if test="telnoCipher != null">
        TELNO_CIPHER = #{telnoCipher,jdbcType=VARCHAR},
      </if>
      <if test="emailCipher != null">
        EMAIL_CIPHER = #{emailCipher,jdbcType=VARCHAR},
      </if>
      <if test="addr2Cipher != null">
        ADDR2_CIPHER = #{addr2Cipher,jdbcType=VARCHAR},
      </if>
      <if test="mobile2Cipher != null">
        MOBILE2_CIPHER = #{mobile2Cipher,jdbcType=VARCHAR},
      </if>
      <if test="email2Cipher != null">
        EMAIL2_CIPHER = #{email2Cipher,jdbcType=VARCHAR},
      </if>
      <if test="linkmanCipher != null">
        LINKMAN_CIPHER = #{linkmanCipher,jdbcType=VARCHAR},
      </if>
      <if test="linktelCipher != null">
        LINKTEL_CIPHER = #{linktelCipher,jdbcType=VARCHAR},
      </if>
      <if test="linkmobileCipher != null">
        LINKMOBILE_CIPHER = #{linkmobileCipher,jdbcType=VARCHAR},
      </if>
      <if test="linkemailCipher != null">
        LINKEMAIL_CIPHER = #{linkemailCipher,jdbcType=VARCHAR},
      </if>
      <if test="linkaddrCipher != null">
        LINKADDR_CIPHER = #{linkaddrCipher,jdbcType=VARCHAR},
      </if>
    </set>
    where CONSCUSTNO = #{conscustno,jdbcType=VARCHAR}
  </update>
  <update id="updateByPrimaryKey" parameterType="com.howbuy.crm.account.dao.po.custinfo.CmConscustIcCipherPO">
    <!--@mbg.generated-->
    update CM_CONSCUST_IC_CIPHER
    set IDNO_CIPHER = #{idnoCipher,jdbcType=VARCHAR},
      CUSTNAME_CIPHER = #{custnameCipher,jdbcType=VARCHAR},
      ADDR_CIPHER = #{addrCipher,jdbcType=VARCHAR},
      MOBILE_CIPHER = #{mobileCipher,jdbcType=VARCHAR},
      TELNO_CIPHER = #{telnoCipher,jdbcType=VARCHAR},
      EMAIL_CIPHER = #{emailCipher,jdbcType=VARCHAR},
      ADDR2_CIPHER = #{addr2Cipher,jdbcType=VARCHAR},
      MOBILE2_CIPHER = #{mobile2Cipher,jdbcType=VARCHAR},
      EMAIL2_CIPHER = #{email2Cipher,jdbcType=VARCHAR},
      LINKMAN_CIPHER = #{linkmanCipher,jdbcType=VARCHAR},
      LINKTEL_CIPHER = #{linktelCipher,jdbcType=VARCHAR},
      LINKMOBILE_CIPHER = #{linkmobileCipher,jdbcType=VARCHAR},
      LINKEMAIL_CIPHER = #{linkemailCipher,jdbcType=VARCHAR},
      LINKADDR_CIPHER = #{linkaddrCipher,jdbcType=VARCHAR}
    where CONSCUSTNO = #{conscustno,jdbcType=VARCHAR}
  </update>
</mapper>