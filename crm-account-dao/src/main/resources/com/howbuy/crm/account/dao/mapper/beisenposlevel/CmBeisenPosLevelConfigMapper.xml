<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.howbuy.crm.account.dao.mapper.beisenposlevel.CmBeisenPosLevelConfigMapper">

    <select id="selectPosLevelConfig" parameterType="com.howbuy.crm.account.dao.po.beisenposlevel.CmBeisenPosLevelConfigPO" resultType="com.howbuy.crm.account.dao.bo.beisenposlevel.CmBeisenPosLevelConfigBO">
        select t1.ID id,
               t1.POSITIONS_LEVEL_BEISEN positionsLevelBeisen,
               t1.POSITIONS_LEVEL_NAME_BEISEN positionsLevelName<PERSON><PERSON><PERSON>,
               t1.USER_LEVEL_CRM userLevelCrm,
               t2.CONSTDESC userLevelCrmName,
               t1.POSITIONS_LEVEL_CRM positionsLevelCrm,
               t3.CONSTDESC positionsLevelCrmName,
               t1.SUB_POSITIONS_LEVEL_CRM subPositionsLevelCrm,
               t4.CONSTDESC subPositionsLevelCrmName,
               t1.START_DATE startDate,
               t1.END_DATE endDate,
               t1.CREATOR,
               t1.MODIFIER,
               t1.CREDT,
               t1.MODDT
        from CM_BEISEN_POS_LEVEL_CONFIG t1
        LEFT join hb_constant t2 ON t2.constcode = t1.USER_LEVEL_CRM AND t2.typecode='userlevel'
        LEFT JOIN hb_constant t3 ON t3.constcode = t1.POSITIONS_LEVEL_CRM AND t3.typecode='hrpositionslevel'
        LEFT JOIN hb_constant t4 ON t4.constcode = t1.SUB_POSITIONS_LEVEL_CRM AND t4.typecode='subpositionslevel'
        WHERE 1=1
        <if test="id != null">
            and t1.id = #{id}
        </if>
        <if test="positionsLevelNameBeisen != null">
            and t1.POSITIONS_LEVEL_NAME_BEISEN like '%'||#{positionsLevelNameBeisen}||'%'
        </if>
        <if test="positionsLevelBeisen != null">
            and t1.POSITIONS_LEVEL_BEISEN = #{positionsLevelBeisen}
        </if>
        <if test="userLevelCrm != null">
            and t1.USER_LEVEL_CRM = #{userLevelCrm}
        </if>
        <if test="positionsLevelCrm != null">
            and t1.POSITIONS_LEVEL_CRM = #{positionsLevelCrm}
        </if>
        <if test="subPositionsLevelCrm != null">
            and t1.SUB_POSITIONS_LEVEL_CRM = #{subPositionsLevelCrm}
        </if>
        <if test="startDate != null">
            and t1.START_DATE >= #{startDate}
        </if>
        <if test="endDate != null">
            and t1.END_DATE &lt;= #{endDate}
        </if>
    </select>

    <insert id="savePosLevelConfig" parameterType="com.howbuy.crm.account.dao.po.beisenposlevel.CmBeisenPosLevelConfigPO">
        merge into CM_BEISEN_POS_LEVEL_CONFIG t1
        using (
            select
            #{id,jdbcType=VARCHAR} id,
            #{positionsLevelBeisen,jdbcType=VARCHAR} as positions_level_beisen,
            #{positionsLevelNameBeisen,jdbcType=VARCHAR} as positions_level_name_beisen,
            #{userLevelCrm,jdbcType=VARCHAR} as user_level_crm,
            #{positionsLevelCrm,jdbcType=VARCHAR} as positions_level_crm,
            #{subPositionsLevelCrm,jdbcType=VARCHAR} as sub_positions_level_crm,
            #{startDate,jdbcType=VARCHAR} as start_date,
            #{endDate,jdbcType=VARCHAR} as end_date
            from dual
        ) t2
        on (t1.id = t2.id)
        when matched then
        update set
        t1.positions_level_name_beisen = t2.positions_level_name_beisen,
        t1.user_level_crm = t2.user_level_crm,
        t1.positions_level_crm = t2.positions_level_crm,
        t1.sub_positions_level_crm = t2.sub_positions_level_crm,
        t1.start_date = t2.start_date,
        t1.end_date = t2.end_date,
        t1.modifier = 'AUTO',
        t1.moddt = sysdate
        when not matched then
        insert (id, positions_level_beisen, positions_level_name_beisen, user_level_crm, positions_level_crm,
        sub_positions_level_crm, start_date, end_date, creator, modifier, credt, moddt)
        values (CM_BEISION_POS_LEVEL_CONF_SEQ.nextval, t2.positions_level_beisen, t2.positions_level_name_beisen,
                t2.user_level_crm, t2.positions_level_crm, t2.sub_positions_level_crm, t2.start_date, t2.end_date, 'AUTO', 'AUTO', sysdate, sysdate)
    </insert>

    <delete id="deletePosLevelConfig" parameterType="Long">
        delete from CM_BEISEN_POS_LEVEL_CONFIG where id = #{id}
    </delete>

    <select id="selectPosLevelConfigById" parameterType="Long" resultType="com.howbuy.crm.account.dao.bo.beisenposlevel.CmBeisenPosLevelConfigBO">
        select t1.ID id,
               t1.POSITIONS_LEVEL_BEISEN positionsLevelBeisen,
               t1.POSITIONS_LEVEL_NAME_BEISEN positionsLevelNameBeisen,
               t1.USER_LEVEL_CRM userLevelCrm,
               t2.CONSTDESC userLevelCrmName,
               t1.POSITIONS_LEVEL_CRM positionsLevelCrm,
               t3.CONSTDESC positionsLevelCrmName,
               t1.SUB_POSITIONS_LEVEL_CRM subPositionsLevelCrm,
               t4.CONSTDESC subPositionsLevelCrmName,
               t1.START_DATE startDate,
               t1.END_DATE endDate,
               t1.CREATOR,
               t1.MODIFIER,
               t1.CREDT,
               t1.MODDT
        from CM_BEISEN_POS_LEVEL_CONFIG t1
        LEFT join hb_constant t2 ON t2.constcode = t1.USER_LEVEL_CRM AND t2.typecode='userlevel'
        LEFT JOIN hb_constant t3 ON t3.constcode = t1.POSITIONS_LEVEL_CRM AND t3.typecode='hrpositionslevel'
        LEFT JOIN hb_constant t4 ON t4.constcode = t1.SUB_POSITIONS_LEVEL_CRM AND t4.typecode='subpositionslevel'
        WHERE t1.ID = #{id}
    </select>
</mapper>