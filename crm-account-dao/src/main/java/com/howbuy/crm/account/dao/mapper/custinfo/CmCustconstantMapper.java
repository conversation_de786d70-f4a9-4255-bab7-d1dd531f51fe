package com.howbuy.crm.account.dao.mapper.custinfo;

import com.howbuy.crm.account.dao.po.custinfo.CmCustconstantPO;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.Date;

/**
 * <AUTHOR>
 * @description: (请在此添加描述)
 * @date 2023/12/26 17:34
 * @since JDK 1.8
 */
@Mapper
public interface CmCustconstantMapper {
    int deleteByPrimaryKey(String custno);

    int insert(CmCustconstantPO record);

    int insertSelective(CmCustconstantPO record);

    CmCustconstantPO selectByPrimaryKey(String custno);

    int updateByPrimaryKeySelective(CmCustconstantPO record);

    int updateByPrimaryKey(CmCustconstantPO record);


    /**
     * @description: (根据投顾consCode 查询客户数)
     * @param consCode
     * @return java.lang.Integer
     * @throws
     * @since JDK 1.8
     */
    Integer countCustNumByConsCode(@Param("consCode") String consCode);


    /**
     * @description: 根据投顾客户号查询分配时间
     * @param custNo 投顾客户号
     * @return Date 分配时间
     * @author: shijie.wang
     * @date: 2025-07-09 14:05:00
     * @since JDK 1.8
     */
    Date selectBindDateByCustNo(String custNo);
}