package com.howbuy.crm.account.dao.po.custinfo;

import lombok.Data;

/**
 * @description:(客户来源)
 * @param
 * @return 
 * @author: haoran.zhang
 * @date: 2023/12/27 16:42
 * @since JDK 1.8
 */
@Data
public class CmSourceInfoPO {

	/**
	 * 来源识别编号
	 * */
	private String sourceId;
	/**
	 * 一级来源编码
	 * */
	private String firstLevelCode;
	/**
	 * 二级来源编码
	 * */
	private String secondLevelCode;
	/**
	 *  三级来源编码
	 *  */
	private String thirdLevelCode;
	/**
	 * 四级来源编码
	 * */
	private String fourthLevelCode;
	/**
	 *  来源编码
	 *  */
	private String sourceNo;
	/**
	 * 来源名称
	 * */
	private String sourceName;


}
