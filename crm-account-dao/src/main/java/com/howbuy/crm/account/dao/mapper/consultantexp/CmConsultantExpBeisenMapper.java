/**
 * Copyright (c) 2024, <PERSON><PERSON><PERSON><PERSON> HOWBUY INVESTMENT MANAGEMENT Co., Ltd.
 * All right reserved.
 * <p>
 * THIS IS UNPUBLISHED PROPRIETARY SOURCE CODE OF HOWBUY INVESTMENT
 * MANAGEMENT CO., LTD.  THE CONTENTS OF THIS FILE MAY NOT BE DISCLOSED
 * TO THIRD PARTIES, COPIED OR DUPLICATED IN ANY FORM, IN WHOLE OR IN PART,
 * WITHOUT THE PRIOR WRITTEN PERMISSION OF HOWBUY INVESTMENT MANAGEMENT
 * CO., LTD.
 */
package com.howbuy.crm.account.dao.mapper.consultantexp;

import com.howbuy.crm.account.dao.po.beisen.CmBeisenOrgDO;
import com.howbuy.crm.account.dao.po.beisen.CmBeisenUserInfoPO;
import com.howbuy.crm.account.dao.po.beisenorg.CmBeisenOrgConfigPO;
import com.howbuy.crm.account.dao.po.beisenposlevel.CmBeisenPosLevelConfigPO;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * @description: (请在此添加描述)
 * <AUTHOR>
 * @date 2024/11/14 19:25
 * @since JDK 1.8
 */
public interface CmConsultantExpBeisenMapper {

    /**
     * @description:(获取北森组织机构所有数据)
     * @param
     * @return java.util.List<com.howbuy.crm.hb.domain.beisen.CmBeisenOrgDO>
     * @author: shijie.wang
     * @date: 2024/11/4 9:47
     * @since JDK 1.8
     */
    List<CmBeisenOrgDO> queryAllCmBeisenOrgData();

    /**
     * @description:(获取CRM和北森组织机构映射关系所有数据)
     * @param
     * @return java.util.List<com.howbuy.crm.hb.domain.beisen.CmBeisenOrgConfigPO>
     * @author: shijie.wang
     * @date: 2024/11/4 10:10
     * @since JDK 1.8
     */
    List<CmBeisenOrgConfigPO> queryAllCmBeisenOrgConfigData();

    /**
     * @description:(获取CRM和北森职级映射关系所有数据)
     * @param
     * @return java.util.List<com.howbuy.crm.hb.domain.beisen.CmBeisenPosLevelConfigPO>
     * @author: shijie.wang
     * @date: 2024/11/4 10:45
     * @since JDK 1.8
     */
    List<CmBeisenPosLevelConfigPO> queryCmBeisenPosLevelConfigData();

    /**
     * @description:(获得北森用户信息数据)
     * @param startDate
     * @param endDate
     * @return java.util.List<com.howbuy.crm.hb.domain.beisen.CmBeisenUserInfoPO>
     * @author: shijie.wang
     * @date: 2024/11/5 18:25
     * @since JDK 1.8
     */
    List<CmBeisenUserInfoPO> queryCmBeisenUserInfoData(@Param("startDate") String startDate, @Param("endDate") String endDate);

    /**
     * @description:(获得北森用户信息数据)
     * @param startDate
     * @return java.util.List<com.howbuy.crm.hb.domain.beisen.CmBeisenUserInfoPO>
     * @author: shijie.wang
     * @date: 2024/11/5 18:25
     * @since JDK 1.8
     */
    List<CmBeisenUserInfoPO> queryCmBeisenUserInfoDataByModdt(@Param("startDate") String startDate);
}