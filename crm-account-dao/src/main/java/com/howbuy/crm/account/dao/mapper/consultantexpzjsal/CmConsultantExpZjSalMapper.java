/**
 * Copyright (c) 2024, <PERSON><PERSON><PERSON><PERSON> HOWBUY INVESTMENT MANAGEMENT Co., Ltd.
 * All right reserved.
 * <p>
 * THIS IS UNPUBLISHED PROPRIETARY SOURCE CODE OF HOWBUY INVESTMENT
 * MANAGEMENT CO., LTD.  THE CONTENTS OF THIS FILE MAY NOT BE DISCLOSED
 * TO THIRD PARTIES, COPIED OR DUPLICATED IN ANY FORM, IN WHOLE OR IN PART,
 * WITHOUT THE PRIOR WRITTEN PERMISSION OF HOWBUY INVESTMENT MANAGEMENT
 * CO., LTD.
 */
package com.howbuy.crm.account.dao.mapper.consultantexpzjsal;

import com.howbuy.crm.account.dao.po.consultantexp.CmConsultantExpZjSalPO;

import java.util.List;

/**
 * @description: (请在此添加描述)
 * <AUTHOR>
 * @date 2024/11/14 18:38
 * @since JDK 1.8
 */
public interface CmConsultantExpZjSalMapper {
    /**
     * @description:(获得所有有效数据)
     * @param
     * @return java.util.List<com.howbuy.crm.hb.domain.system.CmConsultantExpZjSal>
     * @author: shijie.wang
     * @date: 2024/10/31 14:40
     * @since JDK 1.8
     */
    List<CmConsultantExpZjSalPO> selectAllData();
}