package com.howbuy.crm.account.dao.po.custinfo;

import java.math.BigDecimal;
import java.util.Date;

/**
 * 投顾客户收货地址
 */
public class CmConscustDeliveryAddressPO {
    /**
    * ID
    */
    private BigDecimal id;

    /**
    * 投顾客户号
    */
    private String conscustno;

    /**
    * 收货人姓名
    */
    private String receiverName;

    /**
     * 收货人手机号区号
     */
    private String mobileAreaCode;

    /**
    * 收货人手机号摘要
    */
    private String mobileDigest;

    /**
    * 收货人手机号掩码
    */
    private String mobileMask;

    /**
    * 收货人手机号密文
    */
    private String mobileCipher;

    /**
     * 国家/地区编码
     */
    private String nationCode;

    /**
    * 省编码
    */
    private String provCode;

    /**
    * 市编码
    */
    private String cityCode;

    /**
    * 区编码
    */
    private String countyCode;

    /**
    * 详细地址摘要
    */
    private String addrDigest;

    /**
    * 详细地址掩码
    */
    private String addrMask;

    /**
    * 详细地址密文
    */
    private String addrCipher;

    /**
    * 备注
    */
    private String remark;

    /**
    * 记录有效状态（1-正常  0-删除）
    */
    private String recStat;

    /**
    * 创建人
    */
    private String creator;

    /**
    * 创建时间
    */
    private Date createTimestamp;

    /**
    * 修改人
    */
    private String modifier;

    /**
    * 修改时间
    */
    private Date modifyTimestamp;

    public BigDecimal getId() {
        return id;
    }

    public void setId(BigDecimal id) {
        this.id = id;
    }

    public String getConscustno() {
        return conscustno;
    }

    public void setConscustno(String conscustno) {
        this.conscustno = conscustno;
    }

    public String getReceiverName() {
        return receiverName;
    }

    public void setReceiverName(String receiverName) {
        this.receiverName = receiverName;
    }

    public String getMobileDigest() {
        return mobileDigest;
    }

    public void setMobileDigest(String mobileDigest) {
        this.mobileDigest = mobileDigest;
    }

    public String getMobileMask() {
        return mobileMask;
    }

    public void setMobileMask(String mobileMask) {
        this.mobileMask = mobileMask;
    }

    public String getMobileCipher() {
        return mobileCipher;
    }

    public void setMobileCipher(String mobileCipher) {
        this.mobileCipher = mobileCipher;
    }

    public String getProvCode() {
        return provCode;
    }

    public void setProvCode(String provCode) {
        this.provCode = provCode;
    }

    public String getCityCode() {
        return cityCode;
    }

    public void setCityCode(String cityCode) {
        this.cityCode = cityCode;
    }

    public String getCountyCode() {
        return countyCode;
    }

    public void setCountyCode(String countyCode) {
        this.countyCode = countyCode;
    }

    public String getAddrDigest() {
        return addrDigest;
    }

    public void setAddrDigest(String addrDigest) {
        this.addrDigest = addrDigest;
    }

    public String getAddrMask() {
        return addrMask;
    }

    public void setAddrMask(String addrMask) {
        this.addrMask = addrMask;
    }

    public String getAddrCipher() {
        return addrCipher;
    }

    public void setAddrCipher(String addrCipher) {
        this.addrCipher = addrCipher;
    }

    public String getRemark() {
        return remark;
    }

    public void setRemark(String remark) {
        this.remark = remark;
    }

    public String getRecStat() {
        return recStat;
    }

    public void setRecStat(String recStat) {
        this.recStat = recStat;
    }

    public String getCreator() {
        return creator;
    }

    public void setCreator(String creator) {
        this.creator = creator;
    }

    public Date getCreateTimestamp() {
        return createTimestamp;
    }

    public void setCreateTimestamp(Date createTimestamp) {
        this.createTimestamp = createTimestamp;
    }

    public String getModifier() {
        return modifier;
    }

    public void setModifier(String modifier) {
        this.modifier = modifier;
    }

    public Date getModifyTimestamp() {
        return modifyTimestamp;
    }

    public void setModifyTimestamp(Date modifyTimestamp) {
        this.modifyTimestamp = modifyTimestamp;
    }

    public String getMobileAreaCode() {
        return mobileAreaCode;
    }

    public void setMobileAreaCode(String mobileAreaCode) {
        this.mobileAreaCode = mobileAreaCode;
    }

    public String getNationCode() {
        return nationCode;
    }

    public void setNationCode(String nationCode) {
        this.nationCode = nationCode;
    }
}