/**
 * Copyright (c) 2024, <PERSON><PERSON><PERSON><PERSON> HOWBUY INVESTMENT MANAGEMENT Co., Ltd.
 * All right reserved.
 * <p>
 * THIS IS UNPUBLISHED PROPRIETARY SOURCE CODE OF HOWBUY INVESTMENT
 * MANAGEMENT CO., LTD.  THE CONTENTS OF THIS FILE MAY NOT BE DISCLOSED
 * TO THIRD PARTIES, COPIED OR DUPLICATED IN ANY FORM, IN WHOLE OR IN PART,
 * WITHOUT THE PRIOR WRITTEN PERMISSION OF HOWBUY INVESTMENT MANAGEMENT
 * CO., LTD.
 */
package com.howbuy.crm.account.dao.req.consultant;

import lombok.Getter;
import lombok.Setter;

import java.io.Serializable;
import java.util.Date;

/**
 * @description: 查询人员绩效系数列表条件DTO
 * <AUTHOR>
 * @date 2025-07-11 18:52:00
 * @since JDK 1.8
 */
@Getter
@Setter
public class QueryConsPerformanceCoeffListDTO implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 部门代码（必填，带数据广度权限）
     */
    private String orgCode;

    /**
     * 投顾code（必填，带数据广度权限）
     */
    private String consCode;

    /**
     * 分配开始时间
     */
    private String startAssignDay;

    /**
     * 分配结束时间
     */
    private String endAssignDay;

    /**
     * 投顾客户号（可空）
     */
    private String consCustNo;

    /**
     * 页号
     */
    private Integer page;

    /**
     * 每页大小
     */
    private Integer rows;

    /**
     * 排序字段（可空）
     */
    private String sortField;

    /**
     * 排序方式（asc-升序，desc-降序，可空）
     */
    private String sortOrder;
} 