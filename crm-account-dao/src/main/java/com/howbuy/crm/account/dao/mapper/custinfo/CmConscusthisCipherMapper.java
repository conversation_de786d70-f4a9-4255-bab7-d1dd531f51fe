package com.howbuy.crm.account.dao.mapper.custinfo;

import com.howbuy.crm.account.dao.po.custinfo.CmConscusthisCipherPO;
import org.apache.ibatis.annotations.Mapper;

/**
 * @description: (请在此添加描述)
 * <AUTHOR>
 * @date 2023/12/8 14:58
 * @since JDK 1.8
 */
public interface CmConscusthisCipherMapper {
    int deleteByPrimaryKey(String appserialno);

    int insert(CmConscusthisCipherPO record);

    int insertSelective(CmConscusthisCipherPO record);

    CmConscusthisCipherPO selectByPrimaryKey(String appserialno);

    int updateByPrimaryKeySelective(CmConscusthisCipherPO record);

    int updateByPrimaryKey(CmConscusthisCipherPO record);
}