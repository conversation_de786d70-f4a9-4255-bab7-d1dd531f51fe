package com.howbuy.crm.account.dao.po.commvisit;

import java.util.Date;

/**
 * 最新拜访表
 */
public class CsVisitNewestPO {
    /**
    * 主键编号
    */
    private String id;

    /**
    * 投顾客户号
    */
    private String conscustno;

    /**
    * 沟通内容
    */
    private String commcontent;

    /**
    * 投顾预约编码
    */
    private String consbookingid;

    /**
    * 拜访方式 1:电话, 2:见面, 3:参会, 4:短信, 5:邮件, 6:微信
    */
    private String visittype;

    /**
    * 拜访分类 {0:正常客户,1:失联客户,2:黑名单,3:屏蔽客户}
    */
    private String visitclassify;

    /**
    * 可修改标识 1:内容可编辑, 0:内容不可编辑
    */
    private String modifyflag;

    /**
    * 创建人
    */
    private String creator;

    /**
    * 创建日期
    */
    private Date credt;

    /**
    * 修改人
    */
    private String modifier;

    /**
    * 修改日期
    */
    private Date moddt;

    /**
    * 时间戳
    */
    private Date stimestamp;

    /**
    * 历史数据标识 1:沟通记录表, 0:拜访表
    */
    private String hisflag;

    /**
    * 历史数据主键编号
    */
    private String hisid;

    public String getId() {
        return id;
    }

    public void setId(String id) {
        this.id = id;
    }

    public String getConscustno() {
        return conscustno;
    }

    public void setConscustno(String conscustno) {
        this.conscustno = conscustno;
    }

    public String getCommcontent() {
        return commcontent;
    }

    public void setCommcontent(String commcontent) {
        this.commcontent = commcontent;
    }

    public String getConsbookingid() {
        return consbookingid;
    }

    public void setConsbookingid(String consbookingid) {
        this.consbookingid = consbookingid;
    }

    public String getVisittype() {
        return visittype;
    }

    public void setVisittype(String visittype) {
        this.visittype = visittype;
    }

    public String getVisitclassify() {
        return visitclassify;
    }

    public void setVisitclassify(String visitclassify) {
        this.visitclassify = visitclassify;
    }

    public String getModifyflag() {
        return modifyflag;
    }

    public void setModifyflag(String modifyflag) {
        this.modifyflag = modifyflag;
    }

    public String getCreator() {
        return creator;
    }

    public void setCreator(String creator) {
        this.creator = creator;
    }

    public Date getCredt() {
        return credt;
    }

    public void setCredt(Date credt) {
        this.credt = credt;
    }

    public String getModifier() {
        return modifier;
    }

    public void setModifier(String modifier) {
        this.modifier = modifier;
    }

    public Date getModdt() {
        return moddt;
    }

    public void setModdt(Date moddt) {
        this.moddt = moddt;
    }

    public Date getStimestamp() {
        return stimestamp;
    }

    public void setStimestamp(Date stimestamp) {
        this.stimestamp = stimestamp;
    }

    public String getHisflag() {
        return hisflag;
    }

    public void setHisflag(String hisflag) {
        this.hisflag = hisflag;
    }

    public String getHisid() {
        return hisid;
    }

    public void setHisid(String hisid) {
        this.hisid = hisid;
    }
}