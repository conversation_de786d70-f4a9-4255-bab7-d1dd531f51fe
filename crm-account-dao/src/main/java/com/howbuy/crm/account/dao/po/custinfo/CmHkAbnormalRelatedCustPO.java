package com.howbuy.crm.account.dao.po.custinfo;

import java.util.Date;
import lombok.Data;

/**
 * @description: (香港异常客户待关联表)
 * <AUTHOR>
 * @date 2023/12/11 10:57
 * @since JDK 1.8
 */
/**
    * 香港异常客户待关联表
    */
@Data
public class CmHkAbnormalRelatedCustPO {
    /**
    * 异常客户待关联id
    */
    private String id;

    /**
    * 异常客户数据ID
    */
    private String abnormalId;

    /**
    * 客户号
    */
    private String custNo;

    /**
    * 客户名称
    */
    private String custName;

    /**
    * 投资者类型
    */
    private String investType;

    /**
    * 手机地区码
    */
    private String mobileAreaCode;

    /**
    * 手机号摘要
    */
    private String mobileDigest;

    /**
    * 手机号掩码
    */
    private String mobileMask;

    /**
    * 手机号密文
    */
    private String mobileCipher;

    /**
    * 证件地区码
    */
    private String idSignAreaCode;

    /**
    * 证件类型
    */
    private String idType;

    /**
    * 证件号码摘要
    */
    private String idNoDigest;

    /**
    * 证件号码掩码
    */
    private String idNoMask;

    /**
    * 证件号码密文
    */
    private String idNoCipher;

    /**
    * 一账通号
    */
    private String hboneNo;

    /**
    * 香港客户号
    */
    private String hkTxAcctNo;

    /**
    * 客户所属投顾
    */
    private String consCode;

    /**
    * 创建人
    */
    private String creator;

    /**
    * 创建时间
    */
    private Date createTimestamp;

    /**
    * 修改人
    */
    private String modifier;

    /**
    * 修改时间
    */
    private Date modifyTimestamp;
}