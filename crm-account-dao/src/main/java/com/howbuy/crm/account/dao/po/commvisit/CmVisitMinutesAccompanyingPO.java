package com.howbuy.crm.account.dao.po.commvisit;

import java.util.Date;
import lombok.Getter;
import lombok.Setter;

/**
 * 陪访人记录
 */
@Getter
@Setter
public class CmVisitMinutesAccompanyingPO {
    /**
    * 主键
    */
    private String id;

    /**
    * 拜访纪要ID
    */
    private String visitMinutesId;

    /**
    * 陪访人类型，1-项目经理 2-主管 3-总部业资 4-其他
    */
    private String accompanyingType;

    /**
    * 陪访人用户ID
    */
    private String accompanyingUserId;

    /**
    * 陪访人层级
    */
    private String accompanyingUserLevel;

    /**
    * 本次陪访概要经验或教训，陪访人填写的本次陪访概要经验或教训
    */
    private String accompanySummary;

    /**
    * 该客户下阶段工作的建议，陪访人填写的该客户下阶段工作的建议
    */
    private String accompanySuggestion;

    /**
    * 填写时间，陪访人填写反馈时间
    */
    private Date fillTime;

    /**
    * 创建人
    */
    private String creator;

    /**
    * 创建时间
    */
    private Date createTime;

    /**
    * 修改人，最后修改人ID
    */
    private String modifier;

    /**
    * 修改时间，最后修改时间
    */
    private Date modifyTime;
}