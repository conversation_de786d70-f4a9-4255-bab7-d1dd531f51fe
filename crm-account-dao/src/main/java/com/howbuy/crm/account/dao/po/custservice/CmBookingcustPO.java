package com.howbuy.crm.account.dao.po.custservice;

import java.util.Date;
import lombok.Data;

/**
 * @description: (请在此添加描述)
 * <AUTHOR>
 * @date 2023/12/28 18:00
 * @since JDK 1.8
 */
/**
    * 预约客户
    */
@Data
public class CmBookingcustPO {
    /**
    * ID
    */
    private Long id;

    /**
    * 预约时间
    */
    private String bookingdt;

    /**
    * 姓名
    */
    private String custname;

    /**
    * 预约类型(1:私募预约 ;2:合作预约)
    */
    private String bookingtype;

    /**
    * 活动类型
(
"1":"我爱卡","2":"我要牛基","3":"新闻收集","4":"基金诊断","5":"货币基金导购","6":"2012投资指南","7":"掌上网预约","8":"专题预约","9":"UCWEB","10":"联想无线","11":"腾讯财经","12":"微信产品预约","13":"人物专访预约" ,
"101":"私募基金类","102":"公募基金类","103":"固定收益类","104":"私募股权类","105":"其它","106":"手机端固定收益类","107":"--","108":"wap固定收益类","118":"好买分销","117":"好臻分销"
)
    */
    private String activitytype;

    /**
    * 渠道(合作预约需求)

    */
    private String channelcode;

    /**
    * 预约内容
    */
    private String bookingcontent;

    /**
    * 来源地址
    */
    private String sourceaddr;

    /**
    * 处理状态（0：未处理 ，1：已处理）
    */
    private String handlestat;

    /**
    * 投顾客户号
    */
    private String conscustno;

    /**
    * 记录状态( 0：有效 ，1 ：删除 ；2：临时删除)
    */
    private String recstat;

    /**
    * 创建人
    */
    private String creator;

    /**
    * 修改人
    */
    private String modifier;

    /**
    * 记录创建日期
    */
    private String credt;

    /**
    * 记录修改日期
    */
    private String moddt;

    private Date syncDate;

    private Short validateflag;

    private Short fraudflag;

    private String fraudinfo;

    private Short readflag;

    /**
    * 预约流水号
    */
    private String bookingserialno;

    /**
    * 预约详细时间
    */
    private String bookingdetaildt;

    /**
    * 无线渠道（1:掌基、2:臻财、3:M站）
    */
    private String wirelessChannel;

    /**
    * 合规状态（N：否 ，Y：是）
    */
    private String qualifiedStatus;

    /**
    * 可联系开始时间
    */
    private String starthour;

    /**
    * 可联系截止时间
    */
    private String endhour;

    /**
    * 开交易户网点
    */
    private String regOutletCode;

    /**
    * 来源系统：1-CMS;2-ACC
    */
    private String sourceSys;

    /**
    * 一账通
    */
    private String hboneNo;

    /**
    * 注册网点
    */
    private String hboneRegOutletCode;

    /**
    * 客户提交高端KYC问卷的网点号
    */
    private String outletCode;

    /**
    * 手机号掩码
    */
    private String mobileMask;

    /**
    * 手机号摘要
    */
    private String mobileDigest;

    /**
    * 手机号密文
    */
    private String mobileCipher;

    /**
    * 邮箱掩码
    */
    private String emailMask;

    /**
    * 邮箱摘要
    */
    private String emailDigest;

    /**
    * 邮箱密文
    */
    private String emailCipher;

    /**
    * 分销渠道 1:好买,2:好臻,3:香港
    */
    private String discode;
}