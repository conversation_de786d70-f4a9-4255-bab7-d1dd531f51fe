package com.howbuy.crm.account.dao.dto.commvisit;

import lombok.Getter;
import lombok.Setter;

/**
 * @description: 客户沟通记录查询DTO
 * <AUTHOR>
 * @date 2025-04-08 10:26:00
 * @since JDK 1.8
 */
@Getter
@Setter
public class CommunicateQueryDTO {
    
    /**
     * 投顾客户号
     */
    private String consCustNo;
    
    /**
     * 查询开始时间
     */
    private String beginTime;
    
    /**
     * 查询结束时间
     */
    private String endTime;
    
    /**
     * 拜访日期开始
     */
    private String visitDateStart;
    
    /**
     * 拜访日期结束
     */
    private String visitDateEnd;
    
    /**
     * 创建人
     */
    private String creator;
    
    /**
     * 分页页码
     */
    private Integer pageNo;
    
    /**
     * 分页大小
     */
    private Integer pageSize;
} 