package com.howbuy.crm.account.dao.po.commvisit;

import lombok.Getter;
import lombok.Setter;
import java.util.Date;

/**
 * @description: 客户沟通记录PO
 * <AUTHOR>
 * @date 2025-04-08 10:26:00
 * @since JDK 1.8
 */
@Getter
@Setter
public class CsCommVisitPO {
    
    /**
     * 沟通记录ID
     */
    private String id;
    
    /**
     * 创建时间
     */
    private Date createTime;
    
    /**
     * 拜访日期
     */
    private Date visitDt;
    
    /**
     * 创建人ID
     */
    private String creator;
    
    /**
     * 创建人姓名
     */
    private String creatorName;
    
    /**
     * 沟通内容
     */
    private String commcontent;
    
    /**
     * 沟通方式
     */
    private String visittype;
    
    /**
     * 沟通类型
     */
    private String consulttype;
    
    /**
     * 投资意向
     */
    private String investintent;
    
    /**
     * 下次拜访时间
     */
    private Date nextdt;
    
    /**
     * 下次拜访方式
     */
    private String nextvisittype;
    
    /**
     * 下次拜访内容
     */
    private String nextvisitcontent;
    
    /**
     * 预约开始时间
     */
    private String nextstarttime;
    
    /**
     * 预约结束时间
     */
    private String nextendtime;
    
    /**
     * 是否合规标识
     */
    private String complianceFlag;
    
    /**
     * 拜访目的
     */
    private String visitPurpose;
    
    /**
     * 拜访纪要ID
     */
    private String visitMinutesId;
} 