package com.howbuy.crm.account.dao.mapper.commvisit;

import com.howbuy.crm.account.dao.po.commvisit.CmVisitMinutesAccompanyingPO;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * @description: 拜访纪要陪访人Mapper
 * <AUTHOR>
 * @date 2025-04-08 10:26:00
 * @since JDK 1.8
 */
public interface CmVisitMinutesAccompanyingMapper {
    
    /**
     * 根据拜访纪要ID查询陪访人列表
     */
    List<CmVisitMinutesAccompanyingPO> selectByVisitMinutesId(@Param("visitMinutesId") String visitMinutesId);
    
    /**
     * 根据ID查询陪访人信息
     */
    CmVisitMinutesAccompanyingPO selectById(@Param("id") String id);
    
    /**
     * 更新陪访人反馈信息
     */
    int updateFeedback(CmVisitMinutesAccompanyingPO po);
    
    /**
     * 根据拜访纪要ID删除陪访人
     */
    int deleteByVisitMinutesId(@Param("visitMinutesId") String visitMinutesId);
    
    /**
     * 新增陪访人
     */
    int insert(CmVisitMinutesAccompanyingPO po);
}