package com.howbuy.crm.account.dao.mapper.custinfo;

import com.howbuy.crm.account.dao.po.custinfo.CmHboneAbnormalCustInfoPO;
import org.apache.ibatis.annotations.Mapper;

/**
 * @description: (请在此添加描述)
 * <AUTHOR>
 * @date 2023/12/21 15:48
 * @since JDK 1.8
 */
public interface CmHboneAbnormalCustInfoMapper {
    int deleteByPrimaryKey(String id);

    int insert(CmHboneAbnormalCustInfoPO record);

    int insertSelective(CmHboneAbnormalCustInfoPO record);

    CmHboneAbnormalCustInfoPO selectByPrimaryKey(String id);

    int updateByPrimaryKeySelective(CmHboneAbnormalCustInfoPO record);

    int updateByPrimaryKey(CmHboneAbnormalCustInfoPO record);
}