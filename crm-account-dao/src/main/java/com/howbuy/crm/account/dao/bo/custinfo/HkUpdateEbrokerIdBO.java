/**
 * Copyright (c) 2024, <PERSON><PERSON><PERSON><PERSON> HOWBUY INVESTMENT MANAGEMENT Co., Ltd.
 * All right reserved.
 * <p>
 * THIS IS UNPUBLISHED PROPRIETARY SOURCE CODE OF HOWBUY INVESTMENT
 * MANAGEMENT CO., LTD.  THE CONTENTS OF THIS FILE MAY NOT BE DISCLOSED
 * TO THIRD PARTIES, COPIED OR DUPLICATED IN ANY FORM, IN WHOLE OR IN PART,
 * WITHOUT THE PRIOR WRITTEN PERMISSION OF HOWBUY INVESTMENT MANAGEMENT
 * CO., LTD.
 */
package com.howbuy.crm.account.dao.bo.custinfo;

import lombok.Getter;
import lombok.Setter;

/**
 * @description: (请在此添加描述)
 * <AUTHOR>
 * @date 2024/6/4 10:28
 * @since JDK 1.8
 */
@Getter
@Setter
public class HkUpdateEbrokerIdBO {

    /**
     * hkCustNo
     */
    private String hkCustNo;

    /**
     * ebrokerId
     */
    private String ebrokerId;

}