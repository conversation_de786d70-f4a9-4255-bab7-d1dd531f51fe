package com.howbuy.crm.account.dao.po.organization;

public class HbOrganizationPO {
    private Object orgcode;

    private Object orgname;

    private Object englishname;

    private Object parentorgcode;

    private Object sort;

    private Object orgtype;

    private Object status;

    private Object showflag;

    private Object province;

    private Object provincename;

    private Object city;

    private Object cityname;

    private Object area;

    private Object areaname;

    private Object address;

    private Object recstat;

    private Object checkflag;

    private Object telno;

    private Object fax;

    private Object creator;

    private Object modifier;

    private Object checker;

    private Object credate;

    private Object moddate;

    private Object checkdate;

    private Object syscode;

    private Object sysname;

    public Object getOrgcode() {
        return orgcode;
    }

    public void setOrgcode(Object orgcode) {
        this.orgcode = orgcode;
    }

    public Object getOrgname() {
        return orgname;
    }

    public void setOrgname(Object orgname) {
        this.orgname = orgname;
    }

    public Object getEnglishname() {
        return englishname;
    }

    public void setEnglishname(Object englishname) {
        this.englishname = englishname;
    }

    public Object getParentorgcode() {
        return parentorgcode;
    }

    public void setParentorgcode(Object parentorgcode) {
        this.parentorgcode = parentorgcode;
    }

    public Object getSort() {
        return sort;
    }

    public void setSort(Object sort) {
        this.sort = sort;
    }

    public Object getOrgtype() {
        return orgtype;
    }

    public void setOrgtype(Object orgtype) {
        this.orgtype = orgtype;
    }

    public Object getStatus() {
        return status;
    }

    public void setStatus(Object status) {
        this.status = status;
    }

    public Object getShowflag() {
        return showflag;
    }

    public void setShowflag(Object showflag) {
        this.showflag = showflag;
    }

    public Object getProvince() {
        return province;
    }

    public void setProvince(Object province) {
        this.province = province;
    }

    public Object getProvincename() {
        return provincename;
    }

    public void setProvincename(Object provincename) {
        this.provincename = provincename;
    }

    public Object getCity() {
        return city;
    }

    public void setCity(Object city) {
        this.city = city;
    }

    public Object getCityname() {
        return cityname;
    }

    public void setCityname(Object cityname) {
        this.cityname = cityname;
    }

    public Object getArea() {
        return area;
    }

    public void setArea(Object area) {
        this.area = area;
    }

    public Object getAreaname() {
        return areaname;
    }

    public void setAreaname(Object areaname) {
        this.areaname = areaname;
    }

    public Object getAddress() {
        return address;
    }

    public void setAddress(Object address) {
        this.address = address;
    }

    public Object getRecstat() {
        return recstat;
    }

    public void setRecstat(Object recstat) {
        this.recstat = recstat;
    }

    public Object getCheckflag() {
        return checkflag;
    }

    public void setCheckflag(Object checkflag) {
        this.checkflag = checkflag;
    }

    public Object getTelno() {
        return telno;
    }

    public void setTelno(Object telno) {
        this.telno = telno;
    }

    public Object getFax() {
        return fax;
    }

    public void setFax(Object fax) {
        this.fax = fax;
    }

    public Object getCreator() {
        return creator;
    }

    public void setCreator(Object creator) {
        this.creator = creator;
    }

    public Object getModifier() {
        return modifier;
    }

    public void setModifier(Object modifier) {
        this.modifier = modifier;
    }

    public Object getChecker() {
        return checker;
    }

    public void setChecker(Object checker) {
        this.checker = checker;
    }

    public Object getCredate() {
        return credate;
    }

    public void setCredate(Object credate) {
        this.credate = credate;
    }

    public Object getModdate() {
        return moddate;
    }

    public void setModdate(Object moddate) {
        this.moddate = moddate;
    }

    public Object getCheckdate() {
        return checkdate;
    }

    public void setCheckdate(Object checkdate) {
        this.checkdate = checkdate;
    }

    public Object getSyscode() {
        return syscode;
    }

    public void setSyscode(Object syscode) {
        this.syscode = syscode;
    }

    public Object getSysname() {
        return sysname;
    }

    public void setSysname(Object sysname) {
        this.sysname = sysname;
    }
}