/**
 * Copyright (c) 2024, <PERSON><PERSON><PERSON><PERSON> HOWBUY INVESTMENT MANAGEMENT Co., Ltd.
 * All right reserved.
 * <p>
 * THIS IS UNPUBLISHED PROPRIETARY SOURCE CODE OF HOWBUY INVESTMENT
 * MANAGEMENT CO., LTD.  THE CONTENTS OF THIS FILE MAY NOT BE DISCLOSED
 * TO THIRD PARTIES, COPIED OR DUPLICATED IN ANY FORM, IN WHOLE OR IN PART,
 * WITHOUT THE PRIOR WRITTEN PERMISSION OF HOWBUY INVESTMENT MANAGEMENT
 * CO., LTD.
 */
package com.howbuy.crm.account.dao.po.consultantexp;

import java.math.BigDecimal;
import java.util.Date;

/**
 * @description: (请在此添加描述)
 * <AUTHOR>
 * @date 2024/11/14 18:33
 * @since JDK 1.8
 */
public class CmConsultantExpModifyFlagPO {
    /**
     * 主键
     */
    private BigDecimal id;

    /**
     * 投顾编号
     */
    private String userid;

    /**
     * 投顾姓名-人工修改标识_1是0否
     */
    private String consnameFlag;

    /**
     * 省市区-人工修改标识_1是0否
     */
    private String citycodeFlag;

    /**
     * 业务中心-人工修改标识_1是0否
     */
    private String centerOrgFlag;

    /**
     * 员工状态-人工修改标识_1是0否
     */
    private String worktypeFlag;

    /**
     * 在职状态-人工修改标识_1是0否
     */
    private String workstateFlag;

    /**
     * 当月职级-人工修改标识_1是0否
     */
    private String curmonthlevelFlag;

    /**
     * 层级-人工修改标识_1是0否
     */
    private String userlevelFlag;

    /**
     * 当月薪资-人工修改标识_1是0否
     */
    private String curmonthsalaryFlag;

    /**
     * 副职-人工修改标识_1是0否
     */
    private String subpositionsFlag;

    /**
     * 入职日期-人工修改标识_1是0否
     */
    private String startdtFlag;

    /**
     * 入职职级-人工修改标识_1是0否
     */
    private String startlevelFlag;

    /**
     * 入职薪资-人工修改标识_1是0否
     */
    private String salaryFlag;

    /**
     * 转正日期-人工修改标识_1是0否
     */
    private String regulardtFlag;

    /**
     * 转正职级-人工修改标识_1是0否
     */
    private String regularlevelFlag;

    /**
     * 转正薪资-人工修改标识_1是0否
     */
    private String regularsalaryFlag;

    /**
     * 离职日期-人工修改标识_1是0否
     */
    private String quitdtFlag;

    /**
     * 离职职级-人工修改标识_1是0否
     */
    private String quitlevelFlag;

    /**
     * 离职薪资-人工修改标识_1是0否
     */
    private String quitsalaryFlag;

    /**
     * 上家公司-人工修改标识_1是0否
     */
    private String backgroundFlag;

    /**
     * 背景来源-人工修改标识_1是0否
     */
    private String sourceFlag;

    /**
     * 上家工作月份数-人工修改标识_1是0否
     */
    private String beforepositionageFlag;

    /**
     * 招聘经理-人工修改标识_1是0否
     */
    private String recruitFlag;

    /**
     * 推荐人-人工修改标识_1是0否
     */
    private String recommendFlag;

    /**
     * 推荐人工号-人工修改标识_1是0否
     */
    private String recommendusernoFlag;

    private String recommendtypeFlag;

    private String creator;

    private String modifier;

    private Date credt;

    private Date moddt;

    /**
     * 3M日期-人工修改标识_1是0否
     */
    private String dt3mFlag;

    /**
     * 12M日期-人工修改标识_1是0否
     */
    private String dt12mFlag;

    public BigDecimal getId() {
        return id;
    }

    public void setId(BigDecimal id) {
        this.id = id;
    }

    public String getUserid() {
        return userid;
    }

    public void setUserid(String userid) {
        this.userid = userid;
    }

    public String getConsnameFlag() {
        return consnameFlag;
    }

    public void setConsnameFlag(String consnameFlag) {
        this.consnameFlag = consnameFlag;
    }

    public String getCitycodeFlag() {
        return citycodeFlag;
    }

    public void setCitycodeFlag(String citycodeFlag) {
        this.citycodeFlag = citycodeFlag;
    }

    public String getCenterOrgFlag() {
        return centerOrgFlag;
    }

    public void setCenterOrgFlag(String centerOrgFlag) {
        this.centerOrgFlag = centerOrgFlag;
    }

    public String getWorktypeFlag() {
        return worktypeFlag;
    }

    public void setWorktypeFlag(String worktypeFlag) {
        this.worktypeFlag = worktypeFlag;
    }

    public String getWorkstateFlag() {
        return workstateFlag;
    }

    public void setWorkstateFlag(String workstateFlag) {
        this.workstateFlag = workstateFlag;
    }

    public String getCurmonthlevelFlag() {
        return curmonthlevelFlag;
    }

    public void setCurmonthlevelFlag(String curmonthlevelFlag) {
        this.curmonthlevelFlag = curmonthlevelFlag;
    }

    public String getUserlevelFlag() {
        return userlevelFlag;
    }

    public void setUserlevelFlag(String userlevelFlag) {
        this.userlevelFlag = userlevelFlag;
    }

    public String getCurmonthsalaryFlag() {
        return curmonthsalaryFlag;
    }

    public void setCurmonthsalaryFlag(String curmonthsalaryFlag) {
        this.curmonthsalaryFlag = curmonthsalaryFlag;
    }

    public String getSubpositionsFlag() {
        return subpositionsFlag;
    }

    public void setSubpositionsFlag(String subpositionsFlag) {
        this.subpositionsFlag = subpositionsFlag;
    }

    public String getStartdtFlag() {
        return startdtFlag;
    }

    public void setStartdtFlag(String startdtFlag) {
        this.startdtFlag = startdtFlag;
    }

    public String getStartlevelFlag() {
        return startlevelFlag;
    }

    public void setStartlevelFlag(String startlevelFlag) {
        this.startlevelFlag = startlevelFlag;
    }

    public String getSalaryFlag() {
        return salaryFlag;
    }

    public void setSalaryFlag(String salaryFlag) {
        this.salaryFlag = salaryFlag;
    }

    public String getRegulardtFlag() {
        return regulardtFlag;
    }

    public void setRegulardtFlag(String regulardtFlag) {
        this.regulardtFlag = regulardtFlag;
    }

    public String getRegularlevelFlag() {
        return regularlevelFlag;
    }

    public void setRegularlevelFlag(String regularlevelFlag) {
        this.regularlevelFlag = regularlevelFlag;
    }

    public String getRegularsalaryFlag() {
        return regularsalaryFlag;
    }

    public void setRegularsalaryFlag(String regularsalaryFlag) {
        this.regularsalaryFlag = regularsalaryFlag;
    }

    public String getQuitdtFlag() {
        return quitdtFlag;
    }

    public void setQuitdtFlag(String quitdtFlag) {
        this.quitdtFlag = quitdtFlag;
    }

    public String getQuitlevelFlag() {
        return quitlevelFlag;
    }

    public void setQuitlevelFlag(String quitlevelFlag) {
        this.quitlevelFlag = quitlevelFlag;
    }

    public String getQuitsalaryFlag() {
        return quitsalaryFlag;
    }

    public void setQuitsalaryFlag(String quitsalaryFlag) {
        this.quitsalaryFlag = quitsalaryFlag;
    }

    public String getBackgroundFlag() {
        return backgroundFlag;
    }

    public void setBackgroundFlag(String backgroundFlag) {
        this.backgroundFlag = backgroundFlag;
    }

    public String getSourceFlag() {
        return sourceFlag;
    }

    public void setSourceFlag(String sourceFlag) {
        this.sourceFlag = sourceFlag;
    }

    public String getBeforepositionageFlag() {
        return beforepositionageFlag;
    }

    public void setBeforepositionageFlag(String beforepositionageFlag) {
        this.beforepositionageFlag = beforepositionageFlag;
    }

    public String getRecruitFlag() {
        return recruitFlag;
    }

    public void setRecruitFlag(String recruitFlag) {
        this.recruitFlag = recruitFlag;
    }

    public String getRecommendFlag() {
        return recommendFlag;
    }

    public void setRecommendFlag(String recommendFlag) {
        this.recommendFlag = recommendFlag;
    }

    public String getRecommendusernoFlag() {
        return recommendusernoFlag;
    }

    public void setRecommendusernoFlag(String recommendusernoFlag) {
        this.recommendusernoFlag = recommendusernoFlag;
    }

    public String getRecommendtypeFlag() {
        return recommendtypeFlag;
    }

    public void setRecommendtypeFlag(String recommendtypeFlag) {
        this.recommendtypeFlag = recommendtypeFlag;
    }

    public String getCreator() {
        return creator;
    }

    public void setCreator(String creator) {
        this.creator = creator;
    }

    public String getModifier() {
        return modifier;
    }

    public void setModifier(String modifier) {
        this.modifier = modifier;
    }

    public Date getCredt() {
        return credt;
    }

    public void setCredt(Date credt) {
        this.credt = credt;
    }

    public Date getModdt() {
        return moddt;
    }

    public void setModdt(Date moddt) {
        this.moddt = moddt;
    }

    public String getDt3mFlag() {
        return dt3mFlag;
    }

    public void setDt3mFlag(String dt3mFlag) {
        this.dt3mFlag = dt3mFlag;
    }

    public String getDt12mFlag() {
        return dt12mFlag;
    }

    public void setDt12mFlag(String dt12mFlag) {
        this.dt12mFlag = dt12mFlag;
    }
}