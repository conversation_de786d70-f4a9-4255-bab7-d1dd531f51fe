package com.howbuy.crm.account.dao.mapper.custinfo;

import com.howbuy.crm.account.dao.po.custinfo.CmConscustOperationPO;
import org.apache.ibatis.annotations.Mapper;

/**
 * <AUTHOR>
 * @description: (请在此添加描述)
 * @date 2023/12/27 11:08
 * @since JDK 1.8
 */
@Mapper
public interface CmConscustOperationMapper {
    int deleteByPrimaryKey(String operationid);

    int insert(CmConscustOperationPO record);

    int insertSelective(CmConscustOperationPO record);

    CmConscustOperationPO selectByPrimaryKey(String operationid);

    int updateByPrimaryKeySelective(CmConscustOperationPO record);

    int updateByPrimaryKey(CmConscustOperationPO record);
}