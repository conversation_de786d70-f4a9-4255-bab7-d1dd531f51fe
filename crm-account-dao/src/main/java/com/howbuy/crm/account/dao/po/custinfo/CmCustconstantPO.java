package com.howbuy.crm.account.dao.po.custinfo;

import java.util.Date;
import lombok.Data;

/**
 * @description: (请在此添加描述)
 * <AUTHOR>
 * @date 2023/12/26 20:30
 * @since JDK 1.8
 */
/**
    * 客户投顾关系表
    */
@Data
public class CmCustconstantPO {
    /**
    * 客户号
    */
    private String custno;

    /**
    * 投资顾问代码
    */
    private String conscode;

    /**
    * 开始日期
    */
    private String startdt;

    /**
    * 结束日期
    */
    private String enddt;

    /**
    * 备注
    */
    private String memo;

    /**
    * 记录状态 1-有效
    */
    private String recstat;

    /**
    * 复核标志
    */
    private String checkflag;

    /**
    * 创建人
    */
    private String creator;

    /**
    * 修改人
    */
    private String modifier;

    /**
    * 复核人
    */
    private String checker;

    /**
    * 记录创建日期
    */
    private String credt;

    /**
    * 记录修改日期
    */
    private String moddt;

    /**
    * 绑定（分配）时间（带时分秒）
    */
    private Date binddate;

    /**
    * 上一条分配id
    */
    private String beforehisid;

    /**
    * 操作时间
    */
    private Date operateDate;
}