package com.howbuy.crm.account.dao.po.constant;

import java.math.BigDecimal;
import java.util.Date;
import lombok.Data;

/**
 * @description: (请在此添加描述)
 * <AUTHOR>
 * @date 2023/12/26 15:44
 * @since JDK 1.8
 */

/**
 * 系统常量表
 */
@Data
public class HbConstantPO {
    /**
     * ID
     */
    private String id;

    /**
     * 常量类别
     */
    private String typecode;

    /**
     * 类别说明
     */
    private String typedesc;

    /**
     * 编码说明
     */
    private String codedesc;

    /**
     * 常量编码
     */
    private String constcode;

    /**
     * 常量描述
     */
    private String constdesc;

    /**
     * 层次(排序)
     */
    private BigDecimal constlevel;

    /**
     * 是否有效
     */
    private String isvalid;

    /**
     * 别名
     */
    private String constalias;

    /**
     * 扩展1
     */
    private String constext1;

    /**
     * 扩展2
     */
    private String constext2;

    /**
     * 扩展3
     */
    private String constext3;

    /**
     * 创建者
     */
    private String creator;

    /**
     * 修改者
     */
    private String modifier;

    /**
     * 审核者
     */
    private String checker;

    /**
     * 创建时间
     */
    private Date credate;

    /**
     * 修改时间
     */
    private Date moddate;

    /**
     * 审核时间
     */
    private Date checkdate;

    /**
     * 变动标志
     */
    private String updateid;

    /**
     * 来源标志
     */
    private String resourceid;
}