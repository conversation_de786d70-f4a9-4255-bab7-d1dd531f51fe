/**
 * Copyright (c) 2023, <PERSON><PERSON><PERSON><PERSON> HOWBUY INVESTMENT MANAGEMENT Co., Ltd.
 * All right reserved.
 * <p>
 * THIS IS UNPUBLISHED PROPRIETARY SOURCE CODE OF HOWBUY INVESTMENT
 * MANAGEMENT CO., LTD.  THE CONTENTS OF THIS FILE MAY NOT BE DISCLOSED
 * TO THIRD PARTIES, COPIED OR DUPLICATED IN ANY FORM, IN WHOLE OR IN PART,
 * WITHOUT THE PRIOR WRITTEN PERMISSION OF HOWBUY INVESTMENT MANAGEMENT
 * CO., LTD.
 */
package com.howbuy.crm.account.dao.req.custinfo;

import com.howbuy.crm.account.dao.po.custinfo.CmConscustCipherPO;
import com.howbuy.crm.account.dao.po.custinfo.CmConscustPO;
import lombok.Data;

/**
 * @description: (操作投顾客户 IC 相关 划转 )
 * <AUTHOR>
 * @date 2023/12/19 11:08
 * @since JDK 1.8
 */
@Data
public class ConsCustICReqVO {

    private String operationId;

    /**************************CM_CONSCUST_OPERATION 使用 begin************************************************/
    /**
     * IC表  审核标志
     */
    private String status;
    /**
     * 是否申请划转 0-否 1-是
     */
    private String transferapply;
    /**
     * 处理状态:1-待处理;2-已处理-驳回;3-已处理-同意' 等同划转日志表cm_transf_log
     */
    private String opstatus;

    /**
     * 操作来源：1-CRM，2-APP
     */
    private String opensource;

    /**
     * 来源渠道（取值有：小程序、APP分享）
     */
    private String sourcechannel;

    /**************************CM_CONSCUST_OPERATION 使用 end************************************************/


    /**************************CM_CONSCUST_IC 使用 begin************************************************/

    /**
     * 审核标志（0：待审核，1：审核通过，2：审核不通过）
     */
    private String icCheckFlag;


    /**************************CM_CONSCUST_IC 使用 end************************************************/



}