package com.howbuy.crm.account.dao.po.custinfo;

import java.util.Date;
import lombok.Data;

/**
 * @description: (请在此添加描述)
 * <AUTHOR>
 * @date 2023/12/15 13:32
 * @since JDK 1.8
 */
/**
    * 投顾客户关联流水表
    */
@Data
public class CmHboneHkRelationLogPO {
    /**
    * 投顾客户关联记录
    */
    private String id;

    /**
    * 客户号
    */
    private String custNo;

    /**
    * 关联类型：1-一账通号  2-香港客户号
    */
    private String relationType;

    /**
    * 绑定操作类型：0-绑定;1-解绑
    */
    private String operateType;

    /**
    * 操作人
    */
    private String creator;

    /**
    * 操作时间
    */
    private Date createTimestamp;

    /**
    * 备注说明
    */
    private String remark;

    /**
    * 操作来源 [1-MQ]时，为[异常来源]， [2-菜单页面]时，为菜单名称
    */
    private String operateSource;

    /**
    * 操作通道 1-MQ  2-菜单页面
    */
    private String operateChannel;

    /**
    * 修改人
    */
    private String modifier;

    /**
    * 修改时间
    */
    private Date modifyTimestamp;

    /**
    * 关联的账号 一账通 或香港客户号
    */
    private String acctNo;
}