package com.howbuy.crm.account.dao.mapper.custinfo;

import com.howbuy.crm.account.dao.po.custinfo.CmConscustDeliveryAddressPO;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.math.BigDecimal;
import java.util.List;

@Mapper
public interface CmConscustDeliveryAddressMapper {
    int deleteByPrimaryKey(BigDecimal id);

    int insert(CmConscustDeliveryAddressPO record);

    int insertSelective(CmConscustDeliveryAddressPO record);

    CmConscustDeliveryAddressPO selectByPrimaryKey(BigDecimal id);

    int updateByPrimaryKeySelective(CmConscustDeliveryAddressPO record);

    int updateByPrimaryKey(CmConscustDeliveryAddressPO record);

    /**
     * @description: 查询投顾客户收货地址信息
     * @param custNo 投顾客户号
     * @return java.util.List<com.howbuy.crm.account.dao.po.custinfo.CmConscustDeliveryAddressPO> 投顾客户收货地址信息列表
     * @author: jin.wang03
     * @date: 2024/4/7 16:12
     * @since JDK 1.8
     */
    List<CmConscustDeliveryAddressPO> listCustDeliveryAddressByCustNo(@Param("custNo") String custNo);
}