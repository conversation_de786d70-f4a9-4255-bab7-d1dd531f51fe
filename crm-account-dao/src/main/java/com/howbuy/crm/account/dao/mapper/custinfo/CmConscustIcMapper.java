package com.howbuy.crm.account.dao.mapper.custinfo;

import com.howbuy.crm.account.dao.po.custinfo.CmConscustIcPO;
import org.apache.ibatis.annotations.Mapper;

/**
 * <AUTHOR>
 * @description: (请在此添加描述)
 * @date 2024/1/3 10:02
 * @since JDK 1.8
 */
@Mapper
public interface CmConscustIcMapper {
    int deleteByPrimaryKey(String conscustno);

    int insert(CmConscustIcPO record);

    int insertSelective(CmConscustIcPO record);

    CmConscustIcPO selectByPrimaryKey(String conscustno);

    int updateByPrimaryKeySelective(CmConscustIcPO record);

    int updateByPrimaryKey(CmConscustIcPO record);
}