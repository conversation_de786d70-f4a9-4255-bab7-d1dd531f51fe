package com.howbuy.crm.account.dao.mapper.custinfo;

import com.howbuy.crm.account.dao.po.custinfo.CmConscustCipherPO;
import org.apache.ibatis.annotations.Mapper;

/**
 * @description: (请在此添加描述)
 * <AUTHOR>
 * @date 2023/12/8 13:19
 * @since JDK 1.8
 */
public interface CmConscustCipherMapper {
    int deleteByPrimaryKey(String conscustno);

    int insert(CmConscustCipherPO record);

    int insertSelective(CmConscustCipherPO record);

    CmConscustCipherPO selectByPrimaryKey(String conscustno);

    int updateByPrimaryKeySelective(CmConscustCipherPO record);

    int updateByPrimaryKey(CmConscustCipherPO record);
}