package com.howbuy.crm.account.dao.mapper;

import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

/**
 * <AUTHOR>
 * @description: (获取 sequence 的 mapper)
 * @date 2023/12/8 10:51
 * @since JDK 1.8
 */
public interface CommonMapper {

    /**
     * @description: (根据sequenceName 获取 sequence 的值)
     * @param sequenceName
     * @return java.lang.String
     * @throws
     * @since JDK 1.8
     */
    String getSeqValue(@Param("sequenceName") String sequenceName);

}