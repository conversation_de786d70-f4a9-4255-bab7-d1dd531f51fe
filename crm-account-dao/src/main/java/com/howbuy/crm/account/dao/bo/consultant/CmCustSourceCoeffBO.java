/**
 * Copyright (c) 2024, <PERSON><PERSON><PERSON><PERSON> HOWBUY INVESTMENT MANAGEMENT Co., Ltd.
 * All right reserved.
 * <p>
 * THIS IS UNPUBLISHED PROPRIETARY SOURCE CODE OF HOWBUY INVESTMENT
 * MANAGEMENT CO., LTD.  THE CONTENTS OF THIS FILE MAY NOT BE DISCLOSED
 * TO THIRD PARTIES, COPIED OR DUPLICATED IN ANY FORM, IN WHOLE OR IN PART,
 * WITHOUT THE PRIOR WRITTEN PERMISSION OF HOWBUY INVESTMENT MANAGEMENT
 * CO., LTD.
 */
package com.howbuy.crm.account.dao.bo.consultant;

import lombok.Getter;
import lombok.Setter;
import lombok.ToString;

import java.io.Serializable;
import java.math.BigDecimal;

/**
 * @description: 客户来源系数配置业务对象
 * @author: shijie.wang
 * @date: 2025/6/30 13:25
 * @since JDK 1.8
 */
@Getter
@Setter
@ToString
public class CmCustSourceCoeffBO implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 来源类型
     */
    private String sourceType;

    /**
     * 客户折算系数
     */
    private BigDecimal zbCoeff;

    /**
     * 佣金系数起点
     */
    private String startPoint;

    /**
     * 管理系数-分总
     */
    private BigDecimal manageCoeff;
    /**
     * 管理系数-区副
     */
    private BigDecimal manageCoeffRegionalsubtotal;
    /**
     * 管理系数-区总
     */
    private BigDecimal manageCoeffRegionaltotal;

    /**
     * 存续A
     */
    private String cxa;

    /**
     * 存续B
     */
    private String cxb;
} 