/**
 * Copyright (c) 2024, <PERSON><PERSON><PERSON><PERSON> HOWBUY INVESTMENT MANAGEMENT Co., Ltd.
 * All right reserved.
 * <p>
 * THIS IS UNPUBLISHED PROPRIETARY SOURCE CODE OF HOWBUY INVESTMENT
 * MANAGEMENT CO., LTD.  THE CONTENTS OF THIS FILE MAY NOT BE DISCLOSED
 * TO THIRD PARTIES, COPIED OR DUPLICATED IN ANY FORM, IN WHOLE OR IN PART,
 * WITHOUT THE PRIOR WRITTEN PERMISSION OF HOWBUY INVESTMENT MANAGEMENT
 * CO., LTD.
 */
package com.howbuy.crm.account.dao.req.custinfo;

import com.howbuy.crm.account.dao.req.PageReqVO;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.util.List;

/**
 * @description:(查询客户简单信息 通用 查询对象 vo)
 * @return
 * @author: haoran.zhang
 * @date: 2025/3/5 13:38
 * @since JDK 1.8
 */
@Data
@EqualsAndHashCode(callSuper = true)
public class CustSimpleSearchVO extends PageReqVO {

    /**
     * 投顾编号  列表
     */
    private List<String> consCodeList;

    /**
     * 一账通号 列表
     */
    private List<List<String>> hboneNoList;


    /**
     * 香港交易账号 列表
     */
    private List<List<String>> hkTxAcctNoList;

    /**
     * 手机号摘要
     */
    private String mobileDigest;
}
