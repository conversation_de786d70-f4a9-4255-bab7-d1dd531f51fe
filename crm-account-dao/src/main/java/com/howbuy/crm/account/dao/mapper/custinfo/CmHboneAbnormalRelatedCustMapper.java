package com.howbuy.crm.account.dao.mapper.custinfo;

import com.howbuy.crm.account.dao.po.custinfo.CmHboneAbnormalRelatedCustPO;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * @description: (请在此添加描述)
 * <AUTHOR>
 * @date 2023/12/21 15:48
 * @since JDK 1.8
 */
public interface CmHboneAbnormalRelatedCustMapper {
    int deleteByPrimaryKey(String id);

    int insert(CmHboneAbnormalRelatedCustPO record);

    int insertSelective(CmHboneAbnormalRelatedCustPO record);

    CmHboneAbnormalRelatedCustPO selectByPrimaryKey(String id);

    int updateByPrimaryKeySelective(CmHboneAbnormalRelatedCustPO record);

    int updateByPrimaryKey(CmHboneAbnormalRelatedCustPO record);


    /**
     * @description:(批量插入 异常客户明细列表)
     * @param recordList
     * @return int
     * @author: haoran.zhang
     * @date: 2023/12/12 16:56
     * @since JDK 1.8
     */
    int  batchInsert(@Param("recordList") List<CmHboneAbnormalRelatedCustPO> recordList);
}