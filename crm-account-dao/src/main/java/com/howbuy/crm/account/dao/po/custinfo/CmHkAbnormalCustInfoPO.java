package com.howbuy.crm.account.dao.po.custinfo;

import java.util.Date;

/**
 * 香港客户异常信息表
 */
public class CmHkAbnormalCustInfoPO {
    /**
     * 异常客户数据ID
     */
    private String id;

    /**
     * 消息通知的clientId
     */
    private String messageClientId;

    /**
     * rocketMq的消息ID，全局唯一 （messageClientId 记录的是rocketMq客户端id，没有意义）
     */
    private String messageId;

    /**
     * 香港客户号
     */
    private String hkTxAcctNo;

    /**
     * 客户姓名
     */
    private String custName;

    /**
     * 投资者类型
     */
    private String investType;

    /**
     * 手机地区码
     */
    private String mobileAreaCode;

    /**
     * 手机号摘要
     */
    private String mobileDigest;

    /**
     * 手机号掩码
     */
    private String mobileMask;

    /**
     * 手机号密文
     */
    private String mobileCipher;

    /**
     * 证件地区码
     */
    private String idSignAreaCode;

    /**
     * 证件类型
     */
    private String idType;

    /**
     * 证件号码摘要
     */
    private String idNoDigest;

    /**
     * 证件号码掩码
     */
    private String idNoMask;

    /**
     * 证件号码密文
     */
    private String idNoCipher;

    /**
     * 一账通号
     */
    private String hboneNo;

    /**
     * 异常来源：1-香港注册2-香港开户7-好买开户 [暂时同于  7-分销开户]3-香港客户信息同步4-香港客户号/一账通绑定5-一账通实名
     */
    private String abnormalSource;

    /**
     * 异常描述，按异常类别汇总：1-匹配到多个投顾客户号【香港注册、香港开户】2-匹配到的投顾客户号已被占用 【香港注册】3-手机号相同，证件类型/证件号不匹配 【香港开户】4-证件相同，但手机号不匹配【香港开户】5-CRM重复客户预警【香港客户信息同步】6-同时绑定香港客户号时，投顾客户号/香港客户号被占用 【好买开户、一账通实名】7-香港开户证件与一账通证件不一致 【香港开户】8-两边绑定的投顾客户号不一致【香港客户号/一账通绑定】
     */
    private String abnormalSceneType;

    /**
     * 异常级别：3-高 2-中 1-低
     */
    private String abnormalLevel;

    /**
     * 创建人
     */
    private String creator;

    /**
     * 创建时间
     */
    private Date createTimestamp;

    /**
     * 修改人
     */
    private String modifier;

    /**
     * 修改时间
     */
    private Date modifyTimestamp;

    /**
     * 记录有效状态（1-正常  0-删除）
     */
    private String recStat;

    /**
     * 处理状态：0-未处理 1-已处理 2-无需处理
     */
    private String dealStatus;

    /**
     * 处理人
     */
    private String dealOperator;

    /**
     * 处理意见
     */
    private String dealRemark;

    /**
     * 处理时间
     */
    private Date dealTimestamp;

    public String getId() {
        return id;
    }

    public void setId(String id) {
        this.id = id;
    }

    public String getMessageClientId() {
        return messageClientId;
    }

    public void setMessageClientId(String messageClientId) {
        this.messageClientId = messageClientId;
    }

    public String getHkTxAcctNo() {
        return hkTxAcctNo;
    }

    public void setHkTxAcctNo(String hkTxAcctNo) {
        this.hkTxAcctNo = hkTxAcctNo;
    }

    public String getCustName() {
        return custName;
    }

    public void setCustName(String custName) {
        this.custName = custName;
    }

    public String getInvestType() {
        return investType;
    }

    public void setInvestType(String investType) {
        this.investType = investType;
    }

    public String getMobileAreaCode() {
        return mobileAreaCode;
    }

    public void setMobileAreaCode(String mobileAreaCode) {
        this.mobileAreaCode = mobileAreaCode;
    }

    public String getMobileDigest() {
        return mobileDigest;
    }

    public void setMobileDigest(String mobileDigest) {
        this.mobileDigest = mobileDigest;
    }

    public String getMobileMask() {
        return mobileMask;
    }

    public void setMobileMask(String mobileMask) {
        this.mobileMask = mobileMask;
    }

    public String getMobileCipher() {
        return mobileCipher;
    }

    public void setMobileCipher(String mobileCipher) {
        this.mobileCipher = mobileCipher;
    }

    public String getIdSignAreaCode() {
        return idSignAreaCode;
    }

    public void setIdSignAreaCode(String idSignAreaCode) {
        this.idSignAreaCode = idSignAreaCode;
    }

    public String getIdType() {
        return idType;
    }

    public void setIdType(String idType) {
        this.idType = idType;
    }

    public String getIdNoDigest() {
        return idNoDigest;
    }

    public void setIdNoDigest(String idNoDigest) {
        this.idNoDigest = idNoDigest;
    }

    public String getIdNoMask() {
        return idNoMask;
    }

    public void setIdNoMask(String idNoMask) {
        this.idNoMask = idNoMask;
    }

    public String getIdNoCipher() {
        return idNoCipher;
    }

    public void setIdNoCipher(String idNoCipher) {
        this.idNoCipher = idNoCipher;
    }

    public String getHboneNo() {
        return hboneNo;
    }

    public void setHboneNo(String hboneNo) {
        this.hboneNo = hboneNo;
    }

    public String getAbnormalSource() {
        return abnormalSource;
    }

    public void setAbnormalSource(String abnormalSource) {
        this.abnormalSource = abnormalSource;
    }

    public String getAbnormalSceneType() {
        return abnormalSceneType;
    }

    public void setAbnormalSceneType(String abnormalSceneType) {
        this.abnormalSceneType = abnormalSceneType;
    }

    public String getCreator() {
        return creator;
    }

    public void setCreator(String creator) {
        this.creator = creator;
    }

    public Date getCreateTimestamp() {
        return createTimestamp;
    }

    public void setCreateTimestamp(Date createTimestamp) {
        this.createTimestamp = createTimestamp;
    }

    public String getModifier() {
        return modifier;
    }

    public void setModifier(String modifier) {
        this.modifier = modifier;
    }

    public Date getModifyTimestamp() {
        return modifyTimestamp;
    }

    public void setModifyTimestamp(Date modifyTimestamp) {
        this.modifyTimestamp = modifyTimestamp;
    }

    public String getRecStat() {
        return recStat;
    }

    public void setRecStat(String recStat) {
        this.recStat = recStat;
    }

    public String getDealStatus() {
        return dealStatus;
    }

    public void setDealStatus(String dealStatus) {
        this.dealStatus = dealStatus;
    }

    public String getDealOperator() {
        return dealOperator;
    }

    public void setDealOperator(String dealOperator) {
        this.dealOperator = dealOperator;
    }

    public String getDealRemark() {
        return dealRemark;
    }

    public void setDealRemark(String dealRemark) {
        this.dealRemark = dealRemark;
    }

    public Date getDealTimestamp() {
        return dealTimestamp;
    }

    public void setDealTimestamp(Date dealTimestamp) {
        this.dealTimestamp = dealTimestamp;
    }

    public String getAbnormalLevel() {
        return abnormalLevel;
    }

    public void setAbnormalLevel(String abnormalLevel) {
        this.abnormalLevel = abnormalLevel;
    }

    public String getMessageId() {
        return messageId;
    }

    public void setMessageId(String messageId) {
        this.messageId = messageId;
    }
}