package com.howbuy.crm.account.dao.mapper.custinfo;

import com.howbuy.crm.account.dao.po.custinfo.CmHkAbnormalRelatedCustPO;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * @description: (请在此添加描述)
 * <AUTHOR>
 * @date 2023/12/11 10:57
 * @since JDK 1.8
 */
public interface CmHkAbnormalRelatedCustMapper {
    int deleteByPrimaryKey(String id);

    int insert(CmHkAbnormalRelatedCustPO record);

    int insertSelective(CmHkAbnormalRelatedCustPO record);

    CmHkAbnormalRelatedCustPO selectByPrimaryKey(String id);

    int updateByPrimaryKeySelective(CmHkAbnormalRelatedCustPO record);

    int updateByPrimaryKey(CmHkAbnormalRelatedCustPO record);



    /**
     * @description:(根据香港客户异常信息id 查找 关联的客户列表信息)
     * @param abnormalId
     * @return java.util.List<com.howbuy.crm.account.dao.po.custinfo.CmHkAbnormalRelatedCustPO>
     * @author: haoran.zhang
     * @date: 2023/12/11 11:20
     * @since JDK 1.8
     */
    List<CmHkAbnormalRelatedCustPO> selectListByAbnormalId(@Param("abnormalId") String  abnormalId);


    /**
     * @description:(批量插入 异常客户明细列表)
     * @param recordList
     * @return int
     * @author: haoran.zhang
     * @date: 2023/12/12 16:56
     * @since JDK 1.8
     */
    int  batchInsert(@Param("recordList")  List<CmHkAbnormalRelatedCustPO> recordList);
}