package com.howbuy.crm.account.dao.mapper.custinfo;

import com.howbuy.crm.account.dao.po.custinfo.CmHkAbnormalCustInfoPO;
import org.apache.ibatis.annotations.Mapper;

/**
 * @description: (请在此添加描述)
 * <AUTHOR>
 * @date 2023/12/11 10:57
 * @since JDK 1.8
 */
public interface CmHkAbnormalCustInfoMapper {
    int deleteByPrimaryKey(String id);

    int insert(CmHkAbnormalCustInfoPO record);

    int insertSelective(CmHkAbnormalCustInfoPO record);

    CmHkAbnormalCustInfoPO selectByPrimaryKey(String id);

    int updateByPrimaryKeySelective(CmHkAbnormalCustInfoPO record);

    int updateByPrimaryKey(CmHkAbnormalCustInfoPO record);
}