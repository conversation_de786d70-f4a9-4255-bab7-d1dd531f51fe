package com.howbuy.crm.account.dao.mapper.custinfo;

import com.howbuy.crm.account.dao.po.custinfo.CmConscustIcCipherPO;
import org.apache.ibatis.annotations.Mapper;

/**
 * @description: (请在此添加描述)
 * <AUTHOR>
 * @date 2023/12/26 15:40
 * @since JDK 1.8
 */
@Mapper
public interface CmConscustIcCipherMapper {
    int deleteByPrimaryKey(String conscustno);

    int insert(CmConscustIcCipherPO record);

    int insertSelective(CmConscustIcCipherPO record);

    CmConscustIcCipherPO selectByPrimaryKey(String conscustno);

    int updateByPrimaryKeySelective(CmConscustIcCipherPO record);

    int updateByPrimaryKey(CmConscustIcCipherPO record);
}