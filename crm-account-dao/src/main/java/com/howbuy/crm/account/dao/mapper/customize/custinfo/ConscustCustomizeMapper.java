package com.howbuy.crm.account.dao.mapper.customize.custinfo;

import com.github.pagehelper.Page;
import com.howbuy.crm.account.dao.bo.consperformancecoeff.ConsPerformanceCoeffBO;
import com.howbuy.crm.account.dao.bo.custinfo.CmConsCustSimpleBO;
import com.howbuy.crm.account.dao.bo.custinfo.CmConscustForAnalyseBO;
import com.howbuy.crm.account.dao.bo.custinfo.CmCustHisConsultantBO;
import com.howbuy.crm.account.dao.po.custinfo.CmSourceInfoPO;
import com.howbuy.crm.account.dao.req.custinfo.CustSimpleSearchVO;
import com.howbuy.crm.account.dao.req.custinfo.MatchedCustReqVO;
import com.howbuy.crm.account.dao.req.custinfo.PageCustSimpleReqVO;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * <AUTHOR>
 * @description: (客户信息 自定义mapper)
 * @date 2023/12/8 10:51
 * @since JDK 1.8
 */
public interface ConscustCustomizeMapper {


    /**
     * @param custNo
     * @return java.lang.String
     * @throws
     * @description: (根据客户号查询 一账通号)
     * @since JDK 1.8
     */
    String selectHboneNoByCustNo(@Param("custNo") String custNo);


    /**
     * @param custNo
     * @return java.lang.String
     * @throws
     * @description: (根据客户号查询 投顾consCode)
     * @since JDK 1.8
     */
    String selectConsCodeByCustNo(@Param("custNo") String custNo);


    /**
     * @param investTypeList 投资类型列表
     * @param mobileAreaCode 手机号区号
     * @param mobileDigest   手机号
     * @return com.howbuy.crm.account.dao.po.custinfo.CmConscustPO
     * @throws
     * @description: (根据手机区号 + 手机号 查询客户信息)
     * @since JDK 1.8
     */
    List<CmConscustForAnalyseBO> queryCustListByMobile(
            @Param("investTypeList") List<String> investTypeList,
            @Param("mobileAreaCode") String mobileAreaCode,
            @Param("mobileDigest") String mobileDigest);


    /**
     * @param hboneNo 一账通号
     * @return java.util.List<com.howbuy.crm.account.dao.bo.custinfo.CmConscustForAnalyseBO>
     * @description:(根据 一账通 查询客户信息 。 历史数据原因， 此处返回list. 但是list只有一个元素,使用时业务校验)
     * @author: haoran.zhang
     * @date: 2023/12/25 16:29
     * @since JDK 1.8
     */
    List<CmConscustForAnalyseBO> queryCustListByHboneNo(@Param("hboneNo") String hboneNo);


    /**
     * @param custNoList 客户号列表
     * @return com.howbuy.crm.account.dao.po.custinfo.CmConscustPO
     * @throws
     * @description: (根据客户号列表 查询客户信息列表)
     * @since JDK 1.8
     */
    public List<CmConscustForAnalyseBO> queryCustListByCustNoList(@Param("custNoList") List<String> custNoList);


    /**
     * @param investTypeList 投资类型列表
     * @param idTypeList     证件类型列表
     * @param idSignAreaCode 证件签发地区
     * @param idNoDigest     证件号码
     * @return com.howbuy.crm.account.dao.po.custinfo.CmConscustPO
     * @throws
     * @description: (根据证件类型 + 证件签发地区 + 证件号码 查询客户信息)
     * @since JDK 1.8
     */
    List<CmConscustForAnalyseBO> queryCustListByIdNo(
            @Param("investTypeList") List<String> investTypeList,
            @Param("idTypeList") List<String> idTypeList,
            @Param("idSignAreaCode") String idSignAreaCode,
            @Param("idNoDigest") String idNoDigest);


    /**
     * @param investTypeList
     * @param custName
     * @return java.util.List<com.howbuy.crm.account.dao.bo.custinfo.CmConscustForAnalyseBO>
     * @description:(根据 客户姓名 查询客户信息)
     * @author: haoran.zhang
     * @date: 2024/1/12 18:38
     * @since JDK 1.8
     */
    List<CmConscustForAnalyseBO> queryCustListByCustName(@Param("investTypeList") List<String> investTypeList, @Param("custName") String custName);


    /**
     * @param matchedVo
     * @return java.util.List<com.howbuy.crm.account.dao.bo.custinfo.CmConscustForAnalyseBO>
     * @description:(根据 vo 查询客户信息 注意：属性必须非空 )
     * @author: haoran.zhang
     * @date: 2023/12/14 15:58
     * @since JDK 1.8
     */
    List<CmConscustForAnalyseBO> queryCustListByMatchedVo(MatchedCustReqVO matchedVo);


    /**
     * @param custNo
     * @return com.howbuy.crm.account.dao.po.custinfo.CmConsCustSimpleVO
     * @throws
     * @description: (根据客户号查询客户信息 - 常用简单信息)
     * @since JDK 1.8
     */
    CmConsCustSimpleBO queryCustSimpleInfo(@Param("custNo") String custNo);

    /**
     * @description:(根据vo分页查询投顾客户简单信息)
     * @param reqVO
     * @return java.util.List<com.howbuy.crm.account.dao.bo.custinfo.CmConsCustSimpleBO>
     * <AUTHOR>
     * @date 2024/9/24 18:06
     * @since JDK 1.8
     */
    List<CmConsCustSimpleBO> pageQueryCustSimpleByVo(PageCustSimpleReqVO reqVO);

    /**
     * 根据vo分页查询投顾客户简单信息(投顾所属部门&子部门)
     * @param reqVO 请求参数
     * @return List<CmConsCustSimpleBO>
     */
    List<CmConsCustSimpleBO> pageQueryCustSimpleByVoAndOutletCodes(PageCustSimpleReqVO reqVO);

    /**
     * @param sourceCode
     * @return com.howbuy.crm.account.dao.po.custinfo.CmConscustPO
     * @description:(根据来源编号 查找来源信息)
     * @author: haoran.zhang
     * @date: 2023/12/27 16:46
     * @since JDK 1.8
     */
    CmSourceInfoPO querySourceByCode(@Param("sourceCode") String sourceCode);


    /**
     * @param custNo
     * @param hboneNo
     * @return com.howbuy.crm.account.dao.bo.custinfo.CmConscustForAnalyseBO
     * @description:(根据客户号、一账通号 查询客户信息)
     * @author: haoran.zhang
     * @date: 2024/1/9 11:09
     * @since JDK 1.8
     */
    CmConscustForAnalyseBO queryCustBOByCustNoAndHbone(@Param("custNo") String custNo, @Param("hboneNo") String hboneNo);

    /**
     * @description:根据一账通号查询投顾客户简单信息
     * @param hboneNo
     * @return java.util.List<com.howbuy.crm.account.dao.bo.custinfo.CmConsCustSimpleBO>
     * <AUTHOR>
     * @date 2024/10/9 18:27
     * @since JDK 1.8
     */
    List<CmConsCustSimpleBO> queryCustSimpleByHboneNo(String hboneNo);


    /**
     * @description:(根据查询对象查询投顾客户简单信息)
     * @param reqVO
     * @return java.util.List<com.howbuy.crm.account.dao.bo.custinfo.CmConsCustSimpleBO>
     * @author: haoran.zhang
     * @date: 2025/3/5 13:47
     * @since JDK 1.8
     */
    Page<CmConsCustSimpleBO> queryCustSimpleBySearchVo(CustSimpleSearchVO reqVO);

    /**
     * 查询客户历史所属投顾列表
     *
     * @param conscustno 客户号
     * @return CmCustHisConsultantBO
     */
    List<CmCustHisConsultantBO> queryCustHisConsultantList(String conscustno);

    /**
     * 查询客户历史所属投顾数量
     *
     * @param conscustno 客户号
     * @param consCode 投顾
     * @return 数量
     */
    String queryHisCountByConscustno(@Param("conscustno") String conscustno, @Param("consCode") String consCode);


    /**
     * 分页查询初始化数据
     * @param startRow 开始行
     * @param endRow 结束行
     * @return List<ConsPerformanceCoeffBO> 初始化数据列表
     */
    List<ConsPerformanceCoeffBO> queryInitData(@Param("startRow") int startRow, @Param("endRow") int endRow);

    /**
     * 获取初始化数据总数
     * @return int 总数
     */
    int countInitData();
}