/**
 * Copyright (c) 2024, <PERSON><PERSON><PERSON><PERSON> HOWBUY INVESTMENT MANAGEMENT Co., Ltd.
 * All right reserved.
 * <p>
 * THIS IS UNPUBLISHED PROPRIETARY SOURCE CODE OF HOWBUY INVESTMENT
 * MANAGEMENT CO., LTD.  THE CONTENTS OF THIS FILE MAY NOT BE DISCLOSED
 * TO THIRD PARTIES, COPIED OR DUPLICATED IN ANY FORM, IN WHOLE OR IN PART,
 * WITHOUT THE PRIOR WRITTEN PERMISSION OF HOWBUY INVESTMENT MANAGEMENT
 * CO., LTD.
 */
package com.howbuy.crm.account.dao.po.consultantexp;

import lombok.Data;

import java.math.BigDecimal;
import java.util.Date;

/**
 * @description: (请在此添加描述)
 * <AUTHOR>
 * @date 2024/11/14 17:54
 * @since JDK 1.8
 */
@Data
public class CmConsultantExpPO {
    private String conscode;
    private String consname;

    private String mobile;
    private String centerName;

    private String outletcode;

    private String teamcode;
    private String teamname;

    private String telno;
    private String email;


    private String userid;
    private String userno;
    private String provcode;
    private String citycode;
    private String cityname;
    private String gender;
    private String birthday;
    private String age;
    private String edulevel;
    /**
     * 员工状态
     */
    private String worktype;
    /**
     * 在职状态
     */
    private String workstate;
    private String userlevel;
    private String curmonthlevel;
    private String startdt;
    private String startlevel;
    private BigDecimal salary;
    private String salaryStr;
    /**
     * 试用截止日期
     */
    private String probationenddt;
    /**
     * 转正日期
     */
    private String regulardt;
    /**
     * 转正日期人工修改标识1是0否
     */
    private String regulardtFlag;
    /**
     * 转正职级
     */
    private String regularlevel;
    /**
     * 转正薪资
     */
    private BigDecimal regularsalary;
    private String regularsalaryStr;
    /**
     * 离职日期
     */
    private String quitdt;
    /**
     * 离职职级
     */
    private String quitlevel;
    /**
     * 离职薪资
     */
    private BigDecimal quitsalary;
    private String quitsalaryStr;
    /**
     * 离职原因
     */
    private String quitreason;
    private BigDecimal servingage;
    private String checkflag;
    private String checkflagval;
    private String checkor;
    private String creator;
    private Date creatdt;
    private String modor;
    private String ssOrgInfos;

    /**
     * 当月薪资
     */
    private BigDecimal curmonthsalary;
    private String curmonthsalaryStr;
    /**
     * 试用3M考核结果
     */
    private String probationresult3m;
    /**
     * 试用6M考核结果
     */
    private String probationresult6m;
    /**
     * 12M考核结果
     */
    private String probationResult12m;
    /**
     * 下次考核截止日期
     */
    private String nexttestdate;
    /**
     * 下次考核截止日期人工修改标识1是0否
     */
    private String nexttestdateFlag;
    /**
     * 副职
     */
    private String subpositions;
    /**
     * 离职去向
     */
    private String quitInfo;

    /**
     * 当月职级info
     *
     * @return
     */
    private String curmonthlevelinfo;

    /**
     * 晋升日期
     */
    private String promotedate;
    /**
     * 创新方案
     */
    private String bxcommissionway;
    /**
     * 北森ID
     */
    private String beisenid;
    /**
     * 业务中心
     */
    private String centerOrg;
    /**
     * 调整司龄(月)
     */
    private String adjustServingMonth;
    /**
     * 调整管理司龄(月)
     */
    private String adjustManageServingMonth;

    /**
     * 基金从业资格编码
     */
    private String jjcardno;
    /**
     * 是否挂靠
     */
    private String attachtype;
    /**
     * 背景
     */
    private String background;
    /**
     * 来源
     */
    private String source;
    /**
     * 入职好买前职位类型
     */
    private String beforepositiontype;
    /**
     * 入职好买前工作年限
     */
    private String beforepositionage;
    /**
     * 招聘经理
     */
    private String recruit;
    /**
     * 推荐人
     */
    private String recommend;
    /**
     * 推荐人工号
     */
    private String recommenduserno;
    /**
     * 推荐类型
     */
    private String recommendtype;
    /**
     * 备注
     */
    private String remark;
    /**
     * 用于回显机构代码
     */
    private String orgCode;
    /**
     * 修改日期
     */
    private Date moddt;
    /**
     *3M日期
     */
    private String dt3m;
    /**
     *3M日期人工修改标识1是0否
     */
    private String dt3mFlag;
    /**
     *12M日期
     */
    private String dt12m;
    /**
     *12M日期人工修改标识1是0否
     */
    private String dt12mFlag;
    /**
     * 下次考核周期
     */
    private String nextTestPeriod;
    /**
     * 试用3M薪资
     */
    private BigDecimal probationSalary3m;
    private String probationSalary3mStr;
    /**
     * 12M薪资
     */
    private BigDecimal salary12m;
    private String salary12mStr;
    /**
     * 入职档级
     */
    private String joinRank;
    /**
     * 试用3M档级
     */
    private String probationRank3m;
    /**
     * 转正档级
     */
    private String regularRank;
    /**
     * 12M档级
     */
    private String rank12m;
    /**
     * 入职社保基数
     */
    private String joinSsb;
    /**
     * 试用3M社保基数
     */
    private String probationSsb3m;
    /**
     * 转正社保基数
     */
    private String regularSsb;
    /**
     * 12M社保基数
     */
    private String ssb12m;
    /**
     * 试用3M考核职级
     */
    private String probationLevel3m;
    /**
     * 12M考核职级
     */
    private String testLevel12m;
    /**
     * 区code
     */
    private String countyCode;
    /**
     * 在途备注
     */
    private String inTransitRemark;

}