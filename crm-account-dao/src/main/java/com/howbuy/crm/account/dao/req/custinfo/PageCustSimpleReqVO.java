/**
 * Copyright (c) 2024, <PERSON><PERSON><PERSON><PERSON> HOWBUY INVESTMENT MANAGEMENT Co., Ltd.
 * All right reserved.
 * <p>
 * THIS IS UNPUBLISHED PROPRIETARY SOURCE CODE OF HOWBUY INVESTMENT
 * MANAGEMENT CO., LTD.  THE CONTENTS OF THIS FILE MAY NOT BE DISCLOSED
 * TO THIRD PARTIES, COPIED OR DUPLICATED IN ANY FORM, IN WHOLE OR IN PART,
 * WITHOUT THE PRIOR WRITTEN PERMISSION OF HOWBUY INVESTMENT MANAGEMENT
 * CO., LTD.
 */
package com.howbuy.crm.account.dao.req.custinfo;

import com.howbuy.crm.account.dao.req.PageReqVO;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.util.List;

/**
 * @description: (请在此添加描述)
 * <AUTHOR>
 * @date 2024/9/24 18:04
 * @since JDK 1.8
 */
@Data
@EqualsAndHashCode(callSuper = true)
public class PageCustSimpleReqVO extends PageReqVO {

    /**
     * 投顾编号
     */
    private String conscode;

    /**
     * 搜索内容
     */
    private String searchContent;

    /**
     * 手机号摘要
     */
    private String mobileDigest;
}
