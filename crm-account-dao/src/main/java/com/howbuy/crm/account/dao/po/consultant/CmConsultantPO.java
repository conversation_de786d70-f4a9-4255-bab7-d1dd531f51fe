package com.howbuy.crm.account.dao.po.consultant;

import java.math.BigDecimal;
import lombok.Data;

/**
 * @description: (请在此添加描述)
 * <AUTHOR>
 * @date 2023/12/26 15:31
 * @since JDK 1.8
 */
/**
    * 理财顾问信息表
    */
@Data
public class CmConsultantPO {
    /**
    * 理财顾问代码
    */
    private String conscode;

    /**
    * 理财顾问名称
    */
    private String consname;

    /**
    * 理财顾问级别
    */
    private String conslevel;

    /**
    * 所属小组
    */
    private String teamcode;

    /**
    * 理财顾问特点
    */
    private String character;

    /**
    * 理财顾问状态1有效，0无效
    */
    private String consstatus;

    /**
    * 记录状态
    */
    private String recstat;

    /**
    * 复核标志
    */
    private String checkflag;

    /**
    * 创建人
    */
    private String creator;

    /**
    * 修改人
    */
    private String modifier;

    /**
    * 复核人
    */
    private String checker;

    /**
    * 记录创建日期
    */
    private String credt;

    /**
    * 记录修改日期
    */
    private String moddt;

    /**
    * 联系电话
    */
    private String telno;

    /**
    * 手机
    */
    private String mobile;

    /**
    * 所属部门
    */
    private String deptcode;

    /**
    * 所属理财中心
    */
    private String outletcode;

    /**
    * 照片地址
    */
    private String pictureurl;

    /**
    * 电子邮件
    */
    private String email;

    /**
    * 简历
    */
    private String resume;

    /**
    * 入职时间
    */
    private String startdt;

    /**
    * 离职时间
    */
    private String enddt;

    /**
    * 从业资格证书号码
    */
    private String empcardno;

    /**
    * 登陆状态（1：能登陆，0：不能登陆）
    */
    private String loginflag;

    /**
    * ����������1������2������
    */
    private String isseniormgr;

    /**
    * 工作地点（PD：浦东，HK：虹口， QT：其他）
    */
    private String workplace;

    /**
    * 拨号通道（RHPT：睿狐普通，RHCC：睿狐CC，SLT：商路通）
    */
    private String dialchannel;

    /**
    * 用户IP地址
    */
    private String ip;

    /**
    * 投顾2013年底前高净值总销量
    */
    private BigDecimal salaryamtbefore;

    /**
    * 是否虚拟投顾（1：是，0：否）
    */
    private String isvirtual;

    /**
    * 是否内部投顾（1：是，0：否）
    */
    private String isinside;

    /**
    * 投顾照片链接地址
    */
    private String picaddr;

    /**
    * 投顾照片链接地址1
    */
    private String picaddr1;

    /**
    * 理财顾问特点1
    */
    private String character1;

    /**
    * 理财顾问职位
    */
    private String position;

    /**
    * 1，成交备用库；2，优质潜客库；3，一般潜客库；4，陌call库；5，公募库;
    */
    private String ownertype;

    /**
    * 企业微信二维码地址
    */
    private String codepicaddr;

    /**
    * 手机号掩码
    */
    private String mobileDigest;

    /**
    * 坐席工号
    */
    private String agentno;

    /**
    * 企业微信账号
    */
    private String wechatconscode;
}