package com.howbuy.crm.account.dao.bo.commvisit;

import java.util.List;

import com.howbuy.crm.account.dao.po.commvisit.CmVisitMinutesAccompanyingPO;
import com.howbuy.crm.account.dao.po.commvisit.CmVisitMinutesPO;

import lombok.Getter;
import lombok.Setter;
/**
 * <AUTHOR>
 * @description: 请在此添加描述
 * @date 2025/4/10 10:04
 * @since JDK 1.8
 */
@Getter
@Setter
public class CmVisitMinutesBO extends CmVisitMinutesPO {

    /**
     * 拜访日期 
     */
    private String visitDt;
    /**
     * 拜访目的
     */
    private String visitPurpose;
    /**
     * 客户名称
     */
    private String custName;
    /**
     * 客户编号
     */
    private String consCustNo;
    /**
     * 所属部门
     */
    private String orgCode;
    /**
     * 拜访类型
     */
    private String visitType;
    /**
     * 拜访类型
     */
    private String visitTypeName;
    
    /**
     * 陪访人员
     */
    private List<CmVisitMinutesAccompanyingPO> accompanyingUserList;
}