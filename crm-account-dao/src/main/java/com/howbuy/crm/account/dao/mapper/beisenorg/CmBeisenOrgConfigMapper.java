package com.howbuy.crm.account.dao.mapper.beisenorg;

import com.github.pagehelper.Page;
import com.howbuy.crm.account.dao.po.beisen.CmBeisenOrgDO;
import com.howbuy.crm.account.dao.po.beisenorg.CmBeisenOrgConfigPO;
import com.howbuy.crm.account.dao.req.beisen.QueryBeisenOrgConfigVO;
import org.apache.ibatis.annotations.Param;

import java.util.List;

public interface CmBeisenOrgConfigMapper {
    int deleteByPrimaryKey(Long id);

    int insert(CmBeisenOrgConfigPO record);

    int insertSelective(CmBeisenOrgConfigPO record);

    CmBeisenOrgConfigPO selectByPrimaryKey(Long id);

    int updateByPrimaryKeySelective(CmBeisenOrgConfigPO record);

    int updateByPrimaryKey(CmBeisenOrgConfigPO record);

    /**
     * @description 查询列表
     * @param queryBeisenOrgConfigVO
     * @return com.github.pagehelper.Page<com.howbuy.crm.account.dao.po.beisenorg.CmBeisenOrgConfigPO>
     * @author: jianjian.yang
     * @date: 2024/10/24 10:48
     * @since JDK 1.8
     */
    Page<CmBeisenOrgConfigPO> queryList(QueryBeisenOrgConfigVO queryBeisenOrgConfigVO);

    /**
     * @description 根据北森ID查
     * @param orgIdBeisen
     * @return int
     * @author: jianjian.yang
     * @date: 2024/11/19 16:17
     * @since JDK 1.8
     */
    List<Long> selectByOrgId(@Param("orgIdBeisen") String orgIdBeisen);

    /**
     * @description 根据时间区间查
     * @param orgIdBeisen
     * @param startDate
     * @param endDate
     * @return int
     * @author: jianjian.yang
     * @date: 2024/11/19 16:29
     * @since JDK 1.8
     */
    List<Long> selectByInterval(@Param("orgIdBeisen") String orgIdBeisen, @Param("startDate") String startDate, @Param("endDate") String endDate);

    /**
     * @description 查北森机构列表
     * @param
     * @return java.util.List<com.howbuy.crm.account.dao.po.beisen.CmBeisenOrgDO>
     * @author: jianjian.yang
     * @date: 2024/11/19 17:55
     * @since JDK 1.8
     */
    List<CmBeisenOrgDO> selectBeisenOrg();
}