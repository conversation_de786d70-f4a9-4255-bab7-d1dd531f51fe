package com.howbuy.crm.account.dao.mapper.commvisit;

import com.howbuy.crm.account.dao.bo.commvisit.CmVisitMinutesBO;
import com.howbuy.crm.account.dao.bo.commvisit.NoFeedbackMsgBO;
import com.howbuy.crm.account.dao.dto.commvisit.VisitMinutesQueryDTO;
import com.howbuy.crm.account.dao.po.commvisit.CmVisitMinutesPO;
import org.apache.ibatis.annotations.Param;

import java.util.List;

public interface CmVisitMinutesMapper {
    int deleteByPrimaryKey(String id);

    int insert(CmVisitMinutesPO record);

    int insertSelective(CmVisitMinutesPO record);

    CmVisitMinutesPO selectByPrimaryKey(String id);

    int updateByPrimaryKeySelective(CmVisitMinutesPO record);

    int updateByPrimaryKey(CmVisitMinutesPO record);

    /**
     * @description 更新操作
     * @param record
     * @return int
     * @author: jianjian.yang
     * @date: 2025/5/14 9:50
     * @since JDK 1.8
     */
    int updateMinutesById(CmVisitMinutesPO record);

    /**
     * @description 查询拜访纪要是否已反馈的数据
     * @param ids
     * @return java.util.List<com.howbuy.crm.account.dao.po.commvisit.CmVisitMinutesPO>
     * @author: jianjian.yang
     * @date: 2025/4/24 16:10
     * @since JDK 1.8
     */
    Integer getFeedbackCountById(@Param("ids") List<String> ids);
    /**
     * @description 查询拜访纪要列表
     * @param query
     * @return java.util.List<com.howbuy.crm.account.dao.po.commvisit.CmVisitMinutesPO>
     * @author: jianjian.yang
     * @date: 2025/4/8 10:45
     * @since JDK 1.8
     */
    List<CmVisitMinutesBO> getVisitMinutesList(@Param("query") VisitMinutesQueryDTO query);
    /**
     * @description 查单条
     * @param id
     * @return com.howbuy.crm.account.dao.bo.commvisit.CmVisitMinutesBO
     * @author: jianjian.yang
     * @date: 2025/4/14 14:25
     * @since JDK 1.8
     */
    CmVisitMinutesBO getVisitMinutesById(@Param("id") String id);
    /**
     * @description 更新主管信息
     * @param ids 拜访纪要ID
     * @param managerId 主管ID
     * @param userId 用户
     * @return int
     * @author: jianjian.yang
     * @date: 2025/4/8 10:45
     * @since JDK 1.8
     */
    int updateManager(@Param("ids") List<String> ids, @Param("managerId") String managerId,
                      @Param("managerLevel") String managerLevel,
                      @Param("userId") String userId);
    
    /**
     * @description 清空主管反馈信息
     * @param id 拜访纪要ID
     * @return int
     * @author: jianjian.yang
     * @date: 2025/4/8 10:45
     * @since JDK 1.8
     */
    int clearManagerFeedback(@Param("id") String id);
    
    /**
     * @description 查询拜访纪要列表(用于导出)
     * @param query
     * @return java.util.List<com.howbuy.crm.account.dao.po.commvisit.CmVisitMinutesPO>
     * @author: jianjian.yang
     * @date: 2025/4/8 10:45
     * @since JDK 1.8
     */
    List<CmVisitMinutesPO> getVisitMinutesListForExport(@Param("query") VisitMinutesQueryDTO query);

    /**
     * 更新主管反馈信息
     */
    int updateManagerFeedback(CmVisitMinutesPO po);

    /**
     * @description 查菜单名称
     * @param menuCode
     * @return java.lang.String
     * @author: jianjian.yang
     * @date: 2025/4/17 10:13
     * @since JDK 1.8
     */
    String getMenuName(String menuCode);

    /**
     * @description 查询第6天未反馈的列表
     * @param curPreDay 当前日期往前推5天的日期
     * @return java.util.List<com.howbuy.crm.account.dao.bo.commvisit.NoFeedbackMsgBO>
     * @author: jianjian.yang
     * @date: 2025/4/29 19:23
     * @since JDK 1.8
     */
    List<NoFeedbackMsgBO> fetchNoFeedbackList(@Param("notPushAccompanyingType") String notPushAccompanyingType, @Param("curPreDay") String curPreDay);
}