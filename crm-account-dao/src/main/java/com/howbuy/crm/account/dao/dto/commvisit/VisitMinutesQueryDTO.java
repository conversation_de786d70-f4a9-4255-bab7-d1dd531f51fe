package com.howbuy.crm.account.dao.dto.commvisit;

import lombok.Data;
import java.util.List;

/**
 * 拜访纪要查询DTO
 */
@Data
public class VisitMinutesQueryDTO {
    
    /**
     * 组织编码
     */
    private String orgCode;
    /**
     * 所属投顾ID
     */
    private String consCode;
    
    /**
     * 拜访日期开始 格式YYYYMMDD
     */
    private String visitDateStart;
    
    /**
     * 拜访日期结束 格式YYYYMMDD
     */
    private String visitDateEnd;
    
    /**
     * 创建日期开始 格式YYYYMMDD
     */
    private String createDateStart;
    
    /**
     * 创建日期结束 格式YYYYMMDD
     */
    private String createDateEnd;
    
    /**
     * 拜访目的列表
     */
    private List<String> visitPurpose;
    
    /**
     * 客户姓名
     */
    private String custName;
    
    /**
     * 投顾客户号
     */
    private String consCustNo;
    
    /**
     * 陪访人
     */
    private List<String> accompanyingUserIdList;
    
    /**
     * 上级主管
     */
    private List<String> managerUserIdList;
    
    /**
     * 反馈情况
     */
    private List<String> feedbackStatus;
    
    /**
     * 分页页码
     */
    private Integer pageNo;
    
    /**
     * 分页大小
     */
    private Integer pageSize;
} 