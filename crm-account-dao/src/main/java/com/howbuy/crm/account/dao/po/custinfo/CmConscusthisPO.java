package com.howbuy.crm.account.dao.po.custinfo;

import java.math.BigDecimal;
import java.util.Date;
import lombok.Data;

/**
 * @description: (投顾客户信息流水表)
 * <AUTHOR>
 * @date 2023/12/8 14:54
 * @since JDK 1.8
 */
/**
    * 投顾客户信息流水表
    */
@Data
public class CmConscusthisPO {
    /**
     * 流水号
     */
    private String appserialno;

    /**
     * 投顾客户编号
     */
    private String conscustno;

    /**
     * 投顾客户等级
     */
    private String conscustlvl;

    /**
     * 投顾客户评分
     */
    private Short conscustgrade;

    /**
     * 投顾客户状态
     */
    private String conscuststatus;

    /**
     * 证件类型
     */
    private String idtype;

    /**
     * 证件号码
     */
    private String idnoTm;

    /**
     * 投资者名称
     */
    private String custname;

    /**
     * 省份代码
     */
    private String provcode;

    /**
     * 城市代码
     */
    private String citycode;

    /**
     * 投资者学历
     */
    private String edulevel;

    /**
     * 投资者职业代码
     */
    private String vocation;

    /**
     * 投资者年收入
     */
    private String inclevel;

    /**
     * 投资者生日
     */
    private String birthday;

    /**
     * 投资者性别
     */
    private String gender;

    /**
     * 婚否
     */
    private String married;

    /**
     * 个人年收入
     */
    private String pincome;

    /**
     * 家庭年收入
     */
    private String fincome;

    /**
     * 是否投资决策人
     */
    private String decisionflag;

    /**
     * 兴趣爱好
     */
    private String interests;

    /**
     * 家庭状况
     */
    private String familycondition;

    /**
     * 方便联系时段
     */
    private String contacttime;

    /**
     * 是否寄送资料
     */
    private String sendinfoflag;

    /**
     * 是否接受电话
     */
    private String recvtelflag;

    /**
     * 是否接受电子邮件
     */
    private String recvemailflag;

    /**
     * 是否接受短信
     */
    private String recvmsgflag;

    /**
     * 公司
     */
    private String company;

    /**
     * 客户风险承受能力等级
     */
    private String risklevel;

    /**
     * 自定义风险承受能力等级
     */
    private String selfrisklevel;

    /**
     * 地址
     */
    private String addrTm;

    /**
     * 邮政编码
     */
    private String postcode;

    /**
     * 投资者手机号码
     */
    private String mobileTm;

    /**
     * 联系电话
     */
    private String telnoTm;

    /**
     * 传真
     */
    private String fax;

    /**
     * 电子邮箱
     */
    private String emailTm;

    /**
     * 投资者单位电话
     */
    private String officetelno;

    /**
     * 客户来源
     */
    private String source;

    /**
     * 了解途径
     */
    private String knowchan;

    /**
     * 其它途径
     */
    private String otherchan;

    /**
     * 想了解的其他投资品种
     */
    private String otherinvest;

    /**
     * 希望参加的沙龙
     */
    private String salon;

    /**
     * 之前投资品种
     */
    private String beforeinvest;

    /**
     * 自定义标志
     */
    private String selfdefflag;

    /**
     * 沟通频率
     */
    private String visitfqcy;

    /**
     * 销售方向
     */
    private String saledirection;

    /**
     * 发展方向
     */
    private String devdirection;

    /**
     * 客户来源细分
     */
    private String subsource;

    /**
     * 客户来源细分分类
     */
    private String subsourcetype;

    /**
     * 备注
     */
    private String remark;

    /**
     * 地址2
     */
    private String addr2Tm;

    /**
     * 邮政编码2
     */
    private String postcode2;

    /**
     * 投资者手机号码2
     */
    private String mobile2Tm;

    /**
     * 电子邮箱2
     */
    private String email2Tm;

    /**
     * 知道好买
     */
    private String knowhowbuy;

    /**
     * 知道好买细分
     */
    private String subknow;

    /**
     * 知道好买细分分类
     */
    private String subknowtype;

    /**
     * 购买产品
     */
    private String buyingprod;

    /**
     * 曾购买产品
     */
    private String buyedprod;

    /**
     * 免费产品
     */
    private String freeprod;

    /**
     * 是否特殊客户
     */
    private String specialflag;

    /**
     * 报告寄送方式
     */
    private String dlvymode;

    /**
     * 特殊原因
     */
    private String specialreason;

    /**
     * 创建人
     */
    private String creator;

    /**
     * 复核人
     */
    private String checker;

    /**
     * 时间戳
     */
    private Date stimestamp;

    /**
     * 私募风险测试水平
     */
    private String pririsklevel;

    /**
     * 联系人姓名
     */
    private String linkman;

    /**
     * 经办人电话
     */
    private String linktelTm;

    /**
     * 经办人手机
     */
    private String linkmobileTm;

    /**
     * 经办人电子邮箱
     */
    private String linkemailTm;

    /**
     * 经办人邮政编码
     */
    private String linkpostcode;

    /**
     * 经办人地址
     */
    private String linkaddrTm;

    /**
     * 职位
     */
    private String capacity;

    /**
     * GPS投资级别---对应资金额度(A: 投资资产<100万,B:投资资产100-2000万 C:投资资产>2000万)
     */
    private String gpsinvestlevel;

    /**
     * GPS风险偏好( 1: 积极型 ,2:稳健型,3:保守型)
     */
    private String gpsrisklevel;

    /**
     * 是否企业主( 1:是企业主，0)
     */
    private String isboss;

    /**
     * 金融需求
     */
    private String financeneed;

    /**
     * 是否入会（1：已经入会）
     */
    private String isjoinclub;

    /**
     * 说明
     */
    private String explanation;

    /**
     * 是否签署风险提示函
     */
    private String isrisktip;

    /**
     * 客户来源备注
     */
    private String custsourceremark;

    private BigDecimal pmarketamt;

    /**
     * 是否填写过投资者承诺书（0：否；1：是）
     */
    private String iswritebook;

    /**
     * 客户类型枚举 0 机构客户 1 个人客户 2 产品客户
     */
    private String invsttype;

    /**
     * 合并客户最早来源细分分类
     */
    private String source2;

    /**
     * 合并客户最早来源细分
     */
    private String subsource2;

    private String subsourcetype2;

    /**
     * 贵宾账号用户名
     */
    private String vipusername;

    private String wechatcode;

    /**
     * 新来源编号
     */
    private String newsourceno;

    /**
     * 一账通账号
     */
    private String hboneNo;

    /**
     * 新来源编号(合并)
     */
    private String newsourceno2;

    /**
     * 可以期望交易方式（1：电子交易；2：线下交易）
     */
    private String hopetradetype;

    /**
     * 证件有限期
     * 是否长期有效0-否,1-是
     */
    private String validity;

    /**
     * 证件有限期日期
     */
    private String validitydt;

    /**
     * 性质
     */
    private String nature;

    /**
     * 资质
     */
    private String aptitude;

    /**
     * 经营范围
     */
    private String scopebusiness;

    /**
     * 资源类型0：公司资源；1：投顾资源
     */
    private String restype;

    /**
     * 手机归属省份
     */
    private String provcodeMobile;

    /**
     * 手机归属城市
     */
    private String citycodeMobile;

    /**
     * 机构类型
     */
    private String orgtype;

    /**
     * 姓名拼音
     */
    private String pinyin;

    /**
     * 证件号摘要
     */
    private String idnoDigest;

    /**
     * 证件号掩码
     */
    private String idnoMask;

    /**
     * 客户姓名摘要
     */
    private String custnameDigest;

    /**
     * 客户姓名掩码
     */
    private String custnameMask;

    /**
     * 地址摘要
     */
    private String addrDigest;

    /**
     * 地址掩码
     */
    private String addrMask;

    /**
     * 手机号摘要
     */
    private String mobileDigest;

    /**
     * 手机号掩码
     */
    private String mobileMask;

    /**
     * 座机号摘要
     */
    private String telnoDigest;

    /**
     * 座机号掩码
     */
    private String telnoMask;

    /**
     * 邮箱摘要
     */
    private String emailDigest;

    /**
     * 邮箱掩码
     */
    private String emailMask;

    /**
     * 地址2摘要
     */
    private String addr2Digest;

    /**
     * 地址2掩码
     */
    private String addr2Mask;

    /**
     * 手机2摘要
     */
    private String mobile2Digest;

    /**
     * 手机2掩码
     */
    private String mobile2Mask;

    /**
     * 邮箱2摘要
     */
    private String email2Digest;

    /**
     * 邮箱2掩码
     */
    private String email2Mask;

    /**
     * 联系人姓名摘要
     */
    private String linkmanDigest;

    /**
     * 联系人姓名掩码
     */
    private String linkmanMask;

    /**
     * 联系人座机号摘要
     */
    private String linktelDigest;

    /**
     * 联系人座机号掩码
     */
    private String linktelMask;

    /**
     * 联系人手机摘要
     */
    private String linkmobileDigest;

    /**
     * 联系人手机掩码
     */
    private String linkmobileMask;

    /**
     * 联系人邮箱摘要
     */
    private String linkemailDigest;

    /**
     * 联系人邮箱掩码
     */
    private String linkemailMask;

    /**
     * 联系人地址摘要
     */
    private String linkaddrDigest;

    /**
     * 联系人地址掩码
     */
    private String linkaddrMask;

    /**
     * 分享人投顾是否虚拟（1：是，0：否）
     */
    private String isvirtualsharer;

    /**
     * 手机 地区码
     */
    private String mobileAreaCode;

    /**
     * 手机2 地区码
     */
    private String mobile2AreaCode;

    /**
     * 联系人手机 地区码
     */
    private String linkmobileAreaCode;

    /**
     * 证件地区码
     */
    private String idSignAreaCode;

    /**
     * 国籍
     */
    private String nationCode;

    /**
     * 一账通号关联时间
     */
    private Date hboneTimestamp;

    /**
     * 县代码
     */
    private String countyCode;
}