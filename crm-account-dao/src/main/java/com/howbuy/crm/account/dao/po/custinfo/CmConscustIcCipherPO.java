package com.howbuy.crm.account.dao.po.custinfo;

import lombok.Data;

/**
 * @description: (请在此添加描述)
 * <AUTHOR>
 * @date 2023/12/26 15:40
 * @since JDK 1.8
 */
/**
    * 投顾客户密文表
    */
@Data
public class CmConscustIcCipherPO {
    /**
    * 投顾客户号
    */
    private String conscustno;

    /**
    * 证件号密文
    */
    private String idnoCipher;

    /**
    * 客户姓名密文
    */
    private String custnameCipher;

    /**
    * 地址密文
    */
    private String addrCipher;

    /**
    * 手机号密文
    */
    private String mobileCipher;

    /**
    * 座机号密文
    */
    private String telnoCipher;

    /**
    * 邮箱密文
    */
    private String emailCipher;

    /**
    * 地址2密文
    */
    private String addr2Cipher;

    /**
    * 手机2密文
    */
    private String mobile2Cipher;

    /**
    * 邮箱2密文
    */
    private String email2Cipher;

    /**
    * 联系人姓名密文
    */
    private String linkmanCipher;

    /**
    * 联系人座机密文
    */
    private String linktelCipher;

    /**
    * 联系人手机号密文
    */
    private String linkmobileCipher;

    /**
    * 联系人邮箱密文
    */
    private String linkemailCipher;

    /**
    * 联系人地址密文
    */
    private String linkaddrCipher;
}