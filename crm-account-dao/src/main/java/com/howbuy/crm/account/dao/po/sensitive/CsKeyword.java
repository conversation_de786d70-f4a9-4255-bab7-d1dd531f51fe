/**
 * Copyright (c) 2025, <PERSON><PERSON><PERSON><PERSON> HOWBUY INVESTMENT MANAGEMENT Co., Ltd.
 * All right reserved.
 * <p>
 * THIS IS UNPUBLISHED PROPRIETARY SOURCE CODE OF HOWBUY INVESTMENT
 * MANAGEMENT CO., LTD.  THE CONTENTS OF THIS FILE MAY NOT BE DISCLOSED
 * TO THIRD PARTIES, COPIED OR DUPLICATED IN ANY FORM, IN WHOLE OR IN PART,
 * WITHOUT THE PRIOR WRITTEN PERMISSION OF HOWBUY INVESTMENT MANAGEMENT
 * CO., LTD.
 */
package com.howbuy.crm.account.dao.po.sensitive;

import lombok.Getter;
import lombok.Setter;

import java.util.Date;

@Getter
@Setter
public class CsKeyword {

    private Integer id;

    private String keywordName;

    private Date credDt;

    private Date modDt;

    /**
    * "0" 表示有效，“1”表示失效
    */
    private Short isValid;

    private String creator;

    private String modifier;

    /**
    * 功能模块："01" 表示客服来电处理模块，“02”表示资产配置报告模块
    */
    private String keywordmodule;
}