package com.howbuy.crm.account.dao.bo.commvisit;

import lombok.Getter;
import lombok.Setter;

/**
 * <AUTHOR>
 * @description: 请在此添加描述
 * @date 2025/4/29 11:24
 * @since JDK 1.8
 */
@Getter
@Setter
public class CsCommunicateBO {
    /**
     * 主键ID
     */
    private String pkId;

    /**
     * 拜访时间
     */
    private String visitTime;

    /**
     * 修改标志
     */
    private String modifyFlag;

    /**
     * 历史标志
     */
    private String hisFlag;

    /**
     * 历史ID
     */
    private String hisId;

    /**
     * 拜访类型
     */
    private String visitType;

    /**
     * 咨询类型
     */
    private String consultType;

    /**
     * 来源地址
     */
    private String sourceAddr;

    /**
     * 创建人代码
     */
    private String creator;

    /**
     * 投顾姓名
     */
    private String consName;

    /**
     * 订单ID
     */
    private String orderId;

    /**
     * 呼入ID
     */
    private String callInId;

    /**
     * 拜访总结
     */
    private String visitSummary;

    /**
     * 拜访日期
     */
    private String visitDt;

    /**
     * 拜访目的
     */
    private String visitPurpose;

    /**
     * 拜访目的其他
     */
    private String visitPurposeOther;

    /**
     * 拜访纪要ID
     */
    private String visitMinutesId;
}