/**
 * Copyright (c) 2024, <PERSON><PERSON><PERSON><PERSON> HOWBUY INVESTMENT MANAGEMENT Co., Ltd.
 * All right reserved.
 * <p>
 * THIS IS UNPUBLISHED PROPRIETARY SOURCE CODE OF HOWBUY INVESTMENT
 * MANAGEMENT CO., LTD.  THE CONTENTS OF THIS FILE MAY NOT BE DISCLOSED
 * TO THIRD PARTIES, COPIED OR DUPLICATED IN ANY FORM, IN WHOLE OR IN PART,
 * WITHOUT THE PRIOR WRITTEN PERMISSION OF HOWBUY INVESTMENT MANAGEMENT
 * CO., LTD.
 */
package com.howbuy.crm.account.dao.po.consultant;

import lombok.Getter;
import lombok.Setter;
import lombok.ToString;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;

/**
 * @description: 人员绩效系数表
 * <AUTHOR>
 * @date 2025-06-27 17:05:00
 * @since JDK 1.8
 */
@Get<PERSON>
@Setter
@ToString
public class CmConsPerformanceCoeffPO implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 投顾客户号
     */
    private String consCustNo;

    /**
     * 投顾code
     */
    private String conscode;

    /**
     * 分配时间
     */
    private Date assignTime;

    /**
     * 来源类型
     */
    private String sourceType;

    /**
     * 佣金系数起点
     */
    private String commissionCoeffStart;

    /**
     * 客户折算系数
     */
    private BigDecimal custConversionCoeff;

    /**
     * 管理系数-分总
     */
    private BigDecimal manageCoeffSubtotal;

    /**
     * 管理系数-区副
     */
    private BigDecimal manageCoeffRegionalsubtotal;

    /**
     * 管理系数-区总
     */
    private BigDecimal manageCoeffRegionaltotal;

    /**
     * 存续B
     */
    private String cxb;

    /**
     * 是否计提公募
     */
    private String isBigV;

    /**
     * 中心-分配时
     */
    private String centerCode;

    /**
     * 区域-分配时
     */
    private String regionCode;

    /**
     * 区副-分配时
     */
    private String regionalsubtotal;

    /**
     * 分公司-分配时
     */
    private String outletCode;

    /**
     * 创建人
     */
    private String creator;

    /**
     * 修改人
     */
    private String modifier;

    /**
     * 创建时间
     */
    private Date credt;

    /**
     * 修改时间
     */
    private Date moddt;
} 