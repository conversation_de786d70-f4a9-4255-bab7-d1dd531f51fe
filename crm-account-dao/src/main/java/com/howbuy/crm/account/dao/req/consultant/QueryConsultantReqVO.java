/**
 * Copyright (c) 2024, <PERSON><PERSON><PERSON><PERSON> HOWBUY INVESTMENT MANAGEMENT Co., Ltd.
 * All right reserved.
 * <p>
 * THIS IS UNPUBLISHED PROPRIETARY SOURCE CODE OF HOWBUY INVESTMENT
 * MANAGEMENT CO., LTD.  THE CONTENTS OF THIS FILE MAY NOT BE DISCLOSED
 * TO THIRD PARTIES, COPIED OR DUPLICATED IN ANY FORM, IN WHOLE OR IN PART,
 * WITHOUT THE PRIOR WRITTEN PERMISSION OF HOWBUY INVESTMENT MANAGEMENT
 * CO., LTD.
 */
package com.howbuy.crm.account.dao.req.consultant;

import lombok.Data;

import java.util.List;

/**
 * @description: (请在此添加描述)
 * <AUTHOR>
 * @date 2024/10/11 19:15
 * @since JDK 1.8
 */
@Data
public class QueryConsultantReqVO {

    /**
     * 企业微信账号（企微userId）
     */
    private String userId;

    /**
     * 是否区分投顾状态 1-查有效（默认）0-查全部
     */
    private String consStatus;

    /**
     * 投顾consCodeList
     */
    private List<String> consCodeList;

}
