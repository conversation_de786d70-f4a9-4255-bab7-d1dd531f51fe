/**
 * Copyright (c) 2025, <PERSON><PERSON><PERSON><PERSON> HOWBUY INVESTMENT MANAGEMENT Co., Ltd.
 * All right reserved.
 * <p>
 * THIS IS UNPUBLISHED PROPRIETARY SOURCE CODE OF HOWBUY INVESTMENT
 * MANAGEMENT CO., LTD.  THE CONTENTS OF THIS FILE MAY NOT BE DISCLOSED
 * TO THIRD PARTIES, COPIED OR DUPLICATED IN ANY FORM, IN WHOLE OR IN PART,
 * WITHOUT THE PRIOR WRITTEN PERMISSION OF HOWBUY INVESTMENT MANAGEMENT
 * CO., LTD.
 */
package com.howbuy.crm.account.dao.mapper.customize.sensitive;

import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * @description 敏感词查询Mapper
 *
 * <AUTHOR>
 * @date 2024-03-19 14:30:45
 */
public interface CustomizeCsKeywordMapper {

    /**
     * <AUTHOR>
     * @description 根据功能模块查询敏感词列表
     * @date 2024-03-27 14:23:45
     * @param moduleType 功能模块
     * @return List<String> 敏感词列表
     */
    List<String> listKeywordNameByModule(@Param("moduleType") String moduleType);
}
