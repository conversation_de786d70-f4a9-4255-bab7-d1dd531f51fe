/**
 * Copyright (c) 2023, <PERSON><PERSON><PERSON><PERSON> HOWBUY INVESTMENT MANAGEMENT Co., Ltd.
 * All right reserved.
 * <p>
 * THIS IS UNPUBLISHED PROPRIETARY SOURCE CODE OF HOWBUY INVESTMENT
 * MANAGEMENT CO., LTD.  THE CONTENTS OF THIS FILE MAY NOT BE DISCLOSED
 * TO THIRD PARTIES, COPIED OR DUPLICATED IN ANY FORM, IN WHOLE OR IN PART,
 * WITHOUT THE PRIOR WRITTEN PERMISSION OF HOWBUY INVESTMENT MANAGEMENT
 * CO., LTD.
 */
package com.howbuy.crm.account.dao.req.custinfo;

import com.howbuy.crm.account.dao.po.custinfo.CmConscustCipherPO;
import com.howbuy.crm.account.dao.po.custinfo.CmConscustPO;
import lombok.Data;

/**
 * @description: (操作投顾客户 的 父类 )
 * <AUTHOR>
 * @date 2023/12/19 11:08
 * @since JDK 1.8
 */
@Data
public class ModifyConsCustReqVO  {


    /**
     * 投顾客户--密文 更新 信息
     */
    private CmConscustCipherPO cipherPO;


    /**
     * 投顾客户  更新 信息
     */
    private CmConscustPO conscustPO;


    /**
     * 变更 操作人
     */
    private String operator;

}