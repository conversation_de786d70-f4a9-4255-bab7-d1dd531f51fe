package com.howbuy.crm.account.dao.po.commvisit;

import java.util.Date;

/**
 * <AUTHOR>
 * @description: 
 * @date 2024/10/24 10:44
 * @since JDK 1.8
 */
/**
    * 沟通拜访表
    */
public class CsCommunicateVisitPO {
    /**
    * 主键编号
    */
    private String id;

    /**
    * 投顾客户号
    */
    private String conscustno;

    /**
    * 具体参见 HB_CONSTANT consultType 项，每索引位 1选中，0未选
    */
    private String consulttype;

    /**
    * 沟通意向 1:无沟通意向, 2:接受沟通: 3:拒绝沟通
    */
    private String commintent;

    /**
    * 投资意向 1:有投资意向, 0:无投资意向
    */
    private String investintent;

    /**
    * 资金量  1:无资金, 2:0-20万, 3:20-50万, 4:50-100, 5:100万以上
    */
    private String amountflag;

    /**
    * 特殊客户 0:疑似同行, 1:疑似盗卡, 2:投诉客户, 3:同行, 4:机构客户
    */
    private String specialmark;

    /**
    * 所属部门 0:表示垃圾, 1:表示RS, 2:表示RT, 3:表示IC
    */
    private String deptflag;

    /**
    * 沟通及拜访内容
    */
    private String commcontent;

    /**
    * 坐席任务编号
    */
    private String taskid;

    /**
    * 预约内容
    */
    private String bookingcontent;

    /**
    * 呼入表编号
    */
    private String callinid;

    /**
    * 拜访方式
    */
    private String visittype;

    /**
    * 下次拜访时间
    */
    private String nextdt;

    /**
    * 投顾预约编码
    */
    private String consbookingid;

    /**
    * 下次拜访内容
    */
    private String nextvisitcontent;

    /**
    * 下次拜访开始时段（下次拜访类型为见面时，必须填）
    */
    private String nextstarttime;

    /**
    * 下次拜访结束时段（下次拜访类型为见面时，必须填）
    */
    private String nextendtime;

    /**
    * 下次预约拜访方式（与拜访方式的类型一致）
    */
    private String nextvisittype;

    /**
    * 拜访分类 {0:正常客户,1:失联客户,2:黑名单,3:屏蔽客户}
    */
    private String visitclassify;

    /**
    * 备注
    */
    private String remark;

    /**
    * 可修改标识 1:内容可编辑, 0:内容不可编辑
    */
    private String modifyflag;

    /**
    * 创建人
    */
    private String creator;

    /**
    * 创建日期
    */
    private Date credt;

    /**
    * 修改人
    */
    private String modifier;

    /**
    * 修改日期
    */
    private Date moddt;

    /**
    * 时间戳
    */
    private Date stimestamp;

    /**
    * 历史数据标识 1:沟通记录表, 0:拜访表
    */
    private String hisflag;

    /**
    * 历史数据主键编号
    */
    private String hisid;

    /**
     * 拜访日期，格式：YYYYMMDD
     */
    private String visitDate;

    public String getId() {
        return id;
    }

    public void setId(String id) {
        this.id = id;
    }

    public String getConscustno() {
        return conscustno;
    }

    public void setConscustno(String conscustno) {
        this.conscustno = conscustno;
    }

    public String getConsulttype() {
        return consulttype;
    }

    public void setConsulttype(String consulttype) {
        this.consulttype = consulttype;
    }

    public String getCommintent() {
        return commintent;
    }

    public void setCommintent(String commintent) {
        this.commintent = commintent;
    }

    public String getInvestintent() {
        return investintent;
    }

    public void setInvestintent(String investintent) {
        this.investintent = investintent;
    }

    public String getAmountflag() {
        return amountflag;
    }

    public void setAmountflag(String amountflag) {
        this.amountflag = amountflag;
    }

    public String getSpecialmark() {
        return specialmark;
    }

    public void setSpecialmark(String specialmark) {
        this.specialmark = specialmark;
    }

    public String getDeptflag() {
        return deptflag;
    }

    public void setDeptflag(String deptflag) {
        this.deptflag = deptflag;
    }

    public String getCommcontent() {
        return commcontent;
    }

    public void setCommcontent(String commcontent) {
        this.commcontent = commcontent;
    }

    public String getTaskid() {
        return taskid;
    }

    public void setTaskid(String taskid) {
        this.taskid = taskid;
    }

    public String getBookingcontent() {
        return bookingcontent;
    }

    public void setBookingcontent(String bookingcontent) {
        this.bookingcontent = bookingcontent;
    }

    public String getCallinid() {
        return callinid;
    }

    public void setCallinid(String callinid) {
        this.callinid = callinid;
    }

    public String getVisittype() {
        return visittype;
    }

    public void setVisittype(String visittype) {
        this.visittype = visittype;
    }

    public String getNextdt() {
        return nextdt;
    }

    public void setNextdt(String nextdt) {
        this.nextdt = nextdt;
    }

    public String getConsbookingid() {
        return consbookingid;
    }

    public void setConsbookingid(String consbookingid) {
        this.consbookingid = consbookingid;
    }

    public String getNextvisitcontent() {
        return nextvisitcontent;
    }

    public void setNextvisitcontent(String nextvisitcontent) {
        this.nextvisitcontent = nextvisitcontent;
    }

    public String getNextstarttime() {
        return nextstarttime;
    }

    public void setNextstarttime(String nextstarttime) {
        this.nextstarttime = nextstarttime;
    }

    public String getNextendtime() {
        return nextendtime;
    }

    public void setNextendtime(String nextendtime) {
        this.nextendtime = nextendtime;
    }

    public String getNextvisittype() {
        return nextvisittype;
    }

    public void setNextvisittype(String nextvisittype) {
        this.nextvisittype = nextvisittype;
    }

    public String getVisitclassify() {
        return visitclassify;
    }

    public void setVisitclassify(String visitclassify) {
        this.visitclassify = visitclassify;
    }

    public String getRemark() {
        return remark;
    }

    public void setRemark(String remark) {
        this.remark = remark;
    }

    public String getModifyflag() {
        return modifyflag;
    }

    public void setModifyflag(String modifyflag) {
        this.modifyflag = modifyflag;
    }

    public String getCreator() {
        return creator;
    }

    public void setCreator(String creator) {
        this.creator = creator;
    }

    public Date getCredt() {
        return credt;
    }

    public void setCredt(Date credt) {
        this.credt = credt;
    }

    public String getModifier() {
        return modifier;
    }

    public void setModifier(String modifier) {
        this.modifier = modifier;
    }

    public Date getModdt() {
        return moddt;
    }

    public void setModdt(Date moddt) {
        this.moddt = moddt;
    }

    public Date getStimestamp() {
        return stimestamp;
    }

    public void setStimestamp(Date stimestamp) {
        this.stimestamp = stimestamp;
    }

    public String getHisflag() {
        return hisflag;
    }

    public void setHisflag(String hisflag) {
        this.hisflag = hisflag;
    }

    public String getHisid() {
        return hisid;
    }

    public void setHisid(String hisid) {
        this.hisid = hisid;
    }

    public String getVisitDate() {
        return visitDate;
    }

    public void setVisitDate(String visitDate) {
        this.visitDate = visitDate;
    }
}