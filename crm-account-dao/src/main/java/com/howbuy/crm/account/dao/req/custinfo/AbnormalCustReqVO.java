package com.howbuy.crm.account.dao.req.custinfo;

import com.howbuy.crm.account.dao.req.PageReqVO;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.util.Date;
import java.util.List;

/**
 * @description: (香港客户异常信息   查询参数对象 表)
 * <AUTHOR>
 * @date 2023/12/11 10:57
 * @since JDK 1.8
 */

/**
    * 香港客户异常信息表
    */
@Data
@EqualsAndHashCode(callSuper = true)
public class AbnormalCustReqVO extends PageReqVO {


    /**
     * 异常客户数据ID
     */
    private String id;


    /**
     * 投资者类型
     */
    private String investType;


    /**
     * 客户号
     */
    private String custNo;


    /**
     * 客户姓名
     */
    private String custName;

    /**
     * 香港客户号
     */
    private String hkTxAcctNo;


    /**
     * 一账通号
     */
    private String hboneNo;



    /**
     * 手机号
     */
    private String mobileDigest;


    /**
     * 证件号
     */
    private String idNoDigest;



    /**
     * 异常来源列表
     * 异常来源：1-香港注册2-香港开户 7-好买开户 [暂时同于  7-分销开户]3-香港客户信息同步4-香港客户号/一账通绑定5-一账通实名
     */
    private List<String> abnormalSourceList;


    /**
     * 处理状态列表
     * 处理状态：0-未处理 1-已处理 2-无需处理
     */
    private List<String> dealStatusList;


    /**
     * 创建时间-开始
     */
    private Date createBginDdate;

    /**
     * 创建时间-结束
     */
    private Date createEndDate;

    /**
     * 异常id列表
     */
    private List<String> abnormalIdList;

    /**
     * 异常级别列表
     */
    private List<String> abnormalLevelList;

}