/**
 * Copyright (c) 2023, <PERSON><PERSON><PERSON><PERSON> HOWBUY INVESTMENT MANAGEMENT Co., Ltd.
 * All right reserved.
 * <p>
 * THIS IS UNPUBLISHED PROPRIETARY SOURCE CODE OF HOWBUY INVESTMENT
 * MANAGEMENT CO., LTD.  THE CONTENTS OF THIS FILE MAY NOT BE DISCLOSED
 * TO THIRD PARTIES, COPIED OR DUPLICATED IN ANY FORM, IN WHOLE OR IN PART,
 * WITHOUT THE PRIOR WRITTEN PERMISSION OF HOWBUY INVESTMENT MANAGEMENT
 * CO., LTD.
 */
package com.howbuy.crm.account.dao.req.custinfo;

import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * @description: (请在此添加描述)
 * <AUTHOR>
 * @date 2023/12/19 11:08
 * @since JDK 1.8
 */
@Data
@EqualsAndHashCode(callSuper = true)
public class UpdateConsCustReqVO  extends  ModifyConsCustReqVO{

    /**
     * 投顾客户编号
     */
    private String custNo;



    /************************his表记录变更 信息  begin **************************************************/


    /**
     * 变更 操作人
     */
    private String specialReason;

    /**
     * 变更 说明
     */
    private String explanation;

    /**
     * 变更 复核人
     */
    private String checker;
/************************his表记录变更 信息  end **************************************************/


}