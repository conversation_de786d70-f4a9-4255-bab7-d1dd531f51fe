package com.howbuy.crm.account.dao.mapper.custinfo;

import com.howbuy.crm.account.dao.bo.consultant.SimpleConsCustDTO;
import com.howbuy.crm.account.dao.po.custinfo.CmConscustPO;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * <AUTHOR>
 * @description: (请在此添加描述)
 * @date 2023/12/8 10:51
 * @since JDK 1.8
 */
public interface CmConscustMapper {
    int deleteByPrimaryKey(String conscustno);

    int insert(CmConscustPO record);

    int insertSelective(CmConscustPO record);

    CmConscustPO selectByPrimaryKey(String conscustno);

    int updateByPrimaryKeySelective(CmConscustPO record);

    int updateByPrimaryKey(CmConscustPO record);


    /**
     * @description: 根据投顾客户号，如果当前未关联一账通号时，更新一账通号
     * @param custNo 投顾客户号
     * @param newHboneNo 新一账通号
     * @return int 更新条数
     * @author: jin.wang03
     * @date: 2023/12/18 14:32
     * @since JDK 1.8
     */

    int upHboneNoWhenNull(@Param("custNo") String custNo, @Param("newHboneNo") String newHboneNo);

     
    /**
     * @description:(根据投顾客户号，一账通号，更新一账通号为null)
     * @param custNo	
     * @param hboneNo
     * @return int
     * @author: haoran.zhang
     * @date: 2024/1/9 10:39
     * @since JDK 1.8
     */
    int upHboneNoToNull(@Param("custNo") String custNo, @Param("hboneNo") String hboneNo);


    /**
     * @description: 根据一账通号查询投顾客户信息
     * @param hboneNo 一账通号
     * @return com.howbuy.crm.account.dao.po.custinfo.CmConscustPO 投顾客户信息
     * @author: jin.wang03
     * @date: 2023/12/18 18:03
     * @since JDK 1.8
     */
    CmConscustPO queryCustByHboneNo(@Param("hboneNo") String hboneNo);

    /**
     * @description 查客户名
     * @param list
     * @return
     * @author: jianjian.yang
     * @date:
     * @since JDK 1.8
     */
    List<SimpleConsCustDTO> querySimpleConsCustList(@Param("list") List<String> list);
}