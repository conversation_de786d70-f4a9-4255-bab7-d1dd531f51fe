package com.howbuy.crm.account.dao.mapper.commvisit;

import com.howbuy.crm.account.dao.po.commvisit.CsCommunicateVisitPO;

/**
 * <AUTHOR>
 * @description: 
 * @date 2024/10/24 10:44
 * @since JDK 1.8
 */
public interface CsCommunicateVisitMapper {
    int deleteByPrimaryKey(String id);

    int insert(CsCommunicateVisitPO record);

    int insertSelective(CsCommunicateVisitPO record);

    CsCommunicateVisitPO selectByPrimaryKey(String id);

    int updateByPrimaryKeySelective(CsCommunicateVisitPO record);

    int updateByPrimaryKey(CsCommunicateVisitPO record);
}