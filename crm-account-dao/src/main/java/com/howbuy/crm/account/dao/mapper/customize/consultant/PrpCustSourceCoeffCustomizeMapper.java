/**
 * Copyright (c) 2024, <PERSON><PERSON><PERSON><PERSON> HOWBUY INVESTMENT MANAGEMENT Co., Ltd.
 * All right reserved.
 * <p>
 * THIS IS UNPUBLISHED PROPRIETARY SOURCE CODE OF HOWBUY INVESTMENT
 * MANAGEMENT CO., LTD.  THE CONTENTS OF THIS FILE MAY NOT BE DISCLOSED
 * TO THIRD PARTIES, COPIED OR DUPLICATED IN ANY FORM, IN WHOLE OR IN PART,
 * WITHOUT THE PRIOR WRITTEN PERMISSION OF HOWBUY INVESTMENT MANAGEMENT
 * CO., LTD.
 */
package com.howbuy.crm.account.dao.mapper.customize.consultant;

import com.howbuy.crm.account.dao.bo.consultant.CmCustSourceCoeffBO;
import org.apache.ibatis.annotations.Param;

/**
 * @description: 客户来源系数配置自定义mapper
 * <AUTHOR>
 * @date 2024-12-19 17:05:00
 * @since JDK 1.8
 */
public interface PrpCustSourceCoeffCustomizeMapper {

    /**
     * @param sourceType 来源类型
     * @return com.howbuy.crm.account.dao.bo.consultant.CmCustSourceCoeffBO
     * @description: 根据来源类型查询客户来源系数配置
     * <AUTHOR>
     * @date 2024-12-19 17:05:00
     * @since JDK 1.8
     */
    CmCustSourceCoeffBO selectCustSourceCoeffBySourceType(@Param("sourceType") String sourceType);

} 