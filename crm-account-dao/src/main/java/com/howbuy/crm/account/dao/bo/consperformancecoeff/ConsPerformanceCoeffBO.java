/**
 * Copyright (c) 2025, <PERSON>g<PERSON><PERSON> HOWBUY INVESTMENT MANAGEMENT Co., Ltd.
 * All right reserved.
 * <p>
 * THIS IS UNPUBLISHED PROPRIETARY SOURCE CODE OF HOWBUY INVESTMENT
 * MANAGEMENT CO., LTD.  THE CONTENTS OF THIS FILE MAY NOT BE DISCLOSED
 * TO THIRD PARTIES, COPIED OR DUPLICATED IN ANY FORM, IN WHOLE OR IN PART,
 * WITHOUT THE PRIOR WRITTEN PERMISSION OF HOWBUY INVESTMENT MANAGEMENT
 * CO., LTD.
 */
package com.howbuy.crm.account.dao.bo.consperformancecoeff;

import lombok.Getter;
import lombok.Setter;

import java.math.BigDecimal;
import java.util.Date;

/**
 * @description: (人员绩效系数BO)
 * <AUTHOR>
 * @date 2025/6/30 13:19
 * @since JDK 1.8
 */
@Getter
@Setter
public class ConsPerformanceCoeffBO {
    /**
     * 客户类型 1：创建客户，2：分配客户
     */
    private String custType;

    /**
     * 分配时间
     */
    private Date assignTime;

    /**
     * 投顾客户号
     */
    private String consCustNo;

    /**
     * 一账通号 是否计提公募使用
     */
    private String hboneNo;

    /**
     * 投顾code
     */
    private String consCode;

    /**
     * 来源类型 有值代表人工修改
     */
    private String sourceType;

    /**
     * 客户折算系数 有值代表人工修改
     */
    private BigDecimal custConversionCoeff;

    /**
     * 管理系数-分总 有值代表人工修改
     */
    private BigDecimal manageCoeffSubtotal;

    /**
     * 管理系数-区副 有值代表人工修改
     */
    private BigDecimal manageCoeffRegionalsubtotal;

    /**
     * 管理系数-区总 有值代表人工修改
     */
    private BigDecimal manageCoeffRegionaltotal;

    /**
     * 佣金系数起点
     */
    private String commissionCoeffStart;

    /**
     * 存续B
     */
    private String cxb;

    /**
     * 是否计提公募 是/否
     */
    private String isBigV;
    /**
     * 中心-分配时
     */
    private String centerCode;
    /**
     * 区域-分配时
     */
    private String regionCode;
    /**
     * 分公司-分配时
     */
    private String outletCode;
    /**
     * 	区副-分配时
     */
    private String regionalSubtotal;
    /**
     * 修改人
     */
    private String modifier;
    /**
     * 计算类型（1：新增，2：修改）
     */
    private String calcType;
}