package com.howbuy.crm.account.dao.po.custinfo;

import lombok.Data;

import java.math.BigDecimal;
import java.util.Date;

/**
 * @description: (香港客户维护表)
 * <AUTHOR>
 * @date 2023/12/11 17:43
 * @since JDK 1.8
 */
/**
    * 香港客户维护表
    */
@Data
public class CmHkConscustPO {
    /**
    * 主键
    */
    private BigDecimal id;

    /**
    * 投顾客户号
    */
    private String conscustno;

    /**
    * 客户香港id (ebrokerID)
    */
    private String hkcustid;

    /**
    * 创建人
    */
    private String creator;

    /**
    * 记录创建日期
    */
    private Date credt;

    /**
    * 修改人
    */
    private String modifier;

    /**
    * 修改日期
    */
    private Date moddt;

    /**
    * 香港交易账号
    */
    private String hkTxAcctNo;
}