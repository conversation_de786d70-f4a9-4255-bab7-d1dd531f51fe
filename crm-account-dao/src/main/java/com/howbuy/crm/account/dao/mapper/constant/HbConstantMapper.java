package com.howbuy.crm.account.dao.mapper.constant;

import com.howbuy.crm.account.dao.po.constant.HbConstantPO;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * <AUTHOR>
 * @description: (请在此添加描述)
 * @date 2023/12/26 15:44
 * @since JDK 1.8
 */
@Mapper
public interface HbConstantMapper {
    int deleteByPrimaryKey(String id);

    int insert(HbConstantPO record);

    int insertSelective(HbConstantPO record);

    HbConstantPO selectByPrimaryKey(String id);

    int updateByPrimaryKeySelective(HbConstantPO record);

    int updateByPrimaryKey(HbConstantPO record);

    /**
     * @description: 根据常量类别获取常量列表
     * @param typeCode 常量类别
     * @return java.util.List<com.howbuy.crm.hb.domain.system.HbConstant> 常量列表
     * @author: jin.wang03
     * @date: 2023/12/14 10:21
     * @since JDK 1.8
     */
    List<HbConstantPO> listByTypeCode(String typeCode);


    /**
     * @description: 根据常量查备用字段2对应常量名
     * @param constCode
     * @param typeCode
     * @param textTypeCode
     * @return java.lang.String
     * @author: jin.wang03
     * @date: 2025/4/10 16:46
     * @since JDK 1.8
     */
    HbConstantPO getConsText2ConstantByCode(@Param("constCode") String constCode,
                                            @Param("typeCode") String typeCode,
                                            @Param("textTypeCode") String textTypeCode);


    /**
     * 根据备用字段2查询常量代码
     * @param consText2 备用字段2
     * @return
     */
    List<String> listConstantCodeByConsText2(@Param("consText2") String consText2,
                                             @Param("typeCode") String typeCode);

}