/**
 * Copyright (c) 2024, <PERSON>g<PERSON><PERSON> HOWBUY INVESTMENT MANAGEMENT Co., Ltd.
 * All right reserved.
 * <p>
 * THIS IS UNPUBLISHED PROPRIETARY SOURCE CODE OF HOWBUY INVESTMENT
 * MANAGEMENT CO., LTD.  THE CONTENTS OF THIS FILE MAY NOT BE DISCLOSED
 * TO THIRD PARTIES, COPIED OR DUPLICATED IN ANY FORM, IN WHOLE OR IN PART,
 * WITHOUT THE PRIOR WRITTEN PERMISSION OF HOWBUY INVESTMENT MANAGEMENT
 * CO., LTD.
 */
package com.howbuy.crm.account.dao.po.beisenposlevel;

import lombok.Data;

import java.util.Date;

/**
 * @description: (好买北森职级映射表)
 * <AUTHOR>
 * @date 2024/10/24 10:03
 * @since JDK 1.8
 */
@Data
public class CmBeisenPosLevelConfigPO {
    /**
     * 主键
     */
    private Long id;
    /**
     * 职级编码（北森）
     */
    private String positionsLevelBeisen;
    /**
     * 职级名称(北森)
     */
    private String positionsLevelNameBeisen;
    /**
     * 层级（crm）
     */
    private String userLevelCrm;
    /**
     * 职级编码（crm）
     */
    private String positionsLevelCrm;
    /**
     * 副职编码（crm）
     */
    private String subPositionsLevelCrm;
    /**
     * 起始日期
     */
    private String startDate;
    /**
     * 结束日期
     */
    private String endDate;
    /**
     * 创建人
     */
    private String creator;
    /**
     * 修改人
     */
    private String modifier;
    /**
     * 创建时间
     */
    private Date credt;
    /**
     * 修改时间
     */
    private Date moddt;

}