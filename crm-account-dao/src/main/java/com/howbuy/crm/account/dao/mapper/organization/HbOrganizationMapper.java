package com.howbuy.crm.account.dao.mapper.organization;


import com.howbuy.crm.account.dao.po.organization.HbOrganizationPO;
import com.howbuy.crm.account.dao.po.organization.OrgCodeNameDTO;
import org.apache.ibatis.annotations.Mapper;

import java.util.List;

@Mapper
public interface HbOrganizationMapper {
    int insert(HbOrganizationPO record);

    int insertSelective(HbOrganizationPO record);

    /**
     * @param orgCodeList 查询条件
     * @return com.github.pagehelper.Page<com.howbuy.crm.account.dao.po.consultant.CmConsPerformanceCoeffPO>
     * @description: 查组织code对应组织名称
     * <AUTHOR>
     * @date 2025-07-16 14:52:00
     * @since JDK 1.8
     */
    List<OrgCodeNameDTO> getOrgNameByOrgCodeList(List<String> orgCodeList);
}