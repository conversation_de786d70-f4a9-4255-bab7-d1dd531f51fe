/**
 * Copyright (c) 2024, <PERSON><PERSON><PERSON><PERSON> HOWBUY INVESTMENT MANAGEMENT Co., Ltd.
 * All right reserved.
 * <p>
 * THIS IS UNPUBLISHED PROPRIETARY SOURCE CODE OF HOWBUY INVESTMENT
 * MANAGEMENT CO., LTD.  THE CONTENTS OF THIS FILE MAY NOT BE DISCLOSED
 * TO THIRD PARTIES, COPIED OR DUPLICATED IN ANY FORM, IN WHOLE OR IN PART,
 * WITHOUT THE PRIOR WRITTEN PERMISSION OF HOWBUY INVESTMENT MANAGEMENT
 * CO., LTD.
 */
package com.howbuy.crm.account.dao.mapper.customize.commvisit;

import com.howbuy.crm.account.dao.bo.commvisit.CsCommunicateBO;
import com.howbuy.crm.account.dao.po.commvisit.CsCommunicateVisitPO;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * <AUTHOR>
 * @description: (请在此添加描述)
 * @date 2024/10/24 10:46
 * @since JDK 1.8
 */
public interface CsCommVisitCustomizeMapper {

    /**
     * @param consCustNo
     * @param visitTypeList
     * @param startDt
     * @param endDt
     * @return java.util.List<com.howbuy.crm.account.dao.po.commvisit.CsCommunicateVisitPO>
     * @description:根据条件查询沟通拜访列表
     * <AUTHOR>
     * @date 2024/10/24 10:56
     * @since JDK 1.8
     */
    List<CsCommunicateVisitPO> selectByCondition(@Param("consCustNo") String consCustNo,
                                                 @Param("visitTypeList") List<String> visitTypeList,
                                                 @Param("startDt") String startDt,
                                                 @Param("endDt") String endDt);

    /**
     * 查询最新一条客户沟通记录
     *
     * @param consCustNo    投顾客户号
     * @param visitTypeList 沟通类型
     * @return CsCommunicateVisitPO
     */
    CsCommunicateVisitPO selectNewestCsCommunicateVisitPO(@Param("consCustNo") String consCustNo,
                                                          @Param("visitTypeList") List<String> visitTypeList);


    /**
     * @description: 根据主键删除记录
     * @param id 主键
     * @return int
     * @author: jin.wang03
     * @date: 2025/4/10 9:53
     * @since JDK 1.8
     */
    int deleteByPrimaryKey(String id);

    /**
     * @description: 新增一条记录
     * @param record CsCommunicateVisitPO
     * @return int
     * @author: jin.wang03
     * @date: 2025/4/10 9:53
     * @since JDK 1.8
     */
    int insert(CsCommunicateVisitPO record);

    /**
     * @description: 新增一条记录
     * @param record CsCommunicateVisitPO
     * @return int
     * @author: jin.wang03
     * @date: 2025/4/10 9:53
     * @since JDK 1.8
     */
    int insertSelective(CsCommunicateVisitPO record);

    /**
     * @description: 根据主键查询记录
     * @param id 主键
     * @return com.howbuy.crm.account.dao.po.commvisit.CsCommunicateVisitPO
     * @author: jin.wang03
     * @date: 2025/4/10 9:53
     * @since JDK 1.8
     */
    CsCommunicateVisitPO selectByPrimaryKey(String id);

    /**
     * @description: 根据主键更新记录
     * @param record CsCommunicateVisitPO
     * @return int
     * @author: jin.wang03
     * @date: 2025/4/10 9:54
     * @since JDK 1.8
     */
    int updateByPrimaryKeySelective(CsCommunicateVisitPO record);

    /**
     * @description: 根据主键更新记录
     * @param record CsCommunicateVisitPO
     * @return int
     * @author: jin.wang03
     * @date: 2025/4/10 9:54
     * @since JDK 1.8
     */
    int updateByPrimaryKey(CsCommunicateVisitPO record);
    /**
     * @description 查询客户拜访列表
     * @param conscustno
     * @return java.util.List<com.howbuy.crm.account.dao.bo.commvisit.CsCommunicateBO>
     * @author: jianjian.yang
     * @date: 2025/4/29 11:31
     * @since JDK 1.8
     */
    List<CsCommunicateBO> queryCustVisitListByPage(@Param("conscustno") String conscustno);
}
