/**
 * Copyright (c) 2024, <PERSON><PERSON><PERSON><PERSON> HOWBUY INVESTMENT MANAGEMENT Co., Ltd.
 * All right reserved.
 * <p>
 * THIS IS UNPUBLISHED PROPRIETARY SOURCE CODE OF HOWBUY INVESTMENT
 * MANAGEMENT CO., LTD.  THE CONTENTS OF THIS FILE MAY NOT BE DISCLOSED
 * TO THIRD PARTIES, COPIED OR DUPLICATED IN ANY FORM, IN WHOLE OR IN PART,
 * WITHOUT THE PRIOR WRITTEN PERMISSION OF HOWBUY INVESTMENT MANAGEMENT
 * CO., LTD.
 */
package com.howbuy.crm.account.dao.mapper.consultantexpmodifyflag;

import com.howbuy.crm.account.dao.po.consultantexp.CmConsultantExpModifyFlagPO;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * @description: (请在此添加描述)
 * <AUTHOR>
 * @date 2024/11/14 18:42
 * @since JDK 1.8
 */
public interface CmConsultantExpModifyFlagMapper {
    /**
     * @description:(获取所有数据)
     * @param
     * @return com.howbuy.crm.hb.domain.system.CmConsultantExpModifyFlag
     * @author: shijie.wang
     * @date: 2024/10/31 20:07
     * @since JDK 1.8
     */
    List<CmConsultantExpModifyFlagPO> selectAll();

    /**
     * @description:(merge花名册人工修改数据)
     * @param saveModifyFlagList
     * @return void
     * @author: shijie.wang
     * @date: 2024/11/4 16:39
     * @since JDK 1.8
     */
    void mergeExpModifyFlagByList(@Param("list") List<CmConsultantExpModifyFlagPO> saveModifyFlagList);
}