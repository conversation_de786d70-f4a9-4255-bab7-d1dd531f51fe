package com.howbuy.crm.account.dao.mapper.custservice;

import com.howbuy.crm.account.dao.po.custservice.CmBookingcustPO;
import org.apache.ibatis.annotations.Mapper;

/**
 * @description: (请在此添加描述)
 * <AUTHOR>
 * @date 2023/12/28 18:00
 * @since JDK 1.8
 */
@Mapper
public interface CmBookingcustMapper {
    int deleteByPrimaryKey(Long id);

    int insert(CmBookingcustPO record);

    int insertSelective(CmBookingcustPO record);

    CmBookingcustPO selectByPrimaryKey(Long id);

    int updateByPrimaryKeySelective(CmBookingcustPO record);

    int updateByPrimaryKey(CmBookingcustPO record);
}