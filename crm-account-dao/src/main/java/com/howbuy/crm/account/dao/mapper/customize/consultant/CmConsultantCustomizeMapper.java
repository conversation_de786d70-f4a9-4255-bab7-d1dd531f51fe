/**
 * Copyright (c) 2024, <PERSON><PERSON><PERSON><PERSON> HOWBUY INVESTMENT MANAGEMENT Co., Ltd.
 * All right reserved.
 * <p>
 * THIS IS UNPUBLISHED PROPRIETARY SOURCE CODE OF HOWBUY INVESTMENT
 * MANAGEMENT CO., LTD.  THE CONTENTS OF THIS FILE MAY NOT BE DISCLOSED
 * TO THIRD PARTIES, COPIED OR DUPLICATED IN ANY FORM, IN WHOLE OR IN PART,
 * WITHOUT THE PRIOR WRITTEN PERMISSION OF HOWBUY INVESTMENT MANAGEMENT
 * CO., LTD.
 */
package com.howbuy.crm.account.dao.mapper.customize.consultant;

import com.howbuy.crm.account.dao.po.consultant.CmConsultantPO;
import com.howbuy.crm.account.dao.po.consultant.ConsultantSimpleInfoDto;
import com.howbuy.crm.account.dao.po.consultant.SearchConsultantDTO;
import com.howbuy.crm.account.dao.req.consultant.QueryConsultantReqVO;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * @description: (请在此添加描述)
 * <AUTHOR>
 * @date 2024/10/24 10:31
 * @since JDK 1.8
 */
public interface CmConsultantCustomizeMapper {

    /**
     * @description:(根据投顾codelist查询投顾信息)
     * @param consCodeList
     * @return java.util.List<com.howbuy.crm.account.dao.po.consultant.CmConsultantPO>
     * @author: shuai.zhang
     * @date: 2024/3/28 14:40
     * @since JDK 1.8
     */
    List<CmConsultantPO> getListByConscodes(@Param("consCodeList") List<String> consCodeList);

    /**
     * @description:根据reqVO查询投顾信息
     * @param reqVO
     * @return java.util.List<com.howbuy.crm.account.dao.po.consultant.CmConsultantPO>
     * <AUTHOR>
     * @date 2024/10/11 19:17
     * @since JDK 1.8
     */
    List<CmConsultantPO> queryListByVo(QueryConsultantReqVO reqVO);

    /**
     * @description 根据投顾codelist查询包含离职投顾信息
     * @param consCodeList
     * @return java.util.List<com.howbuy.crm.account.dao.po.consultant.CmConsultantPO>
     * @author: jianjian.yang
     * @date: 2025/5/14 17:06
     * @since JDK 1.8
     */
    List<CmConsultantPO> getContainQuitListByConsCodes(@Param("consCodeList") List<String> consCodeList);
    /**
     * @description 用户搜索
     * @param keyword	关键字
     * @param isPm	是否项目经理
     * @param pmRoleCodeList 角色编码集合
     * @return java.util.List<com.howbuy.crm.account.dao.po.consultant.SearchConsultantDTO>
     * @author: jianjian.yang
     * @date: 2025/4/11 15:23
     * @since JDK 1.8
     */
    List<SearchConsultantDTO> searchConsultant(@Param("keyword") String keyword, @Param("isPm") boolean isPm, @Param("pmRoleCodeList") List<String> pmRoleCodeList);
    /**
     * @description 根据投顾名称查询投顾编码
     * @param consName
     * @return java.util.List<java.lang.String>
     * @author: jianjian.yang
     * @date: 2025/4/11 17:17
     * @since JDK 1.8
     */
    List<String> getConsCodeByName(@Param("consName") String consName);

    /**
     * @description:(查询全量投顾简单信息，缓存使用)
     * @param
     * @return java.util.List<com.howbuy.crm.account.dao.po.consultant.ConsultantSimpleInfoDto>
     * @author: haoran.zhang
     * @date: 2025/3/6 13:19
     * @since JDK 1.8
     */
    List<ConsultantSimpleInfoDto> getSimpleConsultList();


    /**
     * @description:(根据投顾号查询投顾简单信息，缓存使用)
     * @param consCode
     * @return com.howbuy.crm.account.dao.po.consultant.ConsultantSimpleInfoDto
     * @author: haoran.zhang
     * @date: 2025/3/6 13:19
     * @since JDK 1.8
     */
    ConsultantSimpleInfoDto getSimpleConsultantByCode(@Param("consCode") String consCode);

}
