package com.howbuy.crm.account.dao.mapper.custinfo;

import com.github.pagehelper.Page;
import com.howbuy.crm.account.dao.bo.custinfo.HkUpdateEbrokerIdBO;
import com.howbuy.crm.account.dao.po.custinfo.CmHkConscustPO;
import com.howbuy.crm.account.dao.req.PageReqVO;
import com.howbuy.crm.account.dao.req.custinfo.CmHkCustReqVO;
import org.apache.ibatis.annotations.Param;

import java.math.BigDecimal;
import java.util.List;

/**
 * @description: (请在此添加描述)
 * <AUTHOR>
 * @date 2023/12/11 17:43
 * @since JDK 1.8
 */
public interface CmHkConscustMapper {
    int deleteByPrimaryKey(BigDecimal id);

    int insert(CmHkConscustPO record);

    CmHkConscustPO selectByPrimaryKey(BigDecimal id);

    int updateByPrimaryKeySelective(CmHkConscustPO record);

    int updateByPrimaryKey(CmHkConscustPO record);


    /**
     * 备份 香港客户记录
     * @param id 数据id
     * @return
     */
    int backUpHkAcctById(@Param("id") BigDecimal id);


   /**
    * @description:(根据 [客户号] 查询 香港交易账户)
    * @param custNo
    * @return com.howbuy.crm.account.dao.po.custinfo.CmHkConscustPO
    * @author: haoran.zhang
    * @date: 2023/12/11 17:51
    * @since JDK 1.8
    */
    CmHkConscustPO selectByCustNo(@Param("custNo") String custNo);

    /**
     * @description:(根据 [香港交易账号] 查询 香港交易账户)
     * @param hkTxAcctNo
     * @return com.howbuy.crm.account.dao.po.custinfo.CmHkConscustPO
     * @author: haoran.zhang
     * @date: 2023/12/11 17:51
     * @since JDK 1.8
     */
    CmHkConscustPO selectByHkTxAcctNo(@Param("hkTxAcctNo") String hkTxAcctNo);


    /**
     * @description:(根据根据 [ebrokerId] 查询 香港交易账户)
     * @param ebrokerId
     * @return com.howbuy.crm.account.dao.po.custinfo.CmHkConscustPO
     * @author: haoran.zhang
     * @date: 2023/12/11 17:51
     * @since JDK 1.8
     */
    CmHkConscustPO selectByEbrokerId(@Param("ebrokerId") String ebrokerId);


    /**
     * @description:(根据根据  reqVO 查询 香港交易账户)
     * @param reqVO
     * @return com.howbuy.crm.account.dao.po.custinfo.CmHkConscustPO
     * @date: 2023/12/11 17:51
     * @since JDK 1.8
     */
    CmHkConscustPO selectByReqVO(CmHkCustReqVO reqVO);

    /**
     * @description: (查询总数)
     * @param
     * @return long
     * @author: jin.wang03
     * @date: 2024/6/4 14:40
     * @since JDK 1.8
     */
    long count();

    /**
     * @description: (分页查询)
     * @param vo
     * @return com.github.pagehelper.Page<com.howbuy.crm.account.dao.po.custinfo.CmHkConscustPO>
     * @author: jin.wang03
     * @date: 2024/6/4 14:40
     * @since JDK 1.8
     */
    Page<CmHkConscustPO> selectPageByVo(PageReqVO vo);

    /**
     * @description: 批量更新：更新指定hkCustNo的ebrokerId
     * @param list
     * @return int
     * @author: jin.wang03
     * @date: 2024/6/4 14:40
     * @since JDK 1.8
     */
    int batchUpdateEbrokerIdByHkCustNo(@Param("list") List<HkUpdateEbrokerIdBO> list);
}