package com.howbuy.crm.account.dao.bo.custinfo;

import lombok.Data;

/**
 * @description: (投顾客户信息  异常分析 使用  包括 香港客户信息)
 * <AUTHOR>
 * @date 2023/12/8 20:57
 * @since JDK 1.8
 */
@Data
public class CmConscustForAnalyseBO {
    /**
     * 投顾客户编号
     */
    private String conscustno;


    /**
     * 客户有效状态：0-正常 1-删除
     */
    private String conscuststatus;

    /**
     * 证件类型
     */
    private String idtype;

    /**
     * 投资者名称
     */
    private String custname;


    /**
     * 客户投资类型 0 机构客户 1 个人客户 2 产品客户
     */
    private String invsttype;

    /**
     * 一账通账号
     */
    private String hboneNo;

    /**
     * 证件号摘要
     */
    private String idnoDigest;

    /**
     * 证件号掩码
     */
    private String idnoMask;

    /**
     * 手机号摘要
     */
    private String mobileDigest;

    /**
     * 手机号掩码
     */
    private String mobileMask;


    /**
     * 邮箱摘要
     */
    private String emailDigest;

    /**
     * 邮箱掩码
     */
    private String emailMask;


    /**
     * 手机 地区码
     */
    private String mobileAreaCode;

    /**
     * 证件地区码
     */
    private String idSignAreaCode;

    /**
     * 国籍
     */
    private String nationCode;

    //以下为 香港客户信息

    /**
     * 客户香港id (ebrokerID)
     */
    private String hkcustid;

    /**
     * 香港交易账号
     */
    private String hkTxAcctNo;

    //以下为  所属投顾信息

    /**
     * 所属投顾
     */
    private String  consCode;


}