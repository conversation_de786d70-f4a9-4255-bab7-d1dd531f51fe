package com.howbuy.crm.account.dao.bo.beisen;

import java.math.BigDecimal;

/**
 * 北森组织配置表
 */
public class CmBeisenOrgConfigBO {
    /**
    * 主键
    */
    private BigDecimal id;

    /**
    * 架构ID（北森）
    */
    private String orgIdBeisen;

    /**
    * 架构名称（北森）
    */
    private String orgNameBeisen;

    /**
    * 中心（crm）
    */
    private String orgCode;

    /**
     * 中心（crm）
     */
    private String u1NameCrm;

    /**
     * 区域（crm）
     */
    private String u2NameCrm;

    /**
     * 分公司（crm）
     */
    private String u3NameCrm;

    /**
    * 业务中心
    */
    private String centerOrg;

    /**
     * 业务中心
     */
    private String centerOrgName;

    /**
    * 起始日期
    */
    private String startDate;

    /**
    * 结束日期
    */
    private String endDate;

    public BigDecimal getId() {
        return id;
    }

    public void setId(BigDecimal id) {
        this.id = id;
    }

    public String getOrgIdBeisen() {
        return orgIdBeisen;
    }

    public void setOrgIdBeisen(String orgIdBeisen) {
        this.orgIdBeisen = orgIdBeisen;
    }

    public String getOrgNameBeisen() {
        return orgNameBeisen;
    }

    public void setOrgNameBeisen(String orgNameBeisen) {
        this.orgNameBeisen = orgNameBeisen;
    }

    public String getCenterOrg() {
        return centerOrg;
    }

    public void setCenterOrg(String centerOrg) {
        this.centerOrg = centerOrg;
    }

    public String getStartDate() {
        return startDate;
    }

    public void setStartDate(String startDate) {
        this.startDate = startDate;
    }

    public String getEndDate() {
        return endDate;
    }

    public void setEndDate(String endDate) {
        this.endDate = endDate;
    }

    public String getOrgCode() {
        return orgCode;
    }

    public void setOrgCode(String orgCode) {
        this.orgCode = orgCode;
    }

    public String getU1NameCrm() {
        return u1NameCrm;
    }

    public void setU1NameCrm(String u1NameCrm) {
        this.u1NameCrm = u1NameCrm;
    }

    public String getU2NameCrm() {
        return u2NameCrm;
    }

    public void setU2NameCrm(String u2NameCrm) {
        this.u2NameCrm = u2NameCrm;
    }

    public String getU3NameCrm() {
        return u3NameCrm;
    }

    public void setU3NameCrm(String u3NameCrm) {
        this.u3NameCrm = u3NameCrm;
    }

    public String getCenterOrgName() {
        return centerOrgName;
    }

    public void setCenterOrgName(String centerOrgName) {
        this.centerOrgName = centerOrgName;
    }
}