package com.howbuy.crm.account.dao.mapper.custinfo;

import com.howbuy.crm.account.dao.po.custinfo.CmHboneHkRelationLogPO;
import org.apache.ibatis.annotations.Mapper;

/**
 * <AUTHOR>
 * @description: (请在此添加描述)
 * @date 2023/12/15 13:32
 * @since JDK 1.8
 */
public interface CmHboneHkRelationLogMapper {
    int deleteByPrimaryKey(String id);

    int insert(CmHboneHkRelationLogPO record);

    int insertSelective(CmHboneHkRelationLogPO record);

    CmHboneHkRelationLogPO selectByPrimaryKey(String id);

    int updateByPrimaryKeySelective(CmHboneHkRelationLogPO record);

    int updateByPrimaryKey(CmHboneHkRelationLogPO record);
}