package com.howbuy.crm.account.dao.bo.commvisit;

import lombok.Getter;
import lombok.Setter;

import java.time.LocalDateTime;

/**
 * <AUTHOR>
 * @description: 没有反馈的要发送消息的记录
 * @date 2025/4/29 19:15
 * @since JDK 1.8
 */
@Getter
@Setter
public class NoFeedbackMsgBO {

    /**
     * 主键
     */
    private Long id;
    /**
     * 要发送的投顾编码
     */
    private String consCode;
    /**
     * 投顾客户号
     */
    private String consCustNo;
    /**
     * 创建投顾名称
     */
    private String consName;
    /**
     * 客户名称
     */
    private String custName;
    /**
     * 创建时间
     */
    private LocalDateTime createTime;
    /**
     * 访问目的
     */
    private String visitPurpose;
}