/**
 * Copyright (c) 2024, <PERSON><PERSON><PERSON><PERSON> HOWBUY INVESTMENT MANAGEMENT Co., Ltd.
 * All right reserved.
 * <p>
 * THIS IS UNPUBLISHED PROPRIETARY SOURCE CODE OF HOWBUY INVESTMENT
 * MANAGEMENT CO., LTD.  THE CONTENTS OF THIS FILE MAY NOT BE DISCLOSED
 * TO THIRD PARTIES, COPIED OR DUPLICATED IN ANY FORM, IN WHOLE OR IN PART,
 * WITHOUT THE PRIOR WRITTEN PERMISSION OF HOWBUY INVESTMENT MANAGEMENT
 * CO., LTD.
 */
package com.howbuy.crm.account.dao.mapper.beisenposlevel;

import com.howbuy.crm.account.dao.bo.beisenposlevel.CmBeisenPosLevelConfigBO;
import com.howbuy.crm.account.dao.po.beisenposlevel.CmBeisenPosLevelConfigPO;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * @description: (好买北森职级映射表mapper)
 * <AUTHOR>
 * @date 2024/10/24 10:14
 * @since JDK 1.8
 */
public interface CmBeisenPosLevelConfigMapper {

    /**
     * @description:(获得好买北森职级配置表信息)
     * @param po
     * @return java.util.List<com.howbuy.crm.account.dao.bo.beisenposlevel.CmBeisenPosLevelConfigBO>
     * @author: shijie.wang
     * @date: 2024/10/24 11:14
     * @since JDK 1.8
     */
    List<CmBeisenPosLevelConfigBO> selectPosLevelConfig(CmBeisenPosLevelConfigPO po);

    /**
     * @description:(保存好买北森职级配置表信息)
     * @param po
     * @return void
     * @author: shijie.wang
     * @date: 2024/10/24 13:10
     * @since JDK 1.8
     */
    void savePosLevelConfig(CmBeisenPosLevelConfigPO po);

    /**
     * @description:(通过id删除好买北森职级配置表信息)
     * @param id
     * @return void
     * @author: shijie.wang
     * @date: 2024/10/24 13:14
     * @since JDK 1.8
     */
    void deletePosLevelConfig(@Param("id") Long id);

    /**
     * @description:(通过id查询好买北森职级配置表信息)
     * @param id
     * @return java.util.List<com.howbuy.crm.account.dao.bo.beisenposlevel.CmBeisenPosLevelConfigBO>
     * @author: shijie.wang
     * @date: 2024/10/24 16:56
     * @since JDK 1.8
     */
    CmBeisenPosLevelConfigBO selectPosLevelConfigById(@Param("id") Long id);

}