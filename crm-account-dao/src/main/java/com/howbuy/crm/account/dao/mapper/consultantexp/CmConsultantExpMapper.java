/**
 * Copyright (c) 2024, <PERSON><PERSON><PERSON><PERSON> HOWBUY INVESTMENT MANAGEMENT Co., Ltd.
 * All right reserved.
 * <p>
 * THIS IS UNPUBLISHED PROPRIETARY SOURCE CODE OF HOWBUY INVESTMENT
 * MANAGEMENT CO., LTD.  THE CONTENTS OF THIS FILE MAY NOT BE DISCLOSED
 * TO THIRD PARTIES, COPIED OR DUPLICATED IN ANY FORM, IN WHOLE OR IN PART,
 * WITHOUT THE PRIOR WRITTEN PERMISSION OF HOWBUY INVESTMENT MANAGEMENT
 * CO., LTD.
 */
package com.howbuy.crm.account.dao.mapper.consultantexp;

import com.howbuy.crm.account.dao.po.consultant.RegionalSubtotalDTO;
import com.howbuy.crm.account.dao.po.consultantexp.CmConsultantExpPO;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * @description: (请在此添加描述)
 * <AUTHOR>
 * @date 2024/11/14 19:30
 * @since JDK 1.8
 */
public interface CmConsultantExpMapper {
    /**
     * @description:(merge花名册数据)
     * @param expList
     * @return void
     * @author: shijie.wang
     * @date: 2024/11/4 16:36
     * @since JDK 1.8
     */
    void mergeCmConsultantExpByBeisen(@Param("list") List<CmConsultantExpPO> expList);

    /**
     * @description:(通过userno获得花名册数据)
     * @param userNos
     * @return java.util.List<com.howbuy.crm.hb.domain.system.CmConsultantExp>
     * @author: shijie.wang
     * @date: 2024/11/5 17:49
     * @since JDK 1.8
     */
    List<CmConsultantExpPO> listCmConsultantExpByUserNo(@Param("userNos") List<String> userNos);

    /**
     * @description: (通过consCode获得花名册数据)
     * @param consCode
     * @return com.howbuy.crm.hb.domain.system.CmConsultantExp
     * @author: jin.wang03
     * @date: 2025/04/08 15:43
     * @since JDK 1.8
     */
    CmConsultantExpPO getCmConsultantExpByConsCode(@Param("consCode") String consCode);

    /**
     * @description: 根据 指定的orgcode和 指定层级userlevel 查询出所有leader
     * @param orgcode
     * @param userlevel
     * @return java.util.List<java.lang.String>
     * @author: jin.wang03
     * @date: 2025/4/14 13:29
     * @since JDK 1.8
     */
    List<String> listLeadersByOrgCodeAndLevel(@Param("orgcode") String orgcode, @Param("userlevel") String userlevel);

    /**
     * @description: 查询指定orgCode的 区域执行副总
     * @param orgCode
     * @return java.util.List<java.lang.String>
     * @author: jin.wang03
     * @date: 2025/4/14 13:28
     * @since JDK 1.8
     */
    List<String> listFLeadersByOrgCode(String orgCode);

    /**
     * @description: 根据orgCode获取区副信息
     * @param orgCode 部门code
     * @return String 区副用户ID
     * @author: shijie.wang
     * @date: 2025/1/17 14:20
     * @since JDK 1.8
     */
    String getRegionalSubtotalByOrgCode(@Param("orgCode") String orgCode);

    /**
     * @description: 根据orgCode获取部门信息
     * @param orgCodes 部门code
     * @return String 部门名称
     * @author: jianjian.yang
     * @date: 2025/7/16 14:20
     * @since JDK 1.8
     */
    List<RegionalSubtotalDTO> getRegionalSubtotalNameByOrgCodeList(@Param("orgCodes") List<String> orgCodes);
}