package com.howbuy.crm.account.dao.mapper.consultant;

import com.howbuy.crm.account.dao.po.consultant.CmConsultantPO;
import com.howbuy.crm.account.dao.req.consultant.QueryConsultantReqVO;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * @description: (请在此添加描述)
 * <AUTHOR>
 * @date 2023/12/26 15:31
 * @since JDK 1.8
 */
@Mapper
public interface CmConsultantMapper {
    int deleteByPrimaryKey(String conscode);

    int insert(CmConsultantPO record);

    int insertSelective(CmConsultantPO record);

    CmConsultantPO selectByPrimaryKey(String conscode);

    int updateByPrimaryKeySelective(CmConsultantPO record);

    int updateByPrimaryKey(CmConsultantPO record);

    /**
     * @description:(查询所有需要刷新客户关系的投顾企微账号)
     * @param
     * @return java.util.List<java.lang.String>
     * @author: shuai.zhang
     * @date: 2024/3/27 11:24
     * @since JDK 1.8
     */
    List<String> getAllNeedRefreshWechatConsCode();

    /**
     * @description: 根据 手机号摘要 查询投顾数量
     * @param mobileDigest
     * @return int
     * @author: jin.wang03
     * @date: 2024/11/4 14:17
     * @since JDK 1.8
     */
    int countByMobileDigest(@Param("mobileDigest") String mobileDigest);

    /**
     * @description:(请在此添加描述)
     * @param consCodes
     * @return java.util.List<com.howbuy.crm.account.dao.po.consultant.CmConsultantPO>
     * @author: jin.wang03
     * @date: 2025/4/14 16:03
     * @since JDK 1.8
     */
    List<CmConsultantPO> listConsultantByConsCodes(@Param("consCodes")List<String> consCodes);
}