package com.howbuy.crm.account.dao.po.custinfo;

import java.util.Date;
import lombok.Data;

/**
 * @description: (请在此添加描述)
 * <AUTHOR>
 * @date 2023/12/21 15:48
 * @since JDK 1.8
 */
/**
    * 一账通异常客户表
    */
@Data
public class CmHboneAbnormalCustInfoPO {
    /**
    * 异常客户数据ID
    */
    private String id;

    /**
    * 消息通知的clientId
    */
    private String messageClientId;

    /**
     * rocketMq的消息ID，全局唯一 （messageClientId 记录的是rocketMq客户端id，没有意义）
     */
    private String messageId;

    /**
    * 一账通号
    */
    private String hboneNo;

    /**
    * 投资者类型
    */
    private String investType;

    /**
    * 客户姓名
    */
    private String custName;

    /**
    * 手机地区码
    */
    private String mobileAreaCode;

    /**
    * 手机号摘要
    */
    private String mobileDigest;

    /**
    * 手机号掩码
    */
    private String mobileMask;

    /**
    * 手机号密文
    */
    private String mobileCipher;

    /**
    * 证件地区码
    */
    private String idSignAreaCode;

    /**
    * 证件类型
    */
    private String idType;

    /**
    * 证件号码摘要
    */
    private String idNoDigest;

    /**
    * 证件号码掩码
    */
    private String idNoMask;

    /**
    * 证件号码密文
    */
    private String idNoCipher;

    /**
    * 香港客户号
    */
    private String hkTxAcctNo;

    /**
    * 异常来源：6-一账通注册7-分销开户2-香港开户8-一账通客户信息同步5-一账通实名
    */
    private String abnormalSource;

    /**
    * 异常描述 ：1-匹配到多个投顾客户号2-匹配到的投顾客户号已被占用3-手机号相同，证件类型/证件号不匹配4-证件相同，但手机号不匹配5-CRM重复客户预警9-客户姓名相同，证件类型/证件号不匹配10-证件相同，但客户姓名不匹配11-分销开户证件与香港实名证件不一致12-同时绑定一账通时，投顾客户号/一账通号被占用13-一账通实名证件与香港实名证件不一致
    */
    private String abnormalSceneType;

    /**
    * 创建人
    */
    private String creator;

    /**
    * 创建时间
    */
    private Date createTimestamp;

    /**
    * 修改人
    */
    private String modifier;

    /**
    * 修改时间
    */
    private Date modifyTimestamp;

    /**
    * 记录有效状态（1-正常  0-删除）
    */
    private String recStat;

    /**
    * 处理状态：0-未处理 1-已处理 2-无需处理
    */
    private String dealStatus;

    /**
    * 处理人
    */
    private String dealOperator;

    /**
    * 处理意见
    */
    private String dealRemark;

    /**
    * 处理时间
    */
    private Date dealTimestamp;
}