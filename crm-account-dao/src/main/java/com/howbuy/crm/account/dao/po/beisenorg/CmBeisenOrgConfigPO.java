package com.howbuy.crm.account.dao.po.beisenorg;

import java.util.Date;

/**
 * 北森组织配置表
 */
public class CmBeisenOrgConfigPO {
    /**
    * 主键
    */
    private Long id;

    /**
    * 架构ID（北森）
    */
    private String orgIdBeisen;

    /**
    * 架构名称（北森）
    */
    private String orgNameBeisen;

    /**
    * 所属部门
    */
    private String orgCode;

    /**
    * 业务中心
    */
    private String centerOrg;

    /**
    * 起始日期
    */
    private String startDate;

    /**
    * 结束日期
    */
    private String endDate;

    /**
    * 创建人
    */
    private String creator;

    /**
    * 修改人
    */
    private String modifier;

    /**
    * 创建时间
    */
    private Date credt;

    /**
    * 修改时间
    */
    private Date moddt;

    public Long getId() {
        return id;
    }

    public void setId(Long id) {
        this.id = id;
    }

    public String getOrgIdBeisen() {
        return orgIdBeisen;
    }

    public void setOrgIdBeisen(String orgIdBeisen) {
        this.orgIdBeisen = orgIdBeisen;
    }

    public String getOrgNameBeisen() {
        return orgNameBeisen;
    }

    public void setOrgNameBeisen(String orgNameBeisen) {
        this.orgNameBeisen = orgNameBeisen;
    }

    public String getCenterOrg() {
        return centerOrg;
    }

    public void setCenterOrg(String centerOrg) {
        this.centerOrg = centerOrg;
    }

    public String getStartDate() {
        return startDate;
    }

    public void setStartDate(String startDate) {
        this.startDate = startDate;
    }

    public String getEndDate() {
        return endDate;
    }

    public void setEndDate(String endDate) {
        this.endDate = endDate;
    }

    public String getCreator() {
        return creator;
    }

    public void setCreator(String creator) {
        this.creator = creator;
    }

    public String getModifier() {
        return modifier;
    }

    public void setModifier(String modifier) {
        this.modifier = modifier;
    }

    public Date getCredt() {
        return credt;
    }

    public void setCredt(Date credt) {
        this.credt = credt;
    }

    public Date getModdt() {
        return moddt;
    }

    public void setModdt(Date moddt) {
        this.moddt = moddt;
    }

    public String getOrgCode() {
        return orgCode;
    }

    public void setOrgCode(String orgCode) {
        this.orgCode = orgCode;
    }
}