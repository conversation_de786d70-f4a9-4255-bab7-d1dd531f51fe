package com.howbuy.crm.account.dao.mapper.commvisit;

import com.howbuy.crm.account.dao.po.commvisit.CsVisitNewestPO;
import org.apache.ibatis.annotations.Param;

public interface CsVisitNewestMapper {
    int deleteByPrimaryKey(String id);

    /**
     * @description: 根据consCustNo删除数据
     * @param consCustNo 投顾客户号
     * @return int
     * @author: jin.wang03
     * @date: 2025/4/10 13:47
     * @since JDK 1.8
     */
    int deleteByConsCustNo(@Param("consCustNo") String consCustNo);

    int insert(CsVisitNewestPO record);

    int insertSelective(CsVisitNewestPO record);

    CsVisitNewestPO selectByPrimaryKey(String id);

    int updateByPrimaryKeySelective(CsVisitNewestPO record);

    int updateByPrimaryKey(CsVisitNewestPO record);
}