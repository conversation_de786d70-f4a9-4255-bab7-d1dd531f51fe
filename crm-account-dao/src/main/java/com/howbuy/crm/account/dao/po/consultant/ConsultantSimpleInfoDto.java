package com.howbuy.crm.account.dao.po.consultant;

import java.io.Serializable;

/**
 * 投顾信息  简单信息 dto
 */
public class ConsultantSimpleInfoDto implements Serializable{

	/** 
	 * (变量说明描述)	
	 *
	 * long CmConsultantInfoDomain.java serialVersionUID
	 */
	private static final long serialVersionUID = -9103222784341770522L;

	/**
	 *理财顾问代码
	 */
	private String consCode;
	/**
	 *理财顾问名称
	 */
	private String consName;

	/**
	 * 理财顾问级别
	 */
	private String consLevel;

	/**
	 * 理财顾问状态1有效，0无效
	 */
	private String consStatus;

	/** 是否虚拟投顾（1 是/0 否） */
	private String isVirtual;

	/**
	 * 所属理财中心
	 */
	private String   outletCode;

	/**
	 *所属小组
	 */
	private String teamCode;

	/**
	 *手机
	 */
	private String mobile;
	/**
	 *投顾照片链接地址
	 */
	private String picAddr;
	/**
	 *企业微信二维码地址
	 */
	private String codePicAddr;
	/**
	 *理财顾问职位
	 */
	private String position;
	/**
	 *电子邮件
	 */
	private String email;

	/**
	 * 所属中心
	 * {@link com.howbuy.crm.account.client.enums.orgnazion.CenterOrgEnum}
	 */
	private String centerOrgCode;

	public String getConsCode() {
		return consCode;
	}

	public void setConsCode(String consCode) {
		this.consCode = consCode;
	}

	public String getConsName() {
		return consName;
	}

	public void setConsName(String consName) {
		this.consName = consName;
	}


	public String getMobile() {
		return mobile;
	}

	public void setMobile(String mobile) {
		this.mobile = mobile;
	}

	public String getPicAddr() {
		return picAddr;
	}

	public void setPicAddr(String picAddr) {
		this.picAddr = picAddr;
	}

	public String getCodePicAddr() {
		return codePicAddr;
	}

	public void setCodePicAddr(String codePicAddr) {
		this.codePicAddr = codePicAddr;
	}

	public String getPosition() {
		return position;
	}

	public void setPosition(String position) {
		this.position = position;
	}

	public String getEmail() {
		return email;
	}

	public void setEmail(String email) {
		this.email = email;
	}

	public String getConsLevel() {
		return consLevel;
	}

	public void setConsLevel(String consLevel) {
		this.consLevel = consLevel;
	}

	public String getTeamCode() {
		return teamCode;
	}

	public void setTeamCode(String teamCode) {
		this.teamCode = teamCode;
	}

	public String getConsStatus() {
		return consStatus;
	}

	public void setConsStatus(String consStatus) {
		this.consStatus = consStatus;
	}

	public String getIsVirtual() {
		return isVirtual;
	}

	public void setIsVirtual(String isVirtual) {
		this.isVirtual = isVirtual;
	}

	public String getOutletCode() {
		return outletCode;
	}

	public void setOutletCode(String outletCode) {
		this.outletCode = outletCode;
	}

	public String getCenterOrgCode() {
		return centerOrgCode;
	}

	public void setCenterOrgCode(String centerOrgCode) {
		this.centerOrgCode = centerOrgCode;
	}
}
