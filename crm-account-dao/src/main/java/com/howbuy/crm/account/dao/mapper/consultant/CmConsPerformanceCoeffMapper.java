/**
 * Copyright (c) 2025, <PERSON><PERSON><PERSON><PERSON> HOWBUY INVESTMENT MANAGEMENT Co., Ltd.
 * All right reserved.
 * <p>
 * THIS IS UNPUBLISHED PROPRIETARY SOURCE CODE OF HOWBUY INVESTMENT
 * MANAGEMENT CO., LTD.  THE CONTENTS OF THIS FILE MAY NOT BE DISCLOSED
 * TO THIRD PARTIES, COPIED OR DUPLICATED IN ANY FORM, IN WHOLE OR IN PART,
 * WITHOUT THE PRIOR WRITTEN PERMISSION OF HOWBUY INVESTMENT MANAGEMENT
 * CO., LTD.
 */
package com.howbuy.crm.account.dao.mapper.consultant;

import com.github.pagehelper.Page;
import com.howbuy.crm.account.dao.bo.consultant.CmConsPerformanceCoeffBO;
import com.howbuy.crm.account.dao.po.consultant.CmConsPerformanceCoeffPO;
import com.howbuy.crm.account.dao.req.consultant.QueryConsPerformanceCoeffListDTO;
import org.apache.ibatis.annotations.Param;

import java.util.Date;
import java.util.List;

/**
 * @description: (人员绩效系数表mapper)
 * <AUTHOR>
 * @date 2025/6/27 17:11
 * @since JDK 1.8
 */
public interface CmConsPerformanceCoeffMapper {

    /**
     * @description: 新增人员绩效系数表记录
     * @param po 人员绩效系数表记录
     * @return int 影响行数
     * <AUTHOR>
     * @date 2025-06-27 17:05:00
     * @since JDK 1.8
     */
    int insert(CmConsPerformanceCoeffPO po);

    /**
     * @description: 选择性新增人员绩效系数表记录
     * @param cmConsPerformanceCoeffPO 人员绩效系数表记录
     * @return int 影响行数
     * <AUTHOR>
     * @date 2025-06-27 17:05:00
     * @since JDK 1.8
     */
    int insertSelective(CmConsPerformanceCoeffPO cmConsPerformanceCoeffPO);

    /**
     * @description: 根据主键更新人员绩效系数表记录
     * @param cmConsPerformanceCoeffPO 人员绩效系数表记录
     * @return int 影响行数
     * <AUTHOR>
     * @date 2025-06-27 17:05:00
     * @since JDK 1.8
     */
    int updateByPrimaryKey(CmConsPerformanceCoeffPO cmConsPerformanceCoeffPO);

    /**
     * @description: 根据主键选择性更新人员绩效系数表记录
     * @param cmConsPerformanceCoeffPO 人员绩效系数表记录
     * @return int 影响行数
     * <AUTHOR>
     * @date 2025-06-27 17:05:00
     * @since JDK 1.8
     */
    int updateByPrimaryKeySelective(CmConsPerformanceCoeffPO cmConsPerformanceCoeffPO);

    /**
     * @description: 根据主键查询人员绩效系数表记录
     * @param consCustNo 投顾客户号
     * @param conscode 投顾code
     * @param assignTime 分配时间
     * @return com.howbuy.crm.account.dao.po.consultant.CmConsPerformanceCoeffPO
     * <AUTHOR>
     * @date 2025-06-27 17:05:00
     * @since JDK 1.8
     */
    CmConsPerformanceCoeffPO selectByPrimaryKey(@Param("consCustNo") String consCustNo, 
                                                @Param("conscode") String conscode, 
                                                @Param("assignTime") Date assignTime);

    /**
     * @description: 根据投顾客户号和投顾code查询最新记录
     * @param consCustNo 投顾客户号
     * @param conscode 投顾code
     * @return com.howbuy.crm.account.dao.po.consultant.CmConsPerformanceCoeffPO
     * <AUTHOR>
     * @date 2025-06-27 17:05:00
     * @since JDK 1.8
     */
    CmConsPerformanceCoeffPO selectLatestByConsCustNoAndConsCode(@Param("consCustNo") String consCustNo, 
                                                                 @Param("conscode") String conscode);

    /**
     * @description: 批量查询投顾客户号列表的最新绩效系数记录
     * @param consCustNoList 投顾客户号列表
     * @param conscode 投顾code（可选）
     * @return java.util.List<CmConsPerformanceCoeffPO>
     * <AUTHOR>
     * @date 2025-07-17 15:50:04
     * @since JDK 1.8
     */
    List<CmConsPerformanceCoeffPO> selectLatestByConsCustNoListAndConsCode(@Param("consCustNoList") List<String> consCustNoList, @Param("conscode") String conscode);

    /**
     * @description: 根据客户+投顾查找CM_CONS_PERFORMANCE_COEFF表中分配时间小于等于当前时间数据
     * @param consCustNo 投顾客户号
     * @param currentTime 当前时间
     * @return List<CmConsPerformanceCoeffPO> 历史分配记录列表
     * <AUTHOR>
     * @date 2025-07-01 16:00:00
     * @since JDK 1.8
     */
    List<CmConsPerformanceCoeffPO> selectHistoryRecordsByConsCustNo(@Param("consCustNo") String consCustNo, 
                                                                    @Param("currentTime") Date currentTime);

    /**
     * @description: 查询客户最新的重复类型分配记录
     * @param consCustNo 投顾客户号
     * @param sourceTypes 来源类型列表
     * @param assignTime
     * @return com.howbuy.crm.account.dao.po.consultant.CmConsPerformanceCoeffPO
     * @author: shijie.wang
     * @date: 2025/1/17 16:30
     * @since JDK 1.8
     */
    CmConsPerformanceCoeffPO selectLatestRepeatRecord(@Param("consCustNo") String consCustNo, 
                                                     @Param("sourceTypes") List<String> sourceTypes,
                                                      @Param("assignTime") Date assignTime);

    /**
     * @description: 查询客户最新记录
     * @param consCustNo 投顾客户号
     * @return com.howbuy.crm.account.dao.po.consultant.CmConsPerformanceCoeffPO
     * @author: shijie.wang
     * @date: 2025/1/17 16:30
     * @since JDK 1.8
     */
    CmConsPerformanceCoeffPO selectNewRecord(@Param("consCustNo") String consCustNo);

    /**
     * 查询指定时间范围内的分配记录
     * @param consCustNo 客户编号
     * @param startTime 开始时间
     * @param endTime 结束时间
     * @return 时间范围内的分配记录列表
     */
    List<CmConsPerformanceCoeffPO> selectRecordsByTimeRange(@Param("consCustNo") String consCustNo,
                                                           @Param("startTime") Date startTime,
                                                           @Param("endTime") Date endTime);

    /**
     * @param queryDTO 查询条件
     * @return com.github.pagehelper.Page<com.howbuy.crm.account.dao.po.consultant.CmConsPerformanceCoeffPO>
     * @description: 分页查询人员绩效系数列表
     * <AUTHOR>
     * @date 2025-07-11 18:52:00
     * @since JDK 1.8
     */
    Page<CmConsPerformanceCoeffBO> selectConsPerformanceCoeffList(QueryConsPerformanceCoeffListDTO queryDTO);

    /**
     * @param queryDTO 列表查询条件
     * @return int
     * @description: 查询人员绩效系数列表数量
     * <AUTHOR>
     * @date 2025-07-31 9:52:00
     * @since JDK 1.8
     */
    int selectConsPerformanceCoeffListCount(QueryConsPerformanceCoeffListDTO queryDTO);
}