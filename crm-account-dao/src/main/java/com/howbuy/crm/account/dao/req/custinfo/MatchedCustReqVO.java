package com.howbuy.crm.account.dao.req.custinfo;

import lombok.Data;

import java.util.List;

/**
 * @description: (客户表 是否匹配  查询 VO )
 * <AUTHOR>
 * @date 2023/12/11 17:43
 * @since JDK 1.8
 */
@Data
public class MatchedCustReqVO {
    /**
     * 手机号摘要
     */
    private String mobileDigest;

    /**
     * 手机 地区码
     */
    private String mobileAreaCode;


    /**
     * 证件类型
     */
    private String idType;

    /**
     * 证件号摘要
     */
    private String idNoDigest;

    /**
     * 证件地区码
     */
    private String idSignAreaCode;

    /**
     * 投资类型列表
     */
    private List<String> investTypeList;


}