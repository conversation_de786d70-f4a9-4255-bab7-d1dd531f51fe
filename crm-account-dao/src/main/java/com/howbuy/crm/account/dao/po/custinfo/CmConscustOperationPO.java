package com.howbuy.crm.account.dao.po.custinfo;

import java.util.Date;
import lombok.Data;

/**
 * @description: (请在此添加描述)
 * <AUTHOR>
 * @date 2023/12/27 11:08
 * @since JDK 1.8
 */
/**
    * 投顾客户操作记录
    */
@Data
public class CmConscustOperationPO {
    /**
    * 主键
    */
    private String operationid;

    /**
    * 操作客户号，来自客户表与潜客表
    */
    private String conscustno;

    /**
    * 城市代码
    */
    private String citycode;

    /**
    * 客户来源备注
    */
    private String custsourceremark;

    /**
    * 新来源编号
    */
    private String newsourceno;

    /**
    * 投顾客户等级
    */
    private String conscustlvl;

    /**
    * 投顾客户评分
    */
    private Short conscustgrade;

    /**
    * 登记日期
    */
    private String regdt;

    /**
    * 是否申请划转 0-否 1-是
    */
    private String transferapply;

    /**
    * 处理状态:1-待处理;2-已处理-驳回;3-已处理-同意' 等同划转日志表cm_transf_log
    */
    private String opstatus;

    /**
    * 操作来源：1-新增，2-修改
    */
    private String status;

    /**
    * 客户姓名
    */
    private String custname;

    /**
    * 创建人
    */
    private String creator;

    /**
    * 创建时间
    */
    private Date creatdt;

    /**
    * 省份代码
    */
    private String provcode;

    /**
    * 操作来源：1-CRM，2-APP
    */
    private String opensource;

    /**
     * 手机 地区码
     */
    private String mobileAreaCode;

    /**
    * 手机号摘要
    */
    private String mobileDigest;

    /**
    * 手机号掩码
    */
    private String mobileMask;

    /**
    * 手机号密文
    */
    private String mobileCipher;

    /**
    * 来源渠道（取值有：小程序、APP分享）
    */
    private String sourcechannel;

    /**
     * 县代码
     */
    private String countyCode;
}