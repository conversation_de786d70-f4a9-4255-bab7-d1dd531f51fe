package com.howbuy.crm.account.dao.mapper.customize.custinfo;

import com.github.pagehelper.Page;
import com.howbuy.crm.account.dao.po.custinfo.CmHboneAbnormalCustInfoPO;
import com.howbuy.crm.account.dao.po.custinfo.CmHboneAbnormalRelatedCustPO;
import com.howbuy.crm.account.dao.req.custinfo.AbnormalCustReqVO;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * @description: (一账通异常客户信息   自定义 mapper)
 * <AUTHOR>
 * @date 2023/12/11 10:57
 * @since JDK 1.8
 */
public interface HboneAbnormalCustomizeMapper {


   /**
    * @description:(一账通异常客户信息   自定义 mapper  分页查询)
    * @param vo
    * @return com.github.pagehelper.Page<com.howbuy.crm.account.dao.po.custinfo.CmHboneAbnormalCustInfoPO>
    * @author: haoran.zhang
    * @date: 2024/1/18 17:21
    * @since JDK 1.8
    */
    Page<CmHboneAbnormalCustInfoPO> selectPageByVo(AbnormalCustReqVO vo);


    /**
     * @description:(查询 关联的客户信息列表)
     * @param abnormalIdList  异常主表id列表
     * @return java.util.List<com.howbuy.crm.account.dao.po.custinfo.CmHboneAbnormalRelatedCustPO>
     * @author: haoran.zhang
     * @date: 2024/1/18 17:21
     * @since JDK 1.8
     */
    List<CmHboneAbnormalRelatedCustPO> selectRelatedListByMainIdList(@Param("abnormalIdList") List<List<String>> abnormalIdList);


    /**
     * @description: (查询 关联的客户信息列表)
     * @param detailIdList 异常明细表表id 列表
     * @return java.util.List<com.howbuy.crm.account.dao.po.custinfo.CmHkAbnormalCustInfoPO>
     */
    List<CmHboneAbnormalRelatedCustPO> selectRelatedListByDetailList(@Param("detailIdList") List<List<String>> detailIdList);
}