package com.howbuy.crm.account.dao.req.custinfo;

import lombok.Data;

import java.math.BigDecimal;

/**
 * @description: (香港客户表 查询 VO )
 * <AUTHOR>
 * @date 2023/12/11 17:43
 * @since JDK 1.8
 */

/**
    * 香港客户维护表
    */
@Data
public class CmHkCustReqVO {
    /**
    * 主键
    */
    private BigDecimal id;

    /**
    * 投顾客户号
    */
    private String conscustno;

    /**
    * 客户香港id (ebrokerID)
    */
    private String hkcustid;

    /**
    * 香港交易账号
    */
    private String hkTxAcctNo;
}