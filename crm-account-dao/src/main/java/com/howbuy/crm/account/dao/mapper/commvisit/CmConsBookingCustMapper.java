package com.howbuy.crm.account.dao.mapper.commvisit;

import com.howbuy.crm.account.dao.po.commvisit.CmConsBookingCustPO;

public interface CmConsBookingCustMapper {
    int deleteByPrimaryKey(String consbookingid);

    int insert(CmConsBookingCustPO record);

    int insertSelective(CmConsBookingCustPO record);

    CmConsBookingCustPO selectByPrimaryKey(String consbookingid);

    int updateByPrimaryKeySelective(CmConsBookingCustPO record);

    int updateByPrimaryKey(CmConsBookingCustPO record);
}