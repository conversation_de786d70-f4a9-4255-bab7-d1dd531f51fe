package com.howbuy.crm.account.dao.po.commvisit;

import java.time.LocalDateTime;
import java.util.Date;
import lombok.Getter;
import lombok.Setter;

/**
 * 拜访纪要
 */
@Getter
@Setter
public class CmVisitMinutesPO {
    /**
     * 主键
     */
    private String id;

    /**
     * 沟通记录ID
     */
    private String communicateId;

    /**
     * 客户号
     */
    private String consCustNo;

    /**
     * 拜访目的，格式：多选项用逗号分隔，1:初次见面,2:产品服务,3:IPS陪访,4:创新陪访,5:Leads及继承客户拜访,6:其他
     */
    private String visitPurpose;

    /**
     * 当拜访目的包含"其他"时填写
     */
    private String visitPurposeOther;

    /**
     * IPS报告ID，IPS陪访必填
     */
    private String assetReportId;

    /**
     * 客户存量
     */
    private String marketVal;

    /**
     * 客户目前综合健康度，1-5星
     */
    private String healthAvgStar;

    /**
     * 提供资料
     */
    private String giveInformation;

    /**
     * 客户参与人员及角色
     */
    private String attendRole;

    /**
     * 对产品或服务的具体反馈
     */
    private String productServiceFeedback;

    /**
     * 对于IPS报告反馈，IPS陪访必填
     */
    private String ipsFeedback;

    /**
     * 近期可用于加仓的金额人民币，IPS陪访必填之一
     */
    private String addAmountRmb;

    /**
     * 近期可用于加仓的金额外币，IPS陪访必填之一
     */
    private String addAmountForeign;

    /**
     * 近期关注的资产类别或具体产品，IPS陪访必填
     */
    private String focusAsset;

    /**
     * 评估客户对创新业务、家族信托、身份、法税的需求，IPS陪访必填
     */
    private String estimateNeedBusiness;

    /**
     * 下一步工作计划
     */
    private String nextPlan;

    /**
     * 上级主管
     */
    private String managerId;

    /**
     * 上级主管层级
     */
    private String managerUserLevel;

    /**
     * 上级主管该客户下阶段工作的建议
     */
    private String managerSuggestion;

    /**
     * 上级主管本次陪访概要经验或教训
     */
    private String managerSummary;

    /**
     * 上级主管反馈时间
     */
    private Date managerFillTime;

    /**
     * 创建人
     */
    private String creator;

    /**
     * 创建时间
     */
    private LocalDateTime createTime;

    /**
     * 修改人
     */
    private String modifier;

    /**
     * 修改时间
     */
    private Date modifyTime;
}