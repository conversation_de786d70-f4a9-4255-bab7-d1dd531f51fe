package com.howbuy.crm.account.dao.po.commvisit;

import java.util.Date;

/**
 * 投顾预约客户表
 */
public class CmConsBookingCustPO {
    private String consbookingid;

    private String conscustno;

    /**
    * 预约状态
    */
    private String bookingstatus;

    /**
    * 预约投顾
    */
    private String bookingcons;

    /**
    * 预约内容
    */
    private String content;

    /**
    * 预约日期
    */
    private String bookingdt;

    /**
    * 创建人
    */
    private String creator;

    /**
    * 记录创建日期
    */
    private String credt;

    /**
    * 记录修改日期
    */
    private String moddt;

    /**
    * 时间戳
    */
    private Date stimestamp;

    /**
    * 预约开始时段
    */
    private String bookingstarttime;

    /**
    * 预约结束时段
    */
    private String bookingendtime;

    /**
    * 预约方式{"5":"投顾去电","1":"客户来电","4":"投顾去访","0":"客户来访","2":"电子邮件","3":"短信"}
    */
    private String visittype;

    /**
    * 拜访分类 {0:正常客户,1:失联客户,2:黑名单,3:屏蔽客户}
    */
    private String visitclassify;

    public String getConsbookingid() {
        return consbookingid;
    }

    public void setConsbookingid(String consbookingid) {
        this.consbookingid = consbookingid;
    }

    public String getConscustno() {
        return conscustno;
    }

    public void setConscustno(String conscustno) {
        this.conscustno = conscustno;
    }

    public String getBookingstatus() {
        return bookingstatus;
    }

    public void setBookingstatus(String bookingstatus) {
        this.bookingstatus = bookingstatus;
    }

    public String getBookingcons() {
        return bookingcons;
    }

    public void setBookingcons(String bookingcons) {
        this.bookingcons = bookingcons;
    }

    public String getContent() {
        return content;
    }

    public void setContent(String content) {
        this.content = content;
    }

    public String getBookingdt() {
        return bookingdt;
    }

    public void setBookingdt(String bookingdt) {
        this.bookingdt = bookingdt;
    }

    public String getCreator() {
        return creator;
    }

    public void setCreator(String creator) {
        this.creator = creator;
    }

    public String getCredt() {
        return credt;
    }

    public void setCredt(String credt) {
        this.credt = credt;
    }

    public String getModdt() {
        return moddt;
    }

    public void setModdt(String moddt) {
        this.moddt = moddt;
    }

    public Date getStimestamp() {
        return stimestamp;
    }

    public void setStimestamp(Date stimestamp) {
        this.stimestamp = stimestamp;
    }

    public String getBookingstarttime() {
        return bookingstarttime;
    }

    public void setBookingstarttime(String bookingstarttime) {
        this.bookingstarttime = bookingstarttime;
    }

    public String getBookingendtime() {
        return bookingendtime;
    }

    public void setBookingendtime(String bookingendtime) {
        this.bookingendtime = bookingendtime;
    }

    public String getVisittype() {
        return visittype;
    }

    public void setVisittype(String visittype) {
        this.visittype = visittype;
    }

    public String getVisitclassify() {
        return visitclassify;
    }

    public void setVisitclassify(String visitclassify) {
        this.visitclassify = visitclassify;
    }
}