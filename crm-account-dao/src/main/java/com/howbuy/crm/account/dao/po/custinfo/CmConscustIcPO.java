package com.howbuy.crm.account.dao.po.custinfo;

import lombok.Data;

/**
 * @description: (请在此添加描述)
 * <AUTHOR>
 * @date 2024/1/3 10:02
 * @since JDK 1.8
 */
/**
    * 投顾客户信息表
    */
@Data
public class CmConscustIcPO {
    /**
    * 投顾客户编号
    */
    private String conscustno;

    /**
    * 投顾客户等级
    */
    private String conscustlvl;

    /**
    * 投顾客户评分
    */
    private Short conscustgrade;

    /**
    * 投顾客户状态
    */
    private String conscuststatus;

    /**
    * 证件类型
    */
    private String idtype;

    /**
    * 投资者名称
    */
    private String custname;

    /**
    * 省份代码
    */
    private String provcode;

    /**
    * 城市代码
    */
    private String citycode;

    /**
    * 投资者学历
    */
    private String edulevel;

    /**
    * 投资者职业代码
    */
    private String vocation;

    /**
    * 投资者年收入
    */
    private String inclevel;

    /**
    * 投资者生日
    */
    private String birthday;

    /**
    * 投资者性别
    */
    private String gender;

    /**
    * 婚否
    */
    private String married;

    /**
    * 个人年收入
    */
    private String pincome;

    /**
    * 家庭年收入
    */
    private String fincome;

    /**
    * 是否投资决策人
    */
    private String decisionflag;

    /**
    * 兴趣爱好
    */
    private String interests;

    /**
    * 家庭状况
    */
    private String familycondition;

    /**
    * 方便联系时段
    */
    private String contacttime;

    /**
    * 最佳联系方式
    */
    private String contactmethod;

    /**
    * 是否寄送资料
    */
    private String sendinfoflag;

    /**
    * 是否接受电话
    */
    private String recvtelflag;

    /**
    * 是否接受电子邮件
    */
    private String recvemailflag;

    /**
    * 是否接受短信
    */
    private String recvmsgflag;

    /**
    * 公司
    */
    private String company;

    /**
    * 客户风险承受能力等级
    */
    private String risklevel;

    /**
    * 自定义风险承受能力等级
    */
    private String selfrisklevel;

    /**
    * 邮政编码
    */
    private String postcode;

    /**
    * 传真
    */
    private String fax;

    /**
    * 投资者住址电话
    */
    private String hometelno;

    /**
    * 投资者单位电话
    */
    private String officetelno;

    /**
    * 活动代码
    */
    private String actcode;

    /**
    * 推荐人客户号
    */
    private String intrcustno;

    /**
    * 客户来源
    */
    private String source;

    /**
    * 了解途径
    */
    private String knowchan;

    /**
    * 其它途径
    */
    private String otherchan;

    /**
    * 想了解的其他投资品种
    */
    private String otherinvest;

    /**
    * 希望参加的沙龙
    */
    private String salon;

    /**
    * 之前投资品种
    */
    private String beforeinvest;

    /**
    * 即时通讯方式-qq
    */
    private String im;

    /**
    * 即时通讯方式-MSN
    */
    private String msn;

    /**
    * 可投资资产
    */
    private String ainvestamt;

    /**
    * 可投资基金资产
    */
    private String ainvestfamt;

    /**
    * 自定义标志
    */
    private String selfdefflag;

    /**
    * 沟通频率
    */
    private String visitfqcy;

    /**
    * 发展方向
    */
    private String devdirection;

    /**
    * 销售方向
    */
    private String saledirection;

    /**
    * 客户来源细分
    */
    private String subsource;

    /**
    * 客户来源细分分类
    */
    private String subsourcetype;

    /**
    * 销售进度
    */
    private String saleprocess;

    /**
    * 被合并的投顾客户号
    */
    private String mergedconscust;

    /**
    * 邮政编码2
    */
    private String postcode2;

    /**
    * 知道好买
    */
    private String knowhowbuy;

    /**
    * 知道好买细分
    */
    private String subknow;

    /**
    * 知道好买细分分类
    */
    private String subknowtype;

    /**
    * 购买产品
    */
    private String buyingprod;

    /**
    * 曾购买产品
    */
    private String buyedprod;

    /**
    * 免费产品
    */
    private String freeprod;

    /**
    * 是否特殊客户
    */
    private String specialflag;

    /**
    * 报告寄送方式
    */
    private String dlvymode;

    /**
    * 备注
    */
    private String remark;

    /**
    * 登记日期
    */
    private String regdt;

    /**
    * 最近修改日期
    */
    private String uddt;

    /**
    * 私募风险测试水平
    */
    private String pririsklevel;

    /**
    * 联系人姓名
    */
    private String linkman;

    /**
    * 经办人邮政编码
    */
    private String linkpostcode;

    /**
    * 职位
    */
    private String capacity;

    /**
    * ������
    */
    private String activityno;

    /**
    * ��������
    */
    private String partnerno;

    /**
    * GPS投资级别---对应资金额度(A: 投资资产<100万,B:投资资产100-2000万 C:投资资产>2000万)
    */
    private String gpsinvestlevel;

    /**
    * GPS风险偏好( 1: 积极型 ,2:稳健型,3:保守型)
    */
    private String gpsrisklevel;

    /**
    * 是否企业主( 1:是企业主，0)
    */
    private String isboss;

    /**
    * 金融需求
    */
    private String financeneed;

    /**
    * 是否入会（1：已经入会）
    */
    private String isjoinclub;

    private String tmpBeforeinvest;

    private String tmpOtherinvest;

    /**
    * 是否签署风险提示函
    */
    private String isrisktip;

    /**
    * 是否临时发送邮件标识符（1：是，0：不是）
    */
    private Short tempemailflag;

    /**
    * 客户来源备注
    */
    private String custsourceremark;

    /**
    * 审核标志（0：待审核，1：审核通过，2：审核不通过）
    */
    private String checkflag;

    /**
    * 审核评论
    */
    private String checkremark;

    /**
    * 审核人
    */
    private String checkman;

    /**
    * 创建人
    */
    private String creator;

    private String invsttype;

    private String wechatcode;

    /**
    * 新来源编号
    */
    private String newsourceno;

    /**
    * 可以期望交易方式（1：电子交易；2：线下交易）
    */
    private String hopetradetype;

    /**
    * 证件有限期
     * 是否长期有效0-否,1-是
    */
    private String validity;

    /**
    * 证件有限期日期
    */
    private String validitydt;

    /**
    * 性质
    */
    private String nature;

    /**
    * 资质
    */
    private String aptitude;

    /**
    * 经营范围
    */
    private String scopebusiness;

    /**
    * 是否申请划转 0-否 1-是
    */
    private String transferapply;

    /**
    * 处理状态:1-待处理;2-已处理-驳回;3-已处理-同意' 等同划转日志表cm_transf_log
    */
    private String opstatus;

    /**
    * 证件号摘要
    */
    private String idnoDigest;

    /**
    * 证件号掩码
    */
    private String idnoMask;

    /**
    * 客户姓名摘要
    */
    private String custnameDigest;

    /**
    * 客户姓名掩码
    */
    private String custnameMask;

    /**
    * 地址摘要
    */
    private String addrDigest;

    /**
    * 地址掩码
    */
    private String addrMask;

    /**
     * 手机 地区码
     */
    private String mobileAreaCode;

    /**
     * 手机2 地区码
     */
    private String mobile2AreaCode;

    /**
     * 联系人手机 地区码
     */
    private String linkmobileAreaCode;

    /**
    * 手机号摘要
    */
    private String mobileDigest;

    /**
    * 手机号掩码
    */
    private String mobileMask;

    /**
    * 座机号摘要
    */
    private String telnoDigest;

    /**
    * 座机号掩码
    */
    private String telnoMask;

    /**
    * 邮箱摘要
    */
    private String emailDigest;

    /**
    * 邮箱掩码
    */
    private String emailMask;

    /**
    * 地址2摘要
    */
    private String addr2Digest;

    /**
    * 地址2掩码
    */
    private String addr2Mask;

    /**
    * 手机2摘要
    */
    private String mobile2Digest;

    /**
    * 手机2掩码
    */
    private String mobile2Mask;

    /**
    * 邮箱2摘要
    */
    private String email2Digest;

    /**
    * 邮箱2掩码
    */
    private String email2Mask;

    /**
    * 联系人姓名摘要
    */
    private String linkmanDigest;

    /**
    * 联系人姓名掩码
    */
    private String linkmanMask;

    /**
    * 联系人座机号摘要
    */
    private String linktelDigest;

    /**
    * 联系人座机号掩码
    */
    private String linktelMask;

    /**
    * 联系人手机摘要
    */
    private String linkmobileDigest;

    /**
    * 联系人手机掩码
    */
    private String linkmobileMask;

    /**
    * 联系人邮箱摘要
    */
    private String linkemailDigest;

    /**
    * 联系人邮箱掩码
    */
    private String linkemailMask;

    /**
    * 联系人地址摘要
    */
    private String linkaddrDigest;

    /**
    * 联系人地址掩码
    */
    private String linkaddrMask;

    /**
     * 县代码
     */
    private String countyCode;

    /**
     * 证件地区码
     */
    private String idSignAreaCode;

    /**
     * 国籍
     */
    private String nationCode;

}